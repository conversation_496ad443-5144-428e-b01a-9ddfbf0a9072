# 修复草稿状态下 field 为空的问题

## 问题描述
在 `modifyOfflineTraining` 接口中，当保存草稿状态（trainStatus=1）的培训时，如果没有传递 `fieldStr` 参数，会导致培训的场次信息（field）被清空。

## 问题原因
1. **原始逻辑缺陷**：代码只在有完整的 `trainStartTime` 和 `trainEndTime` 时才创建场次信息
2. **草稿状态特殊性**：草稿状态下，时间信息可能不完整，导致场次信息被清空
3. **BeanUtils.copyProperties 影响**：会将表单中的空值复制到实体中

## 修复方案

### 1. 修改 `addOfflineTraining` 方法
```java
//更新培训场次信息
if (StringUtils.isNotBlank(req.getFieldStr())) {
    updateCmsTrainFiledList(req.getFieldStr(), entity, 1);
} else {
    // 如果是草稿状态(trainStatus=1)且没有fieldStr，不创建场次信息
    if (entity.getTrainStatus() != null && entity.getTrainStatus().equals(1)) {
        // 草稿状态下，不强制创建场次信息
        logger.info("草稿状态下不强制创建场次信息，培训ID: {}", entity.getId());
    } else {
        // 非草稿状态下，如果有培训时间信息，创建默认场次
        if (StringUtils.isNotBlank(entity.getTrainStartTime()) && StringUtils.isNotBlank(entity.getTrainEndTime())) {
            // ... 创建默认场次的逻辑
        }
    }
}
```

### 2. 修改 `modifyOfflineTraining` 方法
```java
//更新培训场次信息
if (StringUtils.isNotBlank(req.getFieldStr())) {
    updateCmsTrainFiledList(req.getFieldStr(), entity, 1);
} else {
    // 如果是草稿状态(trainStatus=1)且没有fieldStr，保留现有的fieldList，不做任何处理
    if (entity.getTrainStatus() != null && entity.getTrainStatus().equals(1)) {
        // 草稿状态下，保留现有场次信息，不清空也不重新创建
        logger.info("草稿状态下保留现有场次信息，培训ID: {}", entity.getId());
    } else {
        // 非草稿状态下，如果有培训时间信息，创建默认场次
        if (StringUtils.isNotBlank(entity.getTrainStartTime()) && StringUtils.isNotBlank(entity.getTrainEndTime())) {
            // ... 创建默认场次的逻辑
        }
    }
}
```

## 修复效果
1. **草稿状态保护**：在草稿状态下，不会清空或重新创建场次信息
2. **数据完整性**：保留用户已经输入的场次数据
3. **向后兼容**：不影响非草稿状态的正常逻辑

## 测试建议
1. 测试草稿状态下保存培训，验证场次信息不会丢失
2. 测试非草稿状态下的正常流程，确保功能正常
3. 测试有 fieldStr 参数时的场次更新功能

## 相关文件
- `CultureCloudOfflineTrainingServiceImpl.java` - 主要修改文件
- `CultureCloudOfflineTrainingReq.java` - 请求参数定义
- `CultureCloudOfflineTrainingForm.java` - 表单定义
