package com.foshan.controller.businessOpportunity;

import com.foshan.form.businessOpportunity.request.member.*;
import com.foshan.form.response.IResponse;
import com.foshan.service.businessOpportunity.IBusinessOpportunityMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/bo-member")
@Api(tags = "商机会员接口")
public class BusinessOpportunityMemberController {

    @Autowired
    private IBusinessOpportunityMemberService businessOpportunityMemberService;

    @PostMapping("/login")
    @ApiOperation("会员登录")
    public IResponse login(@RequestBody @Valid BusinessOpportunityMemberLoginReq req, HttpServletRequest request) {
        return businessOpportunityMemberService.login(req,request);
    }

    @GetMapping("/info")
    @ApiOperation("获取会员信息")
    public IResponse getMemberInfo(@Valid BusinessOpportunityMemberInfoReq req) {
        return businessOpportunityMemberService.getBusinessOpportunityMemberInfo(req);
    }

    @PutMapping("/info")
    @ApiOperation("更新会员信息")
    public IResponse updateMemberInfo(@RequestBody @Valid BusinessOpportunityMemberUpdateReq req) {
        return businessOpportunityMemberService.updateBusinessOpportunityMemberInfo(req);
    }

    @PostMapping("/bind")
    @ApiOperation("绑定手机号")
    public IResponse bindMobile(@RequestBody @Valid BusinessOpportunityMemberBindReq req) {
        return businessOpportunityMemberService.bindBusinessOpportunityMemberMobile(req);
    }

    @PostMapping("/send-code")
    @ApiOperation("发送验证码")
    public IResponse sendVerificationCode(@RequestBody @Valid BusinessOpportunityMemberSendCodeReq req) {
        return businessOpportunityMemberService.sendVerificationCode(req);
    }
} 