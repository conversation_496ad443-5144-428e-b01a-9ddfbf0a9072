package com.foshan.service.businessOpportunity;

import com.foshan.form.response.IResponse;
import com.foshan.form.businessOpportunity.request.member.BusinessOpportunityMemberLoginReq;
import com.foshan.form.businessOpportunity.request.member.BusinessOpportunityMemberInfoReq;
import com.foshan.form.businessOpportunity.request.member.BusinessOpportunityMemberUpdateReq;
import com.foshan.form.businessOpportunity.request.member.BusinessOpportunityMemberBindReq;
import com.foshan.form.businessOpportunity.request.member.BusinessOpportunityMemberSendCodeReq;

import javax.servlet.http.HttpServletRequest;

public interface IBusinessOpportunityMemberService {
    
    /**
     * 会员登录
     */
    IResponse login(BusinessOpportunityMemberLoginReq req, HttpServletRequest request);
    
    /**
     * 获取商机会员信息
     */
    IResponse getBusinessOpportunityMemberInfo(BusinessOpportunityMemberInfoReq req);

    /**
     * 更新商机会员信息
     */
    IResponse updateBusinessOpportunityMemberInfo(BusinessOpportunityMemberUpdateReq req);

    /**
     * 绑定商机会员手机号
     */
    IResponse bindBusinessOpportunityMemberMobile(BusinessOpportunityMemberBindReq req);

    /**
     * 发送验证码
     */
    IResponse sendVerificationCode(BusinessOpportunityMemberSendCodeReq req);
} 