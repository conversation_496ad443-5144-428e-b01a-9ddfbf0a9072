package com.foshan.service.businessOpportunity.impl;

import com.foshan.dao.shop.IMemberAddressDao;
import com.foshan.dao.shop.IMemberDao;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.shop.MemberEntity;
import com.foshan.model.permssion.AccountPrincipalModel;
import com.foshan.model.permssion.PrincipalModel;
import com.foshan.service.impl.GenericService;
import com.foshan.util.SpringHandler;
import com.hazelcast.spring.cache.HazelcastCacheManager;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.cache.Cache;

import javax.annotation.Resource;
import java.util.List;

public class GenericBusinessService extends GenericService {

    protected IMemberAddressDao memberAddressDao;
    @Resource(name = "memberDao")
    protected IMemberDao memberDao;

    @Resource
    private HazelcastCacheManager hazelcastCacheManager;

    protected Object getPrincipal(boolean returnFullEntity) {
        Subject curUser = SecurityUtils.getSubject();
        PrincipalCollection principals = curUser.getPrincipals();
        if (null != principals && !principals.isEmpty()) {
            @SuppressWarnings("unchecked")
            List<Object> principalList = principals.asList();
            Object principal = (null != principalList.get(1) ? principalList.get(1) : null);

//			if (!returnFullEntity) {
//				return principal;
//			}
            Cache cache =  hazelcastCacheManager.getCache("userObjectCache");
            Cache userIdcache =  hazelcastCacheManager.getCache("userIdCache");
            if (principal instanceof AccountPrincipalModel) {
                Object object =  cache.get(principal, MemberEntity.class);
                if(null == object || returnFullEntity) {
                    AccountPrincipalModel partyUser = (AccountPrincipalModel) principal;
                    String sql = "select * from t_account where id = '" + partyUser.getId() + "' and userState = '"
                            + EntityContext.RECORD_STATE_VALID + "'";
                    Object account = memberDao.getUniqueBySql(sql);
                    cache.put(principal,account);
                    userIdcache.put("account_"+partyUser.getId(), principal);
                    return account;
                }
                return object;
            }
            if (principal instanceof AccountEntity) {
                Object object =  cache.get(principal,AccountEntity.class);
                if(null == object || returnFullEntity) {
                    AccountEntity shiroAccount = (AccountEntity) principal;
                    String sql = "select * from t_account where id = '" + shiroAccount.getId() + "' and userState = '"
                            + EntityContext.RECORD_STATE_VALID + "'";
                    Object account = accountDao.getUniqueBySql(sql);
                    cache.put(principal,account);
                    userIdcache.put("account_"+shiroAccount.getId(), principal);
                    return account;
                }
                return object;
            } else if (principal instanceof PlatformUserEntity) {
                PlatformUserEntity object =  cache.get(principal,PlatformUserEntity.class);
                if(null == object || returnFullEntity) {
                    PlatformUserEntity shiroPlatformUser = (PlatformUserEntity) principal;
                    String sql = "select * from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '"
                            + EntityContext.RECORD_STATE_VALID + "'";
                    Object platformUser = platformUserDao.getUniqueBySql(sql);
                    cache.put(principal,platformUser);
                    userIdcache.put("user_"+shiroPlatformUser.getId(), principal);
                    return platformUser;
                }
                return object;
            }
            return null;
        } else {
            return null;
        }
    }

}
