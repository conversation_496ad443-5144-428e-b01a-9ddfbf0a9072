package com.foshan.service.businessOpportunity.impl;

import com.foshan.dao.businessOpportunity.IBusinessOpportunityDao;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.businessOpportunity.BusinessOpportunity;
import com.foshan.form.businessOpportunity.request.opportunity.*;
import com.foshan.form.businessOpportunity.response.BusinessOpportunityDetailRes;
import com.foshan.form.businessOpportunity.response.BusinessOpportunityForm;
import com.foshan.form.businessOpportunity.response.BusinessOpportunityListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.businessOpportunity.IBusinessOpportunityService;
import org.springframework.beans.BeanUtils;
import com.foshan.dao.generic.Page;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.query.Query;
import org.hibernate.transform.Transformers;

import javax.validation.Validator;
import javax.validation.ConstraintViolation;
import java.util.Set;

import java.util.List;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;
import java.sql.Timestamp;
import java.util.Date;
import java.text.SimpleDateFormat;

@Service("businessOpportunityService")
@Transactional
public class BusinessOpportunityServiceImpl extends GenericBusinessService implements IBusinessOpportunityService {

    private static final Logger logger = LoggerFactory.getLogger(BusinessOpportunityServiceImpl.class);
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private IBusinessOpportunityDao businessOpportunityDao;

    @Autowired
    private Validator validator;

    private String formatDate(Date date) {
        return date != null ? dateFormat.format(date) : null;
    }

    @Override
    public IResponse addBusinessOpportunity(BusinessOpportunityAddReq req) {
        GenericResponse response = new GenericResponse();
        Object userObject = getPrincipal(false);
        AccountEntity user = null;
        if (userObject == null) {
            response.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            response.setRetInfo("用户未登录");
            return response;
        } else {
            user = (AccountEntity) userObject;
        }

        try {
            // 参数校验
            if (req == null) {
                response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_PARAM_ERROR_CODE);
                response.setRetInfo("请求参数不能为空");
                return response;
            }

            if (req.getSubmitStatus() == 1) {
                // 使用validator进行校验
                Set<ConstraintViolation<BusinessOpportunityAddReq>> violations = validator.validate(req);
                if (!violations.isEmpty()) {
                    response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_PARAM_ERROR_CODE);
                    response.setRetInfo(violations.iterator().next().getMessage());
                    return response;
                }

                // 特殊业务规则校验
                if (req.getOpportunityType() == 1 && req.getLineType() == null) {
                    response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_PARAM_ERROR_CODE);
                    response.setRetInfo("企业数据专线类型必须选择专线类型");
                    return response;
                }
            }
            // 通过校验，执行保存操作
            BusinessOpportunity businessOpportunity = new BusinessOpportunity();
            BeanUtils.copyProperties(req, businessOpportunity);
            businessOpportunity.setAccount(user);
            businessOpportunityDao.save(businessOpportunity);
            response.setRet(ResponseContext.RES_SUCCESS_CODE);
            response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("创建商机失败", e);
            response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_CODE);
            response.setRetInfo(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_INFO);
        }
        return response;
    }

    @Override
    public IResponse updateBusinessOpportunity(BusinessOpportunityUpdateReq req) {
        GenericResponse response = new GenericResponse();
        Object userObject = getPrincipal(true);
        AccountEntity user = null;
        if (userObject == null) {
            response.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            response.setRetInfo("用户未登录");
            return response;
        } else {
            user = (AccountEntity) userObject;
        }

        try {
            BusinessOpportunity businessOpportunity = businessOpportunityDao.get(req.getId());
            if (businessOpportunity != null) {
                if (!businessOpportunity.getAccount().getId().equals(user.getId())) {
                    response.setRet(ResponseContext.RES_PERM_ERROR_CODE);
                    response.setRetInfo("权限不足");
                    return response;
                }
                BeanUtils.copyProperties(req, businessOpportunity);
                businessOpportunityDao.update(businessOpportunity);
                response.setRet(ResponseContext.RES_SUCCESS_CODE);
                response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_NOT_EXIST_CODE);
                response.setRetInfo(ResponseContext.RES_BUSINESS_OPPORTUNITY_NOT_EXIST_INFO);
            }
        } catch (Exception e) {
            logger.error("更新商机失败", e);
            response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_CODE);
            response.setRetInfo(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_INFO);
        }
        return response;
    }

    @Override
    public IResponse deleteBusinessOpportunity(BusinessOpportunityDeleteReq req) {
        GenericResponse response = new GenericResponse();
        try {
            BusinessOpportunity businessOpportunity = businessOpportunityDao.get(req.getId());
            if (businessOpportunity != null) {
                businessOpportunityDao.delete(businessOpportunity);
                response.setRet(ResponseContext.RES_SUCCESS_CODE);
                response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_NOT_EXIST_CODE);
                response.setRetInfo(ResponseContext.RES_BUSINESS_OPPORTUNITY_NOT_EXIST_INFO);
            }
        } catch (Exception e) {
            logger.error("删除商机失败", e);
            response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_CODE);
            response.setRetInfo(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_INFO);
        }
        return response;
    }

    @Override
    public IResponse getBusinessOpportunityDetail(BusinessOpportunityDetailReq req) {
        BusinessOpportunityDetailRes response = new BusinessOpportunityDetailRes();
        try {
            BusinessOpportunity businessOpportunity = businessOpportunityDao.get(req.getId());
            if (businessOpportunity != null) {
                BusinessOpportunityForm form = new BusinessOpportunityForm();
                BeanUtils.copyProperties(businessOpportunity, form);
                response.setData(form);
                response.setRet(ResponseContext.RES_SUCCESS_CODE);
                response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_NOT_EXIST_CODE);
                response.setRetInfo(ResponseContext.RES_BUSINESS_OPPORTUNITY_NOT_EXIST_INFO);
            }
        } catch (Exception e) {
            logger.error("获取商机详情失败", e);
            response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_CODE);
            response.setRetInfo(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_INFO);
        }
        return response;
    }

    @Override
    public IResponse getBusinessOpportunityList(BusinessOpportunityQueryReq req) {
        BusinessOpportunityListRes response = new BusinessOpportunityListRes();
        Page<BusinessOpportunity> page = new Page<>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
        try {
            StringBuilder hql = new StringBuilder("from BusinessOpportunity where 1=1")
                    .append(StringUtils.isNotBlank(req.getKeyword()) ?
                            " and (contactName like '%" + req.getKeyword() + "%' or contactPhone like '%" +
                                    req.getKeyword() + "%' or address like '%" + req.getKeyword() + "%')" : "")
                    .append(req.getOpportunityType() != null ?
                            " and opportunityType = " + req.getOpportunityType() : "")
                    .append(req.getStatus() != null ?
                            " and status = " + req.getStatus() : "")
                    .append(StringUtils.isNotBlank(req.getCreator()) ?
                            " and creator = '" + req.getCreator() + "'" : "")
                    .append(null != req.getSubmitStatus() ? " and submitStatus = " + req.getSubmitStatus() : " and submitStatus = 1")
                    .append(req.getStartTime() != null ?
                            " and createTime >= '" + req.getStartTime() + "'" : "")
                    .append(req.getEndTime() != null ?
                            " and createTime <= '" + req.getEndTime() + "'" : "")
                    .append(" order by createTime desc");

            page = businessOpportunityDao.queryPage(page, hql.toString());

            // 转换并设置响应数据
            response.setData(page.getResultList().stream()
                    .map(opportunity -> {
                        BusinessOpportunityForm form = new BusinessOpportunityForm();
                        BeanUtils.copyProperties(opportunity, form);
                        form.setRegisterDate(formatDate(opportunity.getRegisterDate()));
                        return form;
                    })
                    .collect(Collectors.toList()));
            response.setTotal(page.getTotalCount());
            response.setCurrentPage(page.getCurrentPage());
            response.setRet(ResponseContext.RES_SUCCESS_CODE);
            response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("获取商机列表失败", e);
            response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_CODE);
            response.setRetInfo(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_INFO);
        }
        return response;
    }


    @Override
    public IResponse getExpiringContracts() {
        GenericResponse response = new GenericResponse();
        try {
            // TODO: 实现查询即将到期合约的逻辑
            response.setRet(ResponseContext.RES_SUCCESS_CODE);
            response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("查询即将到期合约失败", e);
            response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_CODE);
            response.setRetInfo(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_INFO);
        }
        return response;
    }

    @Override
    public IResponse getNearbyBusinessOpportunities(BusinessOpportunityNearbyReq req) {
        BusinessOpportunityListRes response = new BusinessOpportunityListRes();

        try {
            if (req == null || req.getLatitude() == null || req.getLongitude() == null) {
                response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_PARAM_ERROR_CODE);
                response.setRetInfo("经纬度参数不能为空");
                return response;
            }

            // 使用Haversine公式计算距离
            String distanceFormula = String.format("6371000 * 2 * ASIN(SQRT(" +
                            "POWER(SIN((%f - RADIANS(latitude)) / 2), 2) + " +
                            "COS(%f) * COS(RADIANS(latitude)) * " +
                            "POWER(SIN((%f - RADIANS(longitude)) / 2), 2)" +
                            "))",
                    Math.toRadians(req.getLatitude()),
                    Math.toRadians(req.getLatitude()),
                    Math.toRadians(req.getLongitude()));

            StringBuilder hql = new StringBuilder()
                    .append("SELECT *, (").append(distanceFormula).append(") as distance ")
                    .append("FROM t_business_opportunity bo ")
                    .append("WHERE (").append(distanceFormula).append(") <= ").append(req.getRadius())
                    .append(" ORDER BY distance");

            System.out.println(hql.toString());
            Query query = businessOpportunityDao.createSQLQuery(hql.toString())
                    .setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

            // 获取总记录数
            Integer totalCount = query.getResultList().size();

            // 设置分页
            query.setMaxResults(req.getPageSize() != null ? req.getPageSize() : 10);
            query.setFirstResult((req.getRequestPage() != null ? req.getRequestPage() - 1 : 0) *
                    (req.getPageSize() != null ? req.getPageSize() : 10));

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> resultList = query.getResultList();

            // 转换并设置响应数据
            List<BusinessOpportunityForm> opportunities = resultList.stream().map(map -> {
                BusinessOpportunityForm form = new BusinessOpportunityForm();
                // 从Map中获取各个字段并设置到form对象中
                form.setId(((Number) map.get("id")).intValue());
                form.setContactName((String) map.get("contactName"));
                form.setContactPhone((String) map.get("contactPhone"));
                form.setWechat((String) map.get("wechat"));
                form.setAddress((String) map.get("address"));
                form.setLongitude(((Number) map.get("longitude")).doubleValue());
                form.setLatitude(((Number) map.get("latitude")).doubleValue());
                form.setOpportunityType(((Number) map.get("opportunityType")).intValue());
                form.setRegisterDate(formatDate((Date) map.get("registerDate")));
                form.setStatus(((Number) map.get("status")).intValue());
                form.setUsingCompetitor((Boolean) map.get("usingCompetitor"));
                form.setCompetitorName((String) map.get("competitorName"));
                form.setCompetitorPackage((String) map.get("competitorPackage"));
                form.setCompetitorContractEndDate(formatDate((Date) map.get("competitorContractEndDate")));
                form.setLineType(map.get("lineType") != null ? ((Number) map.get("lineType")).intValue() : null);
                form.setBandwidthRequirement(map.get("bandwidthRequirement") != null ? ((Number) map.get("bandwidthRequirement")).intValue() : null);
                form.setUsageScenario((String) map.get("usageScenario"));
                form.setRemark((String) map.get("remark"));
                form.setCreator((String) map.get("creator"));
                form.setLastFollowTime((Date) map.get("lastFollowTime"));
                form.setLastFollower((String) map.get("lastFollower"));
                form.setCategory(map.get("category") != null ? ((Number) map.get("category")).intValue() : null);
                form.setCategoryRemark((String) map.get("categoryRemark"));
                form.setIsShared((Boolean) map.get("isShared"));
                form.setDistance(((Number) map.get("distance")).intValue());
                return form;
            }).collect(Collectors.toList());

            response.setData(opportunities);
            response.setTotal(totalCount);
            response.setCurrentPage(req.getRequestPage() != null ? req.getRequestPage() : 1);
            response.setRet(ResponseContext.RES_SUCCESS_CODE);
            response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("查询附近商机失败", e);
            response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_CODE);
            response.setRetInfo(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_INFO);
        }
        return response;
    }

} 