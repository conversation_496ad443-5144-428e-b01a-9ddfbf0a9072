package com.foshan.service.businessOpportunity.impl;

import com.foshan.entity.AccountEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.shop.MemberEntity;
import com.foshan.form.businessOpportunity.request.member.*;
import com.foshan.form.permission.response.AccountLoginRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.shop.MemberForm;
import com.foshan.form.shop.response.member.GetMemberInfoRes;
import com.foshan.form.shop.response.member.MemberLoginRes;
import com.foshan.model.permssion.AccountPrincipalModel;
import com.foshan.service.businessOpportunity.IBusinessOpportunityMemberService;
import com.foshan.service.permission.IAccountExtendLogin;
import com.foshan.service.permission.shiro.AbstractAccountRealmHandler;
import com.foshan.service.permission.shiro.AccountRealmHandlerContext;
import com.foshan.util.PhoneNumberUtil;
import com.foshan.util.RedisUtil;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.subject.Subject;
import org.hibernate.query.Query;
import org.hibernate.transform.Transformers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Validator;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service("businessOpportunityMemberService")
@Transactional
public class BusinessOpportunityMemberServiceImpl extends GenericBusinessService implements IBusinessOpportunityMemberService {

    private static final Logger logger = LoggerFactory.getLogger(BusinessOpportunityMemberServiceImpl.class);

    @Resource(name = "accountRealmHandlerContext")
    AccountRealmHandlerContext accountRealmHandlerContext;
    @Autowired(required = false)
    List<IAccountExtendLogin> accountExtendLoginList = new ArrayList<>();
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private Validator validator;

    private static final String VERIFICATION_CODE_PREFIX = "business_opportunity_verification_code:";
    private static final int VERIFICATION_CODE_EXPIRE_MINUTES = 5;

    @Override
    public IResponse login(BusinessOpportunityMemberLoginReq req, HttpServletRequest request) {
        AccountLoginRes response = new AccountLoginRes();
        try {
            // 参数校验
            if (req == null || req.getOpenId() == null || req.getOpenId().trim().isEmpty()) {
                response.setRet(ResponseContext.RES_PERM_ERROR_CODE);
                response.setRetInfo("openId不能为空");
                return response;
            }

            // 如果提供了手机号和验证码，需要验证
            if (req.getMobile() != null && req.getCode() != null) {
                if (!PhoneNumberUtil.isValidPhoneNumber(req.getMobile())) {
                    response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_PARAM_ERROR_CODE);
                    response.setRetInfo("手机号格式不正确");
                    return response;
                }

                String cachedCode = redisUtil.get(VERIFICATION_CODE_PREFIX + req.getMobile());
                if (cachedCode == null || !cachedCode.equals(req.getCode())) {
                    response.setRet(ResponseContext.RES_VERIF_CODE_ERROR_CODE);
                    response.setRetInfo(ResponseContext.RES_VERIF_CODE_ERROR_INFO);
                    return response;
                }

                // 验证通过后删除验证码
                redisUtil.delete(VERIFICATION_CODE_PREFIX + req.getMobile());
            }

            Subject curUser = SecurityUtils.getSubject();
            if (!curUser.isAuthenticated()) {// 本次会话首次登录，根据登录类型分配不同处理器进行用户登录凭证处理。
                AbstractAccountRealmHandler accountRealmHandler = accountRealmHandlerContext.getInstance(req.getType());
                response = (AccountLoginRes) accountRealmHandler.login(req, request, curUser);
                if (!response.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
                    return response;
                }
                //成功登录，处理登录前逻辑
                accountExtendLoginList.forEach(o->{
                    o.doBeforeLogin(curUser);
                });
                response.setRet(ResponseContext.RES_SUCCESS_CODE);
                response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                return response;
            } else {// 已登录的情况，如本次登录用户与已登录用户的类型不同，则提示其重新登录，与已登录用户类型一样则返回现会话的用户信息。
                Object accountObj = getPrincipal(false);
                if (!(accountObj instanceof AccountPrincipalModel)) {
                    response.setRet(ResponseContext.RES_PERM_ERROR_CODE);
                    response.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO + "已登录其他类型用户，请注销后重新登录。");
                    return response;
                } else {
                    response.setRet(ResponseContext.RES_PERM_SIGNEDIN_CODE);
                    response.setRetInfo(ResponseContext.RES_PERM_SIGNEDIN_INFO);
                    return response;
                }
            }

        } catch (Exception e) {
            logger.error("会员登录失败", e);
            response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_CODE);
            response.setRetInfo("会员登录失败");
        }
        return response;
    }

    @Override
    public IResponse getBusinessOpportunityMemberInfo(BusinessOpportunityMemberInfoReq req) {
        GetMemberInfoRes response = new GetMemberInfoRes();
        try {

            Object accountObj = getPrincipal(true);
            MemberForm memberForm = new MemberForm();
            if (null != accountObj) {
                if (accountObj instanceof AccountEntity) {
                    AccountEntity account = (AccountEntity) accountObj;
                    memberForm.setUserName(account.getUserName());
                    memberForm.setUserCode(account.getUserCode());
                    memberForm.setMemberId(account.getId());
                    memberForm.setPhone(account.getPhone());
                    memberForm.setEmail(account.getEmail());
                    memberForm.setWeixinAvatar(account.getWeixinAvatar());
                    memberForm.setWxEnterpriceDepartmentName(account.getWxEnterpriceDepartmentName());
                    memberForm.setWxEnterpricePosition(account.getWxEnterpricePosition());
                    response.setMemberForm(memberForm);
                }else if (accountObj instanceof AccountPrincipalModel) {
                    AccountPrincipalModel accountPrincipalModel = (AccountPrincipalModel) accountObj;
                    memberForm.setUserName(accountPrincipalModel.getUserName());
                    memberForm.setUserCode(accountPrincipalModel.getUserCode());
                    memberForm.setMemberId(accountPrincipalModel.getId());
                    memberForm.setPhone(accountPrincipalModel.getPhone());
                    memberForm.setEmail(accountPrincipalModel.getWxEnterpriceEmail());
                    memberForm.setWxEnterpriceDepartmentName(accountPrincipalModel.getWxEnterpriceDepartmentName());
                    memberForm.setWxEnterpricePosition(accountPrincipalModel.getWxEnterpricePosition());
                    response.setMemberForm(memberForm);
                }else {
                    response.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
                    response.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
                    return response;
                }
            }else if (null != req.getUserCode()) {
                String sql = "SELECT * FROM t_account a WHERE userCode='" +req.getUserCode()+ "'";
                @SuppressWarnings("rawtypes")
                Query query = accountDao.createSQLQuery(sql);
                @SuppressWarnings({ "rawtypes", "deprecation" })
                Optional acocuntObj = query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).uniqueResultOptional();

                if (acocuntObj.isPresent()) {
                    Map accountMap = (Map) acocuntObj.get();
                    memberForm.setUserName((String)accountMap.get("userName"));
                    memberForm.setUserCode((String)accountMap.get("userCode"));
                    memberForm.setPhone((String)accountMap.get("phone"));
                    memberForm.setEmail((String)accountMap.get("wxEnterpriceEmail"));
                    memberForm.setWxEnterpriceDepartmentName((String)accountMap.get("wxEnterpriceDepartmentName"));
                    memberForm.setWxEnterpricePosition((String)accountMap.get("wxEnterpricePosition"));
                    response.setMemberForm(memberForm);
                } else {
                    response.setRet(ResponseContext.RES_PERM_NONEXISTENT_CODE);
                    response.setRetInfo(ResponseContext.RES_PERM_NONEXISTENT_INFO);
                    return response;
                }

            } else {
                response.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
                response.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
                return response;
            }

            response.setRet(ResponseContext.RES_SUCCESS_CODE);
            response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("获取商机会员信息失败", e);
            response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_CODE);
            response.setRetInfo("获取商机会员信息失败");
        }
        return response;
    }

    @Override
    public IResponse updateBusinessOpportunityMemberInfo(BusinessOpportunityMemberUpdateReq req) {
        GenericResponse response = new GenericResponse();
        try {
            if (req == null || req.getOpenId() == null || req.getOpenId().trim().isEmpty()) {
                response.setRet(ResponseContext.RES_PERM_ERROR_CODE);
                response.setRetInfo("openId不能为空");
                return response;
            }

            if (!PhoneNumberUtil.isValidPhoneNumber(req.getMobile())) {
                response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_PARAM_ERROR_CODE);
                response.setRetInfo("手机号格式不正确");
                return response;
            }

            // TODO: 实现更新会员信息的逻辑

            response.setRet(ResponseContext.RES_SUCCESS_CODE);
            response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("更新商机会员信息失败", e);
            response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_CODE);
            response.setRetInfo("更新商机会员信息失败");
        }
        return response;
    }

    @Override
    public IResponse bindBusinessOpportunityMemberMobile(BusinessOpportunityMemberBindReq req) {
        GenericResponse response = new GenericResponse();
        try {
            if (req == null || req.getOpenId() == null || req.getOpenId().trim().isEmpty()) {
                response.setRet(ResponseContext.RES_PERM_ERROR_CODE);
                response.setRetInfo("openId不能为空");
                return response;
            }

            if (!PhoneNumberUtil.isValidPhoneNumber(req.getMobile())) {
                response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_PARAM_ERROR_CODE);
                response.setRetInfo("手机号格式不正确");
                return response;
            }

            String cachedCode = redisUtil.get(VERIFICATION_CODE_PREFIX + req.getMobile());
            if (cachedCode == null || !cachedCode.equals(req.getCode())) {
                response.setRet(ResponseContext.RES_VERIF_CODE_ERROR_CODE);
                response.setRetInfo(ResponseContext.RES_VERIF_CODE_ERROR_INFO);
                return response;
            }

            // TODO: 实现绑定手机号的逻辑

            redisUtil.delete(VERIFICATION_CODE_PREFIX + req.getMobile());
            response.setRet(ResponseContext.RES_SUCCESS_CODE);
            response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("绑定商机会员手机号失败", e);
            response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_ERROR_CODE);
            response.setRetInfo("绑定商机会员手机号失败");
        }
        return response;
    }

    @Override
    public IResponse sendVerificationCode(BusinessOpportunityMemberSendCodeReq req) {
        GenericResponse response = new GenericResponse();
        try {
            if (!PhoneNumberUtil.isValidPhoneNumber(req.getMobile())) {
                response.setRet(ResponseContext.RES_BUSINESS_OPPORTUNITY_PARAM_ERROR_CODE);
                response.setRetInfo("手机号格式不正确");
                return response;
            }

            String verificationCode = String.format("%06d", new Random().nextInt(1000000));
            redisUtil.setEx(VERIFICATION_CODE_PREFIX + req.getMobile(), verificationCode, VERIFICATION_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

            // TODO: 调用短信服务发送验证码

            response.setRet(ResponseContext.RES_SUCCESS_CODE);
            response.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("发送验证码失败", e);
            response.setRet(ResponseContext.RES_SEND_SMS_CODE);
            response.setRetInfo(ResponseContext.RES_SEND_SMS_INFO);
        }
        return response;
    }
} 