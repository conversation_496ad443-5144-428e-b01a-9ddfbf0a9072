package com.foshan.service.businessOpportunity;

import com.foshan.form.businessOpportunity.request.opportunity.*;
import com.foshan.form.response.IResponse;

public interface IBusinessOpportunityService {
    
    /**
     * 添加商机
     */
    IResponse addBusinessOpportunity(BusinessOpportunityAddReq req);


    /**
     * 更新商机
     */
    IResponse updateBusinessOpportunity(BusinessOpportunityUpdateReq req);

    /**
     * 删除商机
     */
    IResponse deleteBusinessOpportunity(BusinessOpportunityDeleteReq req);

    /**
     * 获取商机详情
     */
    IResponse getBusinessOpportunityDetail(BusinessOpportunityDetailReq req);

    /**
     * 分页查询商机列表
     */
    IResponse getBusinessOpportunityList(BusinessOpportunityQueryReq req);



    /**
     * 查询即将到期的友商合约
     */
    IResponse getExpiringContracts();

    /**
     * 查询附近的商机列表
     * @param req 包含经纬度和查询半径的请求参数
     * @return 商机列表响应
     */
    IResponse getNearbyBusinessOpportunities(BusinessOpportunityNearbyReq req);

}