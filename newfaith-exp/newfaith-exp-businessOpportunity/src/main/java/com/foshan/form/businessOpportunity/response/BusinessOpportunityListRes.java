package com.foshan.form.businessOpportunity.response;

import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "商机列表响应")
public class BusinessOpportunityListRes extends BasePageResponse {
    @ApiModelProperty("商机列表")
    private List<BusinessOpportunityForm> data;
}