package com.foshan.form.businessOpportunity.request.opportunity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;

@Data
@ApiModel(description = "更新商机请求")
public class BusinessOpportunityUpdateReq {
    @ApiModelProperty("商机ID")
    private Integer id;

    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("微信号")
    private String wechat;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private Double longitude;

    @ApiModelProperty("纬度")
    private Double latitude;

    @ApiModelProperty("商机类型（1：企业数据专线，2：其他类型...）")
    private Integer opportunityType;

    @ApiModelProperty("商机状态（1：未接触，2：已接触，3：已签约，4：已支付，5：已失效）")
    private Integer status;

    @ApiModelProperty("是否使用友商产品（0：否，1：是）")
    private Boolean usingCompetitor;

    @ApiModelProperty("友商名称")
    private String competitorName;

    @ApiModelProperty("友商套餐名称")
    private String competitorPackage;

    @ApiModelProperty("友商合约到期日期")
    private Timestamp competitorContractEndDate;

    @ApiModelProperty("专线类型（1：数据专线，2：互联网专线）")
    private Integer lineType;

    @ApiModelProperty("带宽需求(Mbps)")
    private Integer bandwidthRequirement;

    @ApiModelProperty("使用场景描述")
    private String usageScenario;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否共享（0：否，1：是）")
    private Boolean isShared;
} 