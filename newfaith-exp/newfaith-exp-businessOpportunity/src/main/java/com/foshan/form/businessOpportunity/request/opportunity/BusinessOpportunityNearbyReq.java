package com.foshan.form.businessOpportunity.request.opportunity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "查询附近商机请求")
public class BusinessOpportunityNearbyReq {
    @ApiModelProperty("经度")
    private Double longitude;

    @ApiModelProperty("纬度")
    private Double latitude;

    @ApiModelProperty("查询半径（单位：米），默认5000米")
    private Double radius = 5000.0;

    @ApiModelProperty("每页记录数")
    private Integer pageSize = 10;

    @ApiModelProperty("当前页码")
    private Integer requestPage = 1;
} 