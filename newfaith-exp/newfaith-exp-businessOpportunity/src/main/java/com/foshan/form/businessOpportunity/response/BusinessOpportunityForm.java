package com.foshan.form.businessOpportunity.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel(description = "商机信息")
public class BusinessOpportunityForm {
    @ApiModelProperty("商机ID")
    private Integer id;

    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("微信号")
    private String wechat;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("经度")
    private Double longitude;

    @ApiModelProperty("纬度")
    private Double latitude;

    @ApiModelProperty("商机类型（1：企业数据专线，2：其他类型...）")
    private Integer opportunityType;

    @ApiModelProperty("商机登记日期")
    private String registerDate;

    @ApiModelProperty("商机状态（1：未接触，2：已接触，3：已签约，4：已支付，5：已失效）")
    private Integer status;

    @ApiModelProperty("是否使用友商产品（0：否，1：是）")
    private Boolean usingCompetitor;

    @ApiModelProperty("友商名称")
    private String competitorName;

    @ApiModelProperty("友商套餐名称")
    private String competitorPackage;

    @ApiModelProperty("友商合约到期日期")
    private String competitorContractEndDate;

    @ApiModelProperty("专线类型（1：数据专线，2：互联网专线）")
    private Integer lineType;

    @ApiModelProperty("带宽需求(Mbps)")
    private Integer bandwidthRequirement;

    @ApiModelProperty("使用场景描述")
    private String usageScenario;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("最后跟进时间")
    private Date lastFollowTime;

    @ApiModelProperty("最后跟进人")
    private String lastFollower;

    @ApiModelProperty("商机分类（1：热商机，2：冷商机）")
    private Integer category;

    @ApiModelProperty("商机分类备注")
    private String categoryRemark;

    @ApiModelProperty("是否共享（0：否，1：是）")
    private Boolean isShared;

    @ApiModelProperty("距离（单位：米）")
    private Integer distance;
} 