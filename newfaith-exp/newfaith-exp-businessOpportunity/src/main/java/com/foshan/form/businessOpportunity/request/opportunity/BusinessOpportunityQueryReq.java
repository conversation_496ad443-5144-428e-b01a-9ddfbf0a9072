package com.foshan.form.businessOpportunity.request.opportunity;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;

@Data
@ApiModel(description = "商机查询请求")
public class BusinessOpportunityQueryReq extends BasePageRequest {


    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("商机类型")
    private Integer opportunityType;

    @ApiModelProperty("商机状态")
    private Integer status;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("商机分类")
    private Integer category;

    @ApiModelProperty("是否共享")
    private Boolean isShared;

    @ApiModelProperty("提交状态（0：暂存，1：已提交）")
    private Integer submitStatus = 1;
    
    @ApiModelProperty("开始时间")
    private String startTime;
    
    @ApiModelProperty("结束时间")
    private String endTime;
} 