package com.foshan.form.businessOpportunity.request.member;

import com.foshan.form.permission.request.AccountLoginReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "商机会员登录请求")
public class BusinessOpportunityMemberLoginReq extends AccountLoginReq {
    
    @NotBlank(message = "openId不能为空")
    @ApiModelProperty(value = "微信openId", required = true)
    private String openId;
    
    @ApiModelProperty("手机号")
    private String mobile;
    
    @ApiModelProperty("验证码")
    private String code;
} 