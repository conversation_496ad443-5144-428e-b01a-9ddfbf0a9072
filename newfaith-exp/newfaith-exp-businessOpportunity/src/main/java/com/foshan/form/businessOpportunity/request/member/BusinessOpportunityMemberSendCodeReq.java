package com.foshan.form.businessOpportunity.request.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;

@Data
@ApiModel(description = "发送验证码请求")
public class BusinessOpportunityMemberSendCodeReq {
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "请输入正确的手机号码")
    @ApiModelProperty(value = "手机号", required = true)
    private String mobile;
} 