package com.foshan.form.businessOpportunity.response;

import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "商机详情响应")
public class BusinessOpportunityDetailRes extends GenericResponse {
    @ApiModelProperty("商机信息")
    private BusinessOpportunityForm data;
} 