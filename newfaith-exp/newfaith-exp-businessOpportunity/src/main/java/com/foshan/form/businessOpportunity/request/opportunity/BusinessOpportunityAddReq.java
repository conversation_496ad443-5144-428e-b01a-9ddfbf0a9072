package com.foshan.form.businessOpportunity.request.opportunity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.*;
import org.hibernate.validator.constraints.Length;
import java.sql.Timestamp;

@Data
@ApiModel(description = "添加商机请求")
public class BusinessOpportunityAddReq {

    @NotBlank(message = "联系人不能为空")
    @Length(max = 50, message = "联系人姓名长度不能超过50个字符")
    @ApiModelProperty("联系人姓名")
    private String contactName;

    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "请输入正确的手机号码")
    @ApiModelProperty("联系电话")
    private String contactPhone;

    @Length(max = 50, message = "微信号长度不能超过50个字符")
    @ApiModelProperty("微信号")
    private String wechat;

    @NotBlank(message = "详细地址不能为空")
    @Length(max = 200, message = "地址长度不能超过200个字符")
    @ApiModelProperty("详细地址")
    private String address;

    @NotNull(message = "经度不能为空")
    @ApiModelProperty("经度")
    private Double longitude;

    @NotNull(message = "纬度不能为空")
    @ApiModelProperty("纬度")
    private Double latitude;

    @NotNull(message = "商机类型不能为空")
    @ApiModelProperty("商机类型（1：企业数据专线，2：其他类型...）")
    private Integer opportunityType;

    @NotNull(message = "登记日期不能为空")
    @ApiModelProperty("商机登记日期")
    private Timestamp registerDate;

    @ApiModelProperty("商机状态（1：未接触，2：已接触，3：已签约，4：已支付，5：已失效）")
    private Integer status;

    @ApiModelProperty("是否使用友商产品（0：否，1：是）")
    private Boolean usingCompetitor;

    @Length(max = 100, message = "友商名称长度不能超过100个字符")
    @ApiModelProperty("友商名称")
    private String competitorName;

    @Length(max = 100, message = "友商套餐名称长度不能超过100个字符")
    @ApiModelProperty("友商套餐名称")
    private String competitorPackage;

    @ApiModelProperty("友商合约到期日期")
    private Timestamp competitorContractEndDate;

    @ApiModelProperty("专线类型（1：数据专线，2：互联网专线）")
    private Integer lineType;

    @ApiModelProperty("带宽需求(Mbps)")
    private Integer bandwidthRequirement;

    @Length(max = 500, message = "使用场景描述长度不能超过500个字符")
    @ApiModelProperty("使用场景描述")
    private String usageScenario;

    @Length(max = 500, message = "备注长度不能超过500个字符")
    @ApiModelProperty("备注")
    private String remark;

    @NotNull(message = "是否共享不能为空")
    @ApiModelProperty("是否共享（0：否，1：是）")
    private Integer isShared = 0;

    @ApiModelProperty("商机分类（1：热商机，2：冷商机）")
    private Integer category;

    @ApiModelProperty("提交状态（0：暂存，1：已提交）")
    private Integer submitStatus = 1;
} 