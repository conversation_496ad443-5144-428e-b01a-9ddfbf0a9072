package com.foshan.entity.businessOpportunity;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.IEntityBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_business_opportunity")
@org.hibernate.annotations.Table(appliesTo = "t_business_opportunity",comment="商机")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class BusinessOpportunity implements IEntityBean {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @ApiModelProperty("联系人姓名")
    @Column(columnDefinition = "varchar(50) comment '联系人姓名'")
    private String contactName;

    @ApiModelProperty("联系电话")
    @Column(columnDefinition = "varchar(20) comment '联系电话'")
    private String contactPhone;

    @ApiModelProperty("微信号")
    @Column(columnDefinition = "varchar(50) comment '微信号'")
    private String wechat;

    @ApiModelProperty("详细地址")
    @Column(columnDefinition = "varchar(200) comment '详细地址'")
    private String address;

    @ApiModelProperty("经度")
    @Column(columnDefinition = "decimal(10,6) comment '经度'")
    private Double longitude;

    @ApiModelProperty("纬度")
    @Column(columnDefinition = "decimal(10,6) comment '纬度'")
    private Double latitude;

    @ApiModelProperty("商机类型（1：企业数据专线，2：其他类型...）")
    @Column(columnDefinition = "int comment '商机类型（1：企业数据专线，2：其他类型...）'")
    private Integer opportunityType;

    @ApiModelProperty("商机登记日期")
    @Column(columnDefinition = "timestamp comment '商机登记日期'")
    private Timestamp registerDate;

    @ApiModelProperty("商机状态（1：未接触，2：已接触，3：已签约，4：已支付，5：已失效）")
    @Column(columnDefinition = "int comment '商机状态（1：未接触，2：已接触，3：已签约，4：已支付，5：已失效）'")
    private Integer status;

    @ApiModelProperty("是否使用友商产品（0：否，1：是）")
    @Column(columnDefinition = "tinyint(1) comment '是否使用友商产品（0：否，1：是）'")
    private Boolean usingCompetitor;

    @ApiModelProperty("友商名称")
    @Column(columnDefinition = "varchar(100) comment '友商名称'")
    private String competitorName;

    @ApiModelProperty("友商套餐名称")
    @Column(columnDefinition = "varchar(100) comment '友商套餐名称'")
    private String competitorPackage;

    @ApiModelProperty("友商合约到期日期")
    @Column(columnDefinition = "timestamp comment '友商合约到期日期'")
    private Timestamp competitorContractEndDate;

    @ApiModelProperty("专线类型（1：数据专线，2：互联网专线）")
    @Column(columnDefinition = "int comment '专线类型（1：数据专线，2：互联网专线）'")
    private Integer lineType;

    @ApiModelProperty("带宽需求(Mbps)")
    @Column(columnDefinition = "int comment '带宽需求(Mbps)'")
    private Integer bandwidthRequirement;

    @ApiModelProperty("使用场景描述")
    @Column(columnDefinition = "varchar(500) comment '使用场景描述'")
    private String usageScenario;

    @ApiModelProperty("备注")
    @Column(columnDefinition = "varchar(500) comment '备注'")
    private String remark;

    @ApiModelProperty("创建人")
    @Column(columnDefinition = "varchar(50) comment '创建人'")
    private String creator;

    @ApiModelProperty("最后跟进时间")
    @Column(columnDefinition = "timestamp comment '最后跟进时间'")
    private Timestamp lastFollowTime;

    @ApiModelProperty("最后跟进人")
    @Column(columnDefinition = "varchar(50) comment '最后跟进人'")
    private String lastFollower;

    @ApiModelProperty("商机分类（1：热商机，2：冷商机）")
    @Column(columnDefinition = "int comment '商机分类（1：热商机，2：冷商机）'")
    private Integer category;

    @ApiModelProperty("商机分类备注")
    @Column(columnDefinition = "varchar(500) comment '商机分类备注（记录客户需求或拒绝原因）'")
    private String categoryRemark;

    @ApiModelProperty("上报用户")
    @ManyToOne
    @JoinColumn(name = "accountId", referencedColumnName = "id")
    private AccountEntity account;

    @ApiModelProperty("是否共享（0：否，1：是）")
    @Column(columnDefinition = "tinyint(1) comment '是否共享（0：否，1：是）'")
    private Boolean isShared;

    @ApiModelProperty("创建时间")
    @Column(columnDefinition = "timestamp default CURRENT_TIMESTAMP comment '创建时间'")
    private Timestamp createTime;
    
    @ApiModelProperty("提交状态（0：暂存，1：已提交）")
    @Column(columnDefinition = "tinyint(1) default 1 comment '提交状态（0：暂存，1：已提交）'")
    private Integer submitStatus;
} 