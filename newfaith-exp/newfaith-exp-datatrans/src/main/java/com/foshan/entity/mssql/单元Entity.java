package com.foshan.entity.mssql;

import java.math.BigDecimal;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "单元")
public class 单元Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2175062190263089612L;
	@Id
	private String 单元ID;
	private String 单元编号;
	/** @pdOid 3ac42c9e-22bd-467c-89a3-aea30a6c19da */
	private Integer 楼层;
	/** @pdOid 8d9302dd-c4ea-4516-b01b-c16f20a51889 */
	private String 房号;
	/** @pdOid 81a296cc-5138-45cd-b745-50b14af83763 */
	private String 规格;
	/** @pdOid 516b0964-0546-42d1-8469-9c0184466640 */
	private BigDecimal 建筑面积;
	/** @pdOid 29148cd7-59e2-4a4d-ac21-b81b84c84758 */
	public BigDecimal 使用面积;
	/** @pdOid 80a28da0-be17-4202-9d47-2735a556428e */
	public BigDecimal 收费面积;
	/** @pdOid 77636aa8-06a3-4137-b5d2-af1ab76d3e3e */
	public String 朝向;
	/** @pdOid c3b2d9ef-3df6-4e89-84ea-2a703dfc9c04 */
	public String 功能;
	/** @pdOid 11c9d75b-38c3-42fc-913e-53042a6c7c85 */
	public BigDecimal 附加面积;
	/** @pdOid 45a45d21-ce1f-4754-a774-7edb62c4c119 */
	public String 装修标准;
	/** @pdOid 552ac34b-8a93-440d-8fa2-5adf83c63938 */
	public String 备注;
	/** @pdOid 9dc79bf6-f5bf-463c-a42a-65d023d86af3 */
	public Integer ord房号;
	/** @pdOid 858b2b7b-0c51-40d8-b474-358ee043a575 */
	public Integer 是否审核;
	private String 销售单元编号;
	private String 楼阁ID;
	private String 楼梯ID;

}