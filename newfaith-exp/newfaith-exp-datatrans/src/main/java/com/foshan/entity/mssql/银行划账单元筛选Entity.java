package com.foshan.entity.mssql;


import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name = "银行划帐_单元筛选")
public class 银行划账单元筛选Entity implements IEntityBean  {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7806326877038558096L;
	@EmbeddedId
	@AttributeOverrides({ @AttributeOverride(name = "单元ID", column = @Column),
			@AttributeOverride(name = "收费项目", column = @Column) })
	private 银行划账单元筛选Id id;
}
