package com.foshan.entity.mssql;

import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name = "收费范围")
public class 收费范围Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6959194137651726269L;
	@EmbeddedId
	@AttributeOverrides({ @AttributeOverride(name = "项目ID", column = @Column),
			@AttributeOverride(name = "单元ID", column = @Column) })
	public 收费范围Id id;
	public Integer 是否审核;

}