package com.foshan.entity.mssql;

import java.math.BigDecimal;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "收费项目_其他收费")
public class 收费项目其他收费Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1224941176884382625L;

	@Id
	public String 收费项目;
	public BigDecimal 金额;
	public String 单位;
	public String 管理处;
	public String 类别;
	
	public 收费项目其他收费Entity(String 收费项目, BigDecimal 金额, String 单位, String 类别,String 管理处) {
		super();
		this.收费项目 = 收费项目;
		this.金额 = 金额;
		this.单位 = 单位;
		this.类别 = 类别;
		this.管理处=管理处;
	}

	
}