package com.foshan.entity.mssql;



import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "车场管理_车辆")
public class 车场管理车辆Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3065253761269154990L;
	@Id
	public String 车辆ID;
	/** @pdOid 26af741f-61d7-41ce-9dc2-b9e38b349fb8 */
	public String 车位ID;
	/** @pdOid 7ce32681-ca2c-47cf-850c-8bb2351e0501 */
	public String 车主ID;
	/** @pdOid f2d49369-b364-42d5-b6ef-301d5205df57 */
	public String 停车牌号;
	/** @pdOid f4bf6b1d-d205-49f1-91fb-ee196b1f28a3 */
	public String 车牌号;
	/** @pdOid 754a6971-c676-4b11-94d6-fba494f8462c */
	public String 行驶证号;
	/** @pdOid a7e21721-3cca-4b55-9075-5446d2aec350 */
	public String 车辆特征;
	/** @pdOid edb9b87a-26af-4364-8547-69bd7ced06e4 */
	public String ic卡号;
	/** @pdOid 743a03da-4864-4b4a-be4c-0767f74d4c1f */
	public Date 起租日期;
	/** @pdOid 238f9225-c828-4495-9e23-3dc9e9979086 */
	public Date 离退日期;
	/** @pdOid 9740b541-6073-413e-adcc-744ba40a4a10 */
	public String 车主类型;
	/** @pdOid 3a785c8c-d527-411a-98a0-f101d76bc738 */
	public String 备注;
	/** @pdOid 603d42b7-9c23-467a-9c97-06e7e7c12422 */
	public String 停放类型;
	/** @pdOid 60020380-4ffe-4d66-b9f4-23eb9e8cdc59 */
	public Integer 是否本区;
	/** @pdOid 27003754-924f-495c-98b2-78368c9ec1d2 */
	public String 车主住所ID;
	/** @pdOid 80dbb208-2658-4c57-8c54-45cc4b5aab15 */
	public Date 止租日期;
	/** @pdOid f44f5b06-6a2e-4fa2-bbc7-3bac8ba25b7c */
	public String 开票类型;
	/** @pdOid 1c2db65c-8546-4920-b0d6-58ba064973de */
	public String 购方名称;
	/** @pdOid c83d8857-5dfd-475a-beaa-55110bc7d177 */
	public String 购方地址电话;
	/** @pdOid 346d482d-e3b0-464a-8eb4-d452ce93ce8d */
	public String 购方银行帐号;
	/** @pdOid bb58725a-483b-4b6a-a3a9-0f6c386b96ef */
	public String 购方税号;
	/** @pdOid 6f0bd169-73f5-4f0a-89b5-59323997659b */
	public String 购方企业类型;
	/** @pdOid 83c52fca-d07c-4be3-acf5-f5e24d2d7fe9 */
	public String eMail;

}