package com.foshan.entity.mssql;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "应收款_减加")
public class 应收款减加Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2271807097618345600L;
	@Id
	public String 应收款ID;
	/** @pdOid f73b8c0d-6f31-45d2-8d70-6d4b890543a4 */
	public String 单元ID;
	/** @pdOid 9b030f45-e8cb-49be-8e8e-942b58531983 */
	public String 收费项目;
	/** @pdOid 7516b011-a138-45ae-aaf1-73e4e41a2a26 */
	public Date 应收日期;
	/** @pdOid 4fb993ee-d12a-4c07-91b4-725c319b1e56 */
	public BigDecimal 应收金额;
	/** @pdOid 5cae8cc3-296b-491f-aa52-93da009e46d9 */
	public BigDecimal 减加金额;
	/** @pdOid 0377be19-1c44-4907-a859-6a72a9e30506 */
	public BigDecimal 已收金额;
	/** @pdOid e8332057-b003-49ee-96b1-2ae52998f39e */
	public String 备注;
	/** @pdOid 6be38a1b-f329-4202-b6a6-ed1565b93331 */
	public Integer 是否审核;
	/** @pdOid 890ec486-2934-4bb7-bb2c-66121e91632f */
	public Date 款项属期;
	/** @pdOid be6ba743-**************-f47aba9e9ae4 */
	public Date 减加日期;
	/** @pdOid f535eef4-5cf5-498c-9349-220b719abcfc */
	public String 减加类型;
	/** @pdOid 71b82177-42b5-4ccf-bbee-11dd3ecf1aba */
	public String 减加人;
	/** @pdOid c02da0ed-d107-4ad7-a7e2-a93cc8980f3e */
	public String 批准人;

}