package com.foshan.entity.mssql;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "分摊设置_自定义分摊公式")
public class 分摊设置自定义分摊公式Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8227394592067605213L;
	@Id
	public String 公式名;
	/** @pdOid f5afd84a-e5af-4bbc-8c41-b7433e62e4e1 */
	public String 公式描述;
	/** @pdOid 9c365583-9267-4e2a-a78e-e71c243e07ff */
	public String 备注;

}