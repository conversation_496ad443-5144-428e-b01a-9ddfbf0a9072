package com.foshan.entity.mssql;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "总表_抄表")
public class 总表抄表Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2906319508523078954L;
	@Id
	public String id;
	/** @pdOid 3f3cc761-2fb4-4e4a-b0aa-7d7fdf43a93d */
	public BigDecimal 本次读数;
	/** @pdOid 42fec056-4101-4bc2-9552-23616e9fbe7f */
	public Date 本次读数日期;
	/** @pdOid 8ed0b556-f019-42b8-80f5-8eb4fd659a95 */
	public String 抄表人;
	/** @pdOid baf58d77-495d-4137-9236-0f93e2f5b179 */
	public Integer 是否归零;
	private String 总表ID;
	private String 上次读数ID;

}