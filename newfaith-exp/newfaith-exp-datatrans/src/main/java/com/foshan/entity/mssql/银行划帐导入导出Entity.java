package com.foshan.entity.mssql;

import java.math.BigDecimal;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name = "银行划帐_导入导出")
public class 银行划帐导入导出Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3566007582591518783L;
	@Id
	public long 导入导出ID;
	/** @pdOid 96fd009f-b714-4a8e-b851-2fd537675857 */
	public String 银行帐户;
	/** @pdOid 662d9abc-dd15-4cfa-8ace-37238f4d6514 */
	public BigDecimal 应收未收金额;
	/** @pdOid 942591a0-9129-4c95-88c4-923e3ea2f022 */
	public BigDecimal 当前结余款;
	/** @pdOid bd173de7-9b31-4b5b-9f1e-f4ad613140ea */
	public BigDecimal 应划金额;
	/** @pdOid cf394a34-397d-4b58-94e4-95a1d818ee6d */
	public Integer 划帐标志 = 0;
	/** @pdOid d257b9c5-db3e-4798-8e48-660ddd7d7438 */
	public String 收支id;
	/** @pdOid b865a6f4-0086-45e4-b8cf-1509afb616bf */
	public Integer 划帐编号;
	/** @pdOid 044b53b6-31c0-4335-80ff-c72266595d6d */
	public String 收费项目;
	private String 单元ID;
	private String 银行划帐ID;

}