package com.foshan.entity.mssql;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "住户管理_报修登记")
public class 住户管理报修登记Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2090979174694700166L;
	@Id
	public String 报修ID;
	/** @pdOid c8e6a1ae-aee8-4a3a-9fa5-8248476697f3 */
	public String 单元ID;
	/** @pdOid 9daf625b-5ba0-41cb-84c1-a5d293ba41ba */
	public String 维修单号;
	/** @pdOid b3329550-1c76-449b-b2bb-3fbe608b9c7a */
	public String 报修类别;
	/** @pdOid 3aee6ccc-361d-4e3f-866a-36aab7df8341 */
	public Date 报修日期;
	/** @pdOid 8729efaf-973e-479c-896e-4dc4f41d674d */
	public String 申请人;
	/** @pdOid 34ec7801-70a3-4d95-a4b8-6bf54e5e2f91 */
	public String 联系人;
	/** @pdOid 258174b3-8d76-4c85-b1bc-73c40113f1ff */
	public String 联系电话;
	/** @pdOid d2986256-979b-4f16-a1c3-fa38e47f3f8f */
	public String 维修地点;
	/** @pdOid 5f17eb98-23fa-4b99-a551-0cd59439a342 */
	public String 报修内容;
	/** @pdOid f06e0232-6897-43cb-9e94-24b519a91dae */
	public String 满意度;
	/** @pdOid 68fda66d-290c-4626-8217-7e78729b1732 */
	public String 领料单;
	/** @pdOid 10c50419-06d5-40ad-a971-2e1b4a7ba856 */
	public String 被派人;
	/** @pdOid 0c3c7ef9-c9bc-4d79-b5dd-74400b4983b6 */
	public Date 派工时间;
	/** @pdOid 2db00119-2a91-4cde-a2d0-47e4b3463cbf */
	public Date 完成时间;
	/** @pdOid dac5bbb6-59e0-4203-8d7b-0eff2e91cffa */
	public BigDecimal 材料费用;
	/** @pdOid 162dadc2-c093-478b-affe-0235f1aa680a */
	public BigDecimal 人工费用;
	/** @pdOid 97ad8905-fbc3-4173-8e01-861c9f05372c */
	public Integer 工时;
	/** @pdOid f7f2b38d-47e1-4eef-8c04-6963db9811fc */
	public String 验收人;
	/** @pdOid 1deec950-f730-4ee9-9eef-4cca9afe708b */
	public String 验收意见;
	/** @pdOid fdc5a19d-bd0a-428a-8bee-0b9df57edf74 */
	public Integer 是否完成;
	/** @pdOid 14d7addf-e8ab-47d5-b52a-e5b514a9b0e4 */
	public String 是否入帐;
	/** @pdOid 3c7116a6-3386-42ea-b8ae-93279f01e657 */
	public String 备注;
	/** @pdOid b483297c-d615-4694-9670-e6168df73b63 */
	public String 收费类别;
	/** @pdOid c14ef4b7-ca1c-4dae-9d0b-acbd53762843 */
	public String 维修人1;
	/** @pdOid 5ec931bc-2c93-4b08-9f25-97a5038cfbfb */
	public String 维修人2;
	/** @pdOid eb0b947d-5c2f-4535-a0f4-16e0d2e192f5 */
	public String 客户联系人;
	/** @pdOid a69a889c-5072-4641-92a6-fbec7bc6daff */
	public String 紧急度;
	/** @pdOid d5703d4a-754a-4ea2-bbc5-4cbc6c1d4c0a */
	public String 工程主管;
	/** @pdOid 8bcf6198-99a9-40cc-9b42-9ed00d9071f7 */
	public String 服务状态;
	/** @pdOid 2cfa43c8-c5d6-443a-85f3-f6f6b33a6e09 */
	public String 维修内容;



}