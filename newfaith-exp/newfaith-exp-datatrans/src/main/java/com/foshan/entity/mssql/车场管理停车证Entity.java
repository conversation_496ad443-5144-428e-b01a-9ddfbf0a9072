package com.foshan.entity.mssql;



import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "车场管理_停车证")
public class 车场管理停车证Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6882164885389170448L;
	@Id
	public String 停车证ID;
	/** @pdOid 08ba7676-7036-4cd8-ac18-38f959d2a83c */
	public String 车辆ID;
	/** @pdOid 234fede5-5b78-4a1f-bc8f-02a0abb0da70 */
	public String 停车证号;
	/** @pdOid 5b2f1cf5-a843-43f9-a597-cfc0fe49626c */
	public String 车主姓名;
	/** @pdOid 775c2eb8-fc60-4e1b-991c-07f73dfaf5e1 */
	public Date 起始日期;
	/** @pdOid 3f2ac009-3e87-43c9-bf37-50c8e8f149a8 */
	public Date 截止日期;
	/** @pdOid 65fd2bfe-c59b-4bf4-b1a4-e89042484237 */
	public String 经办人;
	/** @pdOid 1853beb5-d577-4bc7-a736-dd9e8f010ff6 */
	public Integer 作废;
	/** @pdOid 973c0fe9-7fb7-4ef3-8f69-095505b3b491 */
	public String 备注;

}