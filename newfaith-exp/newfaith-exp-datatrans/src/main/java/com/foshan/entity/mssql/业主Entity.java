package com.foshan.entity.mssql;



import java.util.Date;


import javax.persistence.Entity;

import javax.persistence.Id;

import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name = "业主")
public class 业主Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2737206254989566133L;
	@Id
	public String 业主ID;
	private String 单元ID;
	/** @pdOid c2cfaca9-ab27-4521-8def-2df3df41911d */
	public String 姓名;
	/** @pdOid 646a41cb-a429-486d-9868-8a3dfadff947 */
	public String 性别;
	/** @pdOid 8c917d27-3408-49a0-a84e-2e0dd95f0ca4 */
	public Date 出生日期;
	/** @pdOid 12fea084-4a09-44ee-8f02-137c8043edea */
	public String 民族;
	/** @pdOid fbd0f57e-23eb-4134-84b3-07b7d1a106e8 */
	public String 籍贯;
	/** @pdOid a0c6639d-2935-4ec0-b4e7-4c7fe6beb317 */
	public String 户口所在地派出所;
	/** @pdOid 9e55d8c3-6b63-428f-8f13-8720fca19368 */
	public String 工作单位;
	/** @pdOid 5e27c710-5d42-48bd-a935-a0cc580bd212 */
	public String 职务;
	/** @pdOid d9b6ec81-42c8-421c-bc78-508a249c4bad */
	public String 银行帐户;
	/** @pdOid abfbfa9b-12c9-4b8b-8a86-fc4be0cf154f */
	public String 联系地址;
	/** @pdOid 78df147d-8caa-4554-9bb3-79b05d4132aa */
	public String 邮政编码;
	/** @pdOid e4c32693-1132-48d2-ab31-79c6cabcceb2 */
	public String 电话;
	/** @pdOid 61ff6d8c-283d-47b5-b417-84c86d8b9fae */
	public String 证件号码;
	/** @pdOid 06c905f3-3208-4387-89f2-f1055566afa3 */
	public String 证件类型;
	/** @pdOid 55971b98-52e4-4e55-8d5a-6b7a0426b7ea */
	public String 国籍;
	/** @pdOid a6e3c533-4143-4b91-a2ce-261c4a85c523 */
	public String 房产证号;
	/** @pdOid 692bff69-90b8-44a3-b711-0009838d6e6a */
	public String 购房合同号;
	/** @pdOid 4071ba2f-a9b8-436a-a67b-8ecfeabcfbe5 */
	public Date 收楼日期;
	/** @pdOid 8e11b28f-2596-4b41-bad9-9755e19d5089 */
	public String 备注;
	/** @pdOid dac7f439-e802-4937-94e7-c45c805111f0 */
	public String 紧急联系人姓名;
	/** @pdOid 68d15543-fe16-4f3e-b8ba-c40640d782ab */
	public String 紧急联系人电话;
	/** @pdOid 37c32893-646b-4617-8bca-692d59186598 */
	public String 紧急联系人地址;
	/** @pdOid 869549c5-9554-4e0f-a748-7d7f3a0ed475 */
	public Integer 业主序号 = 1;
	/** @pdOid 6a4c35f6-90f4-4c2f-adc9-4063d3d03c8d */
	public String 微信手机号;
	/** @pdOid c8e8e941-54ea-4cb1-a710-c58e2d50e20e */
	public String 微信号;
	/** @pdOid 2908fbc7-5ce2-4ac9-8336-074c633b6490 */
	public Date 离退日期;
	
}