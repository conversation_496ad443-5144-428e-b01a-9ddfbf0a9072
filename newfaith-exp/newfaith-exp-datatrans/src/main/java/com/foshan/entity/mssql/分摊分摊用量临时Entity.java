package com.foshan.entity.mssql;

import java.math.BigDecimal;


import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name = "分摊_分摊用量_临时")
public class 分摊分摊用量临时Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3145870363646613761L;
	@Id
	private String 总表ID;
	public BigDecimal 分摊后用量;
	/** @pdOid 2ce8b7ff-c5b3-4fb2-9838-bf32026dcd9c */
	public BigDecimal 分摊后金额;
}