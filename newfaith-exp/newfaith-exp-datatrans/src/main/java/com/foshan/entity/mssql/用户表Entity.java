package com.foshan.entity.mssql;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "用户表")
public class 用户表Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 979789873704088093L;
	@Id
	public String 用户表ID;
	/** @pdOid cc733ac0-0c90-4a0b-9d08-e2fdfea5b8f3 */
	public BigDecimal 底数;
	/** @pdOid 7492c314-4ebc-4873-8f65-319488da008c */
	public String 安表地点;
	/** @pdOid a5dfd63d-34df-4192-8332-eac6128c1d41 */
	public Integer 损耗分摊 = 1;
	/** @pdOid 48d9e5a0-ed4b-4483-89ac-28dc2fe0c8b7 */
	public String 备注;
	/** @pdOid 21f48926-a0d8-4743-aa45-940539d79a68 */
	public Date 表坏日期;
	/** @pdOid 9e7429ef-8fee-48bf-a2cb-e0bfeabf2637 */
	public Integer 是否坏表;
	/** @pdOid 94991e30-417b-402e-ad4a-bae729a12586 */
	public BigDecimal 系数;
	public String 总表ID;
	public String 单元ID;
	public String 属性ID;





}