package com.foshan.entity.mssql;


import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name = "分摊_一级总表范围")
public class 分摊一级总表范围Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8107204747828332646L;
	@Id
	private String 总表ID;
	
	
	

}