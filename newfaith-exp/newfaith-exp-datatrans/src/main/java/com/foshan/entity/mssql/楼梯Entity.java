package com.foshan.entity.mssql;


import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "楼梯")
public class 楼梯Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3799480169541448602L;
	@Id
	public String 楼梯ID;
	public String 楼梯名称;
	private String 楼阁ID;
	private String 楼梯NO;


}