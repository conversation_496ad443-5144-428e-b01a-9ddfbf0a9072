package com.foshan.entity.mssql;


import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "银行划帐_id")
public class 银行划帐IdEntity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8415898515707523365L;
	@Id
	public long 银行划帐ID;
	/** @pdOid ba0c32a3-ca78-4d29-9d41-c39c81a4ba65 */
	public Date 起始日期;
	/** @pdOid 40a4ddd8-9be7-4bc8-9128-2fb03a59f9bf */
	public Date 截止日期;
	/** @pdOid 526d706a-e5fd-4a87-8a48-a55eb8f0893d */
	public Date 划出日期;
	/** @pdOid 7f98076f-4dcb-445b-9b23-ec82ea7574f6 */
	public Date 划入日期;
	/** @pdOid 850fa7e1-ba3f-4c47-ade7-b6b58b75920a */
	public String 银行;
	/** @pdOid 6c87602e-1316-4904-a7c6-a01f0d763a0e */
	public String 收费项目;
	/** @pdOid e9dcc2e1-2793-4fb3-ae77-aa31bf36a21e */
	public String 备注;
	/** @pdOid 5c9d71ed-d878-4390-b4d0-9f8b72e861d6 */
	public Integer 记账标志 = 0;
	/** @pdOid 03137985-0025-452d-ab67-bae331392bd2 */
	public String 管理处;
	/** @pdOid abbce471-41cc-49e1-904f-75b8b44b80b3 */
	public String 划帐类别;
	/** @pdOid 183a243b-26e0-4e2a-8a70-51a2a0b113ab */
	public String 合并导出id;
	/** @pdOid d0a39ad7-0dce-4e32-aebb-5d0e39cba423 */
	public String 合并导出备注;



}