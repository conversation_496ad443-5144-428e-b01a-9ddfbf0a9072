package com.foshan.entity.mssql;

import java.math.BigDecimal;

import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name = "公用分摊范围")
public class 公用分摊范围Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6595258843720034264L;
	@EmbeddedId
	@AttributeOverrides({ @AttributeOverride(name = "总表ID", column = @Column),
			@AttributeOverride(name = "单元ID", column = @Column) })
	private 公用分摊范围Id id;
	private BigDecimal 单元系数;

}