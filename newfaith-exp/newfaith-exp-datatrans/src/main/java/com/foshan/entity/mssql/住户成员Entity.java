package com.foshan.entity.mssql;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "住户成员")
public class 住户成员Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4767742002121909997L;
	@Id
	public String 住户成员ID;
	/** @pdOid c0fbe57b-994a-41b0-8a07-734b44fa788b */
	public String 户主ID;
	/** @pdOid 20bd3380-5486-4a37-8004-9d62590f696b */
	public String 姓名;
	/** @pdOid a714a184-ae79-4dbc-bb60-f019a0cb59c4 */
	public String 性别;
	/** @pdOid dabea7d6-41d6-4f96-9a9d-a1ad42723051 */
	public Date 出生日期;
	/** @pdOid 3680aeca-e97c-4f46-97ea-93339f41cd1a */
	public String 民族;
	/** @pdOid f526d1d5-461f-4ee6-a40b-d21f81804ad2 */
	public String 籍贯;
	/** @pdOid a0f7de48-2f71-4fd4-96fa-70130f1374d8 */
	public String 工作单位;
	/** @pdOid 6f4665ba-d826-4b3b-b595-6c30b7707f5f */
	public String 职务;
	/** @pdOid 35dddcef-8699-41dd-9d0f-ba091ce5ca1e */
	public String 联系地址;
	/** @pdOid 61f1e408-1746-4205-b65c-b471dd782965 */
	public String 邮政编码;
	/** @pdOid d5c777d6-22f9-4d64-81da-ecab6ed9200d */
	public String 电话;
	/** @pdOid cd9030ca-d52e-40f0-949e-ac21531d6511 */
	public String 证件号码;
	/** @pdOid 6ff219dc-8b47-4e6d-a03a-caaf86298f71 */
	public String 证件类型;
	/** @pdOid 9ede9a73-ec56-4d1e-86d4-fbbdbc2e071c */
	public String 与户主关系;
	/** @pdOid 5f0bc697-be90-41b3-8410-403bc483ebea */
	public String 备注;
	/** @pdOid 7431a018-708b-4608-961b-31405c6bb0cd */
	public String 单元ID;
	/** @pdOid a7a13931-690e-4904-8ed4-2120f936be93 */
	public String 划帐银行;
	/** @pdOid a6920bd5-218e-49bc-b043-a7801638122a */
	public String 银行帐户;
	/** @pdOid f360afa2-e4d1-4df1-b9f9-3597fa9edaab */
	public String 学历;
	/** @pdOid 23b553a5-fd5f-4c3c-9a95-dee3de97896a */
	public Date 入住日期;
	/** @pdOid e59661e3-4f0c-4af1-a650-a2bf9ca69caf */
	public Date 退住日期;
	/** @pdOid e6fe0aba-c0f1-4c6b-91fc-8547f7b07f0c */
	public String 住户编号;
	/** @pdOid 984e6386-5f50-44bd-b37e-fe9f65319da5 */
	public String 房间编号;
	/** @pdOid 2457cb30-4d12-4cdf-8f9e-b377924f8bc3 */
	public String 床位编号;
	/** @pdOid d6054297-0dc2-41dc-8f4c-493ec7dd95c9 */
	public BigDecimal 分摊系数;
	/** @pdOid 96f959d6-9d03-490a-95bb-9c8f29ef70b8 */
	public String 员工id;
	/** @pdOid 5b4f0bc4-9ec1-4365-abee-1a5d94ab06b3 */
	public BigDecimal 房间面积;
	/** @pdOid 917e2ccc-207b-4da8-b810-3540344d9605 */
	public String 帐号名;
	/** @pdOid 78548060-33eb-46da-950b-c52e9febe90c */
	public String 微信手机号;
	/** @pdOid 6aecde5d-4692-4d6d-b72a-1de751bb98f3 */
	public String 微信号;
	/** @pdOid 49cb6a22-c96d-453c-9da9-30b2e2bfddb5 */
	public String 开票类型;
	/** @pdOid 6765e902-2678-4d72-ac2c-a49373150fee */
	public String 购方名称;
	/** @pdOid 29caea2a-030f-4761-8a34-2e9e010b3adc */
	public String 购方地址电话;
	/** @pdOid 34daeaf3-2a09-423d-a566-b7d73cff273b */
	public String 购方银行帐号;
	/** @pdOid 47a49185-04b6-4edb-a1a1-6a8db3202844 */
	public String 购方税号;
	/** @pdOid 4273db19-8fa8-4f31-bd25-4f6550eb788e */
	public String 购方企业类型;
	/** @pdOid ab028020-b79e-4fcd-bf07-2f0e635beb0e */
	public String email;

}