package com.foshan.entity.mssql;

import java.math.BigDecimal;

import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name = "分摊_单元分摊用量_临时")
public class 分摊单元分摊用量临时Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5726562509122917274L;
	@EmbeddedId
	@AttributeOverrides({ @AttributeOverride(name = "总表ID", column = @Column),
			@AttributeOverride(name = "单元ID", column = @Column) })
	private 公用分摊范围Id id;
	public BigDecimal 分摊用量;
	public BigDecimal 分摊金额;


}