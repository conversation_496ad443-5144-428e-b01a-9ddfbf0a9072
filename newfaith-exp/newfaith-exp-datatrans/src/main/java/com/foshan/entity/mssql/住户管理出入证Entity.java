package com.foshan.entity.mssql;



import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "住户管理_出入证")
public class 住户管理出入证Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7763910570629138497L;
	@Id
	public String 出入证ID;
	/** @pdOid d69fd805-baab-4a34-a0c0-708da20ba6d8 */
	public String 单元ID;
	/** @pdOid 603ae940-db69-48ff-91ff-1a6a37a17c85 */
	public String 姓名;
	/** @pdOid abd22e0f-987a-4623-8cba-d971ea185f97 */
	public Integer 年龄;
	/** @pdOid d6efb10f-dd62-43ee-91e1-e1e8eddf5df4 */
	public String 性别;
	/** @pdOid de9b2b62-0a7f-45b9-a02e-8fb38b860d2e */
	public String 出入证号;
	/** @pdOid 80f99f0c-ea30-4002-87b5-2c1113b381ef */
	public String 身份证号;
	/** @pdOid 96871bff-4e44-4bbf-b373-60cf42496f07 */
	public Date 有效日期;
	/** @pdOid 2ceb8f2f-01a9-4b76-9d34-2071bd3a81fc */
	public Date 到期日期;
	/** @pdOid d79c56da-1062-461c-9aa2-61c2a26cb092 */
	public String 经办人;
	/** @pdOid 7253b2fd-536e-4c19-90e7-d2d1a6042c53 */
	public String 备注;
	/** @pdOid 9831f2a4-7678-4ce3-bf09-78ebc81deacd */
	public String 类别;
	/** @pdOid 6f1210aa-8693-4689-b804-5c0d33d60ba6 */
	public Date 出生日期;
	/** @pdOid 25001764-758c-451a-a1d2-9ec9e9b91f2f */
	public Date 办理日期;
	/** @pdOid fcae8ff1-3f72-49b6-ac16-f0e3e8447ba0 */
	public String 证件类型;

}