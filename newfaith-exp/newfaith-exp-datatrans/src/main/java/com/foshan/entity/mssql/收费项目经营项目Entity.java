package com.foshan.entity.mssql;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "收费项目_经营项目")
public class 收费项目经营项目Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1816728125960682500L;
	@Id
	public String 收费项目;
	/** @pdOid fd651243-c6a0-4277-bd02-89c6dd28df0a */
	public String 经营项目;
	/** @pdOid c1784b50-04dc-4bc4-8b7e-c5c8f622ea87 */
	public Integer 是否应收;
	/** @pdOid 4ba8e91e-b616-490e-9971-6e124b56a7ec */
	public String 管理处;
	/** @pdOid 5c8b0762-48c8-4352-8fe1-3d7140bee2c8 */
	public Integer 税率;
	/** @pdOid 066db3f5-b7ae-4b5e-8b3b-20fbc5d4eb52 */
	public String 项目编码;
	/** @pdOid e155c8e3-799c-452d-92bb-62b3f4943411 */
	public String 税务项目名称;

}