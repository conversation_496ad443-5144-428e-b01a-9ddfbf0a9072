package com.foshan.entity.mssql;

import java.math.BigDecimal;

import java.util.Date;


import javax.persistence.Entity;
import javax.persistence.Id;


import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "应预收款收据")
public class 应预收款收据Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6806776256184254127L;
	@Id
	public String 收据ID;
	/** @pdOid ccdca1ef-228e-4757-8e86-19f516f1d236 */
	public String 单据编号;
	/** @pdOid 093ce8a3-00c7-44bf-8dc6-dad2a04a5fd2 */
	public Date 收款日期;
	/** @pdOid 868fa53c-ba38-432b-8451-16bff88a4938 */
	public String 姓名;
	/** @pdOid 2ca7efef-5b5e-42c3-b3dd-d03ac47e4b64 */
	public BigDecimal 金额;
	/** @pdOid 5c952510-1410-4950-a3f3-6fe968a87286 */
	public String 币种;
	/** @pdOid 3cb394e6-c0d0-4913-9265-7c03f6f57e3b */
	public BigDecimal 汇率;
	/** @pdOid 121fc8e7-d05a-4430-9d10-c931662e3485 */
	public BigDecimal 本位币金额;
	/** @pdOid 779aad6b-f6ef-406b-9eb4-8670fe437794 */
	public String 收款方式;
	/** @pdOid c7a8a068-6436-47cd-80e0-784e4e07abf6 */
	public String 支票号码;
	/** @pdOid 8cddbf79-0ca1-4cd0-a665-7ba8f35df2c4 */
	public String 备注;
	/** @pdOid e02d8f90-0830-4f39-9de7-639ded9713c3 */
	public String 经办人;
	/** @pdOid 4ccfd2ed-ead5-4df9-944c-fa8bdf7cebca */
	public String 出纳;
	/** @pdOid af7222f6-8d8d-4204-834c-8ac6b86922a3 */
	public String 主管;
	/** @pdOid 3d9f4421-e302-4930-858f-6b2fac531ff5 */
	public String 单据类别;
	/** @pdOid 94ced601-1a63-4701-9d00-dc97b4625888 */
	public Integer 应收预收 = 0;
	/** @pdOid 0e3daf21-5247-4c7b-adc5-4148af8f3ec8 */
	public Integer 银行划帐ID;
	/** @pdOid 166690f2-5878-4b27-9cac-a02ed45556a3 */
	public Integer 使用自动流水号 = 0;
	/** @pdOid 3398312f-175c-4daf-b309-580d110561ac */
	public String 住户成员id;
	/** @pdOid 24620008-373c-4520-902b-8528bf73362e */
	public Integer 是否审核;
	/** @pdOid bdd16ea0-9a8a-4b44-88f3-bf65eddc79e5 */
	public Integer taxid;
	/** @pdOid f69eaa5f-865c-4b3c-8000-0a03b701f90d */
	public Integer 是否打印;
	/** @pdOid 85962fa8-7a67-4728-a322-8491bed2b1b4 */
	public Integer 财务帐期;
	private String 单元ID;


}