package com.foshan.entity.mssql;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter

@NoArgsConstructor
@Entity
@Table(name = "收费设定")
public class 收费设定Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8809986713524624068L;
	@Id
	public String 项目ID;
	/** @pdOid e34e1ddf-682a-436a-94d9-77785abb16a2 */
	public String 类别;
	/** @pdOid 28bb55ee-83fe-4627-8760-652195c8ff33 */
	public String 收费项目;
	/** @pdOid 8a1e1812-5513-4a92-aa90-f50a74fb4197 */
	public BigDecimal 单价;
	/** @pdOid d4ad6785-dd2f-41aa-ba70-b58a5926e974 */
	public String 单位;
	/** @pdOid 77100eb5-e45f-4f9f-90f9-91984fe32ec9 */
	public String 间隔;
	/** @pdOid d2a46cf2-a923-4ed3-866c-69fa929500fe */
	public String 间隔单位;
	/** @pdOid 8980a81b-2e30-4f07-a6c6-ad05dbff21b7 */
	public Date 起始时间;
	/** @pdOid afb169e9-9291-415d-8e4e-4e618af80e62 */
	public Integer 自动生成应收款 = 0;
	/** @pdOid eaa97bb4-c5a3-41d9-9811-3dc6fa6fda73 */
	public String 备注;
	/** @pdOid 8215e6c8-43f7-4240-a828-4889350dc9d9 */
	public String 起始时间_参照;
	/** @pdOid 238bd95b-a1fa-479d-991a-7d5e48d00488 */
	public Integer 预收;
	/** @pdOid 8366949c-69da-4232-af9b-90f9f05673f4 */
	public String 管理处;
	/** @pdOid 92565425-f0ae-487b-9e58-50a57e9df93f */
	public Integer 属期间隔;
	/** @pdOid a85e98cf-6298-481d-89b4-dff49d46ff48 */
	public String 单位1;
	/** @pdOid f172b8ad-1806-4f44-8f8e-34116aec1472 */
	public String 自定义公式;
	/** @pdOid f084d753-74d1-4b9c-927b-66844a2836ab */
	public Integer 交款日;
	/** @pdOid d47ff2b7-8c5a-4592-8132-f78f6aa919dd */
	public Integer 是否历史费用;
	/** @pdOid 33ec0bcb-f8c2-409f-a589-356176cb803e */
	public Date 截止时间;


}