package com.foshan.entity.mssql;

import java.math.BigDecimal;

import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name = "实收款")
public class 实收款Entity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2422559025729358678L;
	@EmbeddedId
	@AttributeOverrides({ @AttributeOverride(name = "应收款ID", column = @Column),
			@AttributeOverride(name = "收据ID", column = @Column) })
	private 实收款Id id;
	public BigDecimal 已收金额;
	public BigDecimal 本次收款;

	
	
}