package com.foshan.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.foshan.entity.mssql.住户管理报修登记Entity;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.datatrans.DatatransRes;
import com.foshan.service.RepairService;

@Service("repairService")
public class RepairServiceImpl  extends GenericDatatransService implements RepairService{

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse parseRepair(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		List<住户管理报修登记Entity> 报修List = 住户管理报修登记Dao.getListByHql("from 住户管理报修登记Entity a where a.是否入帐=1");
		
		res.setResult(transRepair(报修List));
		return res;
	}

}
