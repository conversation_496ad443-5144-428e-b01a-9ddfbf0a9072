package com.foshan.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.foshan.entity.mssql.分摊设置自定义分摊公式Entity;
import com.foshan.entity.mssql.总表Entity;
import com.foshan.entity.mssql.总表抄表Entity;
import com.foshan.entity.mssql.用户表Entity;
import com.foshan.entity.mssql.用户表抄表Entity;
import com.foshan.entity.mssql.表属性Entity;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.datatrans.DatatransRes;
import com.foshan.service.ZongbiaoService;

@Service("zongbiaoService")
public class ZongbiaoServiceImpl extends GenericDatatransService implements ZongbiaoService {

	/*
	 * 表属性
	 */
	@Override
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public IResponse parseBiaoshuxing(DataTransReq req) {

		DatatransRes res = new DatatransRes();
		List<表属性Entity> 表属性List = 表属性Dao.getList();
		res.setResult(transBiaoshuxing(表属性List));
		return res;
	}

	/*
	 * 自定义分摊公式
	 */
	@Override
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public IResponse parseZidingyiGongshi(DataTransReq req) {

		DatatransRes res = new DatatransRes();
		List<分摊设置自定义分摊公式Entity> 公式List = 分摊设置自定义分摊公式Dao.getList();
		res.setResult(transZidingyiGongshi(公式List));
		return res;
	}

	/*
	 * 总表
	 */
	@Override
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public IResponse parseZongbiao(DataTransReq req) {

		DatatransRes res = new DatatransRes();
		List<总表Entity> 总表List = new ArrayList<>();

		if (StringUtils.isNotEmpty(req.getTransField()) && StringUtils.isNotEmpty(req.getTransOp())
				&& StringUtils.isNotEmpty(req.getTransValue())) {
			总表List = 总表Dao.getListByHql("from 总表Entity where " + req.getTransField() + " " + req.getTransOp() + " '"
					+ req.getTransValue() + "' order by 层次");
		} else {
			总表List = 总表Dao.getListByHql("from 总表Entity order by 层次");
		}

		res.setResult(transZongbiao(总表List));
		return res;
	}

	/*
	 * 总表抄表
	 */
	@Override
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public IResponse parseZongbiaoChaobiao(DataTransReq req) {

		DatatransRes res = new DatatransRes();
		List<总表Entity> 总表List =  总表Dao.getListByHql("from 总表Entity order by 层次");
		
		List<总表抄表Entity> 总表抄表List = new ArrayList<>();

		if (StringUtils.isNotEmpty(req.getTransField()) && StringUtils.isNotEmpty(req.getTransOp())
				&& StringUtils.isNotEmpty(req.getTransValue())) {
			总表抄表List = 总表抄表Dao.getListByHql("from 总表抄表Entity where " + req.getTransField() + " " + req.getTransOp()
					+ " '" + req.getTransValue() + "' order by 总表Id,本次读数日期");
		} else {
			总表抄表List = 总表抄表Dao.getListByHql("from 总表抄表Entity order by 总表Id,本次读数日期");
		}

		res.setResult(transZongbiaoChaobiao(总表抄表List,总表List));
		return res;
	}

	/*
	 * 用户表
	 */
	@Override
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public IResponse parseYonghubiao(DataTransReq req) {

		DatatransRes res = new DatatransRes();
		List<用户表Entity> 用户表List = new ArrayList<>();

		if (StringUtils.isNotEmpty(req.getTransField()) && StringUtils.isNotEmpty(req.getTransOp())
				&& StringUtils.isNotEmpty(req.getTransValue())) {
			用户表List = 用户表Dao.getListByHql("from 用户表Entity where " + req.getTransField() + " " + req.getTransOp()
					+ " '" + req.getTransValue() + "'");
		} else {
			用户表List = 用户表Dao.getListByHql("from 用户表Entity");
		}

		res.setResult(transYonghubiao(用户表List));
		return res;
	}

	/*
	 * 用户表抄表
	 */
	@Override
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public IResponse parseYonghubiaoChaobiao(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		List<用户表抄表Entity> 用户表抄表List = new ArrayList<>();

		if (StringUtils.isNotEmpty(req.getTransField()) && StringUtils.isNotEmpty(req.getTransOp())
				&& StringUtils.isNotEmpty(req.getTransValue())) {
			用户表抄表List = 用户表抄表Dao.getListByHql("from 用户表抄表Entity where " + req.getTransField() + " " + req.getTransOp()
					+ " '" + req.getTransValue() + "' order by 用户表Id,本次读数日期");
		} else {
			用户表抄表List = 用户表抄表Dao.getListByHql("from 用户表抄表Entity order by 用户表Id,本次读数日期");
		}

		res.setResult(transYonghubiaoChaobiao(用户表抄表List));
		return res;
	}

}
