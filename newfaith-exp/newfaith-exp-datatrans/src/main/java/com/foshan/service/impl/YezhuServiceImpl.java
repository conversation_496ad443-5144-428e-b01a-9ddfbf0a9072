package com.foshan.service.impl;


import java.util.List;
import org.springframework.stereotype.Service;

import com.foshan.entity.mssql.户主Entity;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.datatrans.DatatransRes;
import com.foshan.service.YezhuService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("yezhuService")
public class YezhuServiceImpl extends GenericDatatransService implements YezhuService {

	@Override
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public IResponse parseHuzhu(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();

		List<户主Entity> 户主List1 = 户主Dao.getListByHql(
				"from 户主Entity a where a.id.户主id not in('C06A0AEA-86F5-4984-8593-C893D95E8DE8',"
				+ "'73796AE1-898B-4D01-80A9-7D1CEB139784','ED58B24C-8C9C-4FD4-BA21-A780A634A3FE',"
				+ "'80554271-32CD-4CB0-A96E-2D61DC1FB839','E77CEBE3-350E-4825-9919-90DE08DB3637',"
				+ "'7F595440-66FD-4ED3-A743-17D248C6F699','E23C15E7-F2EA-4595-85ED-1D88FE61D3F2',"
				+ "'3216B81D-7AA7-4741-9A2B-A0B17A9CDFC6') and a.状态='离退'"
				+ " and a.单元id not IN('E90DDA1D-AF41-495A-BEEE-419BFAAA9742')");
		res.getResult().putAll(transHuzhu1(户主List1));
		log.info("=================>>>离退业主数据割接完毕！！！<<<========================");
		List<户主Entity> 户主List2 = 户主Dao.getListByHql(
				"from 户主Entity a where a.id.户主id not in('C06A0AEA-86F5-4984-8593-C893D95E8DE8',"
				+ "'73796AE1-898B-4D01-80A9-7D1CEB139784','ED58B24C-8C9C-4FD4-BA21-A780A634A3FE',"
				+ "'80554271-32CD-4CB0-A96E-2D61DC1FB839','E77CEBE3-350E-4825-9919-90DE08DB3637',"
				+ "'7F595440-66FD-4ED3-A743-17D248C6F699','E23C15E7-F2EA-4595-85ED-1D88FE61D3F2',"
				+ "'3216B81D-7AA7-4741-9A2B-A0B17A9CDFC6') and a.状态<>'离退'"
				+ " and a.单元id not IN('E90DDA1D-AF41-495A-BEEE-419BFAAA9742')");
		res.getResult().putAll(transHuzhu2(户主List2));
		log.info("=================>>>其余业主数据割接完毕！！！<<<========================");
		return res;
	}

	@Override
	@SuppressWarnings({ "rawtypes" })
	public IResponse parseChengyuan(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();

		return res;
	}

}
