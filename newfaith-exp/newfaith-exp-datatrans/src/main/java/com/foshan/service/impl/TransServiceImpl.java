package com.foshan.service.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityBuildingEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMeterAttributesEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityMeterFormulaEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.mssql.住户管理报修登记Entity;
import com.foshan.entity.mssql.公用分摊范围Entity;
import com.foshan.entity.mssql.分摊分摊用量临时Entity;
import com.foshan.entity.mssql.分摊设置自定义分摊公式Entity;
import com.foshan.entity.mssql.单元Entity;
import com.foshan.entity.mssql.实收款Entity;
import com.foshan.entity.mssql.应收款Entity;
import com.foshan.entity.mssql.应收款减加Entity;
import com.foshan.entity.mssql.应预收款收据Entity;
import com.foshan.entity.mssql.总表Entity;
import com.foshan.entity.mssql.总表抄表Entity;
import com.foshan.entity.mssql.户主Entity;
import com.foshan.entity.mssql.收费范围Entity;
import com.foshan.entity.mssql.收费设定Entity;
import com.foshan.entity.mssql.楼盘Entity;
import com.foshan.entity.mssql.楼阁Entity;
import com.foshan.entity.mssql.用户表Entity;
import com.foshan.entity.mssql.用户表抄表Entity;
import com.foshan.entity.mssql.表属性Entity;
import com.foshan.entity.mssql.财务其他收费Entity;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.datatrans.DatatransRes;
import com.foshan.service.TransService;
import com.foshan.util.datatrans.Datatrans;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("transService")
public class TransServiceImpl extends GenericDatatransService implements TransService {

	/*
	 * 批量楼盘
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse batchTransLoupan() {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		List<楼盘Entity> 楼盘List = 楼盘Dao.getList();
		res.setResult(transLoupan(楼盘List));
		楼盘List = null;
		List<CommunityDistrictEntity> disList = communityDistrictDao.getList();
		res.getResult().putAll(transLouge(disList));
		disList = null;
		List<Object[]> 楼梯List = 单元Dao.createSQLQuery("SELECT a.单元id,b.楼阁id,a.楼梯id,a.单元编号,a.楼层,a.房号,a.建筑面积,"
				+ "a.使用面积,a.收费面积,a.朝向,a.功能,a.附加面积,c.楼梯名称,c.楼梯NO FROM 单元 a,楼阁 b,楼梯 c "
				+ "WHERE a.楼梯id=c.楼梯id AND b.楼阁id=c.楼阁id").list();
		res.getResult().putAll(transLouti(楼梯List));
		楼梯List = null;
		List<CommunityBuildingEntity> buildingList = communityBuildingDao.getList();
		res.getResult().putAll(transDanyuan(buildingList));
		buildingList = null;
		return res;
	}

	/*
	 * 批量总表
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse batchTransZongbiao() {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		List<表属性Entity> 表属性List = 表属性Dao.getList();
		res.setResult(transBiaoshuxing(表属性List));
		表属性List = null;
		List<分摊设置自定义分摊公式Entity> 公式List = 分摊设置自定义分摊公式Dao.getList();
		res.getResult().putAll(transZidingyiGongshi(公式List));
		公式List = null;
		List<总表Entity> 总表List = 总表Dao.getListByHql("from 总表Entity order by 层次");
		res.getResult().putAll(transZongbiao(总表List));
		List<总表抄表Entity> 总表抄表List = 总表抄表Dao.getListByHql("from 总表抄表Entity order by 总表Id,本次读数日期");
		res.getResult().putAll(transZongbiaoChaobiao(总表抄表List, 总表List));
		总表抄表List = null;
		List<用户表Entity> 用户表List = 用户表Dao.getListByHql("from 用户表Entity");
		res.getResult().putAll(transYonghubiao(用户表List));
		用户表List = null;
		List<用户表抄表Entity> 用户表抄表List = 用户表抄表Dao.getListByHql("from 用户表抄表Entity order by 用户表Id,本次读数日期");
		res.getResult().putAll(transYonghubiaoChaobiao(用户表抄表List));
		用户表抄表List = null;
		return res;
	}

	/*
	 * 批量费用
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse batchTransFee() {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		List<收费设定Entity> 收费设定List = 收费设定Dao.getList();
		res.setResult(transShoufeiSheding(收费设定List));

		res.getResult().putAll(transOtherPayItem(get其它收费项目List()));

		List<收费范围Entity> 收费范围List = 收费范围Dao.getList();
		res.getResult().putAll(transShoufeiFanwei(收费范围List));
		收费范围List = null;

		List<公用分摊范围Entity> 公摊List = 公用分摊范围Dao.getList();
		res.getResult().putAll(transGongyongFentanFanwei(公摊List));
		Integer totalCount = 公摊List.size();
		Integer totalPage = totalCount / 85000 + 1;
//		for (int i = 1; i <= totalPage; i++) {
//			log.info("========>>>开始割接第>>>>" + i + "<<<<批数据！！！<<<============");
//			Page<公用分摊范围Entity> page = new Page<公用分摊范围Entity>();
//			page.setPageSize(85000);
//			page.setBeginCount((i - 1) * page.getPageSize());
//			page.setCurrentPage(i);
//			String hql = "from 公用分摊范围Entity order by 单元id";
//			page = 公用分摊范围Dao.queryPage(page, hql);
//			res.getResult().putAll(transGongyongFentanFanwei(page.getResultList()));
//			log.info("========>>>第" + i + "批数据割接完毕,还剩"+(totalPage-i)+"批！！！<<<============");
//		}

		totalCount = 应收款Dao.getList().size();
		totalPage = totalCount / 85000 + 1;
		for (int i = 1; i <= totalPage; i++) {
			log.info("========>>>开始割接第>>>>" + i + "<<<<批数据！！！<<<============");
			Page<应收款Entity> page = new Page<应收款Entity>();
			page.setPageSize(85000);
			page.setBeginCount((i - 1) * page.getPageSize());
			page.setCurrentPage(i);
			String hql = "from 应收款Entity order by 应收款no";
			page = 应收款Dao.queryPage(page, hql);
			res.getResult().putAll(transYingshoukuan(page.getResultList()));
			log.info("========>>>第" + i + "批数据割接完毕,还剩" + (totalPage - i) + "批！！！<<<============");
		}

		List<应收款减加Entity> 应收款减加List = 应收款减加Dao.getList();
		res.getResult().putAll(transYingshoukuanJianjia(应收款减加List));
		应收款减加List = null;

		totalCount = 应预收款收据Dao.getList().size();
		totalPage = totalCount / 85000 + 1;
		for (int i = 1; i <= totalPage; i++) {
			log.info("========>>>开始割接第>>>>" + i + "<<<<批数据！！！<<<============");
			Page<应预收款收据Entity> page = new Page<应预收款收据Entity>();
			page.setPageSize(85000);
			page.setBeginCount((i - 1) * page.getPageSize());
			page.setCurrentPage(i);
			String hql = "from 应预收款收据Entity order by taxid";
			page = 应预收款收据Dao.queryPage(page, hql);
			res.getResult().putAll(transShouju(page.getResultList()));
			log.info("========>>>第" + i + "批数据割接完毕,还剩" + (totalPage - i) + "批！！！<<<============");
		}

		List<实收款Entity> 实收List = 实收款Dao.getList();
		res.getResult().putAll(transShishoukuan(实收List));
		实收List = null;

//		totalCount=实收款Dao.getList().size();
//		totalPage = totalCount / 85000+1;
//		for (int i = 1; i <= totalPage; i++) {
//			log.info("========>>>开始割接第>>>>" + i + "<<<<批数据！！！<<<============");
//			Page<实收款Entity> page = new Page<实收款Entity>();
//			page.setPageSize(85000);
//			page.setBeginCount((i - 1) * page.getPageSize());
//			page.setCurrentPage(i);
//			String hql = "from 实收款Entity order by 收据id";
//			page = 实收款Dao.queryPage(page, hql);
//			res.getResult().putAll(transShishoukuan(page.getResultList()));
//			log.info("========>>>第" + i + "批数据割接完毕,还剩" + (totalPage - i) + "批！！！<<<============");
//		}

		List<财务其他收费Entity> 其他收费List = 财务其他收费Dao.getList();
		res.getResult().putAll(transOtherFee(其他收费List));

		res.getResult().putAll(transOtherFeeDeposit(其他收费List));

		List<分摊分摊用量临时Entity> 总表用量分摊List = 分摊分摊用量临时Dao.getList();
		res.getResult().putAll(transZongbiaoFentan(总表用量分摊List));

		List<Object[]> 划账List = 银行划账单元筛选Dao.createSQLQuery("SELECT a.单元id,c.项目id,d.收费项目,0 AS depositType "
				+ "FROM 银行划帐_单元筛选 a,车场管理_车辆 b,收费范围 c,收费设定 d "
				+ "WHERE a.收费项目 NOT LIKE '%滞纳金%' AND a.收费项目='车位管理费' AND b.离退日期 IS NULL "
				+ "AND a.单元id=b.车主住所id AND b.车位id=c.单元id AND c.项目id =d.项目id " + "union "
				+ "SELECT a.单元id,c.项目id,d.收费项目,0 as depositType " + "FROM 银行划帐_单元筛选 a,车场管理_车辆 b,收费范围 c,收费设定 d "
				+ "WHERE a.收费项目 NOT LIKE '%滞纳金%' AND a.收费项目='出租车位管理服务费' AND b.离退日期 IS NULL "
				+ "AND a.单元id=b.车主住所id AND b.车位id=c.单元id AND c.项目id =d.项目id and a.收费项目=d.收费项目 " + "union "
				+ "SELECT a.单元id,c.项目id,d.收费项目,0 AS depositType " + "FROM 银行划帐_单元筛选 a,车场管理_车辆 b,收费范围 c,收费设定 d "
				+ "WHERE a.收费项目 NOT LIKE '%滞纳金%' AND a.收费项目='代收车位租金' AND b.离退日期 IS NULL "
				+ "AND a.单元id=b.车主住所id AND b.车位id=c.单元id AND c.项目id =d.项目id and a.收费项目=d.收费项目 " + "union "
				+ "select a.单元id,null as 项目id,a.收费项目,0 AS depositType "
				+ "from 银行划帐_单元筛选 a where a.收费项目 not like '%滞纳金%' and a.收费项目 not like '%违约金%' and a.收费项目 not in('车位管理费','出租车位管理服务费','代收车位租金') "
				+ "union  " + "SELECT a.单元id,b.项目id,b.收费项目,1 as depositType  "
				+ "FROM 收费范围 a,收费设定 b,滞纳金_收费项目 c,银行划帐_单元筛选 d "
				+ "WHERE a.项目id=b.项目id AND b.收费项目=c.收费项目 AND c.费用名称 ='违约金' and a.单元id=d.单元id and c.费用名称=d.收费项目 ")
				.list();
		// 特殊处理数据
		Object[] t1 = {"5DAC45BE-9B37-43D5-AE1E-B39FB1257C67","F2C422E9-258F-485C-832F-79BD1DE5E9E2","车位管理费",1};
		划账List.add(t1);
		Object[] t2 = {"28489E87-5B5F-4F8A-8C6D-A560B3E85A82","F2C422E9-258F-485C-832F-79BD1DE5E9E2","车位管理费",1};
		划账List.add(t2);
		Object[] t3 = {"E4C4F8A2-32AB-496C-AE2F-0967A6768C33","F2C422E9-258F-485C-832F-79BD1DE5E9E2","车位管理费",1};
		划账List.add(t3);
		Object[] t4 = {"D6F5BA98-0AB5-4AF2-B394-00334396FB67","F2C422E9-258F-485C-832F-79BD1DE5E9E2","车位管理费",1};
		划账List.add(t4);
		res.getResult().putAll(transYinhangHuazhangDanyuanShaixuan(划账List));

		return res;
	}

	/*
	 * 批量业主
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse batchYezhu() {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		List<户主Entity> 户主List1 = 户主Dao.getListByHql(
				"from 户主Entity a where a.id.户主id not in('C06A0AEA-86F5-4984-8593-C893D95E8DE8','73796AE1-898B-4D01-80A9-7D1CEB139784','ED58B24C-8C9C-4FD4-BA21-A780A634A3FE','80554271-32CD-4CB0-A96E-2D61DC1FB839','E77CEBE3-350E-4825-9919-90DE08DB3637','7F595440-66FD-4ED3-A743-17D248C6F699','E23C15E7-F2EA-4595-85ED-1D88FE61D3F2','3216B81D-7AA7-4741-9A2B-A0B17A9CDFC6') and a.状态='离退'");
		res.getResult().putAll(transHuzhu1(户主List1));
		log.info("=================>>>离退业主数据割接完毕！！！<<<========================");
		List<户主Entity> 户主List2 = 户主Dao.getListByHql(
				"from 户主Entity a where a.id.户主id not in('C06A0AEA-86F5-4984-8593-C893D95E8DE8','73796AE1-898B-4D01-80A9-7D1CEB139784','ED58B24C-8C9C-4FD4-BA21-A780A634A3FE','80554271-32CD-4CB0-A96E-2D61DC1FB839','E77CEBE3-350E-4825-9919-90DE08DB3637','7F595440-66FD-4ED3-A743-17D248C6F699','E23C15E7-F2EA-4595-85ED-1D88FE61D3F2','3216B81D-7AA7-4741-9A2B-A0B17A9CDFC6') and a.状态<>'离退'");
		res.getResult().putAll(transHuzhu2(户主List2));
		log.info("=================>>>其余业主数据割接完毕！！！<<<========================");
		return res;
	}

	/*
	 * 批量车位
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse batchChewei() {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		List<Object[]> 车位List = 车场管理车辆Dao.createSQLQuery("SELECT a.车位Id,a.车辆id,a.停车牌号,c.停车证号,c.车主姓名,a.车辆特征,a.ic卡号,"
				+ "a.起租日期,c.起始日期,a.离退日期,c.截止日期,a.备注,a.停放类型,a.车主住所id,c.经办人,a.车牌号,a.车主id,d.建筑面积 "
				+ "FROM 车场管理_车辆 a LEFT JOIN 车场管理_停车证 c ON a.车辆id=c.车辆Id,单元 d,"
				+ "(SELECT 车位Id,MAX(起租日期) AS 起租日期 FROM 车场管理_车辆 GROUP BY 车位Id) b "
				+ "WHERE a.车位Id=b.车位Id AND a.车位ID=d.单元ID AND a.起租日期=b.起租日期 AND a.车辆id NOT IN('5B51D143-DA86-4015-8E9C-EC11AF28FE1D',"
				+ "'68E44FC3-CA55-4F23-BD3B-CD6F25912D1C','EE58BF72-9CE1-44FA-B37F-8E4218136BCC','A9615C5F-171C-4B1F-854E-7B5C34BD194D','100EBD19-18AF-4850-915E-FC111A9F7F41','249D846D-4204-4285-AD40-0EEAED14796F')"
				+ "UNION ALL " + "SELECT a.车位Id,a.车辆id,a.停车牌号,c.停车证号,c.车主姓名,a.车辆特征,a.ic卡号,"
				+ "a.起租日期,c.起始日期,a.离退日期,c.截止日期,a.备注,a.停放类型,a.车主住所id,c.经办人,a.车牌号,a.车主id,d.建筑面积 "
				+ "FROM 车场管理_车辆 a LEFT JOIN 车场管理_停车证 c ON a.车辆id=c.车辆Id,单元 d,"
				+ "(SELECT 车位Id,MAX(起租日期) AS 起租日期 FROM 车场管理_车辆 GROUP BY 车位Id) b "
				+ "WHERE a.车位Id=b.车位Id AND a.车位ID=d.单元ID AND a.起租日期!=b.起租日期 AND a.车辆id NOT IN('5B51D143-DA86-4015-8E9C-EC11AF28FE1D',"
				+ "'68E44FC3-CA55-4F23-BD3B-CD6F25912D1C','EE58BF72-9CE1-44FA-B37F-8E4218136BCC','A9615C5F-171C-4B1F-854E-7B5C34BD194D','100EBD19-18AF-4850-915E-FC111A9F7F41','249D846D-4204-4285-AD40-0EEAED14796F')")
				.list();
		res.getResult().putAll(transChewei(车位List));
		车位List = null;
		log.info("=================>>>车位数据割接完毕！！！<<<========================");

		List<Object[]> cheweiList = communityMemberPropertyDao
				.createSQLQuery("SELECT a.id,a.buildingArea,a.estateType,b.parentPropertyId,c.reservedfield "
						+ "FROM t_community_property a,t_community_member_property b,t_community_property c "
						+ "WHERE a.id=b.propertyid and b.parentpropertyid=c.id AND b.parentPropertyId IS NOT NULL AND b.terminationDate IS NULL AND b.membertype<>2")
				.list();

		Map<Integer, String> temp = new HashMap<>();

		for (Object[] o : cheweiList) {
			try {
				Integer parentPropertyId = Integer.parseInt(o[3].toString());
				BigDecimal buildingArea = new BigDecimal(o[1].toString());

				Map<String, String> tempCarInfo = mapper.readValue(o[4].toString(),
						new HashMap<String, String>().getClass());

				if (temp.containsKey(parentPropertyId)) {
					tempCarInfo = mapper.readValue(temp.get(parentPropertyId),
							new HashMap<String, String>().getClass());
				}

				if (null != o[2] && o[2].toString().equals("人防车位")) {
					Integer defenceParkingNum = Integer.valueOf(tempCarInfo.get("defenceParkingNum"));
					BigDecimal defenceParkingArea = new BigDecimal(tempCarInfo.get("defenceParkingArea"));
					defenceParkingNum++;
					tempCarInfo.put("defenceParkingNum", defenceParkingNum.toString());
					tempCarInfo.put("defenceParkingArea", defenceParkingArea.add(buildingArea).toString());
				} else {
					Integer propertyParkingNum = Integer.valueOf(tempCarInfo.get("propertyParkingNum"));
					propertyParkingNum++;
					BigDecimal nowArea = new BigDecimal(tempCarInfo.get("propertyParkingArea"));
					tempCarInfo.put("propertyParkingNum", propertyParkingNum.toString());
					tempCarInfo.put("propertyParkingArea", nowArea.add(buildingArea).toString());
				}

				temp.put(parentPropertyId, mapper.writeValueAsString(tempCarInfo));
			} catch (Exception e) {
				e.printStackTrace();
				try {
					System.out.println(mapper.writeValueAsString(o));
				} catch (JsonProcessingException e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
				}
			}
		}

		res.getResult().putAll(transCheweiData(temp));

		log.info("=================>>>车位数据处理完毕！！！<<<========================");
		return res;
	}

	
	/*
	 * 批量维修
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse batchRepair() {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		List<住户管理报修登记Entity> 报修List = 住户管理报修登记Dao.getListByHql("from 住户管理报修登记Entity a where a.是否入帐=1");
		res.getResult().putAll(transRepair(报修List));
		return res;
	}

	
	/*
	 * 批量割接
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse batchTrans() {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		res.getResult().putAll(((DatatransRes) batchTransLoupan()).getResult());

		res.getResult().putAll(((DatatransRes) batchTransZongbiao()).getResult());

		Datatrans.shuxingIdList.clear();
		Datatrans.gongshiIdList.clear();
		Datatrans.meterIdList.clear();
		res.getResult().putAll(((DatatransRes) batchTransFee()).getResult());

		res.getResult().putAll(((DatatransRes) batchYezhu()).getResult());

		res.getResult().putAll(((DatatransRes) batchChewei()).getResult());

		res.getResult().putAll(((DatatransRes) batchRepair()).getResult());
		return res;
	}

	/*
	 * 割接结果
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse transResult(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		List<楼盘Entity> 楼盘List = 楼盘Dao.getList();
		List<CommunityDistrictEntity> disList = communityDistrictDao.getList();
		res.getResult().put("楼盘割接结果：", "旧库数据(" + 楼盘List.size() + ")--->新库数据(" + disList.size() + ")");
		List<楼阁Entity> 楼阁List = 楼阁Dao.getList();
		List<CommunityBuildingEntity> buildingList = communityBuildingDao.getList();
		res.getResult().put("楼阁割接结果：", "旧库数据(" + 楼阁List.size() + ")--->新库数据(" + buildingList.size() + ")");
		List<单元Entity> 单元List = 单元Dao.getList();
		List<CommunityEstateEntity> danyuanList = communityEstateDao.getList();
		res.getResult().put("单元割接结果：", "旧库数据(" + 单元List.size() + ")--->新库数据(" + danyuanList.size() + ")");
		List<表属性Entity> 表属性List = 表属性Dao.getList();
		List<CommunityMeterAttributesEntity> shuxingList = communityMeterAttributesDao.getList();
		res.getResult().put("表属性割接结果：", "旧库数据(" + 表属性List.size() + ")--->新库数据(" + shuxingList.size() + ")");
		List<分摊设置自定义分摊公式Entity> 公式List = 分摊设置自定义分摊公式Dao.getList();
		List<CommunityMeterFormulaEntity> formulaList = communityMeterFormulaDao.getList();
		res.getResult().put("自定义分摊公式割接结果：", "旧库数据(" + 公式List.size() + ")--->新库数据(" + formulaList.size() + ")");
		List<总表Entity> 总表List = 总表Dao.getList();
		List<CommunityMeterEntity> meterList = communityMeterDao.getList();
		res.getResult().put("总表割接结果：", "旧库数据(" + 总表List.size() + ")--->新库数据(" + meterList.size() + ")");
		List<收费设定Entity> 收费设定List = 收费设定Dao.getList();
		List<CommunityPayItemsEntity> payItemList = communityPayItemsDao.getList();
		res.getResult().put("收费设定割接结果：", "旧库数据(" + 收费设定List.size() + ")--->新库数据(" + payItemList.size() + ")");

		return res;
	}
}
