package com.foshan.service.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.mssql.车场管理车辆Entity;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.datatrans.DatatransRes;
import com.foshan.service.CheweiService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("cheweiService")
public class CheweiServiceImpl extends GenericDatatransService implements CheweiService {

	@SuppressWarnings("unchecked")
	@Override
	public IResponse parseChewei(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<车场管理车辆Entity> res = new DatatransRes<>();
//		List<车场管理车辆Entity> 车位List = 车场管理车辆Dao.getListBySql(
//				"SELECT a.* FROM 车场管理_车辆 a,(SELECT 车位Id,MAX(起租日期) AS 起租日期 FROM 车场管理_车辆 "
//				+ "GROUP BY 车位Id) b WHERE a.车位Id=b.车位Id AND a.起租日期=b.起租日期 AND a.车辆id "
//				+ "NOT IN('5B51D143-DA86-4015-8E9C-EC11AF28FE1D','68E44FC3-CA55-4F23-BD3B-CD6F25912D1C',"
//				+ "'EE58BF72-9CE1-44FA-B37F-8E4218136BCC','A9615C5F-171C-4B1F-854E-7B5C34BD194D')");
//		List<Object[]> 车位List = 车场管理车辆Dao.createSQLQuery("SELECT a.车位Id,a.车辆id,a.停车牌号,c.停车证号,c.车主姓名,a.车辆特征,a.ic卡号,"
//				+ "a.起租日期,c.起始日期,a.离退日期,c.截止日期,a.备注,a.停放类型,a.车主住所id,c.经办人,a.车牌号,a.车主id "
//				+ "FROM 车场管理_车辆 a LEFT JOIN 车场管理_停车证 c ON a.车辆id=c.车辆Id,"
//				+ "(SELECT 车位Id,MAX(起租日期) AS 起租日期 FROM 车场管理_车辆 GROUP BY 车位Id) b "
//				+ "WHERE a.车位Id=b.车位Id AND a.起租日期=b.起租日期 AND a.车辆id NOT IN('5B51D143-DA86-4015-8E9C-EC11AF28FE1D',"
//				+ "'68E44FC3-CA55-4F23-BD3B-CD6F25912D1C','EE58BF72-9CE1-44FA-B37F-8E4218136BCC','A9615C5F-171C-4B1F-854E-7B5C34BD194D','100EBD19-18AF-4850-915E-FC111A9F7F41')").list();
		List<Object[]> 车位List = 车场管理车辆Dao.createSQLQuery("SELECT a.车位Id,a.车辆id,a.停车牌号,c.停车证号,c.车主姓名,a.车辆特征,a.ic卡号,"
				+ "a.起租日期,c.起始日期,a.离退日期,c.截止日期,a.备注,a.停放类型,a.车主住所id,c.经办人,a.车牌号,a.车主id,d.建筑面积 "
				+ "FROM 车场管理_车辆 a LEFT JOIN 车场管理_停车证 c ON a.车辆id=c.车辆Id,单元 d,"
				+ "(SELECT 车位Id,MAX(起租日期) AS 起租日期 FROM 车场管理_车辆 GROUP BY 车位Id) b "
				+ "WHERE a.车位Id=b.车位Id AND a.车位ID=d.单元ID AND a.起租日期=b.起租日期 AND a.车辆id NOT IN('5B51D143-DA86-4015-8E9C-EC11AF28FE1D',"
				+ "'68E44FC3-CA55-4F23-BD3B-CD6F25912D1C','EE58BF72-9CE1-44FA-B37F-8E4218136BCC','A9615C5F-171C-4B1F-854E-7B5C34BD194D','100EBD19-18AF-4850-915E-FC111A9F7F41','249D846D-4204-4285-AD40-0EEAED14796F')"
				+ "UNION ALL " + "SELECT a.车位Id,a.车辆id,a.停车牌号,c.停车证号,c.车主姓名,a.车辆特征,a.ic卡号,"
				+ "a.起租日期,c.起始日期,a.离退日期,c.截止日期,a.备注,a.停放类型,a.车主住所id,c.经办人,a.车牌号,a.车主id,d.建筑面积 "
				+ "FROM 车场管理_车辆 a LEFT JOIN 车场管理_停车证 c ON a.车辆id=c.车辆Id,单元 d,"
				+ "(SELECT 车位Id,MAX(起租日期) AS 起租日期 FROM 车场管理_车辆 GROUP BY 车位Id) b "
				+ "WHERE a.车位Id=b.车位Id AND a.车位ID=d.单元ID AND a.起租日期!=b.起租日期 AND a.车辆id NOT IN('5B51D143-DA86-4015-8E9C-EC11AF28FE1D',"
				+ "'68E44FC3-CA55-4F23-BD3B-CD6F25912D1C','EE58BF72-9CE1-44FA-B37F-8E4218136BCC','A9615C5F-171C-4B1F-854E-7B5C34BD194D','100EBD19-18AF-4850-915E-FC111A9F7F41','249D846D-4204-4285-AD40-0EEAED14796F')")
				.list();
		res.setResult(transChewei(车位List));
		log.info("=================>>>车位数据割接完毕！！！<<<========================");
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse parseCheweiData(DataTransReq req) {
		// TODO Auto-generated method stub

		DatatransRes<CommunityMemberPropertyEntity> res = new DatatransRes<>();

		List<Object[]> cheweiList = communityMemberPropertyDao
				.createSQLQuery("SELECT a.id,a.buildingArea,a.estateType,b.parentPropertyId,c.reservedfield "
						+ "FROM t_community_property a,t_community_member_property b,t_community_property c "
						+ "WHERE a.id=b.propertyid and b.parentpropertyid=c.id AND b.parentPropertyId IS NOT NULL AND b.terminationDate IS NULL AND b.membertype<>2")
				.list();

		Map<Integer, String> temp = new HashMap<>();

		for (Object[] o : cheweiList) {
			try {
				Integer parentPropertyId = Integer.parseInt(o[3].toString());
				BigDecimal buildingArea = new BigDecimal(o[1].toString());

				Map<String, String> tempCarInfo = mapper.readValue(o[4].toString(),
						new HashMap<String, String>().getClass());
				
				if (temp.containsKey(parentPropertyId)) {
					tempCarInfo = mapper.readValue(temp.get(parentPropertyId), new HashMap<String, String>().getClass());
				}

				if (null != o[2] && o[2].toString().equals("人防车位")) {
					Integer defenceParkingNum = Integer.valueOf(tempCarInfo.get("defenceParkingNum"));
					BigDecimal defenceParkingArea = new BigDecimal(tempCarInfo.get("defenceParkingArea"));
					defenceParkingNum++;
					tempCarInfo.put("defenceParkingNum", defenceParkingNum.toString());
					tempCarInfo.put("defenceParkingArea", defenceParkingArea.add(buildingArea).toString());
				} else {
					Integer propertyParkingNum = Integer.valueOf(tempCarInfo.get("propertyParkingNum"));
					BigDecimal nowArea = new BigDecimal(tempCarInfo.get("propertyParkingArea"));
					propertyParkingNum++;
					tempCarInfo.put("propertyParkingNum", propertyParkingNum.toString());
					tempCarInfo.put("propertyParkingArea", nowArea.add(buildingArea).toString());
				}

				temp.put(parentPropertyId, mapper.writeValueAsString(tempCarInfo));
			} catch (Exception e) {
				e.printStackTrace();
				try {
					System.out.println(mapper.writeValueAsString(o));
				} catch (JsonProcessingException e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
				}
			}
		}

		res.setResult(transCheweiData(temp));

		return res;
	}


}
