package com.foshan.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.foshan.dao.generic.Page;
import com.foshan.entity.mssql.公用分摊范围Entity;
import com.foshan.entity.mssql.分摊分摊用量临时Entity;
import com.foshan.entity.mssql.分摊单元分摊用量临时Entity;
import com.foshan.entity.mssql.实收款Entity;
import com.foshan.entity.mssql.应收款Entity;
import com.foshan.entity.mssql.应收款减加Entity;
import com.foshan.entity.mssql.应预收款收据Entity;
import com.foshan.entity.mssql.收费范围Entity;
import com.foshan.entity.mssql.收费设定Entity;
import com.foshan.entity.mssql.收费项目其他收费Entity;
import com.foshan.entity.mssql.财务其他收费Entity;
import com.foshan.entity.mssql.银行划账单元筛选Entity;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.datatrans.DatatransRes;
import com.foshan.service.FeeService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("feeService")
public class FeeServiceImpl extends GenericDatatransService implements FeeService {

	/*
	 * 收费设定
	 */
	@Override
	public IResponse parseShoufeiSheding(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<收费设定Entity> res = new DatatransRes<>();
		List<收费设定Entity> 收费设定List = 收费设定Dao.getList();
		res.setResult(transShoufeiSheding(收费设定List));
		log.info("=================>>>收费设定数据割接完毕！！！<<<========================");
		return res;
	}

	@Override
	public IResponse parseOtherPayItem(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<收费项目其他收费Entity> res = new DatatransRes<>();
		res.setResult(transOtherPayItem(get其它收费项目List()));
		return res;
	}

	/*
	 * 收费范围
	 */
	@Override
	public IResponse parseShoufeiFanwei(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<收费范围Entity> res = new DatatransRes<>();
		List<收费范围Entity> 收费范围List = 收费范围Dao.getList();
		res.setResult(transShoufeiFanwei(收费范围List));
		log.info("=================>>>收费范围数据割接完毕！！！<<<========================");
		return res;
	}

	/*
	 * 公用分摊范围
	 */
	@Override
	public IResponse parseGongyongFentanFanwei(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<公用分摊范围Entity> res = new DatatransRes<>();
//		Integer totalCount=公用分摊范围Dao.getList().size();
//		log.info("公用分摊范围割接总数："+totalCount);
//		Integer totalPage = totalCount / 85000+1;
//		for (int i = 1; i <= totalPage; i++) {
//			log.info("========>>>开始割接第>>>>" + i + "<<<<批数据！！！<<<============");
//			Page<公用分摊范围Entity> page = new Page<公用分摊范围Entity>();
//			page.setPageSize(85000);
//			page.setBeginCount((i - 1) * page.getPageSize());
//			page.setCurrentPage(i);
//			String hql = "from 公用分摊范围Entity";
//			page = 公用分摊范围Dao.queryPage(page, hql);
//			res.getResult().putAll(transGongyongFentanFanwei(page.getResultList()));
//			log.info("========>>>第" + i + "批数据割接完毕,还剩"+(totalPage-i)+"批！！！<<<============");
//		}
		List<公用分摊范围Entity> 公摊List = 公用分摊范围Dao.getList();
		res.getResult().putAll(transGongyongFentanFanwei(公摊List));
		log.info("=================>>>收费范围数据割接完毕！！！<<<========================");
		return res;
	}

	/*
	 * 应收款
	 */
	@Override
	public IResponse parseYingshoukuan(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<应收款Entity> res = new DatatransRes<>();
		List<应收款Entity> 应收款List = new ArrayList<>();
		if (StringUtils.isNotEmpty(req.getTransField()) && StringUtils.isNotEmpty(req.getTransOp())
				&& StringUtils.isNotEmpty(req.getTransValue())) {
			应收款List = 应收款Dao.getListByHql("from 应收款Entity where " + req.getTransField() + " " + req.getTransOp() + " '"
					+ req.getTransValue() + "' order by 应收款no");
			res.setResult(transYingshoukuan(应收款List));
		} else {
			Integer totalCount = 应收款Dao.getList().size();
			Integer totalPage = totalCount / 85000 + 1;
			for (int i = 1; i <= totalPage; i++) {
				log.info("========>>>开始割接第>>>>" + i + "<<<<批数据！！！<<<============");
				Page<应收款Entity> page = new Page<应收款Entity>();
				page.setPageSize(85000);
				page.setBeginCount((i - 1) * page.getPageSize());
				page.setCurrentPage(i);
				String hql = "from 应收款Entity order by 应收款no";
				page = 应收款Dao.queryPage(page, hql);
				res.getResult().putAll(transYingshoukuan(page.getResultList()));
				log.info("========>>>第" + i + "批数据割接完毕,还剩" + (totalPage - i) + "批！！！<<<============");
			}
		}
		return res;
	}

	/*
	 * 应收款减加
	 */
	@Override
	public IResponse parseYingshoukuanJianjia(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<应收款减加Entity> res = new DatatransRes<>();
		List<应收款减加Entity> 应收款减加List = 应收款减加Dao.getList();
		res.setResult(transYingshoukuanJianjia(应收款减加List));
		return res;
	}

	/*
	 * 应预收款收据
	 */
	@Override
	public IResponse parseShouju(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<应预收款收据Entity> res = new DatatransRes<>();
		Integer totalCount = 应预收款收据Dao.getList().size();
		Integer totalPage = totalCount / 85000 + 1;
		for (int i = 1; i <= totalPage; i++) {
			log.info("========>>>开始割接第>>>>" + i + "<<<<批数据！！！<<<============");
			Page<应预收款收据Entity> page = new Page<应预收款收据Entity>();
			page.setPageSize(85000);
			page.setBeginCount((i - 1) * page.getPageSize());
			page.setCurrentPage(i);
			String hql = "from 应预收款收据Entity order by taxid";
			page = 应预收款收据Dao.queryPage(page, hql);
			res.getResult().putAll(transShouju(page.getResultList()));
			log.info("========>>>第" + i + "批数据割接完毕,还剩" + (totalPage - i) + "批！！！<<<============");
		}
		return res;
	}

	/*
	 * 实收款
	 */
	@Override
	public IResponse parseShishoukuan(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<实收款Entity> res = new DatatransRes<>();

		List<实收款Entity> 应收List = 实收款Dao.getList();

		res.getResult().putAll(transShishoukuan(应收List));

//		Integer totalCount=实收款Dao.getList().size();
//		Integer totalPage = totalCount / 84777+1;
//		for (int i = 1; i <= totalPage; i++) {
//			log.info("========>>>开始割接第>>>>" + i + "<<<<批数据！！！<<<============");
//			Page<实收款Entity> page = new Page<实收款Entity>();
//			page.setPageSize(84777);
//			page.setBeginCount((i - 1) * page.getPageSize());
//			page.setCurrentPage(i);
//			String hql = "from 实收款Entity order by 收据id";
//			page = 实收款Dao.queryPage(page, hql);
//			res.getResult().putAll(transShishoukuan(page.getResultList()));
//			System.gc();
//			log.info("========>>>第" + i + "批数据割接完毕,还剩"+(totalPage-i)+"批！！！<<<============");
//		}
		return res;
	}

	/*
	 * 其它收费应收和收据
	 */
	@Override
	public IResponse parseOtherFee(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<财务其他收费Entity> res = new DatatransRes<>();
		List<财务其他收费Entity> 其他收费List = 财务其他收费Dao.getList();
		res.setResult(transOtherFee(其他收费List));
		return res;
	}

	/*
	 * 其它收费实收和退款
	 */
	@Override
	public IResponse parseOtherFeeDeposit(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<财务其他收费Entity> res = new DatatransRes<>();
		List<财务其他收费Entity> 其他收费List = 财务其他收费Dao.getList();
		res.setResult(transOtherFeeDeposit(其他收费List));
		return res;
	}

	@Override
	public IResponse parseZongbiaoFentan(DataTransReq req) {
		DatatransRes<分摊分摊用量临时Entity> res = new DatatransRes<>();
		// TODO Auto-generated method stub
		List<分摊分摊用量临时Entity> 总表用量分摊List = 分摊分摊用量临时Dao.getList();
		res.setResult(transZongbiaoFentan(总表用量分摊List));
		return res;
	}

	@Override
	public IResponse parseDanyuanZongbiaoFentan(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<分摊单元分摊用量临时Entity> res = new DatatransRes<>();

		List<分摊单元分摊用量临时Entity> 分摊List = 分摊单元分摊用量临时Dao.getList();
		res.getResult().putAll(transDanyuanZongbiaoFentan(分摊List));

//		Integer totalCount=分摊单元分摊用量临时Dao.getList().size();
//		Integer totalPage = totalCount / 85000+1;
//		for (int i = 1; i <= totalPage; i++) {
//			log.info("========>>>开始割接第>>>>" + i + "<<<<批数据！！！<<<============");
//			Page<分摊单元分摊用量临时Entity> page = new Page<分摊单元分摊用量临时Entity>();
//			page.setPageSize(85000);
//			page.setBeginCount((i - 1) * page.getPageSize());
//			page.setCurrentPage(i);
//			String hql = "from 分摊单元分摊用量临时Entity order by 总表id,单元id";
//			page = 分摊单元分摊用量临时Dao.queryPage(page, hql);
//			res.getResult().putAll(transDanyuanZongbiaoFentan(page.getResultList()));
//			log.info("========>>>第" + i + "批数据割接完毕,还剩"+(totalPage-i)+"批！！！<<<============");
//		}
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse parseYinhangHuazhangDanyuanShaixuan(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes<银行划账单元筛选Entity> res = new DatatransRes<>();

		List<Object[]> 划账List = 银行划账单元筛选Dao.createSQLQuery("SELECT a.单元id,c.项目id,d.收费项目,0 AS depositType "
				+ "FROM 银行划帐_单元筛选 a,车场管理_车辆 b,收费范围 c,收费设定 d "
				+ "WHERE a.收费项目 NOT LIKE '%滞纳金%' AND a.收费项目='车位管理费' AND b.离退日期 IS NULL "
				+ "AND a.单元id=b.车主住所id AND b.车位id=c.单元id AND c.项目id =d.项目id " + "union "
				+ "SELECT a.单元id,c.项目id,d.收费项目,0 as depositType " + "FROM 银行划帐_单元筛选 a,车场管理_车辆 b,收费范围 c,收费设定 d "
				+ "WHERE a.收费项目 NOT LIKE '%滞纳金%' AND a.收费项目='出租车位管理服务费' AND b.离退日期 IS NULL "
				+ "AND a.单元id=b.车主住所id AND b.车位id=c.单元id AND c.项目id =d.项目id and a.收费项目=d.收费项目 " + "union "
				+ "SELECT a.单元id,c.项目id,d.收费项目,0 AS depositType " + "FROM 银行划帐_单元筛选 a,车场管理_车辆 b,收费范围 c,收费设定 d "
				+ "WHERE a.收费项目 NOT LIKE '%滞纳金%' AND a.收费项目='代收车位租金' AND b.离退日期 IS NULL "
				+ "AND a.单元id=b.车主住所id AND b.车位id=c.单元id AND c.项目id =d.项目id and a.收费项目=d.收费项目 " + "union "
				+ "select a.单元id,null as 项目id,a.收费项目,0 AS depositType "
				+ "from 银行划帐_单元筛选 a where a.收费项目 not like '%滞纳金%' and a.收费项目 not like '%违约金%' and a.收费项目 not in('车位管理费','出租车位管理服务费','代收车位租金') "
				+ "union  " + "SELECT a.单元id,b.项目id,b.收费项目,1 as depositType  "
				+ "FROM 收费范围 a,收费设定 b,滞纳金_收费项目 c,银行划帐_单元筛选 d "
				+ "WHERE a.项目id=b.项目id AND b.收费项目=c.收费项目 AND c.费用名称 ='违约金' and a.单元id=d.单元id and c.费用名称=d.收费项目 ")
				.list();
		//特殊处理数据
		Object[] t1 = {"5DAC45BE-9B37-43D5-AE1E-B39FB1257C67","F2C422E9-258F-485C-832F-79BD1DE5E9E2","车位管理费",1};
		划账List.add(t1);
		Object[] t2 = {"28489E87-5B5F-4F8A-8C6D-A560B3E85A82","F2C422E9-258F-485C-832F-79BD1DE5E9E2","车位管理费",1};
		划账List.add(t2);
		Object[] t3 = {"E4C4F8A2-32AB-496C-AE2F-0967A6768C33","F2C422E9-258F-485C-832F-79BD1DE5E9E2","车位管理费",1};
		划账List.add(t3);
		Object[] t4 = {"D6F5BA98-0AB5-4AF2-B394-00334396FB67","F2C422E9-258F-485C-832F-79BD1DE5E9E2","车位管理费",1};
		划账List.add(t4);
		
		res.getResult().putAll(transYinhangHuazhangDanyuanShaixuan(划账List));

		return res;
	}

}
