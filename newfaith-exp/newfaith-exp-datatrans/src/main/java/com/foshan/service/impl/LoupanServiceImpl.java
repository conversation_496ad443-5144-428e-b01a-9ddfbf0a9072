package com.foshan.service.impl;


import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.foshan.entity.community.CommunityBuildingEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.mssql.楼盘Entity;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.datatrans.DatatransRes;
import com.foshan.service.LoupanService;

@Service("loupanService")
public class LoupanServiceImpl extends GenericDatatransService implements LoupanService {

	/*
	 * 楼盘
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse parseLoupan(DataTransReq req) {
		DatatransRes res = new DatatransRes();
		List<楼盘Entity> 楼盘List = 楼盘Dao.getList();
		res.setResult(transLoupan(楼盘List));
		return res;
	}

	/*
	 * 楼阁
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse parseLouge(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		List<CommunityDistrictEntity> disList = new ArrayList<>();
		if (StringUtils.isNotEmpty(req.getTransField()) && StringUtils.isNotEmpty(req.getTransOp())
				&& StringUtils.isNotEmpty(req.getTransValue())) {
			disList = communityDistrictDao.getListByHql("from CommunityDistrictEntity where " + req.getTransField()
					+ " " + req.getTransOp() + " '" + req.getTransValue() + "'");
		} else {
			disList = communityDistrictDao.getList();
		}

		res.setResult(transLouge(disList));
		return res;
	}
	
	

	/*
	 * 楼梯
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse parseLouti(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();

		List<Object[]> 楼梯List = 单元Dao.createSQLQuery("SELECT a.单元id,b.楼阁id,a.楼梯id,a.单元编号,a.楼层,a.房号,a.建筑面积,"
				+ "a.使用面积,a.收费面积,a.朝向,a.功能,a.附加面积,c.楼梯名称,c.楼梯NO FROM 单元 a,楼阁 b,楼梯 c "
				+ "WHERE a.楼梯id=c.楼梯id AND b.楼阁id=c.楼阁id").list();

		res.setResult(transLouti(楼梯List));

		return res;
	}

	/*
	 * 单元
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse parseDanyuan(DataTransReq req) {
		// TODO Auto-generated method stub
		DatatransRes res = new DatatransRes();
		List<CommunityBuildingEntity> buildingList = new ArrayList<>();
		if (StringUtils.isNotEmpty(req.getTransField()) && StringUtils.isNotEmpty(req.getTransOp())
				&& StringUtils.isNotEmpty(req.getTransValue())) {
			buildingList = communityBuildingDao.getListByHql("from CommunityBuildingEntity where " + req.getTransField()
					+ " " + req.getTransOp() + " '" + req.getTransValue() + "'");
		} else {
			buildingList = communityBuildingDao.getList();
		}

		res.setResult(transDanyuan(buildingList));

		return res;
	}

	
}
