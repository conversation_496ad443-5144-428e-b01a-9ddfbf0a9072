package com.foshan.util.datatrans;

import java.util.HashMap;

public class Datatrans {
	public static HashMap<String, Integer> buildingIdList = new HashMap<>();
	public static HashMap<String, Integer> shuxingIdList = new HashMap<>();
	public static HashMap<String, Integer> gongshiIdList = new HashMap<>();
	public static HashMap<String, Integer> meterIdList = new HashMap<>();
	public static HashMap<String, Integer> payItemIdList = new HashMap<>();
	public static HashMap<String, Integer> payItemNameList = new HashMap<>();
	public static HashMap<String, Integer> estateIdList = new HashMap<>();
	public static HashMap<String, Integer> yingshoukuanIdList = new HashMap<>();
	public static HashMap<String, Integer> shoujuIdList = new HashMap<>();
	public static HashMap<String, Integer> zongbiaoFentanIdList = new HashMap<>();
	public static HashMap<String, Integer> huzhuIdList = new HashMap<>();
	public static HashMap<String, Integer> repairIdList= new HashMap<>();
	

}
