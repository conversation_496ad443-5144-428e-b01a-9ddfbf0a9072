package com.foshan.config.mssql;

import java.util.Properties;

import javax.annotation.Resource;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.orm.hibernate5.LocalSessionFactoryBean;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class DruidMssqlConfig {

	@Value("${spring.jpa.properties.hibernate.current_session_context_class}")
	public String current_session_context_class;

	@Bean(name = "mssqlDataSource")
	@ConfigurationProperties(prefix = "spring.datasource.druid.mssql")
	public DataSource mssqlDataSource() {
		log.info("MssqlDataSource构建完成！！！");
		return DataSourceBuilder.create().driverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver")
				.type(com.alibaba.druid.pool.DruidDataSource.class).build();
	}

	@Resource(name = "mssqlDataSource")
	private DataSource mssqlDataSource;

	@Bean(name = "mssqlSessionFactory")
	public LocalSessionFactoryBean mssqlSessionFactory() {
		LocalSessionFactoryBean sessionFactory = new LocalSessionFactoryBean();
		sessionFactory.setDataSource(mssqlDataSource);
		sessionFactory.setPackagesToScan("com.foshan.entity");
		sessionFactory.setHibernateProperties(getMssqlDBProperties());
		return sessionFactory;
	}

	private Properties getMssqlDBProperties() {
		Properties properties = new Properties();
		properties.setProperty("hibernate.current_session_context_class", current_session_context_class);
		properties.setProperty("hibernate.dialect", "org.hibernate.dialect.SQLServer2012Dialect");
		return properties;
	}

}
