package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.datatrans.DatatransRes;

@RestController
@ResponseBody
public class ZongbiaoController extends BaseTransController{
	/*
	 * 表属性
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseBiaoshuxing", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseBiaoshuxing(@RequestBody DataTransReq req, HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) zongbiaoService.parseBiaoshuxing(req);
		return res;
	}
	
	/*
	 * 自定义公式
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseZidingyiGongshi", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseZidingyiGongshi(@RequestBody DataTransReq req, HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) zongbiaoService.parseZidingyiGongshi(req);
		return res;
	}
	
	/*
	 * 总表
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseZongbiao", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseZongbiao(@RequestBody DataTransReq req, HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) zongbiaoService.parseZongbiao(req);
		return res;
	}
	
	/*
	 * 总表抄表
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseZongbiaoChaobiao", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseZongbiaoChaobiao(@RequestBody DataTransReq req, HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) zongbiaoService.parseZongbiaoChaobiao(req);
		return res;
	}
	
	/*
	 * 用户表
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseYonghubiao", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseYonghubiao(@RequestBody DataTransReq req, HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) zongbiaoService.parseYonghubiao(req);
		return res;
	}
	
	
	/*
	 * 用户表抄表
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseYonghubiaoChaobiao", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseYonghubiaoChaobiao(@RequestBody DataTransReq req, HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) zongbiaoService.parseYonghubiaoChaobiao(req);
		return res;
	}

	

}
