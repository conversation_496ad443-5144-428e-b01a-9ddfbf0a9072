package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.datatrans.DatatransRes;

@RestController
@ResponseBody
public class CheweiController extends BaseTransController {
	/*
	 * 车位
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseChewei", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseChewei(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) cheweiService.parseChewei(req);
		return res;
	}

	/*
	 * 车位数据处理
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseCheweiData", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseCheweiData(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) cheweiService.parseCheweiData(req);
		return res;
	}
}
