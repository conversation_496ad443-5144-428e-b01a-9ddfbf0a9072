package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.datatrans.DatatransRes;

@RestController
@ResponseBody
public class FeeController extends BaseTransController {
	/*
	 * 收费设定
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseShoufeiSheding", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseShoufeiSheding(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseShoufeiSheding(req);
		return res;
	}
	
	/*
	 * 其他收费项目
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseOtherPayItem", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseOtherPayItem(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseOtherPayItem(req);
		return res;
	}

	/*
	 * 收费范围
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseShoufeiFanwei", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseShoufeiFanwei(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseShoufeiFanwei(req);
		return res;
	}

	/*
	 * 公用分摊范围
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseGongyongFentanFanwei", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseGongyongFentanFanwei(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseGongyongFentanFanwei(req);
		return res;
	}

	/*
	 * 应收款
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseYingshoukuan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseYingshoukuan(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseYingshoukuan(req);
		return res;
	}

	/*
	 * 应收款减加
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseYingshoukuanJianjia", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseYingshoukuanJianjia(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseYingshoukuanJianjia(req);
		return res;
	}

	/*
	 * 应预收款收据
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseShouju", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseShouju(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseShouju(req);
		return res;
	}

	/*
	 * 实收款
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseShishoukuan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseShishoukuan(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseShishoukuan(req);
		return res;
	}


	/*
	 * 其他收费实收和收据
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseOtherFee", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseOtherFee(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseOtherFee(req);
		return res;
	}
	
	/*
	 * 其他收费应收和退款
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseOtherFeeDeposit", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseOtherFeeDeposit(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseOtherFeeDeposit(req);
		return res;
	}


	/*
	 * 总表用量分摊
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseZongbiaoFentan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseZongbiaoFentan(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseZongbiaoFentan(req);
		return res;
	}

	/*
	 * 单元总表用量分摊
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseDanyuanZongbiaoFentan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseDanyuanZongbiaoFentan(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseDanyuanZongbiaoFentan(req);
		return res;
	}
	
	/*
	 * 银行划账单元筛选
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseYinhangHuazhangDanyuanShaixuan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseYinhangHuazhangDanyuanShaixuan(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) feeService.parseYinhangHuazhangDanyuanShaixuan(req);
		return res;
	}
	
}
