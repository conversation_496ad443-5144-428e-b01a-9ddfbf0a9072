package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.datatrans.DatatransRes;

@RestController
@ResponseBody
public class TransController extends BaseTransController {

	/*
	 * 批量割接(楼盘、楼阁、单元、楼梯)
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/batchTransLoupan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes batchTransLoupan(HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) transService.batchTransLoupan();
		return res;
	}

	/*
	 * 批量割接(表属性、自定义公式、总表、总表_抄表、用户表、用户表_抄表)
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/batchTransZongbiao", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes batchTransZongbiao(HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) transService.batchTransZongbiao();
		return res;
	}

	/*
	 * 批量割接(收费设定、收费范围、公用分摊范围、应收款、应收款_减加、应预收款收据、实收款、财务其他收费、总表用量分摊、单元总表用量分摊)
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/batchTransFee", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes batchTransFee(HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) transService.batchTransFee();
		return res;
	}

	/*
	 * 批量割接(业主)
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/batchYezhu", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes batchYezhu(HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) transService.batchYezhu();
		return res;
	}

	/*
	 * 批量割接(车位)
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/batchChewei", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes batchChewei(HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) transService.batchChewei();
		return res;
	}

	/*
	 * 批量割接(以上所有数据割接)
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/batchTrans", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes batchTrans(HttpServletRequest request) throws JsonProcessingException {
		DatatransRes res = (DatatransRes) transService.batchTrans();
		return res;
	}

	/*
	 * 割接结果
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/transResult", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes transResult(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) transService.transResult(req);
		return res;
	}
}
