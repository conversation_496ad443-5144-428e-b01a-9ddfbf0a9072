package com.foshan.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.request.datatrans.DataTransReq;
import com.foshan.form.response.datatrans.DatatransRes;

@RestController
@ResponseBody
public class YezhuController extends BaseTransController {
	/*
	 * 户主
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseHuzhu", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseHuzhu(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) yezhuService.parseHuzhu(req);
		return res;
	}

	/*
	 * 住户成员
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value = "/parseChengyuan", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public DatatransRes parseChengyuan(@RequestBody DataTransReq req, HttpServletRequest request)
			throws JsonProcessingException {
		DatatransRes res = (DatatransRes) yezhuService.parseChengyuan(req);
		return res;
	}
}
