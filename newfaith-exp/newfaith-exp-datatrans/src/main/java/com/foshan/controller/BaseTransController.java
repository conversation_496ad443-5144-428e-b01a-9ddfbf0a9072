package com.foshan.controller;

import javax.annotation.Resource;

import com.foshan.service.CheweiService;
import com.foshan.service.FeeService;
import com.foshan.service.LoupanService;
import com.foshan.service.RepairService;
import com.foshan.service.TransService;
import com.foshan.service.YezhuService;
import com.foshan.service.ZongbiaoService;

public class BaseTransController extends BaseController {

	@Resource(name = "loupanService")
	protected LoupanService testService;
	@Resource(name = "transService")
	protected TransService transService;
	@Resource(name = "zongbiaoService")
	protected ZongbiaoService zongbiaoService;
	@Resource(name = "feeService")
	protected FeeService feeService;
	@Resource(name = "yezhuService")
	protected YezhuService yezhuService;
	@Resource(name = "cheweiService")
	protected CheweiService cheweiService;
	@Resource(name = "repairService")
	protected RepairService repairService;

}
