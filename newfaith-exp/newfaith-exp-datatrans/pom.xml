<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.foshan</groupId>
		<artifactId>newfaith-exp</artifactId>
		<version>V200R001B03</version>
	</parent>
	<artifactId>newfaith-exp-datatrans</artifactId>
	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>newfaith-exp-community</artifactId>
			<version>${project.version}</version>
		</dependency>
		
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
		</dependency>

	</dependencies>
</project>