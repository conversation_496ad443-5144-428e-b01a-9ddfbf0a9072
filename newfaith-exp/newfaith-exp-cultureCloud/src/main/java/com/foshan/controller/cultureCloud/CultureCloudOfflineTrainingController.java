package com.foshan.controller.cultureCloud;


import com.foshan.form.cultureCloud.request.CultureCloudOfflineTrainingReq;
import com.foshan.form.cultureCloud.request.GetCultureCloudOfflineTrainingInfoReq;
import com.foshan.form.cultureCloud.request.GetCultureCloudOfflineTrainingListReq;
import com.foshan.form.response.IResponse;
import com.foshan.form.cultureCloud.response.cultureCloudTrain.GetCultureCloudTrainListRes;
import com.foshan.form.cultureCloud.response.cultureCloudTrain.GetCultureCloudTrainInfoRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import com.fasterxml.jackson.core.JsonProcessingException;

/**
 * 线下培训控制器
 *
 * <AUTHOR> Code Generator
 */
@RestController
@Api(value = "线下培训接口", tags = "线下培训接口")
public class CultureCloudOfflineTrainingController extends BaseCultureCloudController {


    // 添加线下培训
    @ApiOperation(value = "添加线下培训(addOfflineTraining)", httpMethod = "POST", notes = "添加线下培训信息")
    @ResponseBody
    @RequestMapping(value = "/addOfflineTraining", method = {RequestMethod.POST}, 
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse addOfflineTraining(@RequestBody CultureCloudOfflineTrainingReq req, HttpServletRequest request) 
            throws JsonProcessingException {
        return cultureCloudOfflineTrainingService.addOfflineTraining(req);
    }

    // 修改线下培训
    @ApiOperation(value = "修改线下培训(modifyOfflineTraining)", httpMethod = "POST", notes = "修改线下培训信息")
    @ResponseBody
    @RequestMapping(value = "/modifyOfflineTraining", method = {RequestMethod.POST}, 
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse modifyOfflineTraining(@RequestBody CultureCloudOfflineTrainingReq req, HttpServletRequest request) 
            throws JsonProcessingException {
        return cultureCloudOfflineTrainingService.modifyOfflineTraining(req);
    }

    // 删除线下培训
    @ApiOperation(value = "删除线下培训(deleteOfflineTraining)", httpMethod = "POST", notes = "删除线下培训信息")
    @ResponseBody
    @RequestMapping(value = "/deleteOfflineTraining", method = {RequestMethod.POST}, 
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse deleteOfflineTraining(@RequestBody CultureCloudOfflineTrainingReq req, HttpServletRequest request) 
            throws JsonProcessingException {
        return cultureCloudOfflineTrainingService.deleteOfflineTraining(req);
    }

    // 获取线下培训详情
    @ApiOperation(value = "获取线下培训详情(getOfflineTrainingInfo)", httpMethod = "POST", notes = "获取线下培训详细信息")
    @ResponseBody
    @RequestMapping(value = "/getOfflineTrainingInfo", method = {RequestMethod.POST}, 
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudTrainInfoRes getOfflineTrainingInfo(@RequestBody GetCultureCloudOfflineTrainingInfoReq req, HttpServletRequest request) 
            throws JsonProcessingException {
        return (GetCultureCloudTrainInfoRes) cultureCloudOfflineTrainingService.getOfflineTrainingInfo(req);
    }

    // 获取线下培训列表
    @ApiOperation(value = "获取线下培训列表(getOfflineTrainingList)", httpMethod = "POST", notes = "获取线下培训列表信息")
    @ResponseBody
    @RequestMapping(value = "/getOfflineTrainingList", method = {RequestMethod.POST}, 
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCultureCloudTrainListRes getOfflineTrainingList(@RequestBody GetCultureCloudOfflineTrainingListReq req, HttpServletRequest request) 
            throws JsonProcessingException {
        return (GetCultureCloudTrainListRes) cultureCloudOfflineTrainingService.getOfflineTrainingList(req);
    }

    // 上下架培训
    @ApiOperation(value = "上下架培训(publishOfflineTraining)", httpMethod = "POST", notes = "修改培训上下架状态")
    @ResponseBody
    @RequestMapping(value = "/publishOfflineTraining", method = {RequestMethod.POST}, 
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public IResponse publishOfflineTraining(@RequestBody GetCultureCloudOfflineTrainingListReq req, HttpServletRequest request) 
            throws JsonProcessingException {
        return cultureCloudOfflineTrainingService.publishOfflineTraining(req);
    }

}