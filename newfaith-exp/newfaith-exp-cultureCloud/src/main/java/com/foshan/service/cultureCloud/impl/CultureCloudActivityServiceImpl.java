package com.foshan.service.cultureCloud.impl;

import com.foshan.dao.generic.Page;
import com.foshan.entity.UserEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.cultureCloud.*;
import com.foshan.entity.cultureCloud.context.CultureCloudContext;
import com.foshan.entity.cultureCloud.vo.ActivityEventVo;
import com.foshan.entity.cultureCloud.vo.ActivityVo;
import com.foshan.form.cultureCloud.*;
import com.foshan.form.cultureCloud.request.ActivityReq;
import com.foshan.form.cultureCloud.response.cultureCloudActivity.GetCultureCloudActivityInfoRes;
import com.foshan.form.cultureCloud.response.cultureCloudActivity.GetCultureCloudActivityListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.cultureCloud.ICultureCloudActivitySeatService;
import com.foshan.service.cultureCloud.ICultureCloudActivityService;
import com.foshan.util.ExcelExportUtil;
//import org.apache.commons.beanutils.PropertyUtils;
//import org.apache.commons.collections4.splitmap.AbstractIterableGetMapDecorator;
import com.foshan.util.cultureCloud.TimeCompareUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import static java.util.stream.Collectors.groupingBy;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Transactional
@Service("cultureCloudActivityService")
public class CultureCloudActivityServiceImpl extends GenericCultureCloudService implements ICultureCloudActivityService {

    private final static Logger logger = LoggerFactory.getLogger(CultureCloudActivityServiceImpl.class);

    @Resource(name = "cultureCloudActivitySeatService")
    protected ICultureCloudActivitySeatService cultureCloudActivitySeatService;



    @Override
    @Audit(operate = "新增活动")
    public IResponse addActivity(ActivityReq req) {
        GenericResponse res = new GenericResponse();
        Object userObj = getPrincipal(true);
        UserEntity user = (UserEntity) userObj;
		String depIds = getDepartment(userObj);
		if(null!=req.getDepartmentId() && StringUtils.isNoneEmpty(depIds) ) {
			 List<String> depIdList = Stream.of(depIds.split(","))  
		                .collect(Collectors.toList());
			 if(!depIdList.contains(req.getDepartmentId().toString())) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("对不起，不能操作除本部门及子部门以外的数据！");
					return res;
			 }
		}
        //没写到 先注释
//        CmsShop shop = (CmsShop) session.getAttribute("shop");

        String shopPath = null;
//        if (shop != null && StringUtils.isNotBlank(shop.getShopId())) {
//            shopPath = shop.getShopPath();
//        }

        if (user == null) {
            res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
            return res;
        }

        //艺术类型
        if (StringUtils.isNotBlank(req.getActivityArtType())) {
            String activityArtType = req.getActivityArtType();
            String substring = activityArtType.substring(0, activityArtType.length() - 1);
            req.setActivityArtType(substring);
        }

        //判断场次时间是否与活动室开放时间有冲突
        //还没写到 先注释
//        if(req.getIsElecSign()==1 && req.getActivityIsReservation() != 1 && StringUtils.isNotBlank(req.getVenueRoomId())){
//            Map<String,Object> param=new HashMap<>();
//            param.put("eventDateStr",req.getEventDate());
//            param.put("eventTimeStr",req.getEventTime());
//            param.put("roomId",req.getVenueRoomId());
//            Result result=cmsRoomBookService.roomTimeConflict(param);
//            if(result.getStatus()==500){
//                return "timeConflictErr";
//            }
//        }

        //取票时间及地点
        //还没写到 先注释
//        if(null != req.getActivityId()) {
//            List<CmsActivityExchangeTimeDto> activityExchangeTimeList = cmsActivityExchangeTimeService.queryCmsActivityExchangeTimeAll(null, req.getActivityId());
//            if(activityExchangeTimeList != null && activityExchangeTimeList.size()>0) {
//                for (CmsActivityExchangeTimeDto cmsActivityExchangeTimeDto : activityExchangeTimeList) {
//                    //不管是否新增都已存在ActivityId，直接查cms_activity_exchange_time表的换票时间，来进行业务约束：换票日期不能晚于活动结束日期，不然不让创建
//                    //换票日期的结束时间不能大于等于活动结束日期
//
//                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//                    String exchangeTimeStr = sdf.format(cmsActivityExchangeTimeDto.getExchangeDate());
//                    String exchangeEndTime = exchangeTimeStr + " " + cmsActivityExchangeTimeDto.getExchangeEndTime();
//                    String activityEndTime = "";
//                    if (StringUtils.isNotBlank(req.getActivityEndTime())){
//                        activityEndTime += req.getActivityEndTime() + " ";
//                    }
//                    if (StringUtils.isNotBlank(req.getActivityTime())){
//                        activityEndTime += req.getActivityTime().split("-")[1];
//                    }
//                    int res = exchangeEndTime.compareTo(activityEndTime);
//                    if(res >= 0) {
//                        return "bsErrExchangeEndTime";
//                    }
//                }
//            }
//        }

        //活动额外信息
        //还没写到 先注释
//        CultureCloudActivityExtendInfoEntity extendInfo = new CultureCloudActivityExtendInfoEntity();
//        extendInfo.setBranchDeptId(branchDeptId);
//        extendInfo.setBranchDeptShopPath(branchDeptShopPath);
//        extendInfo.setShopProvince(staticServer.getCityInfo().split(",")[3]);
//        if(StringUtils.isNotBlank(detailTimeLimitMinutes)) {//前端参数为精度为1的小时数，转换成分钟数（应为整数）存到数据库
//            extendInfo.setDetailTimeLimitMinutes(new BigDecimal(detailTimeLimitMinutes).multiply(new BigDecimal(60)).intValue());
//        }
//        extendInfo.setTimeLimitOfPickingUpTickets(timeLimitOfPickingUpTickets);
//        extendInfo.setNeedSign(req.getNeedSign());
//        req.setExtendInfo(extendInfo);

//        Result result = null;

//        if (null != req.getActivityId() && (req.getIsNew() == null || req.getIsNew() != 1)) {
            //更新的逻辑 先注释
//            List<CultureCloudActivityOrderEntity> cmsActivityOrders = cultureCloudActivityOrderDao.getListByHql("select distinct a from CultureCloudActivityOrderEntity a where a.activity.id ="+req.getActivityId());
//            //存在订单的时候 不对座位信息进行修改
//            boolean hadOrderInfo = false;
//            if (cmsActivityOrders != null && cmsActivityOrders.size() > 0) {
//                hadOrderInfo = true;
//            }
//
//            if (StringUtils.isNotBlank(req.getActivityCreateTimeBak())) {
//                req.setActivityCreateTime(req.getActivityCreateTimeBak());
//            }
//            String rsStrs = activityService.editActivity(req, user, null, hadOrderInfo, shopPath);
//
//            //类型标签
//            Set<String> tagSet = new HashSet<String>();
//            if (StringUtils.isNotBlank(req.getTagIds())){
//                String[] ids = req.getTagIds().split(",");
//                tagSet.addAll(Arrays.asList(ids));
//            }
//            cmsTagRelateService.updateEntityTagRelateList(req.getActivityId(), Constant.TYPE_ACTIVITY, tagSet);
//
//             ActivityVo cmsActivityVO = new  ActivityVo();
//            CmsActivity cmsActivity = activityService.selectByPrimaryKey(req.getActivityId());
//            copyProperties(cmsActivityVO, cmsActivity);
//            cmsActivityVO.setExtendInfo(req.getExtendInfo());
//            cmsActivityVO.setTagIds(req.getTagIds());
//            cmsActivityVO.setVenueId(req.getVenueId());
//            if (StringUtils.isNotBlank(req.getActivityArtType())) {
//                cmsActivityVO.setActivityArtType(req.getActivityArtType());
//            }
//            if (StringUtils.isNotBlank(req.getFixedTel())) {
//                cmsActivityVO.setFixedTel(req.getFixedTel());
//            }
//
//            cmsActivityVO.setUserId(user.getUserId());
//            cmsActivityVO.setShopProvince(staticServer.getCityInfo().split(",")[3]);
//            String urlStr = "";
//            if(hadOrderInfo) {
//                urlStr = "why/back/activity/updateActivityHasOrder";
//            }else {
//                //同步场次
//                cmsActivityVO = getCultureCloudActivityEventEntityList(req, cmsActivityVO, user);
//                urlStr = "why/back/activity/addActivity";
//            }
//            result = currencyInterfaceService.postForEntityResult(staticServer.getInterfaceGateway() + urlStr, cmsActivityVO);

//        }else
        {
            if (StringUtils.isNotBlank(req.getActivityCreateTimeBak())) {
                    req.setActivityCreateTime(req.getActivityCreateTimeBak());
            } else {
                req.setActivityCreateTime(new Timestamp(new Date().getTime()).toString().replace(".0",""));
            }

            res = (GenericResponse) addActivity(req, user, shopPath);

            //类型标签 还没写到 先注释
//            if (StringUtils.isNotBlank(req.getTagIds())){
//                String[] ids = req.getTagIds().split(",");
//                cmsTagRelateService.insertTagRelateList(req.getActivityId(), Constant.TYPE_ACTIVITY, ids);
//            }
            if(res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
                req.setActivityId(Integer.parseInt(res.getRetInfo()));
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }else {
                return res;
            }
            //返回给前端的 可以注释
            ActivityVo cmsActivityVO = new  ActivityVo();
            try {
                copyProperties(cmsActivityVO, req);
            } catch (Exception e) {
                e.printStackTrace();
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO+e.getMessage());
                return res;
            }
            try {
                cmsActivityVO = getCultureCloudActivityEventEntityList(req, cmsActivityVO, user);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }


            //电子签到活动需要绑定预约活动室（湖南电子屏）
            //不知道有没有用 先注释
//            if(req.getIsElecSign()==1 && req.getActivityIsReservation() != 1 && StringUtils.isNotBlank(req.getVenueRoomId())){
//                SimpleDateFormat df=new SimpleDateFormat("yyyy-MM-dd");
//                SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                List<ActivityEventVo> eventList=cmsActivityVO.getCultureCloudActivityEventEntityList();
//                for(ActivityEventVo event:eventList){
//                    Map<String,Object> param=new HashMap<>();
//                    param.put("roomId",req.getVenueRoomId());
//                    param.put("curDate",df.parse(event.getEventDate()));
//                    List<CmsRoomBook> bookList=cmsRoomBookMapper.queryCmsRoomBookListByRoomId(param);
//                    if(bookList.size()==0){
//                        CmsRoomBook roomBook=new CmsRoomBook();
//                        roomBook.setBookId(UUIDUtils.createUUId());
//                        roomBook.setCurDate(df.parse(event.getEventDate()));
//                        roomBook.setOpenPeriod(event.getEventTime());
//                        roomBook.setDayOfWeek(DateUtils.getWeek(df.parse(event.getEventDate())));
//                        roomBook.setBookStatus(3);
//                        roomBook.setCreateTime(new Date());
//                        roomBook.setUpdateTime(new Date());
//                        roomBook.setRoomId(req.getVenueRoomId());
//                        roomBook.setSysNo("0");
//                        roomBook.setRelateType(1);
//                        roomBook.setRelateId(req.getActivityId());
//                        roomBook.setRelateEventId(event.getEventId());
//                        cmsRoomBookService.addCmsRoomBook(roomBook);
//                    }else {
//                        boolean flag=false;
//                        Date eventStartTime=sdf.parse(event.getEventDate()+" "+event.getEventTime().split("-")[0]+":00");
//                        Date eventEndTime=sdf.parse(event.getEventDate()+" "+event.getEventTime().split("-")[1]+":00");
//                        for(CmsRoomBook book:bookList){
//                            Date bookStartTime=sdf.parse(df.format(book.getCurDate())+" "+book.getOpenPeriod().split("-")[0]+":00");
//                            Date bookEndTime=sdf.parse(df.format(book.getCurDate())+" "+book.getOpenPeriod().split("-")[1]+":00");
//                            if(eventStartTime.getTime()-bookStartTime.getTime()>=0 && eventEndTime.getTime()-bookEndTime.getTime()<=0){
//                                book.setRelateType(1);
//                                book.setRelateId(req.getActivityId());
//                                book.setRelateEventId(event.getEventId());
//                                book.setBookStatus(3);
//                                cmsRoomBookMapper.editCmsRoomBook(book);
//                                flag=true;
//                                break;
//                            }
//                        }
//                        if(!flag){
//                            CmsRoomBook roomBook=new CmsRoomBook();
//                            roomBook.setBookId(UUIDUtils.createUUId());
//                            roomBook.setCurDate(df.parse(event.getEventDate()));
//                            roomBook.setOpenPeriod(event.getEventTime());
//                            roomBook.setDayOfWeek(DateUtils.getWeek(df.parse(event.getEventDate())));
//                            roomBook.setBookStatus(3);
//                            roomBook.setCreateTime(new Date());
//                            roomBook.setUpdateTime(new Date());
//                            roomBook.setRoomId(req.getVenueRoomId());
//                            roomBook.setSysNo("0");
//                            roomBook.setRelateType(1);
//                            roomBook.setRelateId(req.getActivityId());
//                            roomBook.setRelateEventId(event.getEventId());
//                            cmsRoomBookService.addCmsRoomBook(roomBook);
//                        }
//                    }
//                }
//            }

            //返回给前端的 可以注释
            if(StringUtils.isNoneBlank(req.getShowOffline()) && "offline".equals(req.getShowOffline())){
                cmsActivityVO.setShowOffline(req.getShowOffline());
            }

            cmsActivityVO.setUserId(user.getId().toString());
//            cmsActivityVO.setShopProvince(staticServer.getCityInfo().split(",")[3]);
//            result = currencyInterfaceService.postForEntityResult(staticServer.getInterfaceGateway() + "why/back/activity/addActivity", cmsActivityVO);


            try {
                if (req != null) {
                    //if(activity.getActivityState()!=6){
                    //	return Result.ok(Constant.RESULT_STR_SUCCESS);
                    //}
                    //编辑的 注释掉
//                    if (StringUtils.isNotBlank(req.getActivityId())) {
//                         ActivityVo oldCmsActivity = activityService.queryCmsActivityByActivityId(activity.getActivityId());
//                        if(oldCmsActivity!=null){
//                            //如果状态为审核未通过（非外地同步过来的订单），修改后变为未审核
//                            if(StringUtils.isNoneBlank(activity.getShowOffline()) && "offline".equals(activity.getShowOffline())){
//                                activity.setActivityIsDel(3);
//                            }else{
//                                if(oldCmsActivity.getActivityIsDel() == 3 && activity.getAreaType()==null){
//                                    activity.setActivityIsDel(0);
//                                }
//                            }
//
//                            List<CmsActivityOrder> cmsActivityOrders = cmsActivityOrderService.queryCmsActivityOrderListByActivityId(activity.getActivityId());
//                            //存在订单的时候 不对座位信息进行修改
//                            boolean hadOrderInfo = false;
//                            if (cmsActivityOrders != null && cmsActivityOrders.size() > 0) {
//                                hadOrderInfo = true;
//                            }
//
//                            String[] ids = activity.getTagIds().split(",");
//                            String rsStrs = activityService.editActivity(activity, null, hadOrderInfo, ids);
//
//                            try {
//                                //这里是编辑操作
//                                Object[] args = {activity};
//                                cmsActivityLog.createLog(activity.getUserId(), args, null, ActivityInspect.User_Type_Admin, ActivityOperationEnum.EDITACTIVITY, rsStrs);
//                            } catch (Exception e) {
//                                e.printStackTrace();
//                                BizException.Throw(e.getMessage());
//                            }
//
//                            if(rsStrs.equals(Constant.RESULT_STR_SUCCESS)){
//                                return Result.ok(rsStrs);
//                            }else{
//                                return Result.err(rsStrs,"", "操作失败");
//                            }
//                        }
//                    }
                    String rsStrs = "";
//                    if (StringUtils.isNotBlank(req.getUserId())) {
                        String[] ids = StringUtils.isNoneBlank(req.getTagIdList()) ? req.getTagIdList().split(",") : new String[0];
//                        SysUser sysUser = sysUserService.querySysUserById(activity.getUserId());
//                    copyProperties(cmsActivityVO, req);
                        rsStrs = addActivity(cmsActivityVO, ids);
//                        try {
//                            //这里是新增操作
//                            Object[] args = {activity};
//                            cmsActivityLog.createLog(activity.getUserId(),   args, null, ActivityInspect.User_Type_Admin, ActivityOperationEnum.CREATEACTIVITY, rsStrs);
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                            BizException.Throw(e.getMessage());
//                        }
//                    } else {
//                        res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
//                        res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO+);
//                        return Result.err(Constant.RESULT_STR_NOACTIVE,"", "操作失败");
//                    }
//                    if(rsStrs.equals(Constant.RESULT_STR_SUCCESS)){
//                        return Result.ok(Constant.RESULT_STR_SUCCESS);
//                    }else{
//                        return Result.err(rsStrs,"", "操作失败");
//                    }
                }
            } catch (Exception e) {
                logger.info("saveActivity error {}", e);
                res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO+e.getMessage());
            }


        }

        //关于图片等的 先注释
//        if (res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
//
//            if (commonExternalLinkDto.getWebsite() != null){
//                if (StringUtils.isNotBlank(commonExternalLinkId)){
//                    commonExternalLinkDto.setId(commonExternalLinkId);
//                }
//                commonExternalLinkDto.setTypeStr("ACTIVITY");
//                commonExternalLinkDto.setRelatedId(req.getActivityId());
//                commonExternalLinkDto.setUserId(user.getUserId());
//                currencyInterfaceService.postForEntityResult(staticServer.getChinaServerUrl()+"commonExternalLink/saveToProvince.do", commonExternalLinkDto);
//            }
//
//            //图片资源
//            List<String> imgList=null;
//            Map<String,Object> params=new HashMap<>();
//            if (StringUtils.isNotBlank(resourceImgUrl)) {
//                String[] splitImg = resourceImgUrl.split(",");
//                imgList = new ArrayList<>(Arrays.asList(splitImg));
//                if (StringUtils.isNotBlank(req.getActivityIconUrl())){
//                    for (int i = 0; i < imgList.size(); i++) {
//                        if (imgList.get(i).equals(req.getActivityIconUrl())) {
//                            imgList.remove(i);
//                            break;
//                        }
//                    }
//                }
//                params.put("resourceImgUrl", imgList);
//                params.put("resourceVideoUrl", resourceVideoUrl);
//                params.put("relateEntityId", req.getActivityId());
//                params.put("relateEntityType", 1);
//                params.put("userId", user.getUserId());
//                currencyInterfaceService.postForEntityResult(staticServer.getInterfaceGateway() + "why/back/cmsResource/saveResourceList", params);
//            }
//
//            Runnable runnable = new Runnable() {
//                @Override
//                public void run() {
//                    //上传附件
//                    Map<String,Object> attachMap=new HashMap<>();
//                    String[] attachUrlArray = null;
//                    String[] attachTitleArray = null;
//                    if (StringUtils.isNotBlank(attachUrl)){
//                        attachUrlArray = attachUrl.split(",");
//                        attachTitleArray = attachTitle.split(",");
//                    }
//                    attachMap.put("attachUrlArray",attachUrlArray);
//                    attachMap.put("attachTitleArray",attachTitleArray);
//                    attachMap.put("relateId",req.getActivityId());
//                    attachMap.put("relateType",2);
//                    attachMap.put("userId",user.getId().toString());
//                    currencyInterfaceService.postForEntityResult(staticServer.getInterfaceGateway() + "why/back/attachResource/saveResourceAttachment", attachMap);
//                }
//            };
//            new Thread(runnable).start();
//
//            return res;
//        } else {
//            throw new RuntimeException();
//        }
        return res;
    }

    /**
     * 复制活动
     * @param req
     * @return
     */
    @Override
    public IResponse copyActivity(ActivityReq req) {
        GenericResponse res = new GenericResponse();

        try {

            CultureCloudActivityDetailForm cmsActivityFrom =((GetCultureCloudActivityInfoRes) getCultureCloudActivityInfo(req)).getActivity();

            copyProperties(req,cmsActivityFrom);

            req.setActivityId(null);
            req.setActivityState(0);
            CultureCloudVenueForm venueMap = (CultureCloudVenueForm) cmsActivityFrom.getVenueList().get(0);
            req.setVenueId(venueMap.getVenueId());

            req.setActivityProvince(cmsActivityFrom.getActivityProvince());
            req.setActivityCity(cmsActivityFrom.getActivityCity());


            String tagLists = "";
            List tagList = cmsActivityFrom.getTagList();
            for (int i = 0; i < tagList.size(); i++) {
                CultureCloudTagForm tagMap = (CultureCloudTagForm) tagList.get(i);
                tagLists += tagMap.getCultureCloudTagId() + ",";
            }
            tagLists.substring(0, tagLists.length() - 1);

            req.setTagIdList(tagLists);


            String artTypeLists = "";

            List artTypeList = cmsActivityFrom.getArtTypeList();
            for (int i = 0; i < artTypeList.size(); i++) {
                CultureCloudTypeForm artTypeMap= (CultureCloudTypeForm) artTypeList.get(i);
                artTypeLists += artTypeMap.getCultureCloudTypeId() + ",";
            }
            artTypeLists.substring(0, artTypeLists.length() - 1);
            req.setArtTypeIdList(artTypeLists);


            CultureCloudDepartmentForm departmentMap =(CultureCloudDepartmentForm) cmsActivityFrom.getDepartment();
            req.setDepartmentId((Integer) departmentMap.getId());

            String typeIdLists = "";
            List typeList = cmsActivityFrom.getTypeList();
            for (int i = 0; i < typeList.size(); i++) {
                CultureCloudTypeForm typeMap = (CultureCloudTypeForm) typeList.get(i);
                typeIdLists += typeMap.getCultureCloudTypeId() + ",";
            }
            typeIdLists.substring(0, typeIdLists.length() - 1);
            req.setTypeIdList(typeIdLists);

            res = (GenericResponse) addActivity(req);


        } catch (Exception e) {
            e.printStackTrace();
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO+e.getMessage());
        }

        return res;
    }

    @Override
    public IResponse modifyCultureCloudActivity(ActivityReq req) {
        GenericResponse res = new GenericResponse();
        try {
            CultureCloudActivityEntity activity = cultureCloudActivityDao.get(req.getActivityId());
            if (null != activity) {
                Object userObj = getPrincipal(true);
                UserEntity user = (UserEntity) userObj;
                String depIds = getDepartment(userObj);
    			if(StringUtils.isNoneEmpty(depIds) ) {
   				 List<String> depIdList = Stream.of(depIds.split(","))  
   			                .collect(Collectors.toList());
   				 if((null!=req.getDepartmentId()&&!depIdList.contains(req.getDepartmentId().toString())) ||
   						 (!depIdList.contains(activity.getDepartment().getId().toString()))) {
   						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
   						res.setRetInfo("对不起，不能操作除本部门及子部门以外的数据！");
   						return res;
   				 }
   			}
                //判断活动是否已经有订票，如果有的话则只能修改活动部分内容， 不可编辑以下字段：标题、活动日期、活动时间、是否收费、在线售票。其余字段可编辑
            List<CultureCloudActivityOrderEntity> cmsActivityOrders = cultureCloudActivityOrderDao.getListByHql("select distinct a from CultureCloudActivityOrderEntity a where a.orderPayStatus!=2 and a.activityId ="+req.getActivityId());
                //存在订单的时候 不对座位信息进行修改
                boolean hadOrderInfo = false;
                if (cmsActivityOrders != null && cmsActivityOrders.size() > 0) {
                    hadOrderInfo = true;
                }
                res = (GenericResponse) editActivity(req, user, req.getSeatIds(), hadOrderInfo,null);
                if (res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
                    //成功后将最新数据放至内存中  需要在数据库执行完后操作 不然由于spring 进行了数据库事物控制 不会马上查询到推荐的活动信息
                    //还没写到 先注释
//                    activityService.setIndexListInfoToRedis();
                }
                return res;
            }
        } catch (Exception e) {
            logger.error("editActivity error {}", e);
            return res;
        }
        return res;
    }


//    @Override
//    public IResponse modifyCultureCloudActivity(ActivityReq req) {
//        GenericResponse res = new GenericResponse();
//
//        try {
//
//                Object userObj = getPrincipal(true);
//                UserEntity user = (UserEntity) userObj;
//                //判断活动是否已经有订票，如果有的话则只能修改活动部分内容， 不可编辑以下字段：标题、活动日期、活动时间、是否收费、在线售票。其余字段可编辑
//                List<CultureCloudActivityOrderEntity> activityOrders =
//                        cultureCloudActivityOrderDao.queryCmsActivityOrderListByActivityId(req.getActivityId());
//                //存在订单的时候 不对座位信息进行修改
//                boolean hadOrderInfo = false;
//                if (activityOrders != null && activityOrders.size() > 0) {
//                    hadOrderInfo = true;
//                }
//
////                String rsStrs = activityService.editActivity(cur, user, seatIds, hadOrderInfo,null);
//
//                CultureCloudActivityEntity activity = cultureCloudActivityDao.get(req.getActivityId());
//                    if (activity.getPublicTime() == null && req.getActivityState() == 6) {
//                        activity.setPublicTime(new Date());
//                    }
//                    Integer activityIsReservation = req.getActivityIsReservation() != null ? req.getActivityIsReservation()
//                            : 1;
//                    if (activityIsReservation == 1) {
//                        activity.setActivitySalesOnline("N");
//                        activity.setActivityIsReservation(1);
//                        activity.setActivitySupplementType(1);
//                    } else if (activityIsReservation == 2) {
//                        activity.setActivityIsReservation(2);
//                        activity.setActivitySalesOnline("Y");
//                    } else if (activityIsReservation == 3) {
//                        activity.setActivityIsReservation(2);
//                        activity.setActivitySalesOnline("N");
//                    }else if (activityIsReservation == 4) {
//                        activity.setActivityIsReservation(1);
//                        activity.setActivitySalesOnline("N");
//                        activity.setActivitySupplementType(2);
//                    }else if (activityIsReservation == 5) {
//                        activity.setActivityIsReservation(1);
//                        activity.setActivitySalesOnline("N");
//                        activity.setActivitySupplementType(3);
//                    }
//                    // //存在订单时该值不能修改
//                    // if (!hadOrder) {
//                    //
//                    // cur.setEventCount(cur.getActivityReservationCount());
//                    // } else {
//                    // cur.setActivityReservationCount(activity.getActivityReservationCount());
//                    //
//                    // }
//
//            //还没有这个类，先注释掉
////                    UserAddress address = new UserAddress();
////                    if (StringUtils.isNotBlank(req.getAddressId())) {
////                        address = userAddressMapper.selectAddressById(req.getAddressId());
////                        if (address.getActivityLon() != null) {
////                            req.setActivityLon(address.getActivityLon());
////                        }
////                        if (address.getActivityLat() != null) {
////                            req.setActivityLat(address.getActivityLat());
////                        }
////                    } else {
////                        if (req.getActivityLon() == null) {
////                            req.setActivityLon((double) 0);
////                        }
////                        if (req.getActivityLat() == null) {
////                            req.setActivityLat((double) 0);
////                        }
////                    }
//                    if (activity != null && StringUtils.isNotBlank(activity.getActivityName())
//                            && StringUtils.isNotBlank(req.getActivityName())
//                            && !activity.getActivityName().trim().equals(req.getActivityName().trim()))
//                        // 验证活动名称
//                        if (StringUtils.isNotBlank(req.getActivityName())) {
//                            boolean exists = queryActivityNameIsExists(req.getActivityName().trim());
//                            if (exists) {
//                                return Constant.RESULT_STR_REPEAT;
//                            }
//                        }
//
//                if (user != null && StringUtils.isNotBlank(user.getUserId())) {
//                    activity.setActivityUpdateUser(user.getUserId());
//                    activity.setActivityUpdateTime(new Date());
//                }
//                if (req.getCreateActivityCode() != null) {
//                    if (req.getCreateActivityCode() == 1) {
//                        req.setActivityArea("45,上海市");
//                    }
//                }
//
//                // 删除活动场馆原有关联关系
//                // 保存活动场馆新的关联关系
//                if (StringUtils.isNotBlank(req.getVenueId()) && req.getCreateActivityCode() == 0) {
//
//                    CmsActivityVenueRelevance relevance = new CmsActivityVenueRelevance();
//                    relevance.setActivityId(req.getActivityId());
//                    relevance.setVenueId(req.getVenueId());
//                    relevanceMapper.addActivityVenueRelevance(relevance);
//                }
//                if (!hadOrderInfo) {
//                    cmsActivityEventService.deleteEventInfoByActivityId(req.getActivityId());
//                    // cmsActivitySeatMapper.deleteByActivityId(cur.getActivityId());
//                    String eventDate[] = req.getEventDate().split(",");
//                    String eventTimes[] = req.getEventTime().split(",");
//                    String eventIds[] = null;
//                    if (StringUtils.isNotBlank(req.getEventIds())) {
//                        eventIds = req.getEventIds().split(",");
//                    }
//                    String spikeTimes[] = null;
//                    if (StringUtils.isNotBlank(req.getSpikeTimes())) {
//                        spikeTimes = req.getSpikeTimes().split(",");
//                    }
//                    String availableCount[] = null;
//                    if (StringUtils.isNotBlank(req.getAvaliableCount())) {
//                        availableCount = req.getAvaliableCount().split(",");
//                    }
//                    String orderPrice[] = req.getOrderPrice().split(",");
//                    String seatId[] = null;
//                    if (StringUtils.isNotBlank(req.getSeatIds())) {
//                        seatId = req.getSeatIds().split(";");
//                    }
//
//                    if (req.getActivityIsReservation() != 1) {
//                        for (int i = 0; i < eventDate.length; i++) {
//                            CultureCloudActivityEventEntity activityEvent = new CultureCloudActivityEventEntity();
//                            activityEvent.setEventId(UUIDUtils.createUUId());
//                            activityEvent.setActivityId(req.getActivityId());
//                            activityEvent.setEventDate(eventDate[i]);
//                            activityEvent.setSingleEvent(req.getSingleEvent());
//                            if (req.getSingleEvent() == 1) {
//                                activityEvent.setEventDateTime(req.getActivityEndTime() + " " + eventTimes[i]);
//                                activityEvent.setEventEndDate(req.getActivityEndTime());
//                            } else {
//                                activityEvent.setEventDateTime(eventDate[i] + " " + eventTimes[i]);
//                                activityEvent.setEventEndDate(eventDate[i]);
//                            }
//                            activityEvent.setEventTime(eventTimes[i]);
//                            if(req.getSpikeType()!=null&&req.getSpikeType()!=0){
//                                activityEvent.setSpikeType(req.getSpikeType());
//                                if (StringUtils.isNotBlank(req.getSpikeTimes())) {
//                                    activityEvent.setSpikeTime(new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(spikeTimes[i]));
//                                }
//                            }
//
//                            if (req.getActivityIsFree() == 2) {
//                                activityEvent.setOrderPrice(orderPrice[i]);
//                            }
//                            if (StringUtils.isNotBlank(availableCount[i])) {
//                                activityEvent.setAvailableCount(Integer.parseInt(availableCount[i]));
//                            } else {
//                                activityEvent.setAvailableCount(Integer.parseInt(req.getAvaliableCount()));
//                            }
//                            if (StringUtils.isBlank(req.getEndTimePoint())) {
//                                req.setEndTimePoint(activityEvent.getEventDateTime());
//                            } else if (activityEvent.getEventDateTime().compareTo(req.getEndTimePoint()) > 1) {
//                                req.setEndTimePoint(activityEvent.getEventDateTime());
//                            }
//                            cultureCloudActivityEventDao.save(activityEvent);
//                            if (req.getSingleEvent() == 0) {
//                                if (StringUtils.isNotBlank(req.getSeatIds())) {
//                                    activityEvent.setSeatIds(seatId[i]);
//                                    if (i < eventIds.length) {
//                                        cmsActivitySeatService.deleteByEventId(eventIds[i]);
//                                    }
//                                    cmsActivitySeatService.addEventSeatInfo(activityEvent, user);
//                                } else if (i < eventIds.length) {
//                                    Map map = new HashMap();
//                                    map.put("activityId", eventIds[i]);
//                                    map.put("eventId", activityEvent.getEventId());
//                                    cmsActivitySeatMapper.editEventSeat(map);
//                                }
//                            } else {
//                                if (i == 0 && StringUtils.isNotBlank(req.getSeatIds())) {
//                                    activityEvent.setSeatIds(seatId[i]);
//                                    cmsActivitySeatService.deleteByEventId(eventIds[0]);
//                                    cmsActivitySeatService.addEventSeatInfo(activityEvent, user);
//                                } else if (i == 0 && i < eventIds.length) {
//                                    Map map = new HashMap();
//                                    map.put("activityId", eventIds[i]);
//                                    map.put("eventId", activityEvent.getEventId());
//                                    cmsActivitySeatMapper.editEventSeat(map);
//                                }
//                            }
//                        }
//                    } else {
//                        CultureCloudActivityEventEntity activityEvent = new CultureCloudActivityEventEntity();
//                        activityEvent.setEventId(UUIDUtils.createUUId());
//                        activityEvent.setActivityId(req.getActivityId());
//                        activityEvent.setEventDate(req.getActivityStartTime());
//                        activityEvent.setEventTime(req.getActivityTime());
//                        activityEvent.setEventEndDate(req.getActivityEndTime());
//                        activityEvent.setEventDateTime(req.getActivityEndTime() + " " + req.getActivityTime());
//                        req.setEndTimePoint(activityEvent.getEventDateTime());
//                        cultureCloudActivityEventDao.save(activityEvent);
//                    }
//                }
//                int count = activityMapper.editCmsActivity(req);
//                if (count <= 0) {
//                    return Constant.RESULT_STR_FAILURE;
//                }
//
//
//                if (Constant.RESULT_STR_SUCCESS.equals(rsStrs)) {
//                    //成功后将最新数据放至内存中  需要在数据库执行完后操作 不然由于spring 进行了数据库事物控制 不会马上查询到推荐的活动信息
//                    activityService.setIndexListInfoToRedis();
//                }
//                return res;
//
//        } catch (Exception e) {
//            logger.error("editActivity error {}", e);
//            return res;
//        }
//
//        return res;
//    }

    /**
     * 查询后台活动列表信息 new
     *
     * @param
     * @return 活动列表信息
     */
    @Override
    @Audit(operate = "获取活动列表")
    public IResponse getCultureCloudActivityList(ActivityReq req) {

        GetCultureCloudActivityListRes res = new GetCultureCloudActivityListRes();
        Page<CultureCloudActivityEntity> page = new Page<CultureCloudActivityEntity>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
//
//        // 场馆类型
//        if (req != null && StringUtils.isNotBlank(req.getVenueType())) {
//            map.put("venueType", req.getVenueType());
//
//        }
//        // 场馆
//        if (req != null && StringUtils.isNotBlank(req.getVenueId())) {
//            map.put("venueId", req.getVenueId());
//        }
//
//        // 场馆主题
//        if (req != null && StringUtils.isNotBlank(req.getActivityType())) {
//            map.put("activityType", "%" + req.getActivityType() + "%");
//
//        } // 场馆主题
//        if (req != null && StringUtils.isNotBlank(req.getActivityTheme())) {
//            map.put("activityTheme", "%" + req.getActivityTheme() + ",%");
//
//        }
        // 权限验证
//        if (sysUser != null && StringUtils.isNotBlank(sysUser.getUserDeptPath())) {
//
//            List<SysShareDept> sysShareDepts = new ArrayList<SysShareDept>();
//            // 判断用户在部门分享表中是否有共享的信息
//            sysShareDepts = sysShareDeptService.queryShareDeptByTargetDeptId(sysUser.getUserDeptId());
//            SysShareDept sysShareDept = new SysShareDept();
//            sysShareDept.setShareDepthPath(sysUser.getUserDeptPath());
//            sysShareDepts.add(sysShareDept);
//            map.put("activityDepts", sysShareDepts);
//        }


//        if (StringUtils.isNotBlank(req.getTemplId())) {
//            map.put("templId", req.getTemplId());
//        }
//        // 活动评级
//        if (req != null && StringUtils.isNotBlank(req.getRatingsInfo())) {
//            map.put("ratingsInfo", req.getRatingsInfo());
//        }
        // 分页
//        if (page != null && page.getFirstResult() != null && page.getRows() != null) {
//            map.put("firstResult", page.getFirstResult());
//            map.put("rows", page.getRows());
//            int total = activityMapper.queryCmsActivityCountByCondition(map);
//            page.setTotal(total);
//        }
        page = cultureCloudActivityDao.queryPage(page, getQueryHql(req));

        res.setTotalResult(page.getTotalCount());
        res.setPageSize(page.getPageSize());
        res.setCurrentPage(page.getCurrentPage());
        res.setTotal(page.getTotalPage());
        page.getResultList().forEach(o -> {
            CultureCloudActivityForm activityForm = new CultureCloudActivityForm();
            activityForm.setActivityId(o.getId());
            activityForm.setActivityName(o.getActivityName());
            activityForm.setActivityIconUrl(o.getActivityIconUrl());
            activityForm.setActivityMemo(o.getActivityMemo());
            activityForm.setActivityLon(o.getActivityLon());
            activityForm.setActivityLat(o.getActivityLat());
            activityForm.setActivityAddress(o.getActivityAddress());
            activityForm.setActivityTime(o.getActivityTime());
            activityForm.setActivityType(o.getActivityType());
            activityForm.setSignStartTime(new SimpleDateFormat("yyyy-MM-dd HH:mm").format(o.getSignStartTime()));
            activityForm.setSignEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm").format(o.getSignEndTime()));
            activityForm.setActivityTime(o.getActivityTime());
            activityForm.setActivityEndTime(o.getActivityEndTime());
//            activityForm.setActivityLiaison(o.getActivityLiaison());
//            activityForm.setActivityWebSite(o.getActivityWebSite());
//            activityForm.setActivityMail(o.getActivityMail());
            activityForm.setActivityTel(o.getActivityTel());
            activityForm.setActivityMobile(o.getActivityMobile());
            activityForm.setActivityIsFree(o.getActivityIsFree());
//            activityForm.setActivityPaymentDesc(o.getActivityPaymentDesc());
//            activityForm.setActivityPrice(o.getActivityPrice());
            activityForm.setActivityState(o.getActivityState());
//            activityForm.setActivityReservationType(o.getActivityReservationType());
//            activityForm.setActivityIsReservation(o.getActivityIsReservation());
//            activityForm.setActivityReservationCount(o.getActivityReservationCount());
            activityForm.setActivityStartTime(o.getActivityStartTime());
            activityForm.setActivityEndTime(o.getActivityEndTime());
            activityForm.setNeedSync(o.getNeedSync());
            activityForm.setHasSynced(o.getHasSynced());
            List<CultureCloudActivityEventEntity> activityEventList = cultureCloudActivityEventDao.getListByHql("select a from " +
                    "CultureCloudActivityEventEntity a where a.state="+EntityContext.RECORD_STATE_VALID+"  and a.activity.id = "+o.getId());
            String endDate = "";
            if (activityEventList != null && activityEventList.size() > 0) {
                endDate = activityEventList.get(activityEventList.size() - 1).getEventTime();
            }
            if(StringUtils.isNotBlank(o.getPublisher())) {
            	activityForm.setPublisher(o.getPublisher());
            }else if(null!=o.getDepartment()) {
            	if(o.getDepartment().getUserList().size()>0) {
                	UserEntity user = o.getDepartment().getUserList().get(0);
                	if(null!=user) {
                		activityForm.setPublisher(user.getName());
                	}else {
                		activityForm.setPublisher("");
                	}
            	}else {
            		activityForm.setPublisher("");
            	}

            }else{
            	activityForm.setPublisher("");
            }
            Integer aviilableCount = 0;
            for(CultureCloudActivityEventEntity event:activityEventList){
                if (null != event.getAvailableCount()){
                    aviilableCount += event.getAvailableCount();
                }
            }
            activityForm.setActivityAbleCount(aviilableCount);

            try {
                if (StringUtils.isNotBlank(o.getActivityEndTime())) {
                    if (new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(o.getActivityEndTime() + " " + endDate.split("-")[0]).before(new Date())) {
                        activityForm.setIsOver(1);
                    } else {
                        activityForm.setIsOver(0);
                    }
                } else {
                    if (new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(o.getActivityStartTime() + " " + endDate.split("-")[0]).before(new Date())) {
                        activityForm.setIsOver(1);
                    } else {
                        activityForm.setIsOver(0);
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }
//            activityForm.setActivityNotice(o.getActivityNotice());
//            activityForm.setActivityContent(o.getActivityContent());
//            activityForm.setActivitySysUrl(o.getActivitySysUrl());
            activityForm.setActivitySalesOnline(o.getActivitySalesOnline());
            activityForm.setActivityIsReservation(o.getActivityIsReservation());
            activityForm.setActivitySalesOnline(o.getActivitySalesOnline());
//            activityForm.setActivityCreateCode(o.getCreateActivityCode());
//            activityForm.setActivityEventCode(o.getActivityEventCode());
//            activityForm.setActivityRecommendTime(new Timestamp(System.currentTimeMillis()));
//            activityForm.setActivityHost(o.getActivityHost());
//            activityForm.setActivityOrganizer(o.getActivityOrganizer());
//            activityForm.setActivityCoorganizerl(o.getActivityCoorganizerl());
//            activityForm.setActivityPerformed(o.getActivityPerformed());
//            activityForm.setActivityPrompt(o.getActivityPrompt());
//            activityForm.setActivitySpeaker(o.getActivitySpeaker());
//            activityForm.setActivityAttachment(o.getActivityAttachment());
//            activityForm.setActivityPersonal(o.getActivityPersonal());
//            activityForm.setActivityTicketSettings(o.getActivityTicketSettings());
//            activityForm.setActivityTicketNumber(o.getActivityTicketNumber());
//            activityForm.setActivityTicketCount(o.getActivityTicketCount());
//            activityForm.setActivityPublicTime(o.getActivityPublicTime());
//            activityForm.setActivityIdentityCard(o.getActivityIdentityCard());
//            activityForm.setActivityIsRecommend(o.getActivityIsRecommend());
//            activityForm.setActivitySmsType(o.getActivitySmsType());
//            activityForm.setActivityDeptLabel(o.getActivityDeptLabel());
//            activityForm.setActivityPriceNotice(o.getActivityPriceNotice());
//            activityForm.setActivitySort(o.getActivitySort());
//            activityForm.setActivityIsTop(o.getActivityIsTop());
//            activityForm.setActivityTopTime(o.getActivityTopTime());
//            activityForm.setVenueList(o.getVenueList());
            o.getVenueList().forEach(p->{
                CultureCloudVenueForm venue = new CultureCloudVenueForm();
                venue.setVenueId(p.getId());
                venue.setVenueName(p.getVenueName());
                activityForm.getVenueList().add(venue);
            });
            res.getActivityList().add(activityForm);

        });
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

        return res;

    }
    
    public String getQueryHql(ActivityReq req) {
        Object userObj = getPrincipal(true);
        String depIds = getDepartment(userObj);
        StringBuilder hql = new StringBuilder("select distinct a from CultureCloudActivityEntity a join a.venueList v join a.tagList t");

        hql.append(" where a.state="+ EntityContext.RECORD_STATE_VALID)
               .append(" and a.activityState in ("+(StringUtils.isNotEmpty(req.getActivityStateList())?req.getActivityStateList():"0")+")")
                .append(null != req.getActivityId() ? " and a.id=" + req.getActivityId() : "")
                .append(StringUtils.isNotBlank(req.getActivityStartTime()) ? " and a.activityStartTime >= '" + req.getActivityStartTime() + "'" : "")
                .append(StringUtils.isNotBlank(req.getActivityEndTime()) ? " and a.activityEndTime <= '" + req.getActivityEndTime() + "'" : "")
                .append(null != req.getActivityIsDel() ? " and a.activityIsDel =" + req.getActivityIsDel() : "")
                .append(StringUtils.isNotEmpty(req.getTagIdList())? " and t.id in ("+req.getTagIdList()+")":"")
                .append(StringUtils.isNotBlank(req.getActivityRecommend()) ? "and a.activityRecommend ='" + req.getActivityRecommend() + "'" : "")
                .append(StringUtils.isNotBlank(req.getActivityArea()) ? " and a.activityArea like '%" + req.getActivityArea() + "%'" : "")
                .append(StringUtils.isNotBlank(req.getActivityName()) ? " and a.activityName like '%" + req.getActivityName() + "%'" : "")
                .append(StringUtils.isNotBlank(req.getAvailableCount()) ? "and a.availableCount ='" + req.getAvailableCount() + "'" : "")
                .append(null != req.getActivityIsDetails() ? " and a.activityIsDetails =" + req.getActivityIsDetails() : "")
                .append(null != req.getActivityIsFree() ? " and a.activityIsFree =" + req.getActivityIsFree() : "")
                .append(null != req.getVenueId()? " and v.id = " + req.getVenueId() : "")
                .append(StringUtils.isNotBlank(req.getVenueName())? " and v.venueName like '%" + req.getVenueName() + "%'" : "")
                .append(StringUtils.isNotBlank(req.getVenueArea())? " and v.venueArea like '%" + req.getVenueArea() + "%'" : "")
                .append(StringUtils.isNotBlank(req.getVenueType())? " and v.venueType like '%" + req.getVenueType() + "%'" : "")
                .append(null != req.getNeedSync()? " and a.needSync = " + req.getNeedSync() : "")
        		.append(StringUtils.isNoneEmpty(depIds) ? " and a.department.id in("+depIds+")":"");

        // 现在选坐
        if (req != null && StringUtils.isNotBlank(req.getActivitySalesOnline())) {
            if ("Z".equals(req.getActivitySalesOnline())) {
                hql.append(" and a.activityIsReservation = 1 and a.activitySupplementType = 1");
            } else if ("X".equals(req.getActivitySalesOnline())) {
                hql.append(" and a.activityIsReservation = 1 and a.activitySupplementType = 2");
            } else if ("W".equals(req.getActivitySalesOnline())) {
                hql.append(" and a.activityIsReservation = 1 and a.activitySupplementType = 3");
            } else {
                hql.append(" and a.activitySalesOnline = '" + req.getActivitySalesOnline() + "'");
            }
        }
        // 自由入坐
        if (req != null && "N".equals(req.getActivitySalesOnline())) {
            hql.append(" and a.activityIsReservation = 2");
        }

        //智能排序
        if(null != req.getSortType() && req.getSortType() == 1){
            hql.append(" order by a.activityEndTime desc");
        }
        //热门排序
        else if (null != req.getSortType() && req.getSortType() == 2){
            hql.append(" order by a.activityEndTime desc");
        }
        //最新上线
        else if (null != req.getSortType() && req.getSortType() == 3){
            hql.append(" order by a.activityUpdateTime desc");

        }
        //即将结束
        else if (null != req.getSortType() && req.getSortType() == 4){
            hql.append(" order by a.activityEndTime desc");
        }else{
        hql.append(" order by a.activityCreateTime desc,a.activitySort desc");
        }
        return hql.toString();
    }

	public IResponse exportCultureCloudActivityList(ActivityReq req, HttpServletResponse response) {
		GenericResponse res = new GenericResponse();
		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("activityName", "活动名称");
		heardMap.put("activityStartTime", "开始时间");
		heardMap.put("activityTime", "活动时间");
		heardMap.put("activityState", "活动状态");
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();

		List<CultureCloudActivityEntity> list = cultureCloudActivityDao.getListByHql(getQueryHql(req),"");
		list.forEach(o -> {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("activityName", o.getActivityName());
			map.put("activityStartTime", o.getActivityStartTime());
			map.put("activityTime",o.getActivityTime());
			
			switch(o.getActivityState()) {
			case 1:
				map.put("activityState","草稿");
				break;
			case 2:
				map.put("activityState","已审核");
				break;
			case 3:
				map.put("activityState","个人发布活动待审核");
				break;
			case 5:
				map.put("activityState","回收站");
				break;
			case 6:
				map.put("activityState","已发布");
				break;
			case 7:
				map.put("activityState","个人审核未通过");
				break;
			case 8:
				map.put("activityState","Y码活动");
				break;
			default:
				map.put("activityState","");
				break;	
			}
			
			dataList.add(map);
		});
		
		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

		return res;
	}
    @Override
    @Audit(operate = "获取活动信息")
    public IResponse getCultureCloudActivityInfo(ActivityReq req){
        GetCultureCloudActivityInfoRes res = new GetCultureCloudActivityInfoRes();
        CultureCloudActivityDetailForm activityForm = new CultureCloudActivityDetailForm();

        Object userObj = getPrincipal(true);
//        if (null != userObj) {
        if (null != req.getActivityId()) {
            CultureCloudActivityEntity activity = cultureCloudActivityDao.get(req.getActivityId());
            if (null != activity) {
//                    if (userObj instanceof UserEntity || userObj instanceof HealthUserEntity || ((AccountEntity) userObj).getPhone().equals(reservation.getPhone()) || (userObj instanceof AccountEntity && Objects.equals(reservation.getHealthMember().getId(),
//                            ((AccountEntity) userObj).getId()))) {
                activityForm.setActivityId(activity.getId());
                activityForm.setActivityName(activity.getActivityName());
                activityForm.setActivityIconUrl(activity.getActivityIconUrl());
                activityForm.setActivityMemo(activity.getActivityMemo());
                activityForm.setActivityLon(activity.getActivityLon());
                activityForm.setActivityLat(activity.getActivityLat());
                activityForm.setActivityAddress(activity.getActivityAddress());
                activityForm.setActivityTime(activity.getActivityTime());
                activityForm.setActivityType(activity.getActivityType());
                activityForm.setActivityLiaison(activity.getActivityLiaison());
                activityForm.setActivityWebSite(activity.getActivityWebSite());
                activityForm.setActivityMail(activity.getActivityMail());
                activityForm.setActivityTel(activity.getActivityTel());
                activityForm.setActivityMobile(activity.getActivityMobile());
                activityForm.setActivityIsFree(activity.getActivityIsFree());
                activityForm.setActivityPaymentDesc(activity.getActivityPaymentDesc());
                activityForm.setActivityPrice(activity.getActivityPrice());
                activityForm.setActivityState(activity.getActivityState());
                activityForm.setActivityReservationType(activity.getActivityReservationType());
                activityForm.setActivitySupplementType(activity.getActivitySupplementType());
                activityForm.setActivityIsReservation(activity.getActivityIsReservation());
                activityForm.setActivityReservationCount(activity.getActivityReservationCount());
                activityForm.setActivityStartTime(activity.getActivityStartTime());
                activityForm.setActivityEndTime(activity.getActivityEndTime());
                activityForm.setActivityNotice(activity.getActivityNotice());
                activityForm.setActivityContent(activity.getActivityContent());
                activityForm.setActivitySysUrl(activity.getActivitySysUrl());
                activityForm.setActivitySalesOnline(activity.getActivitySalesOnline());
                activityForm.setActivityCreateCode(activity.getCreateActivityCode());
                activityForm.setActivityEventCode(activity.getActivityEventCode());
                activityForm.setActivityRecommendTime(new Timestamp(System.currentTimeMillis()));
                activityForm.setActivityHost(activity.getActivityHost());
                activityForm.setActivityOrganizer(activity.getActivityOrganizer());
                activityForm.setActivityCoorganizer(activity.getActivityCoorganizer());
                activityForm.setActivityPerformed(activity.getActivityPerformed());
                activityForm.setActivityPrompt(activity.getActivityPrompt());
                activityForm.setActivitySpeaker(activity.getActivitySpeaker());
                activityForm.setActivityAttachment(activity.getActivityAttachment());
                activityForm.setActivityPersonal(activity.getActivityPersonal());
                activityForm.setActivityTicketSettings(activity.getActivityTicketSettings());
                activityForm.setActivityTicketNumber(activity.getActivityTicketNumber());
                activityForm.setActivityTicketCount(activity.getActivityTicketCount());
                activityForm.setActivityPublicTime(activity.getActivityPublicTime());
                activityForm.setActivityIdentityCard(activity.getActivityIdentityCard());
                activityForm.setActivityIsRecommend(activity.getActivityIsRecommend());
                activityForm.setActivitySmsType(activity.getActivitySmsType());
                activityForm.setActivityDeptLabel(activity.getActivityDeptLabel());
                activityForm.setActivityPriceNotice(activity.getActivityPriceNotice());
                activityForm.setActivitySort(activity.getActivitySort());
                activityForm.setActivityIsTop(activity.getActivityIsTop());
                activityForm.setActivityTopTime(activity.getActivityTopTime());
                activityForm.setFixedTel(activity.getFixedTel());
                activityForm.setResourceImgUrl(activity.getResourceImgUrl());
                activityForm.setResourceVideoUrl(activity.getResourceVideoUrl());
                activityForm.setActivityProfile(activity.getActivityProfile());
                activityForm.setActivitySite(activity.getActivitySite());
                activityForm.setActivityTimeDes(activity.getActivityTimeDes());
                activityForm.setMaxAge(activity.getMaxAge());
                activityForm.setMinAge(activity.getMinAge());
                activityForm.setActivityArea(activity.getActivityArea());
                activityForm.setActivityCity(activity.getActivityCity());
                activityForm.setActivityProvince(activity.getActivityProvince());
                activityForm.setActivityTown(activity.getActivityTown());
                activityForm.setActivityVillage(activity.getActivityVillage());
                activityForm.setSignStartTime(null != activity.getSignStartTime()? activity.getSignStartTime().toString().replace(".0",""):null);
                activityForm.setSignEndTime(null != activity.getSignEndTime()? activity.getSignEndTime().toString().replace(".0",""):null);
                activityForm.setAvailableCount(activity.getAvailableCount());
                activityForm.setEventTime(activity.getEventTime());
                activityForm.setSingleEvent(activity.getSingleEvent());
                activityForm.setActivityPublishFrom(activity.getActivityPublishFrom());
                activityForm.setCustomizeMethod(activity.getCustomizeMethod());

                activityForm.setCancelTime(activity.getCancelTime());
                activityForm.setCancelEndTime(activity.getCancelEndTime());
                activityForm.setWrittenOffTime(activity.getWrittenOffTime());
                activityForm.setBeforeStartMinute(activity.getBeforeStartMinute());
                activityForm.setAfterStartMinute(activity.getAfterStartMinute());
                activityForm.setDetailTimeLimitMinutes(activity.getDetailTimeLimitMinutes());
                activityForm.setTimeLimitOfPickingUpTickets(activity.getTimeLimitOfPickingUpTickets());
//                activityForm.setActivitySmsInfo(activity.getActivitySmsInfo());
                activityForm.setActivitySmsType(activity.getActivitySmsType());
                activityForm.setNeedSync(activity.getNeedSync());
                activityForm.setHasSynced(activity.getHasSynced());


        		Map<Integer, List<CultureCloudTagEntity>> collect1 = (Map<Integer, List<CultureCloudTagEntity>>) activity.getTagList()
        				.parallelStream().collect(groupingBy(CultureCloudTagEntity::getCategory));
        		for(Integer key : collect1.keySet()) {
        			StringBuilder tags = new StringBuilder();
        			if(key == 0) {
        				for(CultureCloudTagEntity t : collect1.get(key)) {
        					CultureCloudTagForm tag = new CultureCloudTagForm();
        					tag.setCultureCloudTagId(t.getId());
        					tag.setCategory(t.getCategory());
        					tag.setDisplayType(t.getDisplayType());
        					tag.setTagName(t.getTagName());
        					activityForm.getTagList().add(tag);
        				}
        				
        			}else if(key ==1) {
        				for(CultureCloudTagEntity t : collect1.get(key)) {
        					tags.append(t.getTagName()+",");
        				}
        				activityForm.setKeywords(tags.substring(0, tags.length()-1).toString());
        			}else if(key==2){
        				for(CultureCloudTagEntity t : collect1.get(key)) {
        					CultureCloudTypeForm cultureCloudTypeForm = new CultureCloudTypeForm();
        					cultureCloudTypeForm.setCultureCloudTypeId(t.getId());
        		            cultureCloudTypeForm.setCategory(t.getCategory());
        		            cultureCloudTypeForm.setTypeName(t.getTagName());
        		            activityForm.getTypeList().add(cultureCloudTypeForm);
        				}
        			}else if(key==3){
        				for(CultureCloudTagEntity t : collect1.get(key)) {
        					CultureCloudTypeForm cultureCloudTypeForm = new CultureCloudTypeForm();
        					cultureCloudTypeForm.setCultureCloudTypeId(t.getId());
        		            cultureCloudTypeForm.setCategory(t.getCategory());
        		            cultureCloudTypeForm.setTypeName(t.getTagName());
        		            activityForm.getArtTypeList().add(cultureCloudTypeForm);
        				}
        			}
        		}
        		if(null != activity.getDepartment()) {
        			CultureCloudDepartmentForm departmentForm = new CultureCloudDepartmentForm();
        			departmentForm.setId(activity.getDepartment().getId());
        			departmentForm.setDepartmentName(activity.getDepartment().getDepartmentName());
        			activityForm.setDepartment(departmentForm);
        		}


                activity.getVenueList().forEach(o->{
                    CultureCloudVenueForm venue = new CultureCloudVenueForm();
                    venue.setVenueId(o.getId());
                    venue.setVenueName(o.getVenueName());
                    activityForm.getVenueList().add(venue);
                });

                //源代码部分

//                String userId=request.getUserId();
                String activityId=req.getActivityId().toString();

                CultureCloudActivityDetailForm vo=activityForm;

                SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");

                if (null != activity) {
                    //还没有先注释
//                    vo=new CmsActivityDetailVO(activity);

//                    List<CmsTagSubDto> cmsTagSubList=cmsTagSubMapper.queryRelateTagSubList(activity.getActivityId());
//
//                    List<CmsTagSubVO> subList= new ArrayList<CmsTagSubVO>();
//
//                    for (CmsTagSubDto cmsTagSubDto : cmsTagSubList) {
//
//                        subList.add(new CmsTagSubVO(cmsTagSubDto));
//                    }
//
//                    vo.setSubList(subList);

                    String activityIconUrl = "";
                    if (StringUtils.isNotBlank(activity.getActivityIconUrl())) {

//                        if(activity.getActivityIconUrl().indexOf("http")>-1)
//
//                            activityIconUrl=activity.getActivityIconUrl();
//                        else
                            activityIconUrl = activity.getActivityIconUrl();
                    }
                    vo.setActivityIconUrl(activityIconUrl);

//                    vo.setActivityFunName(activity.getFunName() != null ? activity.getFunName() : "");

                    vo.setActivityName(activity.getActivityName() != null ? activity.getActivityName() : "");
                    vo.setActivitySite(activity.getActivitySite() != null ? activity.getActivitySite() : "");
                    //   if (StringUtils.isNotBlank(activity.getVenueName())) {
                    //     vo.setActivityAddress((StringUtils.isNotBlank(activity.getActivityAddress()) ? activity.getActivityAddress() + "." : "") + activity.getVenueName());
                    //  } else {
                    vo.setActivityAddress(StringUtils.isNotBlank(activity.getActivityAddress()) ?
                            activity.getActivityAddress() : "");
                    //  }
                    vo.setActivityStartTime(activity.getActivityStartTime() != null ? activity.getActivityStartTime() : "");
                    vo.setActivityEndTime(StringUtils.isNotBlank(activity.getActivityEndTime()) ?
                            activity.getActivityEndTime() : vo.getActivityStartTime());
                    vo.setActivityTel(activity.getActivityTel() != null ? activity.getActivityTel() : "");

                    //子系统对接修改
//                    vo.setActivityDateNums(activity.getDateNums());

                    vo.setActivityTimeDes(activity.getActivityTimeDes() != null ? activity.getActivityTimeDes() : "");
                    vo.setActivityIsFree(activity.getActivityIsFree() != null ? activity.getActivityIsFree() : 1);
                    vo.setActivityPrice(activity.getActivityPrice() != null ? activity.getActivityPrice() : null);
//                    vo.setPriceDescribe( activity.getPriceDescribe() != null ? activity.getPriceDescribe() : "");

                    Integer aviilableCount = 0;
                    for(CultureCloudActivityEventEntity event:cultureCloudActivityEventDao.getListByHql("select a from " +
                            "CultureCloudActivityEventEntity a where a.state="+EntityContext.RECORD_STATE_VALID+" and a.activity.id = "+activity.getId())){
                        if (null != event.getAvailableCount()){
                            aviilableCount += event.getAvailableCount();
                        }
                    }
                    vo.setActivityAbleCount(aviilableCount);

                    vo.setActivityTime(activity.getActivityTime() != null ? activity.getActivityTime() : "");
                    //获取活动经纬度
//                    double activityLon = 0d;
//                    if (activity.getActivityLon() != null) {
//                        activityLon = activity.getActivityLon();
//                    }
//                    double activityLat = 0d;
//                    if (activity.getActivityLat() != null) {
//                        activityLat = activity.getActivityLat();
//                    }
//                    vo.setActivityLon(activityLon);
//                    vo.setActivityLat(activityLat);
                    vo.setActivityIsReservation(activity.getActivityIsReservation());
//                    vo.setActivitySupplementType(activity.getActivitySupplementType()!=null?activity.getActivitySupplementType():1);
                    vo.setCancelEndTime(activity.getCancelEndTime());
                    vo.setActivitySalesOnline(activity.getActivitySalesOnline() != null ? activity.getActivitySalesOnline() : "");
//                    vo.setActivityId(activity.getActivityId() != null ? activity.getActivityId() : "");
                    vo.setActivityMemo(activity.getActivityMemo() != null ? activity.getActivityMemo() : "");
                    vo.setActivityHost( activity.getActivityHost() != null ? activity.getActivityHost() : "");
                    vo.setActivityOrganizer(activity.getActivityOrganizer() != null ? activity.getActivityOrganizer() : "");
                    vo.setActivityCoorganizer( activity.getActivityCoorganizer() != null ? activity.getActivityCoorganizer() : "");
                    vo.setActivityPerformed( activity.getActivityPerformed() != null ? activity.getActivityPerformed() : "");
                    vo.setActivitySpeaker( activity.getActivitySpeaker() != null ? activity.getActivitySpeaker() : "");
                    vo.setActivityPrompt( activity.getActivityPrompt() != null ? activity.getActivityPrompt() : "");
//                    vo.setSignStartTime(activity.getSignStartTime());
//                    vo.setSignEndTime(activity.getSignEndTime());
                    // 无收费（活动简介前加直接前往、无需预约、无需付费，有任何问题欢迎拨打电话咨询（电话号码？）），有收费（需要事先预定，请点击“活动预定”按钮进行票务预订。有任何问题欢迎拨打电话咨询（电话号码？））
//                    if (activity.getActivityIsFree() == 1) {
//                        if (activity.getActivityIsReservation() == 1) {
//                            vo.setActivityTips("<font color='#FA8808' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'> 温馨提示：</font><font color='#262626' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'>本活动具体参与方式请以主办方公布为准，您可拨打电话" +
//                                    "(" + vo.getActivityTel() + ")进行咨询</font>");
//                        } else if (activity.getActivityIsReservation() == 2) {
//                            vo.setActivityTips("<font color='#FA8808' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'> 温馨提示：</font>" +
//                                    "<font color='#262626' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'>需要事先预定，" +
//                                    "请点击屏幕右下角的“</font><font color='#FA8808' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'>" +
//                                    "立即预约</font><font color='#262626' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'>”按钮进行票务预订。有任何问题欢迎拨打电话" +
//                                    "咨询(" + vo.getActivityTel() + ")</font>");
//                        }
//                    } else if (activity.getActivityIsFree() == 2) {
//                        if (activity.getActivityIsReservation() == 1) {
//                            vo.setActivityTips("<font color='#FA8808' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'>温馨提示：</font><font color='#262626' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'>本活动需要收费，具体参与方式请以主办方公布为准，您可拨打电话" +
//                                    "(" + vo.getActivityTel() + ")进行咨询</font>");
//                        } else if (activity.getActivityIsReservation() == 2) {
//                            vo.setActivityTips("<font color='#FA8808' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'>温馨提示：</font><font color='#262626' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'>需要事先预定，需要付费，" +
//                                    "请点击屏幕右下角的“</font><font color='#FA8808' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'>立即预约</font><font color='#262626' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'>”按钮进行票务预订。" +
//                                    "有任何问题欢迎拨打电话咨询(" + vo.getActivityTel() + ")</font>");
//                        }
//                    } else if (activity.getActivityIsFree() == 3){
//
//                        vo.setActivityTips("<font color='#FA8808' style='font-family:\"STYuanti-SC-Light\";font-size:\"26px\"'>温馨提示：</font>"
//                                + "1.您需要在下单成功后15分钟内付款，逾期订单即被取消。<br>2.活动名额有限，付款视为占用名额，"
//                                + "不接受任何形式的取消订单。付款后未实际到场参与，将视为放弃订单权利，不予退款。请您谨慎下单。"
//                                + "<br>3.如您对订单有疑问，可咨询客服电话(" + vo.getActivityTel() + ")，工作时间周一至周五，9:30-17:00</font>");
//                    }

                    //获取活动视频信息
//	            List<CmsVideo> videoList = cmsVideoMapper.queryVideoById(map);
//	            if (CollectionUtils.isNotEmpty(videoList)) {
//	                for (CmsVideo videos : videoList) {
//	                    Map<String, Object> mapVideo = new HashMap<String, Object>();
//	                    mapVideo.put("videoTitle", videos.getVideoTitle() != null ? videos.getVideoTitle() : "");
//	                    mapVideo.put("videoLink", videos.getVideoLink() != null ? videos.getVideoLink() : "");
//	                    String videoImgUrl = "";
//	                    if (StringUtils.isNotBlank(videos.getVideoImgUrl())) {
//	                        videoImgUrl = staticServer.getStaticServerUrl() + videos.getVideoImgUrl();
//	                    }
//	                    mapVideo.put("videoImgUrl", videoImgUrl);
//	                    mapVideo.put("videoCreateTime", DateUtils.formatDate(videos.getVideoCreateTime()));
//	                    listVideo.add(mapVideo);
//	                }
//	            }
                    List<CultureCloudActivityEventEntity> activityEventList = cultureCloudActivityEventDao.getListByHql("select a from " +
                            "CultureCloudActivityEventEntity a where a.activity.id = "+activity.getId()+" and a.state =1");


                    if (CollectionUtils.size(activityEventList)>0) {
                        StringBuffer eventIds = new StringBuffer(); // 封装活动场次id
                        StringBuffer statusSb = new StringBuffer(); //封装时间段是否有效
                        StringBuffer eventimes = new StringBuffer(); //封装活动具体时间
                        StringBuffer timeQuantums = new StringBuffer(); //封装活动时间段
                        StringBuffer eventCounts = new StringBuffer();//封装每个活动时间段场次票数
                        StringBuffer eventPrices = new StringBuffer();//封装每个活动时间段场次票价
                        StringBuffer spikeDifferences = new StringBuffer();//封装每个活动秒杀倒计时（时间戳）（如果非秒杀活动为0）
                        for (CultureCloudActivityEventEntity events : activityEventList) {

                            Date date = new Date();
                            String times = events.getEventDateTime().substring(0, events.getEventDateTime().lastIndexOf("-"));
                            String nowDate2 = sdf2.format(date);
                            int statusDate = TimeCompareUtil.timeCompare1(times, nowDate2);

                            String eventId = events.getId() != null ? events.getId().toString() : "";
                            String timeQuantum = events.getEventTime() != null ? events.getEventTime() : "";
                            String eventDateTimes = events.getEventDateTime() != null ? events.getEventDateTime() : "";
                            String eventPrice = events.getOrderPrice() != null ? events.getOrderPrice() : "0";
                            int eventCount = null != events.getCounts() && events.getCounts() > 0 ? events.getCounts() : 0;
                            long spikeDifference = 0;
                            if (events.getSpikeTime() != null) {
                                spikeDifference = (events.getSpikeTime().getTime() - new Date().getTime()) / 1000;
                            }
                            //返回 0 表示时间日期相同
                            //返回 1 表示日期1>日期2
                            //返回 -1 表示日期1<日期2
                            if (statusDate == -1 || (null != events.getCounts() && events.getCounts() == 0)) {
                                statusSb.append("0" + ",");
                            } else {
                                statusSb.append("1" + ",");
                            }
                            eventIds.append(eventId + ",");
                            timeQuantums.append(timeQuantum + ",");
                            eventimes.append(eventDateTimes + ",");
                            eventCounts.append(eventCount + ",");
                            eventPrices.append(eventPrice + ",");
                            spikeDifferences.append((spikeDifference >= 0 ? spikeDifference : 0) + ",");
                        }
                        //预定活动时使用id
                        vo.setActivityEventIds(eventIds.toString());
                        //场次开始日期加时间段
                        vo.setActivityEventimes(eventimes.toString());
                        vo.setStatus( statusSb.toString());
                        vo.setTimeQuantum( removerepeatedchar(timeQuantums.toString()));
                        vo.setEventCounts( eventCounts.toString());
                        vo.setEventPrices( eventPrices.toString());
                        vo.setSpikeDifferences( spikeDifferences.toString());

                        //2020-04-25修改 未过期活动改为活动结束时间为准
                        String lastEventTime = vo.getActivityEndTime() + " " + activityEventList.get(activityEventList.size() - 1).getEventTime().split("-")[1];
                        try {
                            vo.setActivityIsPast((sdf2.parse(lastEventTime).getTime() - new Date().getTime()) > 0 ? 0 : 1);
                        } catch (ParseException e) {

                            logger.error(e.getMessage()+ " activityIsPast判断出错");
                        }
                    } else {
                        String lastEventTime = vo.getActivityEndTime()  + " 00:00";
                        try {
                            vo.setActivityIsPast((sdf2.parse(lastEventTime).getTime() - new Date().getTime()) > 0 ? 0 : 1);
                        } catch (ParseException e) {
                            logger.error(e.getMessage()+ " activityIsPast判断出错");
                        }
                    }

                    //添加分享的shareUrl
//                    StringBuffer sb = new StringBuffer();
//                    sb.append(shareUrl);
//                    sb.append("wechatActivity/preActivityDetail.do?");
//                    sb.append("activityId=" + activity.getActivityId());
//                    vo.setShareUrl(sb.toString());

                    //该用户是否已报名该活动 0.该用户未参加 1.参加
//                    vo.setActivityIsWant(activity.getActivityIsWant() > 0 ? activity.getActivityIsWant() : 0);
                    vo.setActivityNotice(activity.getActivityNotice() != null ? activity.getActivityNotice() : "");

//                    vo.setTicketSettings(StringUtils.isNotBlank(activity.getTicketSettings()) ? activity.getTicketSettings() : "Y");

//                    vo.setBrowseCount(activity.getBrowseCount());
//                    vo.setCollectNum(activity.getCollectionCount());
//                    vo.setLikeCount(activity.getLikeCount());
//                    vo.setShareCount(activity.getShareCount());
//                    vo.setIdentityCard(activity.getIdentityCard());

                    //积分判断
//                    if (StringUtils.isNotBlank(userId)) {
//                        Integer integralNow = request.getIntegralNow();
//                        if (integralNow != null) {
//                            if (activity.getLowestCredit() != null) {
//                                if (activity.getLowestCredit() > integralNow) {
//                                    vo.setIntegralStatus("1");
//                                } else {
//                                    if (activity.getCostCredit() != null) {
//                                        if (activity.getCostCredit() > integralNow) {
//                                            vo.setIntegralStatus("2");
//                                        } else {    //消耗积分未填
//                                            vo.setIntegralStatus("0");
//                                        }
//                                    } else {
//                                        vo.setIntegralStatus("0");
//                                    }
//                                }
//                            } else {    //最低积分未填
//                                if (activity.getCostCredit() != null) {
//                                    if (activity.getCostCredit() > integralNow) {
//                                        vo.setIntegralStatus("2");
//                                    } else {
//                                        vo.setIntegralStatus("0");
//                                    }
//                                } else {
//                                    vo.setIntegralStatus("0");
//                                }
//                            }
//                        } else {
//                            if (vo.getLowestCredit() != null) {
//                                vo.setIntegralStatus("1");
//                            } else {
//                                if (vo.getCostCredit() !=null) {
//                                    vo.setIntegralStatus("2");
//                                } else {
//                                    vo.setIntegralStatus("0");
//                                }
//                            }
//                        }
//                    }

/*	            //社团
	            if(activity.getAssnId() != null){
	            	if(activity.getAssnId().equals("4498a594334544058d3985312f342c23")){
	            		vo.setAssnSub(new String[]{"亲子","演出","公司"});
	            	}else if(activity.getAssnId().equals("6e00d525b1b54120926cb2bbf576f9be")){
	            		vo.setAssnSub( new String[]{"歌手","爵士","老歌"});
	            	}else if(activity.getAssnId().equals("ed842f496651403488dee164da78ba17")){
	            		vo.setAssnSub( new String[]{"公益","音乐","美术"});
	            	}else if(activity.getAssnId().equals("816cf35a86554333873f1159f864d73b")){
	            		vo.setAssnSub(new String[]{"相声","民营"});
	            	}else if(activity.getAssnId().equals("0ead672fac2f4e65b0ec12cad960ad98")){
	            		vo.setAssnSub( new String[]{"昆曲","闺门旦","上戏"});
	            	}else if(activity.getAssnId().equals("1883b371dbd4403aba8e3aac9166d19c")){
	            		vo.setAssnSub( new String[]{"相声","快板"});
	            	}else if(activity.getAssnId().equals("ac7e0d5e691a44c2bb5808c8d29ff639")){
	            		vo.setAssnSub( new String[]{"话剧","白领","浦东"});
	            	}else if(activity.getAssnId().equals("97f9b201374a4078abfbf55ca8316d45")){
	            		vo.setAssnSub( new String[]{"人声","乐团"});
	            	}else if(activity.getAssnId().equals("632a0af23b3a4cf1ad77f9a04db65a4f")){
	            		vo.setAssnSub( new String[]{"古琴","非遗","市民文化协会"});
	            	}
	            }else{
	            	vo.setAssnId("");
	            }*/

                    //返回服务器时间
                    vo.setServerTime(new Date());

                    vo.setMinAge(activity.getMinAge());
                    vo.setMaxAge(activity.getMaxAge());
//                    vo.setGenderRestriction(activity.getGenderRestriction());
                    vo.setActivityProfile(activity.getActivityProfile() != null ? activity.getActivityProfile() : "");//活动简介



                    //活动附件
//                    RestTemplateModel restTemplateModel = new RestTemplateModel();
//                    RestTemplate restTemplate = restTemplateModel.restTemplate();
//                    ResponseEntity<Result> response = restTemplate.getForEntity(interfaceGateway + "why/back/attachResource/getAttachResourceList?activityId="+ activity.getActivityId(), Result.class);
//                    Result result = response.getBody();
//                    if(result.resultIsOK()){
//                        vo.setAttachmentList((List<CmsResourceAttachment>) result.getData());
//                    }
                }



                res.setActivity(vo);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//                    } else {
//                        res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
//                        res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
//                    }
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
//        } else {
//            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
//            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
//        }
        return res;
    }


    /**
     * 发布活动/审核活动/拒绝通过活动
     *
     * @param req
     * @return
     */
    @Override
    @Audit(operate = "发布活动")
    public IResponse publishActivity(ActivityReq req) {
        GenericResponse res = new GenericResponse();

        try {
            Object userObj = getPrincipal(true);
            if (userObj instanceof UserEntity) {
            UserEntity user = (UserEntity) userObj;

            List<CultureCloudActivityEventEntity> eventList = cultureCloudActivityEventDao.getListByHql("select a from " +
                    "CultureCloudActivityEventEntity a where a.activity.id = "+req.getActivityId());

            if (eventList.size() == 0) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo("活动无活动项");
                return res;
            }

                if (null != req.getActivityId()) {
                    CultureCloudActivityEntity activity = cultureCloudActivityDao.get(req.getActivityId());

                    //个人发布活动需要设置部门路径 先注释
//                    if (StringUtils.isBlank(activity.getActivityDept())) {
//                        activity.setActivityDept(user.getUserDeptPath());
//                    }
                    activity.setActivityIsDel(1);
                    activity.setActivityState(req.getActivityState() == null ? 6 : req.getActivityState());
                    if (activity.getPublicTime() == null) {
                        activity.setPublicTime(new Timestamp(System.currentTimeMillis()));
                    }
                    activity.setActivityUpdateTime(new Timestamp(System.currentTimeMillis()));
                    activity.setActivityUpdateUser(user.getId().toString());
                    cultureCloudActivityDao.update(activity);
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                }else {
                    res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                    res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            this.logger.error("publishActivity error {}", ex);
        }
        return res;
    }

    @Override
    @Audit(operate = "修改活动排序")
    public IResponse modifyCultureCloudActivitySort(ActivityReq req) {
        GenericResponse res = new GenericResponse();
        try {
            if(null == req.getActivitySort()){
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
                return res;
            }

            CultureCloudActivityEntity activity = cultureCloudActivityDao.get(req.getActivityId());
            if (null != activity) {
                activity.setActivitySort(req.getActivitySort());
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            }
        }catch (Exception e){
            logger.error(e.toString());
        }

        return res;
    }

//    @Override
//    @Audit(operate = "删除活动")
//    public IResponse deleteActivity(ActivityReq req) {
//        GenericResponse res = new GenericResponse();
//        Object userObj = getPrincipal(true);
//        if (userObj instanceof UserEntity) {
//            if (null != req.getActivityId()) {
//                CultureCloudActivityEntity activity = cultureCloudActivityDao.get(req.getActivityId());
//                if (null != activity) {
////                    cultureCloudActivityDao.delete(activity);
//                    activity.setActivityIsDel(CultureCloudContext.DELETE);
//                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
//                    res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//                } else {
//                    res.setRet(ResponseContext.RES_DATA_NULL_CODE);
//                    res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
//                }
//            } else {
//                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//            }
//        } else {
//            res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
//            res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
//        }
//        return res;
//    }


    /**
     * 删除活动
     *
     * @param req
     * @return
     */
    @Override
    @Audit(operate = "删除活动")
    public IResponse deleteActivity(ActivityReq req) {
        GenericResponse res = new GenericResponse();
        try {
            if (null != req.getActivityId()) {
                CultureCloudActivityEntity cmsActivity = cultureCloudActivityDao.get(req.getActivityId());
                if(cmsActivity==null){
                    res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                    res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
                    return res;
                }
                Object userObj = getPrincipal(true);
				String depIds = getDepartment(userObj);
				if(StringUtils.isNoneEmpty(depIds) ) {
					 List<String> depIdList = Stream.of(depIds.split(","))  
				                .collect(Collectors.toList());
					 if(!depIdList.contains(cmsActivity.getDepartment().getId().toString())) {
							res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo("对不起，不能操作除本部门及子部门以外的数据！");
							return res;
					 }
				}
                if (cmsActivity.getActivityState() == 5) {
                    //回收站中的撤销
                    cmsActivity.setActivityIsDel(2);
//                    activityService.updateActivityDelStatus(activityId, 2);
                    cmsActivity.setActivityState(CultureCloudContext.TRASH);
                    cultureCloudActivityDao.update(cmsActivity);

                    //日志相关 先注释
//                    try {
//                        Object[] args = {activityId};
//                        String[] params = {"activityId"};
//                        cmsActivityLog.createLog(userId, args, params, ActivityInspect.User_Type_Admin, ActivityOperationEnum.DELETEACTIVITY, Constant.RESULT_STR_SUCCESS);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                    return res;
                }
                if (cmsActivity.getActivityState() == 6) {

                    String sql = "SELECT count(1) FROM t_culture_cloud_activity a,t_culture_cloud_activity_order o " +
                            "WHERE o.activityId = a.id AND a.id = " + cmsActivity.getId()+
                            " and o.orderPayStatus not in (2,7)";

                    int orderCount = cultureCloudActivityOrderDao.getListBySql(sql).size();

                    if (orderCount > 0 && req.getDelOrder() == null) {
//                        json.put("status", "3");
//                        json.put("activityName", cmsActivity.getActivityName());

//                        try {
//                            Object[] ob = { activityId };
//                            String[] params = { "activityId" };
//                            cmsActivityLog.createLog(userId, ob, params, ActivityInspect.User_Type_Admin,
//                                    ActivityOperationEnum.DELETEACTIVITY, Constant.RESULT_STR_SUCCESS);
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                        }

                        res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
                        res.setRetInfo("该活动已有订单，不能删除");
                        return res;                    }
                }
//                boolean status = activityService.updateActivityDelStatus(activityId, 1);
                cmsActivity.setActivityIsDel(1);
                cmsActivity.setActivityState(CultureCloudContext.TRASH);
                cultureCloudActivityDao.update(cmsActivity);

                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                return res;
//                if (status) {
//                    json.put("status", "2");
//                    json.put("msg", "该活动已删除至回收站");
//
////                    try {
////                        Object[] ob = {activityId};
////                        String[] params = {"activityId"};
////                        cmsActivityLog.createLog(userId, ob, params, ActivityInspect.User_Type_Admin, ActivityOperationEnum.DELETEACTIVITYBAK, Constant.RESULT_STR_SUCCESS);
////                    } catch (Exception e) {
////                        e.printStackTrace();
////                    }
//发短信 先注释
//                    if (StringUtils.isNotBlank(msgSMS)) {
//                        cmsActivityOrderService.revocationActivitySendSMS(activityId, msgSMS, userId);
//                    }
//
//                    return Result.ok(json);
//                } else {
//                    json.put("status", "1");
//                    json.put("msg", "操作失败");
//                    return Result.err(json, "", "操作失败");
//                }
            }
        } catch (Exception e) {
            logger.info("deleteActivity error" + e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO+"删除活动失败");

        }
        res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
        res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO+"删除活动失败");
        return res;
    }

    /**
     * 将回收站的活动还原至草稿箱
     *
     * @param req
     * @return
     */
    @Override
    public IResponse returnActivity(ActivityReq req) {
        GenericResponse res = new GenericResponse();
        try {
            if (null != req.getActivityId()) {
                CultureCloudActivityEntity cmsActivity = cultureCloudActivityDao.get(req.getActivityId());
                if(cmsActivity==null){
                    res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                    res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
                    return res;
                }

                //不知道什么线下活动 先注释
//                    CmsActivityOffline cmsActivityOffline = cmsActivityOfflineMapper.selectByPrimaryKey(id);
//                    if(cmsActivityOffline!=null){
//                        cmsActivity.setActivityIsDel(3);
//                    }else{
                        //待提交
                        cmsActivity.setActivityIsDel(1);
//                    }
                cmsActivity.setActivityState(1);
                    cultureCloudActivityDao.update(cmsActivity);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

            }
        } catch (Exception e) {
            logger.info("deleteActivity error" + e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
        }

        return res;
    }



    private IResponse addActivity(final ActivityReq req, final UserEntity user, String shopPath){
        GenericResponse res = new GenericResponse();
        try {
            CultureCloudActivityEntity activity = new CultureCloudActivityEntity();
            if (req != null) {
//                if(StringUtils.isBlank(shopPath)){
//                    CmsShop shop = (CmsShop) session.getAttribute("shop");
//                    if (shop != null && StringUtils.isNotBlank(shop.getShopId())) {
//                        shopPath = shop.getShopPath();
//                    }
//                }

                activity.setActivityName(StringUtils.isNotEmpty(req.getActivityName()) ? req.getActivityName() : "");
                activity.setActivityIconUrl(StringUtils.isNotEmpty(req.getActivityIconUrl()) ? req.getActivityIconUrl() : "");
                activity.setActivityMemo(StringUtils.isNotEmpty(req.getActivityMemo()) ? req.getActivityMemo() : "");
                activity.setActivityLon(null != req.getActivityLon()? new BigDecimal(req.getActivityLon()):new BigDecimal(0));
                activity.setActivityLat(null != req.getActivityLat()? new BigDecimal(req.getActivityLat()):new BigDecimal(0));
                activity.setActivityProvince(StringUtils.isNotEmpty(req.getActivityProvince()) ? req.getActivityProvince() : "");
                activity.setActivityAddress(StringUtils.isNotEmpty(req.getActivityAddress()) ? req.getActivityAddress() : "");
                activity.setActivityTime(StringUtils.isNotEmpty(req.getActivityTime()) ? req.getActivityTime() : "");
                activity.setActivityType(StringUtils.isNotEmpty(req.getActivityType()) ? req.getActivityType() : "");
                activity.setActivityLiaison(StringUtils.isNotEmpty(req.getActivityLiaison()) ? req.getActivityLiaison() : "");
                activity.setActivityWebSite(StringUtils.isNotEmpty(req.getActivityWebSite()) ? req.getActivityWebSite() : "");
                activity.setActivityMail(StringUtils.isNotEmpty(req.getActivityMail()) ? req.getActivityMail() : "");
                activity.setActivityTel(StringUtils.isNotEmpty(req.getActivityTel()) ? req.getActivityTel() : "");
                activity.setActivityMobile(StringUtils.isNotEmpty(req.getActivityMobile()) ? req.getActivityMobile() : "");
                activity.setActivityIsFree(null != req.getActivityIsFree() ? req.getActivityIsFree() : 0);
                activity.setActivityPaymentDesc(StringUtils.isNotEmpty(req.getActivityPaymentDesc()) ? req.getActivityPaymentDesc() : "");
                activity.setActivityState((null != req.getActivityState() && req.getActivityState()==1) ? req.getActivityState() : 0);
                activity.setActivityReservationType(null != req.getActivityReservationType()?req.getActivityReservationType():null);
                activity.setActivityReservationCount(null != req.getActivityReservationCount()?req.getActivityReservationCount():0);
                activity.setActivityStartTime(StringUtils.isNotEmpty(req.getActivityStartTime()) ? req.getActivityStartTime() : "");
                activity.setActivityEndTime(StringUtils.isNotEmpty(req.getActivityEndTime()) ? req.getActivityEndTime() : "");
                activity.setActivityNotice(StringUtils.isNotEmpty(req.getActivityNotice()) ? req.getActivityNotice() : "");
                activity.setActivityContent(StringUtils.isNotEmpty(req.getActivityContent()) ? req.getActivityContent() : "");
                activity.setActivitySysUrl(StringUtils.isNotEmpty(req.getActivitySysUrl()) ? req.getActivitySysUrl() : "");
                activity.setActivityEventCode(null != req.getActivityEventCode() ? req.getActivityEventCode() : null);
                activity.setActivityRecommendTime(new Timestamp(System.currentTimeMillis()));
                activity.setActivityCreateTime(new Timestamp(System.currentTimeMillis()));
                activity.setActivityHost(StringUtils.isNotEmpty(req.getActivityHost()) ? req.getActivityHost() : "");
                activity.setActivityOrganizer(StringUtils.isNotEmpty(req.getActivityOrganizer()) ? req.getActivityOrganizer() : "");
                activity.setActivityCoorganizer(StringUtils.isNotEmpty(req.getActivityCoorganizer()) ? req.getActivityCoorganizer() : null);
                activity.setActivityPerformed(StringUtils.isNotEmpty(req.getActivityPerformed()) ? req.getActivityPerformed() : null);
                activity.setActivityPrompt(StringUtils.isNotEmpty(req.getActivityPrompt()) ? req.getActivityPrompt() : null);
                activity.setActivitySpeaker(StringUtils.isNotEmpty(req.getActivitySpeaker()) ? req.getActivitySpeaker() : null);
                activity.setActivityAttachment(StringUtils.isNotEmpty(req.getActivityAttachment()) ? req.getActivityAttachment() : null);
                activity.setActivityPersonal(null != req.getActivityPersonal() ? req.getActivityPersonal() : null);
                activity.setActivityTicketSettings(StringUtils.isNotEmpty(req.getActivityTicketSettings()) ? req.getActivityTicketSettings() : "Y");
                activity.setActivityTicketNumber(null != req.getActivityTicketNumber() ? req.getActivityTicketNumber() : 0);
                activity.setActivityTicketCount(null != req.getActivityTicketCount() ? req.getActivityTicketCount() : 0);
                activity.setActivityIdentityCard(null !=req.getActivityIdentityCard() ? req.getActivityIdentityCard() : 0);
                activity.setActivityRecommend(null != req.getActivityRecommend() ? req.getActivityRecommend() : null);
                activity.setCustomizeMethod(null != req.getCustomizeMethod()? req.getCustomizeMethod():null);
//                activity.setActivitySmsType(0);
//                activity.setActivityDeptLabel("");
                activity.setActivityPriceNotice(StringUtils.isNotEmpty(req.getActivityPriceNotice()) ? req.getActivityPriceNotice() : null);
                activity.setActivitySort(null != req.getActivitySort()?req.getActivitySort():0);
//                activity.setActivityIsTop(0);
                activity.setActivityTopTime(new Timestamp(System.currentTimeMillis()));
//                activity.setAvailableCount();
//                activity.setEventTime("");
//                activity.setEventDate("");
//                activity.setKeyword(StringUtils.isNotEmpty(req.getKeywords())?req.getKeywords():null);
                activity.setSeatIds("");
                activity.setFixedTel(StringUtils.isNotEmpty(req.getFixedTel())?req.getFixedTel():null);
                activity.setResourceImgUrl(StringUtils.isNotEmpty(req.getResourceImgUrl())?req.getResourceImgUrl():null);
                activity.setResourceVideoUrl(StringUtils.isNotEmpty(req.getResourceVideoUrl())?req.getResourceVideoUrl():null);
                activity.setActivityProfile(StringUtils.isNotEmpty(req.getActivityProfile())?req.getActivityProfile():null);

                activity.setAvailableCount(StringUtils.isNotEmpty(req.getAvailableCount())?req.getAvailableCount():"0");
                activity.setEventTime(StringUtils.isNoneEmpty(req.getEventTime())?req.getEventTime():null);
                activity.setSignStartTime(StringUtils.isNoneEmpty(req.getSignStartTime())?
                        new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(req.getSignStartTime()).getTime()):null);
                activity.setSignEndTime(StringUtils.isNoneEmpty(req.getSignEndTime())?
                        new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(req.getSignEndTime()).getTime()):null);
                activity.setMaxAge(StringUtils.isNotEmpty(req.getMaxAge())?req.getMaxAge():null);
                activity.setMinAge(StringUtils.isNotEmpty(req.getMinAge())?req.getMinAge():null);
                activity.setActivityTimeDes(StringUtils.isNotEmpty(req.getActivityTimeDes())?req.getActivityTimeDes():null);
                activity.setActivitySite(StringUtils.isNotEmpty(req.getActivitySite())?req.getActivitySite():null);
                activity.setActivityPublishFrom(StringUtils.isNotEmpty(req.getActivityPublishFrom())?req.getActivityPublishFrom():null);
                activity.setActivityArea(StringUtils.isNotEmpty(req.getActivityArea())?req.getActivityArea():null);
                activity.setActivityTown(StringUtils.isNotEmpty(req.getActivityTown())?req.getActivityTown():null);
                activity.setActivityVillage(StringUtils.isNotEmpty(req.getActivityVillage())?req.getActivityVillage():null);
                activity.setSpikeTimes(StringUtils.isNotEmpty(req.getSpikeTimes())?req.getSpikeTimes():"");//                activity.setOrderPayStatus((short) 0);
//                activity.setActivityOrderId("");
//                activity.setShowOffline("");
                activity.setSpikeType(null != req.getSpikeType()?req.getSpikeType():0);
//                activity.setOrderPrice();
                activity.setSingleEvent(null != req.getSingleEvent()?req.getSingleEvent(): 0);
//                activity.setActivityIsDetails(0);
//                activity.setActivityIsRecommend(0);
//                activity.setActivityArtType("");
//                activity.setSignStartTime(new Timestamp(System.currentTimeMillis()));
//                activity.setSignEndTime(new Timestamp(System.currentTimeMillis()));
//                activity.setEventDateTimes("");

                activity.setCreateTime(new Timestamp(System.currentTimeMillis()));
                activity.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
                activity.setState(1);
                activity.setDepartment(null!=req.getDepartmentId() ? cultureCloudDepartmentDao.get(req.getDepartmentId()) : null);

                activity.setCancelTime(null != req.getCancelTime()?req.getCancelTime():null);
                activity.setCancelEndTime(null != req.getCancelEndTime()?req.getCancelEndTime():null);
                activity.setWrittenOffTime(null != req.getWrittenOffTime()?req.getWrittenOffTime():null);
                activity.setBeforeStartMinute(null != req.getBeforeStartMinute()?req.getBeforeStartMinute():null);
                activity.setAfterStartMinute(null != req.getAfterStartMinute()?req.getAfterStartMinute():null);
                activity.setDetailTimeLimitMinutes(null != req.getDetailTimeLimitMinutes()?req.getDetailTimeLimitMinutes():null);
                activity.setTimeLimitOfPickingUpTickets(null != req.getTimeLimitOfPickingUpTickets()?req.getTimeLimitOfPickingUpTickets():null);
//                activity.setActivitySmsInfo(StringUtils.isNotEmpty(req.getActivitySmsInfo())?req.getActivitySmsInfo():null);
                activity.setActivitySmsType(null != req.getActivitySmsType()?req.getActivitySmsType():null);

    			if(StringUtils.isNoneEmpty(req.getKeywords())) {
    				String[] keywords = req.getKeywords().split(",");
    				for (String keyword : keywords) {
    					CultureCloudTagEntity cultureCloudTag = cultureCloudTagDao.getUniqueByHql(
    							"from CultureCloudTagEntity a where a.category=1 and a.displayType=2 and a.tagName='"+keyword+"'", "");
    					if(null == cultureCloudTag) {
    						cultureCloudTag = new CultureCloudTagEntity();
    						cultureCloudTag.setTagName(keyword);
    						cultureCloudTag.setCategory(1);
    						cultureCloudTag.setDisplayType(2);
    						cultureCloudTagDao.save(cultureCloudTag);
    					}
    					activity.getTagList().add(cultureCloudTag);
    				}
    			}
				if (StringUtils.isNotEmpty(req.getTagIdList())) {
					String[] tagIds = req.getTagIdList().split(",");
					for (String tageId : tagIds) {
						activity.getTagList().add(cultureCloudTagDao.get(Integer.parseInt(tageId)));
					}
				}
				
				if (StringUtils.isNotEmpty(req.getTypeIdList())) {
					String[] typeIds = req.getTypeIdList().split(",");
					for (String typeId : typeIds) {
						activity.getTagList().add(cultureCloudTagDao.get(Integer.parseInt(typeId)));
					}
				}
				if (StringUtils.isNotEmpty(req.getArtTypeIdList())) {
					String[] typeIds = req.getArtTypeIdList().split(",");
					for (String typeId : typeIds) {
						activity.getTagList().add(cultureCloudTagDao.get(Integer.parseInt(typeId)));
					}
				}


                if (StringUtils.isNotEmpty(req.getAddressId())) {
                	CultureCloudAddressEntity address = cultureCloudAddressDao.get(Integer.parseInt(req.getAddressId()));
                    if(null != address) {
                    	req.setActivityLon(StringUtils.isNotEmpty(address.getLongitude()) ? 
                    			Double.valueOf(address.getLongitude()) :  Double.valueOf(0));
                    	req.setActivityLat(StringUtils.isNotEmpty(address.getLatitude()) ? 
                    			Double.valueOf(address.getLatitude()):Double.valueOf("0"));
                    }else {
                    	req.setActivityLon(Double.valueOf(0));
                    	req.setActivityLat(Double.valueOf("0"));
                    }
                    
                } else {
                	req.setActivityLon(Double.valueOf(0));
                	req.setActivityLat(Double.valueOf("0"));
                }
//                if (StringUtils.isBlank(req.getActivityId())) {
//                    activity.setActivityId(UUIDUtils.createUUId());
//                }
                activity.setActivityReleaseTime(new Timestamp(new Date().getTime()));
                activity.setPublicTime(new Timestamp(new Date().getTime()));
                activity.setActivityUpdateTime(new Timestamp(new Date().getTime()));

                if(StringUtils.isNoneBlank(req.getShowOffline()) && "offline".equals(req.getShowOffline())){
                    activity.setActivityIsDel(3);
                }else{
                //还没有这个类，先注释掉
//                    if (!staticServer.getCityInfo().split(",")[3].equals("sh")){
//                        //活动无审核开关 按照白名单来
//                        //只有白名单是直接通过
//                        if ( (user.getUserIsWhite() != null && user.getUserIsWhite().intValue() == 1)) {
//                            //审核通过
//                            req.setActivityIsDel(CultureCloudContext.ACTIVITY_IS_DEL_STATE_NORMAL);
//                        } else {
//                            //待提交5
//                            req.setActivityIsDel(5);
//                        }
//
//                    } else {
//                        if (StringUtils.isNotBlank(shopPath)){
//                            Map<String,Object> params = new HashMap<String,Object>();
//                            params.put("shopPath", shopPath);
//                            params.put("switchType", 2);
//                            CmsShopSwitch shopSwitch = cmsShopSwitchMapper.queryShopSwitchByShopPathAndType(params);
//                            if (shopSwitch != null){
//                                if (shopSwitch.getNeedAudit() == 1){
//                                    req.setActivityIsDel(CultureCloudContext.ACTIVITY_IS_DEL_STATE_EXAMINE);
//                                }else {
//                                    req.setActivityIsDel(CultureCloudContext.ACTIVITY_IS_DEL_STATE_NORMAL);
//                                }
//                            }else{
//                                req.setActivityIsDel(CultureCloudContext.ACTIVITY_IS_DEL_STATE_EXAMINE);
//                            }
//                        }
////                    	activity.setActivityIsDel(Constant.NORMAL);
//                    }
                }

                if (req.getActivityState() != null&& req.getActivityState() == 6) {
                    activity.setPublicTime(new Timestamp(new Date().getTime()));
                }
                if (req.getActivityPrice() == null || StringUtils.isBlank(req.getActivityPrice())){
                    activity.setActivityPrice("0");
                }else{
                    activity.setActivityPrice(req.getActivityPrice());
                }

                if (StringUtils.isBlank(req.getActivityProvince())) {
                    //要改
//                    activity.setActivityProvince(user.getUserProvince());
                }

                String activityDept = req.getActivityProvince().split(",")[0];
                if (StringUtils.isNotBlank(req.getActivityCity())) {
                    if (req.getActivityCity().equals("0")) {
                        activity.setActivityCity("");
                        activity.setCreateActivityCode(10);
                    } else {
                        activityDept = activityDept + "." + req.getActivityCity().split(",")[0];
                    }
                }
                if (StringUtils.isNotBlank(req.getActivityArea())) {
                    if (req.getActivityArea().equals("1")) {
                        activity.setActivityArea("");
                        activity.setCreateActivityCode(1);
                    } else {
                        activityDept = activityDept + "." + req.getActivityArea().split(",")[0];
                    }
                }
                if (StringUtils.isNotBlank(req.getActivityTown())) {
                    if (req.getActivityTown().equals("2")) {
                        activity.setActivityTown("");
                        activity.setCreateActivityCode(2);
                    } else {
                        activityDept = activityDept + "." + req.getActivityTown().split(",")[0];
                    }
                }
                if (StringUtils.isNotBlank(req.getActivityVillage())) {
                    if (req.getActivityVillage().equals("3")) {
                        activity.setActivityVillage("");
                        activity.setCreateActivityCode(3);
                    } else {
                        activityDept = activityDept + "." + req.getActivityVillage().split(",")[0];
                    }
                }

                if (StringUtils.isNotBlank(req.getVenueType())) {
                    if (req.getVenueType().equals("4")) {
                        activity.setCreateActivityCode(4);
                    }
                }
                activity.setActivityDept(activityDept);

                if (user != null) {
                    activity.setActivityCreateUser(user.getId().toString());
                    activity.setActivityUpdateUser(user.getId().toString());
                    activity.setPublisher(user.getName());
                    //activity.setActivityDept(sysUser.getUserDeptPath());
                }
                Integer activityIsReservation = req.getActivityIsReservation() != null ? req.getActivityIsReservation() : 1;
                if (activityIsReservation == 1) {    //不可预订
                    activity.setActivitySalesOnline("N");
                    activity.setActivityIsReservation(1);
                    activity.setActivitySupplementType(1);
                } else if (activityIsReservation == 2) {    //在线选座
                    activity.setActivityIsReservation(2);
                    activity.setActivitySalesOnline("Y");
                    

//                    if(null!=req.getEeatTemplateId()) {
//                    	CultureCloudVenueSeatTemplateEntity venueSeatTemplate = cultureCloudVenueSeatTemplateDao.get(req.getEeatTemplateId());
//                    	if(null != venueSeatTemplate) {
//                            activity.setVenueSeatTemplate(venueSeatTemplate);
//                            for(CultureCloudVenueSeatEntity venueSeat : venueSeatTemplate.getVenueSeatList()) {
//                            	CultureCloudSeatAccountEntity seatAccount = new CultureCloudSeatAccountEntity();
//                            	seatAccount.setActivity(activity);
//                            	seatAccount.setState(EntityContext.RECORD_STATE_VALID);
//                            	seatAccount.setSeatArea(venueSeat.getSeatArea());
//                            	seatAccount.setSeatCode(venueSeat.getSeatCode());
//                            	seatAccount.setSeatColumn(venueSeat.getSeatColumn());
//                            	seatAccount.setSeatRow(venueSeat.getSeatRow());
//                            	seatAccount.setSeatStatus(venueSeat.getSeatStatus());
//                            	seatAccount.setVenueSeat(venueSeat);
//                            	cultureCloudSeatAccountDao.save(seatAccount);
//                            }
//                    	}
//                    }
                    
    				if (null!=req.getSeatIdList() && req.getSeatIdList().size()>0 && StringUtils.isBlank(req.getEventDate())) {
       					List<String> seatId_seatStatus_List = req.getSeatIdList().get(0);
       					boolean setVenueSeatTemplateState = true;
    					for(String ids : seatId_seatStatus_List) {
        					String[] seatIds = ids.split(",");
        					for (String seatId_seatStatus : seatIds) {
        						String[] ss = seatId_seatStatus.split("_");
        						if(null!=ss && ss.length==2) {
        							String id = ss[0];
        							String seatStatus = ss[1];
        							CultureCloudVenueSeatEntity seat = cultureCloudVenueSeatDao.get(Integer.valueOf(id));
        							if(null!=seat) {
        								if(setVenueSeatTemplateState) {
        									activity.setVenueSeatTemplate(seat.getVenueSeatTemplate());
        									setVenueSeatTemplateState = false;
        								}
            							CultureCloudVenueSeatEntity venueSeat = new CultureCloudVenueSeatEntity();
            							venueSeat.setLastModifyTime(new Timestamp(new Date().getTime()));
            							venueSeat.setSeatArea(seat.getSeatArea());
            							venueSeat.setSeatCode(seat.getSeatCode());
            							venueSeat.setSeatColumn(seat.getSeatColumn());
            							venueSeat.setSeatRow(seat.getSeatRow());
            							venueSeat.setSeatStatus(Integer.valueOf(seatStatus));
            							venueSeat.setSeatVal(seat.getSeatVal());
            							venueSeat.setState(EntityContext.RECORD_STATE_VALID);
            							venueSeat.setVenueSeatTemplate(seat.getVenueSeatTemplate());
            							venueSeat.setActivity(activity);
            							cultureCloudVenueSeatDao.save(venueSeat);
        							}
        						}
        					}
    					}
    				}
                    
                } else if (activityIsReservation == 3) {    //自由入座
                    activity.setActivityIsReservation(2);
                    activity.setActivitySalesOnline("N");
                } else if (activityIsReservation == 4) {        //直接前往
                    activity.setActivityIsReservation(1);
                    activity.setActivitySalesOnline("N");
                    activity.setActivitySupplementType(2);
                } else if (activityIsReservation == 5) {        //电话预约
                    activity.setActivityIsReservation(1);
                    activity.setActivitySalesOnline("N");
                    activity.setActivitySupplementType(3);
                } else if (activityIsReservation == 6) {        //拼手气
                    activity.setActivityIsReservation(2);
                    activity.setActivitySalesOnline("N");
                    activity.setActivitySupplementType(4);
                }
                activity.setEventCount(req.getActivityReservationCount());

                //保存活动场馆关联关系
                if (null != req.getVenueId()) {
                    CultureCloudVenueEntity venue = cultureCloudVenueDao.get(req.getVenueId());
                    activity.getVenueList().add(venue);
                    //还要保存room关系 先注释
//                    CmsActivityVenueRelevance relevance = new CmsActivityVenueRelevance();
//                    relevance.setActivityId(req.getActivityId());
//                    relevance.setVenueId(req.getVenueId());
//                    relevance.setRoomId(req.getVenueRoomId());
//                    relevanceMapper.addActivityVenueRelevance(relevance);
                }
                String eventDate[] = null;
                if(StringUtils.isNotBlank(req.getEventDate())){
                    eventDate = req.getEventDate().split(",");
                }
                String eventTime[] = null;
                if(StringUtils.isNotBlank(req.getEventTime())){
                    eventTime = req.getEventTime().split(",");
                }
                String spikeTimes[] = null;
                String spikeEndTimes[] = null;
                if (StringUtils.isNotBlank(req.getSpikeTimes())) {
                    spikeTimes = req.getSpikeTimes().split(",");
                }
                if (StringUtils.isNotBlank(req.getSpikeEndTimes())) {
                    spikeEndTimes = req.getSpikeEndTimes().split(",", -1);
                }
                String availableCount[] = null;
                if (StringUtils.isNotBlank(req.getAvailableCount())) {
                    availableCount = req.getAvailableCount().split(",");
                }
                String orderPrice[] = null;
                if (StringUtils.isNotBlank(req.getOrderPrice())){
                    orderPrice = req.getOrderPrice().split(",");
                }
                String seatId[] = null;
                if (StringUtils.isNotBlank(req.getSeatIds())) {
                    seatId = req.getSeatIds().split(";");
                }
                Integer[] activitySecondFloors = req.getActivitySecondFloors();
                cultureCloudActivityDao.save(activity);
                if (activity.getActivityIsReservation() != 1) {
                    if(eventDate !=null){
                        for (int i = 0; i < eventDate.length; i++) {
                            CultureCloudActivityEventEntity activityEvent = new CultureCloudActivityEventEntity();
                            activityEvent.setState(EntityContext.RECORD_STATE_VALID);
//                            activityEvent.setEventId(UUIDUtils.createUUId());
//                            activityEvent.setActivityId(req.getActivityId());
                            activityEvent.setActivity(activity);
                            activityEvent.setEventDate(eventDate[i]);
                            activityEvent.setSingleEvent(req.getSingleEvent());
                            if (req.getSingleEvent() == 1) {
                                activityEvent.setEventDateTime(req.getActivityEndTime() + " " + eventTime[i]);
                                activityEvent.setEventEndDate(req.getActivityEndTime());
                            } else {
                                activityEvent.setEventDateTime(eventDate[i] + " " + eventTime[i]);
                                activityEvent.setEventEndDate(eventDate[i]);
                            }
                            if (StringUtils.isBlank(req.getEndTimePoint())) {
                                activity.setEndTimePoint(activityEvent.getEventDateTime());
                            } else if (activityEvent.getEventDateTime().compareTo(req.getEndTimePoint()) > 1) {
                                activity.setEndTimePoint(activityEvent.getEventDateTime());
                            }
                            activityEvent.setEventTime(eventTime[i]);
                            if (req.getSpikeType() != null && req.getSpikeType() != 0) {
                                activityEvent.setSpikeType(req.getSpikeType());
                                if (StringUtils.isNotBlank(req.getSpikeTimes())) {
                                    activityEvent.setSpikeTime(new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(spikeTimes[i]).getTime()));
                                }
                                if (StringUtils.isNotBlank(req.getSpikeEndTimes())) {
                                    if (spikeEndTimes != null && spikeEndTimes.length > i){
                                        String spikeEndTime = spikeEndTimes[i];
                                        if (StringUtils.isNotBlank(spikeEndTime)) {
                                            activityEvent.setSpikeEndTime(new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(spikeEndTime).getTime()));
                                        }
                                    }
                                }
                            }

                            if (req.getActivityIsFree() == 2) {
                                if(orderPrice!=null && orderPrice.length>i){
                                    activityEvent.setOrderPrice(orderPrice[i]);
                                }
                            }
                            if (req.getActivityIsFree() == 3) {
                                if (req.getActivityPayPrice() != null){
                                    activityEvent.setOrderPrice(String.valueOf(req.getActivityPayPrice()));
                                }
                            }

                            if (StringUtils.isNotBlank(availableCount[i])) {
                                activityEvent.setAvailableCount(null == availableCount[i]?0:Integer.parseInt(availableCount[i]));
                                activityEvent.setOrderCount(0);

                                activity.setEventCount(Integer.parseInt(availableCount[i]));
                            } else {
                                if (null != req.getAvailableCount()){
                                    activityEvent.setAvailableCount(Integer.parseInt(req.getAvailableCount()));
                                    activityEvent.setOrderCount(0);
                                }
                                activity.setEventCount(Integer.parseInt(req.getAvailableCount()));
                            }
                            if(activitySecondFloors != null){
                                activityEvent.setActivitySecondFloor(activitySecondFloors[i]);
                            }
                            //cmsActivityEventService.addActivityEvent(activityEvent);
                            if (req.getSingleEvent() != null && req.getSingleEvent() == 0) {
                                if (StringUtils.isNotBlank(req.getSeatIds())) {
                                    activityEvent.setSeatIds(seatId[i]);
                                    //cmsActivitySeatService.addEventSeatInfo(activityEvent, sysUser);
                                }
                            } else {
                                if (i == 0 && StringUtils.isNotBlank(req.getSeatIds())) {
                                    activityEvent.setSeatIds(seatId[i]);
                                    //cmsActivitySeatService.addEventSeatInfo(activityEvent, sysUser);
                                }
                            }
            				if (null!=req.getSeatIdList() && req.getSeatIdList().size()>0) {
            					List<String> seatId_seatStatus_List = req.getSeatIdList().get(i);
            					boolean setVenueSeatTemplateState = true;
            					for(String ids : seatId_seatStatus_List) {
                					String[] seatIds = ids.split(",");
                					for (String seatId_seatStatus : seatIds) {
                						String[] ss = seatId_seatStatus.split("_");
                						if(null!=ss && ss.length==2) {
                							String id = ss[0];
                							String seatStatus = ss[1];
                							CultureCloudVenueSeatEntity seat = cultureCloudVenueSeatDao.get(Integer.valueOf(id));
                							if(null!=seat) {
                								if(setVenueSeatTemplateState) {
                									activity.setVenueSeatTemplate(seat.getVenueSeatTemplate());
                									setVenueSeatTemplateState = false;
                								}
                    							CultureCloudVenueSeatEntity venueSeat = new CultureCloudVenueSeatEntity();
                    							venueSeat.setLastModifyTime(new Timestamp(new Date().getTime()));
                    							venueSeat.setSeatArea(seat.getSeatArea());
                    							venueSeat.setSeatCode(seat.getSeatCode());
                    							venueSeat.setSeatColumn(seat.getSeatColumn());
                    							venueSeat.setSeatRow(seat.getSeatRow());
                    							venueSeat.setSeatStatus(Integer.valueOf(seatStatus));
                    							venueSeat.setSeatVal(seat.getSeatVal());
                    							venueSeat.setState(EntityContext.RECORD_STATE_VALID);
                    							venueSeat.setVenueSeatTemplate(seat.getVenueSeatTemplate());
                    							venueSeat.setActivity(activity);
                    							venueSeat.setActivityEvent(activityEvent);
                    							cultureCloudVenueSeatDao.save(venueSeat);
                							}
                						}
                					}
            					}

            				}
                            cultureCloudActivityEventDao.save(activityEvent);
                            activity.getEventList().add(activityEvent);
                        }
                    }

                } else {
                    CultureCloudActivityEventEntity activityEvent = new CultureCloudActivityEventEntity();
//                    activityEvent.setEventId(UUIDUtils.createUUId());
//                    activityEvent.setActivityId(req.getActivityId());
                    cultureCloudActivityDao.save(activity);
                    activityEvent.setState(EntityContext.RECORD_STATE_VALID);
                    activityEvent.setActivity(activity);
                    activityEvent.setEventDate(req.getActivityStartTime());
                    activityEvent.setEventTime(req.getActivityTime());
                    activityEvent.setEventEndDate(req.getActivityEndTime());
                    activityEvent.setEventDateTime(req.getActivityEndTime() + " " + req.getActivityTime());
                    activity.setEndTimePoint(req.getActivityEndTime() + " " + req.getActivityTime());
                    if (req.getActivityPayPrice() != null){
                        activityEvent.setOrderPrice(String.valueOf(req.getActivityPayPrice()));
                    }
                    cultureCloudActivityEventDao.save(activityEvent);
                    // cmsActivityEventService.addActivityEvent(activityEvent);
                    activity.getEventList().add(activityEvent);
                }


                //判断中英文 先注释掉
//                boolean chinese = CommonUtil.isContainChinese(req.getActivityName());
//                if (!chinese){
//                    CmsActivityLanguage language = new CmsActivityLanguage();
//                    language.setId(UUIDUtils.createUUId());
//                    language.setActivityId(req.getActivityId());
//                    cmsActivityLanguageMapper.insert(language);
//                }
            }

            if(req.getActivityIsDel() == null){
                activity.setActivityIsDel(CultureCloudContext.NORMAL);
            }

//            activityMapper.addCmsActivity(req);
            cultureCloudActivityDao.saveOrUpdate(activity);
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(activity.getId().toString());
//            CmsOperationLogVO cmsOperationLogVO = new CmsOperationLogVO();
//            cmsOperationLogVO.setShopPath(shopPath);
//            cmsOperationLogVO.setCreateUser(user.getUserId());
//            cmsOperationLogVO.setEnumName("ACTIVITY_ADD");
//            //保存管理员操作日志
//            gatewayCallService.saveOperationLog(cmsOperationLogVO);

        } catch (Exception e) {
            logger.error("addActivity error {}", e);
            e.printStackTrace();
        }
        return res;
    }

    public String addActivity(final ActivityVo activityVo, String[] ids) {
        try {

            if (activityVo != null) {
                // 保存活动场馆关联关系
//                if (null != activityVo.getVenueId() && activityVo.getCreateActivityCode() == 0) {
//                    CultureCloudVenueEntity venue = cultureCloudVenueDao.get(activityVo.getVenueId());
//                    activity.getVenueList().add(venue);
////                    CmsActivityVenueRelevance relevance = new CmsActivityVenueRelevance();
////                    relevance.setActivityId(req.getActivityId());
////                    relevance.setVenueId(req.getVenueId());
////                    relevance.setRoomId(req.getVenueRoomId());
////                    relevanceMapper.addActivityVenueRelevance(relevance);
//                }
                List<ActivityEventVo> eventList = activityVo.getActivityEventList();
                if(eventList!=null&&eventList.size()>0){
                    for (ActivityEventVo cmsActivityEventVO : eventList) {
                        cmsActivityEventVO.setShopProvince(activityVo.getShopProvince());
                        CultureCloudActivityEventEntity activityEvent = new CultureCloudActivityEventEntity();
                        copyProperties(activityEvent, cmsActivityEventVO);
                        activityEvent.setState(EntityContext.RECORD_STATE_VALID);
//                        activityEvent.setActivity(cultureCloudActivityDao.get(Integer.parseInt(cmsActivityEventVO.getActivityId())));
//                        cultureCloudActivityEventDao.save(activityEvent);
//                        cmsActivityEventService.addActivityEvent(cmsActivityEventVO);
                    }
                }
                if (activityVo.getActivityIsReservation() != 1) {
                    List<CultureCloudActivitySeatForm> seat = activityVo.getActivitySeatList();
                	/*for (CultureCloudActivitySeatEntity cmsActivitySeat : seat) {
                		cmsActivitySeat.setShopProvince(activity.getShopProvince());
					}*/
                    if(seat!=null&&seat.size()>0){
//                        for (CultureCloudActivitySeatEntity cmsActivitySeat : seat) {
//                            cmsActivitySeat.setShopProvince(activityVo.getShopProvince());
//                        }
                        for (CultureCloudActivitySeatForm cmsActivitySeat : seat) {
                            CultureCloudActivitySeatEntity seatEntity = new CultureCloudActivitySeatEntity();
                            copyProperties(seatEntity, cmsActivitySeat);
                            cultureCloudActivitySeatDao.save(seatEntity);}
                        //现在没有分province 用同一张表
//                        cmsActivitySeatMapper.addActivitySeatList(seat,activityVo.getShopProvince());
                    }
                }
            }

            //记录外部系统活动id和本系统活动id的关联表 不知道干嘛的 先注释
//            if(activityVo.getAreaType()!=null){
//                ActivityidOutRel activityidOutRel = new ActivityidOutRel();
//                activityidOutRel.setActivityId(activityVo.getActivityId());
//                activityidOutRel.setActivityJaV(activityVo.getOutId());
//                activityidOutRel.setType(activityVo.getAreaType());
//                activityidOutRelMapper.insertSelective(activityidOutRel);
//            }

            if(activityVo.getExtendInfo()!=null) {
                String aeiHql = "from CultureCloudActivityExtendInfoEntity where activityId = "+activityVo.getActivityId();
                CultureCloudActivityExtendInfoEntity yExtendInfo = cultureCloudActivityExtendInfoDao.getUniqueByHql(aeiHql);
                if(yExtendInfo==null) {
//                    activityVo.getExtendInfo().setActivityExtendInfoId(UUIDUtils.createUUId());
                    activityVo.getExtendInfo().setActivityId(activityVo.getActivityId().toString());
                    cultureCloudActivityExtendInfoDao.save(activityVo.getExtendInfo());
//                    activityExtendInfoMapper.insertSelective(activityVo.getExtendInfo());
                }
            }

//            int count = activityMapper.insertSelective(activityVo);
//不知道为什么要保存两次 创图写的什么东西
//            cultureCloudActivityDao.save(activityVo);
            //标签
//            cmsTagRelateService.insertTagRelateList(req.getActivityId(), Constant.TYPE_ACTIVITY, ids);

//            if (count <= 0) {
//                return "FAILURE";
//            }else
//            {
//                return "SUCCESS";
//            }
        } catch (Exception e) {
            logger.error("addActivity error {}", e);
            e.printStackTrace();
            throw new RuntimeException();
        }
        return "SUCCESS";
    }

    private IResponse editActivity(final ActivityReq req, final UserEntity user, final String seatIds, final boolean hadOrder,
                                   String shopPath) {
        GenericResponse res = new GenericResponse();
        CultureCloudActivityEntity activity = cultureCloudActivityDao.get(req.getActivityId());
        try {
            if (req != null) {

                //还没有 先注释
//                if(StringUtils.isBlank(shopPath)){
//                    CmsShop shop = (CmsShop) session.getAttribute("shop");
//                    if (shop != null && StringUtils.isNotBlank(shop.getShopId())) {
//                        shopPath = shop.getShopPath();
//                    }
//                }


                if(StringUtils.isNoneBlank(req.getShowOffline()) && "offline".equals(req.getShowOffline())){
                    activity.setActivityIsDel(3);
                }else{
                    if(!hadOrder){
                        //还没有 先注释
//                        if (!staticServer.getCityInfo().split(",")[3].equals("sh")){
//                            //活动无审核开关 按照白名单来
//                            //只有白名单是直接通过
//                            if ( (user.getUserIsWhite() != null && user.getUserIsWhite().intValue() == 1)) {
//                                //审核通过
//                                activity.setActivityIsDel(CultureCloudContext.ACTIVITY_IS_DEL_STATE_NORMAL);
//                            } else {
//                                //待提交5
//                                activity.setActivityIsDel(5);
//                            }
//                        } else {
//                            if (StringUtils.isNotBlank(shopPath)){
//                                Map<String,Object> params = new HashMap<String,Object>();
//                                params.put("shopPath", shopPath);
//                                params.put("switchType", 2);
//                                CmsShopSwitch shopSwitch = cmsShopSwitchMapper.queryShopSwitchByShopPathAndType(params);
//                                if (shopSwitch != null){
//                                    if (shopSwitch.getNeedAudit() == 1){
//                                        activity.setActivityIsDel(CultureCloudContext.ACTIVITY_IS_DEL_STATE_EXAMINE);
//                                    }else {
//                                        activity.setActivityIsDel(CultureCloudContext.ACTIVITY_IS_DEL_STATE_NORMAL);
//                                    }
//                                }else{
//                                    activity.setActivityIsDel(CultureCloudContext.ACTIVITY_IS_DEL_STATE_EXAMINE);
//                                }
//                            }
////                        	cur.setActivityIsDel(Constant.NORMAL);
//                        }
                    }
                }

                if (StringUtils.isNotBlank(req.getActivityProvince())) {
                    String activityDept = req.getActivityProvince().split(",")[0];

                    if (StringUtils.isNotBlank(req.getActivityCity())) {
                        if (req.getActivityCity().equals("0")) {
                            activity.setActivityCity("");
                            activity.setCreateActivityCode(10);
                        } else {
                            activityDept = activityDept + "." + req.getActivityCity().split(",")[0];
                        }
                    }
                    if (StringUtils.isNotBlank(req.getActivityArea())) {
                        if (req.getActivityArea().equals("1")) {
                            activity.setActivityArea("");
                            activity.setCreateActivityCode(1);
                        } else {
                            activityDept = activityDept + "." + req.getActivityArea().split(",")[0];
                        }
                    }
                    if (StringUtils.isNotBlank(req.getActivityTown())) {
                        if (req.getActivityTown().equals("2")) {
                            activity.setActivityTown("");
                            activity.setCreateActivityCode(2);
                        } else {
                            activityDept = activityDept + "." + req.getActivityTown().split(",")[0];
                        }
                    }
                    if (StringUtils.isNotBlank(req.getActivityVillage())) {
                        if (req.getActivityVillage().equals("3")) {
                            activity.setActivityVillage("");
                            activity.setCreateActivityCode(3);
                        } else {
                            activityDept = activityDept + "." + req.getActivityVillage().split(",")[0];
                        }
                    }

                    if (StringUtils.isNotBlank(req.getVenueType())) {
                        if (req.getVenueType().equals("4")) {
                            activity.setCreateActivityCode(4);
                        }
                    }
                    activity.setActivityDept(activityDept);
                }

                if (activity.getPublicTime() == null && req.getActivityState() != null && req.getActivityState() == 6) {
                    activity.setPublicTime(new Timestamp(new Date().getTime()));
                }
                Integer activityIsReservation = req.getActivityIsReservation() != null ? req.getActivityIsReservation() : 1;
                if (activityIsReservation == 1) {    //不可预订
                    activity.setActivitySalesOnline("N");
                    activity.setActivityIsReservation(1);
                    activity.setActivitySupplementType(1);
                } else if (activityIsReservation == 2) {    //在线选座
                    activity.setActivityIsReservation(2);
                    activity.setActivitySalesOnline("Y");
                    activity.setVenueSeatTemplate(null!=req.getEeatTemplateId() ? 
                    		cultureCloudVenueSeatTemplateDao.get(req.getEeatTemplateId()) 
                    		: activity.getVenueSeatTemplate());
//                    if(null!=req.getEeatTemplateId()) {
//                    	CultureCloudVenueSeatTemplateEntity venueSeatTemplate = cultureCloudVenueSeatTemplateDao.get(req.getEeatTemplateId());
//                    	if(null != venueSeatTemplate&&((activity.getVenueSeatTemplate()==null)||
//                    			(null!=activity.getVenueSeatTemplate() && !activity.getVenueSeatTemplate().getId().toString().equals(venueSeatTemplate.getId().toString())))) {
//                    		if(null!=activity.getVenueSeatTemplate()) {
//                    			for(CultureCloudSeatAccountEntity venueSeat : activity.getSeatAccountList()) {
//                    				venueSeat.setActivity(null);
//                    				venueSeat.setMember(null);
//                    				venueSeat.setVenueSeat(null);
//                    				cultureCloudSeatAccountDao.delete(venueSeat);
//                    			}
//                    		}
//                            activity.setVenueSeatTemplate(venueSeatTemplate);
//                            for(CultureCloudVenueSeatEntity venueSeat : venueSeatTemplate.getVenueSeatList()) {
//                            	CultureCloudSeatAccountEntity seatAccount = new CultureCloudSeatAccountEntity();
//                            	seatAccount.setActivity(activity);
//                            	seatAccount.setState(EntityContext.RECORD_STATE_VALID);
//                            	seatAccount.setSeatArea(venueSeat.getSeatArea());
//                            	seatAccount.setSeatCode(venueSeat.getSeatCode());
//                            	seatAccount.setSeatColumn(venueSeat.getSeatColumn());
//                            	seatAccount.setSeatRow(venueSeat.getSeatRow());
//                            	seatAccount.setSeatStatus(venueSeat.getSeatStatus());
//                            	seatAccount.setVenueSeat(venueSeat);
//                            	cultureCloudSeatAccountDao.save(seatAccount);
//                            }
//                    	}
//                    }
    				
       				if (null!=req.getSeatIdList() && req.getSeatIdList().size()>0 && StringUtils.isBlank(req.getEventDate())) {
       					//activity.getSeatList().clear();
      					StringBuilder hql = new StringBuilder("select distinct a from CultureCloudVenueSeatEntity a  "
       							+ " inner join a.activity b");
       					hql.append(" where  b.id="+activity.getId());
       					List<CultureCloudVenueSeatEntity> vsList = cultureCloudVenueSeatDao.getListByHql(hql.toString(), "");
       					for(CultureCloudVenueSeatEntity venueSeat : vsList) {
       						venueSeat.setVenueSeatTemplate(null);
       						venueSeat.setActivity(null);
       						venueSeat.setActivityEvent(null);
       						venueSeat.setMember(null);
       						cultureCloudVenueSeatDao.delete(venueSeat);
       					}
       					List<String> seatId_seatStatus_List = req.getSeatIdList().get(0);
       					boolean setVenueSeatTemplateState=true;
    					for(String ids : seatId_seatStatus_List) {
        					String[] seat_seatStatus_Ids = ids.split(",");
        					for (String seatId_seatStatus : seat_seatStatus_Ids) {
        						String[] ss = seatId_seatStatus.split("_");
        						if(null!=ss && ss.length==2) {
        							String id = ss[0];
        							String seatStatus = ss[1];
        							CultureCloudVenueSeatEntity seat = cultureCloudVenueSeatDao.get(Integer.valueOf(id));
        							if(null!=seat) {
        								if(setVenueSeatTemplateState) {
        									activity.setVenueSeatTemplate(seat.getVenueSeatTemplate());
        									setVenueSeatTemplateState = false;
        								}
            							CultureCloudVenueSeatEntity venueSeat = new CultureCloudVenueSeatEntity();
            							venueSeat.setLastModifyTime(new Timestamp(new Date().getTime()));
            							venueSeat.setSeatArea(seat.getSeatArea());
            							venueSeat.setSeatCode(seat.getSeatCode());
            							venueSeat.setSeatColumn(seat.getSeatColumn());
            							venueSeat.setSeatRow(seat.getSeatRow());
            							venueSeat.setSeatStatus(Integer.valueOf(seatStatus));
            							venueSeat.setSeatVal(seat.getSeatVal());
            							venueSeat.setState(EntityContext.RECORD_STATE_VALID);
            							venueSeat.setVenueSeatTemplate(seat.getVenueSeatTemplate());
            							venueSeat.setActivity(activity);
            							cultureCloudVenueSeatDao.save(venueSeat);
        							}
        						}
        					}
    					}
    				}
    				
                    
                } else if (activityIsReservation == 3) {    //自由入座
                    activity.setActivityIsReservation(2);
                    activity.setActivitySalesOnline("N");
                } else if (activityIsReservation == 4) {        //直接前往
                    activity.setActivityIsReservation(1);
                    activity.setActivitySalesOnline("N");
                    activity.setActivitySupplementType(2);
                } else if (activityIsReservation == 5) {        //电话预约
                    activity.setActivityIsReservation(1);
                    activity.setActivitySalesOnline("N");
                    activity.setActivitySupplementType(3);
                } else if (activityIsReservation == 6) {        //拼手气
                    activity.setActivityIsReservation(2);
                    activity.setActivitySalesOnline("N");
                    activity.setActivitySupplementType(4);
                }

//                //存在订单时该值不能修改
//                if (!hadOrder) {
//
//                    cur.setEventCount(cur.getActivityReservationCount());
//                } else {
//                    cur.setActivityReservationCount(activity.getActivityReservationCount());
//
//                }

                activity.setActivityName(StringUtils.isNotEmpty(req.getActivityName()) ? req.getActivityName() : activity.getActivityName());
                activity.setActivityIconUrl(StringUtils.isNotEmpty(req.getActivityIconUrl()) ? req.getActivityIconUrl() : activity.getActivityIconUrl());
                activity.setActivityMemo(StringUtils.isNotEmpty(req.getActivityMemo()) ? req.getActivityMemo() : activity.getActivityMemo());
                activity.setActivityLon(null != req.getActivityLon()? new BigDecimal(req.getActivityLon()):activity.getActivityLon());
                activity.setActivityLat(null != req.getActivityLat()? new BigDecimal(req.getActivityLat()):activity.getActivityLat());
                activity.setActivityProvince(StringUtils.isNotEmpty(req.getActivityProvince()) ? req.getActivityProvince() : activity.getActivityProvince());
                activity.setActivityAddress(StringUtils.isNotEmpty(req.getActivityAddress()) ? req.getActivityAddress() : activity.getActivityAddress());
                activity.setActivityTime(StringUtils.isNotEmpty(req.getActivityTime()) ? req.getActivityTime() : activity.getActivityTime());
                activity.setActivityType(StringUtils.isNotEmpty(req.getActivityType()) ? req.getActivityType() : activity.getActivityType());
                activity.setActivityLiaison(StringUtils.isNotEmpty(req.getActivityLiaison()) ? req.getActivityLiaison() : activity.getActivityLiaison());
                activity.setActivityWebSite(StringUtils.isNotEmpty(req.getActivityWebSite()) ? req.getActivityWebSite() : activity.getActivityWebSite());
                activity.setActivityMail(StringUtils.isNotEmpty(req.getActivityMail()) ? req.getActivityMail() : activity.getActivityMail());
                activity.setActivityTel(StringUtils.isNotEmpty(req.getActivityTel()) ? req.getActivityTel() : activity.getActivityTel());
                activity.setActivityMobile(StringUtils.isNotEmpty(req.getActivityMobile()) ? req.getActivityMobile() : activity.getActivityMobile());
                activity.setActivityIsFree(null != req.getActivityIsFree() ? req.getActivityIsFree() : activity.getActivityIsFree());
                activity.setActivityPaymentDesc(StringUtils.isNotEmpty(req.getActivityPaymentDesc()) ? req.getActivityPaymentDesc() : activity.getActivityPaymentDesc());
                activity.setActivityState(null!=req.getActivityState() ? req.getActivityState():activity.getActivityState());
                activity.setActivityReservationType(null != req.getActivityReservationType()?req.getActivityReservationType():activity.getActivityReservationType());
                activity.setActivityReservationCount(null != req.getActivityReservationCount()?req.getActivityReservationCount():activity.getActivityReservationCount());
                activity.setActivityStartTime(StringUtils.isNotEmpty(req.getActivityStartTime()) ? req.getActivityStartTime() : activity.getActivityStartTime());
                activity.setActivityEndTime(StringUtils.isNotEmpty(req.getActivityEndTime()) ? req.getActivityEndTime() : activity.getActivityEndTime());
                activity.setActivityNotice(StringUtils.isNotEmpty(req.getActivityNotice()) ? req.getActivityNotice() : activity.getActivityNotice());
                activity.setActivityContent(StringUtils.isNotEmpty(req.getActivityContent()) ? req.getActivityContent() : activity.getActivityContent());
                activity.setActivitySysUrl(StringUtils.isNotEmpty(req.getActivitySysUrl()) ? req.getActivitySysUrl() : activity.getActivitySysUrl());
                activity.setActivityEventCode(null != req.getActivityEventCode() ? req.getActivityEventCode() : activity.getActivityEventCode());
                activity.setCustomizeMethod(null != req.getCustomizeMethod()?req.getCustomizeMethod():activity.getCustomizeMethod());
                activity.setActivityHost(StringUtils.isNotEmpty(req.getActivityHost()) ? req.getActivityHost() : activity.getActivityHost());
                activity.setActivityOrganizer(StringUtils.isNotEmpty(req.getActivityOrganizer()) ? req.getActivityOrganizer() : activity.getActivityOrganizer());
                activity.setActivityCoorganizer(StringUtils.isNotEmpty(req.getActivityCoorganizer()) ? req.getActivityCoorganizer() : activity.getActivityCoorganizer());
                activity.setActivityPerformed(StringUtils.isNotEmpty(req.getActivityPerformed()) ? req.getActivityPerformed() : activity.getActivityPerformed());
                activity.setActivityPrompt(StringUtils.isNotEmpty(req.getActivityPrompt()) ? req.getActivityPrompt() : activity.getActivityPrompt());
                activity.setActivitySpeaker(StringUtils.isNotEmpty(req.getActivitySpeaker()) ? req.getActivitySpeaker() : activity.getActivitySpeaker());
                activity.setActivityAttachment(StringUtils.isNotEmpty(req.getActivityAttachment()) ? req.getActivityAttachment() : activity.getActivityAttachment());
                activity.setActivityPersonal(null != req.getActivityPersonal() ? req.getActivityPersonal() : activity.getActivityPersonal());
                activity.setActivityTicketSettings(StringUtils.isNotEmpty(req.getActivityTicketSettings()) ? req.getActivityTicketSettings() : activity.getActivityTicketSettings());
                activity.setActivityTicketNumber(null != req.getActivityTicketNumber() ? req.getActivityTicketNumber() : activity.getActivityTicketNumber());
                activity.setActivityTicketCount(null != req.getActivityTicketCount() ? req.getActivityTicketCount() : activity.getActivityTicketCount());
                activity.setActivityIdentityCard(null !=req.getActivityIdentityCard() ? req.getActivityIdentityCard() : activity.getActivityIdentityCard());
                activity.setActivityRecommend(null != req.getActivityRecommend() ? req.getActivityRecommend() : activity.getActivityRecommend());
//                activity.setActivitySmsType(0);
//                activity.setActivityDeptLabel("");
                activity.setActivityPriceNotice(StringUtils.isNotEmpty(req.getActivityPriceNotice()) ? req.getActivityPriceNotice() : activity.getActivityPriceNotice());
                activity.setActivitySort(null != req.getActivitySort()?req.getActivitySort():activity.getActivitySort());
//                activity.setActivityIsTop(0);
//                activity.setEventDate("");
                activity.setAvailableCount(StringUtils.isNotEmpty(req.getAvailableCount())?req.getAvailableCount():activity.getAvailableCount());
                activity.setEventTime(StringUtils.isNoneEmpty(req.getEventTime())?req.getEventTime():activity.getEventTime());
                activity.setSignStartTime(StringUtils.isNoneEmpty(req.getSignStartTime())?
                        new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(req.getSignStartTime()).getTime()):activity.getSignStartTime());
                activity.setSignEndTime(StringUtils.isNoneEmpty(req.getSignEndTime())?
                        new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(req.getSignEndTime()).getTime()):
                activity.getSignEndTime());
                activity.setMaxAge(StringUtils.isNotEmpty(req.getMaxAge())?req.getMaxAge():activity.getMaxAge());
                activity.setMinAge(StringUtils.isNotEmpty(req.getMinAge())?req.getMinAge():activity.getMinAge());
                activity.setActivityTimeDes(StringUtils.isNotEmpty(req.getActivityTimeDes())?req.getActivityTimeDes():activity.getActivityTimeDes());
                activity.setActivitySite(StringUtils.isNotEmpty(req.getActivitySite())?req.getActivitySite():activity.getActivitySite());
//                activity.setKeyword(StringUtils.isNotEmpty(req.getKeyword())?req.getKeyword():activity.getKeyword());
                activity.setSeatIds("");
                activity.setActivityProfile(StringUtils.isNotEmpty(req.getActivityProfile())?req.getActivityProfile():activity.getActivityProfile());
                activity.setFixedTel(StringUtils.isNotEmpty(req.getFixedTel())?req.getFixedTel():activity.getFixedTel());
                activity.setResourceImgUrl(StringUtils.isNotEmpty(req.getResourceImgUrl())?req.getResourceImgUrl():activity.getResourceImgUrl());
                activity.setResourceVideoUrl(StringUtils.isNotEmpty(req.getResourceVideoUrl())?req.getResourceVideoUrl():activity.getResourceVideoUrl());
                activity.setActivityPublishFrom(StringUtils.isNotEmpty(req.getActivityPublishFrom())?req.getActivityPublishFrom():activity.getActivityPublishFrom());
                activity.setSpikeTimes(StringUtils.isNotEmpty(req.getSpikeTimes())?req.getSpikeTimes():"");
//                activity.setOrderPayStatus((short) 0);
//                activity.setActivityOrderId("");
//                activity.setShowOffline("");
                activity.setSpikeType(null != req.getSpikeType()?req.getSpikeType():0);
//                activity.setOrderPrice();
                activity.setSingleEvent(null != req.getSingleEvent()?req.getSingleEvent():activity.getSingleEvent());
//                activity.setActivityIsDetails(0);
//                activity.setActivityIsRecommend(0);
//                activity.setActivityArtType("");
//                activity.setSignStartTime(new Timestamp(System.currentTimeMillis()));
//                activity.setSignEndTime(new Timestamp(System.currentTimeMillis()));
//                activity.setEventDateTimes("");
                activity.setActivityArea(StringUtils.isNotEmpty(req.getActivityArea())?req.getActivityArea():activity.getActivityArea());
                activity.setActivityTown(StringUtils.isNotEmpty(req.getActivityTown())?req.getActivityTown():activity.getActivityTown());
                activity.setActivityVillage(StringUtils.isNotEmpty(req.getActivityVillage())?req.getActivityVillage():activity.getActivityVillage());
                activity.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
                activity.setState(1);
                activity.setCancelTime(null != req.getCancelTime()?req.getCancelTime():activity.getCancelTime());
                activity.setCancelEndTime(null != req.getCancelEndTime()?req.getCancelEndTime():activity.getCancelEndTime());
                activity.setWrittenOffTime(null != req.getWrittenOffTime()?req.getWrittenOffTime():activity.getWrittenOffTime());
                activity.setBeforeStartMinute(null != req.getBeforeStartMinute()?req.getBeforeStartMinute():activity.getBeforeStartMinute());
                activity.setAfterStartMinute(null != req.getAfterStartMinute()?req.getAfterStartMinute():activity.getAfterStartMinute());
                activity.setDetailTimeLimitMinutes(null != req.getDetailTimeLimitMinutes()?req.getDetailTimeLimitMinutes():activity.getDetailTimeLimitMinutes());
                activity.setTimeLimitOfPickingUpTickets(null != req.getTimeLimitOfPickingUpTickets()?req.getTimeLimitOfPickingUpTickets():
                        activity.getTimeLimitOfPickingUpTickets());
//                activity.setActivitySmsInfo(StringUtils.isNotEmpty(req.getActivitySmsInfo())?req.getActivitySmsInfo():null);
                activity.setActivitySmsType(null != req.getActivitySmsType()?req.getActivitySmsType():activity.getActivitySmsType());
                
                activity.setDepartment(null!=req.getDepartmentId() ? cultureCloudDepartmentDao.get(req.getDepartmentId()) :  activity.getDepartment());
                List<CultureCloudTagEntity> tagList = new ArrayList<CultureCloudTagEntity>();
    			if(StringUtils.isNoneEmpty(req.getKeywords())) {
    				String[] keywords = req.getKeywords().split(",");
    				for (String keyword : keywords) {
    					CultureCloudTagEntity cultureCloudTag = cultureCloudTagDao.getUniqueByHql(
    							"from CultureCloudTagEntity a where a.category=1 and a.displayType=2 and a.tagName='"+keyword+"'", "");
    					if(null == cultureCloudTag) {
    						cultureCloudTag = new CultureCloudTagEntity();
    						cultureCloudTag.setTagName(keyword);
    						cultureCloudTag.setCategory(1);
    						cultureCloudTag.setDisplayType(2);
    						cultureCloudTagDao.save(cultureCloudTag);
    					}
    					tagList.add(cultureCloudTag);
    				}
    			}else {
					List<CultureCloudTagEntity> cultureCloudTagList = cultureCloudTagDao.getListByHql(
							"from CultureCloudTagEntity a inner join a.assetList b  where a.category=1 and"
							+ " a.displayType=2 and b.id='"+activity.getId()+"'", "");
					for(CultureCloudTagEntity o : cultureCloudTagList) {
						tagList.add(o);
					}
				}
				if (StringUtils.isNotEmpty(req.getTagIdList())) {
					String[] tagIds = req.getTagIdList().split(",");
					for (String tageId : tagIds) {
						tagList.add(cultureCloudTagDao.get(Integer.parseInt(tageId)));
					}
				}else {
					List<CultureCloudTagEntity> cultureCloudTagList = cultureCloudTagDao.getListByHql(
							"from CultureCloudTagEntity a inner join a.assetList b  where a.category=0 and"
							+ " a.displayType=2 and b.id='"+activity.getId()+"'", "");
					for(CultureCloudTagEntity o : cultureCloudTagList) {
						tagList.add(o);
					}
				}
				
				if (StringUtils.isNotEmpty(req.getTypeIdList())) {
					String[] typeIds = req.getTypeIdList().split(",");
					for (String typeId : typeIds) {
						tagList.add(cultureCloudTagDao.get(Integer.parseInt(typeId)));
					}
				}else {
					List<CultureCloudTagEntity> cultureCloudTagList = cultureCloudTagDao.getListByHql(
							"from CultureCloudTagEntity a inner join a.assetList b  where a.category=2 and"
							+ " a.displayType=2 and b.id='"+activity.getId()+"'", "");
					for(CultureCloudTagEntity o : cultureCloudTagList) {
						tagList.add(o);
					}
				}
				if (StringUtils.isNotEmpty(req.getArtTypeIdList())) {
					String[] artTypeIds = req.getArtTypeIdList().split(",");
					for (String artTypeId : artTypeIds) {
						tagList.add(cultureCloudTagDao.get(Integer.parseInt(artTypeId)));
					}
				}else {
					List<CultureCloudTagEntity> cultureCloudTagList = cultureCloudTagDao.getListByHql(
							"from CultureCloudTagEntity a inner join a.assetList b  where a.category=3 and"
							+ " a.displayType=2 and b.id='"+activity.getId()+"'", "");
					for(CultureCloudTagEntity o : cultureCloudTagList) {
						tagList.add(o);
					}
				}
				
				activity.getTagList().clear();
				activity.setTagList(tagList);


                if (StringUtils.isNotEmpty(req.getAddressId())) {
                	CultureCloudAddressEntity address = cultureCloudAddressDao.get(Integer.parseInt(req.getAddressId()));
                    if(null != address) {
                    	req.setActivityLon(StringUtils.isNotEmpty(address.getLongitude()) ? 
                    			Double.valueOf(address.getLongitude()) :  Double.valueOf(0));
                    	req.setActivityLat(StringUtils.isNotEmpty(address.getLatitude()) ? 
                    			Double.valueOf(address.getLatitude()):Double.valueOf("0"));
                    }else {
                    	req.setActivityLon(Double.valueOf(0));
                    	req.setActivityLat(Double.valueOf("0"));
                    }
                    
                } else {
                	req.setActivityLon(Double.valueOf(0));
                	req.setActivityLat(Double.valueOf("0"));
                }
                
                if (activity != null && StringUtils.isNotBlank(activity.getActivityName())
                        && StringUtils.isNotBlank(req.getActivityName()) && !activity.getActivityName().trim().equals(req.getActivityName().trim()))
                    //验证活动名称
                {
                    if (StringUtils.isNotBlank(req.getActivityName())) {
                        boolean exists = cultureCloudActivityDao.countByHql("select distinct a from CultureCloudActivityEntity a where a" +
                                        ".activityName = '",
                                req.getActivityName().trim() + "'") > 1;
                        if (exists) {
                            //重复状态的res
                            return res;
                        }
                    }
                }
            }
            if (user != null && null != user.getId()) {
//                activity.setActivityUpdateUser(user.getUserId());
                activity.setActivityUpdateTime(new Timestamp(new Date().getTime()));
            }

            //删除活动场馆原有关联关系
//            relevanceMapper.deleteActivityVenueRelevance(req.getActivityId());
            activity.getVenueList().clear();
            //保存活动场馆新的关联关系
            if (null != req.getVenueId() && req.getCreateActivityCode()!=null && req.getCreateActivityCode() == 0) {
                activity.getVenueList().add(cultureCloudVenueDao.get(req.getVenueId()));
//                CmsActivityVenueRelevance relevance = new CmsActivityVenueRelevance();
//                relevance.setActivityId(req.getActivityId());
//                relevance.setVenueId(req.getVenueId());
                //room也要 先注释
//                relevance.setRoomId(req.getVenueRoomId());
//                relevanceMapper.addActivityVenueRelevance(relevance);
            }
            if (!hadOrder) {
                //cmsActivityEventService.deleteEventInfoByActivityId(cur.getActivityId());
//                cmsActivitySeatMapper.deleteByActivityId(cur.getActivityId());

                List<CultureCloudActivityEventEntity>oldEventList = activity.getEventList();
                oldEventList.forEach(event -> {
                    event.setState(0);
                    cultureCloudActivityEventDao.update(event);
                });
                activity.getEventList().clear();
                cultureCloudActivityDao.update(activity);
                String eventDate[] = null;
                if (StringUtils.isNotBlank(req.getEventDate())){
                    eventDate = req.getEventDate().split(",");
                }
                String eventTimes[] = null;
                if (StringUtils.isNotBlank(req.getEventTime())){
                    eventTimes = req.getEventTime().split(",");
                }
                String eventIds[] = null;
                if (StringUtils.isNotBlank(req.getEventIds())) {
                    eventIds = req.getEventIds().split(",");
                }
                String spikeTimes[] = null;
                if (StringUtils.isNotBlank(req.getSpikeTimes())) {
                    spikeTimes = req.getSpikeTimes().split(",");
                }
                String spikeEndTimes[] = null;
                if (StringUtils.isNotBlank(req.getSpikeEndTimes())) {
                    spikeEndTimes = req.getSpikeEndTimes().split(",", -1);
                }
                String availableCount[] = null;
                if (StringUtils.isNotBlank(req.getAvailableCount())) {
                    availableCount = req.getAvailableCount().split(",");
                }
                String orderPrice[] = null;
                if (StringUtils.isNotBlank(req.getOrderPrice())){
                    orderPrice = req.getOrderPrice().split(",");
                }
                String seatId[] = null;
                if (StringUtils.isNotBlank(req.getSeatIds())) {
                    seatId = req.getSeatIds().split(";");
                }

                if (req.getActivityIsReservation() != null && req.getActivityIsReservation() != 1) {
                    cultureCloudActivityDao.saveOrUpdate(activity);
                    if(eventDate !=null){
                        if (null!=req.getSeatIdList() && req.getSeatIdList().size()>0) {
                           // activity.getSeatList().clear();
                            StringBuilder hql = new StringBuilder("select distinct a from CultureCloudVenueSeatEntity a  "
                                    + " inner join a.activity b");
                            hql.append(" where  b.id="+activity.getId());
                            List<CultureCloudVenueSeatEntity> vsList = cultureCloudVenueSeatDao.getListByHql(hql.toString(), "");
                            for(CultureCloudVenueSeatEntity venueSeat : vsList) {
                                venueSeat.setVenueSeatTemplate(null);
                                venueSeat.setActivity(null);
                                venueSeat.setActivityEvent(null);
                                venueSeat.setMember(null);
                                cultureCloudVenueSeatDao.delete(venueSeat);
                            }
                        }
                        List<CultureCloudActivityEventEntity> list = cultureCloudActivityEventDao.getListByHql("select a from CultureCloudActivityEventEntity a where a.activity="+activity.getId(), "");
                        for(CultureCloudActivityEventEntity activityEvent : list) {
                        	activityEvent.setActivity(null);
                        	activityEvent.setVenueSeat(null);
                        	cultureCloudActivityEventDao.delete(activityEvent);
                        }

                        for (int i = 0; i < eventDate.length; i++) {
                            CultureCloudActivityEventEntity activityEvent = new CultureCloudActivityEventEntity();
//                            activityEvent.setEventId(UUIDUtils.createUUId());
                            activityEvent.setActivity(activity);
                            activityEvent.setState(EntityContext.RECORD_STATE_VALID);
//                            activityEvent.setActivityId(req.getActivityId());
                            activityEvent.setEventDate(eventDate[i]);
                            activityEvent.setSingleEvent(req.getSingleEvent());
                            if (req.getSingleEvent() == 1) {
                                activityEvent.setEventDateTime(req.getActivityEndTime() + " " + eventTimes[i]);
                                activityEvent.setEventEndDate(req.getActivityEndTime());
                            } else {
                                activityEvent.setEventDateTime(eventDate[i] + " " + eventTimes[i]);
                                activityEvent.setEventEndDate(eventDate[i]);
                            }
                            activityEvent.setEventTime(eventTimes[i]);

                            if (req.getSpikeType() != null && req.getSpikeType() != 0) {
                                activityEvent.setSpikeType(req.getSpikeType());
                                if (StringUtils.isNotBlank(req.getSpikeTimes())) {
                                    activityEvent.setSpikeTime(new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(spikeTimes[i]).getTime()));
                                }
                                if (StringUtils.isNotBlank(req.getSpikeEndTimes())) {
                                    if (spikeEndTimes != null && spikeEndTimes.length > i){
                                        String spikeEndTime = spikeEndTimes[i];
                                        if (StringUtils.isNotBlank(spikeEndTime)) {
                                            activityEvent.setSpikeEndTime(new Timestamp(new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(spikeEndTime).getTime()));
                                        }
                                    }
                                }
                            }

                            if (req.getActivityIsFree() != null && req.getActivityIsFree() == 2) {
                                if(orderPrice!=null && orderPrice.length>i){
                                    activityEvent.setOrderPrice(orderPrice[i]);
                                }
                            }
                            if(availableCount != null){
                                if (StringUtils.isNotBlank(availableCount[i])) {
                                    activityEvent.setAvailableCount(Integer.parseInt(availableCount[i]));
                                    activityEvent.setOrderCount(0);
                                    //activity.setAvailableCount(activity.getAvailableCount()+Integer.parseInt(availableCount[i]));
                                } else {
                                    activityEvent.setAvailableCount(Integer.parseInt(req.getAvailableCount()));
                                    activityEvent.setOrderCount(0);
                                }
                            }
                            if (StringUtils.isBlank(req.getEndTimePoint())) {
                                activity.setEndTimePoint(activityEvent.getEventDateTime());
                            } else if (activityEvent.getEventDateTime().compareTo(req.getEndTimePoint()) > 1) {
                                activity.setEndTimePoint(activityEvent.getEventDateTime());
                            }
                            if (req.getActivityIsFree() == 3) {
                                activityEvent.setOrderPrice(String.valueOf(req.getActivityPayPrice()));
                            }
                            cultureCloudActivityEventDao.save(activityEvent);
               				if (null!=req.getSeatIdList() && req.getSeatIdList().size()>0) {
                                //cmsActivityEventService.addActivityEvent(activityEvent);
               					boolean setVenueSeatTemplateState = true;
               					List<String> seatId_seatStatus_List = req.getSeatIdList().get(i);
            					for(String ids : seatId_seatStatus_List) {
                					String[] seat_seatStatus_Ids = ids.split(",");
                					for (String seatId_seatStatus : seat_seatStatus_Ids) {
                						String[] ss = seatId_seatStatus.split("_");
                						if(null!=ss && ss.length==2) {
                							String id = ss[0];
                							String seatStatus = ss[1];
                							CultureCloudVenueSeatEntity seat = cultureCloudVenueSeatDao.get(Integer.valueOf(id));
                							if(null!=seat) {
                								if(setVenueSeatTemplateState) {
                									activity.setVenueSeatTemplate(seat.getVenueSeatTemplate());
                									setVenueSeatTemplateState = false;
                								}
                    							CultureCloudVenueSeatEntity venueSeat = new CultureCloudVenueSeatEntity();
                    							venueSeat.setLastModifyTime(new Timestamp(new Date().getTime()));
                    							venueSeat.setSeatArea(seat.getSeatArea());
                    							venueSeat.setSeatCode(seat.getSeatCode());
                    							venueSeat.setSeatColumn(seat.getSeatColumn());
                    							venueSeat.setSeatRow(seat.getSeatRow());
                    							venueSeat.setSeatStatus(Integer.valueOf(seatStatus));
                    							venueSeat.setSeatVal(seat.getSeatVal());
                    							venueSeat.setState(EntityContext.RECORD_STATE_VALID);
                    							venueSeat.setVenueSeatTemplate(seat.getVenueSeatTemplate());
                    							venueSeat.setActivityEvent(activityEvent);
                    							venueSeat.setActivity(activity);
                    							cultureCloudVenueSeatDao.save(venueSeat);
                							}
                						}
                					}
            					}
            				}

                            activity.getEventList().add(activityEvent);
                            if (req.getSingleEvent() != null && req.getSingleEvent() == 0) {
                                if (StringUtils.isNotBlank(req.getSeatIds())) {
                                    activityEvent.setSeatIds(seatId[i]);
                                    if (eventIds != null && i < eventIds.length) {
                                        //cmsActivitySeatService.deleteByEventId(eventIds[i]);
                                    }
                                    //cmsActivitySeatService.addEventSeatInfo(activityEvent, sysUser);
                                } else if (eventIds != null && i < eventIds.length) {
                                    Map map = new HashMap();
                                    map.put("activityId", eventIds[i]);
                                    map.put("eventId", activityEvent.getEventUUID());
                                    // cmsActivitySeatMapper.editEventSeat(map);
                                }
                            } else {
                                if (i == 0 && StringUtils.isNotBlank(req.getSeatIds())) {
                                    activityEvent.setSeatIds(seatId[i]);
                                    if (eventIds != null) {
                                        //cmsActivitySeatService.deleteByEventId(eventIds[0]);
                                    }
                                    //cmsActivitySeatService.addEventSeatInfo(activityEvent, sysUser);
                                } else if (i == 0 && eventIds != null && i < eventIds.length) {
                                    Map map = new HashMap();
                                    map.put("activityId", eventIds[i]);
                                    map.put("eventId", activityEvent.getEventUUID());
                                    //cmsActivitySeatMapper.editEventSeat(map);
                                }
                            }
                        }
                    }

                } else {
                    CultureCloudActivityEventEntity activityEvent = new CultureCloudActivityEventEntity();
//                    activityEvent.setEventId(UUIDUtils.createUUId());
//                    activityEvent.setActivityId(req.getActivityId());
                    cultureCloudActivityDao.saveOrUpdate(activity);
                    activityEvent.setState(EntityContext.RECORD_STATE_VALID);
                    activityEvent.setActivity(activity);
                    activityEvent.setEventDate(req.getActivityStartTime());
                    activityEvent.setEventTime(req.getActivityTime());
                    activityEvent.setEventEndDate(req.getActivityEndTime());
                    String eventDateTime = "";
                    if (StringUtils.isNotBlank(req.getActivityEndTime())){
                        eventDateTime += req.getActivityEndTime() + " ";
                    }
                    if (StringUtils.isNotBlank(req.getActivityTime())){
                        eventDateTime += req.getActivityTime();
                    }
                    activityEvent.setEventDateTime(eventDateTime);
                    if (req.getActivityPayPrice() != null){
                        activityEvent.setOrderPrice(String.valueOf(req.getActivityPayPrice()));
                    }
                    activity.setEndTimePoint(activityEvent.getEventDateTime());
                    //cmsActivityEventService.addActivityEvent(activityEvent);
                    cultureCloudActivityEventDao.save(activityEvent);
                    activity.getEventList().add(activityEvent);
                }
            }

            //判断中英文 先注释
//            boolean chinese = CommonUtil.isContainChinese(req.getActivityName());
//            if (!chinese){
//                // 先删除
//                Map<String,Object> params = new HashMap<String,Object>();
//                params.put("activityId", req.getActivityId());
//                cmsActivityLanguageMapper.delete(params);
//
//                // 再添加
//                CmsActivityLanguage language = new CmsActivityLanguage();
//                language.setId(UUIDUtils.createUUId());
//                language.setActivityId(req.getActivityId());
//                cmsActivityLanguageMapper.insert(language);
//            } else {
//                Map<String,Object> params = new HashMap<String,Object>();
//                params.put("activityId", req.getActivityId());
//                cmsActivityLanguageMapper.delete(params);
//            }

//            if (StringUtils.isBlank(req.getKeyword())){
//                activity.setKeyword("");
//            }

                if(activity.getNeedSync() == 1){
                    activity.setHasSynced(2);
                }

                cultureCloudActivityDao.update(activity);

                //保存管理员操作日志 先注释
//                CmsOperationLogVO cmsOperationLogVO = new CmsOperationLogVO();
//                cmsOperationLogVO.setShopPath(shopPath);
//                cmsOperationLogVO.setCreateUser(sysUser.getUserId());
//                cmsOperationLogVO.setEnumName("ACTIVITY_EDIT");
//                gatewayCallService.saveOperationLog(cmsOperationLogVO);

        } catch (Exception e) {
            e.printStackTrace();
        }

        //this.appActivitySetRedis();
        //this.appActivityListSetRedis();
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        return res;
    }


    private  ActivityVo getCultureCloudActivityEventEntityList(ActivityReq cur,  ActivityVo cmsActivityVO, UserEntity sysUser) throws Exception {

        List<CultureCloudActivitySeatForm> cmsActivitySeat = new ArrayList<CultureCloudActivitySeatForm>();

        cmsActivityVO.setActivitySeatList(cmsActivitySeat);
        //同步场次
        List<ActivityEventVo> list = new ArrayList<ActivityEventVo>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");

        //    cmsActivityEventService.deleteEventInfoByActivityId(cur.getActivityId());
//      cmsActivitySeatMapper.deleteByActivityId(cur.getActivityId());
        String eventDate[] = cur.getEventDate().split(",");
        String eventTimes[] = cur.getEventTime().split(",");
        String eventIds[] = new String[]{};
        if (StringUtils.isNotBlank(cur.getEventIds())) {
            eventIds = cur.getEventIds().split(",");
        }
        String spikeTimes[] = null;
        if (StringUtils.isNotBlank(cur.getSpikeTimes())) {
            spikeTimes = cur.getSpikeTimes().split(",");
        }
        String spikeEndTimes[] = null;
        if (StringUtils.isNotBlank(cur.getSpikeEndTimes())) {
            spikeEndTimes = cur.getSpikeEndTimes().split(",");
        }
        String availableCount[] = null;
        if (StringUtils.isNotBlank(cur.getAvailableCount())) {
            availableCount = cur.getAvailableCount().split(",");
        }
        String orderPrice[] = cur.getOrderPrice().split(",");
        String seatId[] = null;
        if (StringUtils.isNotBlank(cur.getSeatIds())) {
            seatId = cur.getSeatIds().split(";");
        }

        Integer[] activitySecondFloors = cur.getActivitySecondFloors();

        if (cur.getActivityIsReservation() != 1) {
            for (int i = 0; i < eventDate.length; i++) {
                CultureCloudActivityEventEntity activityEvent = new CultureCloudActivityEventEntity();
//                activityEvent.setEventId(UUIDUtils.createUUId());
                activityEvent.setState(EntityContext.RECORD_STATE_VALID);
                activityEvent.setActivity(cultureCloudActivityDao.get(cmsActivityVO.getActivityId()));
                activityEvent.setEventDate(eventDate[i]);
                activityEvent.setSingleEvent(cur.getSingleEvent());
                if (cur.getSingleEvent() == 1) {
                    activityEvent.setEventDateTime(cur.getActivityEndTime() + " " + eventTimes[i]);
                    activityEvent.setEventEndDate(cur.getActivityEndTime());
                } else {
                    activityEvent.setEventDateTime(eventDate[i] + " " + eventTimes[i]);
                    activityEvent.setEventEndDate(eventDate[i]);
                }
                activityEvent.setEventTime(eventTimes[i]);

                if (cur.getSpikeType() != null && cur.getSpikeType() != 0) {
                    activityEvent.setSpikeType(cur.getSpikeType());
                    if (StringUtils.isNotBlank(cur.getSpikeTimes())) {
                        try {
                            activityEvent.setSpikeTime(new Timestamp(sdf.parse(spikeTimes[i]).getTime()));
                        } catch (ParseException e) {
                        }
                    }

                    if (StringUtils.isNotBlank(cur.getSpikeEndTimes())) {
                        if (i<spikeEndTimes.length){
                            String spikeEndTime = spikeEndTimes[i];
                            if (StringUtils.isNotBlank(spikeEndTime)) {
                                try {
                                    activityEvent.setSpikeEndTime(new Timestamp(sdf.parse(spikeEndTime).getTime()));
                                } catch (ParseException e) {
                                }
                            }
                        }
                    }
                }

                if (cur.getActivityIsFree() == 2) {
                    activityEvent.setOrderPrice(orderPrice[i]);
                }
                if(availableCount != null) {
                    if (StringUtils.isNotBlank(availableCount[i])) {
                        activityEvent.setAvailableCount(Integer.parseInt(availableCount[i]));
                    } else {
                        activityEvent.setAvailableCount(Integer.parseInt(cur.getAvailableCount()));
                    }
                }
                if (StringUtils.isBlank(cur.getEndTimePoint())) {
                    cur.setEndTimePoint(activityEvent.getEventDateTime());
                } else if (activityEvent.getEventDateTime().compareTo(cur.getEndTimePoint()) > 1) {
                    cur.setEndTimePoint(activityEvent.getEventDateTime());
                }
                if (cur.getActivityIsFree() == 3) {
                    activityEvent.setOrderPrice(String.valueOf(cur.getActivityPayPrice()));
                }

                if(activitySecondFloors!= null && activitySecondFloors.length>0)
                    activityEvent.setActivitySecondFloor(activitySecondFloors[i]);

                //  cmsActivityEventService.addActivityEvent(activityEvent);
                if (cur.getSingleEvent() == 0) {
                    if (StringUtils.isNotBlank(cur.getSeatIds())) {
                        List<CultureCloudActivitySeatForm> list2 = new ArrayList<CultureCloudActivitySeatForm>();
                        activityEvent.setSeatIds(seatId[i]);
                        if (i < eventIds.length) {
                            //cmsActivitySeatService.deleteByEventId(eventIds[i]);
                        }
                        // cmsActivitySeatService.addEventSeatInfo(activityEvent, sysUser);

                        List<CultureCloudActivitySeatEntity> seatCondition = this.getEventSeatInfo(activityEvent, sysUser);

                        if (seatCondition != null && seatCondition.size() > 0) {
                            for (CultureCloudActivitySeatEntity cas : seatCondition) {
                                CultureCloudActivitySeatForm activitySeat = new CultureCloudActivitySeatForm();
                                copyProperties(activitySeat, cas);
                                list2.add(activitySeat);
                            }
                        }
                        cmsActivityVO.getActivitySeatList().addAll(list2);

                    } else if (i < eventIds.length) {
//                        Map map = new HashMap();
//                        map.put("eventId", eventIds[i]);
                        //  map.put("eventId", activityEvent.getEventId());
//                        List<CultureCloudActivitySeatEntity> seatlist = cmsActivitySeatMapper.queryCultureCloudActivitySeatEntityCondition(map);
                        String seatHql = "from CultureCloudActivitySeatEntity where event.id = '"+eventIds[i]+"'";
                        List<CultureCloudActivitySeatEntity> seatlist = cultureCloudActivitySeatDao.getListByHql(seatHql);
                        List<CultureCloudActivitySeatForm> list2 = new ArrayList<CultureCloudActivitySeatForm>();
                        for (CultureCloudActivitySeatEntity cas : seatlist) {
                            CultureCloudActivitySeatForm activitySeat = new CultureCloudActivitySeatForm();
                            copyProperties(activitySeat, cas);
                            activitySeat.setEventId(activityEvent.getId());
                            list2.add(activitySeat);
                        }
                        cmsActivityVO.getActivitySeatList().addAll(list2);
                    }
                } else {
                    if (i == 0 && StringUtils.isNotBlank(cur.getSeatIds())) {
                        activityEvent.setSeatIds(seatId[i]);
                        // cmsActivitySeatService.deleteByEventId(eventIds[0]);
                        // cmsActivitySeatService.addEventSeatInfo(activityEvent, sysUser);
                        List<CultureCloudActivitySeatForm> list2 = new ArrayList<CultureCloudActivitySeatForm>();
                        List<CultureCloudActivitySeatEntity> seatCondition = this.getEventSeatInfo(activityEvent, sysUser);

                        if (seatCondition != null && seatCondition.size() > 0) {
                            for (CultureCloudActivitySeatEntity cas : seatCondition) {
                                CultureCloudActivitySeatForm activitySeat = new CultureCloudActivitySeatForm();
                                copyProperties(activitySeat, cas);
                                list2.add(activitySeat);
                            }
                        }
                        cmsActivityVO.getActivitySeatList().addAll(list2);

                    } else if (i == 0 && i < eventIds.length) {
//                        Map map = new HashMap();
//                        map.put("eventId", eventIds[i]);
                        //  map.put("eventId", activityEvent.getEventId());
                        String seatHql = "from CultureCloudActivitySeatEntity where event.id = '"+eventIds[i]+"'";
                        List<CultureCloudActivitySeatEntity> seatlist = cultureCloudActivitySeatDao.getListByHql(seatHql);
                        List<CultureCloudActivitySeatForm> list2 = new ArrayList<CultureCloudActivitySeatForm>();
                        for (CultureCloudActivitySeatEntity cas : seatlist) {
                            CultureCloudActivitySeatForm activitySeat = new CultureCloudActivitySeatForm();
                            copyProperties(activitySeat, cas);
                            activitySeat.setEventId(activityEvent.getId());
                            list2.add(activitySeat);
                        }
                        cmsActivityVO.getActivitySeatList().addAll(list2);
                    }
                }

                ActivityEventVo activityEventVO = new ActivityEventVo();
                copyProperties(activityEventVO, activityEvent);
                list.add(activityEventVO);
            }
        } else {
            CultureCloudActivityEventEntity activityEvent = new CultureCloudActivityEventEntity();
//            activityEvent.setEventId(UUIDUtils.createUUId());
            activityEvent.setActivity(cultureCloudActivityDao.get(cur.getActivityId()));
            activityEvent.setState(EntityContext.RECORD_STATE_VALID);
            activityEvent.setEventDate(cur.getActivityStartTime());
            activityEvent.setEventTime(cur.getActivityTime());
            activityEvent.setEventEndDate(cur.getActivityEndTime());
            activityEvent.setEventDateTime(cur.getActivityEndTime() + " " + cur.getActivityTime());
            activityEvent.setOrderPrice(String.valueOf(cur.getActivityPayPrice()));
            activityEvent.setOrderPrice(String.valueOf(cur.getActivityPayPrice()));
            // cmsActivityEventService.addActivityEvent(activityEvent);
            ActivityEventVo activityEventVO = new ActivityEventVo();
            copyProperties(activityEventVO, activityEvent);
            activityEventVO.setActivityId(activityEvent.getActivity().getId().toString());
            list.add(activityEventVO);
        }

        cmsActivityVO.setActivityEventList(list);

        return cmsActivityVO;

    }


    public List<CultureCloudActivitySeatEntity> getEventSeatInfo(CultureCloudActivityEventEntity event, UserEntity loginUser) throws Exception {
        String[] setaIds = event.getSeatIds().split(",");
        List<CultureCloudActivitySeatEntity> seatList = new ArrayList<>();
        for (String seat : setaIds) {
            String[] seats = seat.split("-");
            Integer seatStatus = 3;

            if ("A".equals(seats[0])) {
                seatStatus = 1;
            } else if ("U".equals(seats[0])) {
                seatStatus = 2;
            } else if ("D".equals(seats[0])) {
                seatStatus = 3;
            }
            String[] row_column = seats[1].split("_");
            Integer seatRow = Integer.valueOf(row_column[0]);
            Integer seatColumn = Integer.valueOf(row_column[1]);
            String seatVal = seatRow + "_" + seats[2];
            CultureCloudActivitySeatEntity cmsActivitySeat = new CultureCloudActivitySeatEntity();
//            cmsActivitySeat.setActivitySeatId(UUIDUtils.createUUId());
            cmsActivitySeat.setActivity(event.getActivity());
            cmsActivitySeat.setEvent(event);
//            cmsActivitySeat.setActivityId(event.getActivityId());
//            cmsActivitySeat.setEventId(event.getEventId());
//            cmsActivitySeat.setTotalTicket(event.);
            cmsActivitySeat.setSeatArea("ALL");
            cmsActivitySeat.setSeatCreateTime(new Date());
            cmsActivitySeat.setSeatUpdateTime(new Date());
            cmsActivitySeat.setSeatCreateUser(loginUser.getId().toString());
            cmsActivitySeat.setSeatUpdateUser(loginUser.getId().toString());
            cmsActivitySeat.setSeatRow(seatRow);
            cmsActivitySeat.setSeatCode(seatRow.toString() + "_" + seatColumn.toString());
            cmsActivitySeat.setSeatColumn(seatColumn);
            cmsActivitySeat.setSeatStatus(seatStatus);
            cmsActivitySeat.setSeatIsSold(1);
            cmsActivitySeat.setSeatVal(seatVal);
            if (cmsActivitySeat.getSeatStatus() != 1) {
                cmsActivitySeat.setSeatIsSold(2);
            }
            seatList.add(cmsActivitySeat);
        }
        return seatList;
    }

    @Audit(operate = "修改活动同步文化广东状态")
    @Override
    public IResponse modifySyncWhgdStatus(ActivityReq req) {
        GenericResponse res = new GenericResponse();
        Object userObj = getPrincipal(true);
        UserEntity user = (UserEntity) userObj;

        if (user == null) {
            res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
            return res;
        }
        try {
            CultureCloudActivityEntity activity = cultureCloudActivityDao.get(req.getActivityId());
            activity.setNeedSync(req.getNeedSync());
            cultureCloudActivityDao.update(activity);
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("修改活动同步文化广东状态失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("修改活动同步文化广东状态失败");
        }
        return res;

    }


    public static String removerepeatedchar(String str)
    {
        Set<String> mlinkedset = new LinkedHashSet<String>();
        String[] strarray = str.split(",");
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < strarray.length; i++)
        {
            if (!mlinkedset.contains(strarray[i]))
            {
                mlinkedset.add(strarray[i]);
                sb.append(strarray[i] + ",");
            }
        }
        // System.out.println(mlinkedset);
        return sb.toString().substring(0, sb.toString().length() - 1);
    }
}
