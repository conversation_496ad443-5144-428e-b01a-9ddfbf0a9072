package com.foshan.service.cultureCloud.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.foshan.dao.generic.Page;
import com.foshan.entity.cultureCloud.*;
import com.foshan.form.cultureCloud.CultureCloudOfflineTrainingForm;
import com.foshan.form.cultureCloud.CultureCloudTagForm;
import com.foshan.form.cultureCloud.CultureCloudTypeForm;
import com.foshan.form.cultureCloud.request.CultureCloudOfflineTrainingReq;

import com.foshan.form.cultureCloud.request.GetCultureCloudOfflineTrainingInfoReq;
import com.foshan.form.cultureCloud.request.GetCultureCloudOfflineTrainingListReq;
import com.foshan.form.cultureCloud.response.cultureCloudTrain.GetCultureCloudTrainInfoRes;
import com.foshan.form.cultureCloud.response.cultureCloudTrain.GetCultureCloudTrainListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.cultureCloud.ICultureCloudOfflineTrainingService;
import com.foshan.util.cultureCloud.DateUtils;

import org.hibernate.query.Query;
import org.hibernate.transform.Transformers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;

import static java.util.stream.Collectors.groupingBy;

@Service("cultureCloudOfflineTrainingService")
@Transactional
public class CultureCloudOfflineTrainingServiceImpl extends GenericCultureCloudService implements ICultureCloudOfflineTrainingService {

    private final static Logger logger = LoggerFactory.getLogger(CultureCloudActivityEventServiceImpl.class);


    @Override
    public IResponse addOfflineTraining(CultureCloudOfflineTrainingReq req) {
        GenericResponse res = new GenericResponse();

        if (req.getForm() == null) {
            res.setRet(ResponseContext.RES_DATA_NULL_CODE);
            res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            return res;
        }

        CultureCloudOfflineTrainingEntity entity = new CultureCloudOfflineTrainingEntity();

        try {
            Object userObj = getPrincipal(true);
            if (!(userObj instanceof CultureCloudUserEntity)) {
                res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
                return res;
            }

            CultureCloudUserEntity user = (CultureCloudUserEntity) userObj;
            BeanUtils.copyProperties(req.getForm(), entity);

            entity.setCreateTime(new Timestamp(new Date().getTime()));
            entity.setIsDelete(1);
            entity.setTrainStatus((null != req.getForm().getTrainStatus() && req.getForm().getTrainStatus().equals(1) ? 1 : 0));
            entity.setCreateUser(user.getId().toString());
            entity.setUpdateTime(new Date());

            if (StringUtils.isNoneEmpty(req.getForm().getKeywords())) {
                String[] keywords = req.getForm().getKeywords().split(",");
                for (String keyword : keywords) {
                    CultureCloudTagEntity cultureCloudTag = cultureCloudTagDao.getUniqueByHql(
                            "from CultureCloudTagEntity a where a.category=1 and a.displayType=7 and a.tagName='" + keyword + "'", "");
                    if (null == cultureCloudTag) {
                        cultureCloudTag = new CultureCloudTagEntity();
                        cultureCloudTag.setTagName(keyword);
                        cultureCloudTag.setCategory(1);
                        cultureCloudTag.setDisplayType(3);
                        cultureCloudTagDao.save(cultureCloudTag);
                    }
                    entity.getTagList().add(cultureCloudTag);
                }
            }
            if (StringUtils.isNotEmpty(req.getForm().getTagIdList())) {
                String[] tagIds = req.getForm().getTagIdList().split(",");
                for (String tageId : tagIds) {
                    entity.getTagList().add(cultureCloudTagDao.get(Integer.parseInt(tageId)));
                }
            }
            if (StringUtils.isNotEmpty(req.getForm().getTypeIdList())) {
                String[] typeIds = req.getForm().getTypeIdList().split(",");
                for (String typeId : typeIds) {
                    entity.getTagList().add(cultureCloudTagDao.get(Integer.parseInt(typeId)));
                }
            }

            //设置初始审核状态
//                CmsShop shop = (CmsShop) session.getAttribute("shop");
//                String shopPath = null;
//                if (shop != null && StringUtils.isNotBlank(shop.getShopId())) {
//                    shopPath = shop.getShopPath();
//                }
//                if (StringUtils.isNotBlank(shopPath)) {
//                    Map<String, Object> params = new HashMap<String, Object>();
//                    params.put("shopPath", shopPath);
//                    params.put("switchType", ShopSwitchEnum.CMS_TRAIN.getStatus());
//                    CmsShopSwitch shopSwitch = cmsShopSwitchMapper.queryShopSwitchByShopPathAndType(params);
//                    if (shopSwitch != null) {
//                        if (shopSwitch.getNeedAudit() != null && shopSwitch.getNeedAudit() == 1) {
//                            entity.setCheckStatus(CheckStatusEnum.TO_AUDIT.getStatus());
//                        }else{
//                            entity.setCheckStatus(CheckStatusEnum.PASS_CHECK.getStatus());
//                        }
//                    }else{
//                        entity.setCheckStatus(CheckStatusEnum.PASS_CHECK.getStatus());
//                    }
//                }else{
//                    entity.setCheckStatus(CheckStatusEnum.PASS_CHECK.getStatus());
//                }
            cultureCloudOfflineTrainingDao.save(entity);


            //更新培训场次信息
            if (StringUtils.isNotBlank(req.getFieldStr())) {
                updateCmsTrainFiledList(req.getFieldStr(), entity, 1);
            } else {
                // 如果是草稿状态(trainStatus=1)且没有fieldStr，不创建场次信息
                if (entity.getTrainStatus() != null && entity.getTrainStatus().equals(1)) {
                    // 草稿状态下，不强制创建场次信息
                    logger.info("草稿状态下不强制创建场次信息，培训ID: {}", entity.getId());
                } else {
                    // 非草稿状态下，如果有培训时间信息，创建默认场次
                    if (StringUtils.isNotBlank(entity.getTrainStartTime()) && StringUtils.isNotBlank(entity.getTrainEndTime())) {
                        entity.getFieldList().clear();
                        CultureCloudTrainFieldEntity field = new CultureCloudTrainFieldEntity();
                        field.setTrain(entity);
                        try {
                            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                            field.setFieldStartTime(df.parse(entity.getTrainStartTime()));
                            field.setFieldEndTime(df.parse(entity.getTrainEndTime()));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        field.setFieldUpdateTime(new Date());
                        field.setFieldTitle(entity.getTrainTitle() + "第1课");
                        cultureCloudTrainFieldDao.save(field);
                        entity.getFieldList().add(field);
                    }
                }
            }
            cultureCloudOfflineTrainingDao.save(entity);
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

        } catch (Exception e) {
            logger.error("发布培训失败：", e);
            res.setRet(ResponseContext.RES_TRAIN_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_TRAIN_ERROR_INFO);
        }
        return res;
    }

    @Override
    public IResponse modifyOfflineTraining(CultureCloudOfflineTrainingReq req) {
        GenericResponse res = new GenericResponse();

        if (req.getForm() == null || req.getForm().getId() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("培训ID不能为空");
            return res;
        }

        try {
            Object userObj = getPrincipal(true);
            if (!(userObj instanceof CultureCloudUserEntity)) {
                res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
                return res;
            }

            CultureCloudUserEntity user = (CultureCloudUserEntity) userObj;
            CultureCloudOfflineTrainingEntity entity = cultureCloudOfflineTrainingDao.get(req.getForm().getId());
            if (entity == null) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo("培训不存在");
                return res;
            }

            entity.setUpdateUser(user.getId().toString());
            entity.setUpdateTime(new Date());
            BeanUtils.copyProperties(req.getForm(), entity);
            entity.setTrainStatus((null != req.getForm().getTrainStatus() && req.getForm().getTrainStatus().equals(1) ? 1 : 0));

            List<CultureCloudTagEntity> tagList = new ArrayList<CultureCloudTagEntity>();
            if (StringUtils.isNoneEmpty(req.getForm().getKeywords())) {
                String[] keywords = req.getForm().getKeywords().split(",");
                for (String keyword : keywords) {
                    CultureCloudTagEntity cultureCloudTag = cultureCloudTagDao.getUniqueByHql(
                            "from CultureCloudTagEntity a where a.category=1 and a.displayType=7 and a.tagName='" + keyword + "'", "");
                    if (null == cultureCloudTag) {
                        cultureCloudTag = new CultureCloudTagEntity();
                        cultureCloudTag.setTagName(keyword);
                        cultureCloudTag.setCategory(1);
                        cultureCloudTag.setDisplayType(3);
                        cultureCloudTagDao.save(cultureCloudTag);
                    }
                    tagList.add(cultureCloudTag);
                }
            } else {
                List<CultureCloudTagEntity> cultureCloudTagList = cultureCloudTagDao.getListByHql(
                        "from CultureCloudTagEntity a where a.category=1 and"
                                + " a.displayType=7 ");
                for (CultureCloudTagEntity o : cultureCloudTagList) {
                    tagList.add(o);
                }
            }
            if (StringUtils.isNotEmpty(req.getForm().getTagIdList())) {
                String[] tagIds = req.getForm().getTagIdList().split(",");
                for (String tageId : tagIds) {
                    tagList.add(cultureCloudTagDao.get(Integer.parseInt(tageId)));
                }
            } else {
                List<CultureCloudTagEntity> cultureCloudTagList = cultureCloudTagDao.getListByHql(
                        "from CultureCloudTagEntity a where a.category=0 and"
                                + " a.displayType=7 ");
                for (CultureCloudTagEntity o : cultureCloudTagList) {
                    tagList.add(o);
                }
            }

            if (StringUtils.isNotEmpty(req.getForm().getTypeIdList())) {
                String[] typeIds = req.getForm().getTypeIdList().split(",");
                for (String typeId : typeIds) {
                    tagList.add(cultureCloudTagDao.get(Integer.parseInt(typeId)));
                }
            } else {
                List<CultureCloudTagEntity> cultureCloudTagList = cultureCloudTagDao.getListByHql(
                        "from CultureCloudTagEntity a where a.category=2 and"
                                + " a.displayType=7 ");
                for (CultureCloudTagEntity o : cultureCloudTagList) {
                    tagList.add(o);
                }
            }
            entity.getTagList().clear();
            entity.setTagList(tagList);


            //更新培训场次信息
            if (StringUtils.isNotBlank(req.getFieldStr())) {
                updateCmsTrainFiledList(req.getFieldStr(), entity, 1);
            } else {
                // 如果是草稿状态(trainStatus=1)且没有fieldStr，保留现有的fieldList，不做任何处理
                if (entity.getTrainStatus() != null && entity.getTrainStatus().equals(1)) {
                    // 草稿状态下，保留现有场次信息，不清空也不重新创建
                    logger.info("草稿状态下保留现有场次信息，培训ID: {}", entity.getId());
                } else {
                    // 非草稿状态下，如果有培训时间信息，创建默认场次
                    if (StringUtils.isNotBlank(entity.getTrainStartTime()) && StringUtils.isNotBlank(entity.getTrainEndTime())) {
                        entity.getFieldList().clear();
                        CultureCloudTrainFieldEntity field = new CultureCloudTrainFieldEntity();
                        field.setTrain(entity);
                        try {
                            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                            field.setFieldStartTime(df.parse(entity.getTrainStartTime()));
                            field.setFieldEndTime(df.parse(entity.getTrainEndTime()));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        field.setFieldUpdateTime(new Date());
                        field.setFieldTitle(entity.getTrainTitle() + "第1课");
                        cultureCloudTrainFieldDao.save(field);
                        entity.getFieldList().add(field);
                    }
                }
            }
            cultureCloudOfflineTrainingDao.update(entity);

            if (entity.getIsDelete() != null && entity.getIsDelete() == 0) {
                unsubscribeTrainOrder(entity);
            }

            //设置初始审核状态
//            CmsShop shop = (CmsShop) session.getAttribute("shop");
//            String shopPath = null;
//            if (shop != null && StringUtils.isNotBlank(shop.getShopId())) {
//                shopPath = shop.getShopPath();
//            }
//            if (StringUtils.isNotBlank(shopPath)) {
//                Map<String, Object> params = new HashMap<String, Object>();
//                params.put("shopPath", shopPath);
//                params.put("switchType", ShopSwitchEnum.CMS_TRAIN.getStatus());
//                CmsShopSwitch shopSwitch = cmsShopSwitchMapper.queryShopSwitchByShopPathAndType(params);
//                if (shopSwitch != null) {
//                    if (shopSwitch.getNeedAudit() != null && shopSwitch.getNeedAudit() == 1) {
//                        entity.setCheckStatus(CheckStatusEnum.TO_AUDIT.getStatus());
//                    }else{
//                        entity.setCheckStatus(CheckStatusEnum.PASS_CHECK.getStatus());
//                    }
//                }else{
//                    entity.setCheckStatus(CheckStatusEnum.PASS_CHECK.getStatus());
//                }
//            }else{
//                entity.setCheckStatus(CheckStatusEnum.PASS_CHECK.getStatus());
//            }
            //培训修改为下架,取消所有培训订单  TODO
               /* if (entity.getTrainStatus() != null && entity.getTrainStatus() == 2) {
                    unsubscribeTrainOrder(entity);
                }*/
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("修改培训失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("修改培训失败");
        }

        return res;
    }

    @Override
    public IResponse deleteOfflineTraining(CultureCloudOfflineTrainingReq req) {
        GenericResponse res = new GenericResponse();

        if (null == req.getForm().getId()) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("培训ID不能为空");
            return res;
        }

        try {
            CultureCloudOfflineTrainingEntity entity = cultureCloudOfflineTrainingDao.get(req.getForm().getId());
            if (entity == null) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo("培训不存在");
                return res;
            }

            entity.setIsDelete(0);
            entity.setUpdateTime(new Date());
            unsubscribeTrainOrder(entity);
            cultureCloudOfflineTrainingDao.update(entity);

            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("删除培训失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("删除培训失败");
        }

        return res;
    }

    @Override
    public IResponse getOfflineTrainingInfo(GetCultureCloudOfflineTrainingInfoReq req) {
        GetCultureCloudTrainInfoRes res = new GetCultureCloudTrainInfoRes();

        if (req.getId() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("培训ID不能为空");
            return res;
        }

        String memberId = "";
        Object userObj = getPrincipal(true);
        if (null != userObj && userObj instanceof CultureCloudMemberEntity) {
            memberId = ((CultureCloudMemberEntity) userObj).getId().toString();
        }

        try {
            String hql = "SELECT t.*, v.venueName," +
//                    " ctbot.onlineTrainId, " +
                    "(SELECT COUNT(1) FROM t_culture_cloud_train_order ucto WHERE ucto.trainId = t.id AND ucto.createUser = '" + memberId + "' AND " +
                    "ucto" +
                    ".state != 2) as isRegistration, " +
                    "(SELECT COUNT(1) FROM t_culture_cloud_train_order cto1 WHERE cto1.trainId = t.id AND cto1.state = 1) as currentPeople, " +
                    "(t.maxPeople - (SELECT COUNT(1) FROM t_culture_cloud_train_order cto2 WHERE cto2.trainId = t.id AND cto2.state = 1)) as remainingBalance, " +
                    "tag.id as tagId, tag.tagName as tagName, tag.category as category, tag.displayType as displayType " +
                    "FROM t_culture_cloud_offline_training t " +
                    "LEFT JOIN t_culture_cloud_venue v ON v.id = t.venueId " +
//                    "LEFT JOIN CmsTrainBindOnlineTrain ctbot ON ctbot.trainId = t.id " +
                    "LEFT JOIN t_culture_cloud_tag tag ON tag.id IN (SELECT tagId FROM t_culture_cloud_training_tag WHERE trainingId = t.id) " +
                    "WHERE t.id = " + req.getId();

            Query query = cultureCloudOfflineTrainingDao.createSQLQuery(hql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

            if (null != query.list()) {
                CultureCloudOfflineTrainingForm form = new CultureCloudOfflineTrainingForm();

                // 设置基本信息
                form.setId(req.getId());

                // 处理标签信息
                Map<Integer, List<CultureCloudTagEntity>> tagGroups = new HashMap<>();

                // 用于存储当前报名人数和剩余名额
                Integer currentPeople = null;
                Integer remainingBalance = null;

                for (Object rowObj : query.list()) {
                    Map<String, Object> row = (Map<String, Object>) rowObj;

                    // 获取当前报名人数和剩余名额（只需要获取一次）
                    if (currentPeople == null) {
                        Object currentPeopleObj = row.get("currentPeople");
                        Object remainingBalanceObj = row.get("remainingBalance");
                        currentPeople = currentPeopleObj != null ? ((Number) currentPeopleObj).intValue() : 0;
                        remainingBalance = remainingBalanceObj != null ? ((Number) remainingBalanceObj).intValue() : 0;
                    }

                    Integer tagId = (Integer) row.get("tagId");
                    if (tagId != null) {
                        CultureCloudTagEntity tag = new CultureCloudTagEntity();
                        tag.setId(tagId);
                        tag.setTagName((String) row.get("tagName"));
                        tag.setCategory((Integer) row.get("category"));
                        tag.setDisplayType((Integer) row.get("displayType"));

                        tagGroups.computeIfAbsent(tag.getCategory(), k -> new ArrayList<>()).add(tag);
                    }
                }

                // 处理不同类型的标签
                tagGroups.forEach((key, tags) -> {
                    StringBuilder tagStr = new StringBuilder();
                    if (key == 0) { // 普通标签
                        tags.forEach(t -> tagStr.append(t.getTagName()).append(","));
                        tags.forEach(t -> {
                            CultureCloudTagForm typeForm = new CultureCloudTagForm();
                            typeForm.setCultureCloudTagId(t.getId());
                            typeForm.setTagName(t.getTagName());
                            form.getTagList().add(typeForm);
                        });
                    } else if (key == 1) { // 关键词标签
                        tags.forEach(t -> tagStr.append(t.getTagName()).append(","));
                        if (tagStr.length() > 0) {
                            form.setKeywords(tagStr.substring(0, tagStr.length() - 1));
                        }
                    } else if (key == 2) { // 类型标签
                        tags.forEach(t -> {
                            CultureCloudTypeForm typeForm = new CultureCloudTypeForm();
                            typeForm.setCultureCloudTypeId(t.getId());
                            typeForm.setCategory(t.getCategory());
                            typeForm.setTypeName(t.getTagName());
                            form.getTypeList().add(typeForm);
                        });
                    }
                });



                // 获取其他培训信息
                CultureCloudOfflineTrainingEntity entity = cultureCloudOfflineTrainingDao.get(req.getId());
                if (entity != null) {
                    BeanUtils.copyProperties(entity, form);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    entity.getFieldList().forEach(f -> {
                        Map<String, String> map = new HashMap<>();
                        map.put("fieldStartTime", sdf.format(f.getFieldStartTime()));
                        map.put("fieldEndTime", sdf.format(f.getFieldEndTime()));
                        map.put("fieldTimeStr", f.getFieldTimeStr());
                        form.getFieldList().add(map);
                    });
                    form.setUpdateTime(null != entity.getUpdateTime() ? sdf.format(entity.getUpdateTime()) : null);
                    form.setCreateTime(sdf.format(entity.getCreateTime()));
                    form.setRefundTime(null != entity.getRefundTime() ? sdf.format(entity.getRefundTime()) : null);
                }

                // 设置当前报名人数和剩余名额
                form.setCurrentPeople(currentPeople);
                form.setRemainingBalance(remainingBalance);

                res.setTrain(form);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo("培训不存在");
            }
        } catch (Exception e) {
            logger.error("获取培训详情失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("获取培训详情失败");
        }

        return res;
    }

    @Override
    public IResponse getOfflineTrainingList(GetCultureCloudOfflineTrainingListReq req) {
        GetCultureCloudTrainListRes res = new GetCultureCloudTrainListRes();

        try {
            // 构建 SQL 查询
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT t.id, t.trainTitle, t.trainImgUrl, t.trainIntroduce, t.trainStatus, t.isDelete, ");
            sql.append("t.createTime, t.createUser, t.trainProvince, t.trainCity, t.trainArea, t.trainLocation, ");
            sql.append("t.trainTown, t.trainVillage, t.trainAddress, t.venueType, t.venueId, t.trainType, ");
            sql.append("t.trainTag, t.admissionType, t.maxPeople, t.trainField, t.lon, t.lat, ");
            sql.append("t.interviewTime, t.interviewAddress, t.reminder, t.consultingPhone, t.contactInformation, ");
            sql.append("t.registrationRequirements, t.courseIntroduction, t.teachersIntroduction, ");
            sql.append("t.registrationStartTime, t.registrationEndTime, t.trainStartTime, t.trainEndTime, ");
            sql.append("t.updateUser, t.updateTime, t.courseType, t.registrationCount, t.sign, t.integralNeeded, ");
            sql.append("t.minAge, t.maxAge, t.femaleMinAge, t.femaleMaxAge, t.enclosure, t.weekStr, ");
            sql.append("t.genderRestriction, t.hasIssuingCertificate, t.isAskLeave, t.isCardNo, t.keywords, ");
            sql.append("t.website, t.mobileUrl, t.pcUrl, t.publishSource, t.realNameSystem, t.refundTime, ");
            sql.append("t.resourceVideoUrl, t.scheduleImportType, t.setRefundWay, t.sponsorUnit, t.trainPeriod, ");
            sql.append("t.venueRoomId, t.resourceImgUrl, t.linkman, ");
            sql.append("v.venueName, ");
            sql.append("(SELECT COUNT(1) FROM t_culture_cloud_train_order cto WHERE cto.trainId = t.id AND cto.state = 1) as currentPeople ");
            sql.append("FROM t_culture_cloud_offline_training t ");
            sql.append("LEFT JOIN t_culture_cloud_venue v ON v.id = t.venueId ");
            sql.append("WHERE t.isDelete = 1 ");

            // 培训标题筛选
            if (StringUtils.isNotBlank(req.getTrainTitle())) {
                sql.append("AND t.trainTitle LIKE '%" + req.getTrainTitle() + "%' ");
            }

            // 用户权限筛选
            Object userObj = getPrincipal(true);
            if (userObj instanceof CultureCloudUserEntity) {
                if (req.getTrainStatus() != null) {
                    sql.append("AND t.trainStatus = " + req.getTrainStatus() + " ");
                }
                if (StringUtils.isNotEmpty(req.getTrainStatusList())){
                    sql.append("AND t.trainStatus in (" + req.getTrainStatusList() + ") ");
                }
            } else {
                sql.append("AND t.trainStatus = 6 ");
            }

            // 培训类型筛选
            if (StringUtils.isNotBlank(req.getTrainType())) {
                sql.append("AND t.trainType = '" + req.getTrainType() + "' ");
            }

            // 培训标签筛选
            if (StringUtils.isNotBlank(req.getTrainTag())) {
                sql.append("AND t.trainTag = '" + req.getTrainTag() + "' ");
            }

            // 录取方式筛选
            if (null != req.getAdmissionType()) {
                sql.append("AND t.admissionType = " + req.getAdmissionType() + " ");
            }

            // 培训场次筛选
            if (null != req.getTrainField()) {
                sql.append("AND t.trainField = " + req.getTrainField() + " ");
            }

            // 培训区域筛选
            if (StringUtils.isNotEmpty(req.getTrainArea())) {
                sql.append("AND t.trainArea = '" + req.getTrainArea() + "' ");
            }

            // 场馆名称筛选
            if (StringUtils.isNotBlank(req.getVenueName())) {
                sql.append("AND v.venueName LIKE '%" + req.getVenueName() + "%' ");
            }

            // 报名余额筛选
            if (null != req.getIsBalance()) {
                if (req.getIsBalance() == 1) {
                    // 有余额：当前报名人数 < 最大人数
                    sql.append("AND (SELECT COUNT(1) FROM t_culture_cloud_train_order cto WHERE cto.trainId = t.id AND cto.state = 1) < t.maxPeople ");
                } else if (req.getIsBalance() == 0) {
                    // 无余额：当前报名人数 >= 最大人数
                    sql.append("AND (SELECT COUNT(1) FROM t_culture_cloud_train_order cto WHERE cto.trainId = t.id AND cto.state = 1) >= t.maxPeople ");
                }
            }

            // 报名状态筛选
            if (null != req.getRegistrationState()) {
                String currentTime = "NOW()";
                if (req.getRegistrationState() == 1) {
                    // 未开始：当前时间 < 报名开始时间
                    sql.append("AND " + currentTime + " < t.registrationStartTime ");
                } else if (req.getRegistrationState() == 2) {
                    // 报名中：报名开始时间 <= 当前时间 <= 报名结束时间
                    sql.append("AND t.registrationStartTime <= " + currentTime + " AND " + currentTime + " <= t.registrationEndTime ");
                } else if (req.getRegistrationState() == 3) {
                    // 已结束：当前时间 > 报名结束时间
                    sql.append("AND " + currentTime + " > t.registrationEndTime ");
                }
            }

            // 培训开始时间范围筛选
            if (StringUtils.isNotBlank(req.getTrainStartTimeBegin())) {
                sql.append("AND t.trainStartTime >= '" + req.getTrainStartTimeBegin() + "' ");
            }
            if (StringUtils.isNotBlank(req.getTrainStartTimeEnd())) {
                sql.append("AND t.trainStartTime <= '" + req.getTrainStartTimeEnd() + "' ");
            }

            // 培训结束时间范围筛选
            if (StringUtils.isNotBlank(req.getTrainEndTimeBegin())) {
                sql.append("AND t.trainEndTime >= '" + req.getTrainEndTimeBegin() + "' ");
            }
            if (StringUtils.isNotBlank(req.getTrainEndTimeEnd())) {
                sql.append("AND t.trainEndTime <= '" + req.getTrainEndTimeEnd() + "' ");
            }

            req.setSortType(null != req.getSortType()?req.getSortType():0);
            switch (req.getSortType()) {
                case 1:
                    sql.append("ORDER BY IF((t.registrationStartTime <= NOW() AND t.registrationEndTime >= NOW()), 0, 1), IF((t.registrationStartTime >= NOW()), 0, 1), IF((t.trainStartTime <= NOW() AND t.trainEndTime >= NOW()), 0, 1), IF((t.registrationEndTime <= NOW() AND t.trainStartTime >= NOW()), 0, 1), IF((t.registrationStartTime <= NOW() AND t.registrationEndTime >= NOW()), t.registrationEndTime, NULL), IF((t.registrationStartTime >= NOW()), t.registrationStartTime, NULL), IF((t.trainStartTime <= NOW() AND t.trainEndTime >= NOW()), t.trainEndTime, NULL), IF((t.registrationEndTime <= NOW() AND t.trainStartTime >= NOW()), t.trainStartTime, NULL) ASC, t.trainEndTime DESC");
                    break;
                    //现在没有点赞，没有人气最高筛选
//                case 2:
//                    sql.append("ORDER BY CASE WHEN t.createTime > DATE_SUB(CURDATE(), INTERVAL 6 MONTH) THEN 0 WHEN t.createTime < DATE_SUB(CURDATE(), INTERVAL 6 MONTH) THEN 1 END, CASE WHEN (CASE WHEN t.createTime > DATE_SUB(CURDATE(), INTERVAL 6 MONTH) THEN 0 WHEN t.createTime < DATE_SUB(CURDATE(), INTERVAL 6 MONTH) THEN 1 END) = 0 THEN (SELECT COUNT(d.sid) FROM cmsUserWantgo d WHERE d.relateId = t.id) END DESC, CASE WHEN (CASE WHEN t.createTime > DATE_SUB(CURDATE(), INTERVAL 6 MONTH) THEN 0 WHEN t.createTime < DATE_SUB(CURDATE(), INTERVAL 6 MONTH) THEN 1 END) = 1 THEN t.createTime END DESC, (SELECT COUNT(d.sid) FROM cmsUserWantgo d WHERE d.relateId = t.id) DESC");
//                    break;
                case 3:
                    sql.append("ORDER BY t.updateTime DESC, t.createTime DESC");
                    break;
                case 4:
                    sql.append("ORDER BY IF((t.registrationStartTime >= NOW()), 0, 1), IF((t.registrationStartTime <= NOW() AND t.registrationEndTime >= NOW()), 0, 1), IF((t.trainStartTime <= NOW() AND t.trainEndTime >= NOW()), 0, 1), IF((t.registrationEndTime <= NOW() AND t.trainStartTime >= NOW()), 0, 1), IF((t.registrationStartTime >= NOW()), t.registrationStartTime, NULL), IF((t.registrationStartTime <= NOW() AND t.registrationEndTime >= NOW()), t.registrationEndTime, NULL), IF((t.trainStartTime <= NOW() AND t.trainEndTime >= NOW()), t.trainEndTime, NULL), IF((t.registrationEndTime <= NOW() AND t.trainStartTime >= NOW()), t.trainStartTime, NULL) ASC, t.trainEndTime DESC");
                    break;
                default:
                    sql.append("ORDER BY t.createTime DESC ");
            }

            // 分页查询
            Query query = cultureCloudOfflineTrainingDao.createSQLQuery(sql.toString()).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

            // 计算分页参数
            int pageSize = null != req.getPageSize() ? req.getPageSize() : 10;
            int currentPage = null != req.getRequestPage() ? req.getRequestPage() : 1;

            Integer totalResult = query.getResultList().size();
            query.setMaxResults(null != req.getPageSize() ? req.getPageSize() : 10);
            query.setFirstResult((null != req.getRequestPage() ? req.getRequestPage() - 1 : 1 - 1) * (null != req.getPageSize() ? req.getPageSize() :
                    10));

            List<Map<String, Object>> resultList = query.getResultList();

            // 处理查询结果
            for (Object rowObj : resultList) {
                Map<String, Object> row = (Map<String, Object>) rowObj;
                CultureCloudOfflineTrainingForm form = new CultureCloudOfflineTrainingForm();

                // 映射基本字段
                form.setId((Integer) row.get("id"));
                form.setTrainTitle((String) row.get("trainTitle"));
                form.setTrainImgUrl((String) row.get("trainImgUrl"));
                form.setTrainIntroduce((String) row.get("trainIntroduce"));
                form.setTrainStatus((Integer) row.get("trainStatus"));
                form.setIsDelete((Integer) row.get("isDelete"));
                form.setCreateUser((String) row.get("createUser"));
                form.setTrainProvince((String) row.get("trainProvince"));
                form.setTrainCity((String) row.get("trainCity"));
                form.setTrainArea((String) row.get("trainArea"));
                form.setTrainLocation((String) row.get("trainLocation"));
                form.setTrainTown((String) row.get("trainTown"));
                form.setTrainVillage((String) row.get("trainVillage"));
                form.setTrainAddress((String) row.get("trainAddress"));
                form.setVenueType((String) row.get("venueType"));
                form.setVenueId((String) row.get("venueId"));
                form.setTrainType((String) row.get("trainType"));
                form.setTrainTag((String) row.get("trainTag"));
                form.setAdmissionType((Integer) row.get("admissionType"));
                form.setMaxPeople((Integer) row.get("maxPeople"));

                // 设置当前报名人数和剩余余额
                Integer currentPeople = 0;
                Object currentPeopleObj = row.get("currentPeople");
                if (currentPeopleObj instanceof Number) {
                    currentPeople = ((Number) currentPeopleObj).intValue();
                }
                form.setCurrentPeople(currentPeople);

                // 计算剩余报名余额
                Integer maxPeople = form.getMaxPeople();
                if (maxPeople != null && maxPeople > 0) {
                    Integer remainingBalance = maxPeople - currentPeople;
                    form.setRemainingBalance(Math.max(0, remainingBalance)); // 确保不为负数
                } else {
                    form.setRemainingBalance(0);
                }

                form.setTrainField((Integer) row.get("trainField"));
                // 处理经纬度字段（BigDecimal 转 Double）
                Object lonObj = row.get("lon");
                if (lonObj instanceof BigDecimal) {
                    form.setLon(((BigDecimal) lonObj).doubleValue());
                } else if (lonObj instanceof Double) {
                    form.setLon((Double) lonObj);
                } else if (lonObj != null) {
                    form.setLon(Double.valueOf(lonObj.toString()));
                }

                Object latObj = row.get("lat");
                if (latObj instanceof BigDecimal) {
                    form.setLat(((BigDecimal) latObj).doubleValue());
                } else if (latObj instanceof Double) {
                    form.setLat((Double) latObj);
                } else if (latObj != null) {
                    form.setLat(Double.valueOf(latObj.toString()));
                }
                form.setInterviewTime((String) row.get("interviewTime"));
                form.setInterviewAddress((String) row.get("interviewAddress"));
                form.setReminder((String) row.get("reminder"));
                form.setConsultingPhone((String) row.get("consultingPhone"));
                form.setContactInformation((String) row.get("contactInformation"));
                form.setRegistrationRequirements((String) row.get("registrationRequirements"));
                form.setCourseIntroduction((String) row.get("courseIntroduction"));
                form.setTeachersIntroduction((String) row.get("teachersIntroduction"));
                form.setTrainStartTime((String) row.get("trainStartTime"));
                form.setTrainEndTime((String) row.get("trainEndTime"));
                form.setUpdateUser((String) row.get("updateUser"));
                form.setCourseType((String) row.get("courseType"));
                form.setRegistrationCount((Integer) row.get("registrationCount"));
                form.setSign((Integer) row.get("sign"));
                form.setIntegralNeeded((Integer) row.get("integralNeeded"));
                form.setMinAge((Integer) row.get("minAge"));
                form.setMaxAge((Integer) row.get("maxAge"));
                form.setFemaleMinAge((Integer) row.get("femaleMinAge"));
                form.setFemaleMaxAge((Integer) row.get("femaleMaxAge"));
                form.setEnclosure((Integer) row.get("enclosure"));
                form.setWeekStr((String) row.get("weekStr"));
                form.setGenderRestriction((Integer) row.get("genderRestriction"));
                form.setHasIssuingCertificate((Integer) row.get("hasIssuingCertificate"));
                form.setIsAskLeave((Integer) row.get("isAskLeave"));
                form.setIsCardNo((Integer) row.get("isCardNo"));
                form.setKeywords((String) row.get("keywords"));
                form.setWebsite((String) row.get("website"));
                form.setMobileUrl((String) row.get("mobileUrl"));
                form.setPcUrl((String) row.get("pcUrl"));
                form.setPublishSource((String) row.get("publishSource"));
                form.setRealNameSystem((Integer) row.get("realNameSystem"));
                form.setResourceVideoUrl((String) row.get("resourceVideoUrl"));
                form.setScheduleImportType((Integer) row.get("scheduleImportType"));
                form.setSetRefundWay((Integer) row.get("setRefundWay"));
                form.setSponsorUnit((String) row.get("sponsorUnit"));
                form.setTrainPeriod((String) row.get("trainPeriod"));
                form.setVenueRoomId((String) row.get("venueRoomId"));
                form.setResourceImgUrl((String) row.get("resourceImgUrl"));
                form.setLinkman((String) row.get("linkman"));

                // 处理时间字段
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                if (row.get("createTime") != null) {
                    form.setCreateTime(formatDateTime(row.get("createTime"), sdf));
                }
                if (row.get("updateTime") != null) {
                    form.setUpdateTime(formatDateTime(row.get("updateTime"), sdf));
                }
                if (row.get("registrationStartTime") != null) {
                    form.setRegistrationStartTime(formatDateTime(row.get("registrationStartTime"), sdf));
                }
                if (row.get("registrationEndTime") != null) {
                    form.setRegistrationEndTime(formatDateTime(row.get("registrationEndTime"), sdf));
                }
                if (row.get("refundTime") != null) {
                    form.setRefundTime(formatDateTime(row.get("refundTime"), sdf));
                }

                // 获取标签信息（需要单独查询）
                Integer trainId = (Integer) row.get("id");
                if (trainId != null) {
                    CultureCloudOfflineTrainingEntity entity = cultureCloudOfflineTrainingDao.get(trainId);
                    if (entity != null && entity.getTagList() != null) {
                        Map<Integer, List<CultureCloudTagEntity>> collect1 = (Map<Integer, List<CultureCloudTagEntity>>) entity.getTagList()
                                .parallelStream().collect(groupingBy(CultureCloudTagEntity::getCategory));
                        for (Integer key : collect1.keySet()) {
                            StringBuilder tags = new StringBuilder();
                            if (key == 0) {
                                for (CultureCloudTagEntity t : collect1.get(key)) {
                                    tags.append(t.getTagName() + ",");
                                }
                                if (tags.length() > 0) {
                                    form.setTags(tags.substring(0, tags.length() - 1).toString());
                                }
                            } else if (key == 1) {
                                for (CultureCloudTagEntity t : collect1.get(key)) {
                                    tags.append(t.getTagName() + ",");
                                }
                                if (tags.length() > 0) {
                                    form.setKeywords(tags.substring(0, tags.length() - 1).toString());
                                }
                            } else if (key == 2) {
                                for (CultureCloudTagEntity t : collect1.get(key)) {
                                    CultureCloudTypeForm cultureCloudTypeForm = new CultureCloudTypeForm();
                                    cultureCloudTypeForm.setCultureCloudTypeId(t.getId());
                                    cultureCloudTypeForm.setCategory(t.getCategory());
                                    cultureCloudTypeForm.setTypeName(t.getTagName());
                                    form.getTypeList().add(cultureCloudTypeForm);
                                }
                            }
                        }
                    }
                }

                res.getTrainList().add(form);
            }

            // 设置分页信息
            int totalPage = (int) Math.ceil((double) totalResult / pageSize);
            res.setTotal(totalPage);
            res.setCurrentPage(currentPage);
            res.setPageSize(pageSize);
            res.setTotalResult(totalResult.intValue());
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("获取培训列表失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("获取培训列表失败");
        }
        return res;
    }


    @Override
    public IResponse publishOfflineTraining(GetCultureCloudOfflineTrainingListReq req) {
        GenericResponse res = new GenericResponse();
        Object userObj = getPrincipal(true);
        if (!(userObj instanceof CultureCloudUserEntity)) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        CultureCloudUserEntity user = (CultureCloudUserEntity) userObj;
        if (req.getTrainId() == null || req.getTrainStatus() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("培训ID和状态不能为空");
            return res;
        }

        try {
            CultureCloudOfflineTrainingEntity entity = cultureCloudOfflineTrainingDao.get(req.getTrainId());
            if (entity == null) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo("培训不存在");
                return res;
            }

            // 根据当前状态判断目标状态
            Integer currentStatus = entity.getTrainStatus();
            Integer targetStatus;

            if (req.getTrainStatus() != null) {
                targetStatus = req.getTrainStatus();
            } else {
                // 根据当前状态自动判断下一个状态
                if (currentStatus == null || currentStatus.equals(1)) {
                    // 草稿状态 -> 提交审核
                    targetStatus = 0; // 未审核
                } else if (currentStatus.equals(2)) {
                    // 已审核状态 -> 发布
                    targetStatus = 6; // 已发布
                } else {
                    // 其他状态默认发布
                    targetStatus = 0;
                }
            }

            entity.setIsDelete(1);
            entity.setTrainStatus(targetStatus);
            entity.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
            entity.setUpdateTime(new Date());
            entity.setUpdateUser(user.getId().toString());
            cultureCloudOfflineTrainingDao.update(entity);

            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("更新培训状态失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("更新培训状态失败");
        }

        return res;
    }


    @Override
    public IResponse auditOfflineTraining(GetCultureCloudOfflineTrainingListReq req) {
        GenericResponse res = new GenericResponse();
        Object userObj = getPrincipal(true);
        if (!(userObj instanceof CultureCloudUserEntity)) {
            res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
            return res;
        }

        CultureCloudUserEntity user = (CultureCloudUserEntity) userObj;
        if (req.getTrainId() == null || req.getTrainStatus() == null) {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo("培训ID和状态不能为空");
            return res;
        }

        try {
            CultureCloudOfflineTrainingEntity entity = cultureCloudOfflineTrainingDao.get(req.getTrainId());
            if (entity == null) {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo("培训不存在");
                return res;
            }

            entity.setTrainStatus(req.getTrainStatus());
            entity.setUpdateTime(new Date());
            entity.setUpdateUser(user.getId().toString());
            cultureCloudOfflineTrainingDao.update(entity);

            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } catch (Exception e) {
            logger.error("更新培训状态失败：", e);
            res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
            res.setRetInfo("更新培训状态失败");
        }

        return res;
    }

//    public IResponse addTrainOrder(CultureCloudOfflineTrainingReq req) {
//        try {
//            order.setUpdateUser(sysUser.getUserId());
//            final CmsShop shop = (CmsShop) session.getAttribute("shop");
//            String userShopPath = null;
//            if (shop != null && StringUtils.isNotBlank(shop.getShopId())){
//                userShopPath = shop.getShopPath();
//            }
//            final String shopPath = userShopPath;
//            int res = orderMapper.updateByPrimaryKeySelective(order);
//            if (res > 0 && order.getState() == 1) {
//
//                try {
//                    CmsTrainOrder o = orderMapper.selectByPrimaryKey(order.getId());
//                    CmsTrainBean train = selectByPrimaryKey(o.getTrainId());
//                    //发送预订成功的短信通知
//                    Map<String, Object> params = new HashMap<>();
//                    params.put("username", o.getName());
//                    params.put("trainName", train.getTrainTitle().length()>20?train.getTrainTitle().substring(0,20):train.getTrainTitle());
//                    params.put("trainDateTime", train.getTrainStartTime().split(" ")[0]);
//                    //params.put("trainDateTime", train.getTrainStartTime().split(" ")[0] + " " + train.getTrainStartTime().split(" ")[1] + "-" +
//                    train.getTrainEndTime().split(" ")[1]);
//                    if(StringUtils.isNotBlank(shopPath) && shopPath.equals("szslhqwty")){
//                        // 深圳龙华短信
//                        smsUtilBySZLongHua.sendEntryTrainSuccess(o.getPhoneNum(), params);
//                    }else {
//                        // 文化云短信
//                        smsService.sendMsg(o.getPhoneNum(), params, Constant.SMS_SIGN_TRAIN_SUCCESS, "文化云", "文化培训报名成功通知发送失败");
//                    }
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//
//            } else if(res > 0 && (order.getState() == 4 || order.getState() == 2)){
//                final CmsTrainOrder o = orderMapper.selectByPrimaryKey(order.getId());
//                final CmsTrainBean train = selectByPrimaryKey(o.getTrainId());
//                if(train.getIntegralNeeded() != null && train.getIntegralNeeded() > 0){
//                    //返还积分
//                    MultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<String, Object>();
//                    paramMap.add("userId", o.getCreateUser());
//                    paramMap.add("integralChange", train.getIntegralNeeded());
//                    paramMap.add("trainId", train.getId());
//                    RestTemplateModel restTemplateModel = new RestTemplateModel();
//                    RestTemplate restTemplate = restTemplateModel.restTemplate();
//                    restTemplate.postForEntity(staticServer.getInterfaceGateway()+"user/back/userIntegral/restoreUnderlineTrainOrderIntegral",
//                    paramMap, Result.class);
//                }
//
//                // 发送审核不通过的短信通知
//                try {
//                    Map<String, Object> params = new HashMap<>();
//                    params.put("username", o.getName());
//                    params.put("trainName", train.getTrainTitle().length()>20?train.getTrainTitle().substring(0,20):train.getTrainTitle());
//                    if(StringUtils.isNotBlank(shopPath) && shopPath.equals("szslhqwty")){
//                        // 深圳龙华短信
//                        smsUtilBySZLongHua.sendEntryTrainUnsubscribe(o.getPhoneNum(), params);
//                    }else {
//                        params.put("shopName",shop.getShopName());
//                        // 文化云短信
//                        smsService.sendMsg(o.getPhoneNum(), params, Constant.SMS_SIGN_TRAIN_ERROR, "文化云", "文化培训报名成功通知发送失败");
//
//                    }
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//            return JSONResponse.toAppResultFormat(200, "操作成功");
//        } catch (Exception e) {
//            e.printStackTrace();
//            return JSONResponse.toAppResultFormat(500, "操作订单异常");
//        }
//    }

    private int updateCmsTrainFiledList(String fieldStr, CultureCloudOfflineTrainingEntity train, int flag) {
        int result = 0;
        List<CultureCloudTrainFieldEntity> filedList = new ArrayList<>();
        String ids = "";
        //固定场次
        if (train.getScheduleImportType() == 3) {
            train.getFieldList().clear();
            JSONObject field = JSONObject.parseObject(fieldStr);
            String[] dates = DateUtils.getDates(train.getTrainStartTime(), train.getTrainEndTime(), train.getWeekStr().replaceAll(",", ""));
            for (int i = 0; i < dates.length; i++) {
                String date = dates[i];
                CultureCloudTrainFieldEntity f = new CultureCloudTrainFieldEntity();
                String fieldTimeStr = date + " " + field.getString("fieldStartTime") + "-" + field.getString("fieldEndTime");
                f.setFieldTimeStr(fieldTimeStr);
                f.setFieldStartTime(DateUtils.stringToDate(date + " " + field.getString("fieldStartTime"), "yyyy-MM-dd HH:mm"));
                f.setFieldEndTime(DateUtils.stringToDate(date + " " + field.getString("fieldEndTime"), "yyyy-MM-dd HH:mm"));
                f.setTrain(train);
                f.setFieldUpdateTime(new Date());
                f.setFieldUpdateUser(train.getUpdateUser());
                f.setFieldTitle(train.getTrainTitle() + "第" + (i + 1) + "课");
                f.setFieldState(1);
                ids += f.getId() + ";" + fieldTimeStr + ",";
                cultureCloudTrainFieldDao.save(f);
                filedList.add(f);
            }
        } else {
            if (StringUtils.isNotBlank(fieldStr)) {
                train.getFieldList().clear();
                JSONArray arr = JSONArray.parseArray(fieldStr);
                for (int i = 0; i < arr.size(); i++) {
                    CultureCloudTrainFieldEntity field = new CultureCloudTrainFieldEntity();
                    String fieldTimeStr = arr.getJSONObject(i).getString("fieldTimeStr");
                    try {
                        field.setFieldStartTime(arr.getJSONObject(i).getDate("fieldStartTime"));
                        field.setFieldEndTime(arr.getJSONObject(i).getDate("fieldEndTime"));
                    } catch (Exception e) {
                        e.printStackTrace();
                        return -1;
                    }
                    field.setFieldTimeStr(fieldTimeStr);
                    field.setFieldUpdateTime(new Date());
                    field.setFieldUpdateUser(train.getUpdateUser());
                    field.setFieldTitle(train.getTrainTitle() + "第" + (i + 1) + "课");
                    field.setFieldState(1);
                    if (StringUtils.isNotBlank(fieldTimeStr)) {
                        ids += field.getId() + ";" + fieldTimeStr + ",";
                    } else {
                        ids += field.getId() + ";" + arr.getJSONObject(i).getString("fieldStartTime") + "-" + arr.getJSONObject(i).getString(
                                "fieldEndTime").substring(11, 16) + ",";
                    }
                    cultureCloudTrainFieldDao.save(field);
                    filedList.add(field);
                }
            } else {
                if (StringUtils.isNotBlank(train.getTrainStartTime()) && StringUtils.isNotBlank(train.getTrainEndTime())) {
                    train.getFieldList().clear();
                    CultureCloudTrainFieldEntity field = new CultureCloudTrainFieldEntity();
                    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    try {
                        field.setFieldStartTime(df.parse(train.getTrainStartTime()));
                        field.setFieldEndTime(df.parse(train.getTrainEndTime()));
                    } catch (ParseException e) {
                        e.printStackTrace();
                        return -1;
                    }
                    field.setFieldUpdateTime(new Date());
                    field.setFieldUpdateUser(train.getUpdateUser());
                    field.setFieldTitle(train.getTrainTitle() + "第1课");
                    field.setFieldState(1);
                    ids += field.getId() + ";" + train.getTrainStartTime() + "-" + train.getTrainEndTime().substring(11, 16) + ",";
                    cultureCloudTrainFieldDao.save(field);
                    filedList.add(field);
                }
            }
        }

        if (filedList.size() > 0) {
            train.setFieldList(filedList);
            if (train.getTrainField() == 2 && train.getSetRefundWay().equals(1)) {
                //多场 默认退订时间 取第一场
                filedList.sort((x, y) -> x.getFieldStartTime().compareTo(y.getFieldStartTime()));
                train.setRefundTime(filedList.get(0).getFieldStartTime());
            }

            //新增培训走此逻辑
            //电子签到培训需要绑定预约活动室（湖南电子屏）
//            if(bloBs.getIsElecSign()!=null && bloBs.getIsElecSign() == 1 && StringUtils.isNotBlank(bloBs.getRoomId()) && flag==1){
//                RestTemplateModel restTemplateModel = new RestTemplateModel();
//                RestTemplate restTemplate = restTemplateModel.restTemplate();
//                ids=ids.substring(0,ids.length()-1);
//                Map<String,Object> param=new HashMap<>();
//                param.put("ids",ids);
//                param.put("roomId",bloBs.getRoomId());
//                param.put("trainId",bloBs.getId());
//                restTemplate.postForEntity(bloBs.getCtUrl()+"apiRoomBook/updateRoomBook.do", param, Result.class);
//
//            }
        }
        return result;
    }


    private void unsubscribeTrainOrder(CultureCloudOfflineTrainingEntity entity) {
        String hql = "select model from CultureCloudTrainOrderEntity as model where model.training.id = " + entity.getId();
        List<CultureCloudTrainOrderEntity> orders = cultureCloudTrainOrderDao.getList(hql);
        for (final CultureCloudTrainOrderEntity order : orders) {
            //取消订单
            order.setState(2);
            order.setUpdateUser(entity.getUpdateUser());
            order.setUpdateTime(new Date());
            cultureCloudTrainOrderDao.update(order);

            //发送短信：亲爱的${name}，您报名的培训“${trainTitle}”因故取消，系统已为您自动取消订单，因此为您带来的不便，请您谅解
//            Map<String, Object> smsParams = new HashMap<>();
//            smsParams.put("name", order.getName());
//            smsParams.put("trainTitle", order.getTraining().getTrainTitle());
            /*String sendMsg = SmsUtil.trainOrderUnsubscribe(order.getPhoneNum(), smsParams);
            log.info("培训下架订单’" + order.getOrderNum() + "‘短信发送结果:" + sendMsg);*/
//            SendSmsResponse smsResponse = smsService.sendMsg(order.getPhoneNum(), smsParams, Constant.SMS_UNSUBSCRIBE_TRAIN, "文化云","发送失败");
            String content = "【佛山文化云】亲爱的" + order.getName() + "，您报名的培训 " + order.getTraining().getTrainTitle() + " 因故取消，系统已为您自动取消订单，因此为您带来的不便，请您谅解";
            String phone = order.getPhoneNum();
            sendSms(phone, content);
            //log.info("培训下架订单’" + order.getOrderNum() + "‘短信发送结果:" + smsResponse.getCode());
        }
    }

    /**
     * 安全的时间格式化方法
     * 处理不同类型的时间对象（Date, Timestamp, String等）
     */
    private String formatDateTime(Object dateObj, SimpleDateFormat formatter) {
        if (dateObj == null) {
            return null;
        }

        try {
            if (dateObj instanceof Date) {
                return formatter.format((Date) dateObj);
            } else if (dateObj instanceof Timestamp) {
                return formatter.format(new Date(((Timestamp) dateObj).getTime()));
            } else if (dateObj instanceof java.sql.Date) {
                return formatter.format(new Date(((java.sql.Date) dateObj).getTime()));
            } else if (dateObj instanceof String) {
                // 如果已经是字符串格式，直接返回
                String dateStr = (String) dateObj;
                // 简单验证是否已经是正确格式
                if (dateStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                    return dateStr;
                }
                // 尝试解析其他格式的字符串
                try {
                    SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date parsedDate = inputFormat.parse(dateStr);
                    return formatter.format(parsedDate);
                } catch (ParseException e) {
                    logger.warn("无法解析时间字符串: " + dateStr, e);
                    return dateStr; // 返回原字符串
                }
            } else {
                // 对于其他类型，尝试转换为字符串
                return dateObj.toString();
            }
        } catch (Exception e) {
            logger.error("格式化时间失败: " + dateObj, e);
            return dateObj.toString();
        }
    }

}