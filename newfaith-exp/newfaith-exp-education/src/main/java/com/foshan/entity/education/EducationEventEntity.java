package com.foshan.entity.education;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_education_event")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class EducationEventEntity implements IEntityBean {

    private static final long serialVersionUID = -5231105693049854701L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(columnDefinition = "varchar(64) comment '事件类型'")
    private String eventType;
    @Column(columnDefinition = "varchar(256) comment '事件详情'")
    private String eventDetail;
    @Column(columnDefinition = "varchar(256) comment '事件Key'")
    private String eventKey;
    @Column(columnDefinition = "varchar(256) comment '手机号'")
    private String mobile;
    @Column(columnDefinition = "varchar(256) comment '智能卡号'")
    private String smartCardId;
    @Column(columnDefinition = "Timestamp default current_timestamp comment '事件时间'")
    private Timestamp eventTime;



}
