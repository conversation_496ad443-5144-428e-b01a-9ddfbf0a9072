package com.foshan.entity.education;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_desire")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class DesireEntity implements IEntityBean {

    private static final long serialVersionUID =-1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(columnDefinition = "text comment '愿望'")
    private String desireContent;
    @Column(columnDefinition = "int(1) comment '审核状态 0-未审核 1-审核通过 2-审核不通过'")
    private Integer checkState;
    @Column(columnDefinition = "varchar(11) comment '手机号'")
    private String phone;
    @Column(columnDefinition = "varchar(11) comment '接受手机号'")
    private String toPhone;
    @Column(columnDefinition = "varchar(32) comment '智能卡号'")
    private String smartCardId;
    @Column(columnDefinition = "Timestamp default current_timestamp comment '事件时间'")
    private Timestamp createTime;
    @Column(columnDefinition = "varchar(11) comment '昵称'")
    private String nickName;
    @Column(columnDefinition = "varchar(11) comment '接受者昵称'")
    private String toNickName;



}
