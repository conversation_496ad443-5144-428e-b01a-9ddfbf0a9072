package com.foshan.entity.education;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.RoleEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_role")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@DiscriminatorValue("E")
public class EducationRoleEntity extends RoleEntity {

	/**
	 * 教育平台角色
	 */
	private static final long serialVersionUID = -1973508083144247179L;

}
