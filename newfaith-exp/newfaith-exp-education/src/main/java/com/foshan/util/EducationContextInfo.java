package com.foshan.util;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;


@Component
@PropertySource(value = "classpath:application.yml",encoding="utf-8")
@Getter
@Setter
@NoArgsConstructor
public class EducationContextInfo {
	@Value("${party.memberRoleId}")
	public   String memberRoleId;
	@Value("${party.loginAddress}")
	public  String loginAddress;
	@Value("${party.checkinAddress}")
	public String checkinAddress;
	@Value("${party.commentAddress}")
	public String commentAddress;
}
