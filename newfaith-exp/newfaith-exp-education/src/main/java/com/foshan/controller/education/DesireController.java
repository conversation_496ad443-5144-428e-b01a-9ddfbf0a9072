package com.foshan.controller.education;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.education.request.DesireReq;
import com.foshan.form.education.request.EducationMemberReq;
import com.foshan.form.education.response.GetDesireListRes;
import com.foshan.form.education.response.educationMember.GetEducationMemberInfoRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.service.sensitivewords.annotation.CheckSensitiveWords;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "愿望模块")
@RestController
public class DesireController extends BaseEducationController {

    // 获取社区列表
    @ApiOperation(value = "获取愿望列表(getDesireList)", httpMethod = "POST", notes = "获取愿望列表；")
    @ResponseBody
    @RequestMapping(value = "/getDesireList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetDesireListRes getDesireList(@RequestBody DesireReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetDesireListRes res = (GetDesireListRes) desireService.getDesireList(req);
        return res;
    }

    // 新增社区
    @ApiOperation(value = "新增愿望(addDesire)", httpMethod = "POST", notes = "addDesire")
    @ResponseBody
    @RequestMapping(value = "/addDesire", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse addDesire(@RequestBody DesireReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) desireService.addDesire(req);
        return res;
    }

    // 新增社区
    @ApiOperation(value = "审核愿望(checkDesire)", httpMethod = "POST", notes = "checkDesire")
    @ResponseBody
    @RequestMapping(value = "/checkDesire", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse checkDesire(@RequestBody DesireReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) desireService.checkDesire(req);
        return res;
    }

    // 获取用户信息
    @ApiOperation(value = "获取用户信息(getMemberInfo)", httpMethod = "POST", notes = "getMemberInfo")
    @ResponseBody
    @RequestMapping(value = "/getMemberInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetEducationMemberInfoRes getMemberInfo(@RequestBody EducationMemberReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetEducationMemberInfoRes res = (GetEducationMemberInfoRes) desireService.getMemberInfo(req);
        return res;
    }


}
