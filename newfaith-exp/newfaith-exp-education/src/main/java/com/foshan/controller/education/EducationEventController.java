package com.foshan.controller.education;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.education.request.EducationEventReq;
import com.foshan.form.education.response.AddEducationEventRes;
import com.foshan.form.education.response.GetEducationEventListRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "跨屏事件模块")
@RestController
public class EducationEventController extends BaseEducationController {

//    // 获取社区列表
//    @ApiOperation(value = "获取社区列表(getCrossDeviceEvent)", httpMethod = "POST", notes = "获取事件列表；")
//    @ResponseBody
//    @RequestMapping(value = "/getCrossDeviceEvent", method = {
//            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//    public GetEducationEventListRes getEducationEventList(@RequestBody EducationEventReq req, HttpServletRequest request)
//            throws JsonProcessingException {
//        GetEducationEventListRes res = (GetEducationEventListRes) educationEventService.getEducationEventList(req);
//        return res;
//    }
//
//    // 新增社区
//    @ApiOperation(value = "新增事件(addCrossDeviceEvent)", httpMethod = "POST", notes = "新增事件")
//    @ResponseBody
//    @RequestMapping(value = "/addCrossDeviceEvent", method = {
//            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//    public AddEducationEventRes addEducationEvent(@RequestBody EducationEventReq req, HttpServletRequest request)
//            throws JsonProcessingException {
//        AddEducationEventRes res = (AddEducationEventRes) educationEventService.addEducationEvent(req);
//        return res;
//    }


}
