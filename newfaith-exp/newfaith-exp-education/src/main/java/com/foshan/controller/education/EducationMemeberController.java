package com.foshan.controller.education;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.education.request.EducationMemberLoginReq;
import com.foshan.form.education.request.EducationMemberReq;
import com.foshan.form.education.response.educationMember.AddEducationMemberRes;
import com.foshan.form.education.response.educationMember.GetEducationMemberInfoRes;
import com.foshan.form.education.response.educationMember.GetEducationMemberListRes;
import com.foshan.form.education.response.educationMember.EducationMemberLoginRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "用户模块")
@RequestMapping(value = "member")
@RestController
public class EducationMemeberController extends BaseEducationController {
//
//
//	// 获取用户列表
//	@ApiOperation(value = "获取用户列表(getEducationMemberList)", httpMethod = "POST", notes = "获取用户列表，传空{}即可；")
//	@ResponseBody
//	@RequestMapping(value = "/getEducationMemberList", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public GetEducationMemberListRes getEducationMemberList(@RequestBody EducationMemberReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		GetEducationMemberListRes res = (GetEducationMemberListRes) educationMemberService.getEducationMemberList(req);
//		return res;
//	}
//	// 新增用户列表
//	@ApiOperation(value = "新增用户列表(addEducationMember)", httpMethod = "POST", notes = "新增用户列表，传空{}即可；")
//	@ResponseBody
//	@RequestMapping(value = "/addEducationMember", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public AddEducationMemberRes addEducationMemberList(@RequestBody EducationMemberReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		AddEducationMemberRes res = (AddEducationMemberRes) educationMemberService.addEducationMember(req);
//		return res;
//	}
//
//	// 用户注册
//	@ApiOperation(value = "用户注册(memberRegister)", httpMethod = "POST", notes = "新增用户列表，传空{}即可；")
//	@ResponseBody
//	@RequestMapping(value = "/memberRegister", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public AddEducationMemberRes memberRegister(@RequestBody EducationMemberReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		AddEducationMemberRes res = (AddEducationMemberRes) educationMemberService.memberRegister(req,request);
//		return res;
//	}
//
//	// 修改用户
//	@ApiOperation(value = "修改用户(modifyEducationMember)", httpMethod = "POST", notes = "修改用户，EducationMemberId不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/modifyEducationMember", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public GenericResponse modifyEducationMember(@RequestBody EducationMemberReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		GenericResponse res = (GenericResponse) educationMemberService.modifyEducationMember(req);
//		return res;
//	}
//	// 删除用户
//	@ApiOperation(value = "删除用户(deleteEducationMember)", httpMethod = "POST", notes = "删除用户，EducationMemberId不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/deleteEducationMember", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public GenericResponse deleteEducationMember(@RequestBody EducationMemberReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		GenericResponse res = (GenericResponse) educationMemberService.deleteEducationMember(req);
//		return res;
//	}
//
//	// 获取详情用户
//	@ApiOperation(value = "获取详情用户(getEducationMemberInfo)", httpMethod = "POST", notes = "获取详情用户，EducationMemberId不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/getEducationMemberInfo", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public GetEducationMemberInfoRes getEducationMemberInfo(@RequestBody EducationMemberReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		GetEducationMemberInfoRes res = (GetEducationMemberInfoRes) educationMemberService.getEducationMemberInfo(req);
//		return res;
//	}
//
//	// 查询用户登录状态
//	@ApiOperation(value = "查询用户登录状态(findEducationMemberLoginState)", httpMethod = "POST", notes = "查询用户登录状态，smartcardId不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/findEducationMemberLoginState", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public EducationMemberLoginRes findEducationMemberLoginState(@RequestBody EducationMemberLoginReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		EducationMemberLoginRes res = (EducationMemberLoginRes) educationMemberService.findEducationMemberLoginState(req);
//		return res;
//	}
//
//	// 查询用户登录状态
//	@ApiOperation(value = "查询用户登录状态(getEducationMemberLoginState)", httpMethod = "POST", notes = "查询用户登录状态，phone不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/getEducationMemberLoginState", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public EducationMemberLoginRes getEducationMemberLoginState(@RequestBody EducationMemberLoginReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		EducationMemberLoginRes res = (EducationMemberLoginRes) educationMemberService.getEducationMemberLoginState(req);
//		return res;
//	}
//
//	// 用户注册
//	@ApiOperation(value = "用户注册(educationMemberRegister)", httpMethod = "POST", notes = "用户注册<p>1:phone不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/educationMemberRegister", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public EducationMemberLoginRes memberRegist(@RequestBody EducationMemberReq req, HttpServletRequest request) {
//		EducationMemberLoginRes res = (EducationMemberLoginRes) educationMemberService.memberRegister(req, request);
//		return res;
//	}
//
//
//	// 用户获取验证码
//	@ApiOperation(value = "用户获取验证码(getPhoneVerificationCode)", httpMethod = "POST", notes = "用户注册<p>1:phone不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/getPhoneVerificationCode", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public GenericResponse getPhoneVerificationCode(@RequestBody EducationMemberLoginReq req, HttpServletRequest request) {
//		GenericResponse res = (GenericResponse) educationMemberService.getPhoneVerificationCode(req, request);
//		return res;
//	}

}
