package com.foshan.service.education.impl;

import com.foshan.dao.generic.Page;
import com.foshan.entity.education.EducationEventEntity;
import com.foshan.entity.education.EducationMemberEntity;
import com.foshan.form.education.EducationEventForm;
import com.foshan.form.education.request.EducationEventReq;
import com.foshan.form.education.response.AddEducationEventRes;
import com.foshan.form.education.response.GetEducationEventListRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.education.IEducationEventService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;

@Transactional
@Service("educationEventService")
public class EducationEventServiceImpl extends GenericEducationService implements IEducationEventService {

    @Override
    public IResponse getEducationEventList(EducationEventReq req) {
        GetEducationEventListRes res = new GetEducationEventListRes();

        if (StringUtils.isNoneEmpty(req.getSmartcardId())) {
            Page<EducationEventEntity> page = new Page<EducationEventEntity>();
            page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
            page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
            page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

            if (null == req.getCount()) {
                req.setCount(5);
            }

            String type = "";
            if (StringUtils.isNotEmpty(req.getEventType())) {
                String[] types = req.getEventType().split("\\|");
                for (String o : types
                ) {
                    type = type == "" ? type + "'" + o + "'" : type + ",'" + o + "'";
                }
                if (type.startsWith(",")) {
                    type.replaceFirst(",", "");
                }
            }
            Date date = new Date();
            Date newDate = DateUtils.addSeconds(date, null != req.getSeconds() ? -req.getSeconds() : -5);
            SimpleDateFormat simpleDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Timestamp.valueOf(simpleDate.format(newDate));


            StringBuilder hql = new StringBuilder("select distinct a from EducationEventEntity a where 1=1 ");
            hql.append(StringUtils.isNotEmpty(req.getSmartcardId()) ? " and a.smartCardId =" + req.getSmartcardId() : "");
            hql.append(StringUtils.isNotEmpty(req.getEventType()) ? " and a.eventType in (" + type + ")" : "");
            hql.append(" and a.eventTime > '" + simpleDate.format(newDate) + "'");
            hql.append(" ORDER BY a.eventTime desc");
            page = educationEventDao.queryPage(page, hql.toString());

            res.setTotalResult(page.getTotalCount());
            res.setPageSize(page.getPageSize());
            res.setCurrentPage(page.getCurrentPage());
            res.setTotal(page.getTotalPage());
            page.getResultList().forEach(o -> {
                EducationEventForm educationEventForm = new EducationEventForm();
                educationEventForm.setEventType(o.getEventType());
                educationEventForm.setEventDetail(o.getEventDetail());
                educationEventForm.setEventKey(o.getEventKey());
                educationEventForm.setEventTime(o.getEventTime().toString().replace(".0", ""));

                res.getEventList().add(educationEventForm);
            });

            if (null != req.getCount() && req.getCount() < res.getEventList().size()) {
                res.setEventList(res.getEventList().subList(0, req.getCount()));
            }

            res.setTotal(res.getEventList().size());
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

            String deleteSql = "DELETE FROM t_education_event where smartCardId = '" + req.getSmartcardId() + "'";
            educationEventDao.createSQLQuery(deleteSql).executeUpdate();

        } else {
            res.setRet(ResponseContext.RES_DATA_ERROR_INFO);
            res.setRetInfo("无智能卡号");
        }


        return res;
    }

    @Override
    public IResponse addEducationEvent(EducationEventReq req) {
        AddEducationEventRes res = new AddEducationEventRes();

        Object userObj = getPrincipal(false);
        if (null != userObj) {
            if (userObj instanceof EducationMemberEntity) {
                EducationMemberEntity member = (EducationMemberEntity) userObj;
                if (StringUtils.isNoneEmpty(member.getSmartcardId())) {
                    EducationEventEntity educationEvent = new EducationEventEntity();

                    educationEvent.setEventType(StringUtils.isNotEmpty(req.getEventType()) ? req.getEventType() : "");
                    educationEvent.setEventDetail(StringUtils.isNotEmpty(req.getEventDetail()) ? req.getEventDetail() : "");
                    educationEvent.setEventKey(StringUtils.isNotEmpty(req.getEventKey()) ? req.getEventKey() : "");
                    educationEvent.setEventTime(new Timestamp(new Date().getTime()));
                    educationEvent.setMobile(member.getPhone());
                    educationEvent.setSmartCardId(member.getSmartcardId());
                    educationEventDao.save(educationEvent);
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                } else {
                    res.setRet(ResponseContext.RES_DATA_NULL_INFO);
                    res.setRetInfo("无智能卡号,请绑定智能卡");
                }
            } else {
                res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
        }
        return res;
    }


}