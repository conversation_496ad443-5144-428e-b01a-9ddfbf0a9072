package com.foshan.service.education.impl;

import com.foshan.dao.generic.Page;
import com.foshan.entity.UserEntity;
import com.foshan.entity.education.DesireEntity;
import com.foshan.entity.education.EducationMemberEntity;

import com.foshan.form.education.DesireForm;
import com.foshan.form.education.EducationMemberForm;
import com.foshan.form.education.request.DesireReq;
import com.foshan.form.education.request.EducationMemberReq;
import com.foshan.form.education.response.GetDesireListRes;
import com.foshan.form.education.response.educationMember.GetEducationMemberInfoRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.education.IDesireService;
import com.foshan.service.sensitivewords.annotation.CheckSensitiveWords;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Service("desireService")
public class DesireServiceImpl extends GenericEducationService implements IDesireService {

    @Override
    public IResponse getDesireList(DesireReq req) {
        GetDesireListRes res = new GetDesireListRes();

            Page<DesireEntity> page = new Page<DesireEntity>();
            page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
            page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
            page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

        Object userObj = getPrincipal(false);

        String phone = "";
        String smartCardId=StringUtils.isNotEmpty(req.getSmartcardId())?req.getSmartcardId():"";


            StringBuilder hql = new StringBuilder("select distinct a from DesireEntity a where 1=1");

            if(null == userObj){
                hql.append(" and a.checkState=1 ORDER BY FIELD(a.smartCardId,'").append(smartCardId).append("'), RAND()");
            }else{

            if (userObj instanceof EducationMemberEntity) {
                hql.append(" and a.checkState=1");
                phone = ((EducationMemberEntity)userObj).getPhone();
                if(StringUtils.isNotEmpty(phone) && StringUtils.isNotEmpty(smartCardId)){
                    hql.append(" ORDER BY FIELD(a.smartCardId,'").append(smartCardId).append("'),FIELD(a.phone,'").append(phone).append("'), RAND()");
                }else if(StringUtils.isNotEmpty(smartCardId)){
                    hql.append(" ORDER BY FIELD(a.smartCardId,'").append(smartCardId).append("'), RAND()");
                }else if(StringUtils.isNotEmpty(phone)){
                    hql.append(" ORDER BY FIELD(a.phone,'").append(phone).append("') desc,FIELD(a.toPhone,'").append(phone).append("') desc, RAND()");
                }
            }

            if (userObj instanceof UserEntity){
                if (null != req.getCheckState()){
                    hql.append(" and a.checkState=").append(req.getCheckState());
                }
            }

        }
            page = desireDao.queryPage(page, hql.toString());

            res.setTotalResult(page.getTotalCount());
            res.setPageSize(page.getPageSize());
            res.setCurrentPage(page.getCurrentPage());
            res.setTotal(page.getTotalPage());
            page.getResultList().forEach(o -> {
                DesireForm desireForm = new DesireForm();
                if(userObj instanceof UserEntity){
                desireForm.setDesireId(o.getId());
                desireForm.setDesireContent(o.getDesireContent());
                desireForm.setNickName(o.getNickName());
                desireForm.setCheckState(o.getCheckState());
                desireForm.setPhone(o.getPhone());
                desireForm.setToNickName(o.getToNickName());
                desireForm.setSmartCardId(o.getSmartCardId());
                desireForm.setToPhone(o.getToPhone());
                }else {
                    desireForm.setDesireContent(o.getDesireContent());
                    desireForm.setNickName(o.getNickName());
                    desireForm.setToNickName(o.getToNickName());
                    desireForm.setLastPhoneNum(o.getPhone().substring(o.getPhone().length() - 4));
                    desireForm.setLastToPhoneNum(StringUtils.isNotEmpty(o.getToPhone())? o.getToPhone().substring(o.getToPhone().length()-4):null);
                }
                res.getDesireList().add(desireForm);
            });

            res.setTotal(res.getDesireList().size());
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

        return res;
    }

    @Override
    @CheckSensitiveWords
    public IResponse addDesire(DesireReq req) {
        GenericResponse res = new GenericResponse();

        Object userObj = getPrincipal(false);
        if (null != userObj) {
            if (userObj instanceof EducationMemberEntity) {
                EducationMemberEntity member = (EducationMemberEntity) userObj;
//                if (StringUtils.isNoneEmpty(member.getSmartcardId())) {
                    DesireEntity desireEntity = new DesireEntity();

                    desireEntity.setDesireContent(req.getDesireContent());
                    desireEntity.setCheckState(0);
                    desireEntity.setNickName(StringUtils.isNotEmpty(req.getNikeName())?req.getNikeName():"");
                    desireEntity.setToNickName(StringUtils.isNotEmpty(req.getToNikeName())?req.getToNikeName():"");
                    desireEntity.setPhone(member.getPhone());
                    desireEntity.setSmartCardId(StringUtils.isNotEmpty(member.getSmartcardId())? member.getSmartcardId():"");
                    desireEntity.setToPhone(StringUtils.isNotEmpty(req.getToPhone())?req.getToPhone():"");

                    desireDao.save(desireEntity);
                    res.setRet(ResponseContext.RES_SUCCESS_CODE);
                    res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//                } else {
//                    res.setRet(ResponseContext.RES_DATA_NULL_INFO);
//                    res.setRetInfo("无智能卡号,请绑定智能卡");
//                }
            } else {
                res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
        }
        return res;
    }

    @Override
    public IResponse checkDesire(DesireReq req){
        GenericResponse res = new GenericResponse();

        if (null == req.getDesireId() || null ==req.getCheckState()){
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
            return res;
        }

        Object userObj = getPrincipal(false);
        if (null != userObj) {
            if (userObj instanceof UserEntity) {
                DesireEntity desireEntity = desireDao.get(req.getDesireId());
                desireEntity.setCheckState(req.getCheckState());
                desireDao.update(desireEntity);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
                res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
        }

        return res;
    }

    @Override
    public IResponse getMemberInfo(EducationMemberReq req) {
        GetEducationMemberInfoRes res = new GetEducationMemberInfoRes();
        EducationMemberForm memberForm = new EducationMemberForm();
        Object userObj = getPrincipal(true);
        if (null == userObj){
            res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
            return res;
        }
        //if (null != userObj) {
        EducationMemberEntity member = null;
        if(null != userObj && userObj instanceof EducationMemberEntity &&  null==req.getMemberId()) {
            member = (EducationMemberEntity) userObj;
        }else if((null == userObj &&null!=req.getMemberId()) || null != userObj && userObj instanceof EducationMemberEntity &&  null!=req.getMemberId()) {
            if(null==req.getMemberId()) {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
                return res;
            }
            member = educationMemberDao.get(req.getMemberId());
            if(null == member) {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
                return res;
            }
        }else {
            res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
            res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
            return res;
        }
//			if(null==req.getMemberId()) {
//				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//				return res;
//			}
        //EducationMemberEntity member = educationMemberDao.get(req.getMemberId());
        if(null == member) {
            res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
            return res;
        }
        memberForm.setMemberId(member.getId());
//			memberForm.setPartyId(null!=member.getPartyOrganization() ? member.getPartyOrganization().getId():null);
        memberForm.setEmail(member.getEmail());
        memberForm.setHomePhone(member.getHomePhone());
        memberForm.setNickName(member.getNickName());
        memberForm.setOfficePhone(member.getOfficePhone());
        memberForm.setPhone(member.getPhone());
        memberForm.setRegistName(member.getRegistName());
        memberForm.setSex(member.getSex());
        memberForm.setSmartcardId(member.getSmartcardId());
        memberForm.setOrders(member.getOrders());
        memberForm.setHeadImage(StringUtils.isNotEmpty(member.getHeadImage()) ? member.getHeadImage() : "");
        memberForm.setUserName(StringUtils.isNotEmpty(member.getUserName()) ? member.getUserName() : "");


        memberForm.setMeno(StringUtils.isNotEmpty(member.getMeno()) ? member.getMeno() : "");

        memberForm.setRegionCode(StringUtils.isNotEmpty(member.getRegionCode()) ? member.getRegionCode() : "");
        res.setMemberForm(memberForm);
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
//		}
        return res;
    }

}