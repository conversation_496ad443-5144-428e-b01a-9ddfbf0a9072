package com.foshan.service.education.impl;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.foshan.entity.vo.ehcache.AuthenticationInfoVo;
import com.foshan.service.permission.shiro.ShiroHazelcastCache;
import com.foshan.util.*;
//import net.sf.ehcache.Element;
import com.hazelcast.spring.cache.HazelcastCacheManager;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.education.EducationMemberEntity;
import com.foshan.entity.education.EducationRoleEntity;
import com.foshan.form.education.EducationMemberForm;
import com.foshan.form.education.request.EducationMemberLoginReq;
import com.foshan.form.education.request.EducationMemberReq;
import com.foshan.form.education.response.educationMember.AddEducationMemberRes;
import com.foshan.form.education.response.educationMember.GetEducationMemberInfoRes;
import com.foshan.form.education.response.educationMember.GetEducationMemberListRes;
import com.foshan.form.education.response.educationMember.EducationMemberLoginRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.education.IEducationMemberService;


import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;

@Transactional
@Service("educationMemberService")
public class EducationMemberServiceImpl extends GenericEducationService implements IEducationMemberService {
	private final static Logger logger = LoggerFactory.getLogger(EducationMemberServiceImpl.class);

//	HazelcastInstance hazelcastClient = HazelcastClient.newHazelcastClient();
	@Resource
	private HazelcastCacheManager cacheManager;

	@Override
	public IResponse getEducationMemberList(EducationMemberReq req) {
		GetEducationMemberListRes res = new GetEducationMemberListRes();
		Page<EducationMemberEntity> page = new Page<EducationMemberEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		
		StringBuilder hql = new StringBuilder("select distinct a from EducationMemberEntity a inner join a.partyOrganizationList p where");
		hql.append( null!=req.getWorkingState() ? " a.workingState=" + req.getWorkingState() + " and" :"")
			.append(StringUtils.isNotEmpty(req.getUserName()) ?"  a.userName like '%" + req.getUserName() + "%' and" :"")
			.append(StringUtils.isNotEmpty(req.getSmartcardId()) ?"  a.smartcardId = '" + req.getSmartcardId()+ "' and" :"")
			.append(null!=req.getUserState() ? " a.userState="+req.getUserState()+" and":" a.userState="+EntityContext.RECORD_STATE_VALID+" and");
		hql = (hql.toString().endsWith("where") ? hql.delete(hql.length() - 5, hql.length())
				: hql.toString().endsWith("and") ? hql.delete(hql.length() - 3, hql.length()) : hql);
		hql .append(" ORDER BY a.orders ASC ");
		page = educationMemberDao.queryPage(page, hql.toString());
		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			EducationMemberForm memberForm = new EducationMemberForm();
			memberForm.setMemberId(o.getId());
			memberForm.setEmail(o.getEmail());
			memberForm.setHomePhone(o.getHomePhone());
			memberForm.setNickName(o.getNickName());
			memberForm.setOfficePhone(o.getOfficePhone());
			memberForm.setPhone(o.getPhone());
			memberForm.setRegistName(o.getRegistName());
			memberForm.setSex(o.getSex());
			memberForm.setSmartcardId(o.getSmartcardId());
			memberForm.setHeadImage(StringUtils.isNotEmpty(o.getHeadImage()) ? o.getHeadImage() : "");
			//memberForm.setPosition(StringUtils.isNotEmpty(o.getPosition()) ? o.getPosition() : "");
			memberForm.setUserName(StringUtils.isNotEmpty(o.getUserName()) ? o.getUserName() : "");
			//memberForm.setPoliticCountenance(o.getPoliticCountenance());
			memberForm.setOrders(o.getOrders());

/*			memberForm.setPartyId(null!=o.getPartyOrganization() ? o.getPartyOrganization().getId():null);
			memberForm.setPartyName(null!=o.getPartyOrganization()?o.getPartyOrganization().getPartyName():"");*/

			memberForm.setRegionCode(StringUtils.isNotEmpty(o.getRegionCode()) ? o.getRegionCode() : "");
			res.getMemberFormList().add(memberForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		
		return res;
	}

	@Override
	public IResponse addEducationMember(EducationMemberReq req) {
		AddEducationMemberRes res = new AddEducationMemberRes();
		String phone = (StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : "");
		if (StringUtils.isEmpty(phone) ||
				StringUtils.isEmpty(req.getRoleIdList())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO );
			return res;
		}

		EducationMemberEntity member = educationMemberDao.getUniqueBySql("SELECT * FROM t_account WHERE phone='"+req.getPhone()+"' and userState = 1",
				"");
		if (null != member) {
			//普通注册流程
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("对不起，您注册的用户名或手机号已被注册！");
			return res;
		} else {
			String[] rolerId = contextInfo.memberRoleId.split(",");
			List<EducationRoleEntity> roleList = new ArrayList<EducationRoleEntity>();
			for (String id : rolerId) {
				EducationRoleEntity role = educationRoleDao.get(Integer.valueOf(id));
				if (null != role) {
					roleList.add(role);
				}
			}

			member = new EducationMemberEntity();
			member.setLoginName(req.getPhone());
			member.setNickName(req.getPhone());
			
			//密码选填
			String password = req.getPassword();
			if(StringUtils.isNotBlank(password)) {
				try {
					member.setPassword(DigestUtil.sm3Digest(req.getPassword()));
					member.setLastPwdModifyTime(new Timestamp(new Date().getTime()));
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
			}
			member.setRegistName(req.getPhone());
			member.setPhone(phone);
			member.setSmartcardId(StringUtils.isNotEmpty(req.getSmartcardId()) ? req.getSmartcardId() :"");
			member.setUserName(StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() :"");
//			member.setPartyOrganization(party);
			//member.setPoliticCountenance(null!=req.getPoliticCountenance() ? req.getPoliticCountenance() : 5);
			member.setPhoneVerifyState(EntityContext.RECORD_STATE_VALID);
			member.setUserState(EntityContext.RECORD_STATE_VALID);
//			member.setSex(null != req.getSex() ? req.getSex() : null);
//			member.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "");
			member.setEducationRoleList(roleList);
//			member.setHeadImage(StringUtils.isNotEmpty(req.getHeadImage()) ? req.getHeadImage() :"");
//			member.setMeno(StringUtils.isNotEmpty(req.getMeno()) ? req.getMeno() : "");
			
			//String wechatOpenid = (String) request.getSession().getAttribute("wechatOpenid");

			educationMemberDao.save(member);
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	
	
	@Override
	public IResponse modifyEducationMember(EducationMemberReq req) {
		GenericResponse res = new GenericResponse();
//		Object userObj = getPrincipal(true);
//		if (null != userObj && userObj instanceof MemberEntity) {
		if(null!=req.getMemberId() ) {
			
			//MemberEntity member = (MemberEntity) userObj;
			EducationMemberEntity member = educationMemberDao.get(req.getMemberId());
			if (null != member) {
				if(StringUtils.isNotEmpty(req.getRoleIdList())) {
					String[] rolerId = req.getRoleIdList().split(",");
					List<EducationRoleEntity> roleList = new ArrayList<EducationRoleEntity>();
					for (String id : rolerId) {
						EducationRoleEntity role = educationRoleDao.get(Integer.valueOf(id));
						if (null != role) {
							roleList.add(role);
						}
					}
					member.setEducationRoleList(roleList);
				}

				
				member.setEmail(StringUtils.isNotEmpty(req.getEmail()) ? req.getEmail() : member.getEmail());
//			member.setHomePhone(
//						StringUtils.isNotEmpty(req.getHomePhone()) ? req.getHomePhone() : member.getHomePhone());
//				member.setNickName(
//						StringUtils.isNotEmpty(req.getNickName()) ? req.getNickName() : member.getNickName());
//				member.setOfficePhone(
//						StringUtils.isNotEmpty(req.getOfficePhone()) ? req.getOfficePhone() : member.getOfficePhone());
				member.setPhone(StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : member.getPhone());
				member.setSex(null != req.getSex() ? req.getSex() : member.getSex());
				member.setSmartcardId(
						StringUtils.isNotEmpty(req.getSmartcardId()) ? req.getSmartcardId() : member.getSmartcardId());
				member.setHeadImage(
						StringUtils.isNotEmpty(req.getHeadImage()) ? req.getHeadImage() : member.getHeadImage());
				member.setRegionCode(
						StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : member.getRegionCode());
				member.setUserName(
						StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() : member.getUserName());

				member.setMeno(StringUtils.isNotEmpty(req.getMeno()) ? req.getMeno() : member.getMeno());

				
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo("参数有误！！");
			}
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO );
		}
//		} else {
//			res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
//		}
		return res;
	}

	@Override
	public IResponse deleteEducationMember(EducationMemberReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getMemberId()) {
			EducationMemberEntity member = educationMemberDao.get(req.getMemberId());
			if (null != member) {
				member.setUserState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@SuppressWarnings("unused")
	@Override
	public IResponse getEducationMemberInfo(EducationMemberReq req) {
		GetEducationMemberInfoRes res = new GetEducationMemberInfoRes();
		EducationMemberForm memberForm = new EducationMemberForm();
		Object userObj = getPrincipal(true);
		//if (null != userObj) {
			EducationMemberEntity member = null;
			if(null != userObj && userObj instanceof EducationMemberEntity &&  null==req.getMemberId()) {
				member = (EducationMemberEntity) userObj;
			}else if((null == userObj &&null!=req.getMemberId()) || null != userObj && userObj instanceof EducationMemberEntity &&  null!=req.getMemberId()) {
				if(null==req.getMemberId()) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
					return res;
				}
				member = educationMemberDao.get(req.getMemberId());
				if(null == member) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
			}else {
				res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
				return res;
			}
//			if(null==req.getMemberId()) {
//				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//				return res;
//			}
			//EducationMemberEntity member = educationMemberDao.get(req.getMemberId());
			if(null == member) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			memberForm.setMemberId(member.getId());
//			memberForm.setPartyId(null!=member.getPartyOrganization() ? member.getPartyOrganization().getId():null);
			memberForm.setEmail(member.getEmail());
			memberForm.setHomePhone(member.getHomePhone());
			memberForm.setNickName(member.getNickName());
			memberForm.setOfficePhone(member.getOfficePhone());
			memberForm.setPhone(member.getPhone());
			memberForm.setRegistName(member.getRegistName());
			memberForm.setSex(member.getSex());
			memberForm.setSmartcardId(member.getSmartcardId());
			memberForm.setOrders(member.getOrders());
			memberForm.setHeadImage(StringUtils.isNotEmpty(member.getHeadImage()) ? member.getHeadImage() : "");
			memberForm.setUserName(StringUtils.isNotEmpty(member.getUserName()) ? member.getUserName() : "");


			memberForm.setMeno(StringUtils.isNotEmpty(member.getMeno()) ? member.getMeno() : "");
			
			memberForm.setRegionCode(StringUtils.isNotEmpty(member.getRegionCode()) ? member.getRegionCode() : "");
			res.setMemberForm(memberForm);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
//		}
		return res;
	}


	@SuppressWarnings({"unchecked" })
	@Override
	public IResponse findEducationMemberLoginState(EducationMemberLoginReq req) {
		EducationMemberLoginRes res = new EducationMemberLoginRes();
		if(StringUtils.isNotEmpty(req.getSmartcardId())) {
			Map<String, Object> smartcardCacheMap = (Map<String, Object>) CacheManagerUtil.getCacheByKey(req.getSmartcardId());
			if (null != smartcardCacheMap) {
				res.setMemberId((Integer)smartcardCacheMap.get("id"));
				res.setPhone((String)smartcardCacheMap.get("phone"));
				res.setUserName((String)smartcardCacheMap.get("userName"));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				CacheManagerUtil.clearByKey(req.getSmartcardId());
			}else {
				res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			}
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getEducationMemberLoginState(EducationMemberLoginReq req) {
		EducationMemberLoginRes res = new EducationMemberLoginRes();
		if(StringUtils.isNotEmpty(req.getPhone())) {
//			Subject phoneCacheMap = SecurityUtils.getSubject();
			ConcurrentHashMap<String, Object> cacheMap = ShiroHazelcastCache.shiroCache.get(req.getPhone());
			if (null != cacheMap) {

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo("已登录");

			}else {
				res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			}
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	@Override
	public IResponse memberRegister(EducationMemberReq req, HttpServletRequest request) {
		AddEducationMemberRes res = new AddEducationMemberRes();
		String phone = (StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : "");
		if (StringUtils.isEmpty(phone)) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "注册信息空！");
			return res;
		}
		// ObjectMapper objectMapper = new ObjectMapper();
		// JSONObject json = (JSONObject)
		// request.getSession().getAttribute("verifyCode");
		JsonNode json = null;
		try {
			if (null != request.getSession().getAttribute("verifyCode")) {
				json = new ObjectMapper().readTree((String) request.getSession().getAttribute("verifyCode"));
			}
		} catch (IOException e1) {
			e1.printStackTrace();
		}
		Map<String, Object> smsCache = null;
		if (json != null) {
//			Map<String, Object> cache = hazelcastClient.getMap("smsCache");
//			cache.put("date", "LOLO");
//			System.out.println(cache.get("date"));
//
//			Map<String, Object> cache = hazelcastClient.getMap("smsCache");
			Cache cache = cacheManager.getCache("smsCache");
			try {
				smsCache = mapper.readValue(cache.get(phone,String.class),Map.class);
				System.out.println(smsCache.get("verifyCode").toString());
			} catch (JsonProcessingException e) {
				throw new RuntimeException(e);
			}

			if (null == cache) {
				res.setRet(ResponseContext.RES_VERIF_CODE_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_VERIF_CODE_ERROR_INFO);
				return res;
			}
			if (null == smsCache | !req.getMessageCode().equals(smsCache.get("verifyCode").toString())) {
				res.setRet(ResponseContext.RES_VERIF_CODE_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_VERIF_CODE_ERROR_INFO);
				return res;
			}
		}else {
			res.setRet(ResponseContext.RES_VERIF_CODE_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_VERIF_CODE_ERROR_INFO);
			return res;
		}
		if ((System.currentTimeMillis() - (Long) smsCache.get("createTime")) > 1000L * 60 * contextInfo.smsEffective) {
			res.setRet(ResponseContext.RES_VERIF_CODE_OVERDUE_CODE);
			res.setRetInfo(ResponseContext.RES_VERIF_CODE_OVERDUE_INFO);
			return res;
		}
		EducationMemberEntity member = educationMemberDao.getUniqueBySql("SELECT * FROM t_account WHERE phone='"+req.getPhone()+"' and userState = 1",
				"");
		//		String wechatOpenid = (String) request.getSession().getAttribute("wechatOpenid");
		if (null != member) {

				// 普通注册流程
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo("对不起，您注册的用户名或手机号已被注册！");
				return res;

		} else {
			String[] rolerId = contextInfo.memberRoleId.split(",");
			List<EducationRoleEntity> roleList = new ArrayList<EducationRoleEntity>();
			for (String id : rolerId) {
				EducationRoleEntity role = educationRoleDao.get(Integer.valueOf(id));
				if (null != role) {
					roleList.add(role);
				}
			}

			member = new EducationMemberEntity();
			member.setLoginName(req.getPhone());
			member.setNickName(req.getPhone());

			//密码选填
			String password = req.getPassword();
			if(StringUtils.isNotBlank(password)) {
				try {
					member.setPassword(DigestUtil.sm3Digest(req.getPassword()));
					member.setLastPwdModifyTime(new Timestamp(new Date().getTime()));
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
			}
			member.setRegistName(req.getPhone());
			member.setPhone(phone);
			member.setSmartcardId(StringUtils.isNotEmpty(req.getSmartcardId()) ? req.getSmartcardId() :"");
			member.setUserName(StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() :phone);
			member.setPhoneVerifyState(EntityContext.RECORD_STATE_VALID);
			member.setUserState(EntityContext.RECORD_STATE_VALID);
			member.setSex(null != req.getSex() ? req.getSex() : 2);
			member.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "");
			member.setEducationRoleList(roleList);
			member.setHeadImage(StringUtils.isNotEmpty(req.getHeadImage()) ? req.getHeadImage() :"");
			member.setMeno(StringUtils.isNotEmpty(req.getMeno()) ? req.getMeno() : "");

			//String wechatOpenid = (String) request.getSession().getAttribute("wechatOpenid");

			educationMemberDao.save(member);
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse getPhoneVerificationCode(EducationMemberLoginReq req, HttpServletRequest request) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isEmpty(req.getPhone())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "手机号不能为空！");
			return res;
		}

		String phone = req.getPhone();

//		Map<String, Object> cache = hazelcastClient.getMap("memberAuthCache");
		Cache cache = cacheManager.getCache("memberAuthCache");

		// 将验证码保存到ehcache中。
//		CacheManager ehCacheManager = cacheManager.getCacheManager();
//		Cache cache = ehCacheManager.getCache("memberAuthCache");
//		Element element = cache.get(req.getPhone());
		Object element = cache.get(req.getPhone());
		// 首次发送短信的情况，允许发短信。
		int smsSendCount = 0;
		if (null == element) {
			smsSendCount = 1;
			AuthenticationInfoVo authInfo = new AuthenticationInfoVo();
			// String smsCode = CodeUtil.generateCode(6);
			authInfo.setPhone(phone);
			// authInfo.setSmsCode(smsCode);
			authInfo.setTryCount(1);
			authInfo.setAuthCount(0);// 首次获取短信时未进行账号密码的校验
			authInfo.setDate(new Date());
//			Element authElement = new Element(req.getPhone(), authInfo);
			cache.put(phone,authInfo);
			try {
				res = sendSms(req.getPhone(), request);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			// 之前发送过短信的情况
			AuthenticationInfoVo authInfo = null;
			try {
				authInfo = (AuthenticationInfoVo) element;
			} catch (Exception e) {
				throw new RuntimeException(e);
			}

			Date now = new Date();// 当前时间
			Date date = authInfo.getDate();
			// 1小时内，首次使用密码登录，导致之后首次发送短信时间为null，需要重新设置首次发送短信时间，和时间间隔，默认大于间隔时间
			long timeInterval = Long.parseLong(contextInfo.getSmsTimeInterval().toString());
			long min = timeInterval + 1;
			if (null == date) {
				date = new Date();
			} else {
				long l = now.getTime() - date.getTime();
				min = l / 60000;
			}
//	        long l = now.getTime() - date.getTime();
//	        long day = l / (24 * 60 * 60 * 1000);
//	        long hour = (l / (60 * 60 * 1000) - day * 24);
//	        long min = ((l / (60 * 1000)) - day * 24 * 60 - hour * 60);
			boolean isSameDay = isSameDay(now, date);
			// 对比当时间和当缓存保存的时间是否同一天，否则重置次数,防止止缓存设置超长时用到
			if (!isSameDay) {
				authInfo.setTryCount(0);
			}

			if ((min < timeInterval) && isSameDay) {
				res.setRet(ResponseContext.RES_PERM_TOPLIMIT_CODE);
				res.setRetInfo("您所发送短信过于频繁，请" + timeInterval + "分钟再发。");
				return res;
			} else if (authInfo.getTryCount() >= timeInterval) {
				// 发送短信条数达到上限，禁止发短信。
				res.setRet(ResponseContext.RES_PERM_TOPLIMIT_CODE);
				res.setRetInfo("您所发送短信次数已到达上限，请在1小时后再尝试。");
				return res;
			} else {
				// 发送短信条数不到上限，允许发短信
				smsSendCount = authInfo.getTryCount() + 1;
				// cache.remove(req.getPhone());
				// String smsCode = CodeUtil.generateCode(6);;
				// authInfo.setSmsCode(smsCode);
				authInfo.setTryCount(smsSendCount);
				authInfo.setDate(new Date());
//				Element authElement = new Element(req.getPhone(), authInfo);
				cache.put(phone,authInfo);
				try {
					res = sendSms(req.getPhone(), request);
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
		return res;
	}

	public GenericResponse sendSms(String phone, HttpServletRequest request) {
		GenericResponse res = new GenericResponse();
//      HttpSession session = request.getSession();
		Random r = new Random();
		ObjectMapper objectMapper = new ObjectMapper();
		// JSONObject json = new JSONObject();
		ObjectNode json = objectMapper.createObjectNode();
		// 生成6位验证码
		String time = System.currentTimeMillis() + "";
		String num = CodeUtil.codeGenerator((r.nextInt(999) + 1), 3, "")
				+ time.subSequence(time.length() - 3, time.length());
		String verifyCode = "温馨提示：您的短信验证码是：【" + num + "】," + contextInfo.smsEffective + "分钟内有效，请勿向他人泄露此验证码。";
		// 发送短信
		json.put("messageContent", verifyCode);
		json.put("userNumber", phone);
		json.put("scheduleTime", DateUtil.format(new Date(), 1));
		json.put("f", "1");

		Map<String, String> result = null;
		try {
			objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
			logger.info("--------------------------------------调用短信接口地址：" + contextInfo.smsInterfaceUrl + ";   参数："
					+ json.toString() + "-------------------------");
			result = HttpClientUtil.post(contextInfo.smsInterfaceUrl, objectMapper.writeValueAsString(json), "utf-8");
			logger.info("--------------------------------------调用短信接口返回：" + result
					+ "----------------------------------------");
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_SEND_SMS_CODE);
			res.setRetInfo(ResponseContext.RES_SEND_SMS_INFO);
			return res;
		}

//		if (!result.get("status").equals("success")) {// 发送短信失败
//			res.setRet(ResponseContext.RES_SEND_SMS_CODE);
//			res.setRetInfo(ResponseContext.RES_SEND_SMS_INFO);
//			return res;
//		}
		// 将验证码存到session中,同时存入创建时间
		// 以json存放，这里使用的是阿里的fastjson
		// HttpSession session = request.getSession();
		long createTime = System.currentTimeMillis();
		// json.clear();
		json = objectMapper.createObjectNode();
		json.put("mobile", phone);
		json.put("verifyCode", num);
		json.put("frequency", 0);
		json.put("createTime", createTime);
		objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		// 将认证码存入SESSION
		try {
			request.getSession().setAttribute("verifyCode", objectMapper.writeValueAsString(json));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
//		// 将验证码保存到ehcache中。
//		HazelcastInstance hazelcastClient = HazelcastClient.newHazelcastClient();
//		Map<String, Object> cache = hazelcastClient.getMap("smsCache");
		Cache cache = cacheManager.getCache("smsCache");
		try {
			String info = objectMapper.writeValueAsString(json);
			System.out.print(info);
			cache.put(phone,info);
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		if (result.toString().contains("status=faile")){
			res.setRet(ResponseContext.RES_SEND_SMS_CODE);
			res.setRetInfo(ResponseContext.RES_SEND_SMS_INFO);
		}else{
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		}

		return res;
	}

	public static boolean isSameDay(Date date1, Date date2) {
		if (date1 != null && date2 != null) {
			Calendar cal1 = Calendar.getInstance();
			cal1.setTime(date1);
			Calendar cal2 = Calendar.getInstance();
			cal2.setTime(date2);
			return cal1.get(0) == cal2.get(0) && cal1.get(1) == cal2.get(1) && cal1.get(6) == cal2.get(6);
		} else {
			throw new IllegalArgumentException("The date must not be null");
		}
	}

}
