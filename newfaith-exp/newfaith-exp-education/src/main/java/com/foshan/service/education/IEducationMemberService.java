package com.foshan.service.education;

import javax.servlet.http.HttpServletRequest;

import com.foshan.form.education.request.EducationMemberLoginReq;
import com.foshan.form.education.request.EducationMemberReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;

public interface IEducationMemberService {
	public IResponse getEducationMemberList(EducationMemberReq req);
	public IResponse addEducationMember(EducationMemberReq req);
	public IResponse modifyEducationMember(EducationMemberReq req);
	public IResponse deleteEducationMember(EducationMemberReq req);
	public IResponse getEducationMemberInfo(EducationMemberReq req);
	public IResponse findEducationMemberLoginState(EducationMemberLoginReq req);

    IResponse getEducationMemberLoginState(EducationMemberLoginReq req);

    public IResponse memberRegister(EducationMemberReq req, HttpServletRequest request);

	public IResponse getPhoneVerificationCode(EducationMemberLoginReq req,HttpServletRequest request);

}
