package com.foshan.service.education.impl;

import java.util.List;
import javax.annotation.Resource;

import com.foshan.dao.education.*;
import com.foshan.entity.UserEntity;
import com.foshan.model.permssion.PrincipalModel;
import com.foshan.util.ContextInfo;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.entity.education.EducationMemberEntity;
import com.foshan.form.RegionForm;
import com.foshan.model.permssion.AccountPrincipalModel;
import com.foshan.service.impl.GenericService;
import com.foshan.util.EducationContextInfo;
import org.springframework.cache.ehcache.EhCacheCacheManager;


public class GenericEducationService extends GenericService {

	@Autowired
	protected EducationContextInfo educationContextInfo;

	@Resource(name = "educationEventDao")
	protected IEducationEventDao educationEventDao;

    @Resource(name = "educationMemberDao")
    protected IEducationMemberDao educationMemberDao;

    @Resource(name = "educationRoleDao")
    protected IEducationRoleDao educationRoleDao;

	@Resource(name = "desireDao")
	protected IDesireDao desireDao;

//	@Resource(name = "cacheManager")
	protected EhCacheCacheManager cacheManager;

	//    @SuppressWarnings("unchecked")
//	protected HashMap<String, Object> partyColumnList(Set<ColumnEntity> columnSet, Integer serviceId, Integer columnId,
//			Integer depth, Integer visitFlag, String startDate, String endDate,Integer targetType,Integer columnState) {
//		HashMap<String, Object> columnList = new HashMap<String, Object>();
//		Integer serviceVisits = 0;
//		List<PartyColumnForm> columnFormList = new ArrayList<PartyColumnForm>();
//		depth--;
//		for (ColumnEntity c : columnSet) {
//			if(c instanceof PartyColumnEntity) {
//				PartyColumnEntity o = (PartyColumnEntity)c;
//				PartyColumnForm columnForm = new PartyColumnForm();
//				Set<ColumnEntity> columnSet1 = o.getSubColumnList();
//				if ((null==targetType || (null!=targetType&&o.getTargetType() == targetType)) &&
//						(null==columnState || (null!=columnState&&o.getColumnState() == columnState))) {
//					columnForm.setColumnId(o.getId());
//					columnForm.setColumnName(o.getColumnName());
//					columnForm.setServiceId(serviceId);
//					columnForm.setColumnCode(o.getColumnCode());
//					columnForm.setColumnInfo(o.getColumnInfo());
//					columnForm.setColumnType(o.getColumnType());
//					columnForm.setColumnImage(getAsset(o.getColumnImage()));
//					columnForm.setColumnPhoneImage(getAsset(o.getColumnPhoneImage()));
//					columnForm.setColumnState(o.getColumnState());
//					columnForm.setColumnLevel(o.getColumnLevel());
//					columnForm.setParentColumnId(columnId);
//					columnForm.setOrders(o.getOrders());
//					columnForm.setColumnInfo(o.getColumnInfo());
//					columnForm.setTargetType(o.getTargetType());
//					columnForm.setPartyOrganizationList(getPartyOrganizationFormList(o.getPartyOrganizationList()));
//					o.getPartyTopicList().forEach(t->{
//						PartyTopicForm topicForm = getPartyTopicForm(t,0,EntityContext.RECORD_STATE_VALID,1);
//						if(null != topicForm) {
//							columnForm.getPartyTopicFormList().add(topicForm);
//						}
//					});
//
//					// 查询子栏目
//					if (columnSet1.size() > 0 && depth >= 1) {
//						columnForm.getSubColumnList().addAll((List<ColumnForm>) partyColumnList(columnSet1, serviceId, o.getId(),
//								depth, visitFlag, startDate, endDate,targetType,columnState).get("columnList"));
//					}
//
//					columnFormList.add(columnForm);
//				}
//
//				// 根据访问量统计开关统计当前栏目下的分组访问量，且访问量统计与查询深度无关
//				if (visitFlag == EntityContext.COUNT_VISIT_ON) {
//					columnForm.setTotalVisits(getColumnVisit(serviceId, o.getId(), startDate, endDate));
//				}
//			}
//
//		}
//		columnFormList.sort(comparingInt(ColumnForm::getOrders));
//		//columnFormList.sort(Comparator.comparingInt(ColumnForm::getOrders).thenComparingInt(ColumnForm::getColumnId).reversed());
//		//columnFormList.sort(Comparator.comparingInt(ColumnForm::getColumnId).reversed().thenComparingInt(ColumnForm::getOrders));
//
//		columnList.put("columnList", columnFormList);
//		columnList.put("serviceVisits", serviceVisits);
//		return columnList;
//	}


	private RegionForm getRegionForm(RegionEntity region) {
		RegionForm regionForm = new RegionForm();
		if (null != region) {
			regionForm.setRegionId(region.getId());
			regionForm.setEndRegionCode(region.getEndRegionCode());
			regionForm.setParentRegionId(null != region.getParentRegion() ? region.getParentRegion().getId() : null);
			regionForm.setRegionCode(region.getRegionCode());
			regionForm.setParentRegionName(
					null != region.getParentRegion() ? region.getParentRegion().getRegionName() : null);
			regionForm.setRegionLevel(region.getRegionLevel());
			regionForm.setRegionName(region.getRegionName());
			regionForm.setStartRegionCode(region.getStartRegionCode());

		}
		return regionForm;
	}


	/**
	 * ******** modify by Genie,直接返回实体，参数失效。
	 */
	protected Object getPrincipal(boolean returnFullEntity){
		Subject curUser = SecurityUtils.getSubject();
		PrincipalCollection principals = curUser.getPrincipals();
		if(null!=principals&&!principals.isEmpty()){
			@SuppressWarnings("unchecked")
			List<Object> principalList = principals.asList();
			Object principal = (null != principalList.get(1) ? principalList.get(1) : null);
			if(principal instanceof AccountPrincipalModel) {
				AccountPrincipalModel partyUser = (AccountPrincipalModel) principal;
				String sql = "select * from t_account where id = '" + partyUser.getId() + "' and userState = '" + EntityContext.RECORD_STATE_VALID + "'";
                return educationMemberDao.getUniqueBySql(sql);
			}else if(principal instanceof PrincipalModel) {
				PrincipalModel shiroPlatformUser = (PrincipalModel) principal;
				String sql = "select * from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '" + EntityContext.RECORD_STATE_VALID + "'";
                return userDao.getUniqueBySql(sql);
			}else {
				return null;
			}
		}else{
			return null;
		}
	}



}
