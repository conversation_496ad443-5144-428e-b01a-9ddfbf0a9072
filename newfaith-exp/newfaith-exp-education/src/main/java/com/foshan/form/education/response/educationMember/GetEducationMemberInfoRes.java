package com.foshan.form.education.response.educationMember;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.education.EducationMemberForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取教育会员返回对象(GetEducationMemberInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetEducationMemberInfoRes extends BaseResponse {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = 2449872802497663047L;
	@ApiModelProperty(value = "会员信息",example="1")
	private EducationMemberForm memberForm;


	public GetEducationMemberInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}


}
