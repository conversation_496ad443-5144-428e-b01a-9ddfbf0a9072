package com.foshan.form.education;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="事件对象(EducationEventForm)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public  class EducationEventForm implements IForm {

    /**
     *
     */
    private static final long serialVersionUID = 4117022259152113401L;
    @ApiModelProperty(value = "事件ID",example="1")
    private Integer eventId;
    @ApiModelProperty(value = "事件类型")
    private String eventType;
    @ApiModelProperty(value = "事件详情")
    private String eventDetail;
    @ApiModelProperty(value = "事件Key")
    private String eventKey;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "智能卡号")
    private String smartCardId;
    @ApiModelProperty(value = "事件时间")
    private String eventTime;

    public EducationEventForm(Integer eventId, String eventType, String eventDetail, String eventKey, String mobile, String smartCardId, String eventTime) {
        this.eventId = eventId;
        this.eventType = eventType;
        this.eventDetail = eventDetail;
        this.eventKey = eventKey;
        this.mobile = mobile;
        this.smartCardId = smartCardId;
        this.eventTime = eventTime;
    }

    @Override
    public int compareTo(Object o) {
        // TODO Auto-generated method stub
        return 0;
    }
}
