package com.foshan.form.education.response.educationMember;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.education.EducationMemberForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取教育用户返回列表对象(GetEducationMemberListRes)")
@JsonInclude(Include.NON_NULL)
public class GetEducationMemberListRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8276545283113165503L;
	@ApiModelProperty(value = "用户对象列表")
	private List<EducationMemberForm> memberFormList = new ArrayList<EducationMemberForm>();

}
