package com.foshan.form.education.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="愿望(DesireReq)")
public  class DesireReq extends BasePageRequest {

    private static final long serialVersionUID = -1L;

    private Integer desireId;
    @ApiModelProperty(value = "愿望")
    private String desireContent;
    @ApiModelProperty(value = "审核状态 0-未审核 1-审核通过 2-审核不通过")
    private Integer checkState;
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "接受手机号")
    private String toPhone;
    @ApiModelProperty(value = "昵称")
    private String nikeName;
    @ApiModelProperty(value = "接受者昵称")
    private String toNikeName;




}