package com.foshan.form.education.response;

import com.foshan.form.education.DesireForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="愿望列表(GetDesireListRes)")
public class GetDesireListRes extends BasePageResponse {


    /**
     *
     */
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "愿望列表")
    private List<DesireForm> desireList = new ArrayList<DesireForm>();

}
