package com.foshan.form.education.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(value = "教育登录请求对象(MemberLoginReq)")
public class EducationMemberLoginReq extends BasePageRequest {
	/**
	 * 党员登陆请求
	 */
	private static final long serialVersionUID = 32636125721847096L;

	@ApiModelProperty(value = "手机号，密码、短信验证码、第三方平台获取token、电视客户端登陆方式必填")
	private String phone;
	@ApiModelProperty(value = "手机验证码,短信登录必填")
	private String messageCode;
	@ApiModelProperty(value = "图形验证码")
	private String verifyCode;
	@ApiModelProperty(value = "密码，密码登录类型必填")
	private String password;
	@ApiModelProperty(value = "性别：0--女 1--男 2--保密", example = "1")
	private Integer sex;
	@ApiModelProperty(value = "帐号")
	private String loginName;
	@ApiModelProperty(value = "智能卡号")
	private String smartcardId;
	@ApiModelProperty(value = "保留字段", example = "1")
	private Integer binding;
	@ApiModelProperty(value = "必填，验证方式，1-密码，5-微信小程序")
	private String type;
	@ApiModelProperty(value = "微信/小程序授权登陆的验证码，传入微信/小程序获取到的的code,微信/小程序登陆必填")
	private String wechatCode;
	@ApiModelProperty(value = "分配给第三方平台的令牌，客户端传入token校验,第三方平台获取token登陆必填")
	private String appToken;
	@ApiModelProperty(value = "微信小程序加密数据,微信小程序注册且登陆时必填")
	private String encryptedData;
	@ApiModelProperty(value = "用于解密微信小程序加密数据的初始向量,微信小程序注册且登陆时必填")
	private String iv;
	@ApiModelProperty(value = "用于校验微信小程序开放数据签名")
	private String signature;
	@ApiModelProperty(value = "登陆票据")
	private String loginTicket;


}
