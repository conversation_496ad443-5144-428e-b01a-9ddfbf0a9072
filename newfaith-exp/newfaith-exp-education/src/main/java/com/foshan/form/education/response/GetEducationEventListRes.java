package com.foshan.form.education.response;

import com.foshan.form.education.EducationEventForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="教育(GetEducationEventListRes)")
public class GetEducationEventListRes extends BasePageResponse {


    /**
     *
     */
    private static final long serialVersionUID = -246109781859895310L;

    @ApiModelProperty(value = "教育事件列表")
    private List<EducationEventForm> eventList = new ArrayList<EducationEventForm>();

}
