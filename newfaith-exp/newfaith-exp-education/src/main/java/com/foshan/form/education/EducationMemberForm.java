package com.foshan.form.education;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="党员对象(EducationMemberForm)")
@JsonInclude(Include.NON_NULL)
public  class EducationMemberForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1752932930329645213L;
	@ApiModelProperty(value = "会员ID", example = "1")
	private Integer memberId;
	@ApiModelProperty(value = "昵称")
	private String nickName;
	@ApiModelProperty(value = "email")
	private String email;
	@ApiModelProperty(value = "家庭电话")
	private String homePhone;
	@ApiModelProperty(value = "办公电话")
	private String officePhone;
	@ApiModelProperty(value = "手机号码")
	private String phone;
	@ApiModelProperty(value = "性别：0-女，1-男", example = "1")
	private Integer sex;
	@ApiModelProperty(value = "CA卡号")
	private String smartcardId;
	@ApiModelProperty(value = "注册名")
	private String registName;
	@ApiModelProperty(value = "是否默认电视客户端（最后一次登录的账号）：null/0-否，1-是", example = "1")
	private Integer isDefaultTvClient;
	@ApiModelProperty(value = "头像")
	private String headImage;
	@ApiModelProperty(value = "区域码")
	private String regionCode;
	@ApiModelProperty(value = "职位")
	private String position;
	@ApiModelProperty(value = "在职状态 0：在职； 1：退休；2：离职；", example = "1")
	private Integer workingState;
	@ApiModelProperty(value = "真实名字")
	private String userName;

	@ApiModelProperty(value = "排序值", example = "1")
	private Integer orders;

	@ApiModelProperty(value = "备注") 
	private String meno;

	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
