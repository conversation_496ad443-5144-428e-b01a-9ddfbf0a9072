package com.foshan.form.education.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取号码归属地返回对象(PhoneLocationRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PhoneLocationRes extends BaseResponse {

    @ApiModelProperty(value = "号码")
    private String number;
    @ApiModelProperty(value = "号段")
    private String num;
    @ApiModelProperty(value = "运营商")
    private String isp;
    @ApiModelProperty(value = "省")
    private String province;
    @ApiModelProperty(value = "市")
    private String city;
    @ApiModelProperty(value = "区号")
    private String cityCode;
    @ApiModelProperty(value = "邮编")
    private String zipCode;
    @ApiModelProperty(value = "经度")
    private String longitude;
    @ApiModelProperty(value = "纬度")
    private String latitude;


    public PhoneLocationRes(String number, String num, String isp, String province, String city, String cityCode, String zipCode, String longitude, String latitude) {
        this.number = number;
        this.num = num;
        this.isp = isp;
        this.province = province;
        this.city = city;
        this.cityCode = cityCode;
        this.zipCode = zipCode;
        this.longitude = longitude;
        this.latitude = latitude;
    }


}
