package com.foshan.form.education.request;

import com.foshan.form.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="教育(EducationEventReq)")
public  class EducationEventReq extends BasePageRequest {


    /**
     *
     */
    private static final long serialVersionUID = -7545641566368702735L;
    @ApiModelProperty(value = "ID",example="1")
    private Integer communityId;
    @ApiModelProperty(value = "事件类型",example="1")
    private String eventType;
    @ApiModelProperty(value = "事件详情",example="1")
    private String eventDetail;
    @ApiModelProperty(value = "事件Key",example="1")
    private String eventKey;
    @ApiModelProperty(value = "手机号",example="1")
    private String mobile;
    @ApiModelProperty(value = "秒数",example="1")
    private Integer seconds;
    @ApiModelProperty(value = "条数",example="1")
    private Integer count;




}