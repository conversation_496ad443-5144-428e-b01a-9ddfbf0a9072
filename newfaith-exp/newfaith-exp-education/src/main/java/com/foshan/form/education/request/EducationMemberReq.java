package com.foshan.form.education.request;

import javax.persistence.Column;

import com.foshan.form.request.BasePageRequest;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="党员请求参数(PartyMemberReq)")
public class EducationMemberReq extends BasePageRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = 5866048924666045091L;
	@ApiModelProperty(value = "党员Id",example="1")                   
	private Integer memberId;
	@Column(columnDefinition = "int(2) comment '在职状态'")    
	@ApiModelProperty(value = "在职状态 0：在职； 1：退休；2：离职；",example="1") 
	private Integer workingState;
	@ApiModelProperty(value = "备注") 
	private String meno;
	@ApiModelProperty(value = "用户真实姓名") 
	private String userName;
	@ApiModelProperty(value = "头像") 
	protected String headImage;
	@ApiModelProperty(value = "密码") 
	protected String password;
	@ApiModelProperty(value = "电话") 
	protected String phone;
	@ApiModelProperty(value = "状态 0--无效 1--有效",example="1") 
	protected Integer userState;
	@ApiModelProperty(value = "性别：0--女 1--男 2--保密", example = "1")
	private Integer sex;
	@ApiModelProperty(value = "验证码")
	private String messageCode;

	@ApiModelProperty(value = "邮箱") 
	private String email;
	@ApiModelProperty(value = "角色ID") 
	private String roleIdList;
	@ApiModelProperty(value = "会员ID")
	private String memberIdList;

}
