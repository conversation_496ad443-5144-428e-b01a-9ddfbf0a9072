package com.foshan.form.education.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="教育(EducationEventRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public  class AddEducationEventRes extends BaseResponse {


    /**
     *
     */
    private static final long serialVersionUID = 3799175463915365462L;
    @ApiModelProperty(value = "ID",example="1")
    private Integer educationEventId;


}
