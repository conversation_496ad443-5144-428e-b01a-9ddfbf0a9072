import os
import pymysql
import pandas as pd
import paramiko
import re
from sqlalchemy import create_engine
from urllib import parse

# 环境：python3.9

def print_hi(name):
    # 在下面的代码行中使用断点来调试脚本。
    print(f'Hi, {name}')  # 按 Ctrl+F8 切换断点。


# 视频文件重命名，mp4
# 子目录也一起改名
def vedio_rename(dir, df):
    # 打开需要把信息写入的文件
    txt = open("asset.txt", 'w', encoding='utf-8')
    for root, dirs, files in os.walk(dir):
        for file in files:
            if file.split(".")[1].lower() == "mp4" and df.index.values.tolist().__contains__(file.split(".")[0]):
                txt.write(file.split(".")[0]+','+df.loc[file.split(".")[0], 'newname'] + "\n")

                print("file:", root + "\\" + file, "改名")
                print(df.loc[file.split(".")[0], 'newname'])
                os.rename(root + "\\" + file,
                          root + "\\" + df.loc[file.split(".")[0], 'newname'] + '.' + file.split(".")[1].lower())
    txt.close()

def vedio_upload(username,password,hostname,port,dir):
    count = 0
    global t
    # ssh连接
    t = paramiko.Transport((hostname, port))
    t.connect(username=username, password=password)
    sftp = paramiko.SFTPClient.from_transport(t)
    # sftp上传
    for root, dirs, files in os.walk(dir):
        for file in files:
            #数据库里有匹配的才上传,规则：改名后全英文文件名
            CN_PATTERN = re.compile(u'[\u4e00-\u9fa5]')
            cn = CN_PATTERN.findall(file)
            if len(cn) == 0 and file.split(".")[1].lower() == "mp4":
                sftp.put(root + "\\" + file, "/data1/resource/jyj_resource/" + file)
                print(file + ' 上传完成')
                count += 1
    print('总上传：'+str(count))


# 按间距中的绿色按钮以运行脚本。
if __name__ == '__main__':
    # 配置开始，配置后运行
    # 视频文件所在路径
    dir = 'F:\\tmp'
    # 数据库数据查询，name是文件名，根据名称查找，需要指定新名字的列为newname
    sql = 'select assetName as name,previewAssetId as newname from t_source_asset'
    db_conn = pymysql.connect(
        host='localhost',
        port=3306,
        user='root',
        password='password',
        database='newfaith',
        charset='utf8'
    )
    
    engine = create_engine('mysql+pymysql://%s:%s@%s:%s/%s?charset=utf8'
                           % (MYSQL_USER, parse.quote_plus(MYSQL_PASSWORD), MYSQL_HOST, MYSQL_PORT, MYSQL_NAME))


    # ssh配置
    username = "username"
    password = "password"
    hostname = "*************"
    port = 22
    # 配置结束

    # 读取数据到df
    df = pd.read_sql(sql, engine, index_col="name")

    vedio_rename(dir, df)
    vedio_upload(username,password,hostname,port,dir)
    t.close()
