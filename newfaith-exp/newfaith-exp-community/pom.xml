<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.foshan</groupId>
		<artifactId>newfaith-exp</artifactId>
		<version>V200R001B03</version>
	</parent>
	<artifactId>newfaith-exp-community</artifactId>
	<name>newfaith-exp-community</name>

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>newfaith-plugins-invoice</artifactId>
			<version>${project.version}</version>
		</dependency>
		
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>newfaith-plugins-sms</artifactId>
			<version>${project.version}</version>
		</dependency>
		
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>newfaith-plugins-auditlog</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>newfaith-plugins-payment</artifactId>
			<version>${project.version}</version>
		</dependency>
		
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>newfaith-plugins-vote</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.newfaith</groupId>
			<artifactId>jep</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
		</dependency>
		
		<dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
        </dependency> 		
	</dependencies>

</project>
