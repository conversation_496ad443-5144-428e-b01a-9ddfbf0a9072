package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityReservationDateReq;
import com.foshan.form.community.response.communityReservationDate.GetCommunityReservationDateListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "活动预约日期模块")
@RestController
public class CommunityReservationDateController extends BaseCommunityController {

	// 获取活动预约日期列表
	@ApiOperation(value = "获取活动预约日期列表(getCommunityReservationDateList)", httpMethod = "POST", notes = "获取活动预约日期列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReservationDateList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReservationDateListRes getCommunityReservationDateList(@RequestBody CommunityReservationDateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReservationDateListRes res = (GetCommunityReservationDateListRes) communityReservationDateService.getCommunityReservationDateList(req);
		return res;
	}
	
	// 新增活动预约日期
	@ApiOperation(value = "新增活动预约日期(addCommunityReservationDate)", httpMethod = "POST", notes = "新增活动预约日期，isStart、ActivitiesId和activitiesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityReservationDate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityReservationDate(@RequestBody CommunityReservationDateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReservationDateService.addCommunityReservationDate(req);
		return res;
	}
	
	// 修改活动预约日期
	@ApiOperation(value = "修改活动预约日期(modifyCommunityReservationDate)", httpMethod = "POST", notes = "修改活动预约日期，isStart和reservationDateId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityReservationDate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityReservationDate(@RequestBody CommunityReservationDateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReservationDateService.modifyCommunityReservationDate(req);
		return res;
	}
	// 删除活动预约日期
	@ApiOperation(value = "删除活动预约日期(deleteCommunityReservationDate)", httpMethod = "POST", notes = "删除活动预约日期，reservationDateId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityReservationDate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityReservationDate(@RequestBody CommunityReservationDateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReservationDateService.deleteCommunityReservationDate(req);
		return res;
	}
	

}
