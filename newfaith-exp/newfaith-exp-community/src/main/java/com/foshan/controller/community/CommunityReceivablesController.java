package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityAddBreachReq;
import com.foshan.form.community.request.CommunityAddReceivablesForEstateReq;
import com.foshan.form.community.request.CommunityDeleteReceivablesByDetails;
import com.foshan.form.community.request.CommunityMeterAllocationReq;
import com.foshan.form.community.request.CommunityPreReceivablesReq;
import com.foshan.form.community.request.CommunityReceivablesReq;
import com.foshan.form.community.request.ExportExcelReq;
import com.foshan.form.community.response.communityReceivables.AddCommunityReceivablesRes;
import com.foshan.form.community.response.communityReceivables.CreatePayingInSlipPdfRes;
import com.foshan.form.community.response.communityReceivables.CreatePaymentQrCodeRes;
import com.foshan.form.community.response.communityReceivables.GetCommunityReceivablesInfoRes;
import com.foshan.form.community.response.communityReceivables.GetCommunityReceivablesListRes;
import com.foshan.form.community.response.communityReceivables.GetExportSendDiscListRes;
import com.foshan.form.community.response.communityReceivables.GetRentTrialMeterOperationListRes;
import com.foshan.form.community.response.communityReceivables.ModifyCommunityReceivablesRes;
import com.foshan.form.request.WxParameterReq;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "应收款模块")
@RestController
public class CommunityReceivablesController extends BaseCommunityController {

	// 手机端会员获取应收款列表
	@ApiOperation(value = "获取应收款列表(getCommunityReceivablesList)", httpMethod = "POST", notes = "获取应收款列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReceivablesList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceivablesListRes getCommunityReceivablesList(@RequestBody CommunityReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceivablesListRes res = (GetCommunityReceivablesListRes) communityReceivablesService.getCommunityReceivablesList(req);
		return res;
	}
	
	// 获取催缴费应收款账单
	@ApiOperation(value = " 获取催缴费应收款账单(getCommunityReceivablesBill)", httpMethod = "POST", notes = "催缴费账单对应的keyName必传")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReceivablesBill", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceivablesListRes getCommunityReceivablesBill(@RequestBody WxParameterReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceivablesListRes res = (GetCommunityReceivablesListRes) communityReceivablesService.getCommunityReceivablesBill(req);
		return res;
	}
	
	// 获取应收款列表
	@ApiOperation(value = "管理后台获取应收款列表(getAdminCommunityReceivablesList)", httpMethod = "POST", notes = "获取应收款列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getAdminCommunityReceivablesList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceivablesListRes getAdminCommunityReceivablesList(@RequestBody CommunityReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceivablesListRes res = (GetCommunityReceivablesListRes) communityReceivablesService.getAdminCommunityReceivablesList(req);
		return res;
	}
	
	// 新增应收款
	@ApiOperation(value = "新增应收款(addCommunityReceivables)", httpMethod = "POST", notes = "新增应收款")
	@ResponseBody
	@RequestMapping(value = "/addCommunityReceivables", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityReceivablesRes addCommunityReceivables(@RequestBody CommunityReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityReceivablesRes res = (AddCommunityReceivablesRes) communityReceivablesService.addCommunityReceivables(req);
		return res;
	}
	
	// 修改应收款
	@ApiOperation(value = "修改应收款(modifyCommunityReceivables)", httpMethod = "POST", notes = "修改应收款，communityReceivablesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityReceivables", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityReceivablesRes modifyCommunityReceivables(@RequestBody CommunityReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityReceivablesRes res = (ModifyCommunityReceivablesRes) communityReceivablesService.modifyCommunityReceivables(req);
		return res;
	}
	
	// 删除应收款
	@ApiOperation(value = "删除事件类型项目(deleteCommunityReceivables)", httpMethod = "POST", notes = "删除应收款，communityReceivablesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityReceivables", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityReceivables(@RequestBody CommunityReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReceivablesService.deleteCommunityReceivables(req);
		return res;
	}
	
	// 获取应收款详情
	@ApiOperation(value = "获取应收款详情(getCommunityReceivablesInfo)", httpMethod = "POST", notes = "获取应收款详情，communityReceivablesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReceivablesInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceivablesInfoRes getCommunityReceivablesInfo(@RequestBody CommunityReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceivablesInfoRes res = (GetCommunityReceivablesInfoRes) communityReceivablesService.getCommunityReceivablesInfo(req);
		return res;
	}
	
	// 生成预收款
	@ApiOperation(value = "生成预收(addPreReceivables)", httpMethod = "POST", notes = "生成预收，单元编号或单元ID不能同时为空，预算生成的月份数量需要填写；")
	@ResponseBody
	@RequestMapping(value = "/addPreReceivables", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceivablesListRes addRreReceivables(@RequestBody CommunityPreReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceivablesListRes res = (GetCommunityReceivablesListRes) communityReceivablesService.addPreReceivables(req);
		return res;
	}
	
	// 生成违约金
	@ApiOperation(value = "生成违约金(addBreachReceivables)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/addBreachReceivables", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addBreachReceivables(@RequestBody CommunityAddBreachReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReceivablesService.addBreachReceivables(req);
		return res;
	}
	
	// 根据单元ID生成当月管理费应收
	@ApiOperation(value = "根据单元ID生成当月管理费应收(addManagerReceivables)", httpMethod = "POST", notes = "如果当月管理费应收已经存在将会被覆盖，慎用")
	@ResponseBody
	@RequestMapping(value = "/addManagerReceivables", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addManagerReceivables(@RequestBody CommunityAddReceivablesForEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReceivablesService.addManagerReceivables(req);
		return res;
	}
	

	// 按小区、楼栋、单元Id撤消当月生成的管理费、违约金请求报文
	@ApiOperation(value = "按小区、楼栋、单元Id撤消当月生成的管理费请求报文(deleteManagerReceivablesByDetail)", httpMethod = "POST", notes = "撤消结果为三个条件字段的并集,发生过收款的应收不受影响，慎用")
	@ResponseBody
	@RequestMapping(value = "/deleteManagerReceivablesByDetail", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteManagerReceivablesByDetail(@RequestBody CommunityDeleteReceivablesByDetails req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReceivablesService.deleteManagerReceivablesByDetail(req);
		return res;
	}
	
	
	// 导出月份资料
//	@ApiOperation(value = "导出月份资料(exportSendDisc)", httpMethod = "POST", notes = "导出月份资料，传空{}即可；")
//	@ResponseBody
//	@RequestMapping(value = "/exportSendDisc", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public void exportSendDisc(@RequestBody CommunityReceivablesReq req, HttpServletRequest request, HttpServletResponse response)
//			throws IOException {
/*	
	@ApiOperation(value = "导出划账送盘(exportSendDisc)", httpMethod = "GET", notes = "导出划账送盘；formatList不能为空!")
	@ResponseBody
	@RequestMapping(value = "/exportSendDisc", method = {
			RequestMethod.GET })//, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE
	public void exportSendDisc(@ModelAttribute String month, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		CommunityReceivablesReq req = new CommunityReceivablesReq();
		List<Map<String,String>> list = new ArrayList<Map<String,String>>();
		Map<String,String> map = new HashMap<String,String>();
		map.put("bankNameList", "中国光大银行,农业银行");
		map.put("mergeState", "1");
		map.put("templateType", "1");
		list.add(map);
		map = new HashMap<String,String>();
		map.put("bankNameList", "工商银行");
		map.put("mergeState", "0");
		map.put("templateType", "0");
		list.add(map);
		map = new HashMap<String,String>();
		map.put("bankNameList", "农村信用合作社");
		map.put("mergeState", "0");
		map.put("templateType", "2");
		list.add(map);
		req.setFormatList(list);
		byte[] data = communityReceivablesService.exportSendDisc(req,response);
		response.reset();  
        response.setHeader("Content-Disposition", "attachment; filename=\"sendDisc.zip\"");  
        response.addHeader("Content-Length", "" + data.length);  
        response.setContentType("application/octet-stream; charset=UTF-8");  
        IOUtils.write(data, response.getOutputStream());  

	}*/

	
	// 导出划账送盘
	@ApiOperation(value = "导出划账送盘(exportSendDisc)", httpMethod = "POST", notes = "导出划账送盘，communityReceivablesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/exportSendDisc", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportSendDisc(@RequestBody CommunityReceivablesReq req, HttpServletRequest request, HttpServletResponse response)
			throws JsonProcessingException {
		communityReceivablesService.exportSendDisc(req,response);
	}

	// 获取划账送盘数据列表
	@ApiOperation(value = "获取划账送盘数据列表(getExportSendDiscList)", httpMethod = "POST", notes = "获取划账送盘数据列表，communityReceivablesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getExportSendDiscList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetExportSendDiscListRes getExportSendDiscList(@RequestBody CommunityReceivablesReq req, HttpServletRequest request, HttpServletResponse response)
			throws JsonProcessingException {
		GetExportSendDiscListRes res = (GetExportSendDiscListRes) communityReceivablesService.getExportSendDiscList(req);
		return res;
	}
	
	// 电视端生成交款二维码
	@ApiOperation(value = "电视端生成交款二维码(createPaymentQrCode)", httpMethod = "POST", notes = "电视端生成交款二维码，communityReceivablesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/createPaymentQrCode", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public CreatePaymentQrCodeRes createPaymentQrCode(@RequestBody CommunityReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		CreatePaymentQrCodeRes res = (CreatePaymentQrCodeRes) communityReceivablesService.createPaymentQrCode(req);
		return res;
	}
	
	// 生成交款通知单
	@ApiOperation(value = "生成交款通知单(createPayingInSlipPdf)", httpMethod = "POST", notes = "生成交款通知单，communityReceivablesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/createPayingInSlipPdf", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public CreatePayingInSlipPdfRes createPayingInSlipPdf(@RequestBody CommunityReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		CreatePayingInSlipPdfRes res = (CreatePayingInSlipPdfRes) communityReceivablesService.createPayingInSlipPdf(req);
		return res;
	}
	
	// 生成交款通知单
	@ApiOperation(value = "生成交款通知单(createPayingInSlipPdfTest)", httpMethod = "POST", notes = "生成交款通知单，communityReceivablesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/createPayingInSlipPdfTest", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public CreatePayingInSlipPdfRes createPayingInSlipPdfTest(@RequestBody CommunityReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		CreatePayingInSlipPdfRes res = (CreatePayingInSlipPdfRes) communityReceivablesService.createPayingInSlipPdfTest(req);
		return res;
	}
	
	// 根据小区、楼栋、单元Id，试运算水电表操作
	@ApiOperation(value = "根据小区、楼栋、单元Id，试运算水电表操作(rentTrialMeterOperation)", httpMethod = "POST", notes = "根据小区、楼栋、单元Id，试运算水电表操作")
	@ResponseBody
	@RequestMapping(value = "/rentTrialMeterOperation", method = {
				RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse rentTrialMeterOperation(@RequestBody CommunityAddReceivablesForEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReceivablesService.rentTrialMeterOperation(req);
		return res;
	}
	
	// 获取租赁单元试运算水电表列表
	@ApiOperation(value = "获取租赁单元试运算水电表列表(getRentTrialMeterOperationList)", httpMethod = "POST", notes = "获取租赁单元试运算水电表列表")
	@ResponseBody
	@RequestMapping(value = "/getRentTrialMeterOperationList", method = {
				RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetRentTrialMeterOperationListRes getRentTrialMeterOperationList(@RequestBody CommunityMeterAllocationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetRentTrialMeterOperationListRes res = (GetRentTrialMeterOperationListRes) communityReceivablesService.getRentTrialMeterOperationList(req);
		return res;
	}
	
	// 租赁单元根据试运算生成应收
	@ApiOperation(value = "租赁单元根据试运算生成应收(submitRentMeterAllocation)", httpMethod = "POST", notes = "租赁单元根据试运算生成应收")
	@ResponseBody
	@RequestMapping(value = "/submitRentMeterAllocation", method = {
				RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse submitRentMeterAllocation(@RequestBody CommunityMeterAllocationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReceivablesService.submitRentMeterAllocation();
		return res;
	}
	
	// 根据单元ID生成租金费应收
	@ApiOperation(value = "根据单元ID生成租金费应收(addRentReceivables)", httpMethod = "POST", notes = "如果当月管理费应收已经存在将会被覆盖，慎用")
	@ResponseBody
	@RequestMapping(value = "/addRentReceivables", method = {
				RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addRentReceivables(@RequestBody CommunityAddReceivablesForEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReceivablesService.addRentReceivables(req);
		return res;
	}
	
	// 导出租赁单元试运算列表
	@ApiOperation(value = "导出租赁单元试运算列表(exportRentTrialMeterOperation)", httpMethod = "POST", notes = "导出租赁单元试运算列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/exportRentTrialMeterOperation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportRentTrialMeterOperation(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityReceivablesService.exportRentTrialMeterOperation(req,response);
	}
	
	// 向欠费用户发短信
	@ApiOperation(value = "向欠费用户发短信(sendReceivablesSms)", httpMethod = "POST", notes = "向欠费用户发短信，endTime选填（不填默认当天日期）"
			+ "、estateIdList传空全发（注意）{}即可；")
	@ResponseBody
	@RequestMapping(value = "/sendReceivablesSms", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse sendReceivablesSms(@RequestBody CommunityReceivablesReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		GenericResponse res = (GenericResponse) communityReceivablesService.sendReceivablesSms(req);
		return res;
	}
	
	// 更新property表的reservedField字段的IsSendMessage
//	@ApiOperation(value = "更新property表的reservedField字段的IsSendMessage(updateReservedField)", httpMethod = "POST", notes = "更新property表的reservedField字段的IsSendMessage")
//	@ResponseBody
//	@RequestMapping(value = "/updateReservedField", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public GenericResponse updateReservedField(@RequestBody CommunityReceivablesReq req, HttpServletRequest request, HttpServletResponse response)
//			throws IOException, ParseException {
//		GenericResponse res = (GenericResponse) communityReceivablesService.updateReservedField();
//		return res;
//	}
}
