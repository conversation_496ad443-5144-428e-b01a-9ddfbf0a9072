package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityMeterAttributesReq;
import com.foshan.form.community.response.communityMeterAttributes.AddCommunityMeterAttributesRes;
import com.foshan.form.community.response.communityMeterAttributes.GetCommunityMeterAttributesInfoRes;
import com.foshan.form.community.response.communityMeterAttributes.GetCommunityMeterAttributesListRes;
import com.foshan.form.community.response.communityMeterAttributes.ModifyCommunityMeterAttributesRes;
import com.foshan.form.community.response.communityMeterPriceHistory.GetCommunityMeterPriceHistoryListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "表属性模块")
@RestController
public class CommunityMeterAttributesController extends BaseCommunityController {

	// 获取表属性列表
	@ApiOperation(value = "获取表属性列表(getCommunityMeterAttributesList)", httpMethod = "POST", notes = "获取表属性列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMeterAttributesList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterAttributesListRes getCommunityMeterAttributesList(@RequestBody CommunityMeterAttributesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMeterAttributesListRes res = (GetCommunityMeterAttributesListRes) communityMeterAttributesService.getCommunityMeterAttributesList(req);
		return res;
	}
	
	// 新增表属性
	@ApiOperation(value = "新增事件类型项目(addCommunityMeterAttributes)", httpMethod = "POST", notes = "新增表属性")
	@ResponseBody
	@RequestMapping(value = "/addCommunityMeterAttributes", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityMeterAttributesRes addCommunityMeterAttributes(@RequestBody CommunityMeterAttributesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityMeterAttributesRes res = (AddCommunityMeterAttributesRes) communityMeterAttributesService.addCommunityMeterAttributes(req);
		return res;
	}
	
	// 修改表属性
	@ApiOperation(value = "修改表属性(modifyCommunityMeterAttributes)", httpMethod = "POST", notes = "修改表属性，communityMeterAttributesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityMeterAttributes", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityMeterAttributesRes modifyCommunityMeterAttributes(@RequestBody CommunityMeterAttributesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityMeterAttributesRes res = (ModifyCommunityMeterAttributesRes) communityMeterAttributesService.modifyCommunityMeterAttributes(req);
		return res;
	}
	
	// 删除表属性
	@ApiOperation(value = "删除事件类型项目(deleteCommunityMeterAttributes)", httpMethod = "POST", notes = "删除表属性，communityMeterAttributesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityMeterAttributes", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityMeterAttributes(@RequestBody CommunityMeterAttributesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterAttributesService.deleteCommunityMeterAttributes(req);
		return res;
	}
	
	// 获取表属性详情
	@ApiOperation(value = "获取表属性详情(getCommunityMeterAttributesInfo)", httpMethod = "POST", notes = "获取表属性详情，communityMeterAttributesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMeterAttributesInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterAttributesInfoRes getCommunityMeterAttributesInfo(@RequestBody CommunityMeterAttributesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMeterAttributesInfoRes res = (GetCommunityMeterAttributesInfoRes) communityMeterAttributesService.getCommunityMeterAttributesInfo(req);
		return res;
	}
	
	// 获取表调价历史列表
	@ApiOperation(value = "获取表调价历史列表(getCommunityMeterPriceHistoryList)", httpMethod = "POST", notes = "获取表调价历史列表；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMeterPriceHistoryList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterPriceHistoryListRes getCommunityMeterPriceHistoryList(@RequestBody CommunityMeterAttributesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMeterPriceHistoryListRes res = (GetCommunityMeterPriceHistoryListRes) communityMeterAttributesService.getCommunityMeterPriceHistoryList(req);
		return res;
	}
   
	// 表属性绑定表
	@ApiOperation(value = "表属性绑定表(attributesBindingMeter)", httpMethod = "POST", notes = "表属性绑定表，attributesBindingMeter、meterIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/attributesBindingMeter", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse attributesBindingMeter(@RequestBody CommunityMeterAttributesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterAttributesService.attributesBindingMeter(req);
		return res;
	}
	
	// 解绑表属性表
	@ApiOperation(value = "解绑表属性表(attributesUnbindingMeter)", httpMethod = "POST", notes = "解绑表属性表，attributesBindingMeter、meterIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/attributesUnbindingMeter", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse attributesUnbindingMeter(@RequestBody CommunityMeterAttributesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterAttributesService.attributesUnbindingMeter(req);
		return res;
	}
}
