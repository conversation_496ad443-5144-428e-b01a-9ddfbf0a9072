package com.foshan.controller.community;


import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityPayItemsReq;
import com.foshan.form.community.response.communityPayItems.AddCommunityPayItemsRes;
import com.foshan.form.community.response.communityPayItems.GetCommunityPayItemsInfoRes;
import com.foshan.form.community.response.communityPayItems.GetCommunityPayItemsListByPropertyRes;
import com.foshan.form.community.response.communityPayItems.GetCommunityPayItemsListRes;
import com.foshan.form.community.response.communityPayItems.ModifyCommunityPayItemsRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "收费项目设定模块")
@RestController
public class CommunityPayItemsController extends BaseCommunityController {

	// 获取收费项目设定列表
	@ApiOperation(value = "获取收费项目设定列表(getCommunityPayItemsList)", httpMethod = "POST", notes = "获取收费项目设定列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPayItemsList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPayItemsListRes getCommunityPayItemsList(@RequestBody CommunityPayItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPayItemsListRes res = (GetCommunityPayItemsListRes) communityPayItemsService.getCommunityPayItemsList(req);
		return res;
	}
	
	// 新增收费项目设定
	@ApiOperation(value = "新增事件类型项目(addCommunityPayItems)", httpMethod = "POST", notes = "新增收费项目设定")
	@ResponseBody
	@RequestMapping(value = "/addCommunityPayItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityPayItemsRes addCommunityPayItems(@RequestBody CommunityPayItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityPayItemsRes res = (AddCommunityPayItemsRes) communityPayItemsService.addCommunityPayItems(req);
		return res;
	}
	
	// 修改收费项目设定
	@ApiOperation(value = "修改收费项目设定(modifyCommunityPayItems)", httpMethod = "POST", notes = "修改收费项目设定，communityPayItemsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityPayItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityPayItemsRes modifyCommunityPayItems(@RequestBody CommunityPayItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityPayItemsRes res = (ModifyCommunityPayItemsRes) communityPayItemsService.modifyCommunityPayItems(req);
		return res;
	}
	
	// 删除收费项目设定
	@ApiOperation(value = "删除事件类型项目(deleteCommunityPayItems)", httpMethod = "POST", notes = "删除收费项目设定，communityPayItemsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityPayItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityPayItems(@RequestBody CommunityPayItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityPayItemsService.deleteCommunityPayItems(req);
		return res;
	}
	
	// 获取收费项目设定详情
	@ApiOperation(value = "获取收费项目设定详情(getCommunityPayItemsInfo)", httpMethod = "POST", notes = "获取收费项目设定详情，communityPayItemsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPayItemsInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPayItemsInfoRes getCommunityPayItemsInfo(@RequestBody CommunityPayItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPayItemsInfoRes res = (GetCommunityPayItemsInfoRes) communityPayItemsService.getCommunityPayItemsInfo(req);
		return res;
	}
	
	// 根据单元获取收费项目
	@ApiOperation(value = "根据单元获取收费项目(getCommunityPayItemsListByProperty)", httpMethod = "POST", notes = "根据单元获取收费项目，propertyId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPayItemsListByProperty", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPayItemsListByPropertyRes getCommunityPayItemsListByProperty(@RequestBody CommunityPayItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPayItemsListByPropertyRes res = (GetCommunityPayItemsListByPropertyRes) communityPayItemsService.getCommunityPayItemsListByProperty(req);
		return res;
	}
   
}
