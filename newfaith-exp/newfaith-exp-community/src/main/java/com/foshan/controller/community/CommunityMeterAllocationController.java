package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityMeterAllocationReq;
import com.foshan.form.community.response.communityMeterAllocation.MeterAllocationRes;
import com.foshan.form.community.response.communityMeterAllocation.ExportAllocationListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "公摊模块")
@RestController
public class CommunityMeterAllocationController extends BaseCommunityController {
	// 公摊总表分摊试算
	@ApiOperation(value = "公摊总表分摊试算(calTotalAllocation)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/calTotalAllocation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public MeterAllocationRes calTotalAllocation(@RequestBody CommunityMeterAllocationReq req,
			HttpServletRequest request) throws JsonProcessingException {
		MeterAllocationRes res = (MeterAllocationRes) communityMeterAllocationService.calTotalAllocation(req);
		return res;
	}

	// 提交总表分摊试算
	@ApiOperation(value = "提交总表分摊试算(submitTotalAllocation)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/submitTotalAllocation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse submitTotalAllocation(@RequestBody CommunityMeterAllocationReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterAllocationService.submitTotalAllocation(req);
		return res;
	}
	
	// 获取总表分摊列表
	@ApiOperation(value = "获取总表分摊列表(getTotalAllocationList)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/getTotalAllocationList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public MeterAllocationRes getTotalAllocationList(@RequestBody CommunityMeterAllocationReq req,
			HttpServletRequest request) throws JsonProcessingException {
		MeterAllocationRes res = (MeterAllocationRes) communityMeterAllocationService.getTotalAllocationList(req);
		return res;
	}
	
	// 获取总表分摊列表
	@ApiOperation(value = "获取总表单元分摊列表(getMeterAllocationItemList)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/getMeterAllocationItemList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public MeterAllocationRes getMeterAllocationItemList(@RequestBody CommunityMeterAllocationReq req,
			HttpServletRequest request) throws JsonProcessingException {
		MeterAllocationRes res = (MeterAllocationRes) communityMeterAllocationService.getMeterAllocationItemList(req);
		return res;
	}

	// 公摊总表单元分摊试算
	@ApiOperation(value = "公摊总表分摊试算(calItemAllocation)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/calItemAllocation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public MeterAllocationRes calItemAllocation(@RequestBody CommunityMeterAllocationReq req,
			HttpServletRequest request) throws JsonProcessingException {
		MeterAllocationRes res = (MeterAllocationRes) communityMeterAllocationService.calItemAllocation(req);
		return res;
	}

	// 公摊总表单元分摊试算
	@ApiOperation(value = "公摊总表分摊试算(refreshItemAllocation)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/refreshItemAllocation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse refreshItemAllocation(@RequestBody CommunityMeterAllocationReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterAllocationService.refreshItemAllocation(req);
		return res;
	}

	// 提交单元总表分摊结果
	@ApiOperation(value = "提交总表分摊试算(submitItemAllocation)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/submitItemAllocation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse submitItemAllocation(@RequestBody CommunityMeterAllocationReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterAllocationService.submitItemAllocation(req);
		return res;
	}

	// 导出单元总表分摊结果列表
	@ApiOperation(value = "导出单元总表分摊结果列表(exportAllocationList)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/exportAllocationList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ExportAllocationListRes exportAllocationList(@RequestBody CommunityMeterAllocationReq req,HttpServletResponse response,
			HttpServletRequest request) throws JsonProcessingException {
		ExportAllocationListRes res = (ExportAllocationListRes)communityMeterAllocationService.exportAllocationList(req);
		return res;
	}

	// 提交单元分摊费用应收账单
	@ApiOperation(value = "提交总表分摊试算(submitAllocationReceivables)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/submitAllocationReceivables", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse submitAllocationReceivables(@RequestBody CommunityMeterAllocationReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterAllocationService.submitAllocationReceivables(req);
		return res;
	}
	
	// 导出总表分摊结果
	@ApiOperation(value = "导出总表分摊结果(transExcel)", httpMethod = "POST", notes = "应收未收报表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/transExcel", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void transExcel(@RequestBody CommunityMeterAllocationReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityMeterAllocationService.transExcel(req,response);
	}
}
