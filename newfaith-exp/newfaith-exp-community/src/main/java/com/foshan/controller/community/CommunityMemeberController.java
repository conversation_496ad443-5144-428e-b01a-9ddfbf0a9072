package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import com.foshan.form.community.request.MergeMemberInfoReq;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityMemberReq;
import com.foshan.form.community.response.communityMember.AddCommunityMemberRes;
import com.foshan.form.community.response.communityMember.GetCommunityMemberInfoRes;
import com.foshan.form.community.response.communityMember.GetCommunityMemberListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "社区会员模块")
@RestController
public class CommunityMemeberController extends BaseCommunityController {
	

	// 获取社区会员列表
	@ApiOperation(value = "获取社区会员列表(getCommunityMemberList)", httpMethod = "POST", notes = "获取社区会员列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMemberList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMemberListRes getCommunityMemberList(@RequestBody CommunityMemberReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMemberListRes res = (GetCommunityMemberListRes) communityMemberService.getCommunityMemberList(req);
		return res;
	}
	// 新增社区会员列表
	@ApiOperation(value = "新增社区会员列表(addCommunityMember)", httpMethod = "POST", notes = "新增社区会员列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityMember", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityMemberRes addCommunityMemberList(@RequestBody CommunityMemberReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityMemberRes res = (AddCommunityMemberRes) communityMemberService.addCommunityMember(req);
		return res;
	}
	// 修改社区会员
	@ApiOperation(value = "修改社区会员(modifyCommunityMember)", httpMethod = "POST", notes = "修改社区会员，CommunityMemberId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityMember", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityMember(@RequestBody CommunityMemberReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberService.modifyCommunityMember(req);
		return res;
	}

	// 合并社区会员
	@ApiOperation(value = "合并社区会员信息(mergeCommunityMember)", httpMethod = "POST", notes = "合并社区会员信息，sourceMemberId被合并会员Id,targetMemberId目标会员Id，不能为空；目前只实现了合并注册电话和微信小程序openId信息")
	@ResponseBody
	@RequestMapping(value = "/mergeCommunityMember", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse mergeCommunityMember(@RequestBody MergeMemberInfoReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberService.mergeCommunityMember(req);
		return res;
	}

	// 删除社区会员
	@ApiOperation(value = "删除社区会员(deleteCommunityMember)", httpMethod = "POST", notes = "删除社区会员，CommunityMemberId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityMember", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityMember(@RequestBody CommunityMemberReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberService.deleteCommunityMember(req);
		return res;
	}

	// 获取详情社区会员
	@ApiOperation(value = "获取详情社区会员(getCommunityMemberInfo)", httpMethod = "POST", notes = "获取详情社区会员，CommunityMemberId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMemberInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMemberInfoRes getCommunityMemberInfo(@RequestBody CommunityMemberReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMemberInfoRes res = (GetCommunityMemberInfoRes) communityMemberService.getCommunityMemberInfo(req);
		return res;
	}

}
