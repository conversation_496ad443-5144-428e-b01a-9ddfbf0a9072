package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.UploadedFile;
import com.foshan.form.community.request.CommunityEstateReq;
import com.foshan.form.community.request.CommunityProprietorInfoReq;
import com.foshan.form.community.response.communityDistrict.GetCommunityDistrictListRes;
import com.foshan.form.community.response.communityEstate.GetCommunityEstateInfoRes;
import com.foshan.form.community.response.communityEstate.GetCommunityEstateListRes;
import com.foshan.form.community.response.communityEstate.GetCommunityEstateTreeRes;
import com.foshan.form.community.response.communityEstate.GetCommunityEstateUnitCodeListRes;
import com.foshan.form.community.response.communityProprietorInfo.GetProprietorInfoListRes;
import com.foshan.form.community.response.communityProprietorInfo.GetProprietorInfoRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "房产单元模块")
@RestController
public class CommunityEstateController extends BaseCommunityController {
	// 获取房产单元列表
	@ApiOperation(value = "获取房产单元列表(getCommunityEstateList)", httpMethod = "POST", notes = "获取房产单元列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEstateList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEstateListRes getCommunityEstateList(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEstateListRes res = (GetCommunityEstateListRes) communityEstateService.getCommunityEstateList(req);
		return res;
	}
	
	@ApiOperation(value = "获取认证房产单元列表(getCommunityAuthenticationEstateList)", httpMethod = "POST", notes = "获取认证房产单元列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityAuthenticationEstateList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEstateListRes getCommunityAuthenticationEstateList(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		req.setIsAcceptanceReceive(1);
		GetCommunityEstateListRes res = (GetCommunityEstateListRes) communityEstateService.getCommunityEstateList(req);
		return res;
	}
	
	// 获取租赁房产单元列表
	@ApiOperation(value = "获取租赁房产单元列表(getCommunityRentEstateList)", httpMethod = "POST", notes = "获取租赁房产单元列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityRentEstateList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEstateListRes getCommunityRentEstateList(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEstateListRes res = (GetCommunityEstateListRes) communityEstateService.getCommunityEstateList(req);
		return res;
	}
	
	// 新增房产单元
	@ApiOperation(value = "新增房产单元(addCommunityEstate)", httpMethod = "POST", notes = "新增房产单元，propertyName和buildingId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityEstate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityEstate(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEstateService.addCommunityEstate(req);
		return res;
	}
	
	// 修改房产单元
	@ApiOperation(value = "修改房产单元(modifyCommunityEstate)", httpMethod = "POST", notes = "修改房产单元，EstateId、buildingId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityEstate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityEstate(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEstateService.modifyCommunityEstate(req);
		return res;
	}
	
	// 修改租赁单元
	@ApiOperation(value = "修改租赁单元(modifyCommunityRentEstate)", httpMethod = "POST", notes = "修改租赁单元，EstateId、buildingId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityRentEstate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityRentEstate(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEstateService.modifyCommunityEstate(req);
		return res;
	}
	
	// 删除房产单元
	@ApiOperation(value = "删除房产单元(deleteCommunityEstate)", httpMethod = "POST", notes = "删除房产单元，EstateId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityEstate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityEstate(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEstateService.deleteCommunityEstate(req);
		return res;
	}
	
	// 获取房产单元详情
	@ApiOperation(value = "获取房产单元详情(getCommunityEstateInfo)", httpMethod = "POST", notes = "获取房产单元详情，EstateId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEstateInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEstateInfoRes getCommunityEstateInfo(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEstateInfoRes res = (GetCommunityEstateInfoRes) communityEstateService.getCommunityEstateInfo(req);
		return res;
	}
	
	// 获取租赁房产单元详情
	@ApiOperation(value = "获取租赁房产单元详情(getCommunityRentEstateInfo)", httpMethod = "POST", notes = "获取租赁房产单元详情，EstateId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityRentEstateInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEstateInfoRes getCommunityRentEstateInfo(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEstateInfoRes res = (GetCommunityEstateInfoRes) communityEstateService.getCommunityEstateInfo(req);
		return res;
	}
	
	// 获取房产单元树
	@ApiOperation(value = "获取房产树(getCommunityEstateTree)", httpMethod = "POST", notes = "获取房产单元树，EstateId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEstateTree", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEstateTreeRes getCommunityEstateTree(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEstateTreeRes res = (GetCommunityEstateTreeRes) communityEstateService.getCommunityEstateTree(req);
		return res;
	}
	
	
	// 查询最接近的前10个房产UnitCode
	@ApiOperation(value = "根据输入的字符查询最接近的前10个房产UnitCode列表(GetCommunityEstateUnitCodeList)", httpMethod = "POST", notes = "获取房产UnitCode列表，unitCode必传；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEstateUnitCodeList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEstateUnitCodeListRes getCommunityEstateUnitCodeList(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEstateUnitCodeListRes res = (GetCommunityEstateUnitCodeListRes) communityEstateService.getCommunityEstateUnitCodeList(req);
		return res;
	}

	
	
	// 单元绑定收费项目
	@ApiOperation(value = "单元绑定收费项目(estateBindingPayItems)", httpMethod = "POST", notes = "单元绑定收费项目，EstateId、payItemsIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/estateBindingPayItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse estateBindingPayItems(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEstateService.estateBindingPayItems(req);
		return res;
	}
	
	// 根据表或收费项目获取绑定单元数据
	@ApiOperation(value = "根据表或收费项目获取绑定单元数据(getSelectedCommunityEstateTree)", httpMethod = "POST", notes = "根据表或收费项目获取绑定单元数据，meterId、payItemsId不能同时为空；")
	@ResponseBody
	@RequestMapping(value = "/getSelectedCommunityEstateTree", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityDistrictListRes getSelectedCommunityEstateTree(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityDistrictListRes res = (GetCommunityDistrictListRes) communityEstateService.getSelectedCommunityEstateTree(req);
		return res;
	}
	
	// 单元解绑收费项目
	@ApiOperation(value = "单元解绑收费项目(estateUnbindingPayItems)", httpMethod = "POST", notes = "单元解绑收费项目，EstateId、payItemsIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/estateUnbindingPayItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse estateUnbindingPayItems(@RequestBody CommunityEstateReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEstateService.estateUnbindingPayItems(req);
		return res;
	}
	
	//导出单元/车位列表
	@ApiOperation(value = "导出单元/车位列表(exportEstateList)", httpMethod = "POST", notes = "导出单元/车位列表")
	@ResponseBody
	@RequestMapping(value = "/exportEstateList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse exportEstateList(@RequestBody CommunityEstateReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		GenericResponse  res = (GenericResponse) communityEstateService.exportEstateList(req,response);
		return res;
	}
	
	
	//根据小区/楼栋/单元发送短信
	@ApiOperation(value = "根据小区/楼栋/单元发送短信(exportEstateList)", httpMethod = "POST", notes = "根据小区/楼栋/单元发送短信，smsCode和smsContent不能为空！")
	@ResponseBody
	@RequestMapping(value = "/sendCommunitySms", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse sendCommunitySms(@RequestBody CommunityEstateReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		GenericResponse  res = (GenericResponse) communityEstateService.sendCommunitySms(req);
		return res;
	}
	
	
	//获取业主信息列表
	@ApiOperation(value = "获取业主信息列表(getProprietorInfoList)", httpMethod = "POST", notes = "获取业主信息列表")
	@ResponseBody
	@RequestMapping(value = "/getProprietorInfoList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetProprietorInfoListRes getProprietorInfoList(@RequestBody CommunityEstateReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		GetProprietorInfoListRes  res = (GetProprietorInfoListRes) communityEstateService.getProprietorInfoList(req);
		return res;
	}
	//导出业主信息
	@ApiOperation(value = "导出业主信息(exporProprietorInfo)", httpMethod = "POST", notes = "导出业主信息")
	@ResponseBody
	@RequestMapping(value = "/exporProprietorInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse exporProprietorInfo(@RequestBody CommunityEstateReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		GenericResponse  res = (GenericResponse) communityEstateService.exporProprietorInfo(req,response);
		return res;
	}
	
	@ApiOperation(value = "导入业主信息(importProprietorInfo)", httpMethod = "POST", notes = "导入业主信息；")
	@ResponseBody
	@RequestMapping(value = "/importProprietorInfo",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importBuilding(HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile) throws Exception {
		GenericResponse res = (GenericResponse) communityEstateService.importProprietorInfo(request);
		return res;
	}
	
	//修改业主信息
	@ApiOperation(value = "获取业主信息列表(modifyProprietorInfo)", httpMethod = "POST", notes = "修改业主信息")
	@ResponseBody
	@RequestMapping(value = "/modifyProprietorInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyProprietorInfo(@RequestBody CommunityProprietorInfoReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		GenericResponse  res = (GenericResponse) communityEstateService.modifyProprietorInfo(req);
		return res;
	}
	
	//获取业主信息详情
	@ApiOperation(value = "获取业主信息详情(getProprietorInfo)", httpMethod = "POST", notes = "获取业主信息详情")
	@ResponseBody
	@RequestMapping(value = "/getProprietorInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetProprietorInfoRes getProprietorInfo(@RequestBody CommunityEstateReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		GetProprietorInfoRes  res = (GetProprietorInfoRes) communityEstateService.getProprietorInfo(req);
		return res;
	}
	
	
	//导出业主信息
	@ApiOperation(value = "导出业主信息(exporGradeEstate)", httpMethod = "POST", notes = "导出业主信息")
	@ResponseBody
	@RequestMapping(value = "/exporGradeEstate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse exporGradeEstate(@RequestBody CommunityEstateReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		GenericResponse  res = null;
		//res = (GenericResponse) communityEstateService.exporGradeEstate(req,response);
		return res;
	}
	
	//导出收费项目分摊范围的单元
	@ApiOperation(value = "导出收费项目分摊范围的单元(exportApportionmentScopeEstateList)", httpMethod = "POST", notes = "导出收费项目分摊范围的单元")
	@ResponseBody
	@RequestMapping(value = "/exportApportionmentScopeEstateList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse exportApportionmentScopeEstateList(@RequestBody CommunityEstateReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		GenericResponse  res = (GenericResponse) communityEstateService.exportApportionmentScopeEstateList(req,response);
		return res;
	}
}
