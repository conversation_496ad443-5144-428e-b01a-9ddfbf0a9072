package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityReservationRecordReq;
import com.foshan.form.community.response.communityReservationRecord.GetCommunityReservationRecordInfoRes;
import com.foshan.form.community.response.communityReservationRecord.GetCommunityReservationRecordListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "预约记录模块")
@RestController
public class CommunityReservationRecordController extends BaseCommunityController {
	// 获取预约记录列表
	@ApiOperation(value = "获取预约记录列表(getCommunityReservationRecordList)", httpMethod = "POST", notes = "获取预约记录列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReservationRecordList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReservationRecordListRes getCommunityReservationRecordList(@RequestBody CommunityReservationRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReservationRecordListRes res = (GetCommunityReservationRecordListRes) communityReservationRecordService.getCommunityReservationRecordList(req);
		return res;
	}
	
	// 新增预约记录
	@ApiOperation(value = "新增预约记录(addCommunityReservationRecord)", httpMethod = "POST", notes = "新增预约记录，ReservationNum、PeriodId和ActivitiesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityReservationRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityReservationRecord(@RequestBody CommunityReservationRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReservationRecordService.addCommunityReservationRecord(req);
		return res;
	}
	
	// 修改预约记录
	@ApiOperation(value = "修改预约记录(modifyCommunityReservationRecord)", httpMethod = "POST", notes = "修改预约记录，RecordId和ReservationState不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityReservationRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityReservationRecord(@RequestBody CommunityReservationRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReservationRecordService.modifyCommunityReservationRecord(req);
		return res;
	}
	// 删除预约记录
	@ApiOperation(value = "删除预约记录(deleteCommunityReservationRecord)", httpMethod = "POST", notes = "删除预约记录，RecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityReservationRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityReservationRecord(@RequestBody CommunityReservationRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReservationRecordService.deleteCommunityReservationRecord(req);
		return res;
	}
	
	// 获取预约记录详情
	@ApiOperation(value = "获取预约记录详情(getCommunityReservationRecordInfo)", httpMethod = "POST", notes = "获取预约记录详情，RecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReservationRecordInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReservationRecordInfoRes getCommunityReservationRecordInfo(@RequestBody CommunityReservationRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReservationRecordInfoRes res = (GetCommunityReservationRecordInfoRes) communityReservationRecordService.getCommunityReservationRecordInfo(req);
		return res;
	}
}
