package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.OptionsVoReq;
import com.foshan.form.community.response.optionsVo.GetOptionsVoList;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "下拉框选项（如：社区、小区、区域等）模块")
@RestController
public class OptionsVoController extends BaseCommunityController {

	// 获取社区列表
	@ApiOperation(value = "获取社区列表(getCommunityOptionList)", httpMethod = "POST", notes = "获取社区列表，无分页；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityOptionList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetOptionsVoList getCommunityOptionList(@RequestBody OptionsVoReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetOptionsVoList res = (GetOptionsVoList) optionsVoService.getCommunityOptionList(req);
		return res;
	}
	
	// 获取小区列表
	@ApiOperation(value = "获取小区列表(getDistrictOptionList)", httpMethod = "POST", notes = "获取小区列表，communityId可传，无分页；")
	@ResponseBody
	@RequestMapping(value = "/getDistrictOptionList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetOptionsVoList getDistrictOptionList(@RequestBody OptionsVoReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetOptionsVoList res = (GetOptionsVoList) optionsVoService.getDistrictOptionList(req);
		return res;
	}
	
	// 获取区域列表
	@ApiOperation(value = "获取社区列表(getAreaOptionList)", httpMethod = "POST", notes = "获取区域列表，Districtd可传，无分页；")
	@ResponseBody
	@RequestMapping(value = "/getAreaOptionList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetOptionsVoList getAreaOptionList(@RequestBody OptionsVoReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetOptionsVoList res = (GetOptionsVoList) optionsVoService.getAreaOptionList(req);
		return res;
	}
	
	// 获取栋号列表
	@ApiOperation(value = "获取栋号列表(getBuildingNumOptionList)", httpMethod = "POST", notes = "获取栋号列表，Districtd、Area可传，无分页；")
	@ResponseBody
	@RequestMapping(value = "/getBuildingNumOptionList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetOptionsVoList getBuildingNumOptionList(@RequestBody OptionsVoReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetOptionsVoList res = (GetOptionsVoList) optionsVoService.getBuildingNumOptionList(req);
		return res;
	}
	
	// 获取单元列表
	@ApiOperation(value = "获取单元列表(getUnitNumOptionList)", httpMethod = "POST", notes = "获取单元列表，Districtd、buildingNum和Area可传，无分页；")
	@ResponseBody
	@RequestMapping(value = "/getUnitNumOptionList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetOptionsVoList getUnitNumOptionList(@RequestBody OptionsVoReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetOptionsVoList res = (GetOptionsVoList) optionsVoService.getUnitNumOptionList(req);
		return res;
	}

}
