package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityDecorationAttachmentReq;
import com.foshan.form.community.response.communityDecorationAttachment.AddCommunityDecorationAttachmentRes;
import com.foshan.form.community.response.communityDecorationAttachment.GetCommunityDecorationAttachmentInfoRes;
import com.foshan.form.community.response.communityDecorationAttachment.GetCommunityDecorationAttachmentListRes;
import com.foshan.form.community.response.communityDecorationAttachment.ModifyCommunityDecorationAttachmentRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "装修项目附件模块")
@RestController
public class CommunityDecorationAttachmentController extends BaseCommunityController {

	// 获取装修项目附件列表
	@ApiOperation(value = "获取装修项目附件列表(getCommunityDecorationAttachmentList)", httpMethod = "POST", notes = "获取装修项目附件列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityDecorationAttachmentList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityDecorationAttachmentListRes getCommunityEventCategoryItemsList(@RequestBody CommunityDecorationAttachmentReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityDecorationAttachmentListRes res = (GetCommunityDecorationAttachmentListRes) communityDecorationAttachmentService.getCommunityDecorationAttachmentList(req);
		return res;
	}
	
	// 新增装修项目附件
	@ApiOperation(value = "新增事件类型项目(addCommunityDecorationAttachment)", httpMethod = "POST", notes = "新增装修项目附件")
	@ResponseBody
	@RequestMapping(value = "/addCommunityDecorationAttachment", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityDecorationAttachmentRes addCommunityDecorationAttachment(@RequestBody CommunityDecorationAttachmentReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityDecorationAttachmentRes res = (AddCommunityDecorationAttachmentRes) communityDecorationAttachmentService.addCommunityDecorationAttachment(req);
		return res;
	}
	
	// 修改装修项目附件
	@ApiOperation(value = "修改装修项目附件(modifyCommunityDecorationAttachment)", httpMethod = "POST", notes = "修改装修项目附件，communityDecorationAttachmentId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityDecorationAttachment", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityDecorationAttachmentRes modifyCommunityDecorationAttachment(@RequestBody CommunityDecorationAttachmentReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityDecorationAttachmentRes res = (ModifyCommunityDecorationAttachmentRes) communityDecorationAttachmentService.modifyCommunityDecorationAttachment(req);
		return res;
	}
	
	// 删除装修项目附件
	@ApiOperation(value = "删除事件类型项目(deleteCommunityDecorationAttachment)", httpMethod = "POST", notes = "删除装修项目附件，communityDecorationAttachmentId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityDecorationAttachment", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityDecorationAttachment(@RequestBody CommunityDecorationAttachmentReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityDecorationAttachmentService.deleteCommunityDecorationAttachment(req);
		return res;
	}
	
	// 获取装修项目附件详情
	@ApiOperation(value = "获取装修项目附件详情(getCommunityDecorationAttachmentInfo)", httpMethod = "POST", notes = "获取装修项目附件详情，communityDecorationAttachmentId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityDecorationAttachmentInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityDecorationAttachmentInfoRes getCommunityDecorationAttachmentInfo(@RequestBody CommunityDecorationAttachmentReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityDecorationAttachmentInfoRes res = (GetCommunityDecorationAttachmentInfoRes) communityDecorationAttachmentService.getCommunityDecorationAttachmentInfo(req);
		return res;
	}
   
}
