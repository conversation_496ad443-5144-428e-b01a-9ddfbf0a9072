package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityUserReq;
import com.foshan.form.community.request.GetCameraPlayUrlReq;
import com.foshan.form.community.response.communityUser.GetCameraPlayUrlRes;
import com.foshan.form.community.response.communityUser.GetCommunityUserListRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "用户型模块")
@RestController
public class CommunityUserController extends BaseCommunityController {
	
	// 获取用户列表
	@ApiOperation(value = "获取用户列表(getCommunityUserList)", httpMethod = "POST", notes = "获取用户列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityUserList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityUserListRes getCommunityUserList(@RequestBody CommunityUserReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityUserListRes res = (GetCommunityUserListRes) communityUserService.getCommunityUserList(req);
		return res;
	}
	
	// 用户获取摄像头视频流
	@ApiOperation(value = "用户获取摄像头视频流(getCameraPlayUrl)", httpMethod = "POST", notes = "获取用户列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCameraPlayUrl", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCameraPlayUrlRes getCameraPlayUrl(@RequestBody GetCameraPlayUrlReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCameraPlayUrlRes res = (GetCameraPlayUrlRes) communityUserService.getCameraPlayUrl(req);
		return res;
	}
}
