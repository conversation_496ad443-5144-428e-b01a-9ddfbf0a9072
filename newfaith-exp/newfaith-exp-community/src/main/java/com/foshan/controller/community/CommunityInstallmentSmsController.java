package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityInstallmentSmsReq;
import com.foshan.form.community.response.communityInstallmentSms.GetCommunityInstallmentSmsInfoRes;
import com.foshan.form.community.response.communityInstallmentSms.GetCommunityInstallmentSmsListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.user.GetUserListRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@Api(tags = "账单分期短信模块")
@RestController
public class CommunityInstallmentSmsController extends BaseCommunityController {

	// 获取账单分期短信列表
	@ApiOperation(value = "获取账单分期短信列表(getCommunityInstallmentSmsList)", httpMethod = "POST", notes = "获取账单分期短信列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityInstallmentSmsList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityInstallmentSmsListRes getCommunityInstallmentSmsList(@RequestBody CommunityInstallmentSmsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityInstallmentSmsListRes res = (GetCommunityInstallmentSmsListRes) communityInstallmentSmsService.getCommunityInstallmentSmsList(req);
		return res;
	}
	
	// 新增账单分期短信
	@ApiOperation(value = "新增账单分期短信列表(addCommunityInstallmentSms)", httpMethod = "POST", notes = "新增账单分期短信，estateId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityInstallmentSms", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityInstallmentSms(@RequestBody CommunityInstallmentSmsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityInstallmentSmsService.addCommunityInstallmentSms(req);
		return res;
	}
	
	// 修改账单分期短信
	@ApiOperation(value = "修改账单分期短信(modifyCommunityInstallmentSms)", httpMethod = "POST", notes = "修改账单分期短信，installmentSmsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityInstallmentSms", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityInstallmentSms(@RequestBody CommunityInstallmentSmsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityInstallmentSmsService.modifyCommunityInstallmentSms(req);
		return res;
	}
	
	// 删除账单分期短信
	@ApiOperation(value = "删除账单分期短信(deleteCommunityInstallmentSms)", httpMethod = "POST", notes = "删除账单分期短信，installmentSmsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityInstallmentSms", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityInstallmentSms(@RequestBody CommunityInstallmentSmsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityInstallmentSmsService.deleteCommunityInstallmentSms(req);
		return res;
	}
	
	// 重新发短信
	@ApiOperation(value = "重新发短信(sendSmsAgain)", httpMethod = "POST", notes = "重新发短信，installmentSmsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/sendSmsAgain", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse sendSmsAgain(@RequestBody CommunityInstallmentSmsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityInstallmentSmsService.sendSmsAgain(req);
		return res;
	}
	
	// 修改账单分期短信状态
	@ApiOperation(value = "修改账单分期短信状态(setInstallmentSmsStatus)", httpMethod = "POST", notes = "修改账单分期短信状态，installmentSmsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/setInstallmentSmsStatus", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse setInstallmentSmsStatus(@RequestBody CommunityInstallmentSmsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityInstallmentSmsService.setInstallmentSmsStatus(req);
		return res;
	}
	
	// 获取账单分期短信详情
	@ApiOperation(value = "修改账单分期短信详情(getCommunityInstallmentSmsInfo)", httpMethod = "POST", notes = "修改账单分期短信详情，installmentSmsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityInstallmentSmsInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityInstallmentSmsInfoRes getCommunityInstallmentSmsInfo(@RequestBody CommunityInstallmentSmsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityInstallmentSmsInfoRes res = (GetCommunityInstallmentSmsInfoRes) communityInstallmentSmsService.getCommunityInstallmentSmsInfo(req);
		return res;
	}
	// 获取管家列表
	@ApiOperation(value = "获取管家列表(getStewardList)", httpMethod = "POST", notes = "获取管家列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getStewardList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetUserListRes getStewardList(@RequestBody CommunityInstallmentSmsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetUserListRes res = (GetUserListRes) communityInstallmentSmsService.getStewardList(req);
		return res;
	}
}
