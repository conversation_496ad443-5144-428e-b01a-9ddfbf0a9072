package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityEventNotificationReq;
import com.foshan.form.community.request.CommunityEventsReq;
import com.foshan.form.community.response.communityEventNotification.GetCommunityEventNotificationInfoRes;
import com.foshan.form.community.response.communityEventNotification.GetCommunityEventNotificationListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "事件通知模块")
@RestController
public class CommunityEventNotificationController extends BaseCommunityController {

	// 获取事件通知列表
	@ApiOperation(value = "获取事件通知列表(getCommunityEventNotificationList)", httpMethod = "POST", notes = "获取事件通知列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEventNotificationList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEventNotificationListRes getCommunityEventNotificationList(@RequestBody CommunityEventNotificationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEventNotificationListRes res = (GetCommunityEventNotificationListRes) communityEventNotificationService.getCommunityEventNotificationList(req);
		return res;
	}
	
	// 新增事件通知
	@ApiOperation(value = "新增事件类型项目(addCommunityEventNotification)", httpMethod = "POST", notes = "新增事件通知<p>communityId和name不能为空")
	@ResponseBody
	@RequestMapping(value = "/addCommunityEventNotification", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityEventNotification(@RequestBody CommunityEventNotificationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEventNotificationService.addCommunityEventNotification(req);
		return res;
	}
	
	// 修改事件通知
	@ApiOperation(value = "修改事件通知(modifyCommunityEventNotification)", httpMethod = "POST", notes = "修改事件通知<p>communityEventNotificationId、communityId和name不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityEventNotification", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityEventNotification(@RequestBody CommunityEventNotificationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEventNotificationService.modifyCommunityEventNotification(req);
		return res;
	}
	
	// 删除事件通知
	@ApiOperation(value = "删除事件类型项目(deleteCommunityEventNotification)", httpMethod = "POST", notes = "删除事件通知<p>communityEventNotificationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityEventNotification", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityEventNotification(@RequestBody CommunityEventNotificationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEventNotificationService.deleteCommunityEventNotification(req);
		return res;
	}
	
	// 获取事件通知详情
	@ApiOperation(value = "获取事件通知详情(getCommunityEventNotificationInfo)", httpMethod = "POST", notes = "获取事件通知详情 <p>communityEventNotificationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEventNotificationInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEventNotificationInfoRes getCommunityEventNotificationInfo(@RequestBody CommunityEventNotificationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEventNotificationInfoRes res = (GetCommunityEventNotificationInfoRes) communityEventNotificationService.getCommunityEventNotificationInfo(req);
		return res;
	}
	
	@ApiOperation(value = "导出通知(exportCommunityEventNotification)", httpMethod = "POST", notes = "导出通知")
	@ResponseBody
	@RequestMapping(value = "/exportCommunityEventNotification", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCommunityEventNotification(@RequestBody CommunityEventNotificationReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityEventNotificationService.exportCommunityEventNotification(req,response);
	}

}
