package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityStatisticsReq;
import com.foshan.form.community.request.ReceiptStatisticsReq;
import com.foshan.form.community.request.ReceivablesChangesReq;
import com.foshan.form.community.request.RefundReq;
import com.foshan.form.community.response.communityStatistics.GetBuildingOccupancyRes;
import com.foshan.form.community.response.communityStatistics.GetCommunityBaseStatisticsRes;
import com.foshan.form.community.response.communityStatistics.GetCommunityEventStatisticsRes;
import com.foshan.form.community.response.communityStatistics.GetCommunityReceivablesRes;
import com.foshan.form.community.response.communityStatistics.GetMemberPropertyRes;
import com.foshan.form.community.response.communityStatistics.GetNoReceivablesRes;
import com.foshan.form.community.response.communityStatistics.GetReceiptDjbFileListRes;
import com.foshan.form.community.response.communityStatistics.GetReceiptRes;
import com.foshan.form.community.response.communityStatistics.GetReceivablesChangesRes;
import com.foshan.form.community.response.communityStatistics.GetReceivablesStatisticsRes;
import com.foshan.form.community.response.communityStatistics.GetRufundRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "社区统计模块")
@RestController
public class CommunityStatisticsController extends BaseCommunityController {

	// 获取社区用户统计
	@ApiOperation(value = "获取社区用户统计(getMemberStatistics)", httpMethod = "POST", notes = "获取社区用户统计")
	@PostMapping(value = "/getBaseStatistics", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityBaseStatisticsRes getBaseStatistics(HttpServletRequest request) throws JsonProcessingException {
		GetCommunityBaseStatisticsRes res = (GetCommunityBaseStatisticsRes) communityStatisticsService
				.getBaseStatistics();
		return res;
	}

	// 获取社区事件统计
	@ApiOperation(value = "获取社区事件统计(getEventStatistics)", httpMethod = "POST", notes = "获取社区事件统计；")
	@PostMapping(value = "/getEventStatistics", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEventStatisticsRes getEventStatistics(HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEventStatisticsRes res = (GetCommunityEventStatisticsRes) communityStatisticsService
				.getEventStatistics();
		return res;
	}

	// 获取应收统计
	@ApiOperation(value = "获取应收统计(getReceivablesStatistics)", httpMethod = "POST", notes = "获取应收统计；")
	@PostMapping(value = "/getReceivablesStatistics", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetReceivablesStatisticsRes getReceivablesStatistics(@RequestBody CommunityStatisticsReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GetReceivablesStatisticsRes res = (GetReceivablesStatisticsRes) communityStatisticsService
				.getReceivablesStatistics(req);
		return res;
	}

	// 获取应收报表
	@SuppressWarnings("rawtypes")
	@ApiOperation(value = "查询应收报表(getCommunityReceivables)", httpMethod = "POST", notes = "获取应收统计；")
	@PostMapping(value = "/getCommunityReceivables", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceivablesRes getCommunityReceivables(@RequestBody CommunityStatisticsReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GetCommunityReceivablesRes res = (GetCommunityReceivablesRes) communityStatisticsService
				.getCommunityReceivables(req);
		return res;
	}

	// 获取应收未收报表
	@SuppressWarnings("rawtypes")
	@ApiOperation(value = "查询应收未收报表(getNoReceivables)", httpMethod = "POST", notes = "获取应收统计；")
	@PostMapping(value = "/getNoReceivables", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetNoReceivablesRes getNoReceivables(@RequestBody CommunityStatisticsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetNoReceivablesRes res = (GetNoReceivablesRes) communityStatisticsService.getNoReceivables(req);
		return res;
	}

	// 获取住户信息报表
	@SuppressWarnings("rawtypes")
	@ApiOperation(value = "获取住户信息报表(getMemberProperty)", httpMethod = "POST", notes = "获取应收统计；")
	@PostMapping(value = "/getMemberProperty", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetMemberPropertyRes getMemberProperty(@RequestBody CommunityStatisticsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetMemberPropertyRes res = (GetMemberPropertyRes) communityStatisticsService.getMemberProperty(req);
		return res;
	}

	// 获取收款报表清单
	@SuppressWarnings("rawtypes")
	@ApiOperation(value = "获取收款报表清单(getReceipt)", httpMethod = "POST", notes = "根据应收时间段和收据时间段进行筛选")
	@PostMapping(value = "/getReceipt", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetReceiptRes getReceipt(@RequestBody ReceiptStatisticsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetReceiptRes res = (GetReceiptRes) communityStatisticsService.getReceipt(req);
		return res;
	}

	// 获取应收减免清单
	@SuppressWarnings("rawtypes")
	@ApiOperation(value = "获取应收减免清单(getCommunityReceivablesChanges)", httpMethod = "POST", notes = "根据应收时间段和收据时间段进行筛选")
	@PostMapping(value = "/getCommunityReceivablesChanges", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetReceivablesChangesRes getCommunityReceivablesChanges(@RequestBody ReceivablesChangesReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GetReceivablesChangesRes res = (GetReceivablesChangesRes) communityStatisticsService
				.getCommunityReceivablesChanges(req);
		return res;
	}

	// 获取退款清单
	@SuppressWarnings("rawtypes")
	@ApiOperation(value = "获取退款清单(getRefund)", httpMethod = "POST", notes = "根据减免时间进行筛选")
	@PostMapping(value = "/getRefund", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetRufundRes getRefund(@RequestBody RefundReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetRufundRes res = (GetRufundRes) communityStatisticsService.getRefund(req);
		return res;
	}

	// 获取楼盘入住率
	@SuppressWarnings("rawtypes")
	@ApiOperation(value = "获取楼盘入住率(getBuildingOccupancy)", httpMethod = "POST", notes = "获取楼盘入住率；")
	@PostMapping(value = "/getBuildingOccupancy", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetBuildingOccupancyRes getBuildingOccupancy(@RequestBody CommunityStatisticsReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GetBuildingOccupancyRes res = (GetBuildingOccupancyRes) communityStatisticsService.getBuildingOccupancy(req);
		return res;
	}

	// 获取收据登记表文件列表
	@ApiOperation(value = "获取收据登记表文件列表(getReceiptDjbFileList)", httpMethod = "POST", notes = "获取收据登记表文件列表")
	@PostMapping(value = "/getReceiptDjbFileList", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetReceiptDjbFileListRes getReceiptDjbFileList(HttpServletRequest request) throws JsonProcessingException {
		GetReceiptDjbFileListRes res = (GetReceiptDjbFileListRes) communityStatisticsService.getReceiptDjbFileList();
		return res;
	}

	// 导出应收报表
	@ApiOperation(value = "导出应收报表", httpMethod = "POST", notes = "应收报表，传空{}即可；")
	@PostMapping(value = "/exportCommunityReceivablesExcel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCommunityReceivablesExcel(HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityStatisticsService.exportCommunityReceivablesExcel(response);

	}

	// 导出应收未收报表
	@ApiOperation(value = "导出应收未收报表", httpMethod = "POST", notes = "应收未收报表，传空{}即可；")
	@PostMapping(value = "/exportNoReceivablesExcel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportNoReceivablesExcel(HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityStatisticsService.exportNoReceivablesExcel(response);

	}

	// 导出应收未收措施表
	@ApiOperation(value = "导出应收未收措施表", httpMethod = "POST", notes = "应收未收报表，传空{}即可；")
	@PostMapping(value = "/exportNoReceivablesMeasuresExcel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportNoRexportNoReceivablesMeasuresExceleceivablesExcel(@RequestBody CommunityStatisticsReq req,
			HttpServletRequest request, HttpServletResponse response) throws IOException, ParseException {
		communityStatisticsService.exportNoReceivablesMeasuresExcel(req, response);

	}

	// 导出应收未收措施表
	@ApiOperation(value = "导出应收未收分析表", httpMethod = "POST", notes = "应收未收报表，传空{}即可；")
	@PostMapping(value = "/exportNoReceivablesSummaryExcel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportNoReceivablesSummaryExcel(@RequestBody CommunityStatisticsReq req, HttpServletRequest request,
			HttpServletResponse response) throws IOException, ParseException {
		communityStatisticsService.exportNoReceivablesSummaryExcel(req, response);

	}

	// 导出业主信息报表
	@ApiOperation(value = "导出业主信息报表", httpMethod = "POST", notes = "传空{}即可；")
	@PostMapping(value = "/exportMemberPropertyExcel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportMemberPropertyExcel(HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityStatisticsService.exportMemberPropertyExcel(response);

	}

	// 导出收款透視报表
	@ApiOperation(value = "导出收款透視报表", httpMethod = "POST", notes = "传空{}即可；")
	@PostMapping(value = "/exportReceiptExcel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportReceiptExcel(HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityStatisticsService.exportReceiptExcel(response);

	}

	// 导出减免透視报表
	@ApiOperation(value = "导出收款透視报表", httpMethod = "POST", notes = "传空{}即可；")
	@PostMapping(value = "/exportCommunityReceivablesChangesExcel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCommunityReceivablesChangesExcel(HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityStatisticsService.exportCommunityReceivablesChangesExcel(response);

	}

	// 导出退款报表
	@ApiOperation(value = "导出收款透視报表", httpMethod = "POST", notes = "传空{}即可；")
	@PostMapping(value = "/exportRefundExcel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportRefundExcel(HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityStatisticsService.exportRefundExcel(response);

	}

	// 导出楼盘入住率报表
	@ApiOperation(value = "导出楼盘入住率报表", httpMethod = "POST", notes = "传空{}即可；")
	@PostMapping(value = "/exportBuildingOccupancyExcel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportBuildingOccupancyExcel(HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityStatisticsService.exportBuildingOccupancyExcel(response);

	}

	// 导出退款报表
	@ApiOperation(value = "导出每日收款报表", httpMethod = "POST", notes = "传空{}即可；")
	@PostMapping(value = "/exportEverydayReceiptExcel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportEverydayReceiptExcel(@RequestBody CommunityStatisticsReq req, HttpServletRequest request,
			HttpServletResponse response) throws IOException, ParseException {
		communityStatisticsService.exportEverydayReceiptExcel(req, response);

	}

	// 导出收据登记表
	@ApiOperation(value = "导出当前收据登记表", httpMethod = "POST", notes = "")
	@PostMapping(value = "/exportReceiptDjbExcel", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportReceiptDjbExcel(@RequestBody CommunityStatisticsReq req, HttpServletRequest request,
			HttpServletResponse response) throws IOException, ParseException {
		communityStatisticsService.exportReceiptDjbExcel(req, response);

	}

	// 下载收据登记表文件
	@ApiOperation(value = "下载收据登记表文件", httpMethod = "POST", notes = "")
	@PostMapping(value = "/downloadReceiptDjbFile", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void downloadReceiptDjbFile(@RequestBody CommunityStatisticsReq req, HttpServletRequest request,
			HttpServletResponse response) throws IOException, ParseException {
		communityStatisticsService.downloadReceiptDjbFile(req, response);

	}
}
