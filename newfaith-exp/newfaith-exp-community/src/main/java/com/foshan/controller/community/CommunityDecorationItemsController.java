package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityDecorationItemsReq;
import com.foshan.form.community.response.communityDecorationItems.AddCommunityDecorationItemsRes;
import com.foshan.form.community.response.communityDecorationItems.GetCommunityDecorationItemsInfoRes;
import com.foshan.form.community.response.communityDecorationItems.GetCommunityDecorationItemsListRes;
import com.foshan.form.community.response.communityDecorationItems.ModifyCommunityDecorationItemsRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "装修项目项目模块")
@RestController
public class CommunityDecorationItemsController extends BaseCommunityController {

	// 获取装修项目项目列表
	@ApiOperation(value = "获取装修项目项目列表(getCommunityDecorationItemsList)", httpMethod = "POST", notes = "获取装修项目项目列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityDecorationItemsList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityDecorationItemsListRes getCommunityEventCategoryItemsList(@RequestBody CommunityDecorationItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityDecorationItemsListRes res = (GetCommunityDecorationItemsListRes) communityDecorationItemsService.getCommunityDecorationItemsList(req);
		return res;
	}
	
	// 新增装修项目项目
	@ApiOperation(value = "新增事件类型项目(addCommunityDecorationItems)", httpMethod = "POST", notes = "新增装修项目项目")
	@ResponseBody
	@RequestMapping(value = "/addCommunityDecorationItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityDecorationItemsRes addCommunityDecorationItems(@RequestBody CommunityDecorationItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityDecorationItemsRes res = (AddCommunityDecorationItemsRes) communityDecorationItemsService.addCommunityDecorationItems(req);
		return res;
	}
	
	// 修改装修项目项目
	@ApiOperation(value = "修改装修项目项目(modifyCommunityDecorationItems)", httpMethod = "POST", notes = "修改装修项目项目，communityDecorationItemsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityDecorationItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityDecorationItemsRes modifyCommunityDecorationItems(@RequestBody CommunityDecorationItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityDecorationItemsRes res = (ModifyCommunityDecorationItemsRes) communityDecorationItemsService.modifyCommunityDecorationItems(req);
		return res;
	}
	
	// 删除装修项目项目
	@ApiOperation(value = "删除事件类型项目(deleteCommunityDecorationItems)", httpMethod = "POST", notes = "删除装修项目项目，communityDecorationItemsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityDecorationItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityDecorationItems(@RequestBody CommunityDecorationItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityDecorationItemsService.deleteCommunityDecorationItems(req);
		return res;
	}
	
	// 获取装修项目项目详情
	@ApiOperation(value = "获取装修项目项目详情(getCommunityDecorationItemsInfo)", httpMethod = "POST", notes = "获取装修项目项目详情，communityDecorationItemsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityDecorationItemsInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityDecorationItemsInfoRes getCommunityDecorationItemsInfo(@RequestBody CommunityDecorationItemsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityDecorationItemsInfoRes res = (GetCommunityDecorationItemsInfoRes) communityDecorationItemsService.getCommunityDecorationItemsInfo(req);
		return res;
	}
   
}
