package com.foshan.controller.community;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityInitReq;
import com.foshan.form.community.response.init.CommunityInitRes;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class CommunityInitController extends BaseCommunityController {

	@PostConstruct
	public void initCommunity() {
		log.info("缓存开始装载平台基础数据");
		log.info("---装载单元数据");
		communityInitService.initEstateList();
		log.info("---装载收费项目数据");
		communityInitService.initPayItemsList();
		log.info("---装载总表数据");
		communityInitService.initMeterList();
 //		log.info("---装载总表单元关联数据");
 //		communityInitService.initMeterEstateList();
		log.info("---装载应收款数据");
		communityInitService.initReceivables();
		log.info("缓存装载平台基础数据结束");
	}

	@RequestMapping(value = "/checkInitCache", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public CommunityInitRes checkInitCache(@RequestBody CommunityInitReq req, HttpServletRequest request)
			throws JsonProcessingException {
		CommunityInitRes res = (CommunityInitRes) communityInitService.checkInitCache(req);
		return res;
	}
	
	@RequestMapping(value = "/refreshInitCache", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void refreshInitCache(HttpServletRequest request)
			throws JsonProcessingException {
		log.info("开始刷新缓存");
/*		log.info("---装载单元数据");
		communityInitService.initEstateList();
		log.info("---装载收费项目数据");
		communityInitService.initPayItemsList();
		log.info("---装载总表数据");
		communityInitService.initMeterList();
//		log.info("---装载总表单元关联数据");
//		communityInitService.initMeterEstateList();
		log.info("---装载应收款数据");
		communityInitService.initReceivables();
		log.info("---装载车位开始计费时间数据");
		communityInitService.initParkingBillingDate();*/
		log.info("缓存刷新结束");
	}
	
	@RequestMapping(value = "/getCacheInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public CommunityInitRes getCacheInfo(@RequestBody CommunityInitReq req, HttpServletRequest request)
			throws JsonProcessingException {
		CommunityInitRes res = (CommunityInitRes) communityInitService.getCacheInfo(req);
		return res;
	}

}
