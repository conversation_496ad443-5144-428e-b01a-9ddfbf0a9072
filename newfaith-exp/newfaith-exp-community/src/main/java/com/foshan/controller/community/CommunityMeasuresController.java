package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityMeasuresReq;
import com.foshan.form.community.response.communityMeasures.AddCommunityMeasuresRes;
import com.foshan.form.community.response.communityMeasures.GetCommunityMeasuresInfoRes;
import com.foshan.form.community.response.communityMeasures.GetCommunityMeasuresListRes;
import com.foshan.form.community.response.communityMeasures.ModifyCommunityMeasuresRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "单元欠费采取措施表模块")
@RestController
public class CommunityMeasuresController extends BaseCommunityController {

	// 获取单元欠费采取措施表列表
	@ApiOperation(value = "获取单元欠费采取措施表列表(getCommunityMeasuresList)", httpMethod = "POST", notes = "获取单元欠费采取措施表列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMeasuresList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeasuresListRes getCommunityMeasuresList(@RequestBody CommunityMeasuresReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMeasuresListRes res = (GetCommunityMeasuresListRes) communityMeasuresService.getCommunityMeasuresList(req);
		return res;
	}
	
	// 新增单元欠费采取措施表
	@ApiOperation(value = "新增事件类型项目(addCommunityMeasures)", httpMethod = "POST", notes = "新增单元欠费采取措施表")
	@ResponseBody
	@RequestMapping(value = "/addCommunityMeasures", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityMeasuresRes addCommunityMeasures(@RequestBody CommunityMeasuresReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityMeasuresRes res = (AddCommunityMeasuresRes) communityMeasuresService.addCommunityMeasures(req);
		return res;
	}
	
	// 修改单元欠费采取措施表
	@ApiOperation(value = "修改单元欠费采取措施表(modifyCommunityMeasures)", httpMethod = "POST", notes = "修改单元欠费采取措施表，communityMeasuresId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityMeasures", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityMeasuresRes modifyCommunityMeasures(@RequestBody CommunityMeasuresReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityMeasuresRes res = (ModifyCommunityMeasuresRes) communityMeasuresService.modifyCommunityMeasures(req);
		return res;
	}
	
	// 删除单元欠费采取措施表
	@ApiOperation(value = "删除事件类型项目(deleteCommunityMeasures)", httpMethod = "POST", notes = "删除单元欠费采取措施表，communityMeasuresId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityMeasures", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityMeasures(@RequestBody CommunityMeasuresReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeasuresService.deleteCommunityMeasures(req);
		return res;
	}
	
	// 获取单元欠费采取措施表详情
	@ApiOperation(value = "获取单元欠费采取措施表详情(getCommunityMeasuresInfo)", httpMethod = "POST", notes = "获取单元欠费采取措施表详情，communityMeasuresId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMeasuresInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeasuresInfoRes getCommunityMeasuresInfo(@RequestBody CommunityMeasuresReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMeasuresInfoRes res = (GetCommunityMeasuresInfoRes) communityMeasuresService.getCommunityMeasuresInfo(req);
		return res;
	}
   
}
