package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityReq;
import com.foshan.form.community.response.community.AddCommunityRes;
import com.foshan.form.community.response.community.GetCommunityInfoRes;
import com.foshan.form.community.response.community.GetCommunityListRes;
import com.foshan.form.community.response.community.ModifyCommunityRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "社区模块")
@RestController
public class CommunityController extends BaseCommunityController {

	// 获取社区列表
	@ApiOperation(value = "获取社区列表(getCommunityList)", httpMethod = "POST", notes = "获取社区列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityListRes getCommunityList(@RequestBody CommunityReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityListRes res = (GetCommunityListRes) communityService.getCommunityList(req);
		return res;
	}
	
	// 新增社区
	@ApiOperation(value = "新增事件类型项目(addCommunity)", httpMethod = "POST", notes = "新增社区")
	@ResponseBody
	@RequestMapping(value = "/addCommunity", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityRes addCommunity(@RequestBody CommunityReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityRes res = (AddCommunityRes) communityService.addCommunity(req);
		return res;
	}
	
	// 修改社区
	@ApiOperation(value = "修改社区(modifyCommunity)", httpMethod = "POST", notes = "修改社区，communityId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunity", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityRes modifyCommunity(@RequestBody CommunityReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityRes res = (ModifyCommunityRes) communityService.modifyCommunity(req);
		return res;
	}
	
	// 删除社区
	@ApiOperation(value = "删除事件类型项目(deleteCommunity)", httpMethod = "POST", notes = "删除社区，communityId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunity", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunity(@RequestBody CommunityReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityService.deleteCommunity(req);
		return res;
	}
	
	// 获取社区详情
	@ApiOperation(value = "获取社区详情(getCommunityInfo)", httpMethod = "POST", notes = "获取社区详情，communityId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityInfoRes getCommunityInfo(@RequestBody CommunityReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityInfoRes res = (GetCommunityInfoRes) communityService.getCommunityInfo(req);
		return res;
	}
   
}
