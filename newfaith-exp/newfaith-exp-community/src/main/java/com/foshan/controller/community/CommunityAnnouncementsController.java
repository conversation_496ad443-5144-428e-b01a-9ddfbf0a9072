package com.foshan.controller.community;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.communityAnnouncementsReq.CommunityAnnouncementsReq;
import com.foshan.form.community.request.communityAnnouncementsReq.DeleteCommunityAnnouncementsReq;
import com.foshan.form.community.request.communityAnnouncementsReq.GetCommunityAnnouncementsListReq;
import com.foshan.form.community.request.communityAnnouncementsReq.AuditCommunityAnnouncementsReq;
import com.foshan.form.community.response.communityAnnouncements.CommunityAnnouncementsRes;
import com.foshan.form.community.response.communityAnnouncements.GetCommunityAnnouncementsListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "社区公告模块")
@RestController
public class CommunityAnnouncementsController extends BaseCommunityController {

    // 新增社区公告
    @ApiOperation(value = "新增社区公告(addCommunityAnnouncements)", httpMethod = "POST", notes = "新增社区公告")
    @ResponseBody
    @RequestMapping(value = "/addCommunityAnnouncements", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse addCommunityAnnouncements(@RequestBody CommunityAnnouncementsReq req, HttpServletRequest request)throws JsonProcessingException  {
        GenericResponse res = (GenericResponse) communityAnnouncementsService.addCommunityAnnouncements(req);
        return res;
    }
    // 删除社区公告
    @ApiOperation(value = "删除社区公告(deleteCommunityAnnouncements)", httpMethod = "POST", notes = "删除社区公告,announcementId不能为空")
    @ResponseBody
    @RequestMapping(value = "/deleteCommunityAnnouncements", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteCommunityAnnouncements(@RequestBody DeleteCommunityAnnouncementsReq req, HttpServletRequest request)throws JsonProcessingException  {
        GenericResponse res = (GenericResponse) communityAnnouncementsService.deleteCommunityAnnouncements(req);
        return res;
    }
    // 获取社区公告
    @ApiOperation(value = "获取社区公告(getCommunityAnnouncements)", httpMethod = "POST", notes = "获取社区公告,announcementId不能为空")
    @ResponseBody
    @RequestMapping(value = "/getCommunityAnnouncements", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public CommunityAnnouncementsRes getCommunityAnnouncements(@RequestBody DeleteCommunityAnnouncementsReq req, HttpServletRequest request)throws JsonProcessingException  {
        CommunityAnnouncementsRes res = (CommunityAnnouncementsRes) communityAnnouncementsService.getCommunityAnnouncementsInfo(req);
        return res;
    }
    // 修改社区公告
    @ApiOperation(value = "修改社区公告(modifyCommunityAnnouncements)", httpMethod = "POST", notes = "修改社区公告,announcementId不能为空")
    @ResponseBody
    @RequestMapping(value = "/modifyCommunityAnnouncements", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse modifyCommunityAnnouncements(@RequestBody CommunityAnnouncementsReq req, HttpServletRequest request)throws JsonProcessingException  {
        GenericResponse res = (GenericResponse) communityAnnouncementsService.modifyCommunityAnnouncements(req);
        return res;
    }

    // 审核社区公告
    @ApiOperation(value = "审核社区公告(auditCommunityAnnouncements)", httpMethod = "POST", notes = "审核社区公告,announcementId不能为空")
    @ResponseBody
    @RequestMapping(value = "/auditCommunityAnnouncements", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse auditCommunityAnnouncements(@RequestBody AuditCommunityAnnouncementsReq req, HttpServletRequest request)throws JsonProcessingException  {
        GenericResponse res = (GenericResponse) communityAnnouncementsService.auditCommunityAnnouncements(req);
        return res;
    }

    // 获取社区公告列表
    @ApiOperation(value = "获取社区公告列表(getCommunityAnnouncementsList)", httpMethod = "POST", notes = "获取社区公告列表,columnId不能为空,选填announcementsType和status获取对应公告，不传默认获取栏目下全部公告")
    @ResponseBody
    @RequestMapping(value = "/getCommunityAnnouncementsList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCommunityAnnouncementsListRes getCommunityAnnouncementsList(@RequestBody GetCommunityAnnouncementsListReq req, HttpServletRequest request)throws JsonProcessingException  {
        GetCommunityAnnouncementsListRes res = (GetCommunityAnnouncementsListRes) communityAnnouncementsService.getCommunityAnnouncementsList(req);
        return res;
    }
}
