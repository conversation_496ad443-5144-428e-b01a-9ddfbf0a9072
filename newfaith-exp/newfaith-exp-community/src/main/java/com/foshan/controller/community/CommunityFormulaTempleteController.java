package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityFormulaTempleteReq;
import com.foshan.form.community.response.communityFormulaTemplete.CommunityFormulaTempleteRes;
import com.foshan.form.community.response.communityFormulaTemplete.GetCommunityFormulaTempleteListRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "公式模版模块")
@RestController
public class CommunityFormulaTempleteController extends BaseCommunityController {

	// 新增公式模版
	@ApiOperation(value = "新增公式模版(addCommunityFormulaTemplete)", httpMethod = "POST", notes = "新增公式模版：templeteName和templeteInfo不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityFormulaTemplete", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public CommunityFormulaTempleteRes addCommunityFormulaTemplete(@RequestBody CommunityFormulaTempleteReq req,
			HttpServletRequest request) throws JsonProcessingException {
		CommunityFormulaTempleteRes res = (CommunityFormulaTempleteRes) communityFormulaTempleteService
				.addFormulaTemplete(req);
		return res;
	}

	// 修改公式模版
	@ApiOperation(value = "修改公式模版(modifyCommunityFormulaTemplete)", httpMethod = "POST", notes = "修改公式模版：templeteId、templeteName和templeteInfo不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityFormulaTemplete", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public CommunityFormulaTempleteRes modifyCommunityFormulaTemplete(@RequestBody CommunityFormulaTempleteReq req,
			HttpServletRequest request) throws JsonProcessingException {
		CommunityFormulaTempleteRes res = (CommunityFormulaTempleteRes) communityFormulaTempleteService
				.modifyFormulaTemplete(req);
		return res;
	}

	// 删除公式模版
	@ApiOperation(value = "删除公式模版(deleteCommunityFormulaTemplete)", httpMethod = "POST", notes = "删除公式模版:templeteId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityFormulaTemplete", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public CommunityFormulaTempleteRes deleteCommunityFormulaTemplete(@RequestBody CommunityFormulaTempleteReq req,
			HttpServletRequest request) throws JsonProcessingException {
		CommunityFormulaTempleteRes res = (CommunityFormulaTempleteRes) communityFormulaTempleteService
				.deleteFormulaTemplete(req);
		return res;
	}

	// 获取公式模版
	@ApiOperation(value = "获取公式模版(getCommunityFormulaTempleteInfo)", httpMethod = "POST", notes = "获取公式模版:templeteId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityFormulaTempleteInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public CommunityFormulaTempleteRes getCommunityFormulaTempleteInfo(@RequestBody CommunityFormulaTempleteReq req,
			HttpServletRequest request) throws JsonProcessingException {
		CommunityFormulaTempleteRes res = (CommunityFormulaTempleteRes) communityFormulaTempleteService
				.getFormulaTempleteInfo(req);
		return res;
	}

	// 获取公式模版列表
	@ApiOperation(value = "获取公式模版列表(getCommunityFormulaTempleteList)", httpMethod = "POST", notes = "获取公式模版列表")
	@ResponseBody
	@RequestMapping(value = "/getCommunityFormulaTempleteList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityFormulaTempleteListRes getCommunityFormulaTempleteList(@RequestBody CommunityFormulaTempleteReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GetCommunityFormulaTempleteListRes res = (GetCommunityFormulaTempleteListRes) communityFormulaTempleteService
				.getFormulaTempleteList(req);
		return res;
	}
}
