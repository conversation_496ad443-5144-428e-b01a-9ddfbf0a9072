package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityContractReq;
import com.foshan.form.community.response.contract.AddCommunityContractRes;
import com.foshan.form.community.response.contract.GetCommunityContractInfoRes;
import com.foshan.form.community.response.contract.GetCommunityContractListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "合同模块")
@RestController
public class CommunityContractController extends BaseCommunityController {

	// 获取小区列表
	@ApiOperation(value = "获取合同列表(getCommunityContractList)", httpMethod = "POST", notes = "获取合同列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityContractList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityContractListRes getCommunityContractList(@RequestBody CommunityContractReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityContractListRes res = (GetCommunityContractListRes) communityContractService.getCommunityContractList(req);
		return res;
	}
	
	// 删除合同
	@ApiOperation(value = "删除合同(deleteCommunityContract)", httpMethod = "POST", notes = "删除合同，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityContract", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityContract(@RequestBody CommunityContractReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityContractService.deleteCommunityContract(req);
		return res;
	}
	
	// 获取合同详情
	@ApiOperation(value = "获取合同详情(getCommunityContractInfo)", httpMethod = "POST", notes = "获取合同详情，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityContractInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityContractInfoRes getCommunityContractInfo(@RequestBody CommunityContractReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityContractInfoRes res = (GetCommunityContractInfoRes) communityContractService.getCommunityContractInfo(req);
		return res;
	}
	
	
	// 新增合同
	@ApiOperation(value = "新增合同(addCommunityContract)", httpMethod = "POST", notes = "新增合同，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityContract", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityContractRes addCommunityContract(@RequestBody CommunityContractReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityContractRes res = (AddCommunityContractRes) communityContractService.addCommunityContract(req);
		return res;
	}
	
	// 修改合同
	@ApiOperation(value = "修改合同(modifyCommunityContract)", httpMethod = "POST", notes = "修改合同，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityContract", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityContract(@RequestBody CommunityContractReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityContractService.modifyCommunityContract(req);
		return res;
	}

}
