package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityInvoiceReq;
import com.foshan.form.community.request.CommunityReceiptReq;
import com.foshan.form.community.response.communityReceipt.AddCommunityReceiptRes;
import com.foshan.form.community.response.communityReceipt.GenerateXmlRes;
import com.foshan.form.community.response.communityReceipt.GetCommunityInvoiceListRes;
import com.foshan.form.community.response.communityReceipt.GetCommunityReceiptInfoRes;
import com.foshan.form.community.response.communityReceipt.GetCommunityReceiptListRes;
import com.foshan.form.community.response.communityReceipt.InvoiceRes;
import com.foshan.form.community.response.communityReceipt.ModifyCommunityReceiptRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "收款单据模块")
@RestController
public class CommunityReceiptController extends BaseCommunityController {

	// 获取收款单据列表
	@ApiOperation(value = "获取收款单据列表(getCommunityReceiptList)", httpMethod = "POST", notes = "获取收款单据列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReceiptList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceiptListRes getCommunityReceiptList(@RequestBody CommunityReceiptReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceiptListRes res = (GetCommunityReceiptListRes) communityReceiptService.getCommunityReceiptList(req);
		return res;
	}
	
	// 新增收款单据
	@ApiOperation(value = "新增事件类型项目(addCommunityReceipt)", httpMethod = "POST", notes = "新增收款单据")
	@ResponseBody
	@RequestMapping(value = "/addCommunityReceipt", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityReceiptRes addCommunityReceipt(@RequestBody CommunityReceiptReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityReceiptRes res = (AddCommunityReceiptRes) communityReceiptService.addCommunityReceipt(req);
		return res;
	}
	
	// 修改收款单据
	@ApiOperation(value = "修改收款单据(modifyCommunityReceipt)", httpMethod = "POST", notes = "修改收款单据，communityReceiptId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityReceipt", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityReceiptRes modifyCommunityReceipt(@RequestBody CommunityReceiptReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityReceiptRes res = (ModifyCommunityReceiptRes) communityReceiptService.modifyCommunityReceipt(req);
		return res;
	}
	
	// 删除收款单据
	@ApiOperation(value = "删除事件类型项目(deleteCommunityReceipt)", httpMethod = "POST", notes = "删除收款单据，communityReceiptId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityReceipt", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityReceipt(@RequestBody CommunityReceiptReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReceiptService.deleteCommunityReceipt(req);
		return res;
	}
	
	// 获取收款单据详情
	@ApiOperation(value = "获取收款单据详情(getCommunityReceiptInfo)", httpMethod = "POST", notes = "获取收款单据详情，communityReceiptId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReceiptInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceiptInfoRes getCommunityReceiptInfo(@RequestBody CommunityReceiptReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceiptInfoRes res = (GetCommunityReceiptInfoRes) communityReceiptService.getCommunityReceiptInfo(req);
		return res;
	}
	
	// 创建单据PDF文件
	@ApiOperation(value = "创建单据PDF文件(createCommunityReceiptPdf)", httpMethod = "POST", notes = "创建单据PDF文件，communityReceiptId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/createCommunityReceiptPdf", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceiptInfoRes createCommunityReceiptPdf(@RequestBody CommunityReceiptReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceiptInfoRes res = (GetCommunityReceiptInfoRes) communityReceiptService.createCommunityReceiptPdf(req);
		return res;
	}
	
	// 增加单据打印次数
	@ApiOperation(value = "增加单据打印次数(addPrintNum)", httpMethod = "POST", notes = "增加单据打印次数，communityReceiptId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addPrintNum", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceiptInfoRes addPrintNum(@RequestBody CommunityReceiptReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceiptInfoRes res = (GetCommunityReceiptInfoRes) communityReceiptService.addPrintNum(req);
		return res;
	}
	
	// 开发票
	@ApiOperation(value = "开发票(issueInvoice)", httpMethod = "POST", notes = "开发票，communityReceiptId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/issueInvoice", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public InvoiceRes issueInvoice(@RequestBody CommunityInvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		InvoiceRes res = (InvoiceRes) communityInvoiceService.issueInvoice(req);
		return res;
	}
	
	// 取消红冲申请
	@ApiOperation(value = "取消红冲申请(cancelRedDashedApplication)", httpMethod = "POST", notes = "取消红冲申请，invoiceId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/cancelRedDashedApplication", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public InvoiceRes cancelRedDashedApplication(@RequestBody CommunityInvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		InvoiceRes res = (InvoiceRes) communityInvoiceService.cancelRedDashedApplication(req);
		return res;
	}
	
	// 查询结果
	@ApiOperation(value = "查询结果(queryResult)", httpMethod = "POST", notes = "查询结果，invoiceId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/queryResult", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public InvoiceRes queryResult(@RequestBody CommunityInvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		InvoiceRes res = (InvoiceRes) communityInvoiceService.queryResult(req);
		return res;
	}
   
	// 生成XML
	@ApiOperation(value = "生成XML(generateXml)", httpMethod = "POST", notes = "生成XML，receiptId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/generateXml", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenerateXmlRes generateXml(@RequestBody CommunityInvoiceReq req, HttpServletRequest request, HttpServletResponse response)
			throws JsonProcessingException {
		GenerateXmlRes res = (GenerateXmlRes)communityInvoiceService.generateXml(req,response);
		return res;

	}
	
	// 获取发票列表
	@ApiOperation(value = "获取发票列表(getCommunityInvoiceList)", httpMethod = "POST", notes = "获取发票列表；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityInvoiceList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityInvoiceListRes getCommunityInvoiceList(@RequestBody CommunityInvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityInvoiceListRes res = (GetCommunityInvoiceListRes) communityInvoiceService.getCommunityInvoiceList(req);
		return res;
	}
	
	// 导出收款单据
	@ApiOperation(value = "导出收款单据详情(exportCommunityReceiptList)", httpMethod = "POST", notes = "导出收款单据详情；")
	@ResponseBody
	@RequestMapping(value = "/exportCommunityReceiptList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ExportExcelRes exportCommunityReceiptList(@RequestBody CommunityReceiptReq req, 
			HttpServletRequest request, HttpServletResponse response)
			throws JsonProcessingException {
		ExportExcelRes res = (ExportExcelRes) communityReceiptService.exportCommunityReceiptList(req,response);
		return res;
	}
	
	
	// 获取短信
	@ApiOperation(value = "获取短信(getbWSms)", httpMethod = "POST", notes = "获取短信；")
	@ResponseBody
	@RequestMapping(value = "/getbWSms", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse getbWSms(@RequestBody CommunityInvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityInvoiceService.getbWSms();
		return res;
	}
	
	
	// 验证百望短信码
	@ApiOperation(value = "验证百望短信码(verifyBWSms)", httpMethod = "POST", notes = "验证百望短信码，verifyCode不能为空；")
	@ResponseBody
	@RequestMapping(value = "/verifyBWSms", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse verifyBWSms(@RequestBody CommunityInvoiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityInvoiceService.verifyBWSms(req);
		return res;
	}
}
