package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityInspectionRecordReq;
import com.foshan.form.community.response.communityInspectionRecord.GetCommunityInspectionRecordInfoRes;
import com.foshan.form.community.response.communityInspectionRecord.GetCommunityInspectionRecordListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@Api(tags = "巡查记录模块")
@RestController
public class CommunityInspectionRecordController extends BaseCommunityController {

	// 获取巡查记录列表
	@ApiOperation(value = "获取巡查记录列表(addCommunityFormulaTemplete)", httpMethod = "POST", notes = "获取巡查记录列表")
	@ResponseBody
	@RequestMapping(value = "/getCommunityInspectionRecordList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityInspectionRecordListRes getCommunityInspectionRecordList(@RequestBody CommunityInspectionRecordReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GetCommunityInspectionRecordListRes res = (GetCommunityInspectionRecordListRes) communityInspectionRecordService
				.getCommunityInspectionRecordList(req);
		return res;
	}
	
	// 修改巡查记录
	@ApiOperation(value = "修改巡查记录(modifyCommunityInspectionRecord)", httpMethod = "POST", notes = "修改巡查记录")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityInspectionRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityInspectionRecord(@RequestBody CommunityInspectionRecordReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityInspectionRecordService
				.modifyCommunityInspectionRecord(req);
		return res;
	}
	
	// 新增巡查记录
	@ApiOperation(value = "新增巡查记录(addCommunityInspectionRecord)", httpMethod = "POST", notes = "新增巡查记录")
	@ResponseBody
	@RequestMapping(value = "/addCommunityInspectionRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityInspectionRecord(@RequestBody CommunityInspectionRecordReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityInspectionRecordService
				.addCommunityInspectionRecord(req);
		return res;
	}
	
	// 获取巡查记录详情
	@ApiOperation(value = "获取巡查记录详情(getCommunityInspectionRecordInfo)", httpMethod = "POST", notes = "获取巡查记录详情")
	@ResponseBody
	@RequestMapping(value = "/getCommunityInspectionRecordInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityInspectionRecordInfoRes getCommunityInspectionRecordInfo(@RequestBody CommunityInspectionRecordReq req,
			HttpServletRequest request) throws JsonProcessingException {
		GetCommunityInspectionRecordInfoRes res = (GetCommunityInspectionRecordInfoRes) communityInspectionRecordService
				.getCommunityInspectionRecordInfo(req);
		return res;
	}
}
