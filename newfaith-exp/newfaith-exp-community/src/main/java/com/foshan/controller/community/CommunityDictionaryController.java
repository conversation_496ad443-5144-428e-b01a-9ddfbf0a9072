package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityDictionaryReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.dictionary.GetDictionaryInfoRes;
import com.foshan.form.response.dictionary.GetDictionaryListRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "字典模块")
@RestController
public class CommunityDictionaryController extends BaseCommunityController {

	// 获取字典列表
	@ApiOperation(value = "获取字典列表(getCommunityDictionaryList)", httpMethod = "POST", notes = "获取字典列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityDictionaryList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetDictionaryListRes getCommunityDictionaryList(@RequestBody CommunityDictionaryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetDictionaryListRes res = (GetDictionaryListRes) communityDictionaryService.getCommunityDictionaryList(req);
		return res;
	}
	
	// 新增字典
	@ApiOperation(value = "新增事件类型项目(addCommunityDictionary)", httpMethod = "POST", notes = "新增字典<p>communityId和name不能为空")
	@ResponseBody
	@RequestMapping(value = "/addCommunityDictionary", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityDictionary(@RequestBody CommunityDictionaryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityDictionaryService.addCommunityDictionary(req);
		return res;
	}
	
	// 修改字典
	@ApiOperation(value = "修改字典(modifyCommunityDictionary)", httpMethod = "POST", notes = "修改字典<p>communityDictionaryId、communityId和name不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityDictionary", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityDictionary(@RequestBody CommunityDictionaryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityDictionaryService.modifyCommunityDictionary(req);
		return res;
	}
	
	// 删除字典
	@ApiOperation(value = "删除事件类型项目(deleteCommunityDictionary)", httpMethod = "POST", notes = "删除字典<p>communityDictionaryId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityDictionary", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityDictionary(@RequestBody CommunityDictionaryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityDictionaryService.deleteCommunityDictionary(req);
		return res;
	}
	
	// 获取字典详情
	@ApiOperation(value = "获取字典详情(getCommunityDictionaryInfo)", httpMethod = "POST", notes = "获取字典详情 <p>communityDictionaryId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityDictionaryInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetDictionaryInfoRes getCommunityDictionaryInfo(@RequestBody CommunityDictionaryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetDictionaryInfoRes res = (GetDictionaryInfoRes) communityDictionaryService.getCommunityDictionaryInfo(req);
		return res;
	}
	

}
