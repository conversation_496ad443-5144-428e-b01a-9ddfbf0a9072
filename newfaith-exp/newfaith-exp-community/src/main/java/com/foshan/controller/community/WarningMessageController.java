package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.WarningMessageReq;
import com.foshan.form.community.response.warningMessage.AddWarningMessageRes;
import com.foshan.form.community.response.warningMessage.GetWarningMessageInfoRes;
import com.foshan.form.community.response.warningMessage.GetWarningMessageListRes;
import com.foshan.form.community.response.warningMessage.GetWarningMessageRecordList;
import com.foshan.form.community.response.warningMessage.ModifyWarningMessageRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "告警消息模块")
@RestController
public class WarningMessageController extends BaseCommunityController {

	// 获取告警消息列表
	@ApiOperation(value = "获取告警消息列表(getWarningMessageList)", httpMethod = "POST", notes = "获取告警消息列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getWarningMessageList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetWarningMessageListRes getWarningMessageList(@RequestBody WarningMessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetWarningMessageListRes res = (GetWarningMessageListRes) warningMessageService.getWarningMessageList(req);
		return res;
	}
	
	// 获取告警消息列表
	@ApiOperation(value = "获取告警消息列表(getWarningMessageRecordList)", httpMethod = "POST", notes = "获取告警消息列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getWarningMessageRecordList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetWarningMessageRecordList getWarningMessageRecordList(@RequestBody WarningMessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetWarningMessageRecordList res = (GetWarningMessageRecordList) warningMessageService.getWarningMessageRecordList(req);
		return res;
	}
	
	
	/*// 新增告警消息
	@ApiOperation(value = "新增事件类型项目(addWarningMessage)", httpMethod = "POST", notes = "新增告警消息")
	@ResponseBody
	@RequestMapping(value = "/addWarningMessage", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddWarningMessageRes addWarningMessage(@RequestBody WarningMessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddWarningMessageRes res = (AddWarningMessageRes) warningMessageService.addWarningMessage(req);
		return res;
	}*/
	
	// 修改告警消息
	@ApiOperation(value = "修改告警消息(modifyWarningMessage)", httpMethod = "POST", notes = "修改告警消息，warningMessageId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyWarningMessage", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyWarningMessageRes modifyWarningMessage(@RequestBody WarningMessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyWarningMessageRes res = (ModifyWarningMessageRes) warningMessageService.modifyWarningMessage(req);
		return res;
	}
	
	/*// 删除告警消息
	@ApiOperation(value = "删除事件类型项目(deleteWarningMessage)", httpMethod = "POST", notes = "删除告警消息，warningMessageId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteWarningMessage", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteWarningMessage(@RequestBody WarningMessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) warningMessageService.deleteWarningMessage(req);
		return res;
	}*/
	
	// 获取告警消息详情
	@ApiOperation(value = "获取告警消息详情(getWarningMessageInfo)", httpMethod = "POST", notes = "获取告警消息详情，warningMessageId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getWarningMessageInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetWarningMessageInfoRes getWarningMessageInfo(@RequestBody WarningMessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetWarningMessageInfoRes res = (GetWarningMessageInfoRes) warningMessageService.getWarningMessageInfo(req);
		return res;
	}
   
}
