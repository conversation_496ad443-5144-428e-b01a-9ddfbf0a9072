package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityReceiptReceivablesReq;
import com.foshan.form.community.response.communityReceiptReceivables.AddCommunityReceiptReceivablesRes;
import com.foshan.form.community.response.communityReceiptReceivables.GetCommunityReceiptReceivablesInfoRes;
import com.foshan.form.community.response.communityReceiptReceivables.GetCommunityReceiptReceivablesListRes;
import com.foshan.form.community.response.communityReceiptReceivables.ModifyCommunityReceiptReceivablesRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "应收款-收款单据模块")
@RestController
public class CommunityReceiptReceivablesController extends BaseCommunityController {

	// 获取应收款-收款单据列表
	@ApiOperation(value = "获取应收款-收款单据列表(getCommunityReceiptReceivablesList)", httpMethod = "POST", notes = "获取应收款-收款单据列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReceiptReceivablesList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceiptReceivablesListRes getCommunityReceiptReceivablesList(@RequestBody CommunityReceiptReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceiptReceivablesListRes res = (GetCommunityReceiptReceivablesListRes) communityReceiptReceivablesService.getCommunityReceiptReceivablesList(req);
		return res;
	}
	
	// 新增应收款-收款单据
	@ApiOperation(value = "新增事件类型项目(addCommunityReceiptReceivables)", httpMethod = "POST", notes = "新增应收款-收款单据")
	@ResponseBody
	@RequestMapping(value = "/addCommunityReceiptReceivables", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityReceiptReceivablesRes addCommunityReceiptReceivables(@RequestBody CommunityReceiptReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityReceiptReceivablesRes res = (AddCommunityReceiptReceivablesRes) communityReceiptReceivablesService.addCommunityReceiptReceivables(req);
		return res;
	}
	
	// 修改应收款-收款单据
	@ApiOperation(value = "修改应收款-收款单据(modifyCommunityReceiptReceivables)", httpMethod = "POST", notes = "修改应收款-收款单据，communityReceiptReceivablesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityReceiptReceivables", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityReceiptReceivablesRes modifyCommunitytReceiptReceivables(@RequestBody CommunityReceiptReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityReceiptReceivablesRes res = (ModifyCommunityReceiptReceivablesRes) communityReceiptReceivablesService.modifyCommunityReceiptReceivables(req);
		return res;
	}
	
	// 删除应收款-收款单据
	@ApiOperation(value = "删除事件类型项目(deleteCommunityReceiptReceivables)", httpMethod = "POST", notes = "删除应收款-收款单据，communityReceiptReceivablesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityReceiptReceivables", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityReceiptReceivables(@RequestBody CommunityReceiptReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityReceiptReceivablesService.deleteCommunityReceiptReceivables(req);
		return res;
	}
	
	// 获取应收款-收款单据详情
	@ApiOperation(value = "获取应收款-收款单据详情(getCommunityReceiptReceivablesInfo)", httpMethod = "POST", notes = "获取应收款-收款单据详情，communityReceiptReceivablesId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityReceiptReceivablesInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityReceiptReceivablesInfoRes getCommunityReceiptReceivablesInfo(@RequestBody CommunityReceiptReceivablesReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityReceiptReceivablesInfoRes res = (GetCommunityReceiptReceivablesInfoRes) communityReceiptReceivablesService.getCommunityReceiptReceivablesInfo(req);
		return res;
	}
   

}
