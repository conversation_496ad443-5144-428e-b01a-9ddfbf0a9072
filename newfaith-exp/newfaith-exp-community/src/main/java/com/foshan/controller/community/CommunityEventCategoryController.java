package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityEventCategoryReq;
import com.foshan.form.community.response.communityEventCategory.AddCommunityEventCategoryRes;
import com.foshan.form.community.response.communityEventCategory.GetCommunityEventCategoryInfo;
import com.foshan.form.community.response.communityEventCategory.GetCommunityEventCategoryListRes;
import com.foshan.form.community.response.communityEventCategory.ModifyCommunityEventCategoryRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "事件类型模块")
@RestController
public class CommunityEventCategoryController extends BaseCommunityController {

	// 获取主题列表
	@ApiOperation(value = "获取事件类型列表(getCommunityEventCategoryList)", httpMethod = "POST", notes = "获取事件类型列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEventCategoryList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEventCategoryListRes getCommunityEventCategoryList(@RequestBody CommunityEventCategoryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEventCategoryListRes res = (GetCommunityEventCategoryListRes) communityEventCategoryService.getCommunityEventCategoryList(req);
		return res;
	}
	
	// 新增事件类型
	@ApiOperation(value = "新增事件类型(addCommunityEventCategory)", httpMethod = "POST", notes = "新增事件类型，categoryName和isDispatching不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityEventCategory", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityEventCategoryRes addCommunityEventCategory(@RequestBody CommunityEventCategoryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityEventCategoryRes res = (AddCommunityEventCategoryRes) communityEventCategoryService.addCommunityEventCategory(req);
		return res;
	}
	
	// 修改事件类型
	@ApiOperation(value = "修改事件类型(modifyCommunityEventCategory)", httpMethod = "POST", notes = "修改事件类型，eventCategoryId、categoryName和isDispatching不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityEventCategory", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityEventCategoryRes modifyCommunityEventCategory(@RequestBody CommunityEventCategoryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityEventCategoryRes res = (ModifyCommunityEventCategoryRes) communityEventCategoryService.modifyCommunityEventCategory(req);
		return res;
	}
	
	// 删除事件类型
	@ApiOperation(value = "删除事件类型(deleteCommunityEventCategory)", httpMethod = "POST", notes = "删除事件类型，eventCategoryId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityEventCategory", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityEventCategory(@RequestBody CommunityEventCategoryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEventCategoryService.deleteCommunityEventCategory(req);
		return res;
	}
	
	// 获取事件类型详情
	@ApiOperation(value = "获取事件类型详情(getCommunityEventCategoryInfo)", httpMethod = "POST", notes = "获取事件类型详情，eventCategoryId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEventCategoryInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEventCategoryInfo getCommunityEventCategoryInfo(@RequestBody CommunityEventCategoryReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEventCategoryInfo res = (GetCommunityEventCategoryInfo) communityEventCategoryService.getCommunityEventCategoryInfo(req);
		return res;
	}

}
