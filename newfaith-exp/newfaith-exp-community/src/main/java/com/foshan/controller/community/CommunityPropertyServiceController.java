package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityPropertyServiceReq;
import com.foshan.form.community.response.communityPropertyService.CreatePropertyServicePdfRes;
import com.foshan.form.community.response.communityPropertyService.GetCommunityPropertyServiceInfoRes;
import com.foshan.form.community.response.communityPropertyService.GetCommunityPropertyServiceListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "报修服务模块")
@RestController
public class CommunityPropertyServiceController extends BaseCommunityController {
	
	// 获取报修服务列表
	@ApiOperation(value = "获取报修服务列表(getCommunityPropertyServiceList)", httpMethod = "POST", notes = "获取报修服务列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPropertyServiceList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPropertyServiceListRes getCommunityPropertyServiceList(@RequestBody CommunityPropertyServiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPropertyServiceListRes res = (GetCommunityPropertyServiceListRes) communityPropertyServiceService.getCommunityPropertyServiceList(req);
		return res;
	}
	
	// 获取报修服务列表
	@ApiOperation(value = "获取报修服务列表(addCommunityPropertyService)", httpMethod = "POST", notes = "获取报修服务列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityPropertyService", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityPropertyService(@RequestBody CommunityPropertyServiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityPropertyServiceService.addCommunityPropertyService(req);
		return res;
	}

	
	// 获取报修服务列表
	@ApiOperation(value = "获取报修服务列表(modifyCommunityPropertyService)", httpMethod = "POST", notes = "获取报修服务列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityPropertyService", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityPropertyService(@RequestBody CommunityPropertyServiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityPropertyServiceService.modifyCommunityPropertyService(req);
		return res;
	}
	
	// 获取报修服务列表
	@ApiOperation(value = "获取报修服务列表(deleteCommunityPropertyService)", httpMethod = "POST", notes = "获取报修服务列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityPropertyService", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityPropertyService(@RequestBody CommunityPropertyServiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityPropertyServiceService.deleteCommunityPropertyService(req);
		return res;
	}
	
	// 导出报修服务列表
	@ApiOperation(value = "导出报修服务列表(exportCommunityPropertyServiceList)", httpMethod = "POST", notes = "导出报修服务列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/exportCommunityPropertyServiceList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCommunityPropertyServiceList(@RequestBody CommunityPropertyServiceReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityPropertyServiceService.exportCommunityPropertyServiceList(req,response);
	}

	
	// 获取报修服务列表
	@ApiOperation(value = "获取报修服务列表(getCommunityPropertyServiceInfo)", httpMethod = "POST", notes = "获取报修服务列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPropertyServiceInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPropertyServiceInfoRes getCommunityPropertyServiceInfo(@RequestBody CommunityPropertyServiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPropertyServiceInfoRes res = (GetCommunityPropertyServiceInfoRes) communityPropertyServiceService.getCommunityPropertyServiceInfo(req);
		return res;
	}

	
	// 创建单据PDF文件
	@ApiOperation(value = "创建单据PDF文件(createPropertyServicePdf)", httpMethod = "POST", notes = "创建单据PDF文件，communityReceiptId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/createPropertyServicePdf", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public CreatePropertyServicePdfRes createPropertyServicePdf(@RequestBody CommunityPropertyServiceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		CreatePropertyServicePdfRes res = (CreatePropertyServicePdfRes) communityPropertyServiceService.createPropertyServicePdf(req);
		return res;
	}
}
