package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.ReceiveMessageTypeReq;
import com.foshan.form.community.response.receiveMessageType.AddReceiveMessageTypeRes;
import com.foshan.form.community.response.receiveMessageType.GetReceiveMessageTypeInfoRes;
import com.foshan.form.community.response.receiveMessageType.GetReceiveMessageTypeListRes;
import com.foshan.form.community.response.receiveMessageType.ModifyReceiveMessageTypeRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "接收消息类型表模块")
@RestController
public class ReceiveMessageTypeController extends BaseCommunityController {

	// 获取接收消息类型表列表
	@ApiOperation(value = "获取接收消息类型表列表(getReceiveMessageTypeList)", httpMethod = "POST", notes = "获取接收消息类型表列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getReceiveMessageTypeList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetReceiveMessageTypeListRes getReceiveMessageTypeList(@RequestBody ReceiveMessageTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetReceiveMessageTypeListRes res = (GetReceiveMessageTypeListRes) receiveMessageTypeService.getReceiveMessageTypeList(req);
		return res;
	}
	
	// 新增接收消息类型表
	@ApiOperation(value = "新增事件类型项目(addReceiveMessageType)", httpMethod = "POST", notes = "新增接收消息类型表")
	@ResponseBody
	@RequestMapping(value = "/addReceiveMessageType", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddReceiveMessageTypeRes addReceiveMessageType(@RequestBody ReceiveMessageTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddReceiveMessageTypeRes res = (AddReceiveMessageTypeRes) receiveMessageTypeService.addReceiveMessageType(req);
		return res;
	}
	
	// 修改接收消息类型表
	@ApiOperation(value = "修改接收消息类型表(modifyReceiveMessageType)", httpMethod = "POST", notes = "修改接收消息类型表，receiveMessageTypeId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyReceiveMessageType", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyReceiveMessageTypeRes modifyReceiveMessageType(@RequestBody ReceiveMessageTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyReceiveMessageTypeRes res = (ModifyReceiveMessageTypeRes) receiveMessageTypeService.modifyReceiveMessageType(req);
		return res;
	}
	
	// 删除接收消息类型表
	@ApiOperation(value = "删除事件类型项目(deleteReceiveMessageType)", httpMethod = "POST", notes = "删除接收消息类型表，receiveMessageTypeId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteReceiveMessageType", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteReceiveMessageType(@RequestBody ReceiveMessageTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) receiveMessageTypeService.deleteReceiveMessageType(req);
		return res;
	}
	
	// 获取接收消息类型表详情
	@ApiOperation(value = "获取接收消息类型表详情(getReceiveMessageTypeInfo)", httpMethod = "POST", notes = "获取接收消息类型表详情，receiveMessageTypeId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getReceiveMessageTypeInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetReceiveMessageTypeInfoRes getReceiveMessageTypeInfo(@RequestBody ReceiveMessageTypeReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetReceiveMessageTypeInfoRes res = (GetReceiveMessageTypeInfoRes) receiveMessageTypeService.getReceiveMessageTypeInfo(req);
		return res;
	}
   
}
