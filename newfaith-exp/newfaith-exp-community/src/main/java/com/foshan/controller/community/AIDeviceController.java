package com.foshan.controller.community;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.AIDeviceReq;
import com.foshan.form.community.response.aIDevice.DeviceResponse;
import com.foshan.form.community.response.aIDevice.GetEquipmentOrganizationListByUserIdResponse;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "ai设备模块")
@RestController
public class AIDeviceController extends BaseCommunityController {
//	private final static Logger logger = LoggerFactory.getLogger(AIDeviceController.class);
	
	@PostConstruct
	public void initAIDeviceParameter() {
		aIDeviceService.gainAIDeviceParameter();
	}
	
	// 获取参数
	@ApiOperation(value = "获取参数(gainAIDeviceParameter)", httpMethod = "POST", notes = "获取参数，传空{}即可；")
	@RequestMapping(value = "/gainAIDeviceParameter", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse gainAIDeviceParameter(@RequestBody AIDeviceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) aIDeviceService.gainAIDeviceParameter();
		return res;
	}
	
    // 接收告警消息
    @ApiOperation(value = "接收告警消息(receiveWarningMessage)", httpMethod = "POST", notes = "接收告警消息")
    @RequestMapping(value = "/receiveWarningMessage", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse receiveWarningMessage(@RequestBody AIDeviceReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GenericResponse res = (GenericResponse) aIDeviceService.receiveWarningMessage(req);
        return res;
    }
    
	@PostConstruct
	public void initDoLogin() {
		AIDeviceReq req = new AIDeviceReq();
		aIDeviceService.doLogin(req);
	}
    
    // 登录
    @ApiOperation(value = "登录(doLogin)", httpMethod = "POST", notes = "登录")
    @RequestMapping(value = "/doLogin", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse doLogin(@RequestBody AIDeviceReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GenericResponse res = (GenericResponse) aIDeviceService.doLogin(req);
        return res;
    }
    // 事件告警列表  
    @ApiOperation(value = "事件告警列表  (getEventRecordListByPaging)", httpMethod = "POST", notes = "事件告警列表")
    @RequestMapping(value = "/getEventRecordListByPaging", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public DeviceResponse getEventRecordListByPaging(@RequestBody AIDeviceReq req, HttpServletRequest request)throws JsonProcessingException  {
    	DeviceResponse res = (DeviceResponse) aIDeviceService.getEventRecordListByPaging(req);
        return res;
    }
    
    // 根据事件记录ID查询告警详情
    @ApiOperation(value = "根据事件记录ID查询告警详情(selectById)", httpMethod = "POST", notes = "根据事件记录ID查询告警详情")
    @RequestMapping(value = "/selectById", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public DeviceResponse selectById(@RequestBody AIDeviceReq req, HttpServletRequest request)throws JsonProcessingException  {
    	DeviceResponse res = (DeviceResponse) aIDeviceService.selectById(req);
        return res;
    }
    
    // 根据事件记录ID查询告警图片
    @ApiOperation(value = "根据事件记录ID查询告警图片(selectImgByEventRecordId)", httpMethod = "POST", notes = "根据事件记录ID查询告警图片")
    @RequestMapping(value = "/selectImgByEventRecordId", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public DeviceResponse selectImgByEventRecordId(@RequestBody AIDeviceReq req, HttpServletRequest request)throws JsonProcessingException  {
    	DeviceResponse res = (DeviceResponse) aIDeviceService.selectImgByEventRecordId(req);
        return res;
    }
    
    // 事件触发回放拉流接口
    @ApiOperation(value = "事件触发回放拉流接口(getBackVideo)", httpMethod = "POST", notes = "事件触发回放拉流接口")
    @RequestMapping(value = "/getBackVideo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public DeviceResponse getBackVideo(@RequestBody AIDeviceReq req, HttpServletRequest request)throws JsonProcessingException  {
    	DeviceResponse res = (DeviceResponse) aIDeviceService.getBackVideo(req);
        return res;
    }
    
    // 获取实时流接口
    @ApiOperation(value = "获取实时流接口(getRealVideo)", httpMethod = "POST", notes = "获取实时流接口")
    @RequestMapping(value = "/getRealVideo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public DeviceResponse getRealVideo(@RequestBody AIDeviceReq req, HttpServletRequest request)throws JsonProcessingException  {
    	DeviceResponse res = (DeviceResponse) aIDeviceService.getRealVideo(req);
        return res;
    }
    
    // 获取节点接口
    @ApiOperation(value = "获取节点接口(getEquipmentOrganizationListByUserId)", httpMethod = "POST", notes = "获取节点")
    @RequestMapping(value = "/getEquipmentOrganizationListByUserId", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetEquipmentOrganizationListByUserIdResponse getEquipmentOrganizationListByUserId(@RequestBody AIDeviceReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GetEquipmentOrganizationListByUserIdResponse res = (GetEquipmentOrganizationListByUserIdResponse) aIDeviceService.getEquipmentOrganizationListByUserId(req);
        return res;
    }
    
    // 告警处理
    @ApiOperation(value = "告警处理接口(updateStatus)", httpMethod = "POST", notes = "告警处理")
    @RequestMapping(value = "/updateStatus", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public DeviceResponse updateStatus(@RequestBody AIDeviceReq req, HttpServletRequest request)throws JsonProcessingException  {
    	DeviceResponse res = (DeviceResponse) aIDeviceService.updateStatus(req);
        return res;
    }
    
    
    // 获取小程序OPENID
    @ApiOperation(value = "获取小程序OPENID(getWxMiniOpenId)", httpMethod = "POST", notes = "获取小程序OPENID")
    @RequestMapping(value = "/getWxMiniOpenId", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse getWxMiniOpenId(@RequestBody AIDeviceReq req, HttpServletRequest request)throws JsonProcessingException  {
    	GenericResponse res = (GenericResponse) aIDeviceService.getWxMiniOpenId(req);
        return res;
    }
    
}
