package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityBankDepositRecordReq;
import com.foshan.form.community.response.communityBankDepositRecord.AddCommunityBankDepositRecordRes;
import com.foshan.form.community.response.communityBankDepositRecord.GetCommunityBankDepositRecordInfoRes;
import com.foshan.form.community.response.communityBankDepositRecord.GetCommunityBankDepositRecordListRes;
import com.foshan.form.community.response.communityBankDepositRecord.ModifyCommunityBankDepositRecordRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "银行划账记录模块")
@RestController
public class CommunityBankDepositRecordController extends BaseCommunityController {

	// 获取银行划账记录列表
	@ApiOperation(value = "获取银行划账记录列表(getCommunityBankDepositRecordList)", httpMethod = "POST", notes = "获取银行划账记录列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityBankDepositRecordList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityBankDepositRecordListRes getCommunityBankDepositRecordList(@RequestBody CommunityBankDepositRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityBankDepositRecordListRes res = (GetCommunityBankDepositRecordListRes) communityBankDepositRecordService.getCommunityBankDepositRecordList(req);
		return res;
	}
	
	// 新增银行划账记录
	@ApiOperation(value = "新增事件类型项目(addCommunityBankDepositRecord)", httpMethod = "POST", notes = "新增银行划账记录")
	@ResponseBody
	@RequestMapping(value = "/addCommunityBankDepositRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityBankDepositRecordRes addCommunityBankDepositRecord(@RequestBody CommunityBankDepositRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityBankDepositRecordRes res = (AddCommunityBankDepositRecordRes) communityBankDepositRecordService.addCommunityBankDepositRecord(req);
		return res;
	}
	
	// 修改银行划账记录
	@ApiOperation(value = "修改银行划账记录(modifyCommunityBankDepositRecord)", httpMethod = "POST", notes = "修改银行划账记录，communityBankDepositRecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityBankDepositRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityBankDepositRecordRes modifyCommunityBankDepositRecord(@RequestBody CommunityBankDepositRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityBankDepositRecordRes res = (ModifyCommunityBankDepositRecordRes) communityBankDepositRecordService.modifyCommunityBankDepositRecord(req);
		return res;
	}
	
	// 删除银行划账记录
	@ApiOperation(value = "删除事件类型项目(deleteCommunityBankDepositRecord)", httpMethod = "POST", notes = "删除银行划账记录，communityBankDepositRecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityBankDepositRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityBankDepositRecord(@RequestBody CommunityBankDepositRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityBankDepositRecordService.deleteCommunityBankDepositRecord(req);
		return res;
	}
	
	// 获取银行划账记录详情
	@ApiOperation(value = "获取银行划账记录详情(getCommunityBankDepositRecordInfo)", httpMethod = "POST", notes = "获取银行划账记录详情，communityBankDepositRecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityBankDepositRecordInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityBankDepositRecordInfoRes getCommunityBankDepositRecordInfo(@RequestBody CommunityBankDepositRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityBankDepositRecordInfoRes res = (GetCommunityBankDepositRecordInfoRes) communityBankDepositRecordService.getCommunityBankDepositRecordInfo(req);
		return res;
	}
	
	// 导出银行划账记录
	@ApiOperation(value = "导出银行划账记录(exportCommunityBankDepositRecordList)", httpMethod = "POST", notes = "导出银行划账记录，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/exportCommunityBankDepositRecordList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ExportExcelRes exportCommunityBankDepositRecordList(@RequestBody CommunityBankDepositRecordReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		ExportExcelRes  res = (ExportExcelRes) communityBankDepositRecordService.exportCommunityBankDepositRecordList(req,response);
		return res;
	}
   
}
