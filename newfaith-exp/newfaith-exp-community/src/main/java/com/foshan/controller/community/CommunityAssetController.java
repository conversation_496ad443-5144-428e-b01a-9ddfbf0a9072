package com.foshan.controller.community;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.communityAssetReq.*;
import com.foshan.form.community.response.communityAsset.CommunityAssetRes;
import com.foshan.form.community.response.communityAsset.GetCommunityAssetCommentListRes;
import com.foshan.form.community.response.communityAsset.GetCommunityAssetListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "社区媒资模块")
@RestController
public class CommunityAssetController extends BaseCommunityController{
    // 新增社区媒资
    @ApiOperation(value = "新增社区媒资(addCommunityAsset)", httpMethod = "POST", notes = "新增社区媒资,columnId不能为空，contentType不为0时，assetName传标题，summaryShort传内容")
    @ResponseBody
    @RequestMapping(value = "/addCommunityAsset", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse addCommunityAsset(@RequestBody AddCommunityAssetReq req, HttpServletRequest request)throws JsonProcessingException {
        GenericResponse res = (GenericResponse) communityAssetService.addCommunityAsset(req);
        return res;
    }

    // 删除社区媒资
    @ApiOperation(value = "删除社区媒资(deleteCommunityAsset)", httpMethod = "POST", notes = "删除社区媒资,assetId不能为空")
    @ResponseBody
    @RequestMapping(value = "/deleteCommunityAsset", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteCommunityAsset(@RequestBody CommunityAssetReq req, HttpServletRequest request)throws JsonProcessingException  {
        GenericResponse res = (GenericResponse) communityAssetService.deleteCommunityAsset(req);
        return res;
    }

    // 获取社区媒资
    @ApiOperation(value = "获取社区媒资(getCommunityAsset)", httpMethod = "POST", notes = "获取社区媒资,assetId不能为空")
    @ResponseBody
    @RequestMapping(value = "/getCommunityAsset", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public CommunityAssetRes getCommunityAsset(@RequestBody CommunityAssetReq req, HttpServletRequest request)throws JsonProcessingException  {
        CommunityAssetRes res = (CommunityAssetRes) communityAssetService.getCommunityAsset(req);
        return res;
    }

    // 修改社区媒资
    @ApiOperation(value = "修改社区媒资(modifyCommunityAsset)", httpMethod = "POST", notes = "修改社区媒资,assetId不能为空，contentType不为0时，assetName传标题，summaryShort传内容")
    @ResponseBody
    @RequestMapping(value = "/modifyCommunityAsset", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse modifyCommunityAsset(@RequestBody CommunityAssetReq req, HttpServletRequest request)throws JsonProcessingException  {
        GenericResponse res = (GenericResponse) communityAssetService.modifyCommunityAsset(req);
        return res;
    }

    // 审核社区媒资
    @ApiOperation(value = "审核社区媒资(auditCommunityAsset)", httpMethod = "POST", notes = "审核社区媒资,assetId,auditState不能为空")
    @ResponseBody
    @RequestMapping(value = "/auditCommunityAsset", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse auditCommunityAsset(@RequestBody AuditCommunityAssetReq req, HttpServletRequest request)throws JsonProcessingException  {
        GenericResponse res = (GenericResponse) communityAssetService.auditCommunityAsset(req);
        return res;
    }

    // 获取社区媒资列表
    @ApiOperation(value = "获取社区媒资列表(getCommunityAssetList)", httpMethod = "POST", notes = "获取社区媒资列表,columnId或memberId或userId不能为空,可附加传contentType筛选")
    @ResponseBody
    @RequestMapping(value = "/getCommunityAssetList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCommunityAssetListRes getCommunityAssetList(@RequestBody GetCommunityAssetListReq req, HttpServletRequest request)throws JsonProcessingException  {
        GetCommunityAssetListRes res = (GetCommunityAssetListRes) communityAssetService.getCommunityAssetList(req);
        return res;
    }

    // 新增社区媒资评论
    @ApiOperation(value = "新增社区媒资评论(addCommunityAssetComment)", httpMethod = "POST", notes = "新增社区媒资评论,assetId(主贴的assetId)、summaryShort不能为空")
    @ResponseBody
    @RequestMapping(value = "/addCommunityAssetComment", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse addCommunityAssetComment(@RequestBody AddCommunityAssetCommentReq req, HttpServletRequest request)throws JsonProcessingException {
        GenericResponse res = (GenericResponse) communityAssetService.addCommunityAssetComment(req);
        return res;
    }

    // 删除社区媒资评论
    @ApiOperation(value = "删除社区媒资评论(deleteCommunityAssetComment)", httpMethod = "POST", notes = "删除社区媒资评论,assetId（评论的assetId）不能为空")
    @ResponseBody
    @RequestMapping(value = "/deleteCommunityAssetComment", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteCommunityAssetComment(@RequestBody AddCommunityAssetReq req, HttpServletRequest request)throws JsonProcessingException  {
        GenericResponse res = (GenericResponse) communityAssetService.deleteCommunityAssetComment(req);
        return res;
    }

    // 审核社区媒资评论
    @ApiOperation(value = "审核社区媒资评论(auditCommunityAssetComment)", httpMethod = "POST", notes = "审核社区媒资评论,assetId（评论的assetId）,auditState不能为空")
    @ResponseBody
    @RequestMapping(value = "/auditCommunityAssetComment", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse auditCommunityAssetComment(@RequestBody AuditCommunityAssetReq req, HttpServletRequest request)throws JsonProcessingException  {
        GenericResponse res = (GenericResponse) communityAssetService.auditCommunityAssetComment(req);
        return res;
    }

    // 修改社区媒资评论
    @ApiOperation(value = "修改社区媒资评论(modifyCommunityAssetComment)", httpMethod = "POST", notes = "修改社区媒资评论,assetId（评论的assetId）、summaryShort不能为空")
    @ResponseBody
    @RequestMapping(value = "/modifyCommunityAssetComment", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse modifyCommunityAssetComment(@RequestBody AddCommunityAssetCommentReq req, HttpServletRequest request)throws JsonProcessingException  {
        GenericResponse res = (GenericResponse) communityAssetService.modifyCommunityAssetComment(req);
        return res;
    }

    // 获取社区媒资评论列表
    @ApiOperation(value = "获取社区媒资评论列表(getCommunityAssetCommentList)", httpMethod = "POST", notes = "获取社区媒资评论列表,assetId(主贴的assetId)或memberId或userId不能为空")
    @ResponseBody
    @RequestMapping(value = "/getCommunityAssetCommentList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCommunityAssetCommentListRes getCommunityAssetCommentList(@RequestBody GetCommunityAssetCommentListReq req, HttpServletRequest request)throws JsonProcessingException  {
        GetCommunityAssetCommentListRes res = (GetCommunityAssetCommentListRes) communityAssetService.getCommunityAssetCommentList(req);
        return res;
    }
}
