package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityMeterReq;
import com.foshan.form.community.response.communityMeter.AddCommunityMeterRes;
import com.foshan.form.community.response.communityMeter.GetCommunityMeterInfoRes;
import com.foshan.form.community.response.communityMeter.GetCommunityMeterListRes;
import com.foshan.form.community.response.communityMeter.ModifyCommunityMeterRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "总表模块")
@RestController
public class CommunityMeterController extends BaseCommunityController {

	// 获取总表列表
	@ApiOperation(value = "获取总表列表(getCommunityMeterList)", httpMethod = "POST", notes = "获取总表列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMeterList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterListRes getCommunityMeterList(@RequestBody CommunityMeterReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMeterListRes res = (GetCommunityMeterListRes) communityMeterService.getCommunityMeterList(req);
		return res;
	}
	
	@ApiOperation(value = "导出总表列表(exportCommunityMeterList)", httpMethod = "POST", notes = "导出总表列表")
	@ResponseBody
	@RequestMapping(value = "/exportCommunityMeterList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCommunityMeterList(@RequestBody CommunityMeterReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityMeterService.exportCommunityMeterList(response,req);
	}
	
	// 获取租赁总表列表
	@ApiOperation(value = "获取租赁总表列表(getCommunityRentMeterList)", httpMethod = "POST", notes = "获取租赁总表列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityRentMeterList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterListRes getCommunityRentMeterList(@RequestBody CommunityMeterReq req, HttpServletRequest request)
			throws JsonProcessingException {
		req.setIsRent(1);
		GetCommunityMeterListRes res = (GetCommunityMeterListRes) communityMeterService.getCommunityMeterList(req);
		return res;
	}
	
	// 新增总表
	@ApiOperation(value = "新增事件类型项目(addCommunityMeter)", httpMethod = "POST", notes = "新增总表")
	@ResponseBody
	@RequestMapping(value = "/addCommunityMeter", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityMeterRes addCommunityMeter(@RequestBody CommunityMeterReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityMeterRes res = (AddCommunityMeterRes) communityMeterService.addCommunityMeter(req);
		return res;
	}
	
	// 修改总表
	@ApiOperation(value = "修改总表(modifyCommunityMeter)", httpMethod = "POST", notes = "修改总表，communityMeterId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityMeter", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityMeterRes modifyCommunityMeter(@RequestBody CommunityMeterReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityMeterRes res = (ModifyCommunityMeterRes) communityMeterService.modifyCommunityMeter(req);
		return res;
	}
	
	// 删除总表
	@ApiOperation(value = "删除事件类型项目(deleteCommunityMeter)", httpMethod = "POST", notes = "删除总表，communityMeterId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityMeter", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityMeter(@RequestBody CommunityMeterReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterService.deleteCommunityMeter(req);
		return res;
	}
	
	// 获取总表详情
	@ApiOperation(value = "获取总表详情(getCommunityMeterInfo)", httpMethod = "POST", notes = "获取总表详情，communityMeterId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMeterInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterInfoRes getCommunityMeterInfo(@RequestBody CommunityMeterReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMeterInfoRes res = (GetCommunityMeterInfoRes) communityMeterService.getCommunityMeterInfo(req);
		return res;
	}
	
	// 获取租赁表详情
	@ApiOperation(value = "获取租赁表详情(getCommunityRentMeterInfo)", httpMethod = "POST", notes = "获取租赁表详情，communityMeterId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityRentMeterInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterInfoRes getCommunityRentMeterInfo(@RequestBody CommunityMeterReq req, HttpServletRequest request)
			throws JsonProcessingException {
		req.setIsRent(1);
		GetCommunityMeterInfoRes res = (GetCommunityMeterInfoRes) communityMeterService.getCommunityMeterInfo(req);
		return res;
	}
	
	// 表绑定单元
	@ApiOperation(value = "表绑定单元(bindingMeterAndEstate)", httpMethod = "POST", notes = "表绑定单元，communityMeterId、propertyIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/bindingMeterAndEstate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse bindingMeterAndEstate(@RequestBody CommunityMeterReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterService.bindingMeterAndEstate(req);
		return res;
	}
	
	// 表解绑绑定单元
	@ApiOperation(value = "表解绑绑定单元(unbindingMeterAndEstate)", httpMethod = "POST", notes = "表解绑绑定单元，communityMeterId、propertyIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/unbindingMeterAndEstate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse unbindingMeterAndEstate(@RequestBody CommunityMeterReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterService.unbindingMeterAndEstate(req);
		return res;
	}
	
	// 复制单元分摊范围
	@ApiOperation(value = "复制单元分摊范围(copeMeterRange)", httpMethod = "POST", notes = "复制单元分摊范围，communityMeterIdList、beCopiedMeterIdList不能为空；")
	@ResponseBody
	@RequestMapping(value = "/copeMeterRange", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse copeMeterRange(@RequestBody CommunityMeterReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterService.copeMeterRange(req);
		return res;
	}
   
}
