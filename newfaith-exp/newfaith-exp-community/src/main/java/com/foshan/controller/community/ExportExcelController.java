package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityMeterAttributesReq;
import com.foshan.form.community.request.CommunityMeterRecordReq;
import com.foshan.form.community.request.CommunityReceiptReq;
import com.foshan.form.community.request.CommunityRefundReq;
import com.foshan.form.community.request.ExportExcelReq;
import com.foshan.form.community.response.communityRefund.AddCommunityRefundRes;
import com.foshan.form.community.response.exportExcel.GetReceiptViewRes;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "导出报表模块")
@RestController
public class ExportExcelController extends BaseCommunityController {
	
	// 导出月份资料
/*	@ApiOperation(value = "导出月份资料(exportMonthData)", httpMethod = "POST", notes = "导出月份资料，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/exportMonthData", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportMonthData(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException {*/
	@ApiOperation(value = "导出月份资料(exportMonthData)", httpMethod = "POST", notes = "导出月份资料，月份 格式如：2021-08")
	@ResponseBody
	@RequestMapping(value = "/exportMonthData", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportMonthData(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		byte[] data = exportExcelService.exportMonthData(req,response);
		response.reset();  
        response.setHeader("Content-Disposition", "attachment; filename=\""+req.getMonth()+".zip\"");  
//        response.addHeader("Content-Length", "" + data.length);  
//        response.setContentType("application/octet-stream; charset=UTF-8");  
        IOUtils.write(data, response.getOutputStream());  

	}
	
	// 月份欠费及原因
	@ApiOperation(value = "月份欠费及原因(exportMonthArrearage)", httpMethod = "POST", notes = "月份欠费及原因，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/exportMonthArrearage", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportMonthArrearage(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
//	@ApiOperation(value = "应收未收报表(exportMonthArrearage)", httpMethod = "GET", notes = "应收未收报表，传空{}即可；")
//	@ResponseBody
//	@RequestMapping(value = "/exportMonthArrearage", method = {
//			RequestMethod.GET })//, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE
//	public ExportExcelRes exportMonthArrearage(@ModelAttribute String month, HttpServletRequest request, HttpServletResponse response)
//			throws IOException, ParseException {
//		ExportExcelReq req = new ExportExcelReq();
//		req.setMonth("2021-08");
//		req.setPreview(0);
		exportExcelService.exportMonthArrearage(req,response);
	}
	
	// 未售单元及未到交楼期业主费用清单
	@ApiOperation(value = "未售单元及未到交楼期业主费用清单(exportMonthUnsoldArrearage)", httpMethod = "POST", notes = "未售单元及未到交楼期业主费用清单，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/exportMonthUnsoldArrearage", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportMonthUnsoldArrearage(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.exportMonthUnsoldArrearage(req,response);
	}
	
	
	// 管理费收缴情况表
	@ApiOperation(value = "管理费收缴情况表(CollectionOfMonthManagementFees)", httpMethod = "POST", notes = "管理费收缴情况表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/collectionOfMonthManagementFees", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void collectionOfMonthManagementFees(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.collectionOfMonthManagementFees(req,response);
	}
	
	// 应收未收报表
	@ApiOperation(value = "应收未收报表(exportOutstandingReceivable)", httpMethod = "POST", notes = "应收未收报表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/exportOutstandingReceivable", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportOutstandingReceivable(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.exportOutstandingReceivable(req,response);
	}
	
	
	
/*	// 应收未收报表
	@ApiOperation(value = "应收未收报表(exportOutstandingReceivable)", httpMethod = "GET", notes = "应收未收报表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/exportOutstandingReceivable", method = {
			RequestMethod.GET })//, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE
	public ExportExcelRes exportOutstandingReceivable(@ModelAttribute String month, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		ExportExcelReq req = new ExportExcelReq();
		req.setMonth("2021-08");
		req.setPreview(1);
		ExportExcelRes  res = (ExportExcelRes) exportExcelService.exportOutstandingReceivable(req,response);
		return res;
	}*/

/*	@ApiOperation(value = "根据sql导出报表(exportExcelBySql)", httpMethod = "GET", notes = "根据sql导出报表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/exportExcelBySql", method = {
			RequestMethod.GET })//, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE
	public ExportExcelRes exportExcelBySql( HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {*/
	@ApiOperation(value = "根据sql导出报表(exportExcelBySql)", httpMethod = "POST", notes = "根据sql导出报表，SQL必填；")
	@ResponseBody
	@RequestMapping(value = "/exportExcelBySql", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportExcelBySql(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
//		ExportExcelReq req = new ExportExcelReq();
//		req.setSql("SELECT * FROM  (SELECT b.meterCode AS'表编号',b.meterName AS'表名称', a.createTime AS '创建时间',a.recordNum AS'本次读数',a.recordDate AS '本次读数日期'," + 
//				"b.allocationMethod AS'分摊类别' FROM `t_community_meter_record` a INNER JOIN `t_community_meter` b ON b.id=a.meterId WHERE b.isCommon=1 AND b.id=1)t");
		exportExcelService.exportExcelBySql(req,response);
	}
	
	@ApiOperation(value = "导出表分摊范围报表(exportCommunityMeterProperty)", httpMethod = "POST", notes = "meterId必填；")
	@ResponseBody
	@RequestMapping(value = "/exportCommunityMeterProperty", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCommunityMeterProperty(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.exportCommunityMeterProperty(req,response);
	}
	
	@ApiOperation(value = "导出表记录报表(exportCommunityMeterRecord)", httpMethod = "POST", notes = "meterName、startTime、endTime、isCommon选填")
	@ResponseBody
	@RequestMapping(value = "/exportCommunityMeterRecord", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCommunityMeterRecord(@RequestBody CommunityMeterRecordReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.exportCommunityMeterRecord(req,response);
	}
	
	@ApiOperation(value = "导出表属性报表(exportCommunityMeterAttributes)", httpMethod = "POST", notes = "attributeName、category选填")
	@ResponseBody
	@RequestMapping(value = "/exportCommunityMeterAttributes", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCommunityMeterAttributes(@RequestBody CommunityMeterAttributesReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.exportCommunityMeterAttributes(req,response);
	}
	
	@ApiOperation(value = "导出收费项目(exportCommunitypayItems)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/exportCommunitypayItems", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCommunitypayItems(@RequestBody CommunityMeterAttributesReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.exportCommunitypayItems(response);
	}
	
	@ApiOperation(value = "导出签约信息(exportAccountInfo)", httpMethod = "POST", notes = "")
	@ResponseBody
	@RequestMapping(value = "/exportAccountInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportAccountInfo(@RequestBody CommunityMeterAttributesReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.exportAccountInfo(response);
	}
	
	@ApiOperation(value = "导出表分摊运算(exportCalTotalAllocation)", httpMethod = "POST", notes = "导出表分摊运算")
	@ResponseBody
	@RequestMapping(value = "/exportCalTotalAllocation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCalTotalAllocation(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.exportCalTotalAllocation(req,response);
	
	}
	
	@ApiOperation(value = "导出单元分摊试算(exportCalItemAllocation)", httpMethod = "POST", notes = "导出单元分摊试算")
	@ResponseBody
	@RequestMapping(value = "/exportCalItemAllocation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportCalItemAllocation(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.exportCalItemAllocation(req,response);
	}
	
	@ApiOperation(value = "收款综合一览(exportReceipt)", httpMethod = "POST", notes = "收款综合一览")
	@ResponseBody
	@RequestMapping(value = "/exportReceipt", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportReceipt(@RequestBody CommunityReceiptReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.exportReceipt(req,response);
	
	}
	
	@ApiOperation(value = "获取收款综合一览列表(getReceiptView)", httpMethod = "POST", notes = "获取收款综合一览列表")
	@ResponseBody
	@RequestMapping(value = "/getReceiptView", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetReceiptViewRes getReceiptView(@RequestBody CommunityReceiptReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetReceiptViewRes res = (GetReceiptViewRes) exportExcelService.getReceiptView(req);
		return res;
	}

	@ApiOperation(value = "导出总表单元分摊(exportMeterAllocationItemList)", httpMethod = "POST", notes = "导出总表单元分摊")
	@ResponseBody
	@RequestMapping(value = "/exportMeterAllocationItemList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportMeterAllocationItemList(@RequestBody ExportExcelReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		exportExcelService.exportMeterAllocationItemList(req,response);
	}
}
