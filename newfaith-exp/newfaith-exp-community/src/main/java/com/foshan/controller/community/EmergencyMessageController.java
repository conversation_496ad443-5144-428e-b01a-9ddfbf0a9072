package com.foshan.controller.community;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.UploadedFile;
import com.foshan.form.community.request.EmergencyMessageReq;
import com.foshan.form.community.response.emergencyMessage.GetEmergencyMessageListRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

	
@Api(tags = "应急广播模块")
@RestController
public class EmergencyMessageController extends BaseCommunityController {
	
	@PostConstruct
	public void initDoLogin() {
		emergencyMessageService.gainEmergencyMessageParameter();
	}
	
	// 获取参数
	@ApiOperation(value = "获取参数(gainEmergencyMessageParameter)", httpMethod = "POST", notes = "获取参数，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/gainEmergencyMessageParameter", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse gainEmergencyMessageParameter(@RequestBody EmergencyMessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) emergencyMessageService.gainEmergencyMessageParameter();
		return res;
	}
	
	// 发送消息
	@ApiOperation(value = "发送消息(sendMsg)", httpMethod = "POST", notes = "发送消息，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/sendMsg", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse sendMsg(@RequestBody EmergencyMessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) emergencyMessageService.createXml(req);
		return res;
	}

	
	@ApiOperation(value = "接收反馈包(receiveFeedback)", httpMethod = "POST", notes = "接收反馈包")
//	@ResponseBody
//	@RequestMapping(value = "/receiveFeedback",method = { RequestMethod.POST},produces = MediaType.MULTIPART_FORM_DATA_VALUE)
//	public GenericResponse receiveFeedback(HttpServletRequest request, HttpServletResponse response,
//			@ModelAttribute UploadedFile uploadedFile) throws Exception {
	@RequestMapping(value = "/receiveFeedback",method = { RequestMethod.POST})	
	public GenericResponse receiveFeedback(@RequestParam(value="file",required=false) MultipartFile file,
			@RequestParam(value="filename",required=false) MultipartFile filename,
			HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile) throws Exception {	
		
		request.setCharacterEncoding("utf-8"); 
		//MultipartFile[] multipartFile = uploadedFile.getFilename();

		GenericResponse res = (GenericResponse) emergencyMessageService.receiveFeedback(request,filename);
		return res;
	}
	
	
	
	
	
	// 获取已发消息列表
	@ApiOperation(value = "获取已发消息列表(getEmergencyMessageList)", httpMethod = "POST", notes = "获取已发消息列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getEmergencyMessageList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetEmergencyMessageListRes getEmergencyMessageList(@RequestBody EmergencyMessageReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetEmergencyMessageListRes res = (GetEmergencyMessageListRes) emergencyMessageService.getEmergencyMessageList(req);
		return res;
	}
}
