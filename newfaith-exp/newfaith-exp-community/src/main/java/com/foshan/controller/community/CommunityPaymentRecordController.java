package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityPaymentRecordReq;
import com.foshan.form.community.request.CommunityPaymentReq;
import com.foshan.form.community.response.communityPaymentRecord.GetCommunityPaymentRecordInfoRes;
import com.foshan.form.community.response.communityPaymentRecord.GetCommunityPaymentRecordListRes;
import com.foshan.form.community.response.communityReceiptReceivables.AddCommunityReceiveRes;
import com.foshan.form.request.PaymentNotifyReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "社区收款模块")
@RestController
public class CommunityPaymentRecordController extends BaseCommunityController {

	// 获取在线支付发起的付款记录列表
	@ApiOperation(value = "获取在线支付发起的付款记录列表(getCommunityPaymentRecordList)", httpMethod = "POST", notes = "获取在线支付发起的付款记录列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPaymentRecordList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPaymentRecordListRes getCommunityPaymentRecordList(@RequestBody CommunityPaymentRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPaymentRecordListRes res = (GetCommunityPaymentRecordListRes) communityPaymentRecordService.getCommunityPaymentRecordList(req);
		return res;
	}
	
//	// 新增在线支付发起的付款记录
//	@ApiOperation(value = "新增事件类型项目(addCommunityPaymentRecord)", httpMethod = "POST", notes = "新增在线支付发起的付款记录")
//	@ResponseBody
//	@RequestMapping(value = "/addCommunityPaymentRecord", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public AddCommunityPaymentRecordRes addCommunityPaymentRecord(@RequestBody CommunityPaymentRecordReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		AddCommunityPaymentRecordRes res = (AddCommunityPaymentRecordRes) communityPaymentRecordService.addCommunityPaymentRecord(req);
//		return res;
//	}
//	
	// 修改在线支付发起的付款记录
//	@ApiOperation(value = "修改在线支付发起的付款记录(modifyCommunityPaymentRecord)", httpMethod = "POST", notes = "修改在线支付发起的付款记录，communityPaymentRecordId不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/modifyCommunityPaymentRecord", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public ModifyCommunityPaymentRecordRes modifyCommunityPaymentRecord(@RequestBody CommunityPaymentRecordReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		ModifyCommunityPaymentRecordRes res = (ModifyCommunityPaymentRecordRes) communityPaymentRecordService.modifyCommunityPaymentRecord(req);
//		return res;
//	}
//	
//	// 删除在线支付发起的付款记录
//	@ApiOperation(value = "删除事件类型项目(deleteCommunityPaymentRecord)", httpMethod = "POST", notes = "删除在线支付发起的付款记录，communityPaymentRecordId不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/deleteCommunityPaymentRecord", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public GenericResponse deleteCommunityPaymentRecord(@RequestBody CommunityPaymentRecordReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		GenericResponse res = (GenericResponse) communityPaymentRecordService.deleteCommunityPaymentRecord(req);
//		return res;
//	}
//	
	// 获取在线支付发起的付款记录详情
	@ApiOperation(value = "获取支付记录详情(getCommunityPaymentRecordInfo)", httpMethod = "POST", notes = "获取在线支付发起的付款记录详情，communityPaymentRecordId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPaymentRecordInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPaymentRecordInfoRes getCommunityPaymentRecordInfo(@RequestBody CommunityPaymentRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPaymentRecordInfoRes res = (GetCommunityPaymentRecordInfoRes) communityPaymentRecordService.getCommunityPaymentRecordInfo(req);
		return res;
	}
	
	@ApiOperation(value = "获取支付状态(getCommunityPaymentRecordStatusByCode)", httpMethod = "POST", notes = "获取在线支付发起的付款记录详情，paymentRecordCode不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityPaymentRecordStatusByCode", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityPaymentRecordInfoRes getCommunityPaymentRecordStatusByCode(@RequestBody CommunityPaymentRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityPaymentRecordInfoRes res = (GetCommunityPaymentRecordInfoRes) communityPaymentRecordService.getCommunityPaymentRecordStatusByCode(req);
		return res;
	}
	
//    // 发起支付
//	@ApiOperation(value = "收款(addCommunityReceive)", httpMethod = "POST", notes = "收款，communityReceiptReceivablesId不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/addCommunityReceive", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public AddCommunityReceiveRes addCommunityReceive(@RequestBody CommunityPaymentReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		AddCommunityReceiveRes res =  new AddCommunityReceiveRes();
//		try {
//			 res = (AddCommunityReceiveRes) communityPaymentRecordService.addCommunityReceive(req);
//		}
//		catch(Exception e) {
//			e.printStackTrace();
//			res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + e.getMessage());
//		}
//		return res;
//	}
	
	  // 管理端发起支付
	@ApiOperation(value = "管理端发起收款(/pay)", httpMethod = "POST", notes = "收款详情receiveDetialList、及收据数据communityReceiptForm不能为空；")
	@ResponseBody
	@RequestMapping(value = "/pay", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityReceiveRes addCommunityReceive(@RequestBody CommunityPaymentReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityReceiveRes res =  new AddCommunityReceiveRes();
		try {
			 res = (AddCommunityReceiveRes) communityPaymentRecordService.addCommunityReceive(req);
		}
		catch(Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + e.getMessage());
		}
		return res;
	}
	
	 // 手机端发起支付
	@ApiOperation(value = "手机端发起支付(/payByMember)", httpMethod = "POST", notes = "收款详情receiveDetialList、第三方线上支付paymentScene、及收据数据communityReceiptForm不能为空；")
	@ResponseBody
	@RequestMapping(value = "/payByMember", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityReceiveRes addCommunityReceiveByMember(@RequestBody CommunityPaymentReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityReceiveRes res =  new AddCommunityReceiveRes();
		try {
			 res = (AddCommunityReceiveRes) communityPaymentRecordService.addCommunityReceiveByMember(req);
		}
		catch(Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_PAYMENT_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PAYMENT_ERROR_INFO + e.getMessage());
		}
		return res;
	}
	
	// 在线收款支付结果回调
	@ApiOperation(value = "在线收款支付结果回调(paymentNotify)", httpMethod = "POST", notes = "return_code,clientTradeNo不能为空，如果支付成功，支付金额amount不能为空；")
	@ResponseBody
	@RequestMapping(value = "/paymentNotify", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityReceiveRes paymentNotify(@RequestBody PaymentNotifyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityReceiveRes res = (AddCommunityReceiveRes) communityPaymentRecordService.paymentNotify(req);
		return res;
	}
	

	  // 向支付模块刷新支付结果操作
	@ApiOperation(value = "查询在线支付结果(/queryPaymentResult)", httpMethod = "POST", notes = "查询在线支付结果，若支付成功且未收到支付结果的回调通知，可以触发重新通知，刷新支付状态")
	@ResponseBody
	@RequestMapping(value = "/queryPaymentResult", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse queryPaymentResult(@RequestBody CommunityPaymentRecordReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res =  new GenericResponse();
		try {
			 res = (GenericResponse) communityPaymentRecordService.queryPaymentResult(req);
		}
		catch(Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_QUERY_PAYMENT_RESULT_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_QUERY_PAYMENT_RESULT_ERROR_INFO + e.getMessage());
		}
		return res;
	}
	
//	// 发起在线发起支付
//	@ApiOperation(value = "发起在线发起支付(payment)", httpMethod = "POST", notes = "return_code,clientTradeNo不能为空，如果支付成功，支付金额amount不能为空；")
//	@ResponseBody
//	@RequestMapping(value = "/payment", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public GetCommunityReceiptReceivablesInfoRes payment(@RequestBody CommunityPaymentReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		GetCommunityReceiptReceivablesInfoRes res = (GetCommunityReceiptReceivablesInfoRes) communityPaymentRecordService.payment(req);
//		return res;
//	}
	
	// 修复ReceivablesNo
//	@ApiOperation(value = "修复ReceivablesNo(addReceivablesNO)", httpMethod = "POST", notes = "不需要传参")
//	@ResponseBody
//	@RequestMapping(value = "/addReceivablesNO", method = {
//			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
//	public GenericResponse addReceivablesNO(@RequestBody CommunityReceivablesReq req, HttpServletRequest request)
//			throws JsonProcessingException {
//		GenericResponse res = (GenericResponse) communityPaymentRecordService.addReceivablesNO(req);
//		return res;
//	}
//	
   
}
