package com.foshan.controller.community;

import javax.annotation.Resource;

import com.foshan.controller.BaseController;
import com.foshan.service.community.*;


public class BaseCommunityController extends BaseController {
//	@Resource(name = "communityDepartmentService")
//	protected ICommunityDepartmentService communityDepartmentService;
	@Resource(name = "communityMemberService")
	protected ICommunityMemberService communityMemberService;
	@Resource(name = "communityAnnouncementsService")
	protected ICommunityAnnouncementsService communityAnnouncementsService;
	@Resource(name = "communityEventCategoryService")
	protected ICommunityEventCategoryService communityEventCategoryService;
	@Resource(name = "communityEventCategoryItemsService")
	protected ICommunityEventCategoryItemsService communityEventCategoryItemsService;
	@Resource(name = "communityAssetService")
	protected ICommunityAssetService communityAssetService;
	@Resource(name = "communityEventsService")
	protected ICommunityEventsService communityEventsService;
	@Resource(name = "communityUserService")
	protected ICommunityUserService communityUserService;
	@Resource(name = "communityReservationActivitiesService")
	protected ICommunityReservationActivitiesService communityReservationActivitiesService;
	@Resource(name = "communityReservationDateService")
	protected ICommunityReservationDateService communityReservationDateService;
	@Resource(name = "communityReservationRecordService")
	protected ICommunityReservationRecordService communityReservationRecordService;
	@Resource(name = "communityPropertyService")
	protected ICommunityPropertyService communityPropertyService;
	@Resource(name = "communityColumnService")
	protected ICommunityColumnService communityColumnService;
	@Resource(name = "communityMemberPropertyService")
	protected ICommunityMemberPropertyService communityMemberPropertyService;
	@Resource(name = "optionsVoService")
	protected IOptionsVoService optionsVoService;
	@Resource(name = "communityKingdeeService")
	protected ICommunityKingdeeService communityKingdeeService;
    @Resource(name = "communityDistrictService")
    protected ICommunityDistrictService communityDistrictService;
    @Resource(name = "communityService")
    protected ICommunityService communityService;
    @Resource(name = "communityBuildingService")
    protected ICommunityBuildingService communityBuildingService;
    @Resource(name = "communityBuilderService")
    protected ICommunityBuilderService communityBuilderService;
    @Resource(name = "communityEstateService")
    protected ICommunityEstateService communityEstateService;
    @Resource(name = "communityPaymentAccountService")
    protected ICommunityPaymentAccountService communityPaymentAccountService;
    @Resource(name = "communityMeterFormulaService")
    protected ICommunityMeterFormulaService communityMeterFormulaService;
    @Resource(name = "communityMeterAttributesService")
    protected ICommunityMeterAttributesService communityMeterAttributesService;
    @Resource(name = "communityMeterService")
    protected ICommunityMeterService communityMeterService;
    @Resource(name = "communityMeterRecordService")
    protected ICommunityMeterRecordService communityMeterRecordService;
    
    @Resource(name = "communityPayItemsService")
    protected ICommunityPayItemsService communityPayItemsService;
    @Resource(name = "communityReceivablesChangesService")
    protected ICommunityReceivablesChangesService communityReceivablesChangesService;
    @Resource(name = "communityReceivablesService")
    protected ICommunityReceivablesService communityReceivablesService;
    @Resource(name = "communityReceiptReceivablesService")
    protected ICommunityReceiptReceivablesService communityReceiptReceivablesService;
    @Resource(name = "communityReceiptService")
    protected ICommunityReceiptService communityReceiptService;
    @Resource(name = "communityFormulaTempleteService")
    protected ICommunityFormulaTempleteService communityFormulaTempleteService; 
    @Resource(name = "communityPaymentRecordService")
    protected ICommunityPaymentRecordService communityPaymentRecordService;
    @Resource(name = "communityRefundService")
    protected ICommunityRefundService communityRefundService;
    
    @Resource(name = "exportExcelService")
    protected IExportExcelService exportExcelService;
    @Resource(name = "importExcelService")
    protected IImportExcelService importExcelService;
    @Resource(name = "communityMeterAllocationService")
    protected ICommunityMeterAllocationService communityMeterAllocationService;
    @Resource(name = "communityBankDepositRecordService")
    protected ICommunityBankDepositRecordService communityBankDepositRecordService;
    @Resource(name = "communityBankDepositBatchService")
    protected ICommunityBankDepositBatchService communityBankDepositBatchService;
    @Resource(name = "communityInitService")
    protected ICommunityInitService communityInitService;
    @Resource(name = "communityStatisticsService")
    protected ICommunityStatisticsService communityStatisticsService;
    @Resource(name = "communityPropertyServiceService")
    protected ICommunityPropertyServiceService communityPropertyServiceService;
    @Resource(name = "communityDictionaryService")
    protected ICommunityDictionaryService communityDictionaryService;
    @Resource(name = "communityInvoiceService")
    protected ICommunityInvoiceService communityInvoiceService;
    @Resource(name = "communityPayItemsPriceService")
    protected ICommunityPayItemsPriceService communityPayItemsPriceService;
    @Resource(name = "aIDeviceService")
    protected IAIDeviceService aIDeviceService;
    @Resource(name = "emergencyMessageService")
    protected IEmergencyMessageService emergencyMessageService;
    @Resource(name = "deviceCorrelationService")
    protected IDeviceCorrelationService deviceCorrelationService;
    @Resource(name = "deviceService")
    protected IDeviceService deviceService;
    @Resource(name = "receiveMessageTypeService")
    protected IReceiveMessageTypeService receiveMessageTypeService;
    @Resource(name = "messageAccountService")
    protected IMessageAccountService messageAccountService;
    @Resource(name = "warningMessageService")
    protected IWarningMessageService warningMessageService;
    @Resource(name = "communityMeasuresService")
    protected ICommunityMeasuresService communityMeasuresService;
    @Resource(name = "communityContractService")
    protected ICommunityContractService communityContractService;
    @Resource(name = "communityDecorationAttachmentService")
    protected ICommunityDecorationAttachmentService communityDecorationAttachmentService;
    @Resource(name = "communityDecorationItemsService")
    protected ICommunityDecorationItemsService communityDecorationItemsService;
    @Resource(name = "communityInspectionRecordService")
    protected ICommunityInspectionRecordService communityInspectionRecordService;
    @Resource(name = "communityEventNotificationService")
    protected ICommunityEventNotificationService communityEventNotificationService;
    @Resource(name = "communityInstallmentSmsService")
    protected ICommunityInstallmentSmsService communityInstallmentSmsService;
}
