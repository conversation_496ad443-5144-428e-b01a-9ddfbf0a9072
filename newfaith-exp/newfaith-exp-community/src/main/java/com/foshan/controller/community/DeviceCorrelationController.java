package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.DeviceCorrelationReq;
import com.foshan.form.community.response.deviceCorrelation.AddDeviceCorrelationRes;
import com.foshan.form.community.response.deviceCorrelation.GetDeviceCorrelationInfoRes;
import com.foshan.form.community.response.deviceCorrelation.GetDeviceCorrelationListRes;
import com.foshan.form.community.response.deviceCorrelation.ModifyDeviceCorrelationRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "设备关联模块")
@RestController
public class DeviceCorrelationController extends BaseCommunityController {

	// 获取设备关联列表
	@ApiOperation(value = "获取设备关联列表(getDeviceCorrelationList)", httpMethod = "POST", notes = "获取设备关联列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getDeviceCorrelationList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetDeviceCorrelationListRes getDeviceCorrelationList(@RequestBody DeviceCorrelationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetDeviceCorrelationListRes res = (GetDeviceCorrelationListRes) deviceCorrelationService.getDeviceCorrelationList(req);
		return res;
	}
	
	// 新增设备关联
	@ApiOperation(value = "新增设备关联(addDeviceCorrelation)", httpMethod = "POST", notes = "新增设备关联")
	@ResponseBody
	@RequestMapping(value = "/addDeviceCorrelation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddDeviceCorrelationRes addDeviceCorrelation(@RequestBody DeviceCorrelationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddDeviceCorrelationRes res = (AddDeviceCorrelationRes) deviceCorrelationService.addDeviceCorrelation(req);
		return res;
	}
	
	// 修改设备关联
	@ApiOperation(value = "修改设备关联(modifyDeviceCorrelation)", httpMethod = "POST", notes = "修改设备关联，DeviceCorrelationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyDeviceCorrelation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyDeviceCorrelationRes modifyDeviceCorrelation(@RequestBody DeviceCorrelationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyDeviceCorrelationRes res = (ModifyDeviceCorrelationRes) deviceCorrelationService.modifyDeviceCorrelation(req);
		return res;
	}
	
	// 删除设备关联
	@ApiOperation(value = "删除事件类型项目(deleteDeviceCorrelation)", httpMethod = "POST", notes = "删除设备关联，DeviceCorrelationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteDeviceCorrelation", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteDeviceCorrelation(@RequestBody DeviceCorrelationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) deviceCorrelationService.deleteDeviceCorrelation(req);
		return res;
	}
	
	// 获取设备关联详情
	@ApiOperation(value = "获取设备关联详情(getDeviceCorrelationInfo)", httpMethod = "POST", notes = "获取设备关联详情，DeviceCorrelationId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getDeviceCorrelationInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetDeviceCorrelationInfoRes getDeviceCorrelationInfo(@RequestBody DeviceCorrelationReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetDeviceCorrelationInfoRes res = (GetDeviceCorrelationInfoRes) deviceCorrelationService.getDeviceCorrelationInfo(req);
		return res;
	}
   
}
