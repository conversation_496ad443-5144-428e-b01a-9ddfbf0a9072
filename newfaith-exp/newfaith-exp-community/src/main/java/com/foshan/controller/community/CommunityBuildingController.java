package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityBuildingReq;
import com.foshan.form.community.response.communityBuilding.AddCommunityBuildingRes;
import com.foshan.form.community.response.communityBuilding.GetCommunityBuildingInfoRes;
import com.foshan.form.community.response.communityBuilding.GetCommunityBuildingListRes;
import com.foshan.form.community.response.communityBuilding.ModifyCommunityBuildingRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "楼栋模块")
@RestController
public class CommunityBuildingController extends BaseCommunityController {

	// 获取楼栋列表
	@ApiOperation(value = "获取楼栋列表(getCommunityBuildingList)", httpMethod = "POST", notes = "获取楼栋列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityBuildingList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityBuildingListRes getCommunityBuildingList(@RequestBody CommunityBuildingReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityBuildingListRes res = (GetCommunityBuildingListRes) communityBuildingService.getCommunityBuildingList(req);
		return res;
	}
	
	// 新增楼栋
	@ApiOperation(value = "新增事件类型项目(addCommunityBuilding)", httpMethod = "POST", notes = "新增楼栋")
	@ResponseBody
	@RequestMapping(value = "/addCommunityBuilding", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityBuildingRes addCommunityBuilding(@RequestBody CommunityBuildingReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityBuildingRes res = (AddCommunityBuildingRes) communityBuildingService.addCommunityBuilding(req);
		return res;
	}
	
	// 修改楼栋
	@ApiOperation(value = "修改楼栋(modifyCommunityBuilding)", httpMethod = "POST", notes = "修改楼栋，communityBuildingId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityBuilding", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityBuildingRes modifyCommunityBuilding(@RequestBody CommunityBuildingReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityBuildingRes res = (ModifyCommunityBuildingRes) communityBuildingService.modifyCommunityBuilding(req);
		return res;
	}
	
	// 删除楼栋
	@ApiOperation(value = "删除事件类型项目(deleteCommunityBuilding)", httpMethod = "POST", notes = "删除楼栋，communityBuildingId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityBuilding", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityBuilding(@RequestBody CommunityBuildingReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityBuildingService.deleteCommunityBuilding(req);
		return res;
	}
	
	// 获取楼栋详情
	@ApiOperation(value = "获取楼栋详情(getCommunityBuildingInfo)", httpMethod = "POST", notes = "获取楼栋详情，communityBuildingId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityBuildingInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityBuildingInfoRes getCommunityBuildingInfo(@RequestBody CommunityBuildingReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityBuildingInfoRes res = (GetCommunityBuildingInfoRes) communityBuildingService.getCommunityBuildingInfo(req);
		return res;
	}
   
}
