package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityMeterFormulaReq;
import com.foshan.form.community.response.communityMeterFormula.AddCommunityMeterFormulaRes;
import com.foshan.form.community.response.communityMeterFormula.GetCommunityMeterFormulaInfoRes;
import com.foshan.form.community.response.communityMeterFormula.GetCommunityMeterFormulaListRes;
import com.foshan.form.community.response.communityMeterFormula.ModifyCommunityMeterFormulaRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "自定义分摊公式模块")
@RestController
public class CommunityMeterFormulaController extends BaseCommunityController {

	// 获取自定义分摊公式列表
	@ApiOperation(value = "获取自定义分摊公式列表(getCommunityMeterFormulaList)", httpMethod = "POST", notes = "获取自定义分摊公式列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMeterFormulaList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterFormulaListRes getCommunityMeterFormulaList(@RequestBody CommunityMeterFormulaReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMeterFormulaListRes res = (GetCommunityMeterFormulaListRes) communityMeterFormulaService.getCommunityMeterFormulaList(req);
		return res;
	}
	
	// 新增自定义分摊公式
	@ApiOperation(value = "新增自定义分摊公式(addCommunityMeterFormula)", httpMethod = "POST", notes = "新增自定义分摊公式")
	@ResponseBody
	@RequestMapping(value = "/addCommunityMeterFormula", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityMeterFormulaRes addCommunityMeterFormula(@RequestBody CommunityMeterFormulaReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityMeterFormulaRes res = (AddCommunityMeterFormulaRes) communityMeterFormulaService.addCommunityMeterFormula(req);
		return res;
	}
	
	// 修改自定义分摊公式
	@ApiOperation(value = "修改自定义分摊公式(modifyCommunityMeterFormula)", httpMethod = "POST", notes = "修改自定义分摊公式，communityMeterFormulaId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityMeterFormula", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityMeterFormulaRes modifyCommunityMeterFormula(@RequestBody CommunityMeterFormulaReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityMeterFormulaRes res = (ModifyCommunityMeterFormulaRes) communityMeterFormulaService.modifyCommunityMeterFormula(req);
		return res;
	}
	
	// 删除自定义分摊公式
	@ApiOperation(value = "删除事件类型项目(deleteCommunityMeterFormula)", httpMethod = "POST", notes = "删除自定义分摊公式，communityMeterFormulaId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityMeterFormula", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityMeterFormula(@RequestBody CommunityMeterFormulaReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterFormulaService.deleteCommunityMeterFormula(req);
		return res;
	}
	
	// 获取自定义分摊公式详情
	@ApiOperation(value = "获取自定义分摊公式详情(getCommunityMeterFormulaInfo)", httpMethod = "POST", notes = "获取自定义分摊公式详情，communityMeterFormulaId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMeterFormulaInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMeterFormulaInfoRes getCommunityMeterFormulaInfo(@RequestBody CommunityMeterFormulaReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMeterFormulaInfoRes res = (GetCommunityMeterFormulaInfoRes) communityMeterFormulaService.getCommunityMeterFormulaInfo(req);
		return res;
	}
	
	// 关联表
	@ApiOperation(value = "关联表(relevanceMeter)", httpMethod = "POST", notes = "关联表，communityMeterFormulaId、GenericResponse不能为空；")
	@ResponseBody
	@RequestMapping(value = "/relevanceMeter", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse relevanceMeter(@RequestBody CommunityMeterFormulaReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterFormulaService.relevanceMeter(req);
		return res;
	}
	
	// 取消关联表
	@ApiOperation(value = "取消关联表(cancelRelevanceMeter)", httpMethod = "POST", notes = "取消关联表，communityMeterFormulaId、GenericResponse不能为空；")
	@ResponseBody
	@RequestMapping(value = "/cancelRelevanceMeter", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse cancelRelevanceMeter(@RequestBody CommunityMeterFormulaReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMeterFormulaService.cancelRelevanceMeter(req);
		return res;
	}
   
}
