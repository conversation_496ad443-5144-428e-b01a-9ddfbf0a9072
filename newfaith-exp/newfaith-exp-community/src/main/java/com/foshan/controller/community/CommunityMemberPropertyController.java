package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityMemberPropertyReq;
import com.foshan.form.community.request.ExportExcelReq;
import com.foshan.form.community.response.communityMemberProperty.GetCommunityMemberPropertyInfoRes;
import com.foshan.form.community.response.communityMemberProperty.GetCommunityMemberPropertyListRes;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "会员资产模块")
@RestController
public class CommunityMemberPropertyController extends BaseCommunityController {

	// 获取会员资产列表
	@ApiOperation(value = "获取会员资产列表(getCommunityMemberPropertyList)", httpMethod = "POST", notes = "获取会员资产列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMemberPropertyList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMemberPropertyListRes getCommunityMemberPropertyList(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMemberPropertyListRes res = (GetCommunityMemberPropertyListRes) communityMemberPropertyService.getCommunityMemberPropertyList(req);
		return res;
	}
	
	// 获取租赁合同列表
	@ApiOperation(value = "获取租赁合同列表(getCommunityRentMemberPropertyList)", httpMethod = "POST", notes = "获取租赁合同列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityRentMemberPropertyList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMemberPropertyListRes getCommunityRentMemberPropertyList(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMemberPropertyListRes res = (GetCommunityMemberPropertyListRes) communityMemberPropertyService.getCommunityMemberPropertyList(req);
		return res;
	}
	
	
	// 会员绑定资产
	@ApiOperation(value = "会员绑定资产(bindingCommunityMemberProperty)", httpMethod = "POST", notes = "会员绑定资产，phone、userName和propertyId不能为空")
	@ResponseBody
	@RequestMapping(value = "/bindingCommunityMemberProperty", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse bindingCommunityMemberProperty(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberPropertyService.bindingCommunityMemberProperty(req);
		return res;
	}
	
	
	// 审核会员资产
	@ApiOperation(value = "审核会员资产(auditCommunityMemberProperty)", httpMethod = "POST", notes = "审核会员资产，memberPropertyId和auditState不能为空；")
	@ResponseBody
	@RequestMapping(value = "/auditCommunityMemberProperty", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse auditCommunityMemberProperty(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberPropertyService.auditCommunityMemberProperty(req);
		return res;
	}
	
	// 新增会员资产
	@ApiOperation(value = "新增会员资产(addCommunityMemberProperty)", httpMethod = "POST", notes = "新增会员资产，PropertyIdList和MemberId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityMemberProperty", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityMemberProperty(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberPropertyService.addCommunityMemberProperty(req,null);
		return res;
	}
	
	// 新增租赁合同
	@ApiOperation(value = "新增租赁合同(addCommunityRentMemberProperty)", httpMethod = "POST", notes = "新增租赁合同，PropertyIdList和MemberId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityRentMemberProperty", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse addCommunityRentMemberProperty(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberPropertyService.addCommunityRentMemberProperty(req);
		return res;
	}
	
	// 修改会员资产
	@ApiOperation(value = "修改会员资产(modifyCommunityMemberProperty)", httpMethod = "POST", notes = "修改会员资产，MemberPropertyId、PropertyIdList和MemberId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityMemberProperty", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityMemberProperty(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberPropertyService.modifyCommunityMemberProperty(req);
		return res;
	}
	
	// 修改租赁合同
	@ApiOperation(value = "修改租赁合同(modifyCommunityRentMemberProperty)", httpMethod = "POST", notes = "修改租赁合同，MemberPropertyId、PropertyIdList和MemberId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityRentMemberProperty", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse modifyCommunityRentMemberProperty(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberPropertyService.modifyCommunityRentMemberProperty(req);
		return res;
	}
	
	// 删除会员资产
	@ApiOperation(value = "删除会员资产(deleteCommunityMemberProperty)", httpMethod = "POST", notes = "删除会员资产，MemberPropertyId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityMemberProperty", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityMemberProperty(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberPropertyService.deleteCommunityMemberProperty(req);
		return res;
	}
	
	
	// 删除租赁合同
	@ApiOperation(value = "租赁合同(deleteCommunityRentMemberProperty)", httpMethod = "POST", notes = "租赁合同，MemberPropertyId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityRentMemberProperty", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityRentMemberProperty(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberPropertyService.deleteCommunityMemberProperty(req);
		return res;
	}
	// 获取会员资产详情
	@ApiOperation(value = "获取会员资产详情(getCommunityMemberPropertyInfo)", httpMethod = "POST", notes = "获取会员资产详情，MemberPropertyId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityMemberPropertyInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMemberPropertyInfoRes getCommunityMemberPropertyInfo(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMemberPropertyInfoRes res = (GetCommunityMemberPropertyInfoRes) communityMemberPropertyService.getCommunityMemberPropertyInfo(req);
		return res;
	}
	
	// 获取租赁合同详情
	@ApiOperation(value = "获取租赁合同详情(getCommunityRentMemberPropertyInfo)", httpMethod = "POST", notes = "获取租赁合同详情，MemberPropertyId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityRentMemberPropertyInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityMemberPropertyInfoRes getCommunityRentMemberPropertyInfo(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityMemberPropertyInfoRes res = (GetCommunityMemberPropertyInfoRes) communityMemberPropertyService.getCommunityMemberPropertyInfo(req);
		return res;
	}
	
	// 初始化停车位数据
	@ApiOperation(value = "初始化停车位数据(initializeParkingData)", httpMethod = "POST", notes = "初始化停车位数据；")
	@ResponseBody
	@RequestMapping(value = "/initializeParkingData", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse initializeParkingData(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberPropertyService.initializeParkingData();
		return res;
	}
	
	// 业主认证
	@ApiOperation(value = "业主认证(ownerApprove)", httpMethod = "POST", notes = "业主认证，phone、IdCard不能为空；UnitCode、PropertyId不能同时为空！")
	@ResponseBody
	@RequestMapping(value = "/ownerApprove", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse ownerApprove(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityMemberPropertyService.ownerApprove(req);
		return res;
	}
	
	// 导出单元计费日期
	@ApiOperation(value = "导出单元计费日期(exportBillingDate)", httpMethod = "POST", notes = "导出单元计费日期")
	@ResponseBody
	@RequestMapping(value = "/exportBillingDate", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportBillingDate(@RequestBody CommunityMemberPropertyReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityMemberPropertyService.exportBillingDate(req,response);
	}
}
