package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityRefundNotifyReq;
import com.foshan.form.community.request.CommunityRefundReq;
import com.foshan.form.community.response.communityRefund.AddCommunityRefundRes;
import com.foshan.form.community.response.communityRefund.GetCommunityRefundInfoRes;
import com.foshan.form.community.response.communityRefund.GetCommunityRefundListRes;
import com.foshan.form.community.response.communityRefund.ModifyCommunityRefundRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "退款记录模块")
@RestController
public class CommunityRefundController extends BaseCommunityController {

	// 获取退款记录列表
	@ApiOperation(value = "获取退款记录列表(getCommunityRefundList)", httpMethod = "POST", notes = "获取退款记录列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityRefundList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityRefundListRes getCommunityRefundList(@RequestBody CommunityRefundReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityRefundListRes res = (GetCommunityRefundListRes) communityRefundService.getCommunityRefundList(req);
		return res;
	}
	
	// 新增退款记录
	@ApiOperation(value = "新增退款记录(addCommunityRefund)", httpMethod = "POST", notes = "新增退款记录")
	@ResponseBody
	@RequestMapping(value = "/addCommunityRefund", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityRefundRes addCommunityRefund(@RequestBody CommunityRefundReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityRefundRes res = (AddCommunityRefundRes) communityRefundService.addCommunityRefund(req);
		return res;
	}
	
	// 修改退款记录
	@ApiOperation(value = "修改退款记录(modifyCommunityRefund)", httpMethod = "POST", notes = "修改退款记录，communityRefundId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityRefund", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityRefundRes modifyCommunityRefund(@RequestBody CommunityRefundReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityRefundRes res = (ModifyCommunityRefundRes) communityRefundService.modifyCommunityRefund(req);
		return res;
	}
	
	// 删除退款记录
	@ApiOperation(value = "删除事件类型项目(deleteCommunityRefund)", httpMethod = "POST", notes = "删除退款记录，communityRefundId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityRefund", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityRefund(@RequestBody CommunityRefundReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityRefundService.deleteCommunityRefund(req);
		return res;
	}
	
	// 获取退款记录详情
	@ApiOperation(value = "获取退款记录详情(getCommunityRefundInfo)", httpMethod = "POST", notes = "获取退款记录详情，communityRefundId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityRefundInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityRefundInfoRes getCommunityRefundInfo(@RequestBody CommunityRefundReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityRefundInfoRes res = (GetCommunityRefundInfoRes) communityRefundService.getCommunityRefundInfo(req);
		return res;
	}
	
	
	// 接收支付模块的退款结果通知消息
	@ApiOperation(value = "接收支付模块的退款结果通知消息(refundNotify)", httpMethod = "POST", notes = "接收支付模块的退款结果通知消息；")
	@ResponseBody
	@RequestMapping(value = "/refundNotify", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityRefundRes refundNotify(@RequestBody CommunityRefundNotifyReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityRefundRes res = (ModifyCommunityRefundRes) communityRefundService.refundNotify(req);
		return res;
	}
   
}
