package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.DeviceReq;
import com.foshan.form.community.response.device.AddDeviceRes;
import com.foshan.form.community.response.device.GetDeviceInfoRes;
import com.foshan.form.community.response.device.GetDeviceListRes;
import com.foshan.form.community.response.device.ModifyDeviceRes;
import com.foshan.form.response.GenericResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "设备表模块")
@RestController
public class DeviceController extends BaseCommunityController {

	// 获取设备表列表
	@ApiOperation(value = "获取设备表列表(getDeviceList)", httpMethod = "POST", notes = "获取设备表列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getDeviceList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetDeviceListRes getDeviceList(@RequestBody DeviceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetDeviceListRes res = (GetDeviceListRes) deviceService.getDeviceList(req);
		return res;
	}
	
	// 新增设备
	@ApiOperation(value = "新增设备(addDevice)", httpMethod = "POST", notes = "新增设备表")
	@ResponseBody
	@RequestMapping(value = "/addDevice", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddDeviceRes addDevice(@RequestBody DeviceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddDeviceRes res = (AddDeviceRes) deviceService.addDevice(req);
		return res;
	}
	
	// 修改设备表
	@ApiOperation(value = "修改设备表(modifyDevice)", httpMethod = "POST", notes = "修改设备表，deviceId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyDevice", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyDeviceRes modifyDevice(@RequestBody DeviceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyDeviceRes res = (ModifyDeviceRes) deviceService.modifyDevice(req);
		return res;
	}
	
	// 删除设备
	@ApiOperation(value = "删除设备(deleteDevice)", httpMethod = "POST", notes = "删除设备表，deviceId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteDevice", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteDevice(@RequestBody DeviceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) deviceService.deleteDevice(req);
		return res;
	}
	
	// 获取设备表详情
	@ApiOperation(value = "获取设备表详情(getDeviceInfo)", httpMethod = "POST", notes = "获取设备表详情，deviceId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getDeviceInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetDeviceInfoRes getDeviceInfo(@RequestBody DeviceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetDeviceInfoRes res = (GetDeviceInfoRes) deviceService.getDeviceInfo(req);
		return res;
	}
	
	// 更新融合视频平台摄像头
	@ApiOperation(value = "更新融合视频平台摄像头(updateCameraDevice)", httpMethod = "POST", notes = "更新融合视频平台摄像头")
	@ResponseBody
	@RequestMapping(value = "/updateCameraDevice", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse updateCameraDevice(@RequestBody DeviceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) deviceService.updateCameraDevice();
		return res;
	}
	
	// 设备绑定会员
	@ApiOperation(value = "设备绑定会员(bindingDeviceAndMember)", httpMethod = "POST", notes = "设备绑定会员，"
			+ "deviceIdList不能为空；propertyIdList、districtIdList和buildingIdList不能同时为空！")
	@ResponseBody
	@RequestMapping(value = "/bindingDeviceAndMember", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse bindingDeviceAndMember(@RequestBody DeviceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) deviceService.bindingDeviceAndMember(req);
		return res;
	}
	
	// 设备解绑定会员
	@ApiOperation(value = "设备解绑定会员(unbindingDeviceAndMember)", httpMethod = "POST", notes = "设备解绑定会员，"
			+ "deviceIdList不能为空；propertyIdList、districtIdList和buildingIdList不能同时为空！")
	@ResponseBody
	@RequestMapping(value = "/unbindingDeviceAndMember", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse unbindingDeviceAndMember(@RequestBody DeviceReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) deviceService.unbindingDeviceAndMember(req);
		return res;
	}
	
   
}
