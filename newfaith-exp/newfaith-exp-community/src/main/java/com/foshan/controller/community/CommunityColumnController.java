package com.foshan.controller.community;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.community.request.CommunityColumnReq;
import com.foshan.form.community.response.communityColumn.GetCommunityColumnListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.column.AddColumnRes;
import com.foshan.form.response.column.ModifyColumnRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "社区栏目模块")
@RestController
public class CommunityColumnController extends BaseCommunityController{

    // 获取社区栏目列表
    @ApiOperation(value = "获取社区栏目列表(getCommunityColumnList)", httpMethod = "POST", notes = "获取社区栏目列表，columnId不能为空；")
    @ResponseBody
    @RequestMapping(value = "/getCommunityColumnList", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCommunityColumnListRes getCommunityColumnList(@RequestBody CommunityColumnReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetCommunityColumnListRes res = (GetCommunityColumnListRes) communityColumnService.getCommunityColumnList(req);
        return res;
    }
    // 新增社区栏目
    @ApiOperation(value = "新增社区栏目列表(addCommunityColumn)", httpMethod = "POST", notes = "新增社区栏目列表；")
    @ResponseBody
    @RequestMapping(value = "/addCommunityColumn", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public AddColumnRes addCommunityColumn(@RequestBody CommunityColumnReq req, HttpServletRequest request)
            throws JsonProcessingException {
        AddColumnRes res = (AddColumnRes) communityColumnService.addCommunityColumn(req);
        return res;
    }
    // 修改社区栏目
    @ApiOperation(value = "修改社区栏目(modifyCommunityColumn)", httpMethod = "POST", notes = "修改社区栏目，columnId不能为空；")
    @ResponseBody
    @RequestMapping(value = "/modifyCommunityColumn", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ModifyColumnRes modifyCommunityColumn(@RequestBody CommunityColumnReq req, HttpServletRequest request)
            throws JsonProcessingException {
        ModifyColumnRes res = (ModifyColumnRes) communityColumnService.modifyCommunityColumn(req);
        return res;
    }
    // 删除社区栏目
    @ApiOperation(value = "删除社区栏目(deleteCommunityColumn)", httpMethod = "POST", notes = "删除社区栏目，columnId不能为空；")
    @ResponseBody
    @RequestMapping(value = "/deleteCommunityColumn", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GenericResponse deleteCommunityColumn(@RequestBody CommunityColumnReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GenericResponse res = (GenericResponse) communityColumnService.deleteCommunityColumn(req);
        return res;
    }
    // 获取详情社区栏目
    @ApiOperation(value = "获取详情社区栏目(getCommunityColumnInfo)", httpMethod = "POST", notes = "获取详情社区栏目，ColumnId不能为空；")
    @ResponseBody
    @RequestMapping(value = "/getCommunityColumnInfo", method = {
            RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public GetCommunityColumnListRes getCommunityColumnInfo(@RequestBody CommunityColumnReq req, HttpServletRequest request)
            throws JsonProcessingException {
        GetCommunityColumnListRes res = (GetCommunityColumnListRes) communityColumnService.getCommunityColumnInfo(req);
        return res;
    }
}
