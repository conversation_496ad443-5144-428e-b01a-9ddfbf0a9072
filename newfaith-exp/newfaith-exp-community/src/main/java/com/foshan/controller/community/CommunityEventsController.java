package com.foshan.controller.community;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.form.UploadedFile;
import com.foshan.form.community.request.CommunityEventsReq;
import com.foshan.form.community.request.ExportExcelReq;
import com.foshan.form.community.request.ImportExcelReq;
import com.foshan.form.community.response.communityEvent.AddCommunityEventsRes;
import com.foshan.form.community.response.communityEvent.GetCommunityEventsInfo;
import com.foshan.form.community.response.communityEvent.GetCommunityEventsListRes;
import com.foshan.form.community.response.communityEvent.ModifyCommunityEventsRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "事件型模块")
@RestController
public class CommunityEventsController extends BaseCommunityController {

	// 获取事件项目列表
	@ApiOperation(value = "获取事件项目列表(getCommunityEventsList)", httpMethod = "POST", notes = "获取事件项目列表，传空{}即可；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEventsList", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEventsListRes getCommunityEventsList(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEventsListRes res = (GetCommunityEventsListRes) communityEventsService.getCommunityEventsList(req);
		return res;
	}
	
	// 新增事件项目
	@ApiOperation(value = "新增事件项目(addCommunityEvents)", httpMethod = "POST", notes = "新增事件项目，eventCategoryId、orders、itemName和itemkey不能为空；")
	@ResponseBody
	@RequestMapping(value = "/addCommunityEvents", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public AddCommunityEventsRes addCommunityEvents(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		AddCommunityEventsRes res = (AddCommunityEventsRes) communityEventsService.addCommunityEvents(req);
		return res;
	}
	
	// 修改事件项目
	@ApiOperation(value = "修改事件项目(modifyCommunityEvents)", httpMethod = "POST", notes = "修改事件项目，eventCategoryId、eventCategoryItemsId、orders、itemName和itemkey不能为空；")
	@ResponseBody
	@RequestMapping(value = "/modifyCommunityEvents", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ModifyCommunityEventsRes modifyCommunityEvents(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		ModifyCommunityEventsRes res = (ModifyCommunityEventsRes) communityEventsService.modifyCommunityEvents(req);
		return res;
	}
	
	// 删除事件项目
	@ApiOperation(value = "删除事件项目(deleteCommunityEvents)", httpMethod = "POST", notes = "删除事件项目，eventCategoryItemsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/deleteCommunityEvents", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse deleteCommunityEvents(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEventsService.deleteCommunityEvents(req);
		return res;
	}
	
	// 获取事件项目详情
	@ApiOperation(value = "获取事件项目详情(getCommunityEventsInfo)", httpMethod = "POST", notes = "获取事件项目详情，eventCategoryItemsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/getCommunityEventsInfo", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GetCommunityEventsInfo getCommunityEventsInfo(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GetCommunityEventsInfo res = (GetCommunityEventsInfo) communityEventsService.getCommunityEventsInfo(req);
		return res;
	}
	
	
	
	// 提交事件审核
	@ApiOperation(value = "提交事件审核(submitCommunityEvents)", httpMethod = "POST", notes = "提交事件审核，eventsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/submitCommunityEvents", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse submitCommunityEvents(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		req.setEventState(null!=req.getEventState() ? req.getEventState() : 1);
		GenericResponse res = (GenericResponse) communityEventsService.setEventstate(req);
		return res;
	}
	
	// 审核事件
	@ApiOperation(value = "审核事件(auditCommunityEvents)", httpMethod = "POST", notes = "审核事件，eventsId、eventState不能为空；")
	@ResponseBody
	@RequestMapping(value = "/auditCommunityEvents", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse auditCommunityEvents(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEventsService.setEventstate(req);
		return res;
	}
	
	// 派单
	@ApiOperation(value = "派单(allocationCommunityEvents)", httpMethod = "POST", notes = "派单，eventsId、handlerId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/allocationCommunityEvents", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse allocationCommunityEvents(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		req.setEventState(4);
		GenericResponse res = (GenericResponse) communityEventsService.setEventstate(req);
		return res;
	}
	
	// 完成
	@ApiOperation(value = "完成(completeCommunityEvents)", httpMethod = "POST", notes = "完成，eventsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/completeCommunityEvents", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse completeCommunityEvents(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		req.setEventState(5);
		GenericResponse res = (GenericResponse) communityEventsService.setEventstate(req);
		return res;
	}
	
	// 撤消
	@ApiOperation(value = "撤消(cancelCommunityEvents)", httpMethod = "POST", notes = "撤消，eventsId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/cancelCommunityEvents", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse cancelCommunityEvents(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		req.setEventState(6);
		GenericResponse res = (GenericResponse) communityEventsService.setEventstate(req);
		return res;
	}
	
	
	// 更新云之家参数
	@ApiOperation(value = "更新云之家参数(updateYunzhijiaParameter)", httpMethod = "POST", notes = "更新云之家参数；")
	@ResponseBody
	@RequestMapping(value = "/updateYunzhijiaParameter", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse updateYunzhijiaParameter(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		req.setEventState(6);
		GenericResponse res = (GenericResponse) communityEventsService.updateYunzhijiaParameter();
		return res;
	}
	
	// 生成施工证
	@ApiOperation(value = "生成施工证(generateConstructionPermit)", httpMethod = "POST", notes = "生成施工证；")
	@ResponseBody
	@RequestMapping(value = "/generateConstructionPermit", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse generateConstructionPermit(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		req.setEventState(6);
		GenericResponse res = (GenericResponse) communityEventsService.generateConstructionPermit(req,null);
		return res;
	}
		
	// 部门验收装修申请项目
	@ApiOperation(value = "部门验收装修申请项目(departmentAudit)", httpMethod = "POST", notes = "部门验收装修申请项目；")
	@ResponseBody
	@RequestMapping(value = "/departmentAudit", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse departmentAudit(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEventsService.departmentAudit(req);
		return res;
	}
	
	@ApiOperation(value = "导出退款申请(exportEventsRefund)", httpMethod = "POST", notes = "导出退款申请")
	@ResponseBody
	@RequestMapping(value = "/exportEventsRefund", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void exportEventsRefund(@RequestBody CommunityEventsReq req, HttpServletRequest request, HttpServletResponse response)
			throws IOException, ParseException {
		communityEventsService.exportEventsRefund(req,response);
	}
	
	// 设置已完成退款的装修申请
	@ApiOperation(value = "设置已完成退款的装修申请(setEventsRefund)", httpMethod = "POST", notes = "设置已完成退款的装修申请；")
	@ResponseBody
	@RequestMapping(value = "/setEventsRefund", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse setEventsRefund(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEventsService.setEventsRefund(req);
		return res;
	}
	
	@ApiOperation(value = "导入已完成退款的装修申请(importEventsRefund)", httpMethod = "POST", notes = "导入已完成退款的装修申请；")
	@ResponseBody
	@RequestMapping(value = "/importEventsRefund",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importBuilding(HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile) throws Exception {
		GenericResponse res = (GenericResponse) communityEventsService.importEventsRefund(request);
		return res;
	}
	
	
	// 审核事件
	@ApiOperation(value = "审核事件(platformAuditCommunityEvents)", httpMethod = "POST", notes = "审核事件，eventsId、eventState不能为空；")
	@ResponseBody
	@RequestMapping(value = "/platformAuditCommunityEvents", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse platformAuditCommunityEvents(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEventsService.audit(req);
		return res;
	}
	
	
	// 初始化装修项目附件
	@ApiOperation(value = "初始化装修项目附件(initializeItemsAttachment)", httpMethod = "POST", notes = "初始化装修项目附件；")
	@ResponseBody
	@RequestMapping(value = "/initializeItemsAttachment", method = {
			RequestMethod.POST }, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse initializeItemsAttachment(@RequestBody CommunityEventsReq req, HttpServletRequest request)
			throws JsonProcessingException {
		GenericResponse res = (GenericResponse) communityEventsService.initializeItemsAttachment(req);
		return res;
	}
}
