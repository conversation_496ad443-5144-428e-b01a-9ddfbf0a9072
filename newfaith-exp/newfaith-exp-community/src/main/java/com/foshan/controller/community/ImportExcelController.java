package com.foshan.controller.community;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.foshan.form.UploadedFile;
import com.foshan.form.community.request.ImportExcelReq;
import com.foshan.form.response.GenericResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "导入模块")
@RestController
public class ImportExcelController extends BaseCommunityController {
	

		
	@ApiOperation(value = "导入单元信息(importUnitInfo)", httpMethod = "POST", notes = "导入单元信息，districtId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/importUnitInfo",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importUnitInfo(HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile, Integer districtId, Integer autoCreateUnit) throws Exception {
		ImportExcelReq req = new ImportExcelReq();
		req.setDistrictId(districtId);
		req.setAutoCreateUnit(autoCreateUnit);
		GenericResponse res = (GenericResponse) importExcelService.importUnitInfo(request,req);
		return res;
	}
	
	@ApiOperation(value = "导入楼栋信息(importBuilding)", httpMethod = "POST", notes = "导入楼栋信息，districtId不能为空；")
	@ResponseBody
	@RequestMapping(value = "/importBuilding",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importBuilding(HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile, Integer districtId) throws Exception {
		ImportExcelReq req = new ImportExcelReq();
		req.setDistrictId(districtId);
		GenericResponse res = (GenericResponse) importExcelService.importBuilding(request,req);
		return res;
	}
	
	@ApiOperation(value = "导入抄表记录数据(importMeterRecord)", httpMethod = "POST", notes = "导入抄表记录数据；")
	@ResponseBody
	@RequestMapping(value = "/importMeterRecord",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importMeterRecord(HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile,Integer replace) throws Exception {
		GenericResponse res = (GenericResponse) importExcelService.importMeterRecord(request, replace);
		return res;
	}
	
	@ApiOperation(value = "导入回盘数据(importBackDisk)", httpMethod = "POST", notes = "导入回盘数据；")
	@ResponseBody
	@RequestMapping(value = "/importBackDisk",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importBackDisk(HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile, Integer templateType,Integer bankDepositBatchId,String depositDate) throws Exception {
		GenericResponse res = (GenericResponse) importExcelService.importBackDisk(request,templateType,bankDepositBatchId,depositDate);
		return res;
	}
	
	@ApiOperation(value = "导入表(importMeter)", httpMethod = "POST", notes = "导入表；")
	@ResponseBody
	@RequestMapping(value = "/importMeter",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importMeter(HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile) throws Exception {
		GenericResponse res = (GenericResponse) importExcelService.importMeter(request);
		return res;
	}
	
	@ApiOperation(value = "导入业主智能卡号(importSmartcardId)", httpMethod = "POST", notes = "导入业主智能卡号；")
	@ResponseBody
	@RequestMapping(value = "/importSmartcardId",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importSmartcardId(HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile, Integer templateType) throws Exception {
		GenericResponse res = (GenericResponse) importExcelService.importSmartcardId(request);
		return res;
	}


	@ApiOperation(value = "导入表编码(importMeterCode)", httpMethod = "POST", notes = "导入表编码；")
	@ResponseBody
	@RequestMapping(value = "/importMeterCode",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importMeterCode(HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile,Integer type) throws Exception {
		ImportExcelReq req = new ImportExcelReq();
		req.setDistrictId(type);
		GenericResponse res = (GenericResponse) importExcelService.importMeterCode(request,req);
		return res;
	}
	
	@ApiOperation(value = "导入车位单元关系(importParkingRelation)", httpMethod = "POST", notes = "导入车位单元关系；")
	@ResponseBody
	@RequestMapping(value = "/importParkingRelation",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importParkingRelation(HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile,Integer type) throws Exception {
		ImportExcelReq req = new ImportExcelReq();
		req.setDistrictId(type);
		GenericResponse res = (GenericResponse) importExcelService.importParkingRelation(request);
		return res;
	}
	
	@ApiOperation(value = "初始化全国平台扣费协议(importInitializeAgreementRelation)", httpMethod = "POST", notes = "初始化全国平台扣费协议；")
	@ResponseBody
	@RequestMapping(value = "/importInitializeAgreementRelation",method = { RequestMethod.POST},produces = MediaType.APPLICATION_JSON_VALUE)
	public GenericResponse importInitializeAgreementRelation(HttpServletRequest request, HttpServletResponse response,
				@ModelAttribute UploadedFile uploadedFile,Integer type) throws Exception {
		ImportExcelReq req = new ImportExcelReq();
		req.setDistrictId(type);
		GenericResponse res = (GenericResponse) importExcelService.importInitializeAgreementRelation(request);
		return res;
	}
}
