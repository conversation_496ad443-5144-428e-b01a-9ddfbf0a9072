package com.foshan.service.community;

import com.foshan.form.community.request.CommunityInstallmentSmsReq;
import com.foshan.form.response.IResponse;

public interface ICommunityInstallmentSmsService {
    public IResponse getCommunityInstallmentSmsList(CommunityInstallmentSmsReq req);
	public IResponse addCommunityInstallmentSms(CommunityInstallmentSmsReq req);
	public IResponse modifyCommunityInstallmentSms(CommunityInstallmentSmsReq req);
	public IResponse deleteCommunityInstallmentSms(CommunityInstallmentSmsReq req);
	public IResponse getCommunityInstallmentSmsInfo(CommunityInstallmentSmsReq req);
	public IResponse setInstallmentSmsStatus(CommunityInstallmentSmsReq req) ;
	public IResponse sendSmsAgain(CommunityInstallmentSmsReq req);
	public IResponse getStewardList(CommunityInstallmentSmsReq req);
}
