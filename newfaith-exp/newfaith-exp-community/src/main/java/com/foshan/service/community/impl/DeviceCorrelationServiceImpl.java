package com.foshan.service.community.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.DeviceCorrelationEntity;
import com.foshan.entity.community.DeviceEntity;
import com.foshan.entity.community.ReceiveMessageTypeEntity;
import com.foshan.form.community.CommunityMemberForm;
import com.foshan.form.community.DeviceCorrelationForm;
import com.foshan.form.community.DeviceForm;
import com.foshan.form.community.ReceiveMessageTypeForm;
import com.foshan.form.community.request.DeviceCorrelationReq;
import com.foshan.form.community.response.deviceCorrelation.AddDeviceCorrelationRes;
import com.foshan.form.community.response.deviceCorrelation.GetDeviceCorrelationInfoRes;
import com.foshan.form.community.response.deviceCorrelation.GetDeviceCorrelationListRes;
import com.foshan.form.community.response.deviceCorrelation.ModifyDeviceCorrelationRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.IDeviceCorrelationService;
import com.foshan.util.DateUtil;

@Transactional
@Service("deviceCorrelationService")
public class DeviceCorrelationServiceImpl extends GenericCommunityService implements IDeviceCorrelationService{

	@Override
	public IResponse getDeviceCorrelationList(DeviceCorrelationReq req) {
		GetDeviceCorrelationListRes res = new GetDeviceCorrelationListRes();
		Page<DeviceCorrelationEntity> page = new Page<DeviceCorrelationEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from DeviceCorrelationEntity a where 1=1 ");
		hql.append("");
		hql.append(" ORDER BY a.id desc");
		page = deviceCorrelationDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			DeviceCorrelationForm deviceCorrelationForm = new DeviceCorrelationForm();
			deviceCorrelationForm.setDeviceCorrelationId(o.getId());
            deviceCorrelationForm.setCreateTime(null != o.getCreateTime()? DateUtil.formatLongFormat(o.getCreateTime()) : "");
            deviceCorrelationForm.setRegionCode(o.getRegionCode());
//            deviceCorrelationForm.setDeviceId(o.getDeviceId());
//            deviceCorrelationForm.setReceiveMessageTypeId(o.getReceiveMessageTypeId());
//            deviceCorrelationForm.setAccountId(o.getAccountId());
            if(null!=o.getDevice()) {
                DeviceForm device = new DeviceForm();
                device.setDeviceId(o.getDevice().getId());
                device.setDeviceAddress(o.getDevice().getDeviceAddress());
                device.setDeviceCode(o.getDevice().getDeviceCode());
                deviceCorrelationForm.setDevice(device);
            }

            if(null!=o.getReceiveMessageType()) {
                ReceiveMessageTypeForm receiveMessageType = new ReceiveMessageTypeForm();
                receiveMessageType.setReceiveMessageTypeId(o.getReceiveMessageType().getId());
                receiveMessageType.setObjectName(o.getReceiveMessageType().getObjectName());
                receiveMessageType.setTypeValue(o.getReceiveMessageType().getTypeValue());
                deviceCorrelationForm.setReceiveMessageType(receiveMessageType);
            }

            if(null!=o.getMember()) {
                CommunityMemberForm account = new CommunityMemberForm();
                account.setMemberId(o.getMember().getId());
                account.setPhone(o.getMember().getPhone());
                account.setUserName(o.getMember().getUserName());
                deviceCorrelationForm.setMember(account);
            }
			res.getDeviceCorrelationList().add(deviceCorrelationForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addDeviceCorrelation(DeviceCorrelationReq req) {
		AddDeviceCorrelationRes res = new AddDeviceCorrelationRes();
		if (null!=req.getAccountId() && null!=req.getDeviceId() && null!=req.getReceiveMessageTypeId()) {
			CommunityMemberEntity account = communityMemberDao.get(req.getAccountId());
			DeviceEntity device = deviceDao.get(req.getDeviceId());
			ReceiveMessageTypeEntity receiveMessageType = receiveMessageTypeDao.get(req.getReceiveMessageTypeId());
			if(null==account || null==device || null==receiveMessageType) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			if(receiveMessageType.getTypeValue()==0 && StringUtils.isEmpty(req.getRegionCode())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo("区域码不能为空！");
				return res;
			}
			
			DeviceCorrelationEntity deviceCorrelation = new DeviceCorrelationEntity();
			
			deviceCorrelation.setRegionCode(req.getRegionCode());
            deviceCorrelation.setDevice(device);
            deviceCorrelation.setReceiveMessageType(receiveMessageType);
            deviceCorrelation.setMember(account);
			deviceCorrelationDao.save(deviceCorrelation);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse modifyDeviceCorrelation(DeviceCorrelationReq req) {
		ModifyDeviceCorrelationRes res = new ModifyDeviceCorrelationRes();
		if (null!=req.getDeviceCorrelationId() ) {
			DeviceCorrelationEntity deviceCorrelation = deviceCorrelationDao.get(req.getDeviceCorrelationId()) ;
			if(null != deviceCorrelation){
//                deviceCorrelation.setDeviceId(req.getDeviceId());
//                deviceCorrelation.setReceiveMessageTypeId(req.getReceiveMessageTypeId());
//                deviceCorrelation.setAccountId(req.getAccountId());
				res.setDeviceCorrelationId(deviceCorrelation.getId());
				deviceCorrelation.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : 
					deviceCorrelation.getRegionCode());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteDeviceCorrelation(DeviceCorrelationReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getDeviceCorrelationId()) {
		DeviceCorrelationEntity deviceCorrelation = deviceCorrelationDao.get(req.getDeviceCorrelationId());
			if (null != deviceCorrelation) {
				deviceCorrelation.setMember(null);
				deviceCorrelation.setDevice(null);
				deviceCorrelation.setReceiveMessageType(null);
				deviceCorrelationDao.delete(deviceCorrelation);
				//deviceCorrelation.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getDeviceCorrelationInfo(DeviceCorrelationReq req) {
		GetDeviceCorrelationInfoRes res = new GetDeviceCorrelationInfoRes();
		if (null != req.getDeviceCorrelationId()) {
			DeviceCorrelationEntity deviceCorrelation = deviceCorrelationDao.get(req.getDeviceCorrelationId());
			if (null != deviceCorrelation) {
				DeviceCorrelationForm deviceCorrelationForm = new DeviceCorrelationForm();
				deviceCorrelationForm.setDeviceCorrelationId(deviceCorrelation.getId());
                deviceCorrelationForm.setCreateTime(null != deviceCorrelation.getCreateTime() ? DateUtil.formatLongFormat(deviceCorrelation.getCreateTime()) : "");
                deviceCorrelationForm.setRegionCode(deviceCorrelation.getRegionCode());
//                deviceCorrelationForm.setDeviceId(deviceCorrelation.getDeviceId());
//                deviceCorrelationForm.setReceiveMessageTypeId(deviceCorrelation.getReceiveMessageTypeId());
//                deviceCorrelationForm.setAccountId(deviceCorrelation.getAccountId());
                if(null!=deviceCorrelation.getDevice()) {
                    DeviceForm device = new DeviceForm();
                    device.setDeviceId(deviceCorrelation.getDevice().getId());
                    device.setDeviceAddress(deviceCorrelation.getDevice().getDeviceAddress());
                    device.setDeviceCode(deviceCorrelation.getDevice().getDeviceCode());
                    deviceCorrelationForm.setDevice(device);
                }

                if(null!=deviceCorrelation.getReceiveMessageType()) {
                    ReceiveMessageTypeForm receiveMessageType = new ReceiveMessageTypeForm();
                    receiveMessageType.setReceiveMessageTypeId(deviceCorrelation.getReceiveMessageType().getId());
                    receiveMessageType.setObjectName(deviceCorrelation.getReceiveMessageType().getObjectName());
                    receiveMessageType.setTypeValue(deviceCorrelation.getReceiveMessageType().getTypeValue());
                    deviceCorrelationForm.setReceiveMessageType(receiveMessageType);
                }

                if(null!=deviceCorrelation.getMember()) {
                    CommunityMemberForm account = new CommunityMemberForm();
                    account.setMemberId(deviceCorrelation.getMember().getId());
                    account.setPhone(deviceCorrelation.getMember().getPhone());
                    account.setUserName(deviceCorrelation.getMember().getUserName());
                    deviceCorrelationForm.setMember(account);
                }

                
				res.setDeviceCorrelation(deviceCorrelationForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}