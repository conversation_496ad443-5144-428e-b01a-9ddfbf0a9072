package com.foshan.service.community.impl;

import static java.util.stream.Collectors.groupingBy;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPatriarch;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFSimpleShape;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jdbc.Work;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.entity.UserEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityMeterAllocationEntity;
import com.foshan.entity.community.CommunityMeterAllocationItemEntity;
import com.foshan.entity.community.CommunityMeterAttributesEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityMeterRecordEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityReceiptEntity;
import com.foshan.entity.community.CommunityReceiptReceivablesEntity;
import com.foshan.entity.community.CommunityReceivablesChangesEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.community.context.CommunityContext;
import com.foshan.entity.community.vo.AllocationItemVo;
import com.foshan.entity.community.vo.MeterRecordVo;
import com.foshan.entity.community.vo.ReservedFieldVo;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.request.CommunityMeterAttributesReq;
import com.foshan.form.community.request.CommunityMeterRecordReq;
import com.foshan.form.community.request.CommunityReceiptReq;
import com.foshan.form.community.request.ExportExcelReq;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.community.response.exportExcel.GetReceiptViewRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.IExportExcelService;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityCache;
import com.foshan.util.community.CommunityPage;
import com.foshan.util.community.ExcelToHtml;

@Transactional
@Service("exportExcelService")
public class ExportExcelServiceImpl extends GenericCommunityService implements IExportExcelService {
	public static List<Map<String,String>> keyList = new ArrayList<>();
	//public static List<Map<String, String>> viewList = new LinkedList<>();
	public static  Set<Map<String, String>>  viewList = new LinkedHashSet<>();

	public Date lastDayOfMonth(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.roll(Calendar.DAY_OF_MONTH, -1);
		cal.set(Calendar.HOUR_OF_DAY, 23);
		cal.set(Calendar.MINUTE, 59);
		cal.set(Calendar.SECOND, 59);
		return cal.getTime();
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public byte[] exportMonthData(ExportExcelReq req, HttpServletResponse response) {
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		ZipOutputStream zip = new ZipOutputStream(outputStream);

		if (!StringUtils.isNotEmpty(req.getMonth())) {
			// res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			// res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO);
			return null;
		}

		for (int j = 0; j < 4; j++) {
			String fileName = "";
			StringBuilder hql = new StringBuilder();
			List<Map<String, Object>> maps = new ArrayList<Map<String, Object>>();
//            String[] parameter = req.getExcelExportParameter().split(",");
//            for (int i = 0; i < parameter.length; i++) {
//                propertiesList.add(parameter[i]);
//                if (parameter[i].equals("productName")) titleList.add("商品名称");
//            }
			Map<String, String> heardMap = new LinkedHashMap<String, String>();
			heardMap.put("districtName", "楼盘名称");
			heardMap.put("unitCode", "单元编号");
			heardMap.put("buildingName", "楼阁");
			if (j == 0 || j == 1 || j == 3) {
				BigDecimal villaManagementFeeTotal = new BigDecimal(0);
				BigDecimal houseManagementFeeTotal = new BigDecimal(0);
				BigDecimal gardenAndParkingManagementFeesTotal = new BigDecimal(0);
				BigDecimal gardenManagementFeesTotal = new BigDecimal(0);
				BigDecimal parkingManagementFeesTotal = new BigDecimal(0);
				heardMap.put("receivableDate", "应收日期");
				heardMap.put("year", "收款年");
				heardMap.put("month", "收款月");
				heardMap.put("receiptDate", "收款日期");
				heardMap.put("paymentMethod", "收款方式");
				hql.append("select a from CommunityReceiptEntity a inner join a.receiptReceivablesList b"
						+ " inner join b.receivables c  where c.chargeSource IS NOT NULL AND c.chargeCategory='管理费'  ");
				if (j == 0) {
					fileName = "沉积";
					hql.append(" AND a.receiptDate LIKE '" + req.getMonth() + "%' " + "AND c.receivableDate<'"
							+ req.getMonth() + "-01 00:00:00' group by a ");
				} else if (j == 1) {
					fileName = "实收款";
					hql.append("AND c.receivableDate like'%" + req.getMonth() + "%' group by a");
				} else {
					fileName = "预收款";
					try {
						hql.append(" AND a.receiptDate LIKE '" + req.getMonth() + "%' " + "AND c.receivableDate>'"
								+ DateUtil.formatLongFormat(
										lastDayOfMonth(DateUtil.parseShortFormat(req.getMonth() + "-01")))
								+ "' group by a ");
					} catch (ParseException e) {
						e.printStackTrace();
					}
				}
				List<CommunityReceiptEntity> list = communityReceiptDao.getListByHql(hql.toString());
				for (CommunityReceiptEntity o : list) {
					Map<String, Map> columnMap = new HashMap<String, Map>();
					for (CommunityReceiptReceivablesEntity r : o.getReceiptReceivablesList()) {
						try {
							String payItemsName = r.getReceivables().getPayItemsName();
							String receivableDate = DateUtil.formatLongFormat(r.getReceivables().getReceivableDate());
							if (((j == 3 && lastDayOfMonth(DateUtil.parseShortFormat(req.getMonth() + "-01' "))
									.before(r.getReceivables().getReceivableDate()))
									|| (j == 1 && receivableDate.startsWith(req.getMonth())
											&& lastDayOfMonth(DateUtil.parseShortFormat(req.getMonth() + "-01' "))
													.after(o.getReceiptDate()))// 过滤之后交的数据
									|| (DateUtil.parseShortFormat(req.getMonth() + "-01 00:00:00' ")
											.after(r.getReceivables().getReceivableDate())) && j == 0)
									&& (payItemsName.equals("别墅管理费") || payItemsName.equals("洋房管理费")
											|| payItemsName.equals("花园及停车位管理费") || payItemsName.equals("花园管理费")
											|| payItemsName.equals("车位管理费"))
									&& r.getReceivables().getChargeCategory().equals("管理费")) {
								BigDecimal villaManagementFee = new BigDecimal(0);
								BigDecimal houseManagementFee = new BigDecimal(0);
								BigDecimal gardenAndParkingManagementFees = new BigDecimal(0);
								BigDecimal gardenManagementFees = new BigDecimal(0);
								BigDecimal parkingManagementFees = new BigDecimal(0);
								if (payItemsName.equals("别墅管理费")) {
									villaManagementFeeTotal = villaManagementFeeTotal.add(r.getCurrentAmount());
									villaManagementFee = r.getCurrentAmount();
								} else if (payItemsName.equals("洋房管理费")) {
									houseManagementFeeTotal = houseManagementFeeTotal.add(r.getCurrentAmount());
									houseManagementFee = r.getCurrentAmount();
								} else if (payItemsName.equals("花园及停车位管理费")) {
									gardenAndParkingManagementFeesTotal = gardenAndParkingManagementFeesTotal
											.add(r.getCurrentAmount());
									gardenAndParkingManagementFees = r.getCurrentAmount();
								} else if (payItemsName.equals("花园管理费")) {
									gardenManagementFeesTotal = gardenManagementFeesTotal.add(r.getCurrentAmount());
									gardenManagementFees = r.getReceivedAmount();
								} else if (payItemsName.equals("车位管理费")) {
									parkingManagementFeesTotal = parkingManagementFeesTotal.add(r.getCurrentAmount());
									parkingManagementFees = r.getCurrentAmount();
								}

								Map<String, Object> map = null;
								if (columnMap.containsKey(receivableDate)) {
									map = columnMap.get(receivableDate);
								} else {
									map = new HashMap<String, Object>();
									if (o.getEstate() != null) {
										map.put("unitCode", o.getEstate().getUnitCode());
										if (o.getEstate().getBuilding() != null) {
											map.put("districtName",
													o.getEstate().getBuilding().getDistrict().getDistrictName());
											map.put("buildingName", o.getEstate().getBuilding().getBuildingName());
										}
									}
									map.put("receivableDate", receivableDate);
									map.put("year", req.getMonth().split("-")[0]);
									map.put("month", req.getMonth().split("-")[1]);
									map.put("receiptDate", DateUtil.formatLongFormat(o.getReceiptDate()));
									map.put("paymentMethod", o.getPaymentMethod());
								}
								villaManagementFee = map.containsKey("villaManagementFee")
										? new BigDecimal((String) map.get("villaManagementFee")).add(villaManagementFee)
										: villaManagementFee;
								houseManagementFee = map.containsKey("houseManagementFee")
										? new BigDecimal((String) map.get("houseManagementFee")).add(houseManagementFee)
										: houseManagementFee;
								gardenAndParkingManagementFees = map.containsKey("gardenAndParkingManagementFees")
										? new BigDecimal((String) map.get("gardenAndParkingManagementFees"))
												.add(gardenAndParkingManagementFees)
										: gardenAndParkingManagementFees;
								gardenManagementFees = map.containsKey("gardenManagementFees")
										? new BigDecimal((String) map.get("gardenManagementFees")).add(
												gardenManagementFees)
										: gardenManagementFees;
								parkingManagementFees = map.containsKey("parkingManagementFees")
										? new BigDecimal((String) map.get("parkingManagementFees")).add(
												parkingManagementFees)
										: parkingManagementFees;

								map.put("villaManagementFee", villaManagementFee.toString());
								map.put("houseManagementFee", houseManagementFee.toString());
								map.put("gardenAndParkingManagementFees", gardenAndParkingManagementFees.toString());
								map.put("gardenManagementFees", gardenManagementFees.toString());
								map.put("parkingManagementFees", parkingManagementFees.toString());
								map.put("total",
										villaManagementFee.add(houseManagementFee).add(gardenAndParkingManagementFees)
												.add(gardenManagementFees).add(parkingManagementFees).toString());
								columnMap.put(receivableDate, map);
							}
						} catch (ParseException e) {
							e.printStackTrace();
						}
					}
					for (String key : columnMap.keySet()) {
						maps.add(columnMap.get(key));
					}
				}
			} else {
				fileName = "应收款";
				SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
				Session session = sessionFactory.openSession();
				// Transaction tx = session.beginTransaction();
				session.doWork(new Work() {

					@Override
					public void execute(Connection connection) throws SQLException {
						// TODO Auto-generated method stub
						Statement st = (Statement) connection.createStatement();
						StringBuilder sql = new StringBuilder();
						sql.append(
								"SELECT DISTINCT  a.estateId,f.districtName,d.unitCode,e.buildingName,a.receivableDate,"
										+ "(SELECT SUM(receivableAmount) FROM t_community_receivables b WHERE a.estateId=b.estateId AND b.payItemsName='别墅管理费' AND b.chargeSource IS NOT NULL AND b.chargeCategory='管理费' AND b.receivableDate LIKE '%"
										+ req.getMonth() + "%') AS 'villaManagementFee',  "
										+ "(SELECT SUM(receivableAmount) FROM t_community_receivables b WHERE a.estateId=b.estateId AND b.payItemsName='洋房管理费' AND b.chargeSource IS NOT NULL AND b.chargeCategory='管理费' AND b.receivableDate LIKE '%"
										+ req.getMonth() + "%') AS 'houseManagementFee' , "
										+ "(SELECT SUM(receivableAmount) FROM t_community_receivables b WHERE a.estateId=b.estateId AND b.payItemsName='花园及停车位管理费' AND b.chargeSource IS NOT NULL AND b.chargeCategory='管理费' AND b.receivableDate LIKE '%"
										+ req.getMonth() + "%') AS 'gardenAndParkingManagementFees',  "
										+ "(SELECT SUM(receivableAmount) FROM t_community_receivables b WHERE a.estateId=b.estateId AND b.payItemsName='花园管理费' AND b.chargeSource IS NOT NULL AND b.chargeCategory='管理费' AND b.receivableDate LIKE '%"
										+ req.getMonth() + "%') AS 'gardenManagementFees',  "
										+ "(SELECT SUM(receivableAmount) FROM t_community_receivables b WHERE a.estateId=b.estateId AND b.payItemsName='车位管理费' AND b.chargeSource IS NOT NULL AND b.chargeCategory='管理费' AND b.receivableDate LIKE '%"
										+ req.getMonth() + "%') AS 'parkingManagementFees' "
										+ "FROM t_community_receivables a INNER JOIN `t_community_property` d ON d.id=a.estateId INNER JOIN `t_community_building` e ON e.id=d.buildingId "
										+ "INNER JOIN `t_community_district` f ON f.id=e.districtId  WHERE a.chargeSource IS NOT NULL AND a.chargeCategory='管理费' AND a.receivableDate LIKE '%"
										+ req.getMonth() + "%' GROUP BY a.estateId");

						BigDecimal villaManagementFeeTotal = new BigDecimal(0);
						BigDecimal houseManagementFeeTotal = new BigDecimal(0);
						BigDecimal gardenAndParkingManagementFeesTotal = new BigDecimal(0);
						BigDecimal gardenManagementFeesTotal = new BigDecimal(0);
						BigDecimal parkingManagementFeesTotal = new BigDecimal(0);

						ResultSet rs = st.executeQuery(sql.toString());
						while (rs.next()) {

							Map<String, Object> map = new HashMap<String, Object>();
							BigDecimal villaManagementFee = null != rs.getBigDecimal("villaManagementFee")
									? rs.getBigDecimal("villaManagementFee")
									: new BigDecimal(0);
							BigDecimal houseManagementFee = null != rs.getBigDecimal("houseManagementFee")
									? rs.getBigDecimal("houseManagementFee")
									: new BigDecimal(0);
							BigDecimal gardenAndParkingManagementFees = null != rs
									.getBigDecimal("gardenAndParkingManagementFees")
											? rs.getBigDecimal("gardenAndParkingManagementFees")
											: new BigDecimal(0);
							BigDecimal gardenManagementFees = null != rs.getBigDecimal("gardenManagementFees")
									? rs.getBigDecimal("gardenManagementFees")
									: new BigDecimal(0);
							BigDecimal parkingManagementFees = null != rs.getBigDecimal("parkingManagementFees")
									? rs.getBigDecimal("parkingManagementFees")
									: new BigDecimal(0);
							map.put("districtName", rs.getString("districtName"));
							map.put("unitCode", rs.getString("unitCode"));
							map.put("buildingName", rs.getString("buildingName"));
							map.put("receivableDate", DateUtil.formatLongFormat(rs.getDate("receivableDate")));
							map.put("year", req.getMonth().split("-")[0]);
							map.put("month", req.getMonth().split("-")[1]);
							map.put("villaManagementFee", villaManagementFee.toString());
							map.put("houseManagementFee", houseManagementFee.toString());
							map.put("gardenAndParkingManagementFees", gardenAndParkingManagementFees.toString());
							map.put("gardenManagementFees", gardenManagementFees.toString());
							map.put("parkingManagementFees", parkingManagementFees.toString());
							map.put("total",
									villaManagementFee.add(houseManagementFee).add(gardenAndParkingManagementFees)
											.add(gardenManagementFees).add(parkingManagementFees).toString());
							maps.add(map);

							villaManagementFeeTotal = villaManagementFeeTotal.add(villaManagementFee);
							houseManagementFeeTotal = houseManagementFeeTotal.add(houseManagementFee);
							gardenAndParkingManagementFeesTotal = gardenAndParkingManagementFeesTotal
									.add(gardenAndParkingManagementFees);
							gardenManagementFeesTotal = gardenManagementFeesTotal.add(gardenManagementFees);
							parkingManagementFeesTotal = parkingManagementFeesTotal.add(parkingManagementFees);
						}

						connection.close();
						Map<String, Object> map = new HashMap<String, Object>();
						map.put("districtName", "合计");
						map.put("villaManagementFee", villaManagementFeeTotal.toString());
						map.put("houseManagementFee", houseManagementFeeTotal.toString());
						map.put("gardenAndParkingManagementFees", gardenAndParkingManagementFeesTotal.toString());
						map.put("gardenManagementFees", gardenManagementFeesTotal.toString());
						map.put("parkingManagementFees", parkingManagementFeesTotal.toString());
						map.put("total",
								villaManagementFeeTotal.add(houseManagementFeeTotal)
										.add(gardenAndParkingManagementFeesTotal).add(gardenManagementFeesTotal)
										.add(parkingManagementFeesTotal).toString());
						maps.add(map);
						rs.close();
						st.close();
					}
				});
//    				tx.commit();
				if (session != null) {
					session.close();
				}

				heardMap.put("userName", "姓名");
				heardMap.put("bankAccount", "银行帐户");
				heardMap.put("bankName", "划帐银行");
			}
			heardMap.put("villaManagementFee", "别墅管理费");
			heardMap.put("houseManagementFee", "洋房管理费");
			heardMap.put("gardenAndParkingManagementFees", "花园及停车位管理费");
			heardMap.put("gardenManagementFees", "花园管理费");
			heardMap.put("parkingManagementFees", "车位管理费");
			heardMap.put("total", "总共");
			String sheetTitle = "Sheet1";
			HSSFWorkbook wb = null;
			try {
				wb = ExcelExportUtil.createSheet(null, heardMap, maps, Short.valueOf("9"), Short.valueOf("500"), null,
						sheetTitle);
			} catch (IOException e) {
				// e.printStackTrace();
			}

			ByteArrayOutputStream os = new ByteArrayOutputStream();

			try {
				wb.write(os);
			} catch (IOException e) {
				e.printStackTrace();

			}

			byte[] content = os.toByteArray();
			// 写入输入流
			InputStream is = new ByteArrayInputStream(content);

			/*
			 * // 创建临时文件
			 * 
			 * File zipFile = File.createTempFile(fileName, ".zip");
			 * 
			 * FileOutputStream f = new FileOutputStream(zipFile);
			 * 
			 * CheckedOutputStream csum = new CheckedOutputStream(f, new Adler32());
			 * 
			 * // 用于将数据压缩成Zip文件格式
			 * 
			 * ZipOutputStream zos = new ZipOutputStream(csum);
			 */

			/**
			 * 
			 * 添加excel表格数据
			 * 
			 */

			try {
				zip.putNextEntry(new ZipEntry(fileName + ".xls"));
			} catch (IOException e) {
				e.printStackTrace();
			}
			int bytesRead = 0;
			try {
				while ((bytesRead = is.read()) != -1) {
					try {
						zip.write(bytesRead);
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
				zip.closeEntry();
				os.close();
				is.close();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

		}
		// IOUtils.closeQuietly(zip);
		// zip.close();
		return outputStream.toByteArray();
	}

	public IResponse exportMonthArrearage(ExportExcelReq req, HttpServletResponse response) throws ParseException {
		ExportExcelRes res = new ExportExcelRes();
		if (StringUtils.isNotEmpty(req.getMonth())) {
			StringBuilder hql = new StringBuilder("select distinct a from CommunityDistrictEntity a where a.state=1  ");
			List<CommunityDistrictEntity> districtList = communityDistrictDao.getListByHql(hql.toString(), "");

			Map<String, String> heardMap = new LinkedHashMap<String, String>();
			heardMap.put("region", "");
			heardMap.put("total", "总额");
			heardMap.put("liquidatedDamages", "违约金金额");
			heardMap.put("principal", "欠本金");
			List<Map<String, Object>> maps = new ArrayList<Map<String, Object>>();
			for (int i = 0; i < districtList.size(); i++) {
				CommunityDistrictEntity district = districtList.get(i);
				Map<String, Object> map = new HashMap<String, Object>();
				if (i == 0) {
					map.put("region", "合计");
					map.put("total", "_cellFormula_SUM(C3:C" + districtList.size() + 3 + ")");
					map.put("liquidatedDamages", "_cellFormula_SUM(D3:D" + districtList.size() + 3 + ")");
					map.put("principal", "_cellFormula_C2-D2");
					maps.add(map);
				} else {
					map.put("region", district.getDistrictName());
					map.put("total", "_cellFormula_" + district.getDistrictName() + "!E2");
					map.put("liquidatedDamages", "_cellFormula_" + district.getDistrictName() + "!F2");
					map.put("principal", "_cellFormula_C" + (i + 3) + "-D" + (i + 3));
					maps.add(map);
				}
			}
			String sheetTitle = "汇总";
			HSSFWorkbook wb = null;
			try {
				wb = ExcelExportUtil.createSheet(null, heardMap, maps, Short.valueOf("9"), Short.valueOf("500"), null,
						sheetTitle);
			} catch (IOException e) {

			}

			for (int i = 0; i < districtList.size(); i++) {
				CommunityDistrictEntity district = districtList.get(i);
				int fontSize = 9;
				// 创建工作表
				HSSFSheet wbSheet = wb.createSheet(district.getDistrictName());
				// 设置默认行宽
				// wbSheet.setDefaultColumnWidth(30);
				wbSheet.setColumnWidth(0, 1500);
				wbSheet.setColumnWidth(1, 5000);
				wbSheet.setColumnWidth(2, 5000);
				wbSheet.setColumnWidth(3, 5000);
				wbSheet.setColumnWidth(4, 5000);
				wbSheet.setColumnWidth(5, 5000);
				wbSheet.setColumnWidth(6, 18000);
				wbSheet.setColumnWidth(7, 15000);
				wbSheet.setColumnWidth(8, 5000);
				wbSheet.setColumnWidth(9, 5000);
				wbSheet.setColumnWidth(10, 4000);

				wbSheet.setDefaultRowHeight((short) 410);
				// 标题样式（加粗，垂直居中）
				HSSFCellStyle cellStyle = wb.createCellStyle();
				cellStyle.setAlignment(HorizontalAlignment.CENTER);
				HSSFFont fontStyle = wb.createFont();
				fontStyle.setFontName("宋体");
				fontStyle.setBold(true); // 加粗
				fontStyle.setFontHeightInPoints((short) 14); // 设置标题字体大小
				cellStyle.setFont(fontStyle);

				HSSFCellStyle style = wb.createCellStyle();
				// 设置字体
				HSSFFont font = wb.createFont();
				font.setFontHeightInPoints((short) fontSize);
				font.setFontName("宋体");
				// font.setBold(true); //加粗
				style.setFont(font);

				// 设置列头元素
				HSSFCell cellHead = null;
				hql = new StringBuilder("SELECT distinct a from CommunityReceivablesEntity a inner join "
						+ "a.estate b inner join b.building c inner join c.district d " + "where  d.id="
						+ district.getId() + " AND a.receivableDate<'"
						+ DateUtil.formatLongFormat(lastDayOfMonth(DateUtil.parseShortFormat(req.getMonth() + "-01")))
						+ "' AND a.receivableAmount>a.receivedAmount "
						+ "and  (a.payItemsName NOT LIKE '%代收%' OR a.payItemsName NOT LIKE  '%服务费%') ");
				List<CommunityReceivablesEntity> list = communityReceivablesDao.getListByHql(hql.toString(), "");
				Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
				if (null != list) {
					for (int j = 0; j < list.size(); j++) {
						CommunityReceivablesEntity receivables = list.get(j);
						Map<String, Object> itemMap = null;
						String estateId = receivables.getEstate().getId().toString();
						BigDecimal receivableAmount = new BigDecimal(0);
						BigDecimal penalSum = new BigDecimal(0);
						if (map.containsKey(estateId)) {
							itemMap = map.get(estateId);
							receivableAmount = (BigDecimal) itemMap.get("receivableAmount");
							penalSum = (BigDecimal) itemMap.get("penalSum");
						} else {
							itemMap = new HashMap<String, Object>();
							for (CommunityMemberPropertyEntity memberProperty : receivables.getEstate()
									.getMemberPropertyList()) {
								if (memberProperty.getIsCurrentOwner() == CommunityContext.IS_CURRENT_OWNER
										&& null != memberProperty.getMember()) {
									String name = memberProperty.getMember().getUserName();
									itemMap.put("name", name);
								}
							}
							itemMap.put("unitCode", receivables.getEstate().getUnitCode());
							itemMap.put("payItemsName", "欠");
						}
						receivableAmount = receivableAmount
								.add(receivables.getReceivableAmount().subtract(receivables.getReceivedAmount()));
						if (!((String) itemMap.get("payItemsName")).contains(receivables.getPayItemsName())) {
							itemMap.put("payItemsName",
									itemMap.get("payItemsName") + receivables.getPayItemsName() + ",");
						}
						if (receivables.getPayItemsName().contains("违约金")
								|| receivables.getPayItemsName().contains("滞纳金")) {
							penalSum = penalSum
									.add((receivables.getReceivableAmount().subtract(receivables.getReceivedAmount())));
						}
						itemMap.put("receivableAmount", receivableAmount);
						itemMap.put("penalSum", penalSum);
						if (itemMap.containsKey("startTime")) {
							if (receivables.getReceivableDate().before((Date) itemMap.get("startTime"))) {
								itemMap.put("startTime", receivables.getReceivableDate());
							}
						} else {
							itemMap.put("startTime", receivables.getReceivableDate());
						}
						if (itemMap.containsKey("endTime")) {
							if (receivables.getReceivableDate().after((Date) itemMap.get("endTime"))) {
								itemMap.put("endTime", receivables.getReceivableDate());
							}
						} else {
							itemMap.put("endTime", receivables.getReceivableDate());
						}
						map.put(estateId, itemMap);
					}
				}

				for (int j = 0; j < 3; j++) {// 第一行标题
					if (j == 0) {
						// 在第1行创建rows
						HSSFRow row = wbSheet.createRow((int) 0);
						row.setHeight((short) 500);
						cellHead = row.createCell(0);
						cellHead.setCellStyle(getStyle(wb, (short) 14, true, HorizontalAlignment.CENTER_SELECTION));
						cellHead.setCellValue("应收未收报表（" + district.getDistrictName() + "）");
						// 起始行，结束行，起始列，结束列
						CellRangeAddress region = new CellRangeAddress(0, 0, 0, 10);
						wbSheet.addMergedRegion(region);
					} else if (j == 1) {
						HSSFRow row = wbSheet.createRow((int) 1);
						row.setHeight((short) 420);
						// cellHead = row.createCell(0);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.LEFT));
						// ellHead.setCellValue("制表日期：" + DateUtil.format(new Date(), 1));
						cellHead = row.createCell(0);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.LEFT));
						// cellHead.setCellType(CellType.FORMULA);
						cellHead.setCellFormula("COUNTA(B4:B" + (3 + map.size()) + ")");

						cellHead = row.createCell(4);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.LEFT));
						// cellHead.setCellType(CellType.FORMULA);
						cellHead.setCellFormula("SUM(E4:E" + (3 + map.size()) + ")");

						cellHead = row.createCell(5);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.LEFT));
						cellHead.setCellFormula("SUM(F4:F" + (3 + map.size()) + ")");
					} else if (j == 2) {
						HSSFRow row = wbSheet.createRow((int) 2);
						row.setHeight((short) 420);
						cellHead = row.createCell(0);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("序号");
						cellHead = row.createCell(1);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("单元编号");
						cellHead = row.createCell(2);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("姓名");
						cellHead = row.createCell(3);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("时段");
						cellHead = row.createCell(4);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("合计");
						cellHead = row.createCell(5);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("欠违约金金额");
						cellHead = row.createCell(6);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("原因");
						cellHead = row.createCell(7);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("措施");
						cellHead = row.createCell(8);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("进展");
						cellHead = row.createCell(9);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("继续电话催费");
						cellHead = row.createCell(10);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("减免滞纳金");
					}
				}

				int j = 0;
				if (null != list) {
					for (String key : map.keySet()) {

//                	for(int j=0;j<list.size();j++) {
//                		CommunityReceivablesEntity receivables = list.get(j);
//                        String itemSql ="SELECT distinct a from CommunityReceivablesEntity a inner join "
//                        	+ "a.estate b inner join b.building c inner join c.district d "
//                			+ "where  d.id="+district.getId()+" AND a.receivableDate<'"+DateUtil.formatLongFormat(lastDayOfMonth(
//        					DateUtil.parseShortFormat(req.getMonth()+"-01")))+"' AND a.receivableAmount>a.receivedAmount and b.id="
//        					+receivables.getEstate().getId()+
//        					" and  (a.payItemsName NOT LIKE '%代收%' OR a.payItemsName NOT LIKE  '%服务费%')  ORDER BY a.receivableDate ASC";
//                        List<CommunityReceivablesEntity> itemList  =communityReceivablesDao.getListByHql(itemSql, "");
//                        String name="";
//                        if(null!=receivables.getEstate() && null!=receivables.getEstate().getMemberPropertyList()) {
//                        	for(CommunityMemberPropertyEntity memberProperty : receivables.getEstate().getMemberPropertyList()) {
//                        		if(memberProperty.getIsCurrentOwner() == CommunityContext.IS_CURRENT_OWNER && null!=memberProperty.getMember()) {
//                        			name = memberProperty.getMember().getUserName();
//                        		}
//                        	}
//                        }
//                        BigDecimal receivableAmount = new BigDecimal(0);
//                        BigDecimal penalSum = new BigDecimal(0);
//                        Map<String,String> map = new HashMap<String,String>();
//                        StringBuilder time= new StringBuilder();
//                        for(int r=0; r<itemList.size();r++) {
//                        	CommunityReceivablesEntity rb = itemList.get(r);
//                        	if(r==0) {
//                        		time.append(DateUtil.formatShortFormat(rb.getReceivableDate()).substring(0, 7));
//                        		if(itemList.size()>1) {
//                        			time.append("～");
//                        		}
//                        	}else if(r == itemList.size()-1 && itemList.size()>1) {
//                        		if(!time.toString().contains(DateUtil.formatShortFormat(rb.getReceivableDate()).substring(0, 7))) {
//                        			time.append(DateUtil.formatShortFormat(rb.getReceivableDate()).substring(0, 7));
//                        		}else {
//                        			time.replace(time.length()-1, time.length(), "");
//                        		}
//                        		
//                        	}
//                        	receivableAmount = receivableAmount.add(rb.getReceivableAmount().subtract(rb.getReceivedAmount()));
//                        	map.put(rb.getPayItemsName(), rb.getPayItemsName());
//                        	if(rb.getPayItemsName().contains("违约金") || rb.getPayItemsName().contains("滞纳金")) {
//                        		penalSum = penalSum.add((rb.getReceivableAmount().subtract(rb.getReceivedAmount())));
//                        	}
//                        }
//                        StringBuilder item = new StringBuilder("欠");
//                    	for (String key : map.keySet()) {
//                    		item.append(map.get(key)+"、");
//                    	}
						Map<String, Object> itemMap = map.get(key);
						HSSFRow row = wbSheet.createRow((int) j + 3);
						row.setHeight((short) 420);
						cellHead = row.createCell(0);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue(j + 1);
						cellHead = row.createCell(1);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue((String) itemMap.get("unitCode"));
						cellHead = row.createCell(2);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue((String) itemMap.get("name"));
						cellHead = row.createCell(3);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						if (DateUtil.formatShortFormat((Date) itemMap.get("startTime")).substring(0, 7)
								.equals(DateUtil.formatShortFormat((Date) itemMap.get("endTime")).substring(0, 7))) {
							cellHead.setCellValue(
									DateUtil.formatShortFormat((Date) itemMap.get("endTime")).substring(0, 7));
						} else {
							cellHead.setCellValue(DateUtil.formatShortFormat((Date) itemMap.get("endTime")).substring(0,
									7) + "～"
									+ DateUtil.formatShortFormat((Date) itemMap.get("startTime")).substring(0, 7));
						}
						// cellHead.setCellValue(time.toString());
						cellHead = row.createCell(4);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue(((BigDecimal) itemMap.get("receivableAmount")).doubleValue());
						cellHead = row.createCell(5);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue(((BigDecimal) itemMap.get("penalSum")).doubleValue());
						cellHead = row.createCell(6);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue(((String) itemMap.get("payItemsName")).toString().substring(0,
								((String) itemMap.get("payItemsName")).length() - 1));
						j++;
					}
				}

			}

			if (req.getPreview() == 1) {
				res.setPreviewData(ExcelToHtml.readWorkbook(wb, true));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				try {
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					ExcelExportUtil.export(response, wb, "");
				} catch (IOException e) {
					e.printStackTrace();
				}
			}

		} else {
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO);
			return res;
		}
		return res;
	}

	public HSSFCellStyle getStyle(HSSFWorkbook wb, short fontSize, boolean isBold, HorizontalAlignment align) {
		HSSFCellStyle style = wb.createCellStyle();
		// 设置字体
		HSSFFont font = wb.createFont();
		style.setAlignment(align);
		font.setFontHeightInPoints((short) fontSize);
		font.setFontName("宋体");
		font.setBold(isBold); // 加粗
		style.setFont(font);
//		HSSFCellStyle setBorder = wb.createCellStyle();
//        setBorder.setBorderBottom(HSSFCellStyle.BORDER_THIN); //下边框
//        setBorder.setBorderLeft(HSSFCellStyle.BORDER_THIN);//左边框
//        setBorder.setBorderTop(HSSFCellStyle.BORDER_THIN);//上边框
//        setBorder.setBorderRight(HSSFCellStyle.BORDER_THIN);//右边框

		return style;
	}

	// 未售报表
	public IResponse exportMonthUnsoldArrearage(ExportExcelReq req, HttpServletResponse response)
			throws ParseException {
		ExportExcelRes res = new ExportExcelRes();
		if (StringUtils.isNotEmpty(req.getMonth())) {
			StringBuilder hql = new StringBuilder("select distinct a from CommunityDistrictEntity a where a.state=1  ");
			List<CommunityDistrictEntity> districtList = communityDistrictDao.getListByHql(hql.toString(), "");
			HSSFWorkbook wb = new HSSFWorkbook();
			HSSFSheet wbSheet = wb.createSheet("未售报表");

			for (int i = 0; i < districtList.size(); i++) {
				CommunityDistrictEntity district = districtList.get(i);
				int fontSize = 9;
				wbSheet = wb.createSheet(district.getDistrictName());
				// 设置默认行宽
				// wbSheet.setDefaultColumnWidth(30);
				wbSheet.setColumnWidth(0, 1500);
				wbSheet.setColumnWidth(1, 4000);
				wbSheet.setColumnWidth(2, 2500);
				wbSheet.setColumnWidth(3, 2500);
				wbSheet.setColumnWidth(4, 2500);
				wbSheet.setColumnWidth(5, 2500);
				wbSheet.setColumnWidth(6, 2500);
				wbSheet.setColumnWidth(7, 2500);
				wbSheet.setColumnWidth(8, 2500);
				wbSheet.setColumnWidth(9, 2500);
				wbSheet.setColumnWidth(10, 2500);

				wbSheet.setDefaultRowHeight((short) 410);
				// 标题样式（加粗，垂直居中）
				HSSFCellStyle cellStyle = wb.createCellStyle();
				cellStyle.setAlignment(HorizontalAlignment.CENTER);
				HSSFFont fontStyle = wb.createFont();
				fontStyle.setFontName("宋体");
				fontStyle.setBold(true); // 加粗
				fontStyle.setFontHeightInPoints((short) 14); // 设置标题字体大小
				cellStyle.setFont(fontStyle);

				HSSFCellStyle style = wb.createCellStyle();
				// 设置字体
				HSSFFont font = wb.createFont();
				font.setFontHeightInPoints((short) fontSize);
				font.setFontName("宋体");
				// font.setBold(true); //加粗
				style.setFont(font);

				// 设置列头元素
				HSSFCell cellHead = null;
				hql = new StringBuilder("SELECT distinct a from CommunityReceivablesEntity a inner join "
						+ "a.estate b inner join b.building c inner join c.district d where d.id=" + district.getId()
						+ " and b.estateState=0 group by a.payItemsName");
				List<CommunityReceivablesEntity> list = communityReceivablesDao.getListByHql(hql.toString(), "");
				Map<String, String> payItemsNameMap = new HashMap<String, String>();
				boolean isVilla = false;
				if (null != list && list.size() > 0 && list.get(0).getEstate().getBuilding().getBuildingType() == 1) {
					isVilla = true;
				}

				for (int j = 0; j < 4; j++) {// 第一行标题
					// 在第1行创建rows
					HSSFRow row = wbSheet.createRow((int) j);
					row.setHeight((short) 500);
					if (j == 0) {
						cellHead = row.createCell(0);
						cellHead.setCellStyle(getStyle(wb, (short) 14, true, HorizontalAlignment.CENTER_SELECTION));
						cellHead.setCellValue(district.getDistrictName() + "未售及未到交楼期单元费用清单");

						if (isVilla) {
							cellHead = row.createCell(7);
							cellHead.setCellStyle(getStyle(wb, (short) 14, true, HorizontalAlignment.CENTER_SELECTION));
							cellHead.setCellValue(DateUtil.formatShortFormat(new Date()) + "止");
							// 起始行，结束行，起始列，结束列
							CellRangeAddress region = new CellRangeAddress(0, 0, 0, 6);
							wbSheet.addMergedRegion(region);
							if (null != list && list.size() > 0) {
								region = new CellRangeAddress(0, 0, 7, 6 + list.size());
								wbSheet.addMergedRegion(region);
							}
						} else {
							cellHead = row.createCell(5);
							cellHead.setCellStyle(getStyle(wb, (short) 14, true, HorizontalAlignment.CENTER_SELECTION));
							cellHead.setCellValue(DateUtil.formatShortFormat(new Date()) + "止");
							CellRangeAddress region = new CellRangeAddress(0, 0, 0, 4);
							wbSheet.addMergedRegion(region);
							if (null != list && list.size() > 0) {
								region = new CellRangeAddress(0, 0, 5, 4 + list.size());
								wbSheet.addMergedRegion(region);
							}
						}

						// wbSheet.addMergedRegion(region);
					} else if (j == 1) {
						// cellHead = row.createCell(0);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.LEFT));
						// ellHead.setCellValue("制表日期：" + DateUtil.format(new Date(), 1));
						cellHead = row.createCell(0);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.LEFT));
						cellHead.setCellValue("合计");
						cellHead.setCellStyle(getStyle(wb, (short) 14, true, HorizontalAlignment.CENTER_SELECTION));
						if (isVilla) {
							// 起始行，结束行，起始列，结束列
							CellRangeAddress region = new CellRangeAddress(j, j, 0, 6);
							wbSheet.addMergedRegion(region);
						} else {
							CellRangeAddress region = new CellRangeAddress(j, j, 0, 4);
							wbSheet.addMergedRegion(region);
						}

						/*
						 * cellHead = row.createCell(7); //cellHead.setCellStyle(getStyle(wb, (short)
						 * 11, false, HorizontalAlignment.LEFT));
						 * cellHead.setCellValue("=SUM(E4:E"+(3+list.size())+")");
						 * 
						 * cellHead = row.createCell(8); //cellHead.setCellStyle(getStyle(wb, (short)
						 * 11, false, HorizontalAlignment.LEFT));
						 * cellHead.setCellValue("=SUM(F4:F"+(3+list.size())+")");
						 * 
						 * cellHead = row.createCell(9); //cellHead.setCellStyle(getStyle(wb, (short)
						 * 11, false, HorizontalAlignment.LEFT));
						 * cellHead.setCellValue("=SUM(F4:F"+(3+list.size())+")");
						 * 
						 * cellHead = row.createCell(10); //cellHead.setCellStyle(getStyle(wb, (short)
						 * 11, false, HorizontalAlignment.LEFT));
						 * cellHead.setCellValue("=SUM(F4:F"+(3+list.size())+")");
						 * 
						 * cellHead = row.createCell(11); //cellHead.setCellStyle(getStyle(wb, (short)
						 * 11, false, HorizontalAlignment.LEFT));
						 * cellHead.setCellValue("=SUM(F4:F"+(3+list.size())+")");
						 */
					} else if (j == 2) {
						cellHead = row.createCell(0);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("序号");
						CellRangeAddress region = new CellRangeAddress(j, j + 1, 0, 0);
						wbSheet.addMergedRegion(region);
						cellHead = row.createCell(1);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("单元编号");
						region = new CellRangeAddress(j, j + 1, 1, 1);
						wbSheet.addMergedRegion(region);
//                        cellHead = row.createCell(2);
//                        //cellHead.setCellStyle(getStyle(wb, (short) 11, false, HorizontalAlignment.CENTER));
//                        cellHead.setCellValue("业主");
						cellHead = row.createCell(2);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("单元状态");
						region = new CellRangeAddress(j, j + 1, 2, 2);
						wbSheet.addMergedRegion(region);
						/*
						 * cellHead = row.createCell(4); //cellHead.setCellStyle(getStyle(wb, (short)
						 * 11, false, HorizontalAlignment.CENTER)); cellHead.setCellValue("备注");
						 * cellHead = row.createCell(5); //cellHead.setCellStyle(getStyle(wb, (short)
						 * 11, false, HorizontalAlignment.CENTER)); cellHead.setCellValue("合同交楼日期");
						 */
						cellHead = row.createCell(3);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("房屋面积");
						region = new CellRangeAddress(j, j + 1, 3, 3);
						wbSheet.addMergedRegion(region);
						cellHead = row.createCell(4);
						// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
						// HorizontalAlignment.CENTER));
						cellHead.setCellValue("收费单价");
						region = new CellRangeAddress(j, j + 1, 4, 4);
						wbSheet.addMergedRegion(region);

						cellHead = row.createCell(5);
						if (null != list && list.size() > 0
								&& list.get(0).getEstate().getBuilding().getBuildingType() == 1) {
							// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
							// HorizontalAlignment.CENTER));
							cellHead.setCellValue("花园面积");
							region = new CellRangeAddress(j, j + 1, 5, 5);
							wbSheet.addMergedRegion(region);
							cellHead = row.createCell(6);
							// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
							// HorizontalAlignment.CENTER));
							cellHead.setCellValue("收费单价");
							region = new CellRangeAddress(j, j + 1, 6, 6);
							wbSheet.addMergedRegion(region);
							cellHead = row.createCell(7);
							// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
							// HorizontalAlignment.CENTER));
							cellHead.setCellValue("欠费累计");
							if (null != list && list.size() > 0) {
								region = new CellRangeAddress(j, j, 7, 6 + list.size());
								wbSheet.addMergedRegion(region);
							}
						} else {
							// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
							// HorizontalAlignment.CENTER));
							cellHead.setCellValue("欠费累计");
							if (null != list && list.size() > 0) {
								region = new CellRangeAddress(j, j, 5, 5 + list.size());
								wbSheet.addMergedRegion(region);
							}
						}

					} else if (j == 3) {
						for (int r = 0; r < list.size(); r++) {
							payItemsNameMap.put("key" + r, list.get(r).getPayItemsName());
							if (list.get(r).getEstate().getBuilding().getBuildingType() == 1) {
								cellHead = row.createCell(7 + r);
							} else {
								cellHead = row.createCell(5 + r);
							}
							// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
							// HorizontalAlignment.CENTER));
							cellHead.setCellValue(list.get(r).getPayItemsName());
						}

					}
				}
				List<Map<String, String>> data = getMonthUnsoldArrearageData(payItemsNameMap, district.getId());
				for (int d = 0; d < data.size(); d++) {
					Map<String, String> map = data.get(d);
					HSSFRow row = wbSheet.createRow((int) 4 + d);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue(d);
					cellHead = row.createCell(1);
					cellHead.setCellValue(map.get("unitCode"));
					cellHead = row.createCell(2);
					switch (map.get("estateState")) {
					case "0":
						cellHead.setCellValue("未收楼");
						break;
					case "1":
						cellHead.setCellValue("已入住");
						break;
					case "2":
						cellHead.setCellValue("装修中");
						break;
					case "3":
						cellHead.setCellValue("出租");
						break;
					case "4":
						cellHead.setCellValue("离退");
						break;
					}

					// cellHead.setCellValue(map.get("estateState"));
					cellHead = row.createCell(3);
					cellHead.setCellValue(map.get("buildingArea"));
					cellHead = row.createCell(4);
					if (map.get("buildingType").equals("1")) {
						cellHead.setCellValue(map.get("managementFee"));
						cellHead = row.createCell(5);
						cellHead.setCellValue(map.get("additionalArea"));
						cellHead = row.createCell(6);
						cellHead.setCellValue(map.get("gardenManagementFee"));
						for (int r = 0; r < list.size(); r++) {
							cellHead = row.createCell(7 + r);
							// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
							// HorizontalAlignment.CENTER));
							cellHead.setCellValue(map.get("key" + r));
						}
					} else {
						cellHead.setCellValue(map.get("managementFee"));
						for (int r = 0; r < list.size(); r++) {
							cellHead = row.createCell(5 + r);
							// cellHead.setCellStyle(getStyle(wb, (short) 11, false,
							// HorizontalAlignment.CENTER));
							cellHead.setCellValue(map.get("key" + r));
						}
					}
				}
			}
			if (req.getPreview() == 1) {
				res.setPreviewData(ExcelToHtml.readWorkbook(wb, true));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				try {
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					ExcelExportUtil.export(response, wb, "");
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		} else {
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO);
			return res;
		}
		return res;
	}

	public List<Map<String, String>> getMonthUnsoldArrearageData(Map<String, String> map, Integer districtId) {
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		// Transaction tx = session.beginTransaction();
		session.doWork(new Work() {
			@Override
			public void execute(Connection connection) throws SQLException {
				// TODO Auto-generated method stub
				Statement st = (Statement) connection.createStatement();
				StringBuilder sql = new StringBuilder();

				sql.append(
						"SELECT DISTINCT  a.estateId,d.unitCode,e.buildingName,a.receivableDate,d.estateState,d.buildingArea,d.additionalArea,");

				for (String key : map.keySet()) {
					sql.append(
							"(SELECT SUM(receivableAmount) FROM t_community_receivables b WHERE a.estateId=b.estateId AND b.payItemsName='"
									+ map.get(key) + "' ) AS '" + key + "', ");
				}
				sql.append(
						"(SELECT p1.price FROM t_community_pay_items p1 INNER JOIN `t_community_property_pay_items` ppi1 ON ppi1.payItemsId=p1.id WHERE ppi1.propertyId=d.id  AND (p1.itemsName='洋房管理费' or p1.itemsName='别墅管理费') and p1.startTime<='"
								+ DateUtil.format(new Date(), 1) + "' and p1.endTime>='"
								+ DateUtil.format(new Date(), 1) + "') AS 'managementFee'," +
				// "(SELECT p1.price FROM t_community_pay_items p1 INNER JOIN
				// `t_community_property_pay_items` ppi1 ON ppi1.payItemsId=p1.id WHERE
				// ppi1.propertyId=d.id AND p1.itemsName='别墅管理费' and
				// p1.startTime<='"+DateUtil.format(new Date(), 1)+"' and
				// p1.endTime>='"+DateUtil.format(new Date(), 1)+"' and
				// p1.endTime>='"+DateUtil.format(new Date(), 1)+"') AS 'villaManagementFee'," +
								"(SELECT p1.price FROM t_community_pay_items p1 INNER JOIN `t_community_property_pay_items` ppi1 ON ppi1.payItemsId=p1.id  WHERE ppi1.propertyId=d.id  AND p1.itemsName LIKE'花园%' AND p1.itemsName LIKE'%管理费' and p1.startTime<='"
								+ DateUtil.format(new Date(), 1) + "' and p1.endTime>='"
								+ DateUtil.format(new Date(), 1) + "') AS 'gardenManagementFee',");
				sql.append(
						" e.buildingType FROM t_community_receivables a INNER JOIN `t_community_property` d ON d.id=a.estateId INNER JOIN `t_community_building` e ON e.id=d.buildingId "
								+ "INNER JOIN `t_community_district` f ON f.id=e.districtId  "
								+ " WHERE d.estateState=0 AND f.id=" + districtId + "  GROUP BY a.estateId");
				BigDecimal total = new BigDecimal(0);
				ResultSet rs = st.executeQuery(sql.toString());
				while (rs.next()) {
					Map<String, String> dataMap = new HashMap<String, String>();
					for (String key : map.keySet()) {
						total = total.add(null != rs.getBigDecimal(key) ? rs.getBigDecimal(key) : new BigDecimal(0));
						dataMap.put(key, null != rs.getBigDecimal(key) ? rs.getBigDecimal(key).toString() : "");
					}
					dataMap.put("unitCode", rs.getString("unitCode"));
					dataMap.put("estateState", rs.getString("estateState"));
					dataMap.put("buildingType", rs.getInt("buildingType") + "");
					dataMap.put("buildingArea",
							null != rs.getBigDecimal("buildingArea") ? rs.getBigDecimal("buildingArea").toString()
									: "");
					dataMap.put("additionalArea",
							null != rs.getBigDecimal("additionalArea") ? rs.getBigDecimal("additionalArea").toString()
									: "");
					dataMap.put("total", total.toString());
					dataMap.put("managementFee",
							null != rs.getBigDecimal("managementFee") ? rs.getBigDecimal("managementFee").toString()
									: "");
					// dataMap.put("villaManagementFee",
					// null!=rs.getBigDecimal("villaManagementFee") ?
					// rs.getBigDecimal("villaManagementFee").toString() :"");
					dataMap.put("gardenManagementFee",
							null != rs.getBigDecimal("gardenManagementFee")
									? rs.getBigDecimal("gardenManagementFee").toString()
									: "");
					list.add(dataMap);
				}
				connection.close();
				rs.close();
				st.close();
			}
		});
//    		tx.commit();
		if (session != null) {
			session.close();
		}
		return list;
	}

	public IResponse collectionOfMonthManagementFees(ExportExcelReq req, HttpServletResponse response) {
		ExportExcelRes res = new ExportExcelRes();
		if (StringUtils.isNotEmpty(req.getMonth())) {
			StringBuilder hql = new StringBuilder("select distinct a from CommunityDistrictEntity a where a.state=1  ");
			List<CommunityDistrictEntity> districtList = communityDistrictDao.getListByHql(hql.toString(), "");
			HSSFWorkbook wb = new HSSFWorkbook();
			String[] month = req.getMonth().split("-");
			HSSFSheet wbSheet = wb.createSheet(month[month.length - 1]);
			wbSheet.setColumnWidth(0, 4000);
			wbSheet.setColumnWidth(1, 4000);
			wbSheet.setColumnWidth(2, 6000);
			wbSheet.setColumnWidth(3, 6000);
			wbSheet.setColumnWidth(4, 4000);
			wbSheet.setColumnWidth(5, 4000);
			wbSheet.setColumnWidth(6, 4000);
			wbSheet.setColumnWidth(7, 4000);
			wbSheet.setColumnWidth(8, 4000);
			wbSheet.setColumnWidth(9, 4000);
			wbSheet.setColumnWidth(10, 4000);
			wbSheet.setColumnWidth(11, 4000);
			wbSheet.setColumnWidth(12, 4000);
			wbSheet.setColumnWidth(13, 4000);
			HSSFCell cellHead = null;
			HSSFRow row = wbSheet.createRow(0);// 第一行
			row.setHeight((short) 420);
			cellHead = row.createCell(0);
			cellHead.setCellStyle(getStyle(wb, (short) 14, true, HorizontalAlignment.CENTER_SELECTION));
			cellHead.setCellValue("普华公司" + month[0] + "年" + month[month.length - 1] + "月管理费收缴情况表");
			// 起始行，结束行，起始列，结束列
			CellRangeAddress region = new CellRangeAddress(0, 0, 0, 13);
			wbSheet.addMergedRegion(region);

			row = wbSheet.createRow(1);// 第二行
			row.setHeight((short) 420);
			cellHead = row.createCell(0);
			cellHead.setCellValue("=SUM(A5:A" + (districtList.size() + 4) + ")");
			cellHead = row.createCell(1);
			cellHead.setCellValue("合计");

			cellHead = row.createCell(4);
			cellHead.setCellValue("=SUM(E5:E" + (districtList.size() + 4) + ")");
			cellHead = row.createCell(5);
			cellHead.setCellValue("=SUM(F5:F" + (districtList.size() + 4) + ")");
			cellHead = row.createCell(6);
			cellHead.setCellValue("=SUM(G5:G" + (districtList.size() + 4) + ")");
			cellHead = row.createCell(7);
			cellHead.setCellValue("=SUM(H5:H" + (districtList.size() + 4) + ")");
			cellHead = row.createCell(8);
			cellHead.setCellValue("=E2/A2");
			cellHead = row.createCell(9);
			cellHead.setCellValue("=SUM(J5:J" + (districtList.size() + 4) + ")");
			cellHead = row.createCell(10);
			cellHead.setCellValue("=SUM(K5:K" + (districtList.size() + 4) + ")");
			cellHead = row.createCell(11);
			cellHead.setCellValue("=(F2-K2)/J2");
			cellHead = row.createCell(12);
			cellHead.setCellValue("=SUM(E2:G" + (districtList.size() + 4) + ")");
			cellHead = row.createCell(13);
			cellHead.setCellValue("=SUM(N5:N" + (districtList.size() + 4) + ")");

			row = wbSheet.createRow(2);// 第三行
			row.setHeight((short) 420);
			cellHead = row.createCell(0);
			cellHead.setCellValue("应收业主");
			region = new CellRangeAddress(2, 3, 0, 0);
			wbSheet.addMergedRegion(region);
			cellHead = row.createCell(1);
			cellHead.setCellValue("区域");
			region = new CellRangeAddress(2, 3, 1, 1);
			wbSheet.addMergedRegion(region);
			cellHead = row.createCell(2);
			cellHead.setCellValue("业主应缴");
			cellHead = row.createCell(3);
			cellHead.setCellValue("当月收入");
			cellHead = row.createCell(4);
			cellHead.setCellValue("其中");
			region = new CellRangeAddress(2, 2, 4, 6);
			wbSheet.addMergedRegion(region);
			cellHead = row.createCell(7);
			cellHead.setCellValue("当月应收未收管理费本金");
			region = new CellRangeAddress(2, 3, 7, 7);
			wbSheet.addMergedRegion(region);
			cellHead = row.createCell(8);
			cellHead.setCellValue("实际业主收缴率");
			region = new CellRangeAddress(2, 3, 8, 8);
			wbSheet.addMergedRegion(region);
			cellHead = row.createCell(9);
			cellHead.setCellValue("前期欠管理费本金");
			region = new CellRangeAddress(2, 3, 9, 9);
			wbSheet.addMergedRegion(region);
			cellHead = row.createCell(10);
			cellHead.setCellValue("前欠费回款率");
			region = new CellRangeAddress(2, 2, 10, 11);
			wbSheet.addMergedRegion(region);
			cellHead = row.createCell(12);
			cellHead.setCellValue("当月收入");
			region = new CellRangeAddress(2, 2, 12, 13);
			wbSheet.addMergedRegion(region);

			row = wbSheet.createRow(3);// 第四行
			row.setHeight((short) 600);
			cellHead = row.createCell(2);
			cellHead.setCellValue("业主                    发展商");

			// 画线(由左上到右下的斜线) 在A1的第一个cell（单位 分类）加入一条对角线
			HSSFPatriarch patriarch = (HSSFPatriarch) wbSheet.createDrawingPatriarch();
			HSSFClientAnchor anchor = new HSSFClientAnchor(0, 0, 1023, 255, (short) 2, 3, (short) 2, 3);
			HSSFSimpleShape shape1 = patriarch.createSimpleShape(anchor);
			shape1.setShapeType(HSSFSimpleShape.OBJECT_TYPE_LINE);
			shape1.setLineStyle(HSSFSimpleShape.LINESTYLE_SOLID);

			cellHead = row.createCell(3);
			cellHead.setCellValue("业主                    发展商");
			anchor = new HSSFClientAnchor(0, 0, 1023, 255, (short) 3, 3, (short) 3, 3);
			shape1 = patriarch.createSimpleShape(anchor);
			shape1.setShapeType(HSSFSimpleShape.OBJECT_TYPE_LINE);
			shape1.setLineStyle(HSSFSimpleShape.LINESTYLE_SOLID);
			cellHead = row.createCell(4);
			cellHead.setCellValue("实收业主");
			cellHead = row.createCell(5);
			cellHead.setCellValue("收沉积");
			cellHead = row.createCell(6);
			cellHead.setCellValue("收预缴");

			cellHead = row.createCell(10);
			cellHead.setCellValue("减免交");
			cellHead = row.createCell(11);
			cellHead.setCellValue("实交比率");
			cellHead = row.createCell(12);
			cellHead.setCellValue("业主");
			cellHead = row.createCell(13);
			cellHead.setCellValue("开发商");
			Map<Integer, Integer> locationMap = new HashMap<Integer, Integer>();
			for (int i = 0; i < districtList.size(); i++) {
				row = wbSheet.createRow(4 + i);
				row.setHeight((short) 420);
				// 应收业主
				String proprietorReceivableTotalSql = "SELECT DISTINCT a.id AS receivablesId,SUM(a.receivableAmount) total,d1.id,d1.districtName  FROM `t_community_receivables` a "
						+ "INNER JOIN `t_community_property` b1 ON b1.id=a.estateId INNER JOIN `t_community_building` c1 ON b1.buildingId= c1.id INNER JOIN `t_community_district` d1 ON d1.id=c1.districtId"
						+ " WHERE  a.chargeCategory='管理费' AND a.chargeSource IS NOT NULL AND a.receivableDate LIKE'%"
						+ req.getMonth() + "%' "
						+ " AND a.estateId  IN(SELECT b.id FROM `t_community_property` b INNER JOIN`t_community_member_property` mp ON mp.propertyId=b.id INNER JOIN `t_community_building` c ON b.buildingId= c.id "
						+ " INNER JOIN `t_community_district` d ON d.id=c.districtId  WHERE d.id="
						+ districtList.get(i).getId() + " )";
				List<Map<String, Object>> list = getCollectionOfMonthManagementFeesData(proprietorReceivableTotalSql);
				cellHead = row.createCell(0);
				String proprietorReceivableTotal = "0";
				if (list.size() > 0) {
					proprietorReceivableTotal = list.get(0).get("total").toString();
					cellHead.setCellValue(proprietorReceivableTotal);
				} else {
					cellHead.setCellValue("0");
				}

				// 开发商应收款
				String developersReceivableTotalSql = "SELECT DISTINCT a.id AS receivablesId,d1.id,d1.districtName,SUM(a.receivableAmount) total "
						+ " FROM `t_community_receivables` a INNER JOIN `t_community_property` b1 ON b1.id=a.estateId "
						+ " INNER JOIN `t_community_building` c1 ON b1.buildingId= c1.id INNER JOIN `t_community_district` d1 ON d1.id=c1.districtId "
						+ " WHERE  a.chargeCategory='管理费' AND a.chargeSource IS NOT NULL AND a.receivableDate LIKE'"
						+ req.getMonth() + "%' AND d1.id=" + districtList.get(i).getId() + "    AND a.estateId NOT IN("
						+ " SELECT b.id FROM `t_community_property` b INNER JOIN`t_community_member_property` mp ON mp.propertyId=b.id INNER JOIN `t_community_building` c ON b.buildingId= c.id"
						+ " INNER JOIN `t_community_district` d ON d.id=c.districtId  WHERE d.id="
						+ districtList.get(i).getId() + " )";
				List<Map<String, Object>> developersReceivableTotalList = getCollectionOfMonthManagementFeesData(
						developersReceivableTotalSql);
				// 业主应缴
				cellHead = row.createCell(2);
				String developersReceivableTotal = "0";
				if (developersReceivableTotalList.size() > 0) {
					cellHead.setCellValue(proprietorReceivableTotal + "                 "
							+ developersReceivableTotalList.get(0).get("total"));
					developersReceivableTotal = developersReceivableTotalList.get(0).get("total").toString();
				} else {
					cellHead.setCellValue(proprietorReceivableTotal + "                 0");
				}
				// 画线(由左上到右下的斜线) 在A1的第一个cell（单位 分类）加入一条对角线
				patriarch = (HSSFPatriarch) wbSheet.createDrawingPatriarch();
				anchor = new HSSFClientAnchor(0, 0, 1023, 255, (short) 2, 4 + i, (short) 2, 4 + i);
				shape1 = patriarch.createSimpleShape(anchor);
				shape1.setShapeType(HSSFSimpleShape.OBJECT_TYPE_LINE);
				shape1.setLineStyle(HSSFSimpleShape.LINESTYLE_SOLID);

				// 设置区域
				BigDecimal total = new BigDecimal(0);
				cellHead = row.createCell(1);
				cellHead.setCellValue(districtList.get(i).getDistrictName());
				locationMap.put(districtList.get(i).getId(), i);
				// 实收业主
				String officialReceiptsSql = "SELECT DISTINCT rr.id,SUM(rr.currentAmount) total,d1.districtName FROM `t_community_receivables` a "
						+ " INNER JOIN `t_community_property` b1 ON b1.id=a.estateId INNER JOIN `t_community_building` c1 ON b1.buildingId= c1.id "
						+ " INNER JOIN `t_community_district` d1 ON d1.id=c1.districtId"
						+ " INNER JOIN `t_community_receipt_receivables` rr ON rr.receivablesId=a.id INNER JOIN t_community_receipt r ON r.id=rr.receiptId  "
						+ " WHERE  a.chargeCategory='管理费' AND a.chargeSource IS NOT NULL AND a.receivableDate LIKE'"
						+ req.getMonth() + "%'  AND r.receiptDate LIKE'" + req.getMonth() + "%' AND a.estateId  IN("
						+ " SELECT b.id FROM `t_community_property` b INNER JOIN`t_community_member_property` mp ON mp.propertyId=b.id INNER JOIN `t_community_building` c ON b.buildingId= c.id"
						+ " INNER JOIN `t_community_district` d ON d.id=c.districtId  WHERE d.id="
						+ districtList.get(i).getId() + " )";
				list = getCollectionOfMonthManagementFeesData(officialReceiptsSql);
				cellHead = row.createCell(4);
				if (list.size() > 0) {
					cellHead.setCellValue(list.get(0).get("total").toString());
					total = total.add((new BigDecimal(list.get(0).get("total").toString())));
				} else {
					cellHead.setCellValue("0");
				}
				// 沉积
				String sedimentationSql = "SELECT d.id,d.districtName,SUM(rr.currentAmount) total FROM `t_community_receivables` a INNER JOIN `t_community_property` b ON b.id=a.estateId INNER JOIN `t_community_building` c ON b.buildingId= c.id "
						+ " INNER JOIN `t_community_district` d ON d.id=c.districtId INNER JOIN `t_community_receipt_receivables` rr ON rr.receivablesId=a.id INNER JOIN t_community_receipt r ON r.id=rr.receiptId "
						+ " WHERE  a.chargeCategory='管理费' AND a.chargeSource IS NOT NULL AND a.receivableDate <'"
						+ req.getMonth() + "-01 00:00:00'  AND r.receiptDate LIKE'" + req.getMonth() + "%'  AND d.id="
						+ districtList.get(i).getId();
				list = getCollectionOfMonthManagementFeesData(sedimentationSql);
				cellHead = row.createCell(5);
				if (list.size() > 0) {
					cellHead.setCellValue(list.get(0).get("total").toString());
					total = total.add((new BigDecimal(list.get(0).get("total").toString())));
				} else {
					cellHead.setCellValue("0");
				}
				String prepaySql = "";
				try {
					prepaySql = " SELECT d.id,d.districtName,SUM(rr.currentAmount) total FROM `t_community_receivables` a INNER JOIN `t_community_property` b ON b.id=a.estateId INNER JOIN `t_community_building` c ON b.buildingId= c.id "
							+ " INNER JOIN `t_community_district` d ON d.id=c.districtId INNER JOIN `t_community_receipt_receivables` rr ON rr.receivablesId=a.id INNER JOIN t_community_receipt r ON r.id=rr.receiptId "
							+ " WHERE  a.chargeCategory='管理费' AND a.chargeSource IS NOT NULL AND a.receivableDate >'"
							+ DateUtil
									.formatLongFormat(lastDayOfMonth(DateUtil.parseShortFormat(req.getMonth() + "-01")))
							+ "'  AND r.receiptDate LIKE'" + req.getMonth() + "%' and d.id="
							+ districtList.get(i).getId();
				} catch (ParseException e) {
					e.printStackTrace();
				}
				list = getCollectionOfMonthManagementFeesData(prepaySql);
				cellHead = row.createCell(6);
				if (list.size() > 0) {
					cellHead.setCellValue(list.get(0).get("total").toString());
					total = total.add((new BigDecimal(list.get(0).get("total").toString())));
				} else {
					cellHead.setCellValue("0");
				}

				// 当月收入
				cellHead = row.createCell(3);
				String developers = "0";
				if (!developersReceivableTotal.equals("0")) {
					developersReceivableTotalSql = "SELECT DISTINCT rr.id AS rrId,d1.id,d1.districtName,SUM(rr.currentAmount)  total FROM `t_community_receivables` a "
							+ " INNER JOIN `t_community_receipt_receivables` rr ON rr.receivablesId=a.id INNER JOIN t_community_receipt r ON r.id=rr.receiptId  "
							+ " INNER JOIN `t_community_property` b1 ON b1.id=a.estateId INNER JOIN `t_community_building` c1 ON b1.buildingId= c1.id"
							+ " INNER JOIN `t_community_district` d1 ON d1.id=c1.districtId"
							+ " WHERE  a.chargeCategory='管理费' AND a.chargeSource IS NOT NULL AND a.receivableDate LIKE'"
							+ req.getMonth() + " ' AND r.receiptDate LIKE'%" + req.getMonth() + "%' AND d1.id="
							+ districtList.get(i).getId() + "    AND a.estateId NOT IN("
							+ " SELECT b.id FROM `t_community_property` b INNER JOIN`t_community_member_property` mp ON mp.propertyId=b.id INNER JOIN `t_community_building` c ON b.buildingId= c.id"
							+ " INNER JOIN `t_community_district` d ON d.id=c.districtId  WHERE d.id="
							+ districtList.get(i).getId() + " )";
					developersReceivableTotalList = getCollectionOfMonthManagementFeesData(
							developersReceivableTotalSql);
					if (developersReceivableTotalList.size() > 0) {
						developers = developersReceivableTotalList.get(0).get("total").toString();
						cellHead.setCellValue(total + "                 " + developers);
					} else {
						cellHead.setCellValue(total + "                 0");
					}
				} else {
					cellHead.setCellValue(total + "                 0");
				}
				// 画线(由左上到右下的斜线) 在A1的第一个cell（单位 分类）加入一条对角线
				patriarch = (HSSFPatriarch) wbSheet.createDrawingPatriarch();
				anchor = new HSSFClientAnchor(0, 0, 1023, 255, (short) 3, 4 + i, (short) 3, 4 + i);
				shape1 = patriarch.createSimpleShape(anchor);
				shape1.setShapeType(HSSFSimpleShape.OBJECT_TYPE_LINE);
				shape1.setLineStyle(HSSFSimpleShape.LINESTYLE_SOLID);

				cellHead = row.createCell(7);
				cellHead.setCellValue("=" + proprietorReceivableTotal + "-E" + 5 + i);
				cellHead = row.createCell(8);
				cellHead.setCellValue("=E" + (5 + i) + "/A" + (5 + i));
				String arrearageSql = "SELECT DISTINCT a.id AS receivablesId,SUM(a.receivableAmount-a.receivedAmount) total,d.id,d.districtName FROM `t_community_receivables` a "
						+ " INNER JOIN `t_community_property` b ON b.id=a.estateId INNER JOIN `t_community_building` c ON b.buildingId= c.id"
						+ " INNER JOIN `t_community_district` d ON d.id=c.districtId  WHERE  "
						+ " a.chargeCategory='管理费' AND a.chargeSource IS NOT NULL AND a.receivableAmount>a.receivedAmount AND a.receivableDate <'"
						+ req.getMonth() + "-01 00:00:00' AND d.id=" + districtList.get(i).getId();
				list = getCollectionOfMonthManagementFeesData(arrearageSql);
				cellHead = row.createCell(9);
				if (list.size() > 0) {
					cellHead.setCellValue(list.get(0).get("total").toString());
					total = total.add((new BigDecimal(list.get(0).get("total").toString())));
				} else {
					cellHead.setCellValue("0");
				}
				cellHead = row.createCell(10);
				cellHead.setCellValue("0");

				cellHead = row.createCell(11);
				// cellHead.setCellType(CellType.FORMULA);
				cellHead.setCellFormula("(F" + (5 + i) + "-K" + (5 + i) + ")/J" + (5 + i));
				cellHead = row.createCell(12);
				// cellHead.setCellType(CellType.FORMULA);
				cellHead.setCellFormula("SUM(E" + (5 + i) + ":G" + (5 + i) + ")");

				cellHead = row.createCell(13);
				cellHead.setCellValue(developers);
			}

			if (req.getPreview() == 1) {
				res.setPreviewData(ExcelToHtml.readWorkbook(wb, true));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				try {
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					ExcelExportUtil.export(response, wb, "");
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		} else {
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO);
			return res;
		}
		return res;
	}

	public List<Map<String, Object>> getCollectionOfMonthManagementFeesData(String sql) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		// Transaction tx = session.beginTransaction();
		session.doWork(new Work() {
			@Override
			public void execute(Connection connection) throws SQLException {
				// TODO Auto-generated method stub
				Statement st = (Statement) connection.createStatement();
				ResultSet rs = st.executeQuery(sql);
				while (rs.next()) {
					Map<String, Object> dataMap = new HashMap<String, Object>();
					dataMap.put("id", rs.getInt("id"));
					dataMap.put("districtName", rs.getString("districtName"));
					// dataMap.put("total", null!=rs.getBigDecimal("total") ?
					// rs.getBigDecimal("total") :new BigDecimal(0));
					dataMap.put("total", StringUtils.isNotEmpty(rs.getString("total")) ? rs.getString("total") : "0");
					list.add(dataMap);
				}
				connection.close();
				rs.close();
				st.close();
			}
		});
//    		tx.commit();
		if (session != null) {
			session.close();
		}
		return list;
	}

	public IResponse exportOutstandingReceivable(ExportExcelReq req, HttpServletResponse response) {
		ExportExcelRes res = new ExportExcelRes();
		if (StringUtils.isNotEmpty(req.getMonth())) {
			StringBuilder hql = new StringBuilder("select distinct a from CommunityDistrictEntity a where a.state=1  ");
			List<CommunityDistrictEntity> districtList = communityDistrictDao.getListByHql(hql.toString(), "");
			HSSFWorkbook wb = new HSSFWorkbook();
			// String[] month = req.getMonth().split("-");
			HSSFSheet wbSheet = wb.createSheet("未收款汇总");

			Map<Integer, String> map = new HashMap<Integer, String>();

			int value = 0;
			char key = 'A' - 1;
			char end = 'Z';
			for (int i = 0; i < 5; i++) {
				while (key < end) {
					key++;
					int o = i - 1;
					map.put(value, (null != map.get(o) ? map.get(o) + (key + "") : key + ""));
					value++;
				}
				key = 'A' - 1;
			}

			int column = districtList.size() * 2 + 5;
			for (int i = 0; i < column; i++) {
				if (i == 0) {
					wbSheet.setColumnWidth(0, 6000);
				} else if (i == (column - 1)) {
					wbSheet.setColumnWidth(0, 8000);
				} else if (i == (column - 2) || i == (column - 3) || i == (column - 4)) {
					wbSheet.setColumnWidth(0, 5000);
				} else {
					wbSheet.setColumnWidth(0, 3000);
				}
			}

			HSSFCell cellHead = null;

			for (int i = 0; i < 17; i++) {
				HSSFRow row = null;
				CellRangeAddress region = null;
				switch (i) {
				case 0: {
					row = wbSheet.createRow(i);
					row.setHeight((short) 500);
					cellHead = row.createCell(0);
					cellHead.setCellStyle(getStyle(wb, (short) 18, true, HorizontalAlignment.CENTER_SELECTION));
					cellHead.setCellValue("应收未收款汇总");
					// 起始行，结束行，起始列，结束列
					region = new CellRangeAddress(0, 0, 0, districtList.size() * 2);
					wbSheet.addMergedRegion(region);
					cellHead = row.createCell((districtList.size() * 2) + 1);
					cellHead.setCellStyle(getStyle(wb, (short) 12, true, HorizontalAlignment.CENTER_SELECTION));
					try {
						cellHead.setCellValue("(" + DateUtil
								.formatLongFormat(lastDayOfMonth(DateUtil.parseShortFormat(req.getMonth() + "-01")))
								.split(" ")[0] + "止)");
					} catch (ParseException e) {
						e.printStackTrace();
					}
					// 起始行，结束行，起始列，结束列
					region = new CellRangeAddress(0, 0, (districtList.size() * 2) + 1, (districtList.size() * 2) + 4);
					wbSheet.addMergedRegion(region);
					break;
				}
				case 1: {
					row = wbSheet.createRow(i);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellStyle(getStyle(wb, (short) 12, true, HorizontalAlignment.CENTER_SELECTION));
					cellHead.setCellValue("抄送：");
					region = new CellRangeAddress(1, 1, 0, (districtList.size() * 2) + 4);
					wbSheet.addMergedRegion(region);
					break;
				}
				case 2: {
					row = wbSheet.createRow(i);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("类别");
					region = new CellRangeAddress(2, 3, 0, 0);
					wbSheet.addMergedRegion(region);
					cellHead = row.createCell(1);
					cellHead.setCellValue("户数");
					region = new CellRangeAddress(2, 2, 1, districtList.size());
					wbSheet.addMergedRegion(region);

					cellHead = row.createCell(districtList.size());
					cellHead.setCellValue("金额（含违约金）");
					region = new CellRangeAddress(2, 2, districtList.size() + 1, (districtList.size() * 2));
					wbSheet.addMergedRegion(region);

					cellHead = row.createCell(districtList.size() * 2 + 4);
					cellHead.setCellValue("备注");
					region = new CellRangeAddress(2, 3, districtList.size() * 2 + 4, (districtList.size() * 2) + 4);
					wbSheet.addMergedRegion(region);
					break;
				}
				case 3: {
					row = wbSheet.createRow(i);
					row.setHeight((short) 420);
					for (int j = 0; j < 2; j++) {
						for (int r = 0; r < districtList.size(); r++) {
							cellHead = row.createCell((j * districtList.size()) + r + 1);
							cellHead.setCellValue(districtList.get(r).getDistrictName());
						}
					}
					cellHead = row.createCell((2 * districtList.size()) + 1);
					cellHead.setCellValue("总计");
					cellHead = row.createCell((2 * districtList.size()) + 2);
					cellHead.setCellValue("上月金额");
					cellHead = row.createCell((2 * districtList.size()) + 3);
					cellHead.setCellValue("上月对比");
					break;
				}
				case 4: {
					row = wbSheet.createRow(i);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("总计：");
					for (int j = 0; j < 2; j++) {
						for (int r = 0; r < districtList.size(); r++) {
							cellHead = row.createCell((j * districtList.size()) + r + 1);
							String columnLetter = map.get((j * districtList.size()) + r + 1);
							if (j == 0) {
								cellHead.setCellValue(
										"=SUM(" + columnLetter + "6," + columnLetter + "10:" + columnLetter + "12)");
							} else {
								cellHead.setCellValue(
										"=SUM(" + columnLetter + "6+" + columnLetter + "10+" + columnLetter + "12)");
							}
						}
					}
					break;
				}
				case 5: {
					row = wbSheet.createRow(i);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("已售欠费");
					for (int j = 0; j < 2; j++) {
						for (int r = 0; r < districtList.size(); r++) {
							cellHead = row.createCell((j * districtList.size()) + r + 1);
							String columnLetter = map.get((j * districtList.size()) + r + 1);
							if (j == 0) {
								cellHead.setCellValue(
										"=SUM(" + columnLetter + "6," + columnLetter + "10:" + columnLetter + "12)");
							} else {
								cellHead.setCellValue(
										"=SUM(" + columnLetter + "6+" + columnLetter + "10+" + columnLetter + "12)");
							}
						}
					}
					break;
				}
				case 6: {
					row = wbSheet.createRow(i);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("车位欠费");

					break;
				}
				case 7: {
					row = wbSheet.createRow(i);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("3个月内欠费");

					break;
				}
				case 8: {
					row = wbSheet.createRow(i);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("与上月对比");
					row = wbSheet.createRow(9);
					row.setHeight((short) 1000);
					cellHead = row.createCell(0);
					region = new CellRangeAddress(8, 8, 0, districtList.size() * +4);
					wbSheet.addMergedRegion(region);

					row = wbSheet.createRow(10);
					row.setHeight((short) 420);
					break;
				}

				case 10: {
					row = wbSheet.createRow(11);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("应收发展商可售未售单位费用");

					break;
				}
				case 11: {
					row = wbSheet.createRow(12);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("应收发展商已售未到交楼期单位费用");

					break;
				}
				case 12: {
					row = wbSheet.createRow(13);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("应收发展商负责承担费用");

					break;
				}
				case 13: {
					row = wbSheet.createRow(14);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("高富代付款");
					row = wbSheet.createRow(15);
					row.setHeight((short) 420);

					break;
				}

				case 15: {
					row = wbSheet.createRow(16);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("违约金");

					break;
				}
				case 16: {
					row = wbSheet.createRow(17);
					row.setHeight((short) 420);
					row = wbSheet.createRow(18);
					row.setHeight((short) 420);

					row = wbSheet.createRow(19);
					row.setHeight((short) 420);
					cellHead = row.createCell(0);
					cellHead.setCellValue("电子签阅：");
					region = new CellRangeAddress(19, 26, 0, 13);
					wbSheet.addMergedRegion(region);

					row = wbSheet.createRow(19);
					row.setHeight((short) 420);
					cellHead = row.createCell(21);
					cellHead.setCellValue("制       表：");
					region = new CellRangeAddress(19, 19, 21, 22);
					wbSheet.addMergedRegion(region);

					row = wbSheet.createRow(22);
					row.setHeight((short) 420);
					cellHead = row.createCell(21);
					cellHead.setCellValue("财务复核：");
					region = new CellRangeAddress(22, 22, 21, 22);
					wbSheet.addMergedRegion(region);

					row = wbSheet.createRow(26);
					row.setHeight((short) 420);
					cellHead = row.createCell(21);
					cellHead.setCellValue("制表日期：");
					region = new CellRangeAddress(26, 26, 21, 22);
					wbSheet.addMergedRegion(region);

					row = wbSheet.createRow(27);
					row.setHeight((short) 600);
					cellHead = row.createCell(0);
					cellHead.setCellValue("备注：具体欠费原因见电子版欠费汇总表备注栏");
					region = new CellRangeAddress(27, 27, 0, 13);
					wbSheet.addMergedRegion(region);

					break;
				}

				default:
					break;
				}
			}

			if (req.getPreview() == 1) {
				res.setPreviewData(ExcelToHtml.readWorkbook(wb, true));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				try {
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					ExcelExportUtil.export(response, wb, "");
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		} else {
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO);
			return res;
		}
		return res;
	}

	public Map<String, Object> getOutstandingReceivableData(Integer districtId) {
		Map<String, Object> map = new HashMap<String, Object>();
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		// Transaction tx = session.beginTransaction();
		session.doWork(new Work() {
			@SuppressWarnings("unchecked")
			@Override
			public void execute(Connection connection) throws SQLException {
				// TODO Auto-generated method stub
				Statement st = (Statement) connection.createStatement();
				String sql = "SELECT DISTINCT a.id,b.unitCode,b.estateState,ac.userName,pa.bankAccount,a.payItemsName,(a.receivableAmount-a.receivedAmount) AS receivableAmount,a.receivableDate "
						+ "FROM `t_community_receivables` a INNER JOIN `t_community_property` b ON b.id=a.estateId INNER JOIN `t_community_building` c ON b.buildingId= c.id "
						+ "INNER JOIN `t_community_member_property` mp ON mp.propertyId=b.id INNER JOIN `t_account` ac ON ac.id=mp.memberId INNER JOIN `t_community_payment_account` pa ON pa.id = b.paymentAccountId "
						+ "INNER JOIN `t_community_district` d ON d.id=c.districtId WHERE d.id=" + districtId
						+ " AND mp.isCurrentOwner=1 AND a.receivableAmount>a.receivedAmount ORDER BY a.estateId ASC";
				ResultSet rs = st.executeQuery(sql);
				while (rs.next()) {

					String unitCode = rs.getString("unitCode");
					Map<String, Object> dataMap = null;
					if (map.containsKey(unitCode)) {
						dataMap = (Map<String, Object>) map.get(unitCode);
					} else {
						dataMap = new HashMap<String, Object>();
					}

					map.put(rs.getString("unitCode"), dataMap);

					BigDecimal total = map.containsKey("receivableDate") ? (BigDecimal) map.get("receivableDate")
							: new BigDecimal(0);
					dataMap.put("estateState", rs.getInt("estateState"));
					dataMap.put("userName", rs.getString("userName"));
					dataMap.put("bankAccount", rs.getString("bankAccount"));
					dataMap.put("payItemsName", rs.getString("payItemsName"));

					String receivableAmount = rs.getString("receivableAmount");
					total = total.add(new BigDecimal(receivableAmount));
					dataMap.put("total", total);
					//Date receivableDate = rs.getDate("receivableDate");

					dataMap.put("total", StringUtils.isNotEmpty(rs.getString("total")) ? rs.getString("total") : "0");
				}
				connection.close();
				rs.close();
				st.close();
			}
		});
//    		tx.commit();
		if (session != null) {
			session.close();
		}
		return map;
	}

	public IResponse exportExcelBySql(ExportExcelReq req, HttpServletResponse response) {
		ExportExcelRes res = new ExportExcelRes();
		if (StringUtils.isNotEmpty(req.getSql())) {
			Map<String, String> heardMap = new LinkedHashMap<String, String>();
			List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();
			// Transaction tx = session.beginTransaction();
			session.doWork(new Work() {
				@Override
				public void execute(Connection connection) throws SQLException {
					// TODO Auto-generated method stub
					Statement st = (Statement) connection.createStatement();
					ResultSet rs = st.executeQuery(req.getSql());
					ResultSetMetaData md = rs.getMetaData();
					int columnCount = md.getColumnCount();
					boolean mark = true;
					while (rs.next()) {
						Map<String, Object> map = new HashMap<String, Object>();
						for (int i = 1; i <= columnCount; i++) {
							if (mark) {
								heardMap.put(md.getColumnName(i), md.getColumnName(i));
							}
							if (rs.getObject(i) instanceof String) {
								map.put(md.getColumnName(i),
										StringUtils.isNotEmpty(rs.getString(i)) ? rs.getObject(i) : "");
							} else if (rs.getObject(i) instanceof Integer) {
								map.put(md.getColumnName(i), rs.getInt(i));
							} else if (rs.getObject(i) instanceof BigDecimal) {
								map.put(md.getColumnName(i), null != rs.getBigDecimal(i) ? rs.getBigDecimal(i) : "");
							} else if (rs.getObject(i) instanceof Date) {
								map.put(md.getColumnName(i),
										null != rs.getDate(i) ? DateUtil.formatLongFormat(rs.getDate(i)) : "");
							} else if (rs.getObject(i) instanceof Timestamp) {
								map.put(md.getColumnName(i),
										null != rs.getTimestamp(i) ? DateUtil.formatLongFormat(rs.getTimestamp(i))
												: "");
							} else if (null == rs.getObject(i)) {
								map.put(md.getColumnName(i), "");
							} else {
								map.put(md.getColumnName(i), rs.getObject(i));
							}

						}
						dataList.add(map);
						mark = false;
					}
					connection.close();
					rs.close();
					st.close();
				}
			});
//    	     		tx.commit();
			if (session != null) {
				session.close();
			}
			try {
//    	     		Map<String,Short> map = new HashMap<String,Short>();
//    	     		map.put("表编号", Short.valueOf("30000"));
				ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "",
						"", "", response);
			} catch (Exception e) {
				e.printStackTrace();
				res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
				res.setRetInfo("导出异常！");
			}

		} else {
			res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_INVALID_PARAM_INFO);
			return res;
		}
		return res;
	}

	public IResponse exportCommunityMeterProperty(ExportExcelReq req, HttpServletResponse response)
			throws ParseException {
		ExportExcelRes res = new ExportExcelRes();
		if (null != req.getMeterId()) {
			CommunityMeterEntity communityMeter = communityMeterDao.get(req.getMeterId());
			if (null != communityMeter) {
				Map<String, String> heardMap = new LinkedHashMap<String, String>();
				heardMap.put("meterName", "表名称");
				heardMap.put("unitCode", "单元编号");
				heardMap.put("buildingName", "楼栋名称");
				heardMap.put("floor", "楼层");
				heardMap.put("roomNumber", "房号");
				heardMap.put("buildingArea", "建筑面积");
				heardMap.put("additionalArea", "附加面积");
				heardMap.put("chargingArea", "收费面积");
				heardMap.put("usableArea", "使用面积");
				List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
				communityMeter.getPropertyList().forEach(o -> {
					CommunityEstateEntity estate = (CommunityEstateEntity) o;
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("meterName", communityMeter.getMeterName());
					map.put("unitCode", estate.getUnitCode());
					map.put("buildingName", estate.getBuilding().getBuildingName());
					map.put("floor", estate.getFloor());
					map.put("roomNumber", estate.getRoomNumber());
					map.put("buildingArea", estate.getBuildingArea());
					map.put("additionalArea", estate.getAdditionalArea());
					map.put("chargingArea", estate.getChargingArea());
					map.put("usableArea", estate.getUsableArea());
					dataList.add(map);
				});
				try {
					ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null,
							"", "", "", response);
				} catch (Exception e) {
					e.printStackTrace();
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo("导出异常！");
				}
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}

		return res;
	}

	public IResponse exportCommunityMeterRecord(CommunityMeterRecordReq req, HttpServletResponse response)
			throws ParseException {
		ExportExcelRes res = new ExportExcelRes();

		StringBuilder hql = new StringBuilder(
				"select distinct a from CommunityMeterRecordEntity a inner join a.communityMeter b inner join b.meterAttributes c where 1=1");
		hql.append(null != req.getMeterId() ? " and b.id=" + req.getMeterId() : "").append(
				StringUtils.isNotEmpty(req.getMeterName()) ? " and b.meterName like'%" + req.getMeterName() + "%'" : "")
				.append(StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())
						? " and a.recordDate>='" + req.getStartTime() + "' and a.recordDate<='" + req.getEndTime() + "'"
						: "")
				.append(StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isEmpty(req.getEndTime())
						? " and a.recordDate>='" + req.getStartTime() + "' "
						: "")
				.append(StringUtils.isNotEmpty(req.getCategoryList()) ? " and c.category in("+req.getCategoryList()+")":"" )
				.append(null != req.getState() ? " and a.state=" + req.getState()
						: " and a.state=" + EntityContext.RECORD_STATE_VALID)
				.append(null != req.getIsCommon() ? " and b.isCommon=" + req.getIsCommon() : "")
				.append(StringUtils.isNotEmpty(req.getPropertyIdList()) ? " and b.propertyIdList.id in("+req.getPropertyIdList()+")":"");

		hql.append(" ORDER BY a.recordDate DESC,b.meterName ASC");
		List<CommunityMeterRecordEntity> list = communityMeterRecordDao.getListByHql(hql.toString(), "");

		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("meterName", "表名称");
		heardMap.put("meterCode", "表编号");
		heardMap.put("recordDate", "本次读数日期");
		heardMap.put("recordNum", "本次读数");
		heardMap.put("lastNum", "上次读数");
		heardMap.put("margin", "本次数－上次数");
		heardMap.put("lastModifyTime", "修改时间");
		heardMap.put("additionalAmount", "额外用量");
		heardMap.put("additionalInstructions", "额外用量说明");
		heardMap.put("recorder", "抄表人");
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		list.forEach(o -> {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("meterName", o.getCommunityMeter().getMeterName());
			map.put("meterCode", o.getCommunityMeter().getMeterCode());
			map.put("recordDate", null != o.getRecordDate() ? DateUtil.formatShortFormat(o.getRecordDate()) : "");
			map.put("recordNum", null != o.getRecordNum() ? o.getRecordNum().toString() : "");
			map.put("lastModifyTime",
					null != o.getLastModifyTime() ? DateUtil.formatShortFormat(o.getLastModifyTime()) : "");
			map.put("recorder", o.getRecorder());
			map.put("additionalAmount", null!=o.getAdditionalAmount() ? o.getAdditionalAmount().toString() :"");
			map.put("additionalInstructions", o.getAdditionalInstructions());
			if (null != o.getLastRecordId()) {
				CommunityMeterRecordEntity lastMeterRecord = communityMeterRecordDao.get(o.getLastRecordId());
				if (null != lastMeterRecord) {
					map.put("lastNum", lastMeterRecord.getRecordNum().toString());
					map.put("margin",
							null != o.getRecordNum() && null != lastMeterRecord.getRecordNum()
									? o.getRecordNum().subtract(lastMeterRecord.getRecordNum())
									: (null != o.getRecordNum() && null == lastMeterRecord.getRecordNum()
											? o.getRecordNum().toString()
											: (null == o.getRecordNum() && null != lastMeterRecord.getRecordNum()
													? lastMeterRecord.getRecordNum().toString()
													: "")));
				} else {
					map.put("lastNum", "");
					map.put("margin", null != o.getRecordNum() ? o.getRecordNum().toString() : "");
				}
			} else {
				map.put("lastNum", "");
				map.put("margin", null != o.getRecordNum() ? o.getRecordNum().toString() : "");
			}

			dataList.add(map);
		});
		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

		return res;
	}

	public IResponse exportCommunityMeterAttributes(CommunityMeterAttributesReq req, HttpServletResponse response)
			throws ParseException {
		ExportExcelRes res = new ExportExcelRes();
		StringBuilder hql = new StringBuilder("select distinct a from CommunityMeterAttributesEntity a where 1=1 ");
		hql.append(null != req.getCategory() ? " and a.category=" + req.getCategory() : "")
				.append(StringUtils.isNotEmpty(req.getAttributeName())
						? " and a.attributeName like'%" + req.getAttributeName() + "%'"
						: "");
		hql.append(" ORDER BY a.id desc");
		List<CommunityMeterAttributesEntity> list = communityMeterAttributesDao.getListByHql(hql.toString(), "");

		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("attributeName", "属性名称");
		heardMap.put("category", "类别");
		heardMap.put("rate", "倍率");
		heardMap.put("unitPrice", "单价");
		heardMap.put("measureUnit", "单位");
		heardMap.put("ranges", "量程");
		heardMap.put("comment", "备注");

		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		list.forEach(o -> {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("attributeName", o.getAttributeName());
			int k = o.getCategory() == null ? -1 : o.getCategory().intValue();
			switch (k) {
			case 1:
				map.put("category", "电表");
				break;
			case 2:
				map.put("category", "水表");
				break;
			case 3:
				map.put("category", "临时表");
				break;
			case 4:
				map.put("category", "代收水表");
				break;
			case 5:
				map.put("category", "代收电表");
				break;
			default:
				map.put("category", "");
				break;
			}
			map.put("rate", null != o.getRate() ? o.getRate().toString() : null);
			map.put("unitPrice", null != o.getUnitPrice() ? o.getUnitPrice().toString() : null);

			map.put("measureUnit", StringUtils.isNotEmpty(o.getMeasureUnit()) ? o.getMeasureUnit() : "");
			map.put("ranges", null != o.getRanges() ? o.getRanges() : "");
			map.put("comment", StringUtils.isNotEmpty(o.getComment()) ? o.getComment() : "");

			dataList.add(map);
		});
		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}
		return res;
	}

	public IResponse exportCommunitypayItems(HttpServletResponse response) throws ParseException {
		ExportExcelRes res = new ExportExcelRes();
		StringBuilder hql = new StringBuilder("select distinct a from CommunityPayItemsEntity a where state=1 ");
		hql.append(" ORDER BY a.id desc");
		List<CommunityPayItemsEntity> list = communityPayItemsDao.getListByHql(hql.toString(), "");

		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("payItemsCode", "收费项目编码");
		heardMap.put("itemsName", "项目名称");
		heardMap.put("chargeCategory", "费用类别");
		heardMap.put("price", "单价");
		heardMap.put("priceUnit", "价格单位");
		heardMap.put("startTime", "开始时间");
		heardMap.put("endTime", "结束时间");
		heardMap.put("feeCalType", "管理费计算类型");
		heardMap.put("isBreach", "是否产生违约金");
		heardMap.put("breachRatio", "违约金系数");
		heardMap.put("isReceivables", "是否生成应收款");
		heardMap.put("payDate", "交款日");
		heardMap.put("priority", "扣费优先级");
		heardMap.put("comment", "备注");

		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		list.forEach(o -> {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("payItemsCode", "SFXM-" + o.getId());
			map.put("breachRatio", null != o.getBreachRatio() ? o.getBreachRatio().toString() : "");
			map.put("comment", StringUtils.isNotEmpty(o.getComment()) ? o.getComment() : "");
			map.put("endTime", null != o.getEndTime() ? DateUtil.formatLongFormat(o.getEndTime()) : "");
			map.put("isReceivables", null != o.getIsReceivables() ? o.getIsReceivables().toString() : "");
			map.put("itemsName", o.getItemsName());
			map.put("price", null != o.getPrice() ? o.getPrice() : "");
			map.put("priceUnit", StringUtils.isNotEmpty(o.getPriceUnit()) ? o.getPriceUnit() : "");
			map.put("priority", o.getPriority());
			map.put("startTime", null != o.getStartTime() ? DateUtil.formatLongFormat(o.getStartTime()) : "");

			int k = o.getChargeCategory() == null ? -1 : o.getChargeCategory().intValue();
			switch (k) {
			case 1:
				map.put("chargeCategory", "管理费");
				break;
			case 2:
				map.put("chargeCategory", "车位费");
				break;
			case 3:
				map.put("chargeCategory", "分摊费");
				break;
			case 4:
				map.put("chargeCategory", "商管部");
				break;
			case 5:
				map.put("chargeCategory", "违约金");
				break;
			case 6:
				map.put("chargeCategory", "其它类");
				break;
			case 7:
				map.put("chargeCategory", "押金类");
				break;
			default:
				map.put("chargeCategory", "");
				break;
			}

			k = o.getFeeCalType() == null ? -1 : o.getFeeCalType().intValue();
			switch (k) {
			case 1:
				map.put("feeCalType", "按收费面积计算--管理费");
				break;
			case 2:
				map.put("feeCalType", "按附加面积计算--花园管理费");
				break;
			case 3:
				map.put("feeCalType", "按个数计算--车位租用");
				break;
			default:
				map.put("feeCalType", "");
				break;
			}

			k = o.getIsBreach().intValue();
			switch (k) {
			case 0:
				map.put("feeCalType", "不产生违约金");
				break;
			case 1:
				map.put("feeCalType", "产生违约金");
				break;
			default:
				map.put("feeCalType", "");
				break;
			}

			k = o.getPayDate().intValue();
			switch (k) {
			case 0:
				map.put("payDate", "一次性事务收费");
				break;
			case 1:
				map.put("payDate", "每月1号");
				break;
			case 25:
				map.put("payDate", "");
				break;
			case 28:
				map.put("payDate", "");
				break;
			case 31:
				map.put("payDate", "下月1日");
				break;
			case 32:
				map.put("payDate", "");
				break;
			default:
				map.put("payDate", "");
				break;
			}

			dataList.add(map);
		});
		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}
		return res;
	}

	@SuppressWarnings("unchecked")
	public IResponse exportAccountInfo(HttpServletResponse response) throws ParseException {
		ExportExcelRes res = new ExportExcelRes();
		StringBuilder hql = new StringBuilder("select distinct a from CommunityEstateEntity a where state=1 ");
		hql.append(" and (a.reservedField not like'%\"accountCustomerId\":\"\",\"accountContractNo\":\"\"%') "
				+ "and a.reservedField like'%accountCustomerId%'");
		List<CommunityEstateEntity> list = communityEstateDao.getListByHql(hql.toString(), "");

		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("unitCode", "单元编号");
		heardMap.put("bankName", "划帐银行");
		heardMap.put("accountCustomerId", "客户编号");
		heardMap.put("bankNo", "付款人开户行行号");
		heardMap.put("bankAccount", "银行帐户");
		heardMap.put("accountName", "帐号名");
		heardMap.put("accountContractNo", "存量协议号");
		heardMap.put("accountContractTime", "签约生效时间");
		heardMap.put("accountContractComment", "银行签约备注");

		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		ObjectMapper mapper = new ObjectMapper();
		Map<String, String> bankNoMap = null;
		try {
			bankNoMap = mapper.readValue(communityContextInfo.bankNo, Map.class);
		} catch (JsonMappingException e1) {
			e1.printStackTrace();
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}
		for (CommunityEstateEntity o : list) {
			Map<String, Object> map = new HashMap<String, Object>();
			ReservedFieldVo reservedField = null;
			if (StringUtils.isNotEmpty(o.getReservedField())) {
				try {
					reservedField = mapper.readValue(o.getReservedField(), ReservedFieldVo.class);
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			} else {
				reservedField = new ReservedFieldVo();
			}
			map.put("unitCode", o.getUnitCode());
			String bankName = null != o.getPaymentAccount() ? o.getPaymentAccount().getBankName() : "";
			if (bankName.contains("（")) {
				bankName = bankName.split("（")[0];
			}
			map.put("bankName", bankName);
			map.put("accountCustomerId", null != reservedField ? reservedField.getAccountCustomerId() : "");
			map.put("bankNo", bankNoMap.containsKey(bankName) ? bankNoMap.get(bankName) : "");
			map.put("bankAccount", null != o.getPaymentAccount() ? o.getPaymentAccount().getBankAccount() : "");
			map.put("accountName", null != o.getPaymentAccount() ? o.getPaymentAccount().getAccountName() : "");
			map.put("accountContractNo",
					null != reservedField.getAccountContractNo() ? reservedField.getAccountContractNo() : "");
			map.put("accountContractTime",
					null != reservedField.getAccountContractTime() ? reservedField.getAccountContractTime() : "");
			map.put("accountContractComment",
					null != reservedField.getAccountContractComment() ? reservedField.getAccountContractComment() : "");

			dataList.add(map);
		}
		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}
		return res;
	}

	public IResponse exportCalTotalAllocation(ExportExcelReq req, HttpServletResponse response) throws ParseException {
		ExportExcelRes res = new ExportExcelRes();
		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("meterName", "表名称");
		heardMap.put("meterCode", "表编号");
		heardMap.put("payItemsName", "分摊项目");
		heardMap.put("unitPrice", "分摊单价");
		heardMap.put("allocationDate", "分摊日期");
		heardMap.put("allocationNum", "分摊总数");
		heardMap.put("additionalAmount", "分摊表附加用量");
		heardMap.put("allocationAmount", "分摊总金额");
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();

		Map<Integer, MeterRecordVo> resultList = new TreeMap<>();
		if (StringUtils.isNotEmpty(req.getAllocationDate())) {
			LocalDate allocationDate = LocalDate.parse(req.getAllocationDate(),
					DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			StringBuilder hql = new StringBuilder("from CommunityMeterAllocationEntity a where a.allocationYear="
					+ allocationDate.getYear() + " and a.allocationMonth=" + allocationDate.getMonthValue())
					.append(null != req.getMeterIds() && req.getMeterIds().length > 0
							? " and  a.meter.id in(" + StringUtils.join(req.getMeterIds(), ",") + ")"
							: "");

			List<CommunityMeterAllocationEntity> list = communityMeterAllocationDao.getListByHql(hql.toString(),"");
			list.forEach(o -> {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("additionalAmount", o.getAdditionalAmount().setScale(2, RoundingMode.HALF_UP).toString());
				map.put("allocationAmount", o.getAllocationAmount().setScale(2, RoundingMode.HALF_UP).toString());
				map.put("allocationDate",DateUtil.formatShortFormat(o.getAllocationDate()));
				map.put("allocationNum", o.getAllocationNum().setScale(2, RoundingMode.HALF_UP).toString());
				CommunityMeterEntity meter = o.getMeter();
				if (null != meter) {
					map.put("meterCode", meter.getMeterCode());
					map.put("meterName", meter.getMeterName());
					map.put("payItemsName", meter.getPayItemsName());
					map.put("unitPrice", o.getUnitPrice().setScale(8, RoundingMode.HALF_UP).toString());
				}
				dataList.add(map);
			});
		}else {
			if (req.getMeterIds().length > 0) {
				for (Integer meterId : req.getMeterIds()) {
					resultList.put(meterId, CommunityCache.allocationList.get(meterId));
				}
			} else {
				resultList = CommunityCache.allocationList;
			}
			for (Integer key : resultList.keySet()) {
				MeterRecordVo vo = resultList.get(key);
				if (null != vo) {
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("meterName", vo.getMeterName());
					map.put("payItemsName", vo.getPayItemsName());
					map.put("meterCode", vo.getMeterCode());
					map.put("unitPrice", vo.getUnitPrice().setScale(8, RoundingMode.HALF_UP).toString());
					map.put("allocationDate", (LocalDate.of(vo.getAllocationEndYear(), vo.getAllocationEndMonth(), 15)
							.with(TemporalAdjusters.lastDayOfMonth()).toString()));
					map.put("allocationNum", vo.getAllocationNum().setScale(2, RoundingMode.HALF_UP).toString());
					map.put("additionalAmount", vo.getAdditionalAmount().setScale(2, RoundingMode.HALF_UP).toString());
					map.put("allocationAmount", vo.getAllocationAmount().setScale(2, RoundingMode.HALF_UP).toString());
					dataList.add(map);
				}
			}
		} 

		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

		return res;

//		
//		Map<String, Object> parameterMap = new HashMap<String, Object>();
//		parameterMap.put("meterIds", req.getMeterIds());
//		parameterMap.put("requestPage", 1);
//		parameterMap.put("pageSize", 5000);
//		
//		String jsonstr = JSONObject.valueToString(parameterMap);
//		String postResult="";
//		try {
//			postResult = HttpClientUtil.jsonPost(communityContextInfo.getCalTotalAllocationUrl(), "UTF-8", jsonstr,
//					null);
//		} catch (Exception e) {
//			throw new RuntimeException(e);
//		}
//		
//		ObjectMapper mapper = new ObjectMapper();
//		CalTotalAllocationRes calTotalAllocationRes = null;
//		try {
//			calTotalAllocationRes = mapper.readValue(postResult, CalTotalAllocationRes.class);
//		} catch (JsonMappingException e) {
//			e.printStackTrace();
//		} catch (JsonProcessingException e) {
//			e.printStackTrace();
//		}
//		if(calTotalAllocationRes !=null && calTotalAllocationRes.getRet().equals(ResponseContext.RES_SUCCESS_CODE) ) {
//	        Map<String,String> heardMap = new LinkedHashMap<String,String>();
//	        heardMap.put("meterName", "表名称");
//			heardMap.put("payItemsName", "分摊项目");
//			heardMap.put("unitPrice", "分摊单价");
//			heardMap.put("allocationDate", "分摊日期");
//			heardMap.put("allocationNum", "分摊总数");
//			heardMap.put("additionalAmount", "分摊表附加用量");
//			heardMap.put("allocationAmount", "分摊总金额");
//			List<Map<String,Object>> dataList = new ArrayList<Map<String,Object>>();
//			calTotalAllocationRes.getAllocationList().forEach(o->{
//				Map<String,Object> map = new HashMap<String,Object>();
//				map.put("meterName", o.getMeterName());
//				map.put("payItemsName", o.getPayItemsName());
//				map.put("unitPrice", o.getUnitPrice().toString());
//				map.put("allocationDate", o.getAllocationDate());
//				map.put("allocationNum", o.getAllocationNum().toString());
//				map.put("additionalAmount", o.getAdditionalAmount().toString());
//				map.put("allocationAmount", o.getAllocationAmount().toString());
//				dataList.add(map);
//			});
//			
//	     	try {
//				ExcelExportUtil.exportExcelFile (heardMap,dataList,
//						Short.valueOf("14"),Short.valueOf("500"),null,"","","",
//						 response);
//			} catch (Exception e) {
//				e.printStackTrace();
//	            res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
//	            res.setRetInfo("导出异常！");
//			}
//		}else if(calTotalAllocationRes !=null){
//			res.setRet(calTotalAllocationRes.getRet());
//			res.setRetInfo(calTotalAllocationRes.getRetInfo());
//		}else {
//			res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
//		}
//		return res;
	}

	public IResponse exportCalItemAllocation(ExportExcelReq req, HttpServletResponse response) throws ParseException {
		ExportExcelRes res = new ExportExcelRes();
		List<AllocationItemVo> allocationItemCache = CommunityMeterAllocationServiceImpl.allocationItemCache;
		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("unitCode", "单元编号");
		heardMap.put("payItemsName", "分摊项目");
		heardMap.put("allocationNum", "分摊量");
		heardMap.put("allocationAmount", "分摊金额");
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		allocationItemCache.forEach(o -> {
			if (null != o) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("unitCode",
						StringUtils.isNotEmpty(o.getUnitCode().toString()) ? o.getUnitCode().toString() : "");
				map.put("payItemsName",
						StringUtils.isNotEmpty(o.getPayItemsName().toString()) ? o.getPayItemsName().toString() : "");
				map.put("allocationNum", null != o.getAllocationNum() ? o.getAllocationNum().toString() : "");
				map.put("allocationAmount", null != o.getAllocationAmount() ? o.getAllocationAmount().toString() : "");
				dataList.add(map);
			}
		});

		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

//		
//		Map<String, Object> parameterMap = new HashMap<String, Object>();
//		parameterMap.put("meterIds", req.getMeterIds());
//		parameterMap.put("requestPage", 1);
//		parameterMap.put("pageSize", 5000);
//		
//		String jsonstr = JSONObject.valueToString(parameterMap);
//		String postResult="";
//		try {
//			postResult = HttpClientUtil.jsonPost(communityContextInfo.getCalItemAllocationUrl(), "UTF-8", jsonstr,
//					null);
//		} catch (Exception e) {
//			throw new RuntimeException(e);
//		}
//		
//		ObjectMapper mapper = new ObjectMapper();
//		CalTotalAllocationRes calTotalAllocationRes = null;
//		try {
//			calTotalAllocationRes = mapper.readValue(postResult, CalTotalAllocationRes.class);
//		} catch (JsonMappingException e) {
//			e.printStackTrace();
//		} catch (JsonProcessingException e) {
//			e.printStackTrace();
//		}
//		if(calTotalAllocationRes !=null && calTotalAllocationRes.getRet().equals(ResponseContext.RES_SUCCESS_CODE) ) {
//	        Map<String,String> heardMap = new LinkedHashMap<String,String>();
//	        heardMap.put("unitCode", "单元编号");
//	        heardMap.put("payItemsName", "分摊项目");
//			heardMap.put("allocationNum", "分摊量");
//			heardMap.put("allocationAmount", "分摊金额");
//			List<Map<String,Object>> dataList = new ArrayList<Map<String,Object>>();
//			calTotalAllocationRes.getAllocationItemList().forEach(o->{
//				if(null != o) {
//					Map<String,Object> map = new HashMap<String,Object>();
//					map.put("unitCode",StringUtils.isNotEmpty(o.getUnitCode().toString())? o.getUnitCode().toString():"");
//					map.put("payItemsName",StringUtils.isNotEmpty(o.getPayItemsName().toString())? o.getPayItemsName().toString():"");
//					map.put("allocationNum", null!=o.getAllocationNum() ? o.getAllocationNum().toString():"");
//					map.put("allocationAmount", null!=o.getAllocationAmount() ? o.getAllocationAmount().toString():"");
//					dataList.add(map);
//				}
//			});
//			
//	     	try {
//				ExcelExportUtil.exportExcelFile (heardMap,dataList,
//						Short.valueOf("14"),Short.valueOf("500"),null,"","","",
//						 response);
//			} catch (Exception e) {
//				e.printStackTrace();
//	            res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
//	            res.setRetInfo("导出异常！");
//			}
//		}else if(calTotalAllocationRes !=null){
//			res.setRet(calTotalAllocationRes.getRet());
//			res.setRetInfo(calTotalAllocationRes.getRetInfo());
//		}else {
//			res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
//		}
		return res;
	}

	public String getPayItemsSql(CommunityReceiptReq req) {
		StringBuilder receivablesSql = new StringBuilder(
				"SELECT * from (SELECT DISTINCT pi1.* FROM t_community_receivables a  "
						+ "INNER JOIN t_community_receipt_receivables b ON b.receivablesId=a.id INNER JOIN t_community_receipt c "
						+ "ON c.id=b.receiptId INNER JOIN t_community_property d ON c.estateId=d.id "
						+ "INNER JOIN t_community_pay_items pi1 on pi1.id=a.payItemId INNER JOIN `t_community_building` f "
						+ "ON f.id=d.`buildingId` where 1=1");
		receivablesSql.append(StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())
				? " and c.receiptDate>='" + req.getStartTime() + "' and c.receiptDate<='" + req.getEndTime() + "'"
				: "")
				.append(StringUtils.isNotEmpty(req.getReceiptCode())
						? " and c.receiptCode like'%" + req.getReceiptCode() + "%'"
						: "")
				.append(null != req.getPropertyId() ? " and d.id=" + req.getPropertyId() : "")
				.append(StringUtils.isNotEmpty(req.getStartTime()) && !StringUtils.isNotEmpty(req.getEndTime())
						? " and c.receiptDate>='" + req.getStartTime() + "'"
						: "")
				.append(!StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())
						? " and c.receiptDate<='" + req.getEndTime() + "'"
						: "")
				.append(StringUtils.isNotEmpty(req.getPaymentMethodList())? 
						" and c.paymentMethod in('"+req.getPaymentMethodList().replaceAll(",", "','")+"')":"")
				.append(StringUtils.isNotEmpty(req.getChargeCategoryList())
						? " and pi1.chargeCategory in(" + req.getChargeCategoryList() + ")": "")
				.append(null != req.getFeeType() ? " and c.feeType=" + req.getFeeType() : "")
				.append(null != req.getState() ? " and c.state=" + req.getState()
						: " and c.state=" + EntityContext.RECORD_STATE_VALID)
				.append(StringUtils.isNotEmpty(req.getDistrictIdList()) ? " and f.districtId in("+req.getDistrictIdList()+")":"")
				.append(StringUtils.isNotEmpty(req.getBuildingIdList()) ? " and f.id in("+req.getBuildingIdList()+")":"")
				.append(" group by pi1.itemsName ");

		receivablesSql.append(" UNION SELECT DISTINCT pi2.* FROM t_community_receivables q  "
				+ "INNER JOIN t_community_receipt_receivables rr ON rr.`receivablesId`=q.id INNER JOIN "
				+ "t_community_receipt cr ON cr.id=rr.`receiptId` INNER JOIN t_community_receivables_changes w "
				+ "ON w.receiptId=cr.id INNER JOIN t_community_property e ON e.id=w.estateId "
				+ "INNER JOIN t_community_pay_items pi2 on pi2.id=q.payItemId "
				+ " INNER JOIN `t_community_building` cb ON cb.id=e.`buildingId`  where w.state=1 ")
				.append(StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())
						? " and w.changeDate>='" + req.getStartTime() + "' and w.changeDate<='" + req.getEndTime() + "'"
						: "")
				.append(null != req.getPropertyId() ? " and e.id=" + req.getPropertyId() : "")
				.append(StringUtils.isNotEmpty(req.getStartTime()) && !StringUtils.isNotEmpty(req.getEndTime())
						? " and w.changeDate>='" + req.getStartTime() + "'"
						: "")
				.append(!StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())
						? " and w.changeDate<='" + req.getEndTime() + "'"
						: "")
				.append(StringUtils.isNotEmpty(req.getPaymentMethodList())? 
						" and cr.paymentMethod in('"+req.getPaymentMethodList().replaceAll(",", "','")+"')":"")
				.append(StringUtils.isNotEmpty(req.getChargeCategoryList())
						? " and pi2.chargeCategory in(" + req.getChargeCategoryList() + ")": "")
				.append(StringUtils.isNotEmpty(req.getDistrictIdList()) ? " and cb.districtId in("+req.getDistrictIdList()+")":"")
				.append(StringUtils.isNotEmpty(req.getBuildingIdList()) ? " and cb.id in("+req.getBuildingIdList()+")":"")
				.append(" and w.changeType=2 group by pi2.itemsName ) t group by t.itemsName ");
		return receivablesSql.toString();
	}
	
	public String getReceiptHql(CommunityReceiptReq req) {
		StringBuilder hql = new StringBuilder(
				"select distinct a from CommunityReceiptEntity a inner join a.estate b inner join b.building c inner join c.district d"
				+ " inner join a.receiptReceivablesList rr  inner join rr.receivables r  inner join r.payItem p  where 1=1 ");
		hql.append(StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())
				? " and a.receiptDate>='" + req.getStartTime() + "' and a.receiptDate<='" + req.getEndTime() + "'"
				: "")
				.append(StringUtils.isNotEmpty(req.getReceiptCode())
						? " and a.receiptCode like'%" + req.getReceiptCode() + "%'"
						: "")
				.append(null != req.getPropertyId() ? " and b.id=" + req.getPropertyId() : "")
				.append(StringUtils.isNotEmpty(req.getStartTime()) && !StringUtils.isNotEmpty(req.getEndTime())
						? " and a.receiptDate>='" + req.getStartTime() + "'"
						: "")
				.append(!StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())
						? " and a.receiptDate<='" + req.getEndTime() + "'"
						: "")
				.append(null != req.getFeeType() ? " and a.feeType=" + req.getFeeType() : "")
				.append(null != req.getState() ? " and a.state=" + req.getState()
						: " and a.state=" + EntityContext.RECORD_STATE_VALID)
				.append(StringUtils.isNotEmpty(req.getChargeCategoryList())
						? " and p.chargeCategory in(" + req.getChargeCategoryList() + ")": "")
				.append(StringUtils.isNotEmpty(req.getPaymentMethodList()) ? " and a.paymentMethod in('"
						+req.getPaymentMethodList().replaceAll(",", "','")+"')":"")
				.append(StringUtils.isNotEmpty(req.getBuildingIdList()) ? " and c.id in(" + req.getBuildingIdList() + ")"
						: "")
				.append(StringUtils.isNotEmpty(req.getDistrictIdList()) ? " and d.id in(" + req.getDistrictIdList() + ")"
						: "");
		
		hql.append(null != req.getPropertyId() ? " and b.id=" + req.getPropertyId() : "");
		hql.append(" ORDER BY a.receiptDate  asc");
		return hql.toString();
	}
	
	public String getCommunityReceivablesChangesHql(CommunityReceiptReq req) {
		StringBuilder hql = new StringBuilder(
				"select distinct w from CommunityReceivablesChangesEntity w inner join w.receivables r inner join r.payItem p inner join w.estate e "
				+ "inner join e.building c inner join c.district d ");
		
		hql.append(StringUtils.isNotEmpty(req.getPaymentMethodList()) ? " inner join w.receipt f where   f.paymentMethod in('"
				+req.getPaymentMethodList().replaceAll(",", "','")+"')":"  where w.state=1  ")
			.append(StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())
				? " and w.changeDate>='" + req.getStartTime() + "' and w.changeDate<='" + req.getEndTime() + "'"
				: "").append(null != req.getPropertyId() ? " and e.id=" + req.getPropertyId() : "")
				.append(StringUtils.isNotEmpty(req.getStartTime()) && !StringUtils.isNotEmpty(req.getEndTime())
						? " and w.changeDate>='" + req.getStartTime() + "'"
						: "")
				.append(!StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())
						? " and w.changeDate<='" + req.getEndTime() + "'"
						: "")
				.append(" and w.changeType=2 ")
				.append(StringUtils.isNotEmpty(req.getChargeCategoryList())
						? " and p.chargeCategory in(" + req.getChargeCategoryList() + ")": "")
				.append(StringUtils.isNotEmpty(req.getDistrictIdList()) ? " and d.id in(" + req.getDistrictIdList() + ")"
						: "");
		return hql.toString();
	}
	
	public IResponse exportReceipt(CommunityReceiptReq req, HttpServletResponse response) throws ParseException {
		ExportExcelRes res = new ExportExcelRes();
		Short rowHeight = 300;
		// 创建工作簿
		HSSFWorkbook wb = new HSSFWorkbook();
		// 创建工作表
		HSSFSheet wbSheet = wb.createSheet("sheet1");
		// 设置默认行宽
		wbSheet.setDefaultColumnWidth(10);
		// 设置样式
		HSSFCellStyle style = wb.createCellStyle();
		// 设置字体
		HSSFFont font = wb.createFont();
		font.setFontName("宋体");
		font.setFontHeightInPoints((short) 9);
		style.setFont(font);
		HSSFCellStyle style2 = wb.createCellStyle();
		style2.setFont(font);
		style2.setAlignment(HorizontalAlignment.RIGHT);
		// 加粗
		// font.setBold(true);
		// 在第1行创建rows
		HSSFRow row = wbSheet.createRow((int) 0);
		row.setHeight(rowHeight);
		// 设置列头元素
		HSSFCell cellHead = null;
		Object userObj = getPrincipal(true);
		if (null!=userObj && userObj instanceof UserEntity) {
			UserEntity user = (UserEntity) userObj;
			if(StringUtils.isNotEmpty(user.getDistrictIds())) {
				req.setDistrictIdList(user.getDistrictIds());
				req.setReloadData(1);
			}
		}
		List<CommunityPayItemsEntity> payItemsList = communityPayItemsDao
				.getListBySql(getPayItemsSql(req), "");
		Map<String, Integer> payItemMap = new LinkedHashMap<String, Integer>();
		List<CommunityReceiptEntity> list = communityReceiptDao.getListByHql(getReceiptHql(req), "");
		
		Map<String, List<CommunityReceiptEntity>> collect = (Map<String, List<CommunityReceiptEntity>>) list.parallelStream()
				.collect(groupingBy(CommunityReceiptEntity::getPaymentMethod));
		cellHead = row.createCell(0);
		cellHead.setCellValue("收款方式");
		cellHead.setCellStyle(style);
		cellHead = row.createCell(1);
		cellHead.setCellValue("楼盘名称");
		cellHead = row.createCell(2);
		cellHead.setCellValue("日期_天");
		cellHead = row.createCell(3);
		cellHead.setCellValue("单元编号");
		cellHead = row.createCell(4);
		cellHead.setCellValue("姓名");
		cellHead = row.createCell(5);
		cellHead.setCellValue("单据编号");
		cellHead = row.createCell(6);
		cellHead.setCellValue("经办人");
		cellHead = row.createCell(7);
		cellHead.setCellValue("备注");
		int columnNum = 7;
		Map<Integer,Integer> chargeCategoryMap = new HashMap<>();
		if (null != payItemsList) {
			for (Integer i = 0; i < payItemsList.size(); i++) {
				columnNum = columnNum + 1;
				CommunityPayItemsEntity o = payItemsList.get(i);
				payItemMap.put(o.getItemsName(), columnNum);
				cellHead = row.createCell(columnNum);
				cellHead.setCellValue(o.getItemsName());
				if(StringUtils.isNotEmpty(req.getChargeCategoryList())) {
					chargeCategoryMap.put(o.getChargeCategory(), o.getChargeCategory());
				}
			}
		}
		columnNum = columnNum + 1;
		cellHead = row.createCell(columnNum);
		cellHead.setCellValue("合计");
		Map<String, BigDecimal> totalMap = new HashMap<>();
		BigDecimal total = new BigDecimal(0);
		int rowNum = 0;
		HSSFCellStyle fontStyle = wb.createCellStyle();
		fontStyle.setFont(font);
		fontStyle.setVerticalAlignment(VerticalAlignment.TOP);
		for(String key : collect.keySet()){
			List<CommunityReceiptEntity> l = collect.get(key);
			Collections.sort(l, (p1, p2) -> ((CommunityReceiptEntity) p1).getEstate().getBuilding().getDistrict().getDistrictOrder()
					.compareTo(((CommunityReceiptEntity) p2).getEstate().getBuilding().getDistrict().getDistrictOrder()));
			int firstRow =rowNum+1;
			Map<String, BigDecimal> paymentMethodTotalMap = new HashMap<>();
			for (CommunityReceiptEntity o : l) {
				if (null != o) {
					rowNum = rowNum + 1;
					row = wbSheet.createRow((int) rowNum);
					BigDecimal currentAmountTotal = new BigDecimal("0");
					cellHead = row.createCell(0);
					cellHead.setCellValue(o.getPaymentMethod());
					cellHead.setCellStyle(fontStyle);
					cellHead = row.createCell(1);
					cellHead.setCellValue(null != o.getEstate() && null != o.getEstate().getBuilding()
							&& null != o.getEstate().getBuilding().getDistrict()
									? o.getEstate().getBuilding().getDistrict().getDistrictName()
									: "");
					cellHead.setCellStyle(style);
					cellHead = row.createCell(2);
					cellHead.setCellValue(null != o.getReceiptDate() ? DateUtil.formatLongFormat(o.getReceiptDate()) : "");
					cellHead = row.createCell(3);
					cellHead.setCellValue(null != o.getEstate() ? o.getEstate().getUnitCode() : "");
					cellHead = row.createCell(4);
					cellHead.setCellValue(o.getPayerName());
					cellHead = row.createCell(5);
					cellHead.setCellValue(o.getReceiptCode());
					cellHead = row.createCell(6);
					cellHead.setCellValue(o.getAgent());
					cellHead = row.createCell(7);
					cellHead.setCellValue(o.getComment());
					Map<String,BigDecimal> itemMap = new HashMap<>();
					for (CommunityReceiptReceivablesEntity p : o.getReceiptReceivablesList()) {
						Integer chargeCategory = p.getReceivables().getPayItem().getChargeCategory();
						if(chargeCategoryMap.size()==0 || chargeCategoryMap.containsKey(chargeCategory)) {
							BigDecimal currentAmount = null != p.getCurrentAmount() ? p.getCurrentAmount() : new BigDecimal(0);
							String itemsName = p.getReceivables().getPayItem().getItemsName();
							itemMap.put(itemsName, itemMap.containsKey(itemsName) ? itemMap.get(itemsName).add(currentAmount) : currentAmount);

							cellHead = row.createCell(payItemMap.get(itemsName));
							cellHead.setCellValue(itemMap.get(itemsName).toString());
							cellHead.setCellStyle(style2);
							currentAmountTotal = currentAmountTotal.add(currentAmount);
							if (totalMap.containsKey(itemsName)) {
								BigDecimal t = totalMap.get(itemsName).add(currentAmount);
								totalMap.put(itemsName, t);
							} else {
								totalMap.put(itemsName, currentAmount);
							}
							if (paymentMethodTotalMap.containsKey(itemsName)) {
								BigDecimal t = paymentMethodTotalMap.get(itemsName).add(currentAmount);
								paymentMethodTotalMap.put(itemsName, t);
							} else {
								paymentMethodTotalMap.put(itemsName, currentAmount);
							}
						}
						cellHead = row.createCell(columnNum);
						cellHead.setCellValue(currentAmountTotal.toString());
						cellHead.setCellStyle(style2);
						total = total.add(currentAmountTotal);
					}
				}
			}
			rowNum = rowNum + 1;
			row = wbSheet.createRow((int) rowNum);
			cellHead = row.createCell(1);
			cellHead.setCellValue("合计");
			BigDecimal currentAmountTotal = new BigDecimal("0");
			for (String k : paymentMethodTotalMap.keySet()) {
				cellHead = row.createCell(payItemMap.get(k));
				cellHead.setCellValue(paymentMethodTotalMap.get(k).toString());
				cellHead.setCellStyle(style2);
				currentAmountTotal = currentAmountTotal.add(new BigDecimal(paymentMethodTotalMap.get(k).toString()));
			}
			cellHead = row.createCell(columnNum);
			cellHead.setCellValue(currentAmountTotal.toString());
			cellHead.setCellStyle(style2);
			if(firstRow<rowNum && firstRow!=rowNum) {
  	            CellRangeAddress region = new CellRangeAddress(firstRow, rowNum, 0, 0);
                wbSheet.addMergedRegion(region);
                region = new CellRangeAddress(rowNum, rowNum, 1, 7);
                wbSheet.addMergedRegion(region);
			}
		}

		List<CommunityReceivablesChangesEntity> receivablesChangesList = communityReceivablesChangesDao
				.getListByHql(getCommunityReceivablesChangesHql(req), "");
		for (CommunityReceivablesChangesEntity o : receivablesChangesList) {
			rowNum = rowNum + 1;
			row = wbSheet.createRow((int) rowNum);
			BigDecimal currentAmountTotal = new BigDecimal("0");
			/*
			 * if(null!= o.getRefundMethod()) { switch(o.getRefundMethod()) { case 0:
			 * cellHead = row.createCell(0); cellHead.setCellValue("现金"); break; case 1:
			 * cellHead = row.createCell(0); cellHead.setCellValue("银行转账"); break; case 3:
			 * cellHead = row.createCell(0); cellHead.setCellValue("在线支付原路退还"); break;
			 * default: cellHead = row.createCell(0); cellHead.setCellValue("其它"); break; }
			 * }else { cellHead = row.createCell(0); cellHead.setCellValue(""); }
			 */
			cellHead = row.createCell(0);
			cellHead.setCellValue(StringUtils.isNotEmpty(o.getRefundMethodName()) ? o.getRefundMethodName()
					: o.getRefundMethodName());
			cellHead.setCellStyle(style);
			cellHead = row.createCell(1);
			cellHead.setCellValue(null != o.getEstate() && null != o.getEstate().getBuilding()
					&& null != o.getEstate().getBuilding().getDistrict()
							? o.getEstate().getBuilding().getDistrict().getDistrictName()
							: "");
			cellHead = row.createCell(2);
			cellHead.setCellValue(null != o.getChangeDate() ? DateUtil.formatLongFormat(o.getChangeDate()) : "");
			cellHead = row.createCell(3);
			cellHead.setCellValue(null != o.getEstate() ? o.getEstate().getUnitCode() : "");
			cellHead = row.createCell(4);
			cellHead.setCellValue(StringUtils.isNotEmpty(o.getRefundPayee()) ? o.getRefundPayee() : "");
			cellHead = row.createCell(5);
			cellHead.setCellValue(o.getReceipt().getReceiptCode());
			cellHead = row.createCell(6);
			cellHead.setCellValue(o.getRecorder());
			cellHead = row.createCell(7);
			cellHead.setCellValue(o.getComment());
			BigDecimal currentAmount = null != o.getChangeAmount() ? o.getChangeAmount().negate() : new BigDecimal(0);
			// Integer id = o.getReceivables().getPayItem().getId();
			// 退款都是一次性收费，一张收据只有一个收费项目,故先默认取第一条,如果后续业务有变,代码需要调整
			String itemsName = o.getReceipt().getReceiptReceivablesList().get(0).getReceivables().getPayItem().getItemsName();

			cellHead = row.createCell(payItemMap.get(itemsName));
			cellHead.setCellValue(currentAmount.toString());
			cellHead.setCellStyle(style2);
			currentAmountTotal = currentAmountTotal.add(currentAmount);
			if (totalMap.containsKey(itemsName)) {
				BigDecimal t = totalMap.get(itemsName).add(currentAmount);
				totalMap.put(itemsName, t);
			} else {
				totalMap.put(itemsName, currentAmount);
			}
			cellHead = row.createCell(columnNum);
			cellHead.setCellValue(currentAmountTotal.toString());
			cellHead.setCellStyle(style2);
			total = total.add(currentAmountTotal);
		}
		rowNum = rowNum + 1;
		row = wbSheet.createRow((int) rowNum);
		cellHead = row.createCell(0);
		cellHead.setCellValue("合计");
		cellHead.setCellStyle(style);
		for (String key : totalMap.keySet()) {
			cellHead = row.createCell(payItemMap.get(key));
			cellHead.setCellValue(totalMap.get(key).toString());
			cellHead.setCellStyle(style2);
		}
		cellHead = row.createCell(columnNum);
		cellHead.setCellValue(total.toString());
		cellHead.setCellStyle(style2);
		try {
			ExcelExportUtil.export(response, wb, "zhyl(" + req.getStartTime() + "-" + req.getEndTime() + ")");
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}
		return res;
	}
	
	@SuppressWarnings("unchecked")
	public IResponse getReceiptView(CommunityReceiptReq req) {
		GetReceiptViewRes res = new GetReceiptViewRes(); 
		Object userObj = getPrincipal(true);
		if (null!=userObj && userObj instanceof UserEntity) {
			UserEntity user = (UserEntity) userObj;
			if(StringUtils.isNotEmpty(user.getDistrictIds())) {
				req.setDistrictIdList(user.getDistrictIds());
				req.setReloadData(1);
			}

		}
		if(null == req.getReloadData() || null != req.getReloadData() && 1 == req.getReloadData()) {
			keyList.clear();
			viewList.clear();
			Map<String, String> heardMap = new LinkedHashMap<String, String>();
			Map<String, String> keyMap = new HashMap<String, String>();
			keyMap.put("key", "paymentMethod");
			keyMap.put("name", "收款方式");
			keyList.add(keyMap);
			heardMap.put("paymentMethod", "");
			keyMap = new HashMap<String, String>();
			keyMap.put("key", "districtName");
			keyMap.put("name", "楼盘名称");
			keyList.add(keyMap);
			heardMap.put("districtName", "");
			keyMap = new HashMap<String, String>();
			keyMap.put("key", "receiptDate");
			keyMap.put("name", "日期");
			keyList.add(keyMap);
			heardMap.put("receiptDate", "");
			keyMap = new HashMap<String, String>();
			keyMap.put("key", "unitCode");
			keyMap.put("name", "单元编号");
			keyList.add(keyMap);
			heardMap.put("unitCode", "");
			keyMap = new HashMap<String, String>();
			keyMap.put("key", "payerName");
			keyMap.put("name", "姓名");
			keyList.add(keyMap);
			heardMap.put("payerName", "");
//			res.getKeyList().add("payerName");
//			heardMap.put("payerName", "");
			keyMap = new HashMap<String, String>();
			keyMap.put("key", "receiptCode");
			keyMap.put("name", "单据编号");
			keyList.add(keyMap);
			heardMap.put("receiptCode", "");
			keyMap = new HashMap<String, String>();
			keyMap.put("key", "agent");
			keyMap.put("name", "经办人");
			keyList.add(keyMap);
			heardMap.put("agent", "");
			keyMap = new HashMap<String, String>();
			keyMap.put("key", "comment");
			keyMap.put("name", "备注");
			keyList.add(keyMap);
			heardMap.put("comment", "");
//			List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao
//					.getListBySql(getReceivablesSql(req), "");
			List<CommunityPayItemsEntity> payItemsList = communityPayItemsDao
					.getListBySql(getPayItemsSql(req), "");
			Map<String, String> payItemMap = new LinkedHashMap<String, String>();
			Map<Integer,Integer> chargeCategoryMap = new HashMap<>();
			if (null != payItemsList) {
				for (Integer i = 0; i < payItemsList.size(); i++) {
					CommunityPayItemsEntity o = payItemsList.get(i);
					payItemMap.put(o.getItemsName(), "itemsName"+i);
					keyMap = new HashMap<String, String>();
					keyMap.put("key", "itemsName"+i);
					keyMap.put("name", o.getItemsName());
					keyList.add(keyMap);
					heardMap.put("itemsName"+i, "");
					if(StringUtils.isNotEmpty(req.getChargeCategoryList())) {
						chargeCategoryMap.put(o.getChargeCategory(), o.getChargeCategory());
					}
				}
			} 
			keyMap = new HashMap<String, String>();
			keyMap.put("key", "currentAmountTotal");
			keyMap.put("name", "合计");
			keyList.add(keyMap);
			List<CommunityReceiptEntity> list = communityReceiptDao.getListByHql(getReceiptHql(req), "");
			Map<String, BigDecimal> totalMap = new HashMap<>();
			BigDecimal total = new BigDecimal(0);
			int rowNum = 0;
			ObjectMapper mapper = new ObjectMapper();
			Map<String, List<CommunityReceiptEntity>> collect = (Map<String, List<CommunityReceiptEntity>>) list.parallelStream()
					.collect(groupingBy(CommunityReceiptEntity::getPaymentMethod));
			for(String k : collect.keySet()){
				Map<String, BigDecimal> paymentMethodTotalMap = new HashMap<>();
				List<CommunityReceiptEntity> l = collect.get(k);
				Collections.sort(l, (p1, p2) -> ((CommunityReceiptEntity) p1).getEstate().getBuilding().getDistrict().getDistrictOrder()
						.compareTo(((CommunityReceiptEntity) p2).getEstate().getBuilding().getDistrict().getDistrictOrder()));
				for (CommunityReceiptEntity o : l) {
					if (null != o) {
						rowNum = rowNum + 1;
						Map<String, String> map = null;
						try {
							map = mapper.readValue(mapper.writeValueAsString(heardMap), Map.class);
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
						BigDecimal currentAmountTotal = new BigDecimal("0");
						map.put("paymentMethod", o.getPaymentMethod());
						map.put("districtName", null != o.getEstate() && null != o.getEstate().getBuilding()
								&& null != o.getEstate().getBuilding().getDistrict()
								? o.getEstate().getBuilding().getDistrict().getDistrictName()
								: "");
						map.put("receiptDate", null != o.getReceiptDate() ? DateUtil.formatLongFormat(o.getReceiptDate()) : "");
						map.put("unitCode", null != o.getEstate() ? o.getEstate().getUnitCode() : "");
						map.put("payerName", o.getPayerName());
						map.put("receiptCode", o.getReceiptCode());
						map.put("agent", o.getAgent());
						map.put("comment", o.getComment());
						Map<String,BigDecimal> itemMap = new HashMap<>();
						boolean state = false;
						for (CommunityReceiptReceivablesEntity p : o.getReceiptReceivablesList()) {
							Integer chargeCategory = p.getReceivables().getPayItem().getChargeCategory();
							if(chargeCategoryMap.size()==0 || chargeCategoryMap.containsKey(chargeCategory)) {
								state = true;
								BigDecimal currentAmount = null != p.getCurrentAmount() ? p.getCurrentAmount() : new BigDecimal(0);
								String itemsName = p.getReceivables().getPayItem().getItemsName();
								String key = payItemMap.get(itemsName);
								itemMap.put(key, itemMap.containsKey(key) ? itemMap.get(key).add(currentAmount) : currentAmount);
								map.put(key, itemMap.get(key).toString());
								currentAmountTotal = currentAmountTotal.add(currentAmount);
								if (totalMap.containsKey(key)) {
									BigDecimal t = totalMap.get(key).add(currentAmount);
									totalMap.put(key, t);
								} else {
									totalMap.put(key, currentAmount);
								}
								if (paymentMethodTotalMap.containsKey(key)) {
									BigDecimal t = paymentMethodTotalMap.get(key).add(currentAmount);
									paymentMethodTotalMap.put(key, t);
								} else {
									paymentMethodTotalMap.put(key, currentAmount);
								}
							}
						}
						if(state) {
							map.put("currentAmountTotal", currentAmountTotal.toString());
							total = total.add(currentAmountTotal);
							viewList.add(map);
						}
					}
				}
				Map<String, String> map = null;
				try {
					map = mapper.readValue(mapper.writeValueAsString(heardMap), Map.class);
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				BigDecimal currentAmountTotal = new BigDecimal("0");
				map.put("paymentMethod", k);
				map.put("districtName", "合计");
				for (String key : paymentMethodTotalMap.keySet()) {
					currentAmountTotal = currentAmountTotal.add(new BigDecimal(paymentMethodTotalMap.get(key).toString()));
					map.put(key, paymentMethodTotalMap.get(key).toString());
				}
				map.put("currentAmountTotal", currentAmountTotal.toString());
				viewList.add(map);
			}
			List<CommunityReceivablesChangesEntity> receivablesChangesList = communityReceivablesChangesDao
					.getListByHql(getCommunityReceivablesChangesHql(req), "");
			for (CommunityReceivablesChangesEntity o : receivablesChangesList) {
				BigDecimal currentAmountTotal = new BigDecimal("0");
				Map<String, String> map = null;
				try {
					map = mapper.readValue(mapper.writeValueAsString(heardMap), Map.class);
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				map.put("paymentMethod", StringUtils.isNotEmpty(o.getRefundMethodName()) ? o.getRefundMethodName()
						: o.getRefundMethodName());
				map.put("districtName", null != o.getEstate() && null != o.getEstate().getBuilding()
						&& null != o.getEstate().getBuilding().getDistrict()
						? o.getEstate().getBuilding().getDistrict().getDistrictName()
						: "");
				map.put("receiptDate", null != o.getChangeDate() ? DateUtil.formatShortFormat(o.getChangeDate()) : "");
				map.put("unitCode", null != o.getEstate() ? o.getEstate().getUnitCode() : "");
				map.put("payerName", StringUtils.isNotEmpty(o.getRefundPayee()) ? o.getRefundPayee() : "");
				map.put("receiptCode", o.getReceipt().getReceiptCode());
				map.put("agent", o.getRecorder());
				map.put("comment", o.getComment());
				BigDecimal currentAmount = null != o.getChangeAmount() ? o.getChangeAmount().negate() : new BigDecimal(0);
				// Integer id = o.getReceivables().getPayItem().getId();
				// 退款都是一次性收费，一张收据只有一个收费项目,故先默认取第一条,如果后续业务有变,代码需要调整
				String itemsName = o.getReceipt().getReceiptReceivablesList().get(0).getReceivables().getPayItem().getItemsName();
				String key = payItemMap.get(itemsName);
				map.put(key, currentAmount.toString());
				currentAmountTotal = currentAmountTotal.add(currentAmount);
				if (totalMap.containsKey(key)) {
					BigDecimal t = totalMap.get(key).add(currentAmount);
					totalMap.put(key, t);
				} else {
					totalMap.put(key, currentAmount);
				}
				map.put("currentAmountTotal", currentAmountTotal.toString());
				total = total.add(currentAmountTotal);
				viewList.add(map);
			} 
			Map<String, String> map=null;
			try {
				map = mapper.readValue(mapper.writeValueAsString(heardMap), Map.class);
			} catch (JsonMappingException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} catch (JsonProcessingException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			map.put("paymentMethod", "合计");
			for (String key : totalMap.keySet()) {
				map.put(key, totalMap.get(key).toString());
			}
			map.put("currentAmountTotal", total.toString());
			viewList.add(map);
		}
		res.getKeyList().addAll(keyList);
		CommunityPage<Set<Map<String, String>>> page = CommunityCache.getPage(viewList,
				req.getPageSize(), req.getRequestPage());
		res.setCurrentPage(page.getCurrentPage());
		res.setPageSize(page.getPageSize());
		res.setTotal(page.getTotalPage());
		res.setTotalResult(page.getTotalCount());
		res.setViewList(page.getResultList());

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	public IResponse exportMeterAllocationItemList(ExportExcelReq req, HttpServletResponse response){
		ExportExcelRes res = new ExportExcelRes();
		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("meterName", "总表名称");
		heardMap.put("payItemsName", "收费项目");
		heardMap.put("unitCode", "单元编号");
		heardMap.put("allocationNum", "分摊数量");
		heardMap.put("allocationAmount", "分摊金额");
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		LocalDate allocationDate = LocalDate.parse(req.getAllocationDate(),
				DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		StringBuilder hql = new StringBuilder("from CommunityMeterAllocationItemEntity a where a.id.allocationEndYear="
				+ allocationDate.getYear() + " and a.id.allocationEndMonth=" + allocationDate.getMonthValue())
				.append(null != req.getMeterIds() && req.getMeterIds().length > 0
						? " and  a.meterId in(" + StringUtils.join(req.getMeterIds(), ",") + ")"
						: "");
		List<CommunityMeterAllocationItemEntity> list = communityMeterAllocationItemDao.getListByHql(hql.toString(), "");

		list.forEach(o -> {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("meterName", getMeter(o.getMeterId()).getMeterName());
			map.put("payItemsName", o.getPayItemsName());
			map.put("unitCode", o.getId().getUnitCode());
			map.put("allocationNum", o.getAllocationNum());
			map.put("allocationAmount", o.getAllocationAmount());
			dataList.add(map);
		});
		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

		return res;
	}
}
