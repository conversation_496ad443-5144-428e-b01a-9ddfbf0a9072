package com.foshan.service.community;

import com.foshan.form.community.request.CommunityReceiptReceivablesReq;
import com.foshan.form.response.IResponse;

public interface ICommunityReceiptReceivablesService {
    public IResponse getCommunityReceiptReceivablesList(CommunityReceiptReceivablesReq req);
	public IResponse addCommunityReceiptReceivables(CommunityReceiptReceivablesReq req);
	public IResponse modifyCommunityReceiptReceivables(CommunityReceiptReceivablesReq req);
	public IResponse deleteCommunityReceiptReceivables(CommunityReceiptReceivablesReq req);
	public IResponse getCommunityReceiptReceivablesInfo(CommunityReceiptReceivablesReq req);

}

