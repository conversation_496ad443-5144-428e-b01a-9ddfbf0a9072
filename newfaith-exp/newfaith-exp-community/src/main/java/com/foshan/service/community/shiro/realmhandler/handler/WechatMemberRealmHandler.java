package com.foshan.service.community.shiro.realmhandler.handler;

import javax.servlet.http.HttpServletRequest;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.subject.Subject;
import org.springframework.stereotype.Component;
import com.foshan.form.community.request.CommunityMemberLoginReq;
import com.foshan.form.response.IResponse;
import com.foshan.service.community.shiro.realmhandler.AbstractMemberRealmHandler;
import com.foshan.service.community.shiro.realmhandler.RealmHandlerType;


/**
 * 会员微信登陆授权，通过微信网页授权
 * <AUTHOR>
 *
 */
@Component
@RealmHandlerType("3")
public class WechatMemberRealmHandler extends AbstractMemberRealmHandler {

	//private final static Logger logger = LoggerFactory.getLogger(WechatMemberRealmHandler.class);
	
	@Override
	public SimpleAuthenticationInfo handleAuthenticationInfo(AuthenticationToken token, String realmName) {
		return null;
	}

	@Override
	public IResponse memberLogin(CommunityMemberLoginReq req, HttpServletRequest request, Subject curUser) {
		return null;
	}
}
