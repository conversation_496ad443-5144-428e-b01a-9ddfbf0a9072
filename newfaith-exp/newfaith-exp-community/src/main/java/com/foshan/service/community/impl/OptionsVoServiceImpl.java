package com.foshan.service.community.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.entity.community.vo.OptionsVo;
import com.foshan.form.community.OptionsVoForm;
import com.foshan.form.community.request.OptionsVoReq;
import com.foshan.form.community.response.optionsVo.GetOptionsVoList;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.IOptionsVoService;

@Transactional
@Service("optionsVoService")
public class OptionsVoServiceImpl extends GenericCommunityService implements IOptionsVoService {
	
	
	@Override
	public IResponse getCommunityOptionList(OptionsVoReq req) {
		GetOptionsVoList res = new GetOptionsVoList();
		List<OptionsVo> optionsList = optionsVoDao.getListByHql(
				"select new com.foshan.entity.community.vo.OptionsVo(a.id as optionsId,a.communityName as OptionName) "
						+ "from CommunityEntity a  "+(null!=req.getRegionId() ?" where a.region.id="+req.getRegionId():""),"");
		if(null != optionsList) {
			optionsList.forEach(o -> {
				OptionsVoForm optrions = new OptionsVoForm();
				optrions.setOptionName(o.getOptionName());
				optrions.setOptionsId(o.getOptionsId());
				res.getOptionsList().add(optrions);
			});
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@Override
	public IResponse getDistrictOptionList(OptionsVoReq req) {
		GetOptionsVoList res = new GetOptionsVoList();
		List<OptionsVo> optionsList = optionsVoDao.getListByHql(
				"select new com.foshan.entity.community.vo.OptionsVo(a.id as optionsId,a.districtName as OptionName) "
						+ "from CommunityDistrictEntity a where 1=1 "+(null!=req.getCommunityId() ? 
								" and a.community.id="+req.getCommunityId():""),"");
		if(null != optionsList) {
			optionsList.forEach(o -> {
				OptionsVoForm optrions = new OptionsVoForm();
				optrions.setOptionName(o.getOptionName());
				optrions.setOptionsId(o.getOptionsId());
				res.getOptionsList().add(optrions);
			});
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@Override
	public IResponse getAreaOptionList(OptionsVoReq req) {
		GetOptionsVoList res = new GetOptionsVoList();
		List<OptionsVo> optionsList = new ArrayList<OptionsVo>();
/*		if(null!=req.getEstateType() && req.getEstateType()==0) {
			optionsList=optionsVoDao.getListByHql(
			"select new com.foshan.entity.community.vo.OptionsVo(a.id as optionsId,a.area as OptionName) "
					+ "from CommunityResidenceEntity a where 1=1 "+(null!=req.getDistrictd() ? 
							" and a.district.id="+req.getDistrictd():""),"");*/
		//}else if(null!=req.getEstateType() && req.getEstateType()==1){
			optionsList=optionsVoDao.getListByHql(
			"select new com.foshan.entity.community.vo.OptionsVo(a.id as optionsId, a.regionName as OptionName) "
					+ "from RegionEntity a ","");
		//}
		
		if(null != optionsList) {
			optionsList.forEach(o -> {
				OptionsVoForm optrions = new OptionsVoForm();
				optrions.setOptionName(o.getOptionName());
				optrions.setOptionsId(o.getOptionsId());
				res.getOptionsList().add(optrions);
/*				boolean state =true;
				for(OptionsVoForm p : res.getOptionsList()) { 
					if(p.getOptionName().equals(o.getOptionName())) {
						state=false;
					}
				}
				if(state) {
					res.getOptionsList().add(optrions);
				}*/
			});
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse getBuildingNumOptionList(OptionsVoReq req) {
		GetOptionsVoList res = new GetOptionsVoList();
		List<OptionsVo> optionsList = optionsVoDao.getListByHql(
			"select new com.foshan.entity.community.vo.OptionsVo(a.id as optionsId,a.buildingName as OptionName) "
					+ "from CommunityBuildingEntity a where 1=1 "+(null!=req.getDistrictd() ? 
						" and a.district.id="+req.getDistrictd():"") ,"");
		
		if(null != optionsList) {
			optionsList.forEach(o -> {
				OptionsVoForm optrions = new OptionsVoForm();
				optrions.setOptionName(o.getOptionName());
				optrions.setOptionsId(o.getOptionsId());
				res.getOptionsList().add(optrions);
/*				boolean state =true;
				for(OptionsVoForm p : res.getOptionsList()) { 
					if(p.getOptionName().equals(o.getOptionName())) {
						state=false;
					}
				}
				if(state) {
					res.getOptionsList().add(optrions);
				}*/
			});
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@Override
	public IResponse getUnitNumOptionList(OptionsVoReq req) {
		GetOptionsVoList res = new GetOptionsVoList();
		List<OptionsVo> optionsList = new ArrayList<OptionsVo>();
/*		if(null!=req.getEstateType() && req.getEstateType()==0) {
			optionsList=optionsVoDao.getListByHql(
			"select new com.foshan.entity.community.vo.OptionsVo(a.id as optionsId,a.unitNum as OptionName) "
					+ "from CommunityResidenceEntity a where 1=1 "+(null!=req.getDistrictd() ? 
							" and a.district.id="+req.getDistrictd():"")+
					(StringUtils.isNotEmpty(req.getArea()) ? " and a.area like'%"+req.getArea()+"%'":"")+
					(StringUtils.isNotEmpty(req.getBuildingNum()) ? " and a.buildingNum like'%"+req.getBuildingNum()+"%'":""),"");
		}else if(null!=req.getEstateType() && req.getEstateType()==1){
			optionsList=optionsVoDao.getListByHql(
			"select new com.foshan.entity.community.vo.OptionsVo(a.id as optionsId,a.unitNum as OptionName) "
					+ "from CommunityShopEntity a where 1=1 "+(null!=req.getDistrictd() ? 
							" and a.district.id="+req.getDistrictd():"")+
					(StringUtils.isNotEmpty(req.getArea()) ? " and a.area like'%"+req.getArea()+"%'":""),"");
		}*/
		optionsList=optionsVoDao.getListByHql(
		"select new com.foshan.entity.community.vo.OptionsVo(a.id as optionsId, a.unitCode as OptionName) "
				+ "from CommunityEstateEntity a inner join a.building b inner join b.district c where 1=1 "+(null!=req.getDistrictd() ? 
						" and c.id="+req.getDistrictd():""+(null!=req.getBuildingId() ? " and b.id="+req.getBuildingId():"")),"");
		
		if(null != optionsList) {
			optionsList.forEach(o -> {
				OptionsVoForm optrions = new OptionsVoForm();
				optrions.setOptionName(o.getOptionName());
				optrions.setOptionsId(o.getOptionsId());
				res.getOptionsList().add(optrions);
				/*boolean state =true;
				for(OptionsVoForm p : res.getOptionsList()) { 
					if(p.getOptionName().equals(o.getOptionName())) {
						state=false;
					}
				}
				if(state) {
					res.getOptionsList().add(optrions);
				}*/
			});
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
}
