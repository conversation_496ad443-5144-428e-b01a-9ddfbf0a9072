package com.foshan.service.community;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.foshan.entity.community.CommunityEventsEntity;
import com.foshan.form.community.request.CommunityEventsReq;
import com.foshan.form.response.IResponse;

public interface ICommunityEventsService {
	public IResponse getCommunityEventsList(CommunityEventsReq req);
	public IResponse addCommunityEvents(CommunityEventsReq req);
	public IResponse modifyCommunityEvents(CommunityEventsReq req);
	public IResponse deleteCommunityEvents(CommunityEventsReq req);
	public IResponse getCommunityEventsInfo(CommunityEventsReq req);
	public IResponse setEventstate(CommunityEventsReq req);
	public IResponse updateYunzhijiaParameter();
	public IResponse generateConstructionPermit(CommunityEventsReq req ,CommunityEventsEntity event);
	public IResponse departmentAudit(CommunityEventsReq req) ;
	public IResponse setEventsRefund(CommunityEventsReq req);
	public IResponse importEventsRefund(HttpServletRequest request) ;
	public IResponse exportEventsRefund(CommunityEventsReq req, HttpServletResponse response);
	public IResponse initializeItemsAttachment(CommunityEventsReq req);
	public IResponse audit(CommunityEventsReq req) ;
}
