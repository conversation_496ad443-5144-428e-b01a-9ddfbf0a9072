package com.foshan.service.community.impl;

import java.util.Date;
import java.util.concurrent.ThreadLocalRandom;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.community.CommunityUserEntity;
import com.foshan.form.PlatformUserForm;
import com.foshan.form.community.request.CommunityUserReq;
import com.foshan.form.community.request.GetCameraPlayUrlReq;
import com.foshan.form.community.response.communityUser.GetCameraPlayUrlRes;
import com.foshan.form.community.response.communityUser.GetCommunityUserListRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.ICommunityUserService;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;


@Transactional
@Service("communityUserService")
public class CommunityUserServiceImpl extends GenericCommunityService implements ICommunityUserService {

	@Override
	public IResponse getCommunityUserList(CommunityUserReq req) {
		GetCommunityUserListRes res = new GetCommunityUserListRes();
		Page<CommunityUserEntity> page = new Page<CommunityUserEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

		StringBuilder hql = new StringBuilder("select distinct a from CommunityUserEntity a ");
	
		hql.append(null!=req.getUserState() ? " where a.userState="+req.getUserState():" where a.userState=1")
			.append(null!=req.getDepartmentId() ?" and a.department.id="+req.getDepartmentId():"")
			.append(StringUtils.isNotEmpty(req.getName()) ?" and a.name like '%" + req.getName() + "%'" :"")
			.append(StringUtils.isNotEmpty(req.getUserCode()) ?
				" and a.userCode like '%" + req.getUserCode() + "%'" :"");

		page = communityUserDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			PlatformUserForm user = new PlatformUserForm();
			user.setName(o.getName());
			user.setUserCode(user.getUserCode());
			user.setUserId(o.getId());
			user.setUserName(o.getUserName());
			res.getUserList().add(user);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@Override
	public IResponse getCameraPlayUrl(GetCameraPlayUrlReq req) {
		GetCameraPlayUrlRes res =  new GetCameraPlayUrlRes();
	
		if(req.getUserId() == null) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "userId不能为空！");
			return res;
		}
		if(StringUtils.isEmpty(req.getRoomId())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "roomId不能为空！");
			return res;
		}
		StringBuilder sb =  new StringBuilder("");	
		// 加盐的值，需与验证端一致
		String salt = "Qwer1234!@#$";
		
		Object userObj = getPrincipal(true);
		PlatformUserEntity user =  null;
		if (null != userObj && (userObj instanceof PlatformUserEntity)) {
			user = (PlatformUserEntity) userObj;
		} else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO + "用户未登录！");
			return res;
		}
		if(!user.getId().equals(req.getUserId())) {
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
			return res;
		}

//		LiveRoom selectLiveRoom = liveRoomMapper.selectLiveRoomById(roomId);
//        if(selectLiveRoom == null) {
//        	throw new CustomException("直播间不存在！");
//        }
        
//		if(selectLiveRoom.getState().equals("1")) {
//		String host = contextInfo.selectConfigByKey("live.m3u8-edge-host");
//		String port = contextInfo.selectConfigByKey("live.m3u8-edge-port");
//		sb.append(host);
//		sb.append(StringUtils.isEmpty(port) ? "" : ":" + port);
		sb.append("/live/");
		sb.append(req.getRoomId() + ".m3u8");
		//设置URL有效超时间为1小时
		Date expireTime = DateUtil.getOffsetDateByHour(new Date(), 1);
		//转成10位时间戳
		Long expireTimeLong =expireTime.getTime() / 1000;
		
		String param = "c=" + user.getId() 
				+ "&e=" + expireTimeLong
				+ "&n=" + ThreadLocalRandom.current().nextInt(100000)
				+ "&r=" + req.getRoomId();
		// 加盐，需与验证端一致
		String sign = DigestUtil.getMD5Str(param + "&salt=" + salt, "32");
		param = param + "&s=" + sign;
		
		
		res.setExpireTime(DateUtil.formatByStyle(expireTime,"yyyyMMddHHmmss"));
		res.setPlayUrl(sb.toString() + "?" + param);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}

}
