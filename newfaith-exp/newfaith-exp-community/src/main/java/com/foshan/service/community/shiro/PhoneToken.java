package com.foshan.service.community.shiro;

import org.apache.shiro.authc.UsernamePasswordToken;

public class PhoneToken extends UsernamePasswordToken {

	/**
	 * 党员登录token，用于shiro框架做身份校验和权限认证
	 */
	private static final long serialVersionUID = 9193043341817438962L;
	
	private String type;
	//0：手机号登录；1：loginName登录；
	private Integer accountType=0;
	
	public PhoneToken(String username, String password) {
		super(username, password);
		// TODO Auto-generated constructor stub
	}
	

	public PhoneToken(String username, String password,String type) {
		super(username, password);
		this.type = type;
	}


	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}


	public Integer getAccountType() {
		return accountType;
	}


	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}
	
	
	
}
