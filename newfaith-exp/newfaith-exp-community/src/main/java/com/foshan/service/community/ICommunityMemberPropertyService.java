package com.foshan.service.community;

import java.text.ParseException;

import javax.servlet.http.HttpServletResponse;

import com.foshan.entity.community.CommunityContractEntity;
import com.foshan.form.community.request.CommunityMemberPropertyReq;
import com.foshan.form.response.IResponse;

public interface ICommunityMemberPropertyService {
	public IResponse getCommunityMemberPropertyList(CommunityMemberPropertyReq req);
	public IResponse addCommunityMemberProperty(CommunityMemberPropertyReq req,CommunityContractEntity communityContract);
	public IResponse addCommunityRentMemberProperty(CommunityMemberPropertyReq req);
	public IResponse modifyCommunityMemberProperty(CommunityMemberPropertyReq req);
	public IResponse modifyCommunityRentMemberProperty(CommunityMemberPropertyReq req);
	public IResponse deleteCommunityMemberProperty(CommunityMemberPropertyReq req);
	public IResponse getCommunityMemberPropertyInfo(CommunityMemberPropertyReq req);
	public IResponse bindingCommunityMemberProperty(CommunityMemberPropertyReq req);
	public IResponse auditCommunityMemberProperty(CommunityMemberPropertyReq req);
	public IResponse initializeParkingData();
	public IResponse ownerApprove(CommunityMemberPropertyReq req);
	public IResponse residentApprove(CommunityMemberPropertyReq req);
	public IResponse exportBillingDate(CommunityMemberPropertyReq req, HttpServletResponse response);
}
