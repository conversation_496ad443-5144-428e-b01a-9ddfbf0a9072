package com.foshan.service.community;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityKingdeeReq;
import com.foshan.form.response.IResponse;

public interface ICommunityKingdeeService {
	public IResponse getReceivablesOrder(CommunityKingdeeReq req);
	public IResponse getReceivablesOrderChange(CommunityKingdeeReq req);
	public IResponse getReceiptOrder(CommunityKingdeeReq req);
	public IResponse getBaseData(CommunityKingdeeReq req);
	public IResponse importReceivablesOrder(CommunityKingdeeReq req);
	public IResponse importReceivablesOrderChange(CommunityKingdeeReq req);
	public IResponse importReceiptOrder(CommunityKingdeeReq req);
	public IResponse importBaseData(CommunityKingdeeReq req);
	public IResponse getKingdeeImportHistoryList(CommunityKingdeeReq req);
	public IResponse deleteKingdeeImportHistory(CommunityKingdeeReq req);
	public void exportKingdeeImportFile(CommunityKingdeeReq req,HttpServletResponse response);
}
