package com.foshan.service.community;

import com.foshan.form.community.request.communityAnnouncementsReq.CommunityAnnouncementsReq;
import com.foshan.form.community.request.communityAnnouncementsReq.DeleteCommunityAnnouncementsReq;
import com.foshan.form.community.request.communityAnnouncementsReq.GetCommunityAnnouncementsListReq;
import com.foshan.form.community.request.communityAnnouncementsReq.AuditCommunityAnnouncementsReq;
import com.foshan.form.response.IResponse;

public interface ICommunityAnnouncementsService  {
    public IResponse getCommunityAnnouncementsInfo(DeleteCommunityAnnouncementsReq req);
    public IResponse addCommunityAnnouncements(CommunityAnnouncementsReq req);
    public IResponse modifyCommunityAnnouncements(CommunityAnnouncementsReq req);
    public IResponse deleteCommunityAnnouncements(DeleteCommunityAnnouncementsReq req);
    public IResponse auditCommunityAnnouncements(AuditCommunityAnnouncementsReq req);
    public IResponse getCommunityAnnouncementsList(GetCommunityAnnouncementsListReq req);
}
