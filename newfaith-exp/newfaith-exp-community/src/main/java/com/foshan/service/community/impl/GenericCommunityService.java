package com.foshan.service.community.impl;

import static java.util.Comparator.comparingInt;
import static java.util.stream.Collectors.groupingBy;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.annotation.Resource;
import javax.persistence.LockModeType;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.foshan.dao.ISnDao;
import com.foshan.dao.community.ICommunityAnnouncementsDao;
import com.foshan.dao.community.ICommunityAssetDao;
import com.foshan.dao.community.ICommunityBankDepositBatchDao;
import com.foshan.dao.community.ICommunityBankDepositEstatePayItemsDao;
import com.foshan.dao.community.ICommunityBankDepositRecordDao;
import com.foshan.dao.community.ICommunityBuilderDao;
import com.foshan.dao.community.ICommunityBuildingDao;
import com.foshan.dao.community.ICommunityCecorationItemsAttachmentDao;
import com.foshan.dao.community.ICommunityColumnDao;
import com.foshan.dao.community.ICommunityContractDao;
import com.foshan.dao.community.ICommunityDao;
import com.foshan.dao.community.ICommunityDecorationAttachmentDao;
import com.foshan.dao.community.ICommunityDecorationItemsDao;
import com.foshan.dao.community.ICommunityDepartmentDao;
import com.foshan.dao.community.ICommunityDictionaryDao;
import com.foshan.dao.community.ICommunityDistrictDao;
import com.foshan.dao.community.ICommunityEstateDao;
import com.foshan.dao.community.ICommunityEventCategoryDao;
import com.foshan.dao.community.ICommunityEventCategoryItemsDao;
import com.foshan.dao.community.ICommunityEventNotificationDao;
import com.foshan.dao.community.ICommunityEventsDao;
import com.foshan.dao.community.ICommunityFormulaTempleteDao;
import com.foshan.dao.community.ICommunityInspectionRecordDao;
import com.foshan.dao.community.ICommunityInstallmentSmsDao;
import com.foshan.dao.community.ICommunityInvoiceDao;
import com.foshan.dao.community.ICommunityKingdeeDao;
import com.foshan.dao.community.ICommunityMeasuresDao;
import com.foshan.dao.community.ICommunityMemberDao;
import com.foshan.dao.community.ICommunityMemberPropertyDao;
import com.foshan.dao.community.ICommunityMeterAllocationDao;
import com.foshan.dao.community.ICommunityMeterAllocationItemDao;
import com.foshan.dao.community.ICommunityMeterAttributesDao;
import com.foshan.dao.community.ICommunityMeterDao;
import com.foshan.dao.community.ICommunityMeterFormulaDao;
import com.foshan.dao.community.ICommunityMeterPriceHistoryDao;
import com.foshan.dao.community.ICommunityMeterRecordDao;
import com.foshan.dao.community.ICommunityPayItemsDao;
import com.foshan.dao.community.ICommunityPayItemsPriceDao;
import com.foshan.dao.community.ICommunityPaymentAccountDao;
import com.foshan.dao.community.ICommunityPaymentRecordDao;
import com.foshan.dao.community.ICommunityPropertyDao;
import com.foshan.dao.community.ICommunityPropertyServiceDao;
import com.foshan.dao.community.ICommunityReceiptDao;
import com.foshan.dao.community.ICommunityReceiptReceivablesDao;
import com.foshan.dao.community.ICommunityReceivablesChangesDao;
import com.foshan.dao.community.ICommunityReceivablesDao;
import com.foshan.dao.community.ICommunityReservatPeriodDao;
import com.foshan.dao.community.ICommunityReservationActivitiesDao;
import com.foshan.dao.community.ICommunityReservationDateDao;
import com.foshan.dao.community.ICommunityReservationRecordDao;
import com.foshan.dao.community.ICommunityReservationStrategyDao;
import com.foshan.dao.community.ICommunityRoleDao;
import com.foshan.dao.community.ICommunityUserDao;
import com.foshan.dao.community.IDeviceCorrelationDao;
import com.foshan.dao.community.IDeviceDao;
import com.foshan.dao.community.IEmergencyMessageDao;
import com.foshan.dao.community.IEstateVoDao;
import com.foshan.dao.community.IMessageAccountDao;
import com.foshan.dao.community.IOptionsVoDao;
import com.foshan.dao.community.IReceiveMessageTypeDao;
import com.foshan.dao.community.IWarningMessageDao;
import com.foshan.dao.generic.Page;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.ColumnEntity;
import com.foshan.entity.DepartmentEntity;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.SnEntity;
import com.foshan.entity.community.CommunityAssetEntity;
import com.foshan.entity.community.CommunityColumnEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityEventNotificationEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityPropertyEntity;
import com.foshan.entity.community.CommunityReceiptEntity;
import com.foshan.entity.community.CommunityReceivablesChangesEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.community.context.CommunityContext;
import com.foshan.entity.community.vo.CancelBenefitAmountChangeDetailItem;
import com.foshan.entity.community.vo.CancelBenefitDetailItemVo;
import com.foshan.entity.community.vo.CancelBenefitDetailVo;
import com.foshan.entity.community.vo.EstateReceivablesVo;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.ColumnForm;
import com.foshan.form.community.CommunityBuilding;
import com.foshan.form.community.CommunityColumnForm;
import com.foshan.form.community.CommunityDistrict;
import com.foshan.form.community.CommunityEstateForm;
import com.foshan.form.community.CommunityMemberForm;
import com.foshan.form.community.CommunityMemberPropertyForm;
import com.foshan.form.community.CommunityPropertyForm;
import com.foshan.form.community.CommunityReceivablesChangesForm;
import com.foshan.form.community.CommunityReceivablesForm;
import com.foshan.model.permssion.AccountPrincipalModel;
import com.foshan.model.permssion.PrincipalModel;
import com.foshan.service.impl.GenericService;
import com.foshan.util.CodeUtil;
import com.foshan.util.CommonUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.JsonUtil;
import com.foshan.util.community.CommunityCache;
import com.foshan.util.community.CommunityContextInfo;
import com.foshan.util.community.CommunityDateUtil;
import com.foshan.util.community.JepUtil;

public class GenericCommunityService extends GenericService {
	@Autowired
	protected CommunityContextInfo communityContextInfo;
	@Resource(name = "communityDepartmentDao")
	protected ICommunityDepartmentDao communityDepartmentDao;
	@Resource(name = "communityMemberDao")
	protected ICommunityMemberDao communityMemberDao;
	@Resource(name = "communityRoleDao")
	protected ICommunityRoleDao communityRoleDao;
	@Resource(name = "communityAnnouncementsDao")
	protected ICommunityAnnouncementsDao communityAnnouncementsDao;
	@Resource(name = "communityEventCategoryItemsDao")
	protected ICommunityEventCategoryItemsDao communityEventCategoryItemsDao;
	@Resource(name = "communityEventCategoryDao")
	protected ICommunityEventCategoryDao communityEventCategoryDao;
	@Resource(name = "communityFormulaTempleteDao")
	protected ICommunityFormulaTempleteDao communityFormulaTempleteDao;
	@Resource(name = "communityEventsDao")
	protected ICommunityEventsDao communityEventsDao;
	@Resource(name = "communityPropertyDao")
	protected ICommunityPropertyDao communityPropertyDao;
	@Resource(name = "communityAssetDao")
	protected ICommunityAssetDao communityAssetDao;
	@Resource(name = "communityUserDao")
	protected ICommunityUserDao communityUserDao;
	@Resource(name = "communityReservationActivitiesDao")
	protected ICommunityReservationActivitiesDao communityReservationActivitiesDao;
	@Resource(name = "communityReservationStrategyDao")
	protected ICommunityReservationStrategyDao communityReservationStrategyDao;
	@Resource(name = "communityReservationDateDao")
	protected ICommunityReservationDateDao communityReservationDateDao;
	@Resource(name = "communityReservationRecordDao")
	protected ICommunityReservationRecordDao communityReservationRecordDao;
	@Resource(name = "communityReservatPeriodDao")
	protected ICommunityReservatPeriodDao communityReservatPeriodDao;
	@Resource(name = "communityColumnDao")
	protected ICommunityColumnDao communityColumnDao;
	@Resource(name = "communityDistrictDao")
	protected ICommunityDistrictDao communityDistrictDao;
	@Resource(name = "communityMemberPropertyDao")
	protected ICommunityMemberPropertyDao communityMemberPropertyDao;
	@Resource(name = "optionsVoDao")
	protected IOptionsVoDao optionsVoDao;
	@Resource(name = "communityDao")
	protected ICommunityDao communityDao;
	@Resource(name = "communityBuildingDao")
	protected ICommunityBuildingDao communityBuildingDao;
	@Resource(name = "communityBuilderDao")
	protected ICommunityBuilderDao communityBuilderDao;
	@Resource(name = "communityEstateDao")
	protected ICommunityEstateDao communityEstateDao;
	@Resource(name = "communityMeterDao")
	protected ICommunityMeterDao communityMeterDao;
	@Resource(name = "communityMeterAttributesDao")
	protected ICommunityMeterAttributesDao communityMeterAttributesDao;
	@Resource(name = "communityPaymentAccountDao")
	protected ICommunityPaymentAccountDao communityPaymentAccountDao;
	@Resource(name = "communityMeterFormulaDao")
	protected ICommunityMeterFormulaDao communityMeterFormulaDao;
	@Resource(name = "communityMeterRecordDao")
	protected ICommunityMeterRecordDao communityMeterRecordDao;
	@Resource(name = "communityPayItemsDao")
	protected ICommunityPayItemsDao communityPayItemsDao;
	@Resource(name = "communityReceivablesChangesDao")
	protected ICommunityReceivablesChangesDao communityReceivablesChangesDao;
	@Resource(name = "communityReceivablesDao")
	protected ICommunityReceivablesDao communityReceivablesDao;
	@Resource(name = "communityReceiptReceivablesDao")
	protected ICommunityReceiptReceivablesDao communityReceiptReceivablesDao;
	@Resource(name = "communityReceiptDao")
	protected ICommunityReceiptDao communityReceiptDao;
	@Resource(name = "communityMeterAllocationDao")
	protected ICommunityMeterAllocationDao communityMeterAllocationDao;
	@Resource(name = "communityMeterAllocationItemDao")
	protected ICommunityMeterAllocationItemDao communityMeterAllocationItemDao;
	@Resource(name = "communityPaymentRecordDao")
	protected ICommunityPaymentRecordDao communityPaymentRecordDao;
	@Resource(name = "communityMeterPriceHistoryDao")
	protected ICommunityMeterPriceHistoryDao communityMeterPriceHistoryDao;
	@Resource(name = "communityBankDepositRecordDao")
	protected ICommunityBankDepositRecordDao communityBankDepositRecordDao;
	@Resource(name = "communityBankDepositBatchDao")
	protected ICommunityBankDepositBatchDao communityBankDepositBatchDao;
	@Resource(name = "communityBankDepositEstatePayItemsDao")
	protected ICommunityBankDepositEstatePayItemsDao communityBankDepositEstatePayItemsDao;
	@Resource(name = "communityPropertyServiceDao")
	protected ICommunityPropertyServiceDao communityPropertyServiceDao;
	@Resource(name = "communityDictionaryDao")
	protected ICommunityDictionaryDao communityDictionaryDao;
	@Resource(name = "snDao")
	protected ISnDao snDao;
	@Resource(name = "estateVoDao")
	protected IEstateVoDao estateVoDao;
	@Resource(name = "communityInvoiceDao")
	protected ICommunityInvoiceDao communityInvoiceDao;
	@Resource(name = "communityKingdeeDao")
	protected ICommunityKingdeeDao communityKingdeeDao;
	@Resource(name = "communityPayItemsPriceDao")
	protected ICommunityPayItemsPriceDao communityPayItemsPriceDao;
	@Resource(name = "emergencyMessageDao")
	protected IEmergencyMessageDao emergencyMessageDao;
	@Resource(name = "warningMessageDao")
	protected IWarningMessageDao warningMessageDao;
    @Resource(name = "deviceCorrelationDao")
    protected IDeviceCorrelationDao deviceCorrelationDao;
    @Resource(name = "deviceDao")
    protected IDeviceDao deviceDao;
    @Resource(name = "receiveMessageTypeDao")
    protected IReceiveMessageTypeDao receiveMessageTypeDao;
    @Resource(name = "messageAccountDao")
    protected IMessageAccountDao messageAccountDao;
    @Resource(name = "communityMeasuresDao")
    protected ICommunityMeasuresDao communityMeasuresDao;
    @Resource(name = "communityContractDao")
    protected ICommunityContractDao communityContractDao;
    @Resource(name = "communityDecorationAttachmentDao")
    protected ICommunityDecorationAttachmentDao communityDecorationAttachmentDao;
    @Resource(name = "communityDecorationItemsDao")
    protected ICommunityDecorationItemsDao communityDecorationItemsDao;
    @Resource(name = "communityInspectionRecordDao")
    protected ICommunityInspectionRecordDao communityInspectionRecordDao;
    @Resource(name = "communityEventNotificationDao")
    protected ICommunityEventNotificationDao communityEventNotificationDao;
    @Resource(name = "communityCecorationItemsAttachmentDao")
    protected ICommunityCecorationItemsAttachmentDao communityCecorationItemsAttachmentDao;
    @Resource(name = "communityInstallmentSmsDao")
    protected ICommunityInstallmentSmsDao communityInstallmentSmsDao;

	protected Object getPrincipal(boolean returnFullEntity) {
		Subject curUser = SecurityUtils.getSubject();
		PrincipalCollection principals = curUser.getPrincipals();
		if (null != principals && !principals.isEmpty()) {
			@SuppressWarnings("unchecked")
			List<Object> principalList = principals.asList();
			Object principal = (null != principalList.get(1) ? principalList.get(1) : null);
			if (!returnFullEntity) {
				return principal;
			}
			if (principal instanceof AccountPrincipalModel) {
				AccountPrincipalModel partyUser = (AccountPrincipalModel) principal;
				String sql = "select * from t_account where id = '" + partyUser.getId() + "' and userState = '"
						+ EntityContext.RECORD_STATE_VALID + "'";
				Object account = communityMemberDao.getUniqueBySql(sql);
				return account;
			}
			if (principal instanceof AccountEntity) {
				AccountEntity shiroAccount = (AccountEntity) principal;
				String sql = "select * from t_account where id = '" + shiroAccount.getId() + "' and userState = '"
						+ EntityContext.RECORD_STATE_VALID + "'";
				Object account = accountDao.getUniqueBySql(sql);
				return account;
			} else if (principal instanceof PlatformUserEntity) {
				PlatformUserEntity shiroPlatformUser = (PlatformUserEntity) principal;
				String sql = "select * from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '"
						+ EntityContext.RECORD_STATE_VALID + "'";
				Object platformUser = platformUserDao.getUniqueBySql(sql);
				return platformUser;
			} else if (principal instanceof PrincipalModel) {
				PrincipalModel shiroPlatformUser = (PrincipalModel) principal;
				String sql = "select * from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '"
						+ EntityContext.RECORD_STATE_VALID + "'";
				Object platformUser = platformUserDao.getUniqueBySql(sql);
				return platformUser;
			}

			return null;
		} else {
			return null;
		}
	}

	public CommunityPropertyForm getCommunityPropertyForm(CommunityPropertyEntity o, String auditStateList,
			String relationList) {
		CommunityPropertyForm propertyForm = new CommunityPropertyForm();
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if (null != o) {
			propertyForm.setPropertyId(o.getId());
			propertyForm.setCreateTime(o.getCreateTime() != null ? sdf.format(o.getCreateTime()) : null);
			propertyForm.setLastModifyTime(o.getLastModifyTime() != null ? sdf.format(o.getLastModifyTime()) : null);
			propertyForm.setPropertyName(o.getPropertyName());

			/*
			 * if(o instanceof CommunityAccessCardEntity) { CommunityAccessCardEntity
			 * property = (CommunityAccessCardEntity) o;
			 * propertyForm.setModel(property.getModel());
			 * propertyForm.setBarcodes(property.getBarcodes());
			 * propertyForm.setManufacturers(property.getManufacturers());
			 * propertyForm.setSupplier(property.getSupplier());
			 * propertyForm.setUsageTerm(sdf.format(property.getUseDate()));
			 * propertyForm.setSupportPhone(property.getSupportPhone());
			 * propertyForm.setFacilitiesState(property.getFacilitiesState());
			 * propertyForm.setFacilitiesTyp(property.getFacilitiesTyp());
			 * propertyForm.setPropertyType(0);
			 * propertyForm.setLocation(property.getLocation());
			 * propertyForm.setTypes(property.getTypes());
			 * propertyForm.setNetworkIP(property.getNetworkIP()); }else if( o instanceof
			 * CommunityResidenceEntity) { CommunityResidenceEntity property =
			 * (CommunityResidenceEntity) o;
			 * propertyForm.setSaleState(property.getSaleState());
			 * propertyForm.setUserState(property.getUserState());
			 * propertyForm.setIsAcceptanceReceive(property.getIsAcceptanceReceive());
			 * propertyForm.setUsageTerm(property.getUsageTerm());
			 * propertyForm.setEstateType(property.getEstateType());
			 * 
			 * propertyForm.setOrientation(property.getOrientation());
			 * propertyForm.setPropertyType(1); }else if(o instanceof CommunityShopEntity) {
			 * CommunityShopEntity property = (CommunityShopEntity) o;
			 * propertyForm.setSaleState(property.getSaleState());
			 * propertyForm.setUserState(property.getUserState());
			 * propertyForm.setIsAcceptanceReceive(property.getIsAcceptanceReceive());
			 * propertyForm.setUsageTerm(property.getUsageTerm());
			 * propertyForm.setEstateType(property.getEstateType());
			 * 
			 * propertyForm.setOrientation(property.getOrientation());
			 * propertyForm.setPropertyType(2); }else if(o instanceof
			 * CommunityParkingEntity) { CommunityParkingEntity property =
			 * (CommunityParkingEntity) o;
			 * propertyForm.setSaleState(property.getSaleState());
			 * propertyForm.setUserState(property.getUserState());
			 * propertyForm.setIsAcceptanceReceive(property.getIsAcceptanceReceive());
			 * propertyForm.setUsageTerm(property.getUsageTerm());
			 * propertyForm.setEstateType(property.getEstateType());
			 * propertyForm.setPropertyType(3);
			 * 
			 * }else if(o instanceof CommunityPublicAreaEntity) { CommunityPublicAreaEntity
			 * property = (CommunityPublicAreaEntity) o;
			 * propertyForm.setSaleState(property.getSaleState());
			 * propertyForm.setUserState(property.getUserState());
			 * propertyForm.setIsAcceptanceReceive(property.getIsAcceptanceReceive());
			 * propertyForm.setUsageTerm(property.getUsageTerm());
			 * propertyForm.setEstateType(property.getEstateType());
			 * 
			 * propertyForm.setIsReserved(property.getIsReserved());
			 * propertyForm.setPropertyType(4); }else
			 */
			if (o instanceof CommunityEstateEntity) {
				CommunityEstateEntity property = (CommunityEstateEntity) o;
				propertyForm.setUnitCode(property.getUnitCode());
				propertyForm.setPropertyName(property.getPropertyName());
				propertyForm.setUnitCode(property.getUnitCode());
				propertyForm.setIsAcceptanceReceive(property.getIsAcceptanceReceive());
				propertyForm.setUsageTerm(property.getUsageTerm());
				propertyForm.setOrientation(property.getOrientation());
				propertyForm.setEstateType(property.getEstateType());
				propertyForm.setIsReserved(property.getIsReserved());
				propertyForm.setEstateType(property.getEstateType());
				propertyForm.setFloor(property.getFloor());
				propertyForm.setRoomNumber(property.getRoomNumber());
				propertyForm.setUsableArea(null != property.getUsableArea() ? property.getUsableArea().toString() : "");
				propertyForm.setChargingArea(
						null != property.getChargingArea() ? property.getChargingArea().toString() : "");
				propertyForm.setSpecialAllocationFlag(property.getSpecialAllocationFlag());
				propertyForm.setAdditionalArea(
						null != property.getAdditionalArea() ? property.getAdditionalArea().toString() : "");
				propertyForm.setDecoration(property.getDecoration());
				propertyForm.setComment(property.getComment());
				
				propertyForm.setCommunityAddress(property.getBuilding().getDistrict().getDistrictName()
						+ property.getBuilding().getBuildingName() + property.getFloor() + property.getRoomNumber());

				if (null != o.getBuilding()) {
					CommunityBuilding communityBuildingForm = new CommunityBuilding();
					communityBuildingForm.setCommunityBuildingId(o.getBuilding().getId());
					communityBuildingForm.setAddress(o.getBuilding().getAddress());
					communityBuildingForm.setBuildingCode(o.getBuilding().getBuildingCode());
					communityBuildingForm.setBuildingName(o.getBuilding().getBuildingName());
					communityBuildingForm.setBuildingPermitNum(o.getBuilding().getBuildingPermitNum());
					if (null != o.getBuilding().getDistrict()) {
						communityBuildingForm
								.setDistrictForm(new CommunityDistrict(o.getBuilding().getDistrict().getId(),
										o.getBuilding().getDistrict().getDistrictName()));
					}
					propertyForm.setBuildingForm(communityBuildingForm);
				}
			}

			String[] auditStates = StringUtils.isNotEmpty(auditStateList) ? auditStateList.split(",") : null;
			String[] relations = StringUtils.isNotEmpty(relationList) ? relationList.split(",") : null;
			List<String> auditStateDataList = null != auditStates ? Arrays.asList(auditStates) : null;
			List<String> relationsDataList = null != relations ? Arrays.asList(relations) : null;
			o.getMemberPropertyList().forEach(p -> {
				if ((null == auditStateDataList && null == relationsDataList)
//						|| 
//						(null==auditStateDataList && null!=relationsDataList && relationsDataList.contains(p.getRelation().toString())) ||
//						(null!=auditStateDataList && auditStateDataList.contains(p.getAuditState().toString()) && null==relationsDataList) || 
//						(null!=auditStateDataList && auditStateDataList.contains(p.getAuditState().toString()) && null!=relationsDataList && relationsDataList.contains(p.getRelation().toString()))
//						
				) {
					CommunityMemberPropertyForm memberProperForm = new CommunityMemberPropertyForm();
//					memberProperForm.setAuditState(p.getAuditState());
					memberProperForm.setCreateTime(sdf.format(p.getCreateTime()));
//					memberProperForm.setInTime(null!=p.getInTime() ? sdf.format(p.getInTime()):"");
//					memberProperForm.setRelation(p.getRelation());
					memberProperForm.setMemberPropertyId(p.getId());

					memberProperForm.setMemberType(p.getMemberType());
					memberProperForm.setAuditState(p.getAuditState());
					memberProperForm.setIsCurrentMember(p.getIsCurrentMember());
					memberProperForm.setBillingDate(
							null != p.getBillingDate() ? DateUtil.formatLongFormat(p.getBillingDate()) : "");
					memberProperForm.setCreateTime(
							null != p.getCreateTime() ? DateUtil.formatLongFormat(p.getCreateTime()) : "");
					memberProperForm.setIsCurrentOwner(p.getIsCurrentOwner());
					memberProperForm.setRecordDate(
							null != p.getRecordDate() ? DateUtil.formatLongFormat(p.getRecordDate()) : "");
					memberProperForm.setTerminationDate(
							null != p.getTerminationDate() ? DateUtil.formatLongFormat(p.getTerminationDate()) : "");
					// memberProperForm.setAcceptanceDate(null != p.getAcceptanceDate()?
					// DateUtil.formatLongFormat(p.getAcceptanceDate()) : "");
					memberProperForm.setCarInfo(StringUtils.isNotEmpty(p.getCarInfo()) ? p.getCarInfo() : "");
					memberProperForm.setParentPropertyForm(getCommunityPropertyForm(p.getParentProperty(), null, null));
					/*
					 * if(null != p.getParentProperty()) { CommunityEstateEntity parentProperty =
					 * (CommunityEstateEntity) p.getParentProperty(); CommunityPropertyForm
					 * parentPropertyForm = new CommunityPropertyForm();
					 * parentPropertyForm.setPropertyId(p.getParentProperty().getId());
					 * parentPropertyForm.setCreateTime(p.getParentProperty().getCreateTime() !=
					 * null ? sdf.format(p.getParentProperty().getCreateTime()) : null );
					 * parentPropertyForm.setLastModifyTime(p.getParentProperty().getLastModifyTime(
					 * ) != null ? sdf.format(p.getParentProperty().getLastModifyTime()) : null);
					 * parentPropertyForm.setPropertyName(p.getParentProperty().getPropertyName());
					 * parentPropertyForm.setUnitCode(parentProperty.getUnitCode());
					 * parentPropertyForm.setPropertyName(parentProperty.getPropertyName());
					 * parentPropertyForm.setIsAcceptanceReceive(parentProperty.
					 * getIsAcceptanceReceive());
					 * parentPropertyForm.setUsageTerm(parentProperty.getUsageTerm());
					 * parentPropertyForm.setOrientation(parentProperty.getOrientation());
					 * parentPropertyForm.setEstateType(parentProperty.getEstateType());
					 * parentPropertyForm.setIsReserved(parentProperty.getIsReserved());
					 * parentPropertyForm.setEstateType(parentProperty.getEstateType());
					 * parentPropertyForm.setFloor(parentProperty.getFloor());
					 * parentPropertyForm.setRoomNumber(parentProperty.getRoomNumber());
					 * 
					 * }
					 */
					memberProperForm
							.setEndDate(null != p.getEndDate() ? DateUtil.formatLongFormat(p.getEndDate()) : "");
					memberProperForm.setComment(p.getComment());
					if (null != p.getMember()) {
						CommunityMemberForm memberForm = new CommunityMemberForm();
						memberForm.setMemberId(p.getMember().getId());
						memberForm.setEmail(p.getMember().getEmail());
						memberForm.setHomePhone(p.getMember().getHomePhone());
						memberForm.setNickName(p.getMember().getNickName());
						memberForm.setOfficePhone(p.getMember().getOfficePhone());
						memberForm.setPhone(p.getMember().getPhone());
						memberForm.setRegistName(p.getMember().getRegistName());
						memberForm.setSex(p.getMember().getSex());
						memberForm.setSmartcardId(p.getMember().getSmartcardId());
						memberForm.setIdCard(
								StringUtils.isNotEmpty(p.getMember().getIdCard()) ? p.getMember().getIdCard() : "");
						memberForm.setHomeAddress(
								StringUtils.isNotEmpty(p.getMember().getHomeAddress()) ? p.getMember().getHomeAddress()
										: "");
						memberForm.setHeadImage(
								StringUtils.isNotEmpty(p.getMember().getHeadImage()) ? p.getMember().getHeadImage()
										: "");
						memberForm.setUserName(
								StringUtils.isNotEmpty(p.getMember().getUserName()) ? p.getMember().getUserName() : "");
						memberProperForm.setMemberForm(memberForm);
					}

					// memberProperForm.setPropertyForm(getCommunityPropertyForm(p.getProperty()));

					propertyForm.getMemberPropertyList().add(memberProperForm);
				}

			});
		}

		return propertyForm;
	}

	@SuppressWarnings("unchecked")
	protected HashMap<String, Object> communityColumnList(Set<ColumnEntity> columnSet, Integer serviceId,
			Integer columnId, Integer depth, Integer visitFlag, String startDate, String endDate, Integer targetType,
			Integer columnState,String columnName) {
		HashMap<String, Object> columnList = new HashMap<String, Object>();
		Integer serviceVisits = 0;
		List<CommunityColumnForm> columnFormList = new ArrayList<CommunityColumnForm>();
		depth--;
		for (ColumnEntity c : columnSet) {
			if (c instanceof CommunityColumnEntity) {
				CommunityColumnEntity o = (CommunityColumnEntity) c;
				CommunityColumnForm columnForm = new CommunityColumnForm();
	       		StringBuilder hql = new StringBuilder("select distinct a from CommunityAssetEntity  a   where  a.auditState in(0,1) " + 
                		" and a.id in(select b.resourceId from UpshelfColumnEntity b where  b.column.id ="+o.getId()+")");
        		List<CommunityAssetEntity> list = communityAssetDao.getListByHql(hql.toString(), "");
        		Map<Integer, List<CommunityAssetEntity>> collect1 = (Map<Integer, List<CommunityAssetEntity>>) list
        			    .parallelStream().collect(groupingBy(CommunityAssetEntity::getAuditState));
        		columnForm.setFirstAuditCount(collect1.containsKey(0) ? collect1.get(0).size() : 0);
        		columnForm.setFinalAuditCount(collect1.containsKey(1) ? collect1.get(1).size() : 0);
				
				Set<ColumnEntity> columnSet1 = o.getSubColumnList();
				if ((null == targetType || (null != targetType && o.getTargetType() == targetType))
						&& (null == columnState || (null != columnState && o.getColumnState() == columnState))
						&&(StringUtils.isEmpty(columnName) || (StringUtils.isNotEmpty(columnName) && columnName.contains(o.getColumnName())))
						&& o.getState()!=EntityContext.RECORD_STATE_INVALID) {
					columnForm.setColumnId(o.getId());
					columnForm.setColumnName(o.getColumnName());
					columnForm.setServiceId(serviceId);
					columnForm.setColumnCode(o.getColumnCode());
					columnForm.setColumnInfo(o.getColumnInfo());
					columnForm.setColumnType(o.getColumnType());
					columnForm.setColumnImage(getAsset(o.getColumnImage()));
					columnForm.setColumnPhoneImage(getAsset(o.getColumnPhoneImage()));
					columnForm.setColumnState(o.getColumnState());
					columnForm.setColumnLevel(o.getColumnLevel());
					columnForm.setParentColumnId(columnId);
					columnForm.setOrders(o.getOrders());
					columnForm.setColumnInfo(o.getColumnInfo());
					columnForm.setTargetType(o.getTargetType());

					// 查询子栏目
					if (columnSet1.size() > 0 && depth >= 1) {
						columnForm.getSubColumnList()
								.addAll((List<ColumnForm>) communityColumnList(columnSet1, serviceId, o.getId(), depth,
										visitFlag, startDate, endDate, targetType, columnState,columnName).get("columnList"));
					}

					columnFormList.add(columnForm);
				}

				// 根据访问量统计开关统计当前栏目下的分组访问量，且访问量统计与查询深度无关
				if (visitFlag == EntityContext.COUNT_VISIT_ON) {
					columnForm.setTotalVisits(getColumnVisit(serviceId, o.getId(), startDate, endDate));
				}
			}

		}
		columnFormList.sort(comparingInt(ColumnForm::getOrders));
		// columnFormList.sort(Comparator.comparingInt(ColumnForm::getOrders).thenComparingInt(ColumnForm::getColumnId).reversed());
		// columnFormList.sort(Comparator.comparingInt(ColumnForm::getColumnId).reversed().thenComparingInt(ColumnForm::getOrders));

		columnList.put("columnList", columnFormList);
		columnList.put("serviceVisits", serviceVisits);
		return columnList;
	}

	protected CommunityEstateEntity getEstate(Integer estateId) {
		CommunityEstateEntity estate = null;
		if (CommunityCache.estateList.containsKey(estateId)) {
			estate = CommunityCache.estateList.get(estateId);
		} else {
			estate = communityEstateDao.get(estateId);
			CommunityCache.estateList.put(estateId, estate);
		}
		return estate;
	}

	protected CommunityMeterEntity getMeter(Integer meterId) {
		CommunityMeterEntity meter = null;
		if (CommunityCache.meterList.containsKey(meterId)) {
			meter = CommunityCache.meterList.get(meterId);
		} else {
			meter = communityMeterDao.get(meterId);
			CommunityCache.meterList.put(meterId, meter);
		}
		return meter;
	}

	protected CommunityEstateForm parseEstateForm(CommunityEstateEntity estate) {
		CommunityEstateForm estateForm = new CommunityEstateForm();
		BeanUtils.copyProperties(estate, estateForm);
		estateForm.setEstateId(estate.getId());
		estateForm.setMemberPropertyList(null);
		return estateForm;
	}

	/*
	 * 根据payItemsId获取payItemsEntity
	 */
	protected CommunityPayItemsEntity getPayItems(Integer payItemsId) {
		CommunityPayItemsEntity payItems = null;
		if (CommunityCache.payItemsList.containsKey(payItemsId)) {
			payItems = CommunityCache.payItemsList.get(payItemsId);
		} else {
			payItems = communityPayItemsDao.get(payItemsId);
			CommunityCache.payItemsList.put(payItemsId, payItems);
		}
		return payItems;
	}

	/*
	 * 讲Entity对象转换成Form对象
	 */
	protected CommunityReceivablesForm parseReceivablesForm(CommunityReceivablesEntity receivables, Integer estateId) {
		CommunityReceivablesForm receivablesForm = new CommunityReceivablesForm();
		BeanUtils.copyProperties(receivables, receivablesForm);
		receivablesForm.setCommunityReceivablesId(receivables.getId());
		receivablesForm.setPayItemsCategory(null!=receivables.getPayItem() ? receivables.getPayItem().getChargeCategory() : null);
		receivablesForm.setReceivableAmount(
				null != receivables.getReceivableAmount() ? receivables.getReceivableAmount().toString() : "0");
		receivablesForm.setReceivedAmount(
				null != receivables.getReceivedAmount() ? receivables.getReceivedAmount().toString() : "0");
		receivablesForm.setNotReceivedAmount(CommonUtil
				.subtractAmount(receivables.getReceivableAmount(), receivables.getReceivedAmount()).toString());
		receivablesForm.setReceivableDate(DateUtil.formatShortFormat(receivables.getReceivableDate()));
		receivablesForm.setStartTime(
				null != receivables.getStartTime() ? DateUtil.formatShortFormat(receivables.getStartTime()) : "");
		receivablesForm.setEndTime(
				null != receivables.getEndTime() ? DateUtil.formatShortFormat(receivables.getEndTime()) : "");
		if (StringUtils.isEmpty(receivables.getChargeCategory()) || receivables.getChargeCategory().equals("其它类")
				|| receivables.getChargeCategory().equals("押金类")) {
			receivablesForm.setFeeType(0);
		} else {
			receivablesForm.setFeeType(1);
		}
		receivablesForm.setBenefitValue(receivables.getBenefitValue());
		receivablesForm.setBreachRatio(
				null != receivables.getPayItem() ? receivables.getPayItem().getBreachRatio().toString() : "0.00");
		List<CommunityReceivablesChangesEntity> changeList = receivables.getReceivablesChangesList();
		if (changeList.size() > 0) {
			List<CommunityReceivablesChangesForm> changeFormList = new ArrayList<>();
			changeList.forEach(o -> {
				if (o.getState().equals(EntityContext.RECORD_STATE_VALID)) {
					CommunityReceivablesChangesForm change = new CommunityReceivablesChangesForm();
					change.setCommunityReceivablesChangesId(o.getId());
					change.setChangeAmount(o.getChangeAmount().toString());
					change.setChangeDate(DateUtil.formatShortFormat(o.getChangeDate()));
					change.setComment(o.getComment());
					change.setApprover(o.getApprover());
					change.setChangeType(o.getChangeType());
					changeFormList.add(change);
				}
			});
			receivablesForm.setReceivablesChangesList(changeFormList);
			;
		} else {
			receivablesForm.setReceivablesChangesList(null);
		}
		if (null != estateId) {
			receivablesForm.setEstateForm(parseEstateForm(getEstate(estateId)));
		}
		return receivablesForm;
	}

	/*
	 * 将应收款Entity对象列表转换成Form列表,ReceivablesForm不返回Estate信息。
	 */
	protected Map<String, Map<String, Set<CommunityReceivablesForm>>> parseReceivablesFormMap(
			Map<String, Map<String, Set<CommunityReceivablesEntity>>> source) {
		return parseReceivablesFormMap(source, null);
	}

	/*
	 * 将应收款Entity对象列表转换成Form列表
	 */
	protected Map<String, Map<String, Set<CommunityReceivablesForm>>> parseReceivablesFormMap(
			Map<String, Map<String, Set<CommunityReceivablesEntity>>> source, Integer estateId) {
		Map<String, Map<String, Set<CommunityReceivablesForm>>> formList = new TreeMap<>(Collections.reverseOrder());
		source.keySet().forEach(o -> {
			Map<String, Set<CommunityReceivablesEntity>> o1 = source.get(o);
			Map<String, Set<CommunityReceivablesForm>> o2 = new TreeMap<>(Collections.reverseOrder());
			o1.keySet().forEach(b -> {
				Set<CommunityReceivablesEntity> s1 = o1.get(b);
				Set<CommunityReceivablesForm> s2 = new HashSet<>();
				s1.forEach(c -> {
					s2.add(parseReceivablesForm(c, estateId));

				});
				o2.put(b, s2);
			});
			formList.put(o, o2);
		});
		return formList;
	}

	/*
	 * 将应收款Entity对象列表转换成Form列表,返回Map<项目名, Set<应收项>>,ReceivablesForm不返回Estate信息。
	 */
	protected Map<String, Set<CommunityReceivablesForm>> parseReceivablesToNameFormMap(
			Map<String, Map<String, Set<CommunityReceivablesEntity>>> source) {
		return parseReceivablesToNameFormMap(source, null);
	}

	/*
	 * 将应收款Entity对象列表转换成Form列表,返回Map<项目名, Set<应收项>>
	 */
	protected Map<String, Set<CommunityReceivablesForm>> parseReceivablesToNameFormMap(
			Map<String, Map<String, Set<CommunityReceivablesEntity>>> source, Integer estateId) {
		Map<String, Set<CommunityReceivablesForm>> formList = new TreeMap<>();
		source.keySet().forEach(o -> {
			Map<String, Set<CommunityReceivablesEntity>> o1 = source.get(o);
			Set<CommunityReceivablesForm> o2 = new TreeSet<>();
			o1.keySet().forEach(b -> {
				Set<CommunityReceivablesEntity> s1 = o1.get(b);
				s1.forEach(c -> {
					o2.add(parseReceivablesForm(c, null));
				});
			});
			formList.put(o, o2);

		});
		return formList;
	}

	/*
	 * 计算待收的管理费和车位费
	 */
	// @SuppressWarnings("unchecked")
	protected void calManagerFee(LocalDate startDate, LocalDate endDate, Integer estateId, String unitCode,
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> target) {
		calManagerFee(startDate, endDate, estateId, unitCode, "", null, 0, target);
	}

	/*
	 * 计算待收的管理费和车位费 renkFlag = 0 不生成洋房和车位的租金费用 renkFlag = 1 都生成洋房和车位的租金费用 renkFlag =
	 * 2 只生洋房租金费用和不生成车位的租金费用 renkFlag = 3 不生洋房租金费用和只生成车位的租金费用
	 */
	protected void calManagerFee(LocalDate startDate, LocalDate endDate, Integer estateId, String unitCode,
			Integer payItemId, String chargeCategoryIds, Integer rentFlag,
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> target) {
		String payItems = payItemId != null ? payItemId.toString() : null;
		calManagerFee(startDate, endDate, estateId, unitCode, payItems, chargeCategoryIds, rentFlag, target);
	}

	@SuppressWarnings("unchecked")
	protected void calManagerFee(LocalDate startDate, LocalDate endDate, Integer estateId, String unitCode,
			String payItemIds, String chargeCategoryIds, Integer rentFlag,
			Map<Integer, Map<String, Map<String, Set<CommunityReceivablesEntity>>>> target) {
		rentFlag = null == rentFlag ? 0 : rentFlag;

		StringBuilder baseSql = new StringBuilder(
				"SELECT a.id,a.unitcode,a.additionalArea,a.chargingArea,d.parentPropertyId,a.paymentaccountId,"
						+ "b.payItemsId,c.chargeCategory,c.comment,c.endtime,c.feecaltype,c.itemsname,c.price,c.priceunit,c.starttime,"
						+ "a.acceptanceDate,d.billingDate,c.payDate,d.isCurrentMember,d.iscurrentowner,d.memberid,d.terminationDate,d.endDate,a.rentState,d.id AS memberPropertyId "
						+ "FROM t_community_property a LEFT JOIN t_community_member_property d ON a.id = d.propertyid, "
						+ "t_community_property_pay_items b,t_community_pay_items c "
						+ "WHERE a.id = b.propertyid AND b.payitemsid = c.id AND c.state = 1 "
						+ "AND c.chargeCategory != 4 AND c.chargeCategory != 5 AND c.chargeCategory != 9 AND d.iscurrentowner = 1 AND d.isCurrentMember =1 "
						+ "AND a.specialFeeFlag IN (0,2,3,5) AND c.starttime <= '"
						+ startDate.toString() + " 00:00:00' AND c.endTime>='" + endDate.toString() + " 00:00:00'");
		baseSql.append(StringUtils.isNotEmpty(payItemIds) ? (" AND c.id IN (" + payItemIds + ")") : "");
		baseSql.append(
				StringUtils.isNotEmpty(chargeCategoryIds) ? (" AND c.chargeCategory IN (" + chargeCategoryIds + ")")
						: "");

		StringBuilder estateObjectSql = new StringBuilder(baseSql.toString());
		estateObjectSql.append((rentFlag.intValue() == 0 || rentFlag.intValue() == 3) ? " AND c.payDate != 1 " : "");
		estateObjectSql.append(null != estateId ? " AND a.id = " + estateId : "");

		// 把车位收费也加进来
		estateObjectSql.append(" union ");
		estateObjectSql.append(baseSql.toString());
		estateObjectSql.append((rentFlag.intValue() == 0 || rentFlag.intValue() == 2) ? " AND c.payDate != 1 " : "");
		estateObjectSql.append(null != estateId ? " AND d.parentPropertyId = " + estateId : "");

		// System.out.println(estateObjectSql.toString());
		List<Object[]> resultList = communityEstateDao.createSQLQuery(estateObjectSql.toString()).list();
		// 互斥组
		Set<String> groupFeeSet = new HashSet<String>(); 
		for (Object[] temp : resultList) {
			EstateReceivablesVo o = EstateReceivablesVo.getEstateReceivablesVo(temp);

			if ((null == o.getParentPropertyId() || (null != o.getParentPropertyId()
					&& (null == o.getEndDate() || o.getEndDate().isAfter(endDate) || o.getEndDate().isEqual(endDate))))

					&& (((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) >= 0
							&& (int) (endDate.toEpochDay() - o.getBillingDate().toEpochDay()) >= 0)
							|| ((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) <= 0))) {
				CommunityReceivablesEntity receivables = new CommunityReceivablesEntity();
				receivables.setPayItemsName(o.getItemsName());
				receivables.setChargeCategory(CommunityPayItemsEntity.getChargeCategoryStr(o.getChargeCategory()));
				receivables.setChargeSource("收费设定");
				receivables.setComment(o.getUnitCode() + "：" + startDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"))
						+ (null != o.getParentPropertyId() ? "车位费" : "管理费"));
				CommunityEstateEntity estate = getEstate(
						null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId());
				if (estate.getSpecialFeeFlag().equals(0) || estate.getSpecialFeeFlag().equals(2)
						|| estate.getSpecialFeeFlag().equals(3)) {
					receivables.setEstate(estate);

					receivables.setSubEstateId(null != o.getParentPropertyId() ? o.getId() : null);
					receivables.setPayItem(getPayItems(o.getPayItemsId()));
					BigDecimal receivableAmount = new BigDecimal("0");
					/*
					 * 管理费根据单元的计费日期开始计算管理费 车位管理费根据车位租用时间开始计算
					 */
					LocalDate billingDate = o.getBillingDate().with(TemporalAdjusters.firstDayOfMonth())
							.isEqual(startDate) ? o.getBillingDate() : startDate;

					if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_CHARGE)) {
						receivableAmount = JepUtil.cal("单价*收费面积/本月天数*计费天数", 4, o.getPrice(), o.getChargingArea(),
								CommunityDateUtil.monthDays(endDate.toString()),
								CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
					} else if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_ADD)) {
						receivableAmount = JepUtil.cal("单价*附加面积/本月天数*计费天数", 4, o.getPrice(), o.getAdditionalArea(),
								CommunityDateUtil.monthDays(endDate.toString()),
								CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
					} else {
						receivableAmount = JepUtil.cal("单价/本月天数*计费天数", 4, o.getPrice(),
								CommunityDateUtil.monthDays(endDate.toString()),
								CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
					}

					receivables.setReceivableAmount(receivableAmount.setScale(2, RoundingMode.HALF_UP));
					receivables.setReceivedAmount(new BigDecimal("0"));
					receivables.setSourceNotes(o.getItemsName() + "(" + o.getUnitCode() + ")");
					receivables.setLockMark(0);
					receivables.setState(1);
					if (o.getPayDate() != null && o.getPayDate().intValue() > 0 && o.getPayDate().intValue() < 32) {
						// paydate[1,28]按生成应收日期,但当>28时需要判断闰收及大小月，后面有需求再优化。
						try {
							receivables.setReceivableDate(Date
									.from(LocalDate.of(startDate.getYear(), startDate.getMonthValue(), o.getPayDate())
											.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						} catch (DateTimeException e) {
							// 如果paydate不正确，划超出当月范围，应收日期为月未最后一日
							receivables.setReceivableDate(
									Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						}
					} else {
						// paydate > 31 应收日期为月未最后一日 ,未实现生成下个月2日起逻辑
						receivables
								.setReceivableDate(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					}
					if ((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) >= 0
							&& (int) (endDate.toEpochDay() - o.getBillingDate().toEpochDay()) >= 0) {
						receivables
								.setStartTime(Date.from(billingDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					} else {
						receivables.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					}

					receivables.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					receivables.setPaymentPeriod(receivables.getReceivableDate());
					// 如查应表中存在已经生成的互斥收费项目的应收，则跳过不生成该应收。
					if(receivables.getPayItem().getGroupFee() != null) {
						StringBuilder sameGroupSql = new StringBuilder("select a.* from t_community_receivables a inner join t_community_pay_items i on a.payItemId = i.id where a.estateid=" + (receivables.getEstate().getId()) 
								+ " and a.receivableDate='" + DateUtil.format(receivables.getReceivableDate(), 0) + "'"
								+ " and i.groupFee = " + receivables.getPayItem().getGroupFee())
								.append(null != receivables.getSubEstateId() ? " and a.subEstateId=" + receivables.getSubEstateId()
										: "");
						CommunityReceivablesEntity sameGroupReceivables = communityReceivablesDao.getUniqueBySql(sameGroupSql.toString());
						if(sameGroupReceivables != null) {
							continue;
						}
					}
					// 如查应表中存在已经生成相同应收，则不再生成
					StringBuilder dbSql = new StringBuilder(
							"select a.* from t_community_receivables a where a.estateid="
									+ (null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId())
									+ " and a.payItemId = " + receivables.getPayItem().getId()
									+ " and a.receivableDate='" + DateUtil.format(receivables.getReceivableDate(), 1)
									+ "'").append(
											null != receivables.getSubEstateId()
													&& !receivables.getSubEstateId().equals(0)
															? " and a.subEstateId=" + receivables.getSubEstateId()
															: "");
					CommunityReceivablesEntity dbReceivables = communityReceivablesDao.getUniqueBySql(dbSql.toString());
					if (receivables.getReceivableAmount().doubleValue() > 0 && null == dbReceivables) {
						// 已经生成的互斥收费项目的应收，则跳过不生成该应收。
						// 判断标准相同单元、相同应收日期、相同子单元、互斥ID的项目
						if(receivables.getPayItem().getGroupFee() != null) {
							String identify = receivables.getEstate().getId().toString() 
									  + "_" + DateUtil.format(receivables.getReceivableDate(), 0)
									  + "_" + ((null != receivables.getSubEstateId() && !receivables.getSubEstateId().equals(0)) ? receivables.getSubEstateId().toString(): "")
									  + "_" +  receivables.getPayItem().getGroupFee().toString();
									 
							if(groupFeeSet.contains(identify)) {
								continue;
							}
							else {
								groupFeeSet.add(identify);
							}
						}
						if(receivables.getPayItem().getEnableBenefitPolicy().intValue() == 1) {
							// 管理费优惠计算
							calBenefitReceivables(receivables, JepUtil.cal("单价/本月天数*计费天数", 4, receivables.getPayItem().getBenefitOrginPrice(),
									CommunityDateUtil.monthDays(endDate.toString()),
									CommunityDateUtil.subDate(billingDate.toString(), endDate.toString())).setScale(2, RoundingMode.HALF_UP));
						}
						CommunityCache.putReceivableCache(receivables, target);
						CommunityCache.putReceivableCache(receivables, CommunityCache.receivableCalEstateList);
					}
				}
			}
		}
	}

	// 获取违约金对应的payitem
	protected CommunityPayItemsEntity getBreachPayItem() {
		return communityPayItemsDao
				.getUniqueByHql("select i from CommunityPayItemsEntity i where i.chargeCategory = 5 and i.state = 1");
	}

    /**
     * 生成收据编号
     * 当receiptDate为当前月时，直接读取数据库的t_sn对应snType的值；否则直接查找收据表receiptCode like prefixStr_monthStr%的最大值,在此基础上序号加1
     * @param receiptType 收据类型名称
     * @param receiptDate 收据日期
     * @return 编号
     */
	protected synchronized String getReceiptCode(String receiptType, Date receiptDate) {
		String result = null;
		try {
			String prefixStr = CommunityReceiptEntity.getReceiptCodePrefix(receiptType);
			String monthStr = DateUtil.formatByStyle(receiptDate, "yyyyMM");
			Boolean isThisMonth = DateUtil.formatByStyle(new Date(), "yyyyMM").equals(monthStr);
			// isThisMonth = true时，当前月直接读取数据库的t_sn对应snType的值
			// isThisMonth = false时，直接查找收据表receiptCode like prefixStr_monthStr%的最大值,在此基础上序号加1
			Integer lastValue = 1;
			if(true == isThisMonth) {
				// 当前月份的收据
				lastValue = generateSnLastValue(prefixStr);	
			}
			else {
				// 非当前月的收据
				String hql = "select r from CommunityReceiptEntity r where r.receiptCode like '" + prefixStr + "_" + monthStr + "%' order by r.receiptCode desc";
				Page<CommunityReceiptEntity> page = new Page<CommunityReceiptEntity>();
				page.setPageSize(1);
				page.setBeginCount(1);
				page.setCurrentPage(1);
				Page<CommunityReceiptEntity> maxCodeRecord = communityReceiptDao.queryPage(page, hql);
				if(maxCodeRecord.getResultList().size() > 0) {
					String maxReceiptCode = maxCodeRecord.getResultList().get(0).getReceiptCode();
					String[] splitArr = maxReceiptCode.split("_");
					if(splitArr.length > 2) {
						lastValue = Integer.parseInt(splitArr[2]) + 1;
					}
				}
			}
			result = prefixStr + "_" + monthStr + "_"
					+ CodeUtil.getCode(6, lastValue.toString());
			
		} catch (Exception e) {
			// 读取编号失败，将用当前系统timestamp + 1位随机数为编号，不影响收据生成。
			e.printStackTrace();
			result = CommunityReceiptEntity.getReceiptCodePrefix(receiptType) + "_" + System.currentTimeMillis()
					+ new Random(System.currentTimeMillis()).nextInt();
			//log.error("读取编号失败，将用prefix+timestamp + 1位随机数为编号，保证收据顺利生成。{}", e);
		}
		return result;
	}
	
	/**
	 * 根据type获取编号
	 * @param snType 编号前缀标识
	 * @return
	 */
	protected synchronized Integer generateSnLastValue(String snType) {
		return getSnLastValue(snType);
	}

	private Integer getSnLastValue(String snType) {
		Integer result = 0;
		String hql = "select sn from SnEntity sn where sn.snType = :type";
		@SuppressWarnings("unchecked")
		List<SnEntity> resultList = snDao.createQuery(hql).setLockMode(LockModeType.PESSIMISTIC_WRITE)
				.setParameter("type", snType).list();
		if (resultList.size() > 0) {
			result = resultList.get(0).getLastValue();
		}
		resultList.get(0).setLastModifyTime(new Timestamp(System.currentTimeMillis()));
		;
		resultList.get(0).setLastValue(result + 1);
		return result;
	}

	/**
	 * 用户是否可以管理指定单元的账单
	 * 
	 * @param memberId 用户ID
	 * @param estateId 单元ID
	 * @return
	 */
	protected Boolean canManageBillOfEstate(Integer memberId, Integer estateId) {
		Boolean result = false;
		if (null == memberId || null == estateId) {
			return result;
		}
		StringBuilder sb = new StringBuilder(
				"select mp from CommunityMemberPropertyEntity mp inner join mp.member m inner join mp.property e ");
		sb.append("where (mp.isCurrentMember = 1 or mp.isCurrentOwner = 1) and m.id =" + memberId + " and e.id = "
				+ estateId);
		CommunityMemberPropertyEntity findUnique = communityMemberPropertyDao.getUniqueByHql(sb.toString());
		return findUnique != null;
	}

	/**
	 * 2023-1-1（已经废弃）
	 * 十六区优惠策略:16区从2023-1-1起，5区从2024-1-1起执行优惠策略，按2.38元单价计算物业管理费若连续三个月存在欠费情况，则第四个月开始按原有的3.18元单价进行计算，
	 * 之前的未缴月份也需补交按3.18计算部分金额，连同违约金也需要计算。若缴清费用，则下一个月恢复优惠按2.38元进行计算物业费。
	 * 
	 * 2025-1-4
	 * 优惠策略修改为：16区从2023-1-1起，5区从2024-1-1起执行优惠策略，当前月的历史管理费应收中只要存在3个月或以上欠费的情况，取消优惠，按原价3.18元单价进行计算，
	 * 之前的未缴月份也需补交按3.18计算部分金额，连同违约金也需要计算。该策略从2025年1月1日生效。
	 * @param receivables
	 * @param orginAmount 不符合优惠时的应收款金额
	 */
	protected void calBenefitReceivables(CommunityReceivablesEntity receivables, BigDecimal orginAmount) {

		if(CommunityDateUtil.dateToLocalDate(receivables.getReceivableDate()).with(TemporalAdjusters.firstDayOfMonth()).isAfter(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()))) {
			// 预收情况下不考虑取消优惠逻辑
			return;
		}
		
		if(receivables.getPayItem().getEnableBenefitPolicy()!= null && receivables.getPayItem().getEnableBenefitPolicy() == CommunityContext.RECORD_STATE_VALID && receivables.getPayItem().getBenefitStartDate() != null && (!receivables.getPayItem().getBenefitStartDate().after(receivables.getReceivableDate()))) {
			//LocalDate beforeDay = CommunityDateUtil.dateToLocalDate(receivables.getReceivableDate()).plus(-3, ChronoUnit.MONTHS).with(TemporalAdjusters.firstDayOfMonth());
			LocalDate beforeDay = CommunityDateUtil.dateToLocalDate(receivables.getPayItem().getBenefitStartDate());
			String benefitSql =  "SELECT * FROM t_community_receivables t WHERE (t.payItemId = " + receivables.getPayItem().getId() + ") AND t.estateId = " + receivables.getEstate().getId() 
					+ " AND t.receivableDate >= '" + beforeDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00")) + "'";
			@SuppressWarnings("unchecked")
			List<CommunityReceivablesEntity> benefitReceivablesList = (List<CommunityReceivablesEntity>) communityReceivablesDao
					.createSQLQuery(benefitSql.toString()).addEntity(CommunityReceivablesEntity.class)
					.list();
			Boolean isBenefit = false;
			//String districtAddress = receivables.getEstate().getBuilding().getDistrict().getAddress();
			String districtAddressSql =  "SELECT t.address FROM `t_community_district` t INNER JOIN `t_community_building` b ON b.`districtId` =  t.id INNER JOIN `t_community_property` p ON p.`buildingId` = b.id WHERE p.id = " + receivables.getEstate().getId();
			Object districtAddressObj = communityReceivablesDao.createSQLQuery(districtAddressSql).uniqueResult();
			String districtAddress = districtAddressObj == null ? "" : (String)districtAddressObj;
			if(benefitReceivablesList.size() < 3) {
				// 本月应收符合优惠政策
				receivables.setComment(receivables.getComment() + ";符合" + districtAddress + "区优惠政策");
				return;
			}
			Map<Integer,CancelBenefitDetailItemVo> hasCatchup =  new HashMap<Integer,CancelBenefitDetailItemVo>();
			
//*****2025年1月4日御江南提出新的优惠策略修改部分*****
//			for(CommunityReceivablesEntity benefitReceivables : benefitReceivablesList) {
//				// 前3个月存完成交费情况
//				if(benefitReceivables.getReceivableAmount().compareTo(benefitReceivables.getReceivedAmount()) <= 0) {
//					isBenefit = true;
//					break;
//				}
//				if(benefitReceivables.getBenefitValue() != null) {
//					CancelBenefitDetailVo benefitVo  = JsonUtil.jsonToObject(benefitReceivables.getBenefitValue(), CancelBenefitDetailVo.class);
//					for(CancelBenefitDetailItemVo item : benefitVo.getItems()) {
//						hasCatchup.put(item.getReceivablesId(), item);
//					}
//				}
//			}
			// 2023年1月1日后未完成付款应收记录数
			Integer unPaidNum = 0;
			for(CommunityReceivablesEntity benefitReceivables : benefitReceivablesList) {
				// 统计未完成付款的应收记录数
				if(benefitReceivables.getReceivableAmount().compareTo(benefitReceivables.getReceivedAmount()) > 0) {
					unPaidNum++;
				}
				// 统计已经补收的记录，防止后面计算重复补收
				if(benefitReceivables.getBenefitValue() != null) {
					CancelBenefitDetailVo benefitVo  = JsonUtil.jsonToObject(benefitReceivables.getBenefitValue(), CancelBenefitDetailVo.class);
					for(CancelBenefitDetailItemVo item : benefitVo.getItems()) {
						hasCatchup.put(item.getReceivablesId(), item);
					}
				}
			}
			isBenefit = unPaidNum < 3;
////*****2025年1月4日御江南提出新的优惠策略修改部分*****			
			if(isBenefit == false) {
				// 取消优惠
				BigDecimal catchUpAmount = BigDecimal.ZERO;
				List<CancelBenefitDetailItemVo> items =  new ArrayList<CancelBenefitDetailItemVo>();
				// 按3.18计算
				receivables.setReceivableAmount(orginAmount); 
				receivables.setComment(receivables.getComment() + ";不符合" + districtAddress + "区优惠政策");
				for(CommunityReceivablesEntity benefitReceivables : benefitReceivablesList) {
					//if(benefitReceivables.getReceivableAmount().compareTo(receivables.getReceivableAmount()) < 0 && benefitReceivables.getBenefitValue() == null) {
					if(!hasCatchup.containsKey(benefitReceivables.getId()) && benefitReceivables.getBenefitValue() == null
							&& benefitReceivables.getReceivableAmount().compareTo(benefitReceivables.getReceivedAmount())>0) {
						// 3个月内曾经优惠的应用需要补差额
						BigDecimal subAmount = receivables.getReceivableAmount().subtract(benefitReceivables.getReceivableAmount());
						// 判断是否住满整个月，如果不是，补缴金额为：整月补缴金额*入住天数/当月总天数
						BigDecimal divide = CommunityDateUtil.subDate(CommunityDateUtil.formatShortFormat(benefitReceivables.getStartTime()), 
								CommunityDateUtil.formatShortFormat(benefitReceivables.getEndTime()))
								.divide(CommunityDateUtil.monthDays(CommunityDateUtil.formatShortFormat(benefitReceivables.getEndTime())),2, RoundingMode.HALF_UP);
						
						subAmount = subAmount.multiply(divide).setScale(2, RoundingMode.HALF_UP);
						if(subAmount.compareTo(new BigDecimal("0.05")) > 0) {  //2024年5月起小数精确度从小数点后1位调整到2位
							CancelBenefitDetailItemVo item =  new CancelBenefitDetailItemVo(benefitReceivables.getId(),DateUtil.format(benefitReceivables.getReceivableDate(), 1) ,subAmount);
							items.add(item);
							catchUpAmount = catchUpAmount.add(subAmount);
						}
					}
				}
				if(catchUpAmount.compareTo(BigDecimal.ZERO) > 0) {
					// 修改应收并，记录优惠被取消的详情
					receivables.setReceivableAmount(receivables.getReceivableAmount().add(catchUpAmount).setScale(2, RoundingMode.HALF_UP));
					receivables.setBenefitValue(JsonUtil.objectToJSON(new CancelBenefitDetailVo(catchUpAmount, null ,items)));
					receivables.setComment(receivables.getComment() + "，需补回此前月份差额，补回" + catchUpAmount.setScale(2, RoundingMode.HALF_UP)+ "元");
				}
			}
			else {
				// 本月应收符合优惠政策，按2.38计算
				receivables.setComment(receivables.getComment() + ";符合" + districtAddress + "区优惠政策");
			}
			//防止大量应收数据hibernate插入变慢
			for(CommunityReceivablesEntity benefitReceivables : benefitReceivablesList) {
				communityReceivablesDao.evict(benefitReceivables);
			}
		}
	}
	
	/**
	 * 增加管理费补收对象动账记录，优先给补交的钱，优先补交最久日期的应收
	 * @param sourceId
	 * @param receivable
	 * @param paymentAmount
	 * @param changeDate
	 * @param changeType
	 */
	protected void modifySubReceivaedManageAmount(Integer sourceId,
			CommunityReceivablesEntity receivable, BigDecimal currentPaymentAmount,
			Date changeDate, Integer changeType) {
		if(receivable.getBenefitValue() != null && receivable.getBreachReceivablesId() == null && currentPaymentAmount.compareTo(BigDecimal.ZERO) > 0) {
			//补差额部分是否已经补完，如果未补完需要拆分。
			CancelBenefitDetailVo benefitVo  = JsonUtil.jsonToObject(receivable.getBenefitValue(), CancelBenefitDetailVo.class);
			Set<CancelBenefitDetailItemVo> items = new TreeSet<CancelBenefitDetailItemVo>(Comparator.reverseOrder());
			items.addAll(benefitVo.getItems()); //json反列化排序失效
			
			BigDecimal paymentAmount = currentPaymentAmount;
			for(CancelBenefitDetailItemVo item : items) {
			    BigDecimal subReceivedManageAmount = item.getSubReceivedManageAmount() ==  null ? BigDecimal.ZERO : item.getSubReceivedManageAmount();
			    BigDecimal itemPayableAmount = item.getSubManageAmount().subtract(subReceivedManageAmount);
			    
				if (itemPayableAmount.compareTo(BigDecimal.ZERO) <= 0) {
					// 已经完成付款，不需要付款
					continue;
				} else if (paymentAmount.compareTo(itemPayableAmount) >= 0) {
					item.setSubReceivedManageAmount(subReceivedManageAmount.add(itemPayableAmount));
					item.getChangeAmountDetail().add(new CancelBenefitAmountChangeDetailItem(sourceId, changeDate, itemPayableAmount, changeType));
					paymentAmount = paymentAmount.subtract(itemPayableAmount);
				} else {
					// 部分付款
					item.setSubReceivedManageAmount(subReceivedManageAmount.add(paymentAmount));
					item.getChangeAmountDetail().add(new CancelBenefitAmountChangeDetailItem(sourceId, changeDate, paymentAmount, changeType));
					paymentAmount = paymentAmount.subtract(paymentAmount);
				}
				if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
					// 完成付款
					break;
				}
			}
			receivable.setBenefitValue(JsonUtil.objectToJSON(benefitVo));
		}
	}
	
	/**
	 * 删除管理费补收对象动账记录
	 * @param sourceId
	 * @param receivable
	 * @param changeType
	 */
	protected void deleteSubReceivaedManageAmount(Integer sourceId, CommunityReceivablesEntity receivable ,Integer changeType) {
		if(receivable.getBenefitValue() != null && receivable.getBreachReceivablesId() == null) {
			//补差额部分是否已经补完，如果未补完需要拆分。
			CancelBenefitDetailVo benefitVo  = JsonUtil.jsonToObject(receivable.getBenefitValue(), CancelBenefitDetailVo.class);
			Set<CancelBenefitDetailItemVo> items = benefitVo.getItems();
			for(CancelBenefitDetailItemVo item : items) {
				Iterator<CancelBenefitAmountChangeDetailItem> it = item.getChangeAmountDetail().iterator();
				BigDecimal subReceivedManageAmount = item.getSubReceivedManageAmount() ==  null ? BigDecimal.ZERO : item.getSubReceivedManageAmount();
				while(it.hasNext()) {
					CancelBenefitAmountChangeDetailItem changeItem = it.next();
					if(changeItem.getSourceId().equals(sourceId) && changeItem.getChangeType().equals(changeType))
					{
						subReceivedManageAmount = subReceivedManageAmount.subtract(changeItem.getChangeAmount());
						it.remove();
					}
				}
				subReceivedManageAmount = subReceivedManageAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subReceivedManageAmount;
				item.setSubReceivedManageAmount(subReceivedManageAmount);
			}
			receivable.setBenefitValue(JsonUtil.objectToJSON(benefitVo));
		}
	}
	
	public  void sendSms(String phone,String templateData,String templateCode,String verifyCode) {
		ObjectMapper objectMapper = new ObjectMapper();
		ObjectNode json = objectMapper.createObjectNode();
		//String verifyCode = "尊敬的御江南"+unitCode+"业户，你已欠费"+amount+"元，本月将在"+day+"日银行划扣，为避免逾期违约，请您尽快缴清。";
		// 发送短信
		json.put("messageContent", verifyCode);
		json.put("userNumber", phone);
		json.put("scheduleTime", DateUtil.format(new Date(), 1));
		json.put("f", "1");
		json.put("templateCode", templateCode);
		json.put("templateData", templateData);

		Map<String, String> result = null;
		try {
			objectMapper.setSerializationInclusion(Include.NON_NULL);
//			log.info("--------------------------------------调用短信接口地址：" + communityContextInfo.smsInterfaceUrl + ";   参数："
//					+ json.toString() + "-------------------------");
			result = HttpClientUtil.post(communityContextInfo.smsInterfaceUrl, objectMapper.writeValueAsString(json), "utf-8");
//			log.info("--------------------------------------调用短信接口返回：" + result
//					+ "----------------------------------------");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
