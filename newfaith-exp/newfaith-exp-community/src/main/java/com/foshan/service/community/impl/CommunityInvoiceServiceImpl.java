package com.foshan.service.community.impl;

import static java.util.stream.Collectors.groupingBy;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletResponse;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.apache.commons.lang3.StringUtils;
//import org.dom4j.Document;
//import org.dom4j.DocumentHelper;
//import org.dom4j.Element;
//import org.dom4j.io.OutputFormat;
//import org.dom4j.io.XMLWriter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.foshan.dao.generic.Page;
import com.foshan.entity.DictionaryDataEntity;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityInvoiceEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityReceiptEntity;
import com.foshan.entity.community.CommunityReceiptReceivablesEntity;
import com.foshan.entity.community.context.CommunityContext;
import com.foshan.entity.community.vo.ReservedFieldVo;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityGoodsForm;
import com.foshan.form.community.CommunityInviceInfoForm;
import com.foshan.form.community.CommunityInvoiceForm;
import com.foshan.form.community.request.CommunityInvoiceReq;
import com.foshan.form.community.request.InvoiceQueryReq;
import com.foshan.form.community.request.InvoiceReq;
import com.foshan.form.community.request.RedInvoiceReq;
import com.foshan.form.community.response.communityReceipt.GenerateXmlRes;
import com.foshan.form.community.response.communityReceipt.GetCommunityInvoiceListRes;
import com.foshan.form.community.response.communityReceipt.InvoiceRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.model.permssion.PrincipalModel;
import com.foshan.service.community.ICommunityInvoiceService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.FileUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.XmlUtil;

@Transactional
@Service("communityInvoiceService")
public class CommunityInvoiceServiceImpl extends GenericCommunityService implements ICommunityInvoiceService{

	@Override
	public IResponse issueInvoice(CommunityInvoiceReq req) {
		InvoiceRes res = new InvoiceRes();
		if (null!=req.getInvoiceType() && 
				((req.getInvoiceType()==0 && null != req.getReceiptId() && 
				StringUtils.isNotEmpty(req.getReceiptReceivablesIdList())) 
				|| ((req.getInvoiceType()==1|| req.getInvoiceType()==2 )&& null != req.getInvoiceId()) )) {
			CommunityReceiptEntity communityReceipt = null;
			CommunityEstateEntity estate = null;
			CommunityInvoiceEntity parentInvoice = null;
			//蓝票
			if(req.getInvoiceType()==0) {
				communityReceipt = communityReceiptDao.get(req.getReceiptId());
				//按不同税率，分出不同实收项
				Map<String,Map<Integer,List<CommunityReceiptReceivablesEntity>>> map = new HashMap<>();
				if (null != communityReceipt) {
					estate = communityReceipt.getEstate();
					Map<Integer, List<CommunityReceiptReceivablesEntity>> collect = 
							(Map<Integer, List<CommunityReceiptReceivablesEntity>>)communityReceipt.
							getReceiptReceivablesList().parallelStream().
							collect(groupingBy(CommunityReceiptReceivablesEntity::getId));
					String[] receiptReceivablesIds = req.getReceiptReceivablesIdList().split(",");
					for (String receiptReceivablesid : receiptReceivablesIds) {
						CommunityReceiptReceivablesEntity receiptReceivables =  collect.get(
								Integer.parseInt(receiptReceivablesid)).get(0);
						Map<Integer,List<CommunityReceiptReceivablesEntity>> rrMap = null;
						CommunityInviceInfoForm inviceInfo = null;
						if(StringUtils.isNotEmpty(receiptReceivables.
								getReceivables().getPayItem().getInviceInfo())) {
							try {
								inviceInfo =  mapper.readValue(receiptReceivables.
										getReceivables().getPayItem().getInviceInfo(),
										CommunityInviceInfoForm.class);
							} catch (JsonMappingException e) {
								e.printStackTrace();
							} catch (JsonProcessingException e) {
								e.printStackTrace();
							}
						}else {
			                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
									res.setRetInfo("请重新检查收费项目“"+receiptReceivables.
											getReceivables().getPayItem().getItemsName()+"”的发票信息！");
									return res;
						}
						if(inviceInfo==null || StringUtils.isEmpty(inviceInfo.getTaxProductName()) 
								|| StringUtils.isEmpty(inviceInfo.getTaxRate())  
								|| StringUtils.isEmpty(inviceInfo.getTaxCode())) {
			                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo("请重新检查收费项目“"+receiptReceivables.
									getReceivables().getPayItem().getItemsName()+"”的发票信息！");
							return res;
						}
						if(!collect.containsKey(Integer.parseInt(receiptReceivablesid))){
			                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
							return res;
						}else {
							List<CommunityReceiptReceivablesEntity> receiptReceivablesList = null;
							if(map.containsKey(inviceInfo.getTaxRate())) {//分出不同税率的应收项，相同税率的开在同一发票上
								rrMap = map.get(inviceInfo.getTaxRate());
								if(rrMap.containsKey(receiptReceivables.getReceivables().getPayItem().getId())) {
									receiptReceivablesList = rrMap.get(receiptReceivables.getReceivables().getPayItem().getId());
									receiptReceivablesList.add(receiptReceivables);
									rrMap.put(receiptReceivables.getReceivables().getPayItem().getId(), receiptReceivablesList);
								}else {
									receiptReceivablesList = new ArrayList<>();
									receiptReceivablesList.add(receiptReceivables);
									rrMap.put(receiptReceivables.getReceivables().getPayItem().getId(), receiptReceivablesList);
								}
							}else {
								rrMap = new HashMap<>();
								receiptReceivablesList = new ArrayList<>();
								receiptReceivablesList.add(receiptReceivables);
								rrMap.put(receiptReceivables.getReceivables().getPayItem().getId(), receiptReceivablesList);
							}
							map.put(inviceInfo.getTaxRate(), rrMap);
							//receiptReceivablesList.add(collect.get(Integer.parseInt(receiptReceivablesid)).get(0));
							//communityInvoice.getReceiptReceivablesList().add(collect.get(Integer.parseInt(receiptReceivablesid)).get(0));
						}
						if(req.getInvoiceType()==0 && receiptReceivables.getInvoiceState() != 0) {
			                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo("请重新核对实收项；已开或正在开的的实收项不能再开发票！");
							return res;
						}else if(req.getInvoiceType()==1 && (receiptReceivables.getInvoiceState() == 0 || 
								receiptReceivables.getInvoiceState() == 2)){
			                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo("请重新核对实收项；未开或已红冲的实收项不能再开！");
							return res;
						}
					}
				} else {
	                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				String buyerTaxCode = "";
				String buyerName = "";
				String buyerAddress = "";
				String buyerBankAccount = "";
				String buyerEmail = "";
				String buyerMobile = "";
				for(CommunityMemberPropertyEntity memberProperty : estate.getMemberPropertyList()) {
					if(memberProperty.getIsCurrentMember()==1 && memberProperty.getMemberType() == 0) {
						buyerTaxCode = memberProperty.getPaytaxNo();
						buyerName = memberProperty.getBuyersName();
						buyerAddress= memberProperty.getBuyersAddress();
						buyerBankAccount = memberProperty.getBuyersBankAccount();
						buyerEmail = memberProperty.getBuyerEmail();
					}
				}
				if(StringUtils.isEmpty(buyerName)) {
	                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("“购货单位名称”不能为空！请检查相关信息！");
					return res;
				}
				buyerMobile = StringUtils.isNotEmpty(req.getBuyerMobile()) ? req.getBuyerMobile() : buyerMobile;
				buyerEmail = StringUtils.isNotEmpty(req.getBuyerEmail()) ? req.getBuyerEmail() : buyerEmail;
				
				for(String key : map.keySet()) {
					BigDecimal amount = new BigDecimal(0);//去税总金额
					BigDecimal taxAmount = new BigDecimal(0);//总税额
					BigDecimal totalAmount = new BigDecimal(0);//总金额（amount+totalAmount）
					InvoiceReq invoiceReq = new InvoiceReq();
					CommunityInvoiceEntity communityInvoice = new CommunityInvoiceEntity();
					invoiceReq = new InvoiceReq();
					invoiceReq.setTaxationMode("");
					invoiceReq.setInvoiceKindCode(StringUtils.isNoneBlank(req.getInvoiceKindCode()) ? 
							req.getInvoiceKindCode() : "82");
					invoiceReq.setBuyerTaxCode(buyerTaxCode);
					invoiceReq.setBuyerName(buyerName);
					invoiceReq.setBuyerBusinessAddress(buyerAddress);
					invoiceReq.setBuyerMobile(buyerMobile);
					invoiceReq.setBuyerBankAccount(buyerBankAccount);
					invoiceReq.setBuyerEmail(buyerEmail);
					invoiceReq.setSpecialFlag("");
					invoiceReq.setRealEstateLease(req.getRealEstateLease());
					Object userObj = getPrincipal(true);
					if (userObj instanceof PlatformUserEntity) {
						PlatformUserEntity shiroPlatformUser = (PlatformUserEntity) userObj;
						invoiceReq.setDrawer(shiroPlatformUser.getName());
					} else if (userObj instanceof PrincipalModel) {
						PrincipalModel shiroPlatformUser = (PrincipalModel) userObj;
						invoiceReq.setDrawer(shiroPlatformUser.getName());
					}
				
//					List<CommunityReceiptReceivablesEntity> receiptReceivablesList = map.get(key);
//					 Map<Integer,Map<String,Object>> compoundMap = getCompoundReceivables(receiptReceivablesList);	
					//for(CommunityReceiptReceivablesEntity receiptReceivables : receiptReceivablesList) {
					Map<Integer,List<CommunityReceiptReceivablesEntity>> rrMap = map.get(key);
					int i=0;
					for(Integer id : rrMap.keySet()) {
						List<CommunityReceiptReceivablesEntity> list = rrMap.get(id);
						CommunityGoodsForm goods = new CommunityGoodsForm();
						goods.setLineNo((i+1)+"");
						i++;
						boolean state = true;
						BigDecimal goodsAmount = new BigDecimal(0);//去税总金额
						BigDecimal goodsTaxAmount = new BigDecimal(0);//总税额
						BigDecimal goodsTotalAmount = new BigDecimal(0); 
						if(list.size()>1) {
							Collections.sort(list, (p1, p2) -> ((CommunityReceiptReceivablesEntity) p1).getReceivables().getReceivableDate()
									.compareTo(((CommunityReceiptReceivablesEntity) p2).getReceivables().getReceivableDate()));
						}
						
						for(CommunityReceiptReceivablesEntity receiptReceivables : list) {
							if(state) {
								CommunityInviceInfoForm inviceInfo = null;
								try {
									inviceInfo =  mapper.readValue(receiptReceivables.
											getReceivables().getPayItem().getInviceInfo(), 
											CommunityInviceInfoForm.class);
								} catch (JsonMappingException e) {
									e.printStackTrace();
								} catch (JsonProcessingException e) {
									e.printStackTrace();
								}
								goods.setGoodsName(inviceInfo.getTaxProductName());
								goods.setTaxKindCode(inviceInfo.getTaxCode());
								goods.setTaxRate(inviceInfo.getTaxRate());
								goods.setSaleUnit(StringUtils.isNotEmpty(inviceInfo.getInviceUnit()) ?
										inviceInfo.getInviceUnit() : "");
								goods.setCouponFlag(StringUtils.isNotEmpty(inviceInfo.getDiscountIdentify()) ? 
										inviceInfo.getDiscountIdentify():"" );
								if(StringUtils.isNotEmpty(inviceInfo.getDiscountIdentify()) && inviceInfo.getDiscountIdentify().equals("0")) {
									goods.setDutyFree("05");
								}
								goods.setCouponPolicy(StringUtils.isNotEmpty(inviceInfo.getDiscountDetails()) ? 
										inviceInfo.getDiscountDetails() :"");
								state = false;
							}
							//receiptReceivables.setInvoiceState(2);
							goodsTotalAmount = goodsTotalAmount.add(receiptReceivables.getCurrentAmount());
							totalAmount = totalAmount.add(receiptReceivables.getCurrentAmount());
							//goods.setTotalAmount(goodsTotalAmount.setScale(2, RoundingMode.HALF_UP).toString());
							communityInvoice.getReceiptReceivablesList().add(receiptReceivables);
						}
						
			    		 if(list.size() >0 && list.get(0).getReceivables().getReceivableDate().compareTo(list.get(list.size()-1).getReceivables().getReceivableDate())!=0) {
					    	 goods.setSpecName(getDateStr(DateUtil.formatShortFormat(list.get(0).getReceivables().getReceivableDate()))+"-"+
					    			 getDateStr(DateUtil.formatShortFormat(list.get(list.size()-1).getReceivables().getReceivableDate())));
			    		 }else {
			    			 goods.setSpecName(getDateStr(DateUtil.formatShortFormat(list.get(0).getReceivables().getReceivableDate())));
			    		 }
						goods.setTotalAmount(goodsTotalAmount.setScale(2, RoundingMode.HALF_UP).toString());
//						goodsAmount = goodsTotalAmount.divide(new BigDecimal(1).add(
//								new BigDecimal(key)),4, RoundingMode.HALF_UP)
//								.setScale(2, RoundingMode.HALF_UP);//goodsTotalAmount.subtract(goodsTaxAmount);
//						goodsTaxAmount = goodsAmount.multiply(new BigDecimal(key))
//								.setScale(2, RoundingMode.HALF_UP);

						goodsTaxAmount = (goodsTotalAmount.divide(new BigDecimal(1).add(
								new BigDecimal(key)),4, RoundingMode.HALF_UP)).multiply(new BigDecimal(key));
						goodsAmount = goodsTotalAmount.subtract(goodsTaxAmount)
								.setScale(2, RoundingMode.HALF_UP);
//						amount = amount.add(goodsAmount);
//						taxAmount = taxAmount.add(goodsTaxAmount);
						goods.setAmount(goodsAmount.setScale(2, RoundingMode.HALF_UP).toString());
						goods.setTaxAmount(goodsTaxAmount.setScale(2, RoundingMode.HALF_UP).toString());
						goods.setLineKind(0);
						goods.setTaxFlag("1");
						goods.setQty("");
						goods.setPrice(goodsTotalAmount.setScale(2, RoundingMode.HALF_UP).toString());
						invoiceReq.getGoodsList().add(goods);
						taxAmount = taxAmount.add(goodsTaxAmount.setScale(2, RoundingMode.HALF_UP));
						amount = amount.add(goodsAmount.setScale(2, RoundingMode.HALF_UP));
					}
//					taxAmount = (totalAmount.divide(new BigDecimal(1).add(
//							new BigDecimal(key)),4, RoundingMode.HALF_UP)).multiply(new BigDecimal(key));
//					amount = totalAmount.subtract(taxAmount).setScale(2, RoundingMode.HALF_UP);

					invoiceReq.setActType("0");
					invoiceReq.setAmount(amount.setScale(2, RoundingMode.HALF_UP).toString());
					invoiceReq.setTaxAmount(taxAmount.setScale(2, RoundingMode.HALF_UP).toString());
					invoiceReq.setTotalAmount(totalAmount.setScale(2, RoundingMode.HALF_UP).toString());
					String billCode =  getReqSerialNo();
					invoiceReq.setReqSerialNo(billCode);
					communityInvoice.setInvoiceSn(billCode);
					res = getResult(invoiceReq,"/invoice");
					if(res.getRet().equals( ResponseContext.RES_SUCCESS_CODE)) {
						for(Integer id : rrMap.keySet()) {
							List<CommunityReceiptReceivablesEntity> list = rrMap.get(id);
							for(CommunityReceiptReceivablesEntity receiptReceivables : list) {
								receiptReceivables.setInvoiceState(1);
							}
						}
						communityReceipt.setHaveInvoice(1);
						Map<String,String> resMap = new HashMap<>();
						resMap.put("invoiceDate", res.getInvoiceDate());
						resMap.put("redInfoNo", res.getRedInfoNo());
						resMap.put("redInfoId", res.getRedInfoId());
						String json="";
						try {
							json = mapper.writeValueAsString(resMap);
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
						communityInvoice.setExpand(json);
						communityInvoice.setState(EntityContext.RECORD_STATE_VALID);
						communityInvoice.setAgent(invoiceReq.getDrawer());
						communityInvoice.setBuyerAddre(invoiceReq.getBuyerBusinessAddress());
						communityInvoice.setBuyerBankAccount(invoiceReq.getBuyerBankAccount());
						communityInvoice.setBuyerCode(invoiceReq.getBuyerTaxCode());
						communityInvoice.setBuyerName(invoiceReq.getBuyerName());
						communityInvoice.setInvoiceType(req.getInvoiceType());
						communityInvoice.setInvoiceState(1);
						//communityInvoice.setPlatUrl(res.getPdfUrl());
						communityInvoice.setIsRedDashed(0);
						communityInvoice.setTaxAmount(taxAmount.setScale(2, RoundingMode.HALF_UP));
						communityInvoice.setTotalAmount(totalAmount.setScale(2, RoundingMode.HALF_UP));
						communityInvoice.setParentInvoice(parentInvoice);
						communityInvoice.setReceipt(communityReceipt);
						communityInvoice.setInvoiceNum(res.getInvoiceNo());
						if(StringUtils.isNotEmpty(res.getPdfUrl())) {
							communityInvoice.setPlatUrl(res.getPdfUrl());
							saveFile(res.getPdfUrl(),communityInvoice);
						}
						communityInvoiceDao.save(communityInvoice);
						
//						for(CommunityReceiptReceivablesEntity o : communityReceipt.getReceiptReceivablesList()){
//							if(o.getInvoiceState()==1) {
//								o.getReceipt().setHaveInvoice(1);
//								break;
//							}else {
//								o.getReceipt().setHaveInvoice(2);
//							}
//						}
						communityInvoice.setReturnMessage(res.getRetInfo());
					}

				}
			}else if(req.getInvoiceType()==2){//红冲申请
				RedInvoiceReq redInvoiceReq = new RedInvoiceReq();
				CommunityInvoiceEntity communityInvoice = new CommunityInvoiceEntity();
//				String billCode =  getReqSerialNo();
//				invoiceReq.setBillCode(billCode);
//				invoiceReq.setInvoiceType(req.getInvoiceType());
//				communityInvoice.setInvoiceSn(billCode);
				parentInvoice = communityInvoiceDao.get(req.getInvoiceId());
				if(null == parentInvoice) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}

				if(parentInvoice.getInvoiceType()==0 && parentInvoice.getInvoiceState()==1) {
					communityReceipt = parentInvoice.getReceipt();
					estate = communityReceipt.getEstate();
					redInvoiceReq.setRedReasonCode(StringUtils.isNotEmpty(req.getRedDashedReason()) ? req.getRedDashedReason():"01");
					redInvoiceReq.setOriginalInvoiceNo(parentInvoice.getInvoiceNum());
					redInvoiceReq.setBuyerTaxCode(parentInvoice.getBuyerCode());
					redInvoiceReq.setRedApplyType("1");
					redInvoiceReq.setInvoiceUserType("0");
					redInvoiceReq.setApplyUserType("0");
					
					Map<String,String> map = new HashMap<>();
					try {
						map =  mapper.readValue(parentInvoice.getExpand(), Map.class);
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
					redInvoiceReq.setOriginalInvoiceDate(map.get("invoiceDate"));
					
					res = getResult(redInvoiceReq,"/bWRedApply");
					if(res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
						Map<String,String> resMap = new HashMap<>();
						resMap.put("invoiceDate", res.getInvoiceDate());
						resMap.put("redInfoNo", res.getRedInfoNo());
						resMap.put("redInfoId", res.getRedInfoId());
						String json="";
						try {
							json = mapper.writeValueAsString(resMap);
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
						Object userObj = getPrincipal(true);
						if (userObj instanceof PlatformUserEntity) {
							PlatformUserEntity shiroPlatformUser = (PlatformUserEntity) userObj;
							communityInvoice.setAgent(shiroPlatformUser.getName());
						} else if (userObj instanceof PrincipalModel) {
							PrincipalModel shiroPlatformUser = (PrincipalModel) userObj;
							communityInvoice.setAgent(shiroPlatformUser.getName());
						}
						communityInvoice.setState(EntityContext.RECORD_STATE_VALID);
						communityInvoice.setBuyerCode(redInvoiceReq.getOriginalInvoiceNo());
						communityInvoice.setInvoiceType(req.getInvoiceType());
						communityInvoice.setInvoiceState(2);
						communityInvoice.setIsRedDashed(0);
						communityInvoice.setBuyerName(parentInvoice.getBuyerName());
						communityInvoice.setPlatUrl("");
						communityInvoice.setExpand(json);
						communityInvoice.setTaxAmount(parentInvoice.getTaxAmount());
						communityInvoice.setTotalAmount(parentInvoice.getTotalAmount());
						communityInvoice.setParentInvoice(parentInvoice);
						communityInvoice.setReceipt(communityReceipt);
						communityInvoiceDao.save(communityInvoice);
						communityInvoice.setReturnMessage(res.getRetInfo());
					}

				}else {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				}
			}else if(req.getInvoiceType()==1){
//				InvoiceReq invoiceReq = new InvoiceReq();
//				CommunityInvoiceEntity communityInvoice = communityInvoiceDao.get(req.getInvoiceId());
//				if(null == communityInvoice) {
//					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
//					return res;
//				}
//				if(communityInvoice.getInvoiceType()==2 && communityInvoice.getInvoiceState()==1) {
//					invoiceReq.setBillCode(communityInvoice.getInvoiceSn());
//					invoiceReq.setInvoiceType(req.getInvoiceType());
//					res = getResult(invoiceReq,"/invoice");
//					communityInvoice.setReturnMessage(res.getRetInfo());
//					communityInvoice.setInvoiceType(1);
//					communityInvoice.setInvoiceState(2);
//				}else {
//					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
//				}
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	@SuppressWarnings({ "unchecked", "unlikely-arg-type" })
	public Map<Integer,Map<String,Object>> getCompoundReceivables(List<CommunityReceiptReceivablesEntity> receiptReceivablesList){
		Map<Integer,Map<String,Object>> map = new HashMap<Integer,Map<String,Object>>();
		
		for(CommunityReceiptReceivablesEntity o : receiptReceivablesList) {
			List<CommunityReceiptReceivablesEntity> list = null;
			Map<String,Object> data = null;
			//预防收费项目为空的情况
			Integer payItemsId = null!=o.getReceivables().getPayItem() ?
					o.getReceivables().getPayItem().getId() : o.getReceivables().getId() * 300;
			if(map.containsKey(payItemsId)) {
				list =  (List<CommunityReceiptReceivablesEntity>) map.get("listes");
				list.add(o);
				data = map.get(payItemsId);
				data.put("listes", list);
				data.put("amount", o.getCurrentAmount().add((BigDecimal) data.get("amount")));
				if(o.getReceivables().getReceivableDate().before((Date) data.get("startReceivableDate"))) {
					data.put("startReceivableDate", o.getReceivables().getReceivableDate());
				}else if(o.getReceivables().getReceivableDate().after((Date) data.get("endReceivableDate"))) {
					data.put("endReceivableDate", o.getReceivables().getReceivableDate());
				}
				map.put(payItemsId, data);
			}else {
				data = new HashMap<String,Object>();
				list = new ArrayList<CommunityReceiptReceivablesEntity>();
				list.add(o);
				data.put("listes", list);
				data.put("payItemsName", o.getReceivables().getPayItemsName());
				data.put("startReceivableDate", o.getReceivables().getReceivableDate());
				data.put("endReceivableDate", o.getReceivables().getReceivableDate());
				data.put("inviceInfo", o.getReceivables().getPayItem().getInviceInfo());
				if(null!=o.getReceivables().getPayItem()) {
					if(null != o.getReceivables().getPayItem()) {
						if(o.getReceivables().getPayItem().getPayDate()!=0 &&
								o.getReceivables().getPayItem().getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_ALLOCATION 
								&& o.getReceivables().getPayItem().getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_BREAK &&
										o.getReceivables().getPayItem().getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_DEPOSIT) {
							data.put("unitPrice", 
									o.getReceivables().getPayItem().getPrice().toString()+
									(StringUtils.isNotEmpty(o.getReceivables().getPayItem().getPriceUnit()) ? 
											o.getReceivables().getPayItem().getPriceUnit() : ""));
						}else {
							data.put("unitPrice", "");
						}
					}
				}

				data.put("amount", o.getCurrentAmount());
				map.put(payItemsId, data);
			}
		}
		return map;
	}
	
	public InvoiceRes getResult(Object object,String interfaceName) {
		//InvoiceRes res = new InvoiceRes();
		String postResult="";
		String jsonstr="";
		try {
			jsonstr = mapper.writeValueAsString(object);
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}
		try {
			postResult = HttpClientUtil.jsonPost(communityContextInfo.getInvoiceUrl()+interfaceName, "UTF-8", jsonstr,
					null);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
		InvoiceRes invoiceRes = null;
		try {
			invoiceRes = mapper.readValue(postResult, InvoiceRes.class);
		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		if(null==invoiceRes) {
			invoiceRes = new InvoiceRes();
			invoiceRes.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
			invoiceRes.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
		}
		return invoiceRes;
	}
	
	public IResponse cancelRedDashedApplication(CommunityInvoiceReq req){
		InvoiceRes res = new InvoiceRes();
		if(null != req.getInvoiceId()) {
			CommunityInvoiceEntity communityInvoice = communityInvoiceDao.get(req.getInvoiceId());
			if(null == communityInvoice) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			if(communityInvoice.getInvoiceType()==2 && communityInvoice.getInvoiceState()==1) {
				InvoiceReq invoiceReq = new InvoiceReq();
				invoiceReq.setBillCode(communityInvoice.getInvoiceSn());
				res = getResult(invoiceReq,"/withdrawalRedRushApplication");
				communityInvoice.setInvoiceType(3);
				communityInvoice.setInvoiceState(2);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);	
		}
		return res;
	}
	
	public IResponse queryResult(CommunityInvoiceReq req){
		InvoiceRes res = new InvoiceRes();
		if(null != req.getInvoiceId()) {
			CommunityInvoiceEntity communityInvoice = communityInvoiceDao.get(req.getInvoiceId());
			if(null == communityInvoice) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			if(StringUtils.isNotEmpty(communityInvoice.getExpand())) {
			
				res = getResult(communityInvoice);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo("对不起，无此数据！");
			}
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);	
		}
		return res;
	}
	
	public InvoiceRes getResult(CommunityInvoiceEntity communityInvoice) {
		InvoiceRes res = new InvoiceRes();
		InvoiceQueryReq invoiceQueryReq = new InvoiceQueryReq();
		Map<String,String> map = new HashMap<>();
		try {
			map =  mapper.readValue(communityInvoice.getExpand(), Map.class);
		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		if(communityInvoice.getInvoiceType()==0 ) {
			invoiceQueryReq.setInvoiceNo(communityInvoice.getInvoiceNum());
			invoiceQueryReq.setQueryKind("0");
			invoiceQueryReq.setInvoiceDate(map.get("invoiceDate"));
			res = getResult(invoiceQueryReq,"/bWQuery");
			if(res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
				communityInvoice.setInvoiceState(1);
				communityInvoice.setInvoiceNum(res.getInvoiceNo());
				communityInvoice.setLastModifyTime(new Timestamp(new Date().getTime()));
				for(CommunityReceiptReceivablesEntity o : communityInvoice.getReceiptReceivablesList()) {
					o.setInvoiceState(1);
					o.getReceipt().setHaveInvoice(1);
				}
				if(StringUtils.isNotEmpty(res.getPdfUrl())) {
					communityInvoice.setPlatUrl(res.getPdfUrl());
					saveFile(res.getPdfUrl(),communityInvoice);
				}

				communityInvoice.setReturnMessage(res.getRetInfo());
			}else if(!res.getRetInfo().contains("身份认证已超")){
				communityInvoice.setInvoiceState(0);
				communityInvoice.setReturnMessage(res.getRetInfo());
				communityInvoice.setLastModifyTime(new Timestamp(new Date().getTime()));
				Map<Integer, List<CommunityReceiptReceivablesEntity>> collect = 
						(Map<Integer, List<CommunityReceiptReceivablesEntity>>)communityInvoice.
						getReceiptReceivablesList().parallelStream().
						collect(groupingBy(CommunityReceiptReceivablesEntity::getId));
				boolean state = true;
				for(CommunityReceiptReceivablesEntity o : communityInvoice.getReceipt().getReceiptReceivablesList()){
					if(collect.containsKey(o.getId())) {
						o.setInvoiceState(0);
					}
					if(o.getInvoiceState()==1) {
						o.getReceipt().setHaveInvoice(1);
						break;
					}else if(o.getInvoiceState()==2){
						o.getReceipt().setHaveInvoice(2);
						state = false;
					}else if(state){
						o.getReceipt().setHaveInvoice(0);
					}
				}
			}else {
				
			}
		}else if(communityInvoice.getInvoiceType()== 2  || (communityInvoice.getInvoiceType()== 1 
				&& StringUtils.isEmpty(communityInvoice.getLocalUrl()))){
			invoiceQueryReq.setReqSerialNo(map.get("redInfoId"));
			invoiceQueryReq.setQueryKind("2");
			res = getResult(invoiceQueryReq,"/bWQuery");
			if(res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
				communityInvoice.setInvoiceType(1);
				communityInvoice.setInvoiceState(1);
				communityInvoice.setPlatUrl(res.getPdfUrl());
				communityInvoice.setInvoiceNum(res.getInvoiceNo());
				map.put("invoiceDate", res.getInvoiceDate());
				String json="";
				try {
					json = mapper.writeValueAsString(map);
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				communityInvoice.setExpand(json);
				communityInvoice.getParentInvoice().setIsRedDashed(1);
				communityInvoice.setLastModifyTime(new Timestamp(new Date().getTime()));
				
				if(null!=communityInvoice.getReceipt()) {
					StringBuilder hql = new StringBuilder("select distinct a from CommunityReceiptReceivablesEntity "
							+ "a where a.receipt.id="+communityInvoice.getReceipt().getId());
					List<CommunityReceiptReceivablesEntity> list = communityReceiptReceivablesDao.getListByHql(hql.toString(), "");
					
					Map<Integer, List<CommunityReceiptReceivablesEntity>> collect = 
							(Map<Integer, List<CommunityReceiptReceivablesEntity>>)communityInvoice.getReceipt()
							.getReceiptReceivablesList().parallelStream().collect(groupingBy(CommunityReceiptReceivablesEntity::getId));
					Integer haveInvoice = 0;
					for(CommunityReceiptReceivablesEntity o :  list){
						if(collect.containsKey(o.getId())) {
							o.setInvoiceState(0);
						}
						if(haveInvoice!=2 && o.getInvoiceState()==1) {//存在已开票
							haveInvoice = 1;
						}else if(o.getInvoiceState()==2) {//存在正在开票
							haveInvoice = 2;
						}
					}
					communityInvoice.getReceipt().setHaveInvoice(haveInvoice);
					if(StringUtils.isNotEmpty(res.getPdfUrl())) {
						saveFile(res.getPdfUrl(),communityInvoice);
					}
				}
			}else {
				//communityInvoice.setInvoiceState(0);
				 res.setRet(ResponseContext.RES_SUCCESS_CODE);
				 res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}
		}
		return res;
	}
	
	public void saveFile(String pdfUrl,CommunityInvoiceEntity communityInvoice) {
		if(StringUtils.isNotEmpty(pdfUrl)) {
	 		Date date = new Date();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
			String today = sdf.format(date);
			
			String name = communityInvoice.getReceipt().getEstate().getUnitCode();
			for(CommunityMemberPropertyEntity memberProperty : communityInvoice.getReceipt().getEstate().getMemberPropertyList()) {
				if(memberProperty.getMemberType()==0 && memberProperty.getIsCurrentMember()==1) {
					name = name+memberProperty.getMember().getUserName();
				}
			}
			String dir = contextInfo.getAssetFilePath()+ File.separator +"invoicePdf"+ 
					File.separator + today+ File.separator;
			//String fileName = getFileName(dir,name,0); 
			String fileName = name+"("+communityInvoice.getInvoiceSn()+")";
			boolean mark = FileUtil.getInternetFile(pdfUrl,dir,fileName+".pdf");
			File file = new File(dir+File.separator +fileName+".pdf");
	        
	        if (file.exists()) {
	            long fileSize = file.length();
	            if(fileSize>10240 && mark) {
					communityInvoice.setLocalUrl(contextInfo.getAssetFileUrl()+ "/" +"invoicePdf"+ 
							"/" + today+ "/"+fileName+".pdf");
	            }else {
	            	communityInvoice.setInvoiceState(2);
	            	file.delete();
	            }
	        } else {
	        	communityInvoice.setInvoiceState(2);
	        }

		}
	}
	
	public String getFileName(String url ,String name,int i) {
		File file = null;
		if(i==0) {
			file = new File(url+name+".pdf");
			if(file.exists()) {
				i++;
				return getFileName(url,name,i);
			}
		}else {
			file = new File(url+name+i+".pdf");
			if(file.exists()) {
				i++;
				return getFileName(url,name,i);
			}
			return name+i;
		}

		return name;
	}
	
	public void communityQueryInvoiceResultTask() {
		List<CommunityInvoiceEntity> list = communityInvoiceDao.getListByHql(""
				+ "select a from CommunityInvoiceEntity a where (a.invoiceState=2) "
				+ "or (a.invoiceState=1 and platUrl='' and (invoiceType=0 or invoiceType=1))  "
				+ "OR (a.invoiceState=1 AND (platUrl IS NULL or platUrl=''))", "");
		if(null != list) {
			for(CommunityInvoiceEntity o : list) {
				if(StringUtils.isNotEmpty(o.getExpand())) {
					getResult(o);
				}
				
			}
			list.clear();
		}
//		list = communityInvoiceDao.getListByHql(""
//				+ "select c from CommunityInvoiceEntity c inner join c.receipt a  where  c.invoiceType=0 AND "
//				+ "c.isRedDashed=0 AND a.haveInvoice=0 AND c.invoiceState=1 ", "");
//		if(null != list) {
//			for(CommunityInvoiceEntity o : list) {
//				for(CommunityReceiptReceivablesEntity p : o.getReceiptReceivablesList()) {
//					p.setInvoiceState(1);
//				}
//				o.getReceipt().setHaveInvoice(1);
//			}
//		}
	}
	
	public  String getReqSerialNo() {
		Integer lastValue = generateSnLastValue("KFP");
		String result = DateUtil.formatByStyle(new Date(), "yyyyMMdd")  + 
				CodeUtil.getCode(6, lastValue.toString())
				+(Math.random()*9000+1000);//加四位随机数
		return result;
	}
	
	
	public IResponse generateXml(CommunityInvoiceReq req,HttpServletResponse response) {
		GenerateXmlRes res = new GenerateXmlRes();
		req.setGenerationMode(0);
		if (null!=req.getGenerationMode() && null != req.getReceiptId()) {
			CommunityReceiptEntity communityReceipt = communityReceiptDao.get(req.getReceiptId());
			if (null != communityReceipt) {
				Map<String,List<Map<String,String>>> goodsMap = new HashMap<>();
				Map<String,String> receiptMap = new HashMap<>();
				for(CommunityMemberPropertyEntity memberProperty : communityReceipt.getEstate().getMemberPropertyList()) {
					if(memberProperty.getIsCurrentMember()==1 && memberProperty.getMemberType() == 0) {
						receiptMap.put("Gfsh", memberProperty.getPaytaxNo());
						receiptMap.put("Gfmc", memberProperty.getBuyersName());
						receiptMap.put("Gfdzdh", memberProperty.getBuyersAddress());
						receiptMap.put("Gfyhzh", memberProperty.getBuyersBankAccount());
					}
				}
				String version="";
				List<DictionaryDataEntity> dictionaryDataList = dictionaryDataDao
						.getListByHql("select distinct a from DictionaryDataEntity a "
								+ " inner join a.dictionary  b where b.directoryCode='FPZD' and a.state=1", "");
				if (null != dictionaryDataList && dictionaryDataList.size() > 0) {
					version = dictionaryDataList.get(0).getDataKey();
				}else {
					version= StringUtils.isNotEmpty(req.getSpbmbbh()) ? req.getSpbmbbh(): "48.0";
				}
				receiptMap.put("Djh", communityReceipt.getReceiptCode());
				if(!receiptMap.containsKey("Gfmc")) {
	                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("“购货单位名称”不能为空！请检查相关信息！");
					return res;
				}
				Map<String,Map<Integer,List<CommunityReceiptReceivablesEntity>>> map = new HashMap<>();
				for (CommunityReceiptReceivablesEntity receiptReceivables : communityReceipt.getReceiptReceivablesList()) {
					CommunityInviceInfoForm inviceInfo = null;
					Map<Integer,List<CommunityReceiptReceivablesEntity>> rrMap = null;
					if(StringUtils.isNotEmpty(receiptReceivables.
							getReceivables().getPayItem().getInviceInfo())) {
						try {
							inviceInfo =  mapper.readValue(receiptReceivables.
									getReceivables().getPayItem().getInviceInfo(),
									CommunityInviceInfoForm.class);
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
					}else {
		                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo("请重新检查收费项目“"+receiptReceivables.
								getReceivables().getPayItem().getItemsName()+"”的发票信息！");
						return res;
					}
					if(inviceInfo==null || StringUtils.isEmpty(inviceInfo.getTaxProductName()) 
							|| StringUtils.isEmpty(inviceInfo.getTaxRate())  
							|| StringUtils.isEmpty(inviceInfo.getTaxCode())) {
		                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo("请重新检查收费项目“"+receiptReceivables.
								getReceivables().getPayItem().getItemsName()+"”的发票信息！");
						return res;
					}
					List<CommunityReceiptReceivablesEntity> receiptReceivablesList = null;
					if(req.getGenerationMode()==0) {//只生成一个XML
						if(map.containsKey("100")) {
							rrMap = map.get("100");
							if(rrMap.containsKey(receiptReceivables.getReceivables().getPayItem().getId())) {
								receiptReceivablesList = rrMap.get(receiptReceivables.getReceivables().getPayItem().getId());
								receiptReceivablesList.add(receiptReceivables);
								rrMap.put(receiptReceivables.getReceivables().getPayItem().getId(), receiptReceivablesList);
							}else {
								receiptReceivablesList = new ArrayList<>();
								receiptReceivablesList.add(receiptReceivables);
								rrMap.put(receiptReceivables.getReceivables().getPayItem().getId(), receiptReceivablesList);
							}
						}else {
							rrMap = new HashMap<>();
							receiptReceivablesList = new ArrayList<>();
							receiptReceivablesList.add(receiptReceivables);
							rrMap.put(receiptReceivables.getReceivables().getPayItem().getId(), receiptReceivablesList);
						}
						map.put("100", rrMap);
					}else {//根据税率生成不同XML
						if(map.containsKey(inviceInfo.getTaxRate())) {
							rrMap = map.get(inviceInfo.getTaxRate());
							if(rrMap.containsKey(receiptReceivables.getReceivables().getPayItem().getId())) {
								receiptReceivablesList = rrMap.get(receiptReceivables.getReceivables().getPayItem().getId());
								receiptReceivablesList.add(receiptReceivables);
								rrMap.put(receiptReceivables.getReceivables().getPayItem().getId(), receiptReceivablesList);
							}else {
								receiptReceivablesList = new ArrayList<>();
								receiptReceivablesList.add(receiptReceivables);
								rrMap.put(receiptReceivables.getReceivables().getPayItem().getId(), receiptReceivablesList);
							}
						}else {
							rrMap = new HashMap<>();
							receiptReceivablesList = new ArrayList<>();
							receiptReceivablesList.add(receiptReceivables);
							rrMap.put(receiptReceivables.getReceivables().getPayItem().getId(), receiptReceivablesList);
						}
						map.put(inviceInfo.getTaxRate(), rrMap);
					}
				}
				for(String key : map.keySet()) {
					Map<Integer,List<CommunityReceiptReceivablesEntity>> rrMap = map.get(key);
					List<Map<String,String>> sphList = null;
					if(goodsMap.containsKey(key)) {
						sphList = goodsMap.get(key);
					}else {
						sphList = new ArrayList<>();
					}
					for(Integer id : rrMap.keySet()) {
						List<CommunityReceiptReceivablesEntity> list = rrMap.get(id);

						boolean state = true;
						if(list.size()>1) {
							Collections.sort(list, (p1, p2) -> ((CommunityReceiptReceivablesEntity) p1).getReceivables().getReceivableDate()
									.compareTo(((CommunityReceiptReceivablesEntity) p2).getReceivables().getReceivableDate()));
						}
						BigDecimal goodsAmount = new BigDecimal(0);//去税总金额
						BigDecimal goodsTaxAmount = new BigDecimal(0);//总税额
						BigDecimal goodsTotalAmount = new BigDecimal(0);//总金额
						String taxRate = "";
						Map<String,String> sphMap = new HashMap<>();
						for(CommunityReceiptReceivablesEntity receiptReceivables : list) {
							goodsTotalAmount = goodsTotalAmount.add(receiptReceivables.getCurrentAmount());
							if(state) {
								CommunityInviceInfoForm inviceInfo = null;
								try {
									inviceInfo =  mapper.readValue(receiptReceivables.
											getReceivables().getPayItem().getInviceInfo(), 
											CommunityInviceInfoForm.class);
								} catch (JsonMappingException e) {
									e.printStackTrace();
								} catch (JsonProcessingException e) {
									e.printStackTrace();
								}
								state = false;
								taxRate = inviceInfo.getTaxRate();
								sphMap.put("Spmc", inviceInfo.getTaxProductName());
								sphMap.put("Spbm", inviceInfo.getTaxCode().toString());
								sphMap.put("Jldw",  StringUtils.isNotEmpty(inviceInfo.getInviceUnit()) ? inviceInfo.getInviceUnit():"");
							}
						}
			    		if(list.size() >0 && list.get(0).getReceivables().getReceivableDate().compareTo(list.get(list.size()-1).getReceivables().getReceivableDate())!=0) {
			    			sphMap.put("Ggxh",getDateStr(DateUtil.formatShortFormat(list.get(0).getReceivables().getReceivableDate()))+"-"+
			    					getDateStr(DateUtil.formatShortFormat(list.get(list.size()-1).getReceivables().getReceivableDate())));
			    		}else {
			    			sphMap.put("Ggxh",getDateStr(DateUtil.formatShortFormat(list.get(0).getReceivables().getReceivableDate())));
			    		}
						sphMap.put("Qyspbm", "");
						sphMap.put("Syyhzcbz", "0");
						sphMap.put("Lslbz", "");
						sphMap.put("Yhzcsm", "");
						sphMap.put("Sl", "1");
						
						goodsTaxAmount  = goodsTotalAmount.divide(new BigDecimal(1).add(
								new BigDecimal(taxRate)),4, RoundingMode.HALF_UP).multiply(new BigDecimal(taxRate))
								.setScale(2, RoundingMode.HALF_UP);
						goodsAmount = goodsTotalAmount.subtract(goodsTaxAmount)
								.setScale(2, RoundingMode.HALF_UP);
						
						sphMap.put("Je", goodsAmount.toString());
						sphMap.put("Dj", goodsAmount.toString());
						
						
						sphMap.put("Slv", taxRate);
						sphMap.put("Kce", "0");
						sphMap.put("Se", goodsTaxAmount.toString());
						sphList.add(sphMap);
					}
					goodsMap.put(key, sphList);
				} 
				//receiptMap.put("Spxx", goodsMap);
				receiptMap.put("Bz", StringUtils.isNotEmpty(req.getComment()) ? req.getComment(): "");
				receiptMap.put("Spbmbbh", version);
				receiptMap.put("Hsbz", StringUtils.isNotEmpty(req.getHsbz()) ? req.getHsbz(): "0");
				receiptMap.put("Sgbz", StringUtils.isNotEmpty(req.getSgbz()) ? req.getSgbz(): "0");
				receiptMap.put("Fhr", StringUtils.isNotEmpty(req.getChecker()) ? req.getChecker(): "杨惠霞");
				receiptMap.put("Skr", StringUtils.isNotEmpty(req.getPayee()) ? req.getPayee(): "邓敏贞");
				
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
				String today = sdf.format(new Date());
				String dir = contextInfo.getAssetFilePath() + File.separator + "temp" + File.separator + today
						+ File.separator;
				FileUtil.createMultilayerFile(dir);
				
				SimpleDateFormat format = new SimpleDateFormat("yyyy/MM");
				Date date = new Date();
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(date);
				calendar.add(Calendar.MONTH,-1);
				date = calendar.getTime();
				String lastMonth = format.format(date);
				String lastDir = contextInfo.getAssetFilePath() + File.separator + "temp" + File.separator + lastMonth
						+ File.separator;
				if(new File(lastDir).exists()) {//删除上个月所有临时文件
					FileUtil.deleteFolder(new File(lastDir));
				}
				
				createXml(receiptMap, goodsMap,req.getVersion(),response,dir);
//				res.setZipUrl(contextInfo.getAssetFileUrl()+ "/" +"temp"+ 
//						"/" + today+ "/"+receiptMap.get("Djh")+".zip");
				res.setZipUrl(contextInfo.getAssetFileUrl()+ "/" +"temp"+ 
						"/" + today+ "/"+receiptMap.get("Djh")+".xml");
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				communityReceipt.setHaveInvoice(1);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
		}else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		return res;
	}
	
	public String getDateStr(String date) {
		String dateStr = date.replaceAll("-", ".");
		return dateStr.substring(0, dateStr.length()-3);
	}
	
	public  String replaceChinese(String source, String replacement){
		String reg = "[\u4e00-\u9fa5]";
		Pattern pat = Pattern.compile(reg);
		Matcher mat=pat.matcher(source);
		String repickStr = mat.replaceAll(replacement);
		return repickStr;

	}
    public  void createXml(Map<String,String> receiptMap,Map<String,
    		List<Map<String,String>>> goodsMap,String version,HttpServletResponse response,String path){

    	/*ZipOutputStream zipOut=null;
		try {
			zipOut = new ZipOutputStream(new FileOutputStream(path+File.separator+receiptMap.get("Djh")+".zip"));
		} catch (FileNotFoundException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}*/
    	//DataOutputStream dataOs = null;
        try {
            for(String key : goodsMap.keySet()) {
            	Document document = XmlUtil.createDocument();
	            Element kp = XmlUtil.rootElement(document, "Kp");
	            XmlUtil.addAttrtoElement(document, kp, "Version", (StringUtils.isNotEmpty(version) ? version: "2.0"));
	            Element fpxx = XmlUtil.docCreateChildElement(document, kp,"Fpxx");
	            XmlUtil.addAttrtoElement(document, fpxx, "Zsl", "1");
	            Element fpsj = XmlUtil.docCreateChildElement(document, fpxx,"Fpsj");
	            Element fp = XmlUtil.docCreateChildElement(document, fpsj,"Fp");
	            for(String k : receiptMap.keySet()) {
	            	if(k.contains("Djh")) {
	            		StringBuilder code = new StringBuilder();
	            		if(receiptMap.get(k).startsWith("XTSJ")) {
	            			code.append("100");
	            		}else if(receiptMap.get(k).startsWith("QTSJ")) {
	            			code.append("200");
	            		}else if(receiptMap.get(k).startsWith("YHSJ")|| receiptMap.get(k).startsWith("YHSK")) {
	            			code.append("300");
	            		}else if(receiptMap.get(k).startsWith("WXSJ")) {
	            			code.append("400");        			
	            		}else if(receiptMap.get(k).startsWith("ALSJ")) {
	            			code.append("500");
						}else {
							code.append("600");
						}
	            		String strNew = receiptMap.get(k).replaceAll("[a-zA-Z]","");
	            		//替换所有汉字
	            		code.append(replaceChinese(strNew,"").replaceAll("-", "").replaceAll("_", ""));
	            		XmlUtil.addAttrtoElement(document, fp, k, code.toString());
	            	}else {
	            		XmlUtil.addAttrtoElement(document, fp, k, (StringUtils.isNotEmpty(receiptMap.get(k)) ? receiptMap.get(k) : ""));
	            	}
	            	
	            }
	            List<Map<String,String>> sphList = goodsMap.get(key);
	            Element spxx = XmlUtil.docCreateChildElement(document, fp,"Spxx");
	            int i=1;
	            for(Map<String,String> map : sphList) {
	            	Element sph = XmlUtil.docCreateChildElement(document, spxx,"Sph");
	            	boolean state = true;
		            for(String k : map.keySet()) {
		            	if(state) {
		            		XmlUtil.addAttrtoElement(document, sph, "Xh", (i+""));
		            		state = false;
		            		i++;
		            	}
		            	XmlUtil.addAttrtoElement(document, sph, k, (StringUtils.isNotEmpty(map.get(k)) ? map.get(k) : ""));
		            }
	            }

		        TransformerFactory tf = TransformerFactory.newInstance();
		        Transformer t = tf.newTransformer();
		        t.setOutputProperty(OutputKeys.ENCODING, "GBK");//指定编码级别
		        t.setOutputProperty(OutputKeys.INDENT, "yes");
		        ByteArrayOutputStream bos = new ByteArrayOutputStream();
		        t.transform(new DOMSource(document), new StreamResult(bos));
		       
		        //String fileName = key.equals("100") ? receiptMap.get("Djh")+".xml" : receiptMap.get("Djh")+"_"+key+".xml";
		        String fileName = receiptMap.get("Djh")+".xml" ;
		        saveXml(path+File.separator+fileName,kp,"GBK");
		        //ZipEntry entry = new ZipEntry(fileName);
		        // 将entry加入到zipOut中。
		        //zipOut.putNextEntry(entry);
		        //zipOut.write(bos.toByteArray());
            }
            /*zipOut.closeEntry();
            zipOut.flush();
            zipOut.close();*/
//	        dataOs = new DataOutputStream(zipOut);
//	        dataOs.close();
//            
//            byte[] data = outputStream.toByteArray();
//            
//            response.reset();  
//            response.setHeader("Content-Disposition", "attachment; filename=\""+receiptMap.get("Djh")+".zip\"");  
//            response.addHeader("Content-Length", "" + data.length);  
//            response.setContentType("application/octet-stream; charset=UTF-8");  
//            IOUtils.write(data, response.getOutputStream());  
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    

	public  void saveXml(final String fileName, final Node node,
			String encoding) throws FileNotFoundException, TransformerException {
		writeXml(new FileOutputStream(fileName), node, encoding);
    }	
	

	private  void writeXml(OutputStream os, Node node, String encoding)
			throws TransformerException {
		TransformerFactory transFactory = TransformerFactory.newInstance();
		Transformer transformer = transFactory.newTransformer();
		transformer.setOutputProperty("indent", "yes");
		transformer.setOutputProperty(OutputKeys.ENCODING, encoding);
 
		DOMSource source = new DOMSource();
		source.setNode(node);
		StreamResult result = new StreamResult();
		result.setOutputStream(os);
 
		transformer.transform(source, result);

	}

   

    public IResponse getCommunityInvoiceList(CommunityInvoiceReq req) {
    	GetCommunityInvoiceListRes res = new GetCommunityInvoiceListRes();
		Page<CommunityInvoiceEntity> page = new Page<CommunityInvoiceEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityInvoiceEntity a where 1=1 ");
		hql.append(null != req.getReceiptId() ? " and a.receipt.id="+req.getReceiptId():"")
			.append(null!=req.getInvoiceType() ? " and a.invoiceType="+req.getInvoiceType():"")
			.append(null!=req.getInvoiceState() ? " and a.invoiceState="+req.getInvoiceState():"")
			.append(StringUtils.isNotEmpty(req.getInvoiceSn()) ? " and a.invoiceSn like'%"+req.getInvoiceSn()+"%'":"");
		hql.append(" ORDER BY a.id desc");
		page = communityInvoiceDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		page.getResultList().forEach(o -> {
			CommunityInvoiceForm invoiceForm = new CommunityInvoiceForm();
			invoiceForm.setAgent(o.getAgent());
			invoiceForm.setBuyerAddre(o.getBuyerAddre());
			invoiceForm.setBuyerBankAccount(o.getBuyerBankAccount());
			invoiceForm.setBuyerCode(o.getBuyerCode());
			invoiceForm.setBuyerName(o.getBuyerName());
			invoiceForm.setInvoiceId(o.getId());
			invoiceForm.setInvoiceCode(o.getInvoiceCode());
			invoiceForm.setInvoiceNum(o.getInvoiceNum());
			invoiceForm.setInvoiceSn(o.getInvoiceSn());
			invoiceForm.setInvoiceState(o.getInvoiceState());
			invoiceForm.setInvoiceType(o.getInvoiceType());
			invoiceForm.setLocalUrl(o.getLocalUrl());
			invoiceForm.setPlatUrl(o.getPlatUrl());
			invoiceForm.setRedDashedReason(o.getRedDashedReason());
			invoiceForm.setReturnCode(o.getReturnCode());
			invoiceForm.setReturnMessage(o.getReturnMessage());
			invoiceForm.setTaxAmount(o.getTaxAmount().toString());
			invoiceForm.setTotalAmount(o.getTotalAmount().toString());
			invoiceForm.setCreateTime(sdf.format(o.getCreateTime()));
			invoiceForm.setLastModifyTime(sdf.format(o.getLastModifyTime()));
			invoiceForm.setIsRedDashed(o.getIsRedDashed());

			res.getInvoiceList().add(invoiceForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
    }

    public IResponse  getbWSms() {
    	return bWSms(new HashMap<>(),"getbWSmsVerify");
    	
    }
    public IResponse verifyBWSms(CommunityInvoiceReq req){
		GenericResponse res = new GenericResponse();
		if(StringUtils.isNotEmpty(req.getVerifyCode())) {
			Map<String,String> map = new HashMap<>();
			map.put("verifyCode", req.getVerifyCode());
			return bWSms(map,"bWSmsCheck");
		}else {
			 res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			 res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
    }

    
    public IResponse bWSms(Map<String,String> map,String interfaceName) {
		String postResult="";
		String jsonstr="";
		try {
			jsonstr = mapper.writeValueAsString(map);
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}
		try {
			postResult = HttpClientUtil.jsonPost(communityContextInfo.getInvoiceUrl()+interfaceName, "UTF-8", jsonstr,
					null);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
		GenericResponse res = null;
		try {
			res = mapper.readValue(postResult, GenericResponse.class);
		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		if(null==res) {
			res = new GenericResponse();
			res.setRet(ResponseContext.RES_INVOICE_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_INVOICE_ERROR_INFO);
		}
		return res;
    }

    
}
