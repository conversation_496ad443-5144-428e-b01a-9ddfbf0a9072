package com.foshan.service.community.impl;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityPaymentRecordEntity;
import com.foshan.entity.community.CommunityReceiptEntity;
import com.foshan.entity.community.CommunityReceiptReceivablesEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.community.context.CommunityContext;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityPayItemsForm;
import com.foshan.form.community.CommunityReceiptForm;
import com.foshan.form.community.CommunityReceiptReceivablesForm;
import com.foshan.form.community.CommunityReceivablesChangesForm;
import com.foshan.form.community.CommunityReceivablesForm;
import com.foshan.form.community.request.CommunityReceiptReq;
import com.foshan.form.community.response.communityReceipt.AddCommunityReceiptRes;
import com.foshan.form.community.response.communityReceipt.GetCommunityReceiptInfoRes;
import com.foshan.form.community.response.communityReceipt.GetCommunityReceiptListRes;
import com.foshan.form.community.response.communityReceipt.ModifyCommunityReceiptRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityReceiptService;
import com.foshan.util.ChineseNumberUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.FreemarkUtil;
import com.foshan.util.PinYinUtil;

@Transactional
@Service("communityReceiptService")
public class CommunityReceiptServiceImpl extends GenericCommunityService implements ICommunityReceiptService{

	@Override
	public IResponse getCommunityReceiptList(CommunityReceiptReq req) {
		GetCommunityReceiptListRes res = new GetCommunityReceiptListRes();
		Page<CommunityReceiptEntity> page = new Page<CommunityReceiptEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		
		Object userObj = getPrincipal(true);
		if (null != userObj) {
			StringBuilder billingDateStr = new StringBuilder();
			if(userObj instanceof CommunityMemberEntity) {
				CommunityMemberEntity member = (CommunityMemberEntity) userObj;
				if(req.getPropertyId() == null) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "propertyId不能为空！");
					return res;
				}
				Integer estateId = req.getPropertyId();
				
				StringBuilder sb = new StringBuilder(
						"select mp from CommunityMemberPropertyEntity mp inner join mp.member m inner join mp.property e ");
				sb.append("where (mp.isCurrentMember = 1 or mp.isCurrentOwner = 1) and m.id =" + member.getId() + " and e.id = "
						+ estateId);
				CommunityMemberPropertyEntity findUnique = communityMemberPropertyDao.getUniqueByHql(sb.toString());
				//当前用户没有权限查阅该账单
				if(null==findUnique) {
					res.setRet(ResponseContext.RES_PERM_BEYOND_AUTHORITY_CODE);
					res.setRetInfo(ResponseContext.RES_PERM_BEYOND_AUTHORITY_INFO);
					return res;
				}
				billingDateStr.append(null!= findUnique.getBillingDate()? 
						" and a.receiptDate>='"+DateUtil.formatLongFormat(findUnique.getBillingDate())+"' " :"")
					.append(null!=findUnique.getTerminationDate() ? " and a.receiptDate<='"+
						DateUtil.formatLongFormat(findUnique.getTerminationDate())+"'":"");
			}
		

			page = communityReceiptDao.queryPage(page, queryHql(req,userObj,billingDateStr.toString()));

			res.setTotalResult(page.getTotalCount());
			res.setPageSize(page.getPageSize());
			res.setCurrentPage(page.getCurrentPage());
			res.setTotal(page.getTotalPage());
			page.getResultList().forEach(o -> {
				CommunityReceiptForm communityReceiptForm = new CommunityReceiptForm();
				communityReceiptForm.setCommunityReceiptId(o.getId());
	            communityReceiptForm.setAgent(o.getAgent());
	            communityReceiptForm.setAmount(null!=o.getAmount() ? o.getAmount().toString() : "");
	            communityReceiptForm.setBankAccount(o.getBankAccount());
	            communityReceiptForm.setCashier(o.getCashier());
	            communityReceiptForm.setComment(StringUtils.isNotEmpty(o.getComment()) ? o.getComment() : "");
	            communityReceiptForm.setCurrencyAmount(null!=o.getCurrencyAmount() ? o.getCurrencyAmount().toString() : "");
	            communityReceiptForm.setExchangeRate(null!=o.getExchangeRate() ? o.getExchangeRate().toString() : "");
	            communityReceiptForm.setMoneytype(o.getMoneytype());
	            communityReceiptForm.setOldData(o.getOldData());
	            communityReceiptForm.setOldId(o.getOldId());
	            communityReceiptForm.setPayerName(o.getPayerName());
	            communityReceiptForm.setPaymentMethod(o.getPaymentMethod());
	            communityReceiptForm.setPaymentType(o.getPaymentType());
	            communityReceiptForm.setPrintNum(null!=o.getPrintNum() ? o.getPrintNum() : 0);
	            communityReceiptForm.setReceiptCode(StringUtils.isNotEmpty(o.getReceiptCode()) ? o.getReceiptCode() : "");
	            communityReceiptForm.setReceiptDate(null != o.getReceiptDate()? DateUtil.formatLongFormat(o.getReceiptDate()) : "");
	            communityReceiptForm.setReceiptType(o.getReceiptType());
	            communityReceiptForm.setSupervisor(o.getSupervisor());
	            communityReceiptForm.setOutTradeNo(o.getWxTradeNo());
	            communityReceiptForm.setWxorderNo(o.getWxorderNo());
	            communityReceiptForm.setEstateAddress(o.getEstateAddress());
	            communityReceiptForm.setPdfUrl(StringUtils.isNotEmpty(o.getPdfUrl()) ? o.getPdfUrl():"");
	            communityReceiptForm.setHaveInvoice(o.getHaveInvoice());
	            communityReceiptForm.setFeeType(o.getFeeType());
	            communityReceiptForm.setEstateId(o.getEstate().getId().toString());
	            communityReceiptForm.setUnitCode(StringUtils.isNotEmpty(o.getEstate().getUnitCode()) ? o.getEstate().getUnitCode() : "");
	            if(o.getFeeType()==1) {//一次性收费返回退款记录
	            	o.getRefundList().forEach(p->{
	            		if(p.getChangeType() == 2) {
	            			CommunityReceivablesChangesForm changes = new CommunityReceivablesChangesForm();
	            			changes.setCommunityReceivablesChangesId(p.getId());
	            			changes.setChangeAmount(p.getChangeAmount().toString());
	            			changes.setRefundMethod(p.getRefundMethod());
	            			changes.setComment(StringUtils.isNotEmpty(p.getComment()) ? p.getComment():"");
	            			changes.setRecorder(StringUtils.isNotEmpty(p.getRecorder()) ? p.getRecorder():"");
	            			changes.setChangeDate(null != p.getChangeDate()? DateUtil.formatLongFormat(p.getChangeDate()) : "");
	            			changes.setRefundMethodName(StringUtils.isNotEmpty(p.getRefundMethodName()) ? p.getRefundMethodName():"");
	            			communityReceiptForm.getReceivablesChangesList().add(changes);
	            		}
	            	});
	            }
	            if( o.getReceiptReceivablesList() != null) {
	            	List<CommunityReceiptReceivablesForm> list = new ArrayList<>();
	            	o.getReceiptReceivablesList().forEach(p->{
						CommunityReceiptReceivablesForm communityReceiptReceivablesForm = new CommunityReceiptReceivablesForm();
						communityReceiptReceivablesForm.setCommunityReceiptReceivablesId(p.getId());
		                communityReceiptReceivablesForm.setCurrentAmount(null!=p.getCurrentAmount() ? p.getCurrentAmount().toString() : "");
		                communityReceiptReceivablesForm.setReceivedAmount(null!=p.getReceivedAmount() ? p.getReceivedAmount().toString() : "");
		                communityReceiptReceivablesForm.setInvoiceState(p.getInvoiceState());
		                if(null != p.getReceivables()) {
		        			CommunityReceivablesForm communityReceivablesForm = new CommunityReceivablesForm();
		        			communityReceivablesForm.setCommunityReceivablesId(p.getReceivables().getId());
		                    communityReceivablesForm.setChargeCategory(p.getReceivables().getChargeCategory());
		                    communityReceivablesForm.setChargeSource(p.getReceivables().getChargeSource());
		                    communityReceivablesForm.setReceivableAmount(p.getReceivables().getReceivableAmount().toString());
		                    communityReceivablesForm.setPayItemsName(p.getReceivables().getPayItemsName());
		                    communityReceiptReceivablesForm.setPayItemsId(p.getReceivables().getPayItem().getId());
		                    communityReceiptReceivablesForm.setReceivableDate(p.getReceivables().getReceivableDate().getTime());
		                    communityReceivablesForm.setReceivableDate(null != p.getReceivables().getReceivableDate() ? 
		                    		DateUtil.formatLongFormat(p.getReceivables().getReceivableDate()) : "");
		                    communityReceivablesForm.setComment(StringUtils.isNotEmpty(p.getReceivables().getComment()) ? 
		                    		p.getReceivables().getComment():"");
		                    communityReceiptReceivablesForm.setReceivablesForm(communityReceivablesForm);
		                    if(null != p.getReceivables().getPayItem()) {
		                    	CommunityPayItemsForm payItemsForm = new CommunityPayItemsForm();
		                    	payItemsForm.setCommunityPayItemsId(p.getReceivables().getPayItem().getId());
		                    	payItemsForm.setItemsName(p.getReceivables().getPayItem().getItemsName());
		                    	payItemsForm.setChargeCategory(p.getReceivables().getPayItem().getChargeCategory());
		                    	payItemsForm.setInviceInfo(p.getReceivables().getPayItem().getInviceInfo());
		                    	communityReceivablesForm.setPayItemsForm(payItemsForm);
		                    }
		                }
		                list.add(communityReceiptReceivablesForm);
		            });
	                communityReceiptForm.getReceiptReceivablesList().addAll(list.stream().sorted(Comparator.comparingInt(
	            			CommunityReceiptReceivablesForm::getPayItemsId).reversed().
	            			thenComparingLong(CommunityReceiptReceivablesForm::getReceivableDate).reversed()).collect(Collectors.toList()));
	            }

				res.getCommunityReceiptList().add(communityReceiptForm);
			});
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
		}
		return res;
	}
	
	public String queryHql(CommunityReceiptReq req,Object userObj,String billingDateStr) {
		StringBuilder hql = new StringBuilder("select distinct a from CommunityReceiptEntity a inner join a.estate b "
				+ " inner join a.receiptReceivablesList c inner join c.receivables d inner join b.building e "
				+ "inner join e.district f inner join d.payItem h where 1=1 ");
		if(userObj instanceof CommunityMemberEntity) {
			hql.append(billingDateStr);
		}else {
			hql.append(StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime()) ? 
					" and a.receiptDate>='"+req.getStartTime()+"' and a.receiptDate<='"+req.getEndTime()+"'" :"")
			.append(StringUtils.isNotEmpty(req.getStartTime()) && !StringUtils.isNotEmpty(req.getEndTime()) ? 
					" and a.receiptDate>='"+req.getStartTime()+"'" :"")
			.append(!StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime()) ? 
					" and a.receiptDate<='"+req.getEndTime()+"'" :"");
		}
	
		hql.append(StringUtils.isNotEmpty(req.getReceiptCode()) ? " and a.receiptCode like'%"+req.getReceiptCode()+"%'":"")
			.append(null!=req.getPropertyId() ? " and b.id="+req.getPropertyId():"")
			.append(StringUtils.isNotEmpty(req.getEstateTypeList()) ? " and b.estateType in("+req.getEstateTypeList()+") ":"")
			.append(StringUtils.isNotEmpty(req.getUnitCode()) ? 
					" and b.unitCode like'%"+req.getUnitCode()+"%'" :"")
			.append(StringUtils.isNotEmpty(req.getChargeCategoryList()) ? 
					" and h.chargeCategory in("+req.getChargeCategoryList()+")" :"")
			.append(null!=req.getFeeType() ? " and a.feeType="+req.getFeeType() :"")
			.append(StringUtils.isNotEmpty(req.getDistrictIdList()) ? " and f.id in("+req.getDistrictIdList() +")":"")
			.append(StringUtils.isNotEmpty(req.getPayerName()) ? 
					" and a.payerName like'%"+req.getPayerName()+"%'" :"")
			.append(StringUtils.isNotEmpty(req.getPaymentMethodList()) ? " and a.paymentMethod in("+req.getPaymentMethodList() +")":"")
			.append(null!=req.getState() ? " and a.state="+req.getState():" and a.state="+EntityContext.RECORD_STATE_VALID);
		
		hql.append(null!=req.getPropertyId() ? " and b.id="+req.getPropertyId():"");
		hql.append(" ORDER BY a.receiptDate desc"); 
		return hql.toString();
	}
	
	public IResponse exportCommunityReceiptList(CommunityReceiptReq req, HttpServletResponse response)  {
		ExportExcelRes res = new ExportExcelRes();
		Object userObj = getPrincipal(true);
		if (null != userObj) {
			List<CommunityReceiptEntity> list = communityReceiptDao.getListByHql(queryHql(req,userObj,""),"");
			Map<String, String> heardMap = new LinkedHashMap<String, String>();
			heardMap.put("receiptCode", "单据编号");
			heardMap.put("unitCode", "单元编号");
			heardMap.put("createTime", "收款日期");
			heardMap.put("payerName", "付款人");
			heardMap.put("amount", "金额");
			heardMap.put("paymentMethod", "收款方式");
			heardMap.put("receiptType", "单据类别");
			heardMap.put("agent", "经办人");
			heardMap.put("printNum", "打印次数");
			heardMap.put("haveInvoice", "是否已打发票");
			heardMap.put("comment", "备注");
			List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
			list.forEach(o -> {
				if (null != o) {
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("receiptCode", o.getReceiptCode());
					map.put("unitCode",
							StringUtils.isNotEmpty(o.getEstate().getUnitCode().toString()) ? o.getEstate().getUnitCode().toString() : "");
					map.put("createTime", DateUtil.formatLongFormat(o.getCreateTime()));
					map.put("payerName", o.getPayerName());
					map.put("amount", o.getAmount().toString());
					map.put("paymentMethod", o.getPaymentMethod());
					map.put("receiptType", o.getReceiptType());
					map.put("agent", o.getAgent());
					map.put("printNum", null!=o.getPrintNum()?o.getPrintNum().toString():"0");
					map.put("haveInvoice", null!=o.getHaveInvoice()&&o.getHaveInvoice()==0?"否":
						(null!=o.getHaveInvoice()&&o.getHaveInvoice()==1?"是":""));
					map.put("comment", o.getComment());
					dataList.add(map);
				}
			});
			try {
				ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
						"", response);
			} catch (Exception e) {
				e.printStackTrace();
				res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
				res.setRetInfo("导出异常！");
			}
		} else {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "新增收款单据")
	public IResponse addCommunityReceipt(CommunityReceiptReq req) {
		AddCommunityReceiptRes res = new AddCommunityReceiptRes();
		if (req.getReceivablesList().size()>0) {
			if(StringUtils.isEmpty(req.getReceiptDate())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收据日期不能为空！");
				return res;
			}
			CommunityReceiptEntity communityReceipt = new CommunityReceiptEntity();
		    try {
                communityReceipt.setReceiptDate(StringUtils.isNotEmpty(req.getReceiptDate()) ? 
                    DateUtil.parseLongFormat(req.getReceiptDate()) : null);
            } catch (ParseException e) {
                e.printStackTrace();
            }
			if(StringUtils.isNotEmpty(req.getReceiptCode())) {
            	Pattern pattern = Pattern.compile("^\\w+_\\d{6}_\\d{6}");
				Matcher matcher = pattern.matcher(req.getReceiptCode());
				if(!matcher.matches()) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("对不起，输入的单据编号格式有误,请按XXXX_yyyyMM_DDDDDD格式提交！");
					return res;
				}
				communityReceipt = communityReceiptDao.findUnique("select a from CommunityReceiptEntity a where a.receiptCode='"+req.getReceiptCode()+"'", "");
				if(null != communityReceipt) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("对不起，单据编号有重复！");
					return res;
				}else {
					communityReceipt = new CommunityReceiptEntity();
					communityReceipt.setReceiptCode(req.getReceiptCode());
				}
			}else {
				communityReceipt = new CommunityReceiptEntity();
				communityReceipt.setReceiptCode(getReceiptCode(req.getReceiptCodePrefix(), communityReceipt.getReceiptDate()));
			}
			BigDecimal totalAmount = new BigDecimal(0);
			for (CommunityReceivablesForm receivablesForm : req.getReceivablesList()) {
				if(null != receivablesForm.getCommunityReceivablesId() && StringUtils.isNotEmpty(receivablesForm.getAmount())) {
					CommunityReceivablesEntity receivables = communityReceivablesDao.get(receivablesForm.getCommunityReceivablesId());
					if(null != receivables) {
						if(null == communityReceipt.getEstate()) {
							communityReceipt.setEstate(receivables.getEstate());
						}
						
						BigDecimal amount = new BigDecimal(receivablesForm.getAmount());
						totalAmount = totalAmount.add(amount);
						CommunityReceiptReceivablesEntity receiptReceivables = new CommunityReceiptReceivablesEntity();
						receiptReceivables.setReceivedAmount(receivables.getReceivedAmount());
						receiptReceivables.setCurrentAmount(amount);
						receiptReceivables.setReceipt(communityReceipt);
						receiptReceivables.setReceivables(receivables);
						receiptReceivables.setInvoiceState(0);
						communityReceiptReceivablesDao.save(receiptReceivables);
						receivables.setReceivedAmount(receivables.getReceivedAmount().add(amount));
						receivables.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
					}
				}
			}
			
            communityReceipt.setAgent(req.getAgent());
            communityReceipt.setAmount(totalAmount);
            communityReceipt.setBankAccount(req.getBankAccount());
            communityReceipt.setCashier(req.getCashier());
            communityReceipt.setComment(req.getComment());
            communityReceipt.setCurrencyAmount(StringUtils.isNotEmpty(req.getCurrencyAmount()) ?  new BigDecimal(req.getCurrencyAmount()) : null);
            communityReceipt.setExchangeRate(StringUtils.isNotEmpty(req.getExchangeRate()) ?  new BigDecimal(req.getExchangeRate()) : null);
            communityReceipt.setMoneytype(req.getMoneytype());
            communityReceipt.setOldData(req.getOldData());
            communityReceipt.setOldId(req.getOldId());
            communityReceipt.setPayerName(req.getPayerName());
            communityReceipt.setPaymentMethod(req.getPaymentMethod());
            communityReceipt.setPaymentType(req.getPaymentType());
            communityReceipt.setPrintNum(req.getPrintNum());
            communityReceipt.setReceiptType("收据");
            communityReceipt.setSupervisor(req.getSupervisor());
            communityReceipt.setWxTradeNo(req.getWxTradeNo());
            communityReceipt.setWxorderNo(req.getWxorderNo());
            communityReceipt.setEstateAddress(req.getEstateAddress());
            communityReceipt.setHaveInvoice(req.getHaveInvoice());
            communityReceipt.setFeeType(req.getFeeType());
			communityReceiptDao.save(communityReceipt);
			req.setCommunityReceiptId(communityReceipt.getId());
			createCommunityReceiptPdf(req);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "修改收款单据")
	public IResponse modifyCommunityReceipt(CommunityReceiptReq req) {
		ModifyCommunityReceiptRes res = new ModifyCommunityReceiptRes();
		if (null!=req.getCommunityReceiptId() ) {
			CommunityReceiptEntity communityReceipt = communityReceiptDao.get(req.getCommunityReceiptId()) ;
			if(null != communityReceipt){
				/*
				 * if(StringUtils.isNotEmpty(req.getReceiptCode())) { CommunityReceiptEntity
				 * receipt = communityReceiptDao.
				 * findUnique("select a from CommunityReceiptEntity where a.receiptCode='"+
				 * req.getReceiptCode()+"' and a.id!="+req.getCommunityReceiptId(), ""); if(null
				 * != receipt) { res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				 * res.setRetInfo("对不起，单据编号有重复！"); return res; }else {
				 * communityReceipt.setReceiptCode(req.getReceiptCode()); } }
				 */
				BigDecimal amount = StringUtils.isNotEmpty(req.getAmount()) ?  new BigDecimal(req.getAmount()) : null;
                communityReceipt.setAgent(req.getAgent());
                communityReceipt.setAmount(amount);
                communityReceipt.setBankAccount(req.getBankAccount());
                communityReceipt.setCashier(req.getCashier());
                communityReceipt.setComment(req.getComment());
                communityReceipt.setCurrencyAmount(StringUtils.isNotEmpty(req.getCurrencyAmount()) ?  new BigDecimal(req.getCurrencyAmount()) : null);
                communityReceipt.setExchangeRate(StringUtils.isNotEmpty(req.getExchangeRate()) ?  new BigDecimal(req.getExchangeRate()) : null);
                communityReceipt.setMoneytype(req.getMoneytype());
                //一次性收费，只能对应一条应收款
                if(communityReceipt.getFeeType()==1 && null!=req.getPayItemId() && 
                		!communityReceipt.getReceiptReceivablesList().get(0).getReceivables().getPayItem().getId().equals(req.getPayItemId())) {
                	CommunityPayItemsEntity payItem = communityPayItemsDao.get(req.getPayItemId());
                	if(null != payItem) {
                		communityReceipt.getReceiptReceivablesList().get(0).getReceivables().setPayItemsName(payItem.getItemsName());
                    	communityReceipt.getReceiptReceivablesList().get(0).getReceivables().setPayItem(payItem);
                    	communityReceipt.getReceiptReceivablesList().get(0).getReceivables().setLastModifyTime(new Timestamp(System.currentTimeMillis()));
                	}
                }
                if(communityReceipt.getFeeType()==1 && null != amount) {
                	communityReceipt.getReceiptReceivablesList().get(0).getReceivables().setReceivableAmount(amount);
                	communityReceipt.getReceiptReceivablesList().get(0).getReceivables().setReceivedAmount(amount);
                	communityReceipt.getReceiptReceivablesList().get(0).setCurrentAmount(amount);
                	communityReceipt.getReceiptReceivablesList().get(0).setReceivedAmount(amount);
                	communityReceipt.getReceiptReceivablesList().get(0).getReceivables().setLastModifyTime(new Timestamp(System.currentTimeMillis()));
                }

                communityReceipt.setPayerName(req.getPayerName());
                communityReceipt.setPaymentMethod(req.getPaymentMethod());
                communityReceipt.setPaymentType(req.getPaymentType());
                communityReceipt.setPrintNum(req.getPrintNum());
                //旧的收据有需要编辑,暂时屏
//                if(StringUtils.isNotEmpty(req.getReceiptCode())) {
//                	Pattern pattern = Pattern.compile("^\\w+_\\d{6}_\\d{6}");
//    				Matcher matcher = pattern.matcher(req.getReceiptCode());
//    				if(!matcher.matches()) {
//    					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//    					res.setRetInfo("对不起，输入的单据编号格式有误,请按XXXX_yyyyMM_DDDDDD格式提交！");
//    					return res;
//    				}
//                }
                communityReceipt.setReceiptCode(req.getReceiptCode());
                try {
                    communityReceipt.setReceiptDate(StringUtils.isNotEmpty(req.getReceiptDate()) ? 
                        DateUtil.parseLongFormat(req.getReceiptDate()) : null);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                communityReceipt.setReceiptType("收据");
                communityReceipt.setSupervisor(req.getSupervisor());
                communityReceipt.setWxTradeNo(req.getWxTradeNo());
                communityReceipt.setWxorderNo(req.getWxorderNo());
                communityReceipt.setEstateAddress(req.getEstateAddress());
                communityReceipt.setHaveInvoice(null!=req.getHaveInvoice() ? req.getHaveInvoice() : communityReceipt.getHaveInvoice());
                //communityReceipt.setFeeType(null!=req.getFeeType() ? req.getFeeType() : communityReceipt.getFeeType());
                createCommunityReceiptPdf(req);
				res.setCommunityReceiptId(communityReceipt.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除收款单据")
	public IResponse deleteCommunityReceipt(CommunityReceiptReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityReceiptId()) {
			CommunityReceiptEntity communityReceipt = communityReceiptDao.get(req.getCommunityReceiptId());
			if (null != communityReceipt) {
				if(communityReceipt.getRefundList().size()==0) {
					for(CommunityPaymentRecordEntity paymentRecord : communityReceipt.getPaymentRecordList()) {
						if(StringUtils.isNotEmpty(paymentRecord.getPaymentScene())&& paymentRecord.getStatus()!=3) {
			                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo("对不起，删除失败；此数据有其他关联数据！");
							return res;
						}
					}
					
					@SuppressWarnings("rawtypes")
					Iterator it = communityReceipt.getReceiptReceivablesList().iterator();
					while (it.hasNext()) {
						CommunityReceiptReceivablesEntity receiptReceivables = (CommunityReceiptReceivablesEntity) it.next();

						CommunityReceivablesEntity receivables = receiptReceivables.getReceivables();
						//if(receivables.getReceiptReceivablesList().size()>1) {
							receivables.setReceivedAmount(receivables.getReceivedAmount().subtract(receiptReceivables.getCurrentAmount()));
							receivables.getReceiptReceivablesList().remove(receiptReceivables);
						//}
						receiptReceivables.setReceipt(null);
						receiptReceivables.setReceivables(null);
						communityReceiptReceivablesDao.delete(receiptReceivables);
						// 修改补差额对象
						deleteSubReceivaedManageAmount(receiptReceivables.getId(), receivables, 0);		
						
					}

					for(CommunityPaymentRecordEntity paymentRecord : communityReceipt.getPaymentRecordList()) {
						paymentRecord.setReceipt(null);
						paymentRecord.setEstate(null);
						communityPaymentRecordDao.delete(paymentRecord);
					}
					communityReceipt.setEstate(null);
					communityReceipt.setReceiptReceivablesList(null);	
					communityReceipt.setPaymentRecordList(null);
					communityReceipt.setBankDepositRecord(null);
					communityReceiptDao.delete(communityReceipt);
					//communityReceiptDao.deleteById(communityReceipt.getId());
					//communityReceipt.setState(EntityContext.RECORD_STATE_INVALID);
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				}else {
	                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("对不起，删除失败；此数据有其他关联数据！");
				}
			
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityReceiptInfo(CommunityReceiptReq req) {
		GetCommunityReceiptInfoRes res = new GetCommunityReceiptInfoRes();
		if (null != req.getCommunityReceiptId()) {
			CommunityReceiptEntity communityReceipt = communityReceiptDao.get(req.getCommunityReceiptId());
			if (null != communityReceipt) {
				CommunityReceiptForm communityReceiptForm = new CommunityReceiptForm();
				communityReceiptForm.setCommunityReceiptId(communityReceipt.getId());
                communityReceiptForm.setAgent(communityReceipt.getAgent());
                communityReceiptForm.setAmount(null!=communityReceipt.getAmount() ? communityReceipt.getAmount().toString() : "");
                communityReceiptForm.setBankAccount(communityReceipt.getBankAccount());
                communityReceiptForm.setCashier(communityReceipt.getCashier());
                communityReceiptForm.setComment(communityReceipt.getComment());
                communityReceiptForm.setCurrencyAmount(null!=communityReceipt.getCurrencyAmount() ? communityReceipt.getCurrencyAmount().toString() : "");
                communityReceiptForm.setExchangeRate(null!=communityReceipt.getExchangeRate() ? communityReceipt.getExchangeRate().toString() : "");
                communityReceiptForm.setMoneytype(communityReceipt.getMoneytype());
                communityReceiptForm.setOldData(communityReceipt.getOldData());
                communityReceiptForm.setOldId(communityReceipt.getOldId());
                communityReceiptForm.setPayerName(communityReceipt.getPayerName());
                communityReceiptForm.setPaymentMethod(communityReceipt.getPaymentMethod());
                communityReceiptForm.setPaymentType(communityReceipt.getPaymentType());
                communityReceiptForm.setPrintNum(null!=communityReceipt.getPrintNum() ? communityReceipt.getPrintNum() : 0);
                communityReceiptForm.setReceiptCode(communityReceipt.getReceiptCode());
                communityReceiptForm.setReceiptDate(null != communityReceipt.getReceiptDate()? DateUtil.formatLongFormat(communityReceipt.getReceiptDate()) : "");
                communityReceiptForm.setReceiptType(communityReceipt.getReceiptType());
                communityReceiptForm.setSupervisor(communityReceipt.getSupervisor());
                communityReceiptForm.setOutTradeNo(communityReceipt.getWxTradeNo());
                communityReceiptForm.setWxorderNo(communityReceipt.getWxorderNo());
                communityReceiptForm.setEstateId(communityReceipt.getEstate().getId().toString());
                communityReceiptForm.setUnitCode( StringUtils.isNotEmpty( communityReceipt.getEstate().getUnitCode()) 
                		? communityReceipt.getEstate().getUnitCode():"");
                communityReceiptForm.setEstateAddress(communityReceipt.getEstateAddress());
                communityReceiptForm.setPdfUrl(StringUtils.isNotEmpty(communityReceipt.getPdfUrl()) ? communityReceipt.getPdfUrl() : "");
                communityReceiptForm.setHaveInvoice(communityReceipt.getHaveInvoice());
                communityReceiptForm.setFeeType(communityReceipt.getFeeType());
//            	List<CommunityReceiptReceivablesEntity> receiptReceivablesList = communityReceiptReceivablesDao.getListByHql(
//            			"select distinct a from CommunityReceiptReceivablesEntity a inner join a.receivables b inner join b.payItem c where a.receipt.id="
//            					+communityReceipt.getId()+" order by c.itemsName desc,b.receivableDate desc", "");
                List<CommunityReceiptReceivablesForm> list = new ArrayList<>();
                communityReceipt.getReceiptReceivablesList().forEach(o->{
        			if (null != o) {
        				CommunityReceiptReceivablesForm communityReceiptReceivablesForm = new CommunityReceiptReceivablesForm();
        				communityReceiptReceivablesForm.setCommunityReceiptReceivablesId(o.getId());
                        communityReceiptReceivablesForm.setCurrentAmount(null!=o.getCurrentAmount() ? o.getCurrentAmount().toString() : "");
                        communityReceiptReceivablesForm.setReceivedAmount(null!=o.getReceivedAmount() ? o.getReceivedAmount().toString() : "");
                        communityReceiptReceivablesForm.setInvoiceState(o.getInvoiceState());

                        if(null != o.getReceivables()) {
                			CommunityReceivablesForm communityReceivablesForm = new CommunityReceivablesForm();
                			communityReceivablesForm.setCommunityReceivablesId(o.getReceivables().getId());
                            communityReceivablesForm.setChargeCategory(o.getReceivables().getChargeCategory());
                            communityReceivablesForm.setChargeSource(o.getReceivables().getChargeSource());
                            communityReceivablesForm.setPayItemsName(o.getReceivables().getPayItemsName());
    	                    communityReceivablesForm.setReceivableDate(null != o.getReceivables().getReceivableDate()
    	    						? DateUtil.formatLongFormat(o.getReceivables().getReceivableDate()): "");
                            communityReceiptReceivablesForm.setReceivablesForm(communityReceivablesForm);
    	                    communityReceiptReceivablesForm.setPayItemsId(o.getReceivables().getPayItem().getId());
    	                    communityReceiptReceivablesForm.setReceivableDate(o.getReceivables().getReceivableDate().getTime());

                            if(null != o.getReceivables().getPayItem()) {
                            	CommunityPayItemsForm payItemsForm = new CommunityPayItemsForm();
                            	payItemsForm.setCommunityPayItemsId(o.getReceivables().getPayItem().getId());
                            	payItemsForm.setItemsName(o.getReceivables().getPayItem().getItemsName());
                            	payItemsForm.setInviceInfo(o.getReceivables().getPayItem().getInviceInfo());                            	
                            	communityReceivablesForm.setPayItemsForm(payItemsForm);
                            }
                        }
                       // communityReceiptForm.getReceiptReceivablesList().add(communityReceiptReceivablesForm);
                        list.add(communityReceiptReceivablesForm);
        			}
                });
                communityReceiptForm.getReceiptReceivablesList().addAll(list.stream().sorted(Comparator.comparingInt(
            			CommunityReceiptReceivablesForm::getPayItemsId).reversed().
            			thenComparingLong(CommunityReceiptReceivablesForm::getReceivableDate).reversed()).collect(Collectors.toList()));
                
//            	communityReceiptForm.getReceiptReceivablesList().stream().sorted(Comparator.
//    			comparingLong(CommunityReceiptReceivablesForm::getReceivableDate)).collect(Collectors.toList());
    
				res.setCommunityReceiptForm(communityReceiptForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	public IResponse addPrintNum(CommunityReceiptReq req) {
		GetCommunityReceiptInfoRes res = new GetCommunityReceiptInfoRes();
		if (null != req.getCommunityReceiptId()) {
			CommunityReceiptEntity communityReceipt = communityReceiptDao.get(req.getCommunityReceiptId());
			if (null != communityReceipt) {
				communityReceipt.setPrintNum(null!=communityReceipt.getPrintNum() ? (communityReceipt.getPrintNum()+1) : 1);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@Audit(operate = "创建收据PDF文件")
	@Override
	public IResponse createCommunityReceiptPdf(CommunityReceiptReq req) {
		GetCommunityReceiptInfoRes res = new GetCommunityReceiptInfoRes();
		if (null != req.getCommunityReceiptId()) {
			CommunityReceiptEntity communityReceipt = communityReceiptDao.get(req.getCommunityReceiptId());
			if (null != communityReceipt) {
				Map<String,Object> params = new HashMap<String,Object>();
		    	 params.put("propertyManagementCompany", "佛山普华美居物业服务有限公司");
		    	 
		    	 params.put("userName", communityReceipt.getPayerName());
		    	 params.put("unitCode", communityReceipt.getEstate().getBuilding().getDistrict().getDistrictName()+
		    			 communityReceipt.getEstate().getBuilding().getBuildingName()+communityReceipt.getEstate().getFloor()+communityReceipt.getEstate().getRoomNumber());
		    	 params.put("receiptCode", communityReceipt.getReceiptCode());
		    	 params.put("receiptDate", null != communityReceipt.getReceiptDate()? 
		    			 DateUtil.formatShortFormat(communityReceipt.getReceiptDate()) : "");
		    	 params.put("buildingArea", communityReceipt.getEstate().getBuildingArea().setScale(2, RoundingMode.HALF_UP).toString());
		    	 
		    	 params.put("accountant", "");
		    	 params.put("cashier", StringUtils.isNotEmpty(communityReceipt.getCashier()) ? communityReceipt.getCashier():"");
		    	 params.put("agent", StringUtils.isNotEmpty(communityReceipt.getAgent()) ? communityReceipt.getAgent() : "");
		    	//解决备注显示不全问题
		    	 if(StringUtils.isNotEmpty(communityReceipt.getComment()) && communityReceipt.getComment().length()>60) {
		    		 StringBuilder comment = new StringBuilder();
		    		 //一行43个字符比较理想，由于第一行首字符器占三个字符（），所以第一行显示60；
		    		 comment.append(communityReceipt.getComment().subSequence(0, 60)+"<br>");
		    		 String c = (String) communityReceipt.getComment().subSequence(60, communityReceipt.getComment().length());
		    		 if(StringUtils.isNotEmpty(c) && c.length()>60) {
			    		 int section = c.length()/60;
			    		 int surplus = c.length()%60;
			    		 for(int i=0; i<section ; i++) {
			    			 comment.append(c.subSequence(i*60, (i+1)*60)+"<br>");
			    		 }
			    		 if(surplus > 0) {
			    			 comment.append(c.subSequence(section*60,c.length()));
			    		 }
		    		 }else {
		    			 comment.append(c);
		    		 }
		    		 params.put("comment", comment.toString());
		    	 }else {
		    		 params.put("comment", StringUtils.isNotEmpty(communityReceipt.getComment()) ? 
		    				 communityReceipt.getComment() : "");
		    	 }
		    	 
		    	 params.put("total", communityReceipt.getAmount().toString()+"元");
		    	 params.put("capitalization", ChineseNumberUtil.convert(communityReceipt.getAmount().toString()));
		    	 List<Map<String,String>> list = new ArrayList<Map<String,String>>();
		    	/* communityReceipt.getReceiptReceivablesList().forEach(o->{
			    	 Map<String,String> map = new HashMap<String,String>();
			    	 map.put("payItemsName", o.getReceivables().getPayItemsName());
			    	 
			    	 map.put("period", null != o.getReceivables().getPaymentPeriod()? 
			    			 DateUtil.formatShortFormat(o.getReceivables().getPaymentPeriod()).substring(0, 7) : "");
			    	 map.put("lastRecordNum", "");
			    	 map.put("recordNum", "");
			    	 map.put("dosage", "");
			    	 map.put("unitPrice", "");
			    	 map.put("amount", o.getCurrentAmount().toString());
			    	 list.add(map);
		    	 });*/
		    	 Map<String,Map<String,Object>> compoundMap = getCompoundReceivables(communityReceipt.getReceiptReceivablesList());
		    	 int height=260;
		    	 int size=0;
		    	 for(String payItemsName : compoundMap.keySet()) {
		    		 Map<String,String> map = new HashMap<String,String>();
		    		 Map<String,Object> data = compoundMap.get(payItemsName);
		    		 if(((Date) data.get("endReceivableDate")).compareTo((Date) data.get("startReceivableDate"))==0) {
				    	 map.put("period", DateUtil.formatShortFormat((Date) data.get("startReceivableDate")).
				    			 substring(0, 7).replaceAll("-", ".") );
		    		 }else {
				    	 map.put("period", DateUtil.formatShortFormat((Date) data.get("startReceivableDate")).
				    			 substring(0, 7).replaceAll("-", ".") + "-" +
				    			 DateUtil.formatShortFormat((Date) data.get("endReceivableDate")).
				    			 substring(0, 7).replaceAll("-", ".") );
		    		 }
		    		 map.put("payItemsName", (String) data.get("payItemsName"));

			    	 //map.put("lastRecordNum", (String) data.get("lastRecordNum"));
			    	 //map.put("recordNum", (String) data.get("recordNum"));
			    	 //map.put("dosage", (String) data.get("dosage"));
			    	 map.put("unitPrice", (String) data.get("unitPrice"));
			    	 map.put("amount", ((BigDecimal)data.get("amount")).toString());
			    	 list.add(map);
			    	 size++;
			    	 if(size>13) {
			    		 height = height+20;
			    	 }
			    	 
		    	 }
		    	 params.put("height", height);
		    	 params.put("receiptList", list);
		    	 
		 		Date date = new Date();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
				String today = sdf.format(date);
				String dir = contextInfo.getAssetFilePath()+ File.separator +"receiptPdf"+ 
						File.separator + today+ File.separator+PinYinUtil.parseSimplePY(communityReceipt.getEstate().getUnitCode())+
						File.separator;
				createMultilayerFile(dir);
	    		File templateFile = new File(contextInfo.getAssetFilePath()+ File.separator+"receiptPdfTemplate.ftl");
	    		if(!templateFile.exists()) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("模板不存在，请重新上传模板！");
					return res;
	    		}
	    		String fileName = "";
	    		if(communityReceipt.getReceiptCode().contains("-")) {
	    			//String[] code = communityReceipt.getReceiptCode().split("-");
	    			//fileName = code.length>=2 ? code[1] : code[0];
	    			fileName = communityReceipt.getReceiptCode().replaceAll("-","");
	    		}else{
	    			fileName =  communityReceipt.getReceiptCode();
	    		}
				if(FreemarkUtil.generatePdf(contextInfo.getAssetFilePath(), 
						"receiptPdfTemplate.ftl", params,dir+fileName+".pdf",null)) {
					communityReceipt.setPdfUrl(contextInfo.getAssetFileUrl()+ "/" +"receiptPdf"+ 
							"/" + today+ "/"+PinYinUtil.parseSimplePY(communityReceipt.getEstate().getUnitCode())+ 
							"/"+fileName+".pdf");
					CommunityReceiptForm communityReceiptForm = new CommunityReceiptForm();
					communityReceiptForm.setCommunityReceiptId(communityReceipt.getId());
					communityReceiptForm.setPdfUrl(communityReceipt.getPdfUrl());
					res.setCommunityReceiptForm(communityReceiptForm);
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				}else {
					res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
				}
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	public Map<String,Map<String,Object>> getCompoundReceivables(List<CommunityReceiptReceivablesEntity> receiptReceivablesList){
		Map<String,Map<String,Object>> map = new HashMap<String,Map<String,Object>>();
		
//		Map<String, List<CommunityReceivablesEntity>> collect1 = (Map<String, List<CommunityReceivablesEntity>>) receivablesList
//				.parallelStream().collect(groupingBy(CommunityReceivablesEntity::getPayItemsName));
//		for (String payItemsName : collect1.keySet()) {
//			
//		}
		for(CommunityReceiptReceivablesEntity o : receiptReceivablesList) {
			Map<String,Object> data = null;
//			//预防收费项目为空的情况
//			Integer payItemsId = null!=o.getReceivables().getPayItem() ?
//					o.getReceivables().getPayItem().getId() : o.getReceivables().getId() * 300;
			String payItemsName = o.getReceivables().getPayItemsName();
			if(map.containsKey(payItemsName)) {
				data = map.get(payItemsName);
				data.put("amount", o.getCurrentAmount().add((BigDecimal) data.get("amount")));
				if(o.getReceivables().getReceivableDate().before((Date) data.get("startReceivableDate"))) {
					data.put("startReceivableDate", o.getReceivables().getReceivableDate());
				}else if(o.getReceivables().getReceivableDate().after((Date) data.get("endReceivableDate"))) {
					data.put("endReceivableDate", o.getReceivables().getReceivableDate());
				}
				map.put(payItemsName, data);
			}else {
				data = new HashMap<String,Object>();
				data.put("payItemsName", o.getReceivables().getPayItemsName());
				data.put("startReceivableDate", o.getReceivables().getReceivableDate());
				data.put("endReceivableDate", o.getReceivables().getReceivableDate());
				if(null!=o.getReceivables().getPayItem()) {
					if(null != o.getReceivables().getPayItem()) {
						if(o.getReceivables().getPayItem().getPayDate()!=0 &&
								o.getReceivables().getPayItem().getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_ALLOCATION 
								&& o.getReceivables().getPayItem().getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_BREAK &&
										o.getReceivables().getPayItem().getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_DEPOSIT &&
												o.getReceivables().getPayItem().getChargeCategory() != CommunityContext.COMMUNITY_PAY_ITEMS_CHARGE_CATEGORY_OTHER) {
							data.put("unitPrice", 
									o.getReceivables().getPayItem().getPrice().toString()+
									(StringUtils.isNotEmpty(o.getReceivables().getPayItem().getPriceUnit()) ? 
											o.getReceivables().getPayItem().getPriceUnit() : ""));
						}else {
							data.put("unitPrice", "");
						}
					}
				}

				data.put("amount", o.getCurrentAmount());
				map.put(payItemsName, data);
			}
		}
		
		return map;
	}

	private static boolean createMultilayerFile(String dir) {
		try {
			File dirPath = new File(dir);
			if (!dirPath.exists()) {
				dirPath.mkdirs();
			}
		} catch (Exception e) {
			System.out.println("创建多层目录操作出错: " + e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}

}