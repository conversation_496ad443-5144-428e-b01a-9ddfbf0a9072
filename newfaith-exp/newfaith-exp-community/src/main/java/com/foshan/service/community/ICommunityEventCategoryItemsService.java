package com.foshan.service.community;

import com.foshan.form.community.request.CommunityEventCategoryItemsReq;
import com.foshan.form.response.IResponse;

public interface ICommunityEventCategoryItemsService {
	public IResponse getCommunityEventCategoryItemsList(CommunityEventCategoryItemsReq req);
	public IResponse addCommunityEventCategoryItems(CommunityEventCategoryItemsReq req);
	public IResponse modifyCommunityEventCategoryItems(CommunityEventCategoryItemsReq req);
	public IResponse deleteCommunityEventCategoryItems(CommunityEventCategoryItemsReq req);
	public IResponse getCommunityEventCategoryItemsInfo(CommunityEventCategoryItemsReq req);
}
