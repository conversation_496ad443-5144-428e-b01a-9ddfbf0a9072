package com.foshan.service.community.impl;

import com.foshan.dao.generic.Page;
import com.foshan.entity.RoleEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityMemberForm;
import com.foshan.form.community.CommunityMemberPropertyForm;
import com.foshan.form.community.request.CommunityMemberReq;
import com.foshan.form.community.response.communityMember.AddCommunityMemberRes;
import com.foshan.form.community.response.communityMember.GetCommunityMemberInfoRes;
import com.foshan.form.community.response.communityMember.GetCommunityMemberListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityMemberService;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import com.foshan.form.community.request.MergeMemberInfoReq;

@Transactional
@Service("communityMemberService")
public class CommunityMemberServiceImpl extends GenericCommunityService implements ICommunityMemberService {

	@Override
	public IResponse getCommunityMemberList(CommunityMemberReq req) {
		GetCommunityMemberListRes res = new GetCommunityMemberListRes();
		Page<CommunityMemberEntity> page = new Page<CommunityMemberEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		
		StringBuilder hql = new StringBuilder("select distinct a from CommunityMemberEntity a ");
		if(null!=req.getBindingState() && req.getBindingState()==0) {
			hql.append("where a.id not in(select distinct a1.id from CommunityMemberEntity a1  inner join a1.memberPropertyList b )")
				.append(null!=req.getUserState() ? " and a.userState="+req.getUserState():
				"  and a.userState="+EntityContext.RECORD_STATE_VALID);
		}else if((null!=req.getBindingState() && req.getBindingState()==1) 
				 || StringUtils.isNotEmpty(req.getUnitCode())) {
			hql.append("inner join a.memberPropertyList b inner join b.property c where 1=1")
				.append(StringUtils.isNotEmpty(req.getPropertyIdList()) ?" and c.id in ("+req.getPropertyIdList()+")":"")
				.append(StringUtils.isNotEmpty(req.getUnitCode()) ?" and c.unitCode like'%"+req.getUnitCode()+"%' ":"")
				.append(null!=req.getUserState() ? " and a.userState="+req.getUserState():
					"  and a.userState="+EntityContext.RECORD_STATE_VALID);
		}else {
			hql.append(null!=req.getUserState() ? " where a.userState="+req.getUserState():
				"  where a.userState="+EntityContext.RECORD_STATE_VALID);
		}
		hql.append(null!=req.getMemberType() ? " and  a.memberType="+req.getMemberType():"");
		hql.append(StringUtils.isNotEmpty(req.getUserName()) ?" and  a.userName like '%" + req.getUserName() + "%'" :"")
			.append(StringUtils.isNotEmpty(req.getHomePhone()) ?" and  a.homePhone like '%" + req.getHomePhone() + "%'" :"")
			.append(StringUtils.isNotEmpty(req.getIdCard()) ?" and  a.idCard like '%" + req.getIdCard() + "%'" :"")
			.append(StringUtils.isNotEmpty(req.getPhone()) ?" and  a.phone like '%" + req.getPhone() + "%'" :"")
			.append(StringUtils.isNotEmpty(req.getMemberIdList()) ?" and a.id in ("+req.getMemberIdList()+")":"")
			.append(null!=req.getParentMemberId() ? " and a.parentMember.id="+req.getParentMemberId():"");

		page = communityMemberDao.queryPage(page, hql.toString());
		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		page.getResultList().forEach(o -> {
			CommunityMemberForm memberForm = new CommunityMemberForm();
			memberForm.setMemberId(o.getId());
			memberForm.setEmail(o.getEmail());
			memberForm.setHomePhone(o.getHomePhone());
			memberForm.setNickName(o.getNickName());
			memberForm.setOfficePhone(o.getOfficePhone());
			memberForm.setPhone(o.getPhone());
			memberForm.setRegistName(o.getRegistName());
			memberForm.setSex(o.getSex());
			memberForm.setSmartcardId(o.getSmartcardId());
			memberForm.setHeadImage(StringUtils.isNotEmpty(o.getHeadImage()) ? o.getHeadImage() : "");
			memberForm.setUserName(StringUtils.isNotEmpty(o.getUserName()) ? o.getUserName() : "");
			memberForm.setState(null != o.getUserState()?o.getUserState():null);
			memberForm.setRegistTime(null != o.getRegistTime()?sdf.format(o.getRegistTime()):null);
			memberForm.setRegionCode(StringUtils.isNotEmpty(o.getRegionCode()) ? o.getRegionCode() : "");
			memberForm.setIdCard(StringUtils.isNotEmpty(o.getIdCard()) ? o.getIdCard() : "");
			memberForm.setHomeAddress(StringUtils.isNotEmpty(o.getHomeAddress()) ? o.getHomeAddress() : "");
			memberForm.setOfficeAddress(StringUtils.isNotEmpty(o.getOfficeAddress()) ? o.getOfficeAddress() : "");
			
			o.getMemberPropertyList().forEach(p->{
				CommunityMemberPropertyForm  memberPropertyForm = new CommunityMemberPropertyForm();
				memberPropertyForm.setAuditState(p.getAuditState());
				memberPropertyForm.setCreateTime(sdf.format(p.getCreateTime()));
//				memberPropertyForm.setInTime(sdf.format(p.getInTime()));
				memberPropertyForm.setMemberPropertyId(p.getId());
				memberPropertyForm.setPropertyForm(getCommunityPropertyForm(p.getProperty(),null,null));
//				memberPropertyForm.setRelation(p.getRelation());
				memberForm.getMemberPropertyFormList().add(memberPropertyForm);
			});
			res.getMemberFormList().add(memberForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		
		return res;
	}

	@Audit(operate = "新增业主")
	@Override
	public AddCommunityMemberRes addCommunityMember(CommunityMemberReq req) {
		AddCommunityMemberRes res = new AddCommunityMemberRes();
		String phone = (StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : "");
		if (StringUtils.isEmpty(phone) ||
				StringUtils.isEmpty(req.getRoleIdList())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO );
			return res;
		}

		CommunityMemberEntity member = communityMemberDao.getUniqueBySql("SELECT * FROM t_account WHERE phone='"+req.getPhone()+"' and userState = 1",
				"");
		if (null != member) {
			//普通注册流程
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("对不起，您注册的用户名或手机号已被注册！");
			return res;
		} else {
			String[] rolerId = req.getRoleIdList().split(",");
			List<RoleEntity> roleList = new ArrayList<RoleEntity>();
			for (String id : rolerId) {
				RoleEntity role = roleDao.get(Integer.valueOf(id));
				if (null != role) {
					roleList.add(role);
				}
			}
			
			member = new CommunityMemberEntity();
			//member.setLoginName(req.getPhone());
			//member.setNickName(req.getPhone());
			//密码选填
			String password = req.getPassword();
			if(StringUtils.isNotBlank(password)) {
				try {
					member.setPassword(DigestUtil.sm3Digest(req.getPassword()));
					member.setLastPwdModifyTime(new Timestamp(new Date().getTime()));
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
			}
			//member.setRegistName(req.getPhone());
			member.setPhone(phone);
			member.setUserName(StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() :"");
//			member.setCommunityOrganization(Community);
			//member.setWorkingState(null!=req.getWorkingState() ? req.getWorkingState() : 0);
			//member.setPoliticCountenance(null!=req.getPoliticCountenance() ? req.getPoliticCountenance() : 5);
			member.setPhoneVerifyState(EntityContext.RECORD_STATE_VALID);
			member.setIdCard(StringUtils.isNotEmpty(req.getIdCard())?req.getIdCard():"");
			member.setUserState(EntityContext.RECORD_STATE_VALID);
			member.setSex(null != req.getSex() ? req.getSex() : 2);
			//member.setOrders(null!=req.getOrders() ? req.getOrders() :10);
			member.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "");
			member.setCommunityRoleList(roleList);
			member.setHeadImage(StringUtils.isNotEmpty(req.getHeadImage()) ? req.getHeadImage() :"");
			//member.setCommunityPosition(null!=req.getCommunityPosition()?req.getCommunityPosition():null);
			//member.setWorkingState(null!=req.getWorkingState() ? req.getWorkingState():null);
			//member.setDevelopmentStages(null!=req.getDevelopmentStages()?req.getDevelopmentStages():null);
			member.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment(): "");
			member.setComment(req.getComment());
			member.setContactPerson(req.getContactPerson());
			member.setEmergencyContact(req.getEmergencyContact());
			member.setRelation(req.getRelation());
//			member.setBuyersName(req.getBuyersName());
//			member.setBuyersAddress(req.getBuyersAddress());
//			member.setBusinessType(req.getBusinessType());
//			member.setInvoiceType(req.getInvoiceType());
//			member.setPaytaxNo(req.getPaytaxNo());
//			member.setBuyersBankAccount(req.getBuyersBankAccount());
			member.setTvNo(req.getTvNo());
			member.setMac(req.getMac());
			member.setIp(req.getIp());
			member.setWeixinOpenId(req.getWeixinOpenId());
			member.setWeixinAvatar(req.getWeixinAvatar());
			member.setHomeAddress(req.getHomeAddress());
			member.setCompany(req.getCompany());
			member.setPostalCode(req.getPostalCode());
			member.setNation(req.getNation());
			member.setPoliceStation(req.getPoliceStation());
			member.setNativePlace(req.getNativePlace());
			member.setOfficeAddress(req.getOfficeAddress());
			member.setExchangeRegion(req.getExchangeRegion());
			member.setExchangeHall(req.getExchangeHall());
			member.setMiniProgramOpenId(req.getMiniProgramOpenId());
			member.setIsSmartcardMaster(req.getIsSmartcardMaster());
			member.setBirthday(req.getBirthday());
			member.setPhoneVerifyState(req.getPhoneVerifyState());
			member.setQq(req.getQq());
			member.setWeixin(req.getWeixin());
			member.setPhoto(req.getPhoto());
			member.setIdType(req.getIdType());
			member.setIdCard(req.getIdCard());
			member.setMemberType(req.getMemberType());
			//String wechatOpenid = (String) request.getSession().getAttribute("wechatOpenid");

			communityMemberDao.save(member);

		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}


	@Audit(operate = "修改业主")
	@Override
	public IResponse modifyCommunityMember(CommunityMemberReq req) {
		GenericResponse res = new GenericResponse();
//		Object userObj = getPrincipal(true);
//		if (null != userObj && userObj instanceof MemberEntity) {
		//if(null!=req.getMemberId() ) {
		Object userObj = getPrincipal(true);
		if (null != userObj) {
			CommunityMemberEntity member = null;
			if(userObj instanceof CommunityMemberEntity) {
				member = (CommunityMemberEntity) userObj;
			}else if(!(userObj instanceof CommunityMemberEntity) &&  null!=req.getMemberId()) {
				if(null==req.getMemberId()) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
					return res;
				}
				member = communityMemberDao.get(req.getMemberId());
				if(null == member) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
			}
			if (null != member) {
				if(StringUtils.isNotEmpty(req.getRoleIdList())) {
					String[] rolerId = req.getRoleIdList().split(",");
					List<RoleEntity> roleList = new ArrayList<RoleEntity>();
					for (String id : rolerId) {
						RoleEntity role = roleDao.get(Integer.valueOf(id));
						if (null != role) {
							roleList.add(role);
						}
					}
					member.setCommunityRoleList(roleList);
				}

				if(StringUtils.isNotEmpty(req.getPhone()) && !req.getPhone().equals(member.getPhone())) {
					CommunityMemberEntity m= communityMemberDao.getUniqueBySql(
							"SELECT * FROM t_account WHERE phone='"+req.getPhone()+"' and userState = 1","");
					if (null != m && !m.getId().toString().equalsIgnoreCase(req.getMemberId().toString())) {
						//普通注册流程
						res.setRet(ResponseContext.RES_DATA_DUPLICATE_CODE);
						res.setRetInfo("[" + req.getPhone() + "]" + "该手机号已被注册！用户ID为：" + m.getId());
						return res;
					}
				}
				
				member.setEmail(StringUtils.isNotEmpty(req.getEmail()) ? req.getEmail() : member.getEmail());
				member.setPhone(StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : member.getPhone());
				member.setSex(null != req.getSex() ? req.getSex() : member.getSex());
				member.setSmartcardId(
						StringUtils.isNotEmpty(req.getSmartcardId()) ? req.getSmartcardId() : member.getSmartcardId());
				member.setHeadImage(
						StringUtils.isNotEmpty(req.getHeadImage()) ? req.getHeadImage() : member.getHeadImage());
				member.setRegionCode(
						StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : member.getRegionCode());
				member.setUserName(
						StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() : member.getUserName());
				member.setHomePhone(
						StringUtils.isNotEmpty(req.getHomePhone()) ? req.getHomePhone() : member.getHomePhone());

				member.setComment(req.getComment());
				member.setContactPerson(req.getContactPerson());
				member.setEmergencyContact(req.getEmergencyContact());
				member.setHomeAddress(req.getHomeAddress());
				member.setCompany(req.getCompany());
				member.setPostalCode(req.getPostalCode());
				member.setNation(req.getNation());
				member.setPoliceStation(req.getPoliceStation());
				member.setNativePlace(req.getNativePlace());
				member.setBirthday(req.getBirthday());
				member.setIdType(req.getIdType());
				member.setIdCard(req.getIdCard());

				member.setRelation(StringUtils.isNotEmpty(req.getRelation()) ? req.getRelation() : member.getRelation());
				member.setTvNo(StringUtils.isNotEmpty(req.getTvNo()) ? req.getTvNo() : member.getTvNo());
				member.setMac(StringUtils.isNotEmpty(req.getMac()) ? req.getMac() : member.getMac());
				member.setIp(StringUtils.isNotEmpty(req.getMac()) ? req.getIp() : member.getIp());
				member.setWeixinOpenId(StringUtils.isNotEmpty(req.getWeixinOpenId()) ? req.getWeixinOpenId() : member.getWeixinOpenId());
				member.setWeixinAvatar(StringUtils.isNotEmpty(req.getWeixinAvatar()) ? req.getWeixinAvatar() : member.getWeixinAvatar() );
				member.setOfficeAddress(StringUtils.isNotEmpty(req.getOfficeAddress()) ? req.getOfficeAddress() : member.getOfficeAddress() );
				member.setExchangeRegion(StringUtils.isNotEmpty(req.getExchangeRegion()) ? req.getExchangeRegion() : member.getExchangeRegion() );
				member.setExchangeHall(StringUtils.isNotEmpty(req.getExchangeHall()) ? req.getExchangeHall() : member.getExchangeHall() );
				member.setMiniProgramOpenId(StringUtils.isNotEmpty(req.getMiniProgramOpenId()) ? req.getMiniProgramOpenId() : member.getMiniProgramOpenId() );
				member.setIsSmartcardMaster(null != req.getIsSmartcardMaster() ? req.getIsSmartcardMaster() : member.getIsSmartcardMaster() );
				member.setPhoneVerifyState(null != req.getPhoneVerifyState()  ? req.getPhoneVerifyState() : member.getPhoneVerifyState() );
				member.setQq(StringUtils.isNotEmpty(req.getQq()) ? req.getQq() : member.getQq() );
				member.setWeixin(StringUtils.isNotEmpty(req.getWeixin()) ? req.getWeixin() : member.getWeixin());
				member.setPhoto(StringUtils.isNotEmpty(req.getPhoto()) ? req.getPhoto() : member.getPhoto() );

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo("参数有误！！");
			}
		}

//		}else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO );
//		}
//		} else {
//			res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
//			res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
//		}
		return res;
	}

	@Audit(operate = "合并业主信息")
	@Override
	public IResponse mergeCommunityMember(MergeMemberInfoReq req){
		GenericResponse res = new GenericResponse();
		if(null == req.getSourceMemberId() || null == req.getTargetMemberId()){
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		CommunityMemberEntity sourceMember= communityMemberDao.get(req.getSourceMemberId());
		CommunityMemberEntity targetMember= communityMemberDao.get(req.getTargetMemberId());
		// 可以合并前提条件是，源会员没有绑定任何房产
		if(null != sourceMember.getMemberPropertyList() && !sourceMember.getMemberPropertyList().isEmpty()){
			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "用户ID"+sourceMember.getId()+"["+sourceMember.getPhone()+"]名下存在房产，"+"整合失败，请联系管理员！");
			return res;
		}
		String oldPhone = targetMember.getPhone();
		targetMember.setPhone(sourceMember.getPhone());
		if(!sourceMember.getWxServiceAccountList().isEmpty()){
			targetMember.getWxServiceAccountList().addAll(sourceMember.getWxServiceAccountList());
			sourceMember.getWxServiceAccountList().clear();
		}
		sourceMember.setUserState(0);
		sourceMember.setPhoneVerifyState(0);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO + "该业主的注册电话已经从["+oldPhone+"]改为["+sourceMember.getPhone()+"]，原用户的账号ID为["+sourceMember.getId()+"]已经注销！");

		return  res;
	}

	@Audit(operate = "删除业主")
	@Override
	public IResponse deleteCommunityMember(CommunityMemberReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getMemberId()) {
			CommunityMemberEntity member = communityMemberDao.get(req.getMemberId());
			if (null != member) {
				member.setUserState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}


	@Override
	public IResponse getCommunityMemberInfo(CommunityMemberReq req) {
		GetCommunityMemberInfoRes res = new GetCommunityMemberInfoRes();
		CommunityMemberForm memberForm = new CommunityMemberForm();
		Object userObj = getPrincipal(true);
		if (null != userObj) {
			CommunityMemberEntity member = null;
			if(userObj instanceof CommunityMemberEntity) {
				member = (CommunityMemberEntity) userObj;
			}else if(!(userObj instanceof CommunityMemberEntity) &&  null!=req.getMemberId()) {
				if(null==req.getMemberId()) {
					res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
					return res;
				}
				member = communityMemberDao.get(req.getMemberId());
				if(null == member) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				memberForm.setEmail(member.getEmail());
				memberForm.setHomePhone(member.getHomePhone());
				memberForm.setNickName(member.getNickName());
				memberForm.setOfficePhone(member.getOfficePhone());
				memberForm.setRegistName(member.getRegistName());
				memberForm.setState(member.getUserState());
				memberForm.setIdCard(member.getIdCard());
				memberForm.setContactPerson(member.getContactPerson());
				memberForm.setEmergencyContact(member.getEmergencyContact());
				memberForm.setRelation(member.getRelation());
//				memberForm.setBuyersName(member.getBuyersName());
//				memberForm.setBuyersAddress(member.getBuyersAddress());
				memberForm.setComment(member.getComment());
				memberForm.setIdType(member.getIdType());
				memberForm.setHomeAddress(member.getHomeAddress());
//				memberForm.setBuyersBankAccount(member.getBuyersBankAccount());
				memberForm.setWeixinOpenId(member.getWeixinOpenId());
				memberForm.setWeixinAvatar(member.getWeixinAvatar());
				memberForm.setPostalCode(member.getPostalCode());
				memberForm.setNation(member.getNation());
				memberForm.setPoliceStation(member.getPoliceStation());
				memberForm.setNativePlace(member.getNativePlace());
				memberForm.setOfficeAddress(member.getOfficeAddress());
				memberForm.setExchangeRegion(member.getExchangeRegion());
				memberForm.setExchangeHall(member.getExchangeHall());
				memberForm.setMiniProgramOpenId(member.getMiniProgramOpenId());
				memberForm.setQq(member.getQq());
				memberForm.setWeixin(member.getWeixin());
			}else {
				res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
				return res;
			}

			memberForm.setMemberId(member.getId());
//			memberForm.setCommunityId(null!=member.getCommunityOrganization() ? member.getCommunityOrganization().getId():null);
			memberForm.setSex(member.getSex());
			memberForm.setSmartcardId(member.getSmartcardId());
//			memberForm.setBusinessType(member.getBusinessType());
			memberForm.setBirthday(member.getBirthday());
			//memberForm.setOrders(member.getOrders());
			memberForm.setHeadImage(StringUtils.isNotEmpty(member.getHeadImage()) ? member.getHeadImage() : "");
			memberForm.setUserName(StringUtils.isNotEmpty(member.getUserName()) ? member.getUserName() : "");
//			memberForm.setPaytaxNo(member.getPaytaxNo());
			memberForm.setCompany(member.getCompany());
			memberForm.setMemberType(member.getMemberType());
			memberForm.setPhone(member.getPhone());
//			memberForm.setInvoiceType(member.getInvoiceType());
			memberForm.setTvNo(member.getTvNo());
			memberForm.setMac(member.getMac());
			memberForm.setIp(member.getIp());
			memberForm.setIsSmartcardMaster(member.getIsSmartcardMaster());
			memberForm.setUserCode(member.getUserCode());
			memberForm.setLoginName(member.getLoginName());
			memberForm.setPhoneVerifyState(member.getPhoneVerifyState());
			memberForm.setMailVerifyState(member.getMailVerifyState());
			memberForm.setUserState(member.getUserState());
			memberForm.setPhoto(member.getPhoto());
			memberForm.setLastPwdModifyTime(null!=member.getLastPwdModifyTime() ? 
					DateUtil.formatLongFormat(member.getLastPwdModifyTime()):"");
			
			DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
			member.getMemberPropertyList().forEach(o->{
				CommunityMemberPropertyForm  memberPropertyForm = new CommunityMemberPropertyForm();
				memberPropertyForm.setAuditState(o.getAuditState());
				memberPropertyForm.setMemberType(o.getMemberType());
				memberPropertyForm.setIsCurrentMember(o.getIsCurrentMember());
				memberPropertyForm.setIsCurrentOwner(o.getIsCurrentOwner());
				memberPropertyForm.setCreateTime(sdf.format(o.getCreateTime()));
//				memberPropertyForm.setInTime(sdf.format(o.getInTime()));
				memberPropertyForm.setMemberPropertyId(o.getId());
				memberPropertyForm.setPropertyForm(getCommunityPropertyForm(o.getProperty(),null,null));
//				memberPropertyForm.setRelation(o.getRelation());
				memberForm.getMemberPropertyFormList().add(memberPropertyForm);
			});
			memberForm.setRegionCode(StringUtils.isNotEmpty(member.getRegionCode()) ? member.getRegionCode() : "");
			res.setMemberForm(memberForm);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_PERM_INSUFFICIENT_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_INSUFFICIENT_INFO);
		}
		return res;
	}

}
