package com.foshan.service.community;

import com.foshan.form.community.request.CommunityPayItemsReq;
import com.foshan.form.response.IResponse;

public interface ICommunityPayItemsService {
    public IResponse getCommunityPayItemsList(CommunityPayItemsReq req);
	public IResponse addCommunityPayItems(CommunityPayItemsReq req);
	public IResponse modifyCommunityPayItems(CommunityPayItemsReq req);
	public IResponse deleteCommunityPayItems(CommunityPayItemsReq req);
	public IResponse getCommunityPayItemsInfo(CommunityPayItemsReq req);
	public IResponse getCommunityPayItemsListByProperty(CommunityPayItemsReq req);
}

