package com.foshan.service.community;

import com.foshan.form.community.request.ReceiveMessageTypeReq;
import com.foshan.form.response.IResponse;

public interface IReceiveMessageTypeService {
    public IResponse getReceiveMessageTypeList(ReceiveMessageTypeReq req);
	public IResponse addReceiveMessageType(ReceiveMessageTypeReq req);
	public IResponse modifyReceiveMessageType(ReceiveMessageTypeReq req);
	public IResponse deleteReceiveMessageType(ReceiveMessageTypeReq req);
	public IResponse getReceiveMessageTypeInfo(ReceiveMessageTypeReq req);
}

