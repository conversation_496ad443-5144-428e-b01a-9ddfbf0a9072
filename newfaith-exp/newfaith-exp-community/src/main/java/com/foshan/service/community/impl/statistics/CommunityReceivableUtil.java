package com.foshan.service.community.impl.statistics;

import static java.util.stream.Collectors.groupingBy;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jdbc.Work;

import com.foshan.form.community.statistics.CommunityReceivablesVo;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityCache;

import cn.hutool.core.util.ArrayUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CommunityReceivableUtil {

	public static String getCommunityReceivablesSql(Integer[] districtIds,Integer[] buildingIds, String[] chargeCategorys,
			String[] payItemsNames, String startDate, String endDate, Integer receivableAmountFlag,
			Integer receivedAmountFlag, Integer arrearsFlag, Integer onlyArrears) {

		String base1 = null != receivableAmountFlag && receivableAmountFlag.equals(1)
				? "GROUP_CONCAT(CONCAT('MAX(CASE payitemsname WHEN ''',payitemsname,''' then receivableamount else 0 end) as ',payitemsname)),"
				: "";
		String base2 = null != receivedAmountFlag && receivedAmountFlag.equals(1)
				? "GROUP_CONCAT(CONCAT('MAX(CASE payitemsname WHEN ''',payitemsname,''' then receivedAmount else 0 end) as ',payitemsname,'已收金额')),"
				: "";
		String base3 = null != arrearsFlag && arrearsFlag.equals(1)
				? "GROUP_CONCAT(CONCAT('MAX(CASE payitemsname WHEN ''',payitemsname,''' then arrears else 0 end) as ',payitemsname,'欠费')),"
				: "";
		String base = base1
				+ (null != receivableAmountFlag && receivableAmountFlag.equals(1) && null != receivedAmountFlag
						&& receivedAmountFlag.equals(1) ? "','," : "")
				+ base2
				+ (null != receivedAmountFlag && receivedAmountFlag.equals(1) && null != arrearsFlag
						&& arrearsFlag.equals(1) ? "',',"
								: (null != receivableAmountFlag && receivableAmountFlag.equals(1) && null != arrearsFlag
										&& arrearsFlag.equals(1) ? "','," : ""))
				+ base3;

		base = base.endsWith(",") ? base.substring(0, base.length() - 1) : base;

		StringBuilder sql = new StringBuilder(
				"SELECT districtCode,CONCAT('select districtCode,districtName,buildingName,',")
				.append("'unitCode,receivableDate,userName,estateState,bankName,bankAccount,accountName,',")
				.append("'SUM(receivableamount) AS totalReceivableAmount,',")
				.append("'SUM(receivedamount) AS totalReceivedAmount,',")
				.append("'SUM(arrears) AS totalArrears,buildingArea,recordDate,billingDate,comment,',")
				.append("CONCAT(" + base + "),")
				.append("' from v_community_receivables where districtcode=',districtcode,")
				.append(null != districtIds && districtIds.length > 0
						? "' and districtid in(" + StringUtils.join(districtIds, ",") + ") ',"
						: "'',")
				.append(null != buildingIds && buildingIds.length > 0
					? "' and buildingId in(" + StringUtils.join(buildingIds, ",") + ") ',"
							: "'',")
				.append(null != chargeCategorys && chargeCategorys.length > 0
						? "' and chargeCategory in(''" + StringUtils.join(chargeCategorys, "'',''") + "'') ',"
						: "'',")
				.append(null != payItemsNames && payItemsNames.length > 0
						? "' and payItemsName in(''" + StringUtils.join(payItemsNames, "'',''") + "'') ',"
						: "'',")
				.append((receivableAmountFlag.equals(0) && receivedAmountFlag.equals(0) && arrearsFlag.equals(1))
						|| onlyArrears.equals(1) ? "' and arrears<>0 '," : " ")
				.append(receivableAmountFlag.equals(0) && receivedAmountFlag.equals(1) && arrearsFlag.equals(0)
						? "' and receivedamount<>0 ',"
						: " ")
				.append("' AND receivabledate>=''" + startDate + "'' AND receivabledate<=''" + endDate + "'' ")
				.append("GROUP BY unitcode,receivabledate ',")
				.append("'ORDER BY districtOrder,buildingOrder,unitcode,receivabledate') as subSql ")
				.append("FROM (SELECT DISTINCT districtCode,payitemsname ").append("FROM v_community_receivables ")
				.append("WHERE receivabledate>='" + startDate + "' ").append("AND receivabledate<='" + endDate + "' ")
				.append(null != districtIds && districtIds.length > 0
						? "and districtid in(" + StringUtils.join(districtIds, ",") + ") "
						: " ")
				.append(null != buildingIds && buildingIds.length > 0
						? "and buildingId in(" + StringUtils.join(buildingIds, ",") + ") "
						: " ")
				.append(null != chargeCategorys && chargeCategorys.length > 0
						? "and chargeCategory in('" + StringUtils.join(chargeCategorys, "','") + "') "
						: " ")
				.append(null != payItemsNames && payItemsNames.length > 0
						? "and payItemsName in('" + StringUtils.join(payItemsNames, "','") + "') "
						: " ")
				.append((receivableAmountFlag.equals(0) && receivedAmountFlag.equals(0) && arrearsFlag.equals(1))
						|| onlyArrears.equals(1) ? "and arrears<>0 " : " ")
				.append("GROUP BY unitcode,receivabledate,payitemsname) aa GROUP BY districtcode");

		return sql.toString();
	}

	public static Map<String, Object> getCommunityReceivableList(Integer[] districtIds,Integer[] buildingIds, String[] chargeCategorys,
			String[] payItemsNames, String startDate, String endDate, Integer receivableAmountFlag,
			Integer receivedAmountFlag, Integer arrearsFlag, Integer onlyArrears) {
		Map<String, Object> res = new HashMap<>();

		String[] title = { "楼盘名称", "楼阁", "单元编号", "建筑面积", "入住时间", "计费日期", "应收日期", "姓名", "状态", "划账银行", "银行账户", "账户名" };
		List<CommunityReceivablesVo> dataList = new LinkedList<>();

		Set<String> feeTitle = new TreeSet<>();

		// 获取动态sql
		String groupSql = getCommunityReceivablesSql(districtIds,buildingIds, chargeCategorys, payItemsNames, startDate, endDate,
				receivableAmountFlag, receivedAmountFlag, arrearsFlag, onlyArrears);

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {
				Statement st = conn.createStatement();
				ResultSet group_rs = st.executeQuery(groupSql);

				while (group_rs.next()) {
					String districtCode = group_rs.getString("districtCode");
					String subSql = group_rs.getString("subSql");
					ResultSet subrs = st.executeQuery(subSql);

					while (subrs.next()) {
						ResultSetMetaData rsmd = subrs.getMetaData();
						CommunityReceivablesVo vo = new CommunityReceivablesVo();
						vo.setDistrictCode(districtCode.length() == 1 ? "0" + districtCode : districtCode);
						vo.setDistrictName(subrs.getString("districtName"));
						vo.setBuildingName(subrs.getString("buildingName"));
						vo.setUnitCode(subrs.getString("unitCode"));
						vo.setReceivableDate(subrs.getString("receivableDate"));
						vo.setUserName(subrs.getString("userName"));
						vo.setEstateState(subrs.getString("estateState"));
						vo.setBankNmae(subrs.getString("bankName"));
						vo.setBankAccount(subrs.getString("bankAccount"));
						vo.setAccountName(subrs.getString("accountName"));
						vo.setTotalReceivableAmount(subrs.getString("totalReceivableAmount"));
						vo.setTotalReceivedAmount(subrs.getString("totalReceivedAmount"));
						vo.setTotalArrears(subrs.getString("totalArrears"));
						vo.setBuildingArea(subrs.getString("buildingArea"));
						vo.setRecordDate(subrs.getString("recordDate"));
						vo.setBillingDate(subrs.getString("billingDate"));
						vo.setComment(subrs.getString("comment"));
						Map<String, String> feeMap = new TreeMap<>();
						for (int i = 18; i <= rsmd.getColumnCount(); i++) {
							feeTitle.add(rsmd.getColumnName(i));
							feeMap.put(rsmd.getColumnName(i), subrs.getString(i));
						}
						vo.setFeeMap(feeMap);
						dataList.add(vo);
					}

				}

			}
		});
		session.close();

		for (String fee : feeTitle) {
			title = ArrayUtil.insert(title, title.length, fee);
		}

		res.put("title", title);
		res.put("data", dataList);

		return res;
	}

	/**
	 * @param response
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static void transReceivablesExcel(HttpServletResponse response) {

		// 获取缓存数据并进行处理
		Map<String, Map<String, Object>> temp1 = CommunityCache.statisticsCache.get("应收报表");
		String key = "";
		for (String o : temp1.keySet()) {
			key = o;
		}

		String[] paras = key.split("_");
		Integer receivableAmountFlag = Integer.parseInt(paras[1]);
		Integer receivedAmountFlag = Integer.parseInt(paras[2]);
		Integer arrearsFlag = Integer.parseInt(paras[3]);

		Map<String, Object> temp2 = temp1.get(key);
		String[] titles = (String[]) temp2.get("title");
		for (int i = 1; i <= (receivableAmountFlag + receivedAmountFlag + arrearsFlag); i++) {
			titles = ArrayUtil.insert(titles, titles.length, "合计");
		}

//		titles = ArrayUtil.insert(titles, titles.length, "备注");

		List<CommunityReceivablesVo> dataList = (List<CommunityReceivablesVo>) temp2.get("data");

		// 将缓存的队列数据按楼盘编码进行排序
		Map<String, List<CommunityReceivablesVo>> districtReceivableMap = (HashMap<String, List<CommunityReceivablesVo>>) dataList
				.stream().collect(groupingBy(CommunityReceivablesVo::getNewDistrictCode));

		if (districtReceivableMap.size() > 1) {
			districtReceivableMap.put("000全区", dataList);
		}

		TreeSet<String> keys = new TreeSet<>(Comparator.naturalOrder());
		districtReceivableMap.keySet().forEach(o -> {
			keys.add(o);
		});

		XSSFWorkbook wb = new XSSFWorkbook();
		Map<String, XSSFCellStyle> cellStyle = ExcelUtil.getExcelStyle(wb);

		for (String o : keys) {

			Sheet sheet = wb.createSheet(o.substring(3));

			LinkedList<CommunityReceivablesVo> tempList = new LinkedList(districtReceivableMap.get(o));

			// 构建表头
			Row title1 = sheet.createRow(0);
			Row title2 = null;
			if (receivableAmountFlag + receivedAmountFlag + arrearsFlag > 1) {
				title2 = sheet.createRow(1);
			}
			createTitle(sheet, cellStyle, title1, title2, titles, receivableAmountFlag, receivedAmountFlag,
					arrearsFlag);

			// 获取每楼盘下单元单元编号进行排序
//			List<CommunityReceivablesVo> unitList = tempList.stream()
//					.sorted(Comparator.comparing(CommunityReceivablesVo::getUnitCode)).collect(Collectors.toList());

			int rowNumber = null != title2 ? 2 : 1;
			for (CommunityReceivablesVo vo : tempList) {
				Row row = sheet.createRow(rowNumber);
				// 楼盘名称
				Cell cell0 = row.createCell(0, CellType.STRING);
				cell0.setCellStyle(cellStyle.get("cell_left"));
				cell0.setCellValue(vo.getDistrictName());

				// 楼阁
				Cell cell1 = row.createCell(1, CellType.STRING);
				cell1.setCellStyle(cellStyle.get("cell_left"));
				cell1.setCellValue(vo.getBuildingName());

				// 单元编号
				Cell cell2 = row.createCell(2, CellType.STRING);
				cell2.setCellStyle(cellStyle.get("cell_left"));
				cell2.setCellValue(vo.getUnitCode());

				// 建筑面积
				Cell cell3 = row.createCell(3, CellType.STRING);
				cell3.setCellStyle(cellStyle.get("cell_left"));
				cell3.setCellValue(vo.getBuildingArea());

				// 入住时间
				Cell cell4 = row.createCell(4, CellType.STRING);
				cell4.setCellStyle(cellStyle.get("cell_left"));
				cell4.setCellValue(vo.getRecordDate());

				// 计费时间
				Cell cell5 = row.createCell(5, CellType.STRING);
				cell5.setCellStyle(cellStyle.get("cell_left"));
				cell5.setCellValue(vo.getBillingDate());

				// 应收日期
				Cell cell6 = row.createCell(6, CellType.STRING);
				cell6.setCellStyle(cellStyle.get("cell"));
				cell6.setCellValue(vo.getReceivableDate());

				// 姓名
				Cell cell7 = row.createCell(7, CellType.STRING);
				cell7.setCellStyle(cellStyle.get("cell_left"));
				cell7.setCellValue(vo.getUserName());

				// 状态
				Cell cell8 = row.createCell(8, CellType.STRING);
				cell8.setCellStyle(cellStyle.get("cell_left"));
				cell8.setCellValue(vo.getEstateState());

				// 划账银行
				Cell cell9 = row.createCell(9, CellType.STRING);
				cell9.setCellStyle(cellStyle.get("cell_left"));
				cell9.setCellValue(vo.getBankNmae());

				// 银行帐户
				Cell cell10 = row.createCell(10, CellType.STRING);
				cell10.setCellStyle(cellStyle.get("cell_left"));
				cell10.setCellValue(vo.getBankAccount());

				// 账号名
				Cell cell11 = row.createCell(11, CellType.STRING);
				cell11.setCellStyle(cellStyle.get("cell_left"));
				cell11.setCellValue(vo.getAccountName());

				if (sheet.getSheetName().equals("全区")) {
					for (int i = 1; i < titles.length - 11; i++) {
						Cell dcell = row.createCell(i + 11, CellType.NUMERIC);
						dcell.setCellStyle(cellStyle.get("cell_right2"));
						String payItemsName = "";
						if (receivableAmountFlag + receivedAmountFlag + arrearsFlag == 1) {
							payItemsName = title1.getCell(i + 11).getStringCellValue();
						} else if (receivableAmountFlag + receivedAmountFlag + arrearsFlag == 2) {
							if (title2.getCell(i + 11).getStringCellValue().equals("应收金额")) {
								payItemsName = title1.getCell(i + 11).getStringCellValue();
							} else {
								if (i % 2 == 0) {
									payItemsName = title1.getCell(i + 10).getStringCellValue()
											+ title2.getCell(i + 11).getStringCellValue();
								} else {
									payItemsName = title1.getCell(i + 11).getStringCellValue()
											+ title2.getCell(i + 11).getStringCellValue();
								}
							}
						} else {
							if (title2.getCell(i + 11).getStringCellValue().equals("应收金额")) {
								payItemsName = title1.getCell(i + 11).getStringCellValue();
							} else if (title2.getCell(i + 11).getStringCellValue().equals("已收金额")) {
								payItemsName = title1.getCell(i + 10).getStringCellValue()
										+ title2.getCell(i + 11).getStringCellValue();
							} else {
								payItemsName = title1.getCell(i + 9).getStringCellValue()
										+ title2.getCell(i + 11).getStringCellValue();
							}
						}

						payItemsName = payItemsName.startsWith("合计") ? "合计" : payItemsName;

						if (vo.getFeeMap().containsKey(payItemsName)) {
							dcell.setCellValue(new BigDecimal(vo.getFeeMap().get(payItemsName)).doubleValue());
						} else {
							if (payItemsName.equals("合计")) {
								dcell.setCellStyle(cellStyle.get("total_double"));
								if (receivableAmountFlag + receivedAmountFlag + arrearsFlag == 1) {
									if (receivableAmountFlag == 1) {
										dcell.setCellValue(new BigDecimal(vo.getTotalReceivableAmount()).doubleValue());
									} else if (receivedAmountFlag == 1) {
										dcell.setCellValue(new BigDecimal(vo.getTotalReceivedAmount()).doubleValue());
									} else {
										dcell.setCellValue(new BigDecimal(vo.getTotalArrears()).doubleValue());
									}
								} else {
									if (title2.getCell(i + 11).getStringCellValue().equals("应收金额")) {
										dcell.setCellValue(new BigDecimal(vo.getTotalReceivableAmount()).doubleValue());
									} else if (title2.getCell(i + 11).getStringCellValue().equals("已收金额")) {
										dcell.setCellValue(new BigDecimal(vo.getTotalReceivedAmount()).doubleValue());
									} else {
										dcell.setCellValue(new BigDecimal(vo.getTotalArrears()).doubleValue());
									}
								}
							} else {
								dcell.setBlank();
							}
						}

						Double cellValue = dcell.getNumericCellValue();
						if (null == cellValue || cellValue == 0) {
							dcell.setBlank();
						}
					}
				} else {
					Map<String, String> feeMap = vo.getFeeMap();
					int i = 1;
					for (String payItemsName : feeMap.keySet()) {

						if (receivableAmountFlag + receivedAmountFlag + arrearsFlag == 1) {
							Cell t1 = title1.createCell(i + 11, CellType.STRING);
							t1.setCellStyle(cellStyle.get("title"));
							t1.setCellValue(payItemsName);
						} else {
							if (null != title2 && null == title2.getCell(i + 11)) {
								Cell t2 = title2.createCell(i + 11, CellType.STRING);
								t2.setCellStyle(cellStyle.get("title"));
								t2.setCellValue(payItemsName.endsWith("欠费") ? "欠费"
										: payItemsName.endsWith("已收金额") ? "已收金额" : "应收金额");

								if (receivableAmountFlag + receivedAmountFlag + arrearsFlag == 2 && i % 2 == 0) {
									Cell t1 = title1.createCell(i + 10, CellType.STRING);
									t1.setCellStyle(cellStyle.get("title"));
									t1.setCellValue(payItemsName.endsWith("欠费")
											? payItemsName.substring(0, payItemsName.length() - 2)
											: payItemsName.endsWith("已收金额")
													? payItemsName.substring(0, payItemsName.length() - 4)
													: payItemsName);

									CellRangeAddress region = new CellRangeAddress(0, 0, i + 10, i + 11);
									sheet.addMergedRegion(region);
									ExcelUtil.addMergeCellBorder(region, sheet);
								} else if (receivableAmountFlag + receivedAmountFlag + arrearsFlag == 3 && i % 3 == 0) {
									Cell t1 = title1.createCell(i + 9, CellType.STRING);
									t1.setCellStyle(cellStyle.get("title"));
									t1.setCellValue(payItemsName);

									CellRangeAddress region = new CellRangeAddress(0, 0, i + 9, i + 11);
									sheet.addMergedRegion(region);
									ExcelUtil.addMergeCellBorder(region, sheet);
								}
							}
						}

						Cell dcell = row.createCell(i + 11, CellType.NUMERIC);
						dcell.setCellStyle(cellStyle.get("cell_right2"));
						dcell.setCellValue(new BigDecimal(feeMap.get(payItemsName)).doubleValue());

						Double cellValue = dcell.getNumericCellValue();
						if (null == cellValue || cellValue == 0) {
							dcell.setBlank();
						}
						i++;
					}

					// 合计
					createTotal(sheet, row, cellStyle, title1, title2, i, vo, receivableAmountFlag, receivedAmountFlag,
							arrearsFlag);

				}

				// 备注
				createComment(sheet, row, cellStyle, title1, title2, vo, receivableAmountFlag, receivedAmountFlag,
						arrearsFlag);

				rowNumber++;
			}

			// 合计行
			Row total = sheet.createRow(rowNumber);
			Cell totalStr = total.createCell(0, CellType.STRING);
			totalStr.setCellValue("合计");
			totalStr.setCellStyle(cellStyle.get("total_double"));

			CellRangeAddress region3 = new CellRangeAddress(rowNumber, rowNumber, 0, 11);
			sheet.addMergedRegion(region3);
			ExcelUtil.addMergeCellBorder(region3, sheet);

			int totalRowColumns = null != title2 ? title2.getLastCellNum() : title1.getLastCellNum();
			for (int i = 12; i < totalRowColumns - 1; i++) {
				Cell tt = total.createCell(i, CellType.NUMERIC);
				tt.setCellStyle(cellStyle.get("total_double"));
				tt.setCellFormula("sum(" + sheet.getRow(null != title2 ? 2 : 1).getCell(i).getAddress() + ":"
						+ sheet.getRow(rowNumber - 1).getCell(i).getAddress() + ")");
			}

			sheet.autoSizeColumn(0);
			sheet.autoSizeColumn(1);
			sheet.autoSizeColumn(2);
			sheet.autoSizeColumn(3);
			sheet.autoSizeColumn(4);
			sheet.autoSizeColumn(5);
			sheet.autoSizeColumn(6);
			sheet.setColumnWidth(title1.getLastCellNum() - 1, 35 * 256);

			// 冻结首行
			sheet.createFreezePane(0, null != title2 ? 2 : 1, 0, null != title2 ? 2 : 1);
			// 冻结首列
			sheet.createFreezePane(12, null != title2 ? 2 : 1, 12, null != title2 ? 2 : 1);
			// 增加筛选框
			CellRangeAddress c = CellRangeAddress.valueOf(sheet.getRow(null != title2 ? 1 : 0).getCell(0).getAddress()
					+ ":" + sheet.getRow(null != title2 ? 1 : 0)
							.getCell(sheet.getRow(null != title2 ? 1 : 0).getLastCellNum() - 1).getAddress());
			sheet.setAutoFilter(c);

		}
		String fileName = "应收报表" + DateUtil.formatByStyle(new Date(), "_yyyy_MM_dd_HH_mm_ss") + ".xlsx";

		try {
			ExcelExportUtil.export(response, wb, fileName);
		} catch (IOException e) {
			log.error(e.getMessage());
		}

	}

	private static void createTitle(Sheet sheet, Map<String, XSSFCellStyle> cellStyle, Row title1, Row title2,
			String[] titles, Integer receivableAmountFlag, Integer receivedAmountFlag, Integer arrearsFlag) {

		int columnCount = (receivableAmountFlag + receivedAmountFlag + arrearsFlag) == 3 ? (titles.length - 11) / 3 + 11
				: (receivableAmountFlag + receivedAmountFlag + arrearsFlag) == 2 ? (titles.length - 11) / 2 + 11
						: titles.length - 1;

		for (int i = 0; i <= columnCount; i++) {
			if (receivableAmountFlag + receivedAmountFlag + arrearsFlag == 1) {
				if (sheet.getSheetName().equals("全区")) {
					Cell tt = title1.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
					tt.setCellStyle(cellStyle.get("title"));
				} else {
					if (i <= 11) {
						Cell tt = title1.createCell(i, CellType.STRING);
						tt.setCellValue(titles[i]);
						tt.setCellStyle(cellStyle.get("title"));
					}
				}
			} else {
				if (i <= 11) {
					Cell tt = title1.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
					tt.setCellStyle(cellStyle.get("title"));

					CellRangeAddress region = new CellRangeAddress(0, 1, i, i);
					sheet.addMergedRegion(region);
					ExcelUtil.addMergeCellBorder(region, sheet);
				} else {
					if (sheet.getSheetName().equals("全区")) {
						if (receivableAmountFlag + receivedAmountFlag + arrearsFlag == 2) {
							Cell t1 = title1.createCell((2 * (i - 11) - 1) + 11, CellType.STRING);
							String payItemsName = titles[(2 * (i - 11) - 1) + 11];
							t1.setCellValue(
									payItemsName.endsWith("已收金额") ? payItemsName.substring(0, payItemsName.length() - 4)
											: payItemsName);
							t1.setCellStyle(cellStyle.get("title"));

							CellRangeAddress region = new CellRangeAddress(0, 0, (2 * (i - 11) - 1) + 11,
									2 * (i - 11) + 11);
							sheet.addMergedRegion(region);
							ExcelUtil.addMergeCellBorder(region, sheet);

							Cell t2_1 = title2.createCell((2 * (i - 11) - 1) + 11, CellType.STRING);
							Cell t2_2 = title2.createCell(2 * (i - 11) + 11, CellType.STRING);
							if (receivableAmountFlag.equals(1) && receivedAmountFlag.equals(1)) {
								t2_1.setCellValue("应收金额");
								t2_2.setCellValue("已收金额");
							} else if (receivableAmountFlag.equals(1) && arrearsFlag.equals(1)) {
								t2_1.setCellValue("应收金额");
								t2_2.setCellValue("欠费");
							} else {
								t2_1.setCellValue("已收金额");
								t2_2.setCellValue("欠费");
							}
							t2_1.setCellStyle(cellStyle.get("title"));
							t2_2.setCellStyle(cellStyle.get("title"));
						} else {
							Cell t1 = title1.createCell((3 * (i - 11) - 2) + 11, CellType.STRING);
							t1.setCellValue(titles[(3 * (i - 11) - 2) + 11]);
							t1.setCellStyle(cellStyle.get("title"));

							CellRangeAddress region = new CellRangeAddress(0, 0, (3 * (i - 11) - 2) + 11,
									3 * (i - 11) + 11);
							sheet.addMergedRegion(region);
							ExcelUtil.addMergeCellBorder(region, sheet);

							Cell t3_1 = title2.createCell((3 * (i - 11) - 2) + 11, CellType.STRING);
							t3_1.setCellValue("应收金额");
							t3_1.setCellStyle(cellStyle.get("title"));
							Cell t3_2 = title2.createCell((3 * (i - 11) - 1) + 11, CellType.STRING);
							t3_2.setCellValue("已收金额");
							t3_2.setCellStyle(cellStyle.get("title"));
							Cell t3_3 = title2.createCell(3 * (i - 11) + 11, CellType.STRING);
							t3_3.setCellValue("欠费");
							t3_3.setCellStyle(cellStyle.get("title"));
						}
					}
				}
			}

		}
	}

	private static void createTotal(Sheet sheet, Row row, Map<String, XSSFCellStyle> cellStyle, Row title1, Row title2,
			int column, CommunityReceivablesVo vo, Integer receivableAmountFlag, Integer receivedAmountFlag,
			Integer arrearsFlag) {
		// 合计
		if (receivableAmountFlag + receivedAmountFlag + arrearsFlag == 1) {
			if (null == title1.getCell(column + 11)) {
				Cell t1 = title1.createCell(column + 11, CellType.STRING);
				t1.setCellStyle(cellStyle.get("title"));
				t1.setCellValue("合计");
			}

			Cell t2 = row.createCell(column + 11, CellType.NUMERIC);
			t2.setCellStyle(cellStyle.get("total_double"));
			t2.setCellValue(receivableAmountFlag.equals(1) ? new BigDecimal(vo.getTotalReceivableAmount()).doubleValue()
					: receivedAmountFlag.equals(1) ? new BigDecimal(vo.getTotalReceivedAmount()).doubleValue()
							: new BigDecimal(vo.getTotalArrears()).doubleValue());
		} else if (receivableAmountFlag + receivedAmountFlag + arrearsFlag == 2) {
			if (null == title1.getCell(column + 11)) {
				Cell t1 = title1.createCell(column + 11, CellType.STRING);
				t1.setCellStyle(cellStyle.get("title"));
				t1.setCellValue("合计");

				CellRangeAddress region = new CellRangeAddress(0, 0, column + 11, column + 12);
				sheet.addMergedRegion(region);
				ExcelUtil.addMergeCellBorder(region, sheet);

				Cell t2 = title2.createCell(column + 11, CellType.STRING);
				t2.setCellStyle(cellStyle.get("title"));
				t2.setCellValue(receivableAmountFlag.equals(1) ? "应收金额" : "已收金额");

				Cell t3 = title2.createCell(column + 12, CellType.STRING);
				t3.setCellStyle(cellStyle.get("title"));
				t3.setCellValue(arrearsFlag.equals(1) ? "欠费" : "已收金额");

			}

			Cell t4 = row.createCell(column + 11, CellType.NUMERIC);
			t4.setCellStyle(cellStyle.get("total_double"));
			t4.setCellValue(receivableAmountFlag.equals(1) ? new BigDecimal(vo.getTotalReceivableAmount()).doubleValue()
					: new BigDecimal(vo.getTotalReceivedAmount()).doubleValue());

			Double cellValue1 = t4.getNumericCellValue();
			if (null == cellValue1 || cellValue1 == 0) {
				t4.setBlank();
			}

			Cell t5 = row.createCell(column + 12, CellType.NUMERIC);
			t5.setCellStyle(cellStyle.get("total_double"));
			t5.setCellValue(arrearsFlag.equals(1) ? new BigDecimal(vo.getTotalArrears()).doubleValue()
					: new BigDecimal(vo.getTotalReceivedAmount()).doubleValue());

			Double cellValue2 = t5.getNumericCellValue();
			if (null == cellValue2 || cellValue2 == 0) {
				t5.setBlank();
			}
		} else {
			if (null == title1.getCell(column + 11)) {
				Cell t1 = title1.createCell(column + 11, CellType.STRING);
				t1.setCellStyle(cellStyle.get("title"));
				t1.setCellValue("合计");

				CellRangeAddress region = new CellRangeAddress(0, 0, column + 11, column + 13);
				sheet.addMergedRegion(region);
				ExcelUtil.addMergeCellBorder(region, sheet);

				Cell t2 = title2.createCell(column + 11, CellType.STRING);
				t2.setCellStyle(cellStyle.get("title"));
				t2.setCellValue("应收金额");

				Cell t3 = title2.createCell(column + 12, CellType.STRING);
				t3.setCellStyle(cellStyle.get("title"));
				t3.setCellValue("已收金额");

				Cell t4 = title2.createCell(column + 13, CellType.STRING);
				t4.setCellStyle(cellStyle.get("title"));
				t4.setCellValue("欠费");
			}

			Cell t4 = row.createCell(column + 11, CellType.NUMERIC);
			t4.setCellStyle(cellStyle.get("total_double"));
			t4.setCellValue(new BigDecimal(vo.getTotalReceivableAmount()).doubleValue());

			Double cellValue1 = t4.getNumericCellValue();
			if (null == cellValue1 || cellValue1 == 0) {
				t4.setBlank();
			}

			Cell t5 = row.createCell(column + 12, CellType.NUMERIC);
			t5.setCellStyle(cellStyle.get("total_double"));
			t5.setCellValue(new BigDecimal(vo.getTotalReceivedAmount()).doubleValue());

			Double cellValue2 = t5.getNumericCellValue();
			if (null == cellValue2 || cellValue2 == 0) {
				t5.setBlank();
			}

			Cell t6 = row.createCell(column + 13, CellType.NUMERIC);
			t6.setCellStyle(cellStyle.get("total_double"));
			t6.setCellValue(new BigDecimal(vo.getTotalArrears()).doubleValue());

			Double cellValue3 = t6.getNumericCellValue();
			if (null == cellValue3 || cellValue3 == 0) {
				t6.setBlank();
			}
		}
	}

	private static void createComment(Sheet sheet, Row row, Map<String, XSSFCellStyle> cellStyle, Row title1,
			Row title2, CommunityReceivablesVo vo, Integer receivableAmountFlag, Integer receivedAmountFlag,
			Integer arrearsFlag) {
		if (null == title1.getCell(title1.getLastCellNum() - 1)
				|| !title1.getCell(title1.getLastCellNum() - 1).getStringCellValue().equals("备注")) {
			Cell t1 = title1.createCell(title1.getLastCellNum(), CellType.STRING);
			t1.setCellStyle(cellStyle.get("title"));
			t1.setCellValue("备注");
			
			if (receivableAmountFlag + receivedAmountFlag + arrearsFlag > 1) {
				CellRangeAddress region = new CellRangeAddress(0, 1, title1.getLastCellNum() - 1,
						title1.getLastCellNum() - 1);
				sheet.addMergedRegion(region);
				ExcelUtil.addMergeCellBorder(region, sheet);
			}
		}

		Cell t2 = row.createCell(row.getLastCellNum(), CellType.STRING);
		t2.setCellStyle(cellStyle.get("cell_left"));
		t2.setCellValue(vo.getComment());
	}

}
