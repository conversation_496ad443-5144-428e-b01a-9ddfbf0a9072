package com.foshan.service.community.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.foshan.dao.generic.Page;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.community.CommunityFormulaTempleteEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityMeterFormulaEntity;
import com.foshan.form.community.CommunityFormulaTempleteForm;
import com.foshan.form.community.CommunityMeterFormulaForm;
import com.foshan.form.community.OperationDiaryForm;
import com.foshan.form.community.request.CommunityMeterFormulaReq;
import com.foshan.form.community.response.communityMeterFormula.AddCommunityMeterFormulaRes;
import com.foshan.form.community.response.communityMeterFormula.GetCommunityMeterFormulaInfoRes;
import com.foshan.form.community.response.communityMeterFormula.GetCommunityMeterFormulaListRes;
import com.foshan.form.community.response.communityMeterFormula.ModifyCommunityMeterFormulaRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityMeterFormulaService;
import com.foshan.util.DateUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional
@Service("communityMeterFormulaService")
public class CommunityMeterFormulaServiceImpl extends GenericCommunityService implements ICommunityMeterFormulaService {

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getCommunityMeterFormulaList(CommunityMeterFormulaReq req) {
		GetCommunityMeterFormulaListRes res = new GetCommunityMeterFormulaListRes();
		Page<CommunityMeterFormulaEntity> page = new Page<CommunityMeterFormulaEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityMeterFormulaEntity a ");
		hql.append(null!=req.getCategory() ? " inner join a.meterList b inner join b.meterAttributes c where c.category ='"+req.getCategory()+"' " : " where 1=1 ")
			.append(StringUtils.isNotEmpty(req.getFormulaName()) ? " and a.formulaName like'%"+req.getFormulaName()+"%'":"");
		hql.append(" ORDER BY a.id desc");
		page = communityMeterFormulaDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			try {
				CommunityMeterFormulaForm communityMeterFormulaForm = new CommunityMeterFormulaForm();
				communityMeterFormulaForm.setCommunityMeterFormulaId(o.getId());
				communityMeterFormulaForm.setComment(StringUtils.isNotEmpty(o.getComment())  ? o.getComment() : "");
				communityMeterFormulaForm.setFormulaName(o.getFormulaName());
				if(StringUtils.isNotEmpty(o.getDynamicParameter())) {
					Map<String, BigDecimal> paras = mapper.readValue(o.getDynamicParameter(),
							new HashMap<String, BigDecimal>().getClass());
					communityMeterFormulaForm.setDynamicParameter(paras);
				}

				communityMeterFormulaForm
						.setLastDate(null != o.getLastDate() ? DateUtil.formatLongFormat(o.getLastDate()) : "");
//				communityMeterFormulaForm.setOldData(o.getOldData());
//				communityMeterFormulaForm.setOldId(o.getOldId());

				CommunityFormulaTempleteEntity templete = o.getTemplete();
				if (null != templete) {
					CommunityFormulaTempleteForm templeteForm = new CommunityFormulaTempleteForm();
					templeteForm.setDynamicParameter(mapper.readValue(templete.getDynamicParameter(),
							new HashMap<String, BigDecimal>().getClass()));
					templeteForm.setTempleteId(templete.getId());
					templeteForm.setTempleteInfo(templete.getTempleteInfo());
					templeteForm.setTempleteName(templete.getTempleteName());
					communityMeterFormulaForm.setTempleteForm(templeteForm);
				}

				res.getCommunityMeterFormulaList().add(communityMeterFormulaForm);
			} catch (Exception ex) {
				log.error("{}",ex);
			}
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Audit(operate = "新增自定义分摊公式")
	public IResponse addCommunityMeterFormula(CommunityMeterFormulaReq req) {
		AddCommunityMeterFormulaRes res = new AddCommunityMeterFormulaRes();

		CommunityMeterFormulaEntity communityMeterFormula = null;
		communityMeterFormula = communityMeterFormulaDao.getUniqueByNProperty("select distinct a "
				+ "from CommunityMeterFormulaEntity a where a.formulaName='"+req.getComment()+"'");
		if(null != communityMeterFormula) {
			res.setRet(ResponseContext.RES_DATA_DUPLICATE_CODE);
			res.setRetInfo("对不起，新增失败，此公式名已存在！");
			return res;
		}else {
			communityMeterFormula = new CommunityMeterFormulaEntity();
		}

		communityMeterFormula.setComment(req.getComment());
		communityMeterFormula.setFormulaName(req.getFormulaName());
		if (null != req.getTempleteId()) {
			CommunityFormulaTempleteEntity templete = communityFormulaTempleteDao.get(req.getTempleteId());
			if (null != templete) {
				communityMeterFormula.setTemplete(templete);
				communityMeterFormula.setDynamicParameter(templete.getDynamicParameter());
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo("选择的公式模版不存在！！！");
				return res;
			}
		}

		JSONObject json = null;
		List<OperationDiaryForm> diaryFormList = null;
		if (StringUtils.isNoneEmpty(communityMeterFormula.getOperateLog())) {
			json = new JSONObject(communityMeterFormula.getOperateLog());
			try {
				diaryFormList = mapper.readValue(json.get("diary").toString(), ArrayList.class);
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JSONException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}

		} else {
			json = new JSONObject();
			diaryFormList = new ArrayList<OperationDiaryForm>();
		}

		PlatformUserEntity account = (PlatformUserEntity)getPrincipal(true);
		String accountName = account == null ? "" : account.getUserName();
		Integer accountId =  account == null ? null : account.getId();
		OperationDiaryForm diaryForm = new OperationDiaryForm("新增", "公式名称:" + communityMeterFormula.getFormulaName()
				+ ";动态参数:" + communityMeterFormula.getDynamicParameter() + ";备注:" + communityMeterFormula.getComment()
				+ ";模板ID:"
				+ (null != communityMeterFormula.getTemplete() ? communityMeterFormula.getTemplete().getId() : "无")
				+ ";", DateUtil.formatLongFormat(new Date()), accountName, accountId, "");
		diaryFormList.add(diaryForm);
		communityMeterFormula.setOperateLog(json.put("diary", diaryFormList).toString());

		communityMeterFormulaDao.save(communityMeterFormula);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Audit(operate = "修改自定义分摊公式")
	public IResponse modifyCommunityMeterFormula(CommunityMeterFormulaReq req) {
		ModifyCommunityMeterFormulaRes res = new ModifyCommunityMeterFormulaRes();
		if (null != req.getCommunityMeterFormulaId()) {
			CommunityMeterFormulaEntity communityMeterFormula = communityMeterFormulaDao
					.get(req.getCommunityMeterFormulaId());
			if (null != communityMeterFormula) {
				CommunityMeterFormulaEntity formula = communityMeterFormulaDao.getUniqueByNProperty("select distinct a "
						+ "from CommunityMeterFormulaEntity a where a.formulaName='"+req.getComment()+"'");
				if(null != formula && !formula.getId().toString().equals(communityMeterFormula.getId().toString())) {
					res.setRet(ResponseContext.RES_DATA_DUPLICATE_CODE);
					res.setRetInfo("对不起，修改失败，此公式名已存在！");
					return res;
				}
				try {
					communityMeterFormula.setComment(req.getComment());
					communityMeterFormula.setFormulaName(req.getFormulaName());
					if (null != req.getTempleteId()) {
						//if (null == communityMeterFormula.getTemplete() || (null != communityMeterFormula.getTemplete()
						//		&& !communityMeterFormula.getTemplete().getId().equals(req.getTempleteId()))) {
							CommunityFormulaTempleteEntity templete = communityFormulaTempleteDao.get(req.getTempleteId());
							if (null != templete) {
								communityMeterFormula.setTemplete(templete);
								if(req.getDynamicParameter() != null) {
									communityMeterFormula.setDynamicParameter(req.getDynamicParameter());
								}
							} else {
								res.setRet("0001");
								res.setRetInfo("要替换的公式模版不存在！！！");
								return res;
							}
						//}	
					}
			
					
					Map<String, List<OperationDiaryForm>> json = null;
					List<OperationDiaryForm> diaryFormList = null;
					if (StringUtils.isNoneEmpty(communityMeterFormula.getOperateLog())) {
						json = mapper.readValue(
								communityMeterFormula.getOperateLog(),
								new HashMap<String, ArrayList<OperationDiaryForm>>().getClass());
						diaryFormList=json.get("diary");
					}else {
						json=new HashMap<>();
						diaryFormList =new ArrayList<>();
					}
					
					PlatformUserEntity account = (PlatformUserEntity)getPrincipal(true);
					String accountName = account == null ? "" : account.getUserName();
					Integer accountId =  account == null ? null : account.getId();
					//String accountType = account == null ? "" : account.getUserType();
					OperationDiaryForm diaryForm = new OperationDiaryForm("修改",
							"公式名称:" + communityMeterFormula.getFormulaName() + ";动态参数:"
									+ communityMeterFormula.getDynamicParameter() + ";备注:"
									+ communityMeterFormula.getComment() + ";模板ID:"
									+ (null != communityMeterFormula.getTemplete()
											? communityMeterFormula.getTemplete().getId()
											: "无")
									+ ";",
							DateUtil.formatLongFormat(new Date()), accountName, accountId, "");
					diaryFormList.add(diaryForm);
					json.put("diary", diaryFormList);
					communityMeterFormula.setOperateLog(mapper.writeValueAsString(json));
					res.setCommunityMeterFormulaId(communityMeterFormula.getId());
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} catch (Exception ex) {
					res.setRet("0001");
					res.setRetInfo("动态参数格不正确：" + ex.getMessage());
					return res;
				}

			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除自定义分摊公式")
	public IResponse deleteCommunityMeterFormula(CommunityMeterFormulaReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityMeterFormulaId()) {
			CommunityMeterFormulaEntity formula = communityMeterFormulaDao.get(req.getCommunityMeterFormulaId());
			if(null!=formula) {
				formula.setMeterList(null);
				formula.setTemplete(null);
				communityMeterFormulaDao.update(formula);
				communityMeterFormulaDao.delete(formula);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse getCommunityMeterFormulaInfo(CommunityMeterFormulaReq req) {
		GetCommunityMeterFormulaInfoRes res = new GetCommunityMeterFormulaInfoRes();
		if (null != req.getCommunityMeterFormulaId()) {
			CommunityMeterFormulaEntity communityMeterFormula = communityMeterFormulaDao
					.get(req.getCommunityMeterFormulaId());
			if (null != communityMeterFormula) {
				try {
					CommunityMeterFormulaForm communityMeterFormulaForm = new CommunityMeterFormulaForm();
					communityMeterFormulaForm.setCommunityMeterFormulaId(communityMeterFormula.getId());
					communityMeterFormulaForm.setComment(communityMeterFormula.getComment());
					communityMeterFormulaForm.setFormulaName(communityMeterFormula.getFormulaName());
					Map<String, BigDecimal> paras = mapper.readValue(communityMeterFormula.getDynamicParameter(),
							new HashMap<String, BigDecimal>().getClass());
					communityMeterFormulaForm.setDynamicParameter(paras);
					communityMeterFormulaForm.setLastDate(null != communityMeterFormula.getLastDate()
							? DateUtil.formatLongFormat(communityMeterFormula.getLastDate())
							: "");
//					communityMeterFormulaForm.setOldData(communityMeterFormula.getOldData());
//					communityMeterFormulaForm.setOldId(communityMeterFormula.getOldId());

					CommunityFormulaTempleteEntity templete = communityMeterFormula.getTemplete();
					if (null != templete) {
						CommunityFormulaTempleteForm templeteForm = new CommunityFormulaTempleteForm();
						templeteForm.setDynamicParameter(mapper.readValue(templete.getDynamicParameter(),
								new HashMap<String, BigDecimal>().getClass()));
						templeteForm.setTempleteId(templete.getId());
						templeteForm.setTempleteInfo(templete.getTempleteInfo());
						templeteForm.setTempleteName(templete.getTempleteName());
						communityMeterFormulaForm.setTempleteForm(templeteForm);
					}
					if (StringUtils.isNoneEmpty(communityMeterFormula.getOperateLog())) {
						JSONObject json = null;
						List<LinkedHashMap> diaryFormList = null;
						json = new JSONObject(communityMeterFormula.getOperateLog());
						try {
							diaryFormList = mapper.readValue(json.get("diary").toString(), ArrayList.class);
				            Collections.sort(diaryFormList, new Comparator<LinkedHashMap>() {
				                public int compare(LinkedHashMap o1, LinkedHashMap o2) {
				                	Date time1 = null;
				                	Date time2 = null;
									try {
										time1 = DateUtil.parseLongFormat((String)o1.get("time"));
										time2 = DateUtil.parseLongFormat((String)o2.get("time"));
									} catch (ParseException e) {
										e.printStackTrace();
									}
				                    if (null == time1 || null == time2) {
				                        return 0;
				                    }
				                    if (time2.before(time1)) {
				                        return -1;
				                    }
				                    if (time1 == time2)
				                        return 0;
				                    return 1;
				                }
				            });
							communityMeterFormulaForm.setOperateLog(diaryFormList);
						} catch (JsonParseException e) {
							e.printStackTrace();
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (JSONException e) {
							e.printStackTrace();
						} catch (IOException e) {
							e.printStackTrace();
						}
					}

					res.setCommunityMeterFormulaForm(communityMeterFormulaForm);
				} catch (Exception ex) {
					ex.printStackTrace();
					log.error(ex.getMessage());
				}

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	@Audit(operate = "自定义分摊公式关联表")
	public IResponse relevanceMeter(CommunityMeterFormulaReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityMeterFormulaId() && StringUtils.isNotEmpty(req.getMeterIdList())) {
			CommunityMeterFormulaEntity communityMeterFormula = communityMeterFormulaDao
					.get(req.getCommunityMeterFormulaId());
			if (null != communityMeterFormula) {
				if (StringUtils.isNotEmpty(req.getMeterIdList())) {
					String[] meterIds = req.getMeterIdList().split(",");
					List<CommunityMeterEntity> meterList = new ArrayList<CommunityMeterEntity>();
					for (String meterId : meterIds) {
						CommunityMeterEntity meter = communityMeterDao.get(Integer.parseInt(meterId));
						if (null != meter) {
							meter.setMeterFormula(communityMeterFormula);
							meterList.add(meter);
						}
					}
					communityMeterFormula.setMeterList(meterList);
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	
	@Override
	@Audit(operate = "自定义分摊公式取消关联表")
	public IResponse cancelRelevanceMeter(CommunityMeterFormulaReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityMeterFormulaId() && StringUtils.isNotEmpty(req.getMeterIdList())) {
			CommunityMeterFormulaEntity communityMeterFormula = communityMeterFormulaDao
					.get(req.getCommunityMeterFormulaId());
			if (null != communityMeterFormula) {
				if (StringUtils.isNotEmpty(req.getMeterIdList())) {
					String[] meterIds = req.getMeterIdList().split(",");
					//List<CommunityMeterEntity> meterList = new ArrayList<CommunityMeterEntity>();
					for (String meterId : meterIds) {
						CommunityMeterEntity meter = communityMeterDao.get(Integer.parseInt(meterId));
						if (null != meter && meter.getMeterFormula().getId().equals(communityMeterFormula.getId()) && communityMeterFormula.getMeterList().contains(meter)) {
							communityMeterFormula.getMeterList().remove(meter);
						}
					}
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
}