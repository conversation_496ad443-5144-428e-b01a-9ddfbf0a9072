package com.foshan.service.community.impl;

import static java.util.Comparator.comparingInt;
import static java.util.stream.Collectors.groupingBy;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;
import java.io.FileOutputStream;

import javax.activation.MimetypesFileTypeMap;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;

import com.fasterxml.jackson.core.type.TypeReference;
import com.foshan.form.request.MessageReq;
import com.foshan.service.IMessageService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.DictionaryDataEntity;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.community.CommunityBuildingEntity;
import com.foshan.entity.community.CommunityCecorationItemsAttachmentEntity;
import com.foshan.entity.community.CommunityDecorationAttachmentEntity;
import com.foshan.entity.community.CommunityDecorationItemsEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityEventCategoryEntity;
import com.foshan.entity.community.CommunityEventCategoryItemsEntity;
import com.foshan.entity.community.CommunityEventsEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityMeterAllocationEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityReceivablesChangesEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.community.CommunityUserEntity;
import com.foshan.entity.community.vo.MeterRecordVo;
import com.foshan.entity.community.vo.RefundInfoVo;
import com.foshan.entity.community.vo.ReservedFieldVo;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.AssetForm;
import com.foshan.form.community.CommunityEventCategoryForm;
import com.foshan.form.community.CommunityEventCategoryItemsForm;
import com.foshan.form.community.CommunityEventsForm;
import com.foshan.form.community.CommunityMemberForm;
import com.foshan.form.community.CommunityReceivablesChangesForm;
import com.foshan.form.community.CommunityReceivablesForm;
import com.foshan.form.community.OperationDiaryForm;
import com.foshan.form.community.request.CommunityEventsReq;
import com.foshan.form.community.request.CommunityReceivablesReq;
import com.foshan.form.community.request.ExportExcelReq;
import com.foshan.form.community.request.ImportExcelReq;
import com.foshan.form.community.response.communityEvent.AddCommunityEventsRes;
import com.foshan.form.community.response.communityEvent.GetCommunityEventsListRes;
import com.foshan.form.community.response.communityEvent.GetCommunityEventsInfo;
import com.foshan.form.community.response.communityEvent.ModifyCommunityEventsRes;
import com.foshan.form.community.response.communityReceivables.AddCommunityReceivablesRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.IWeiXinApiService;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityEventsService;
import com.foshan.util.CodeUtil;
import com.foshan.util.CommonUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.JsonUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.WeiXinApiUtil;
import com.foshan.util.community.CommunityCache;
import com.hazelcast.spring.cache.HazelcastCacheManager;


@Transactional
@Service("communityEventsService")
public  class CommunityEventsServiceImpl extends GenericCommunityService implements ICommunityEventsService {
	@Resource
	private HazelcastCacheManager cacheManager;
    @Autowired
	private IMessageService messageService;

	private static Map<String,String> parameterMap=null;
	private final static Logger logger = LoggerFactory.getLogger(CommunityReceivablesServiceImpl.class);
	public static final String RECEIVABLESNO_SN_TYPE = "RECEIVABLESNO";

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getCommunityEventsList(CommunityEventsReq req) {
		GetCommunityEventsListRes res = new GetCommunityEventsListRes();
		Page<CommunityEventsEntity> page = new Page<CommunityEventsEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		
		String hql = queryCondition(req);
		page = communityEventsDao.queryPage(page, hql);
		List<CommunityEventsEntity> list = communityEventsDao.getListByHql(hql, "");
		Map<Integer, List<CommunityEventsEntity>> collect1 = (Map<Integer, List<CommunityEventsEntity>>) list
			    .parallelStream().collect(groupingBy(CommunityEventsEntity::getEventState));
		res.setPendingCount(collect1.containsKey(1) ? collect1.get(1).size() : 0);
		res.setCompleteCount(collect1.containsKey(5) ? collect1.get(5).size() : 0);

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		ObjectMapper mapper = new ObjectMapper();
		page.getResultList().forEach(o -> {
			
			List<Object> itemsdetailList = new ArrayList<Object>();
			Map<String,Object> diaryMap = new HashMap<String,Object>();
			try {
				if(StringUtils.isNoneEmpty(o.getItemsdetail())) {
					itemsdetailList =mapper.readValue(o.getItemsdetail(), ArrayList.class);
				}
				if(StringUtils.isNoneEmpty(o.getDiary())) {
					diaryMap =mapper.readValue(o.getDiary(), Map.class);
				}
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JSONException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			CommunityEventsForm communityEventForm = new CommunityEventsForm(o.getId(),sdf.format(o.getCreateTime()),
					sdf.format(o.getLastModifyTime()),o.getAuditOpinion(),
					o.getCentent(),o.getTitle(),o.getEventState(),o.getState(),diaryMap) ;
			communityEventForm.setEventType(o.getEventType());
			communityEventForm.setOrderCode(StringUtils.isNotEmpty(o.getOrderCode()) ? o.getOrderCode():"");
			if(null != o.getEventCategory()) {
				CommunityEventCategoryForm eventCategoryForm = new CommunityEventCategoryForm(o.getEventCategory().getId(),
						o.getEventCategory().getCategoryName(),o.getEventCategory().getCategoryLevel(),o.getEventCategory().getIsDispatching(),
						sdf.format(o.getEventCategory().getCreateTime()),sdf.format(o.getEventCategory().getLastModifyTime()),o.getEventCategory().getIcon());
				communityEventForm.setCommunityEventCategoryForm(eventCategoryForm);
			}
			CommunityMemberForm memberForm = new CommunityMemberForm();
			memberForm.setMemberId(o.getMember().getId());
			memberForm.setEmail(o.getMember().getEmail());
			memberForm.setHomePhone(o.getMember().getHomePhone());
			memberForm.setNickName(o.getMember().getNickName());
			memberForm.setOfficePhone(o.getMember().getOfficePhone());
			memberForm.setPhone(o.getMember().getPhone());
			memberForm.setRegistName(o.getMember().getRegistName());
			memberForm.setSex(o.getMember().getSex());
			memberForm.setSmartcardId(o.getMember().getSmartcardId());
			memberForm.setHeadImage(StringUtils.isNotEmpty(o.getMember().getHeadImage()) ? o.getMember().getHeadImage() : "");
			memberForm.setUserName(StringUtils.isNotEmpty(o.getMember().getUserName()) ? o.getMember().getUserName() : "");
			communityEventForm.setMember(memberForm);
			communityEventForm.setProperty(getCommunityPropertyForm(o.getProperty(),null,null));
			communityEventForm.setSendstatus(o.getSendstatus());
			communityEventForm.setParameterReq(o.getParameterReq());
		
			communityEventForm.setItemsdetailList(itemsdetailList);
			communityEventForm.setQrCodeForm(getAsset(o.getQrCode()));
			communityEventForm.setConstructionPermitForm(getAsset(o.getConstructionPermit()));
		    communityEventForm.setCheckAcceptItems(o.getCheckAcceptItems());
		    communityEventForm.setAcceptanceTime(null != o.getAcceptanceTime()? DateUtil.formatShortFormat(o.getAcceptanceTime()) : "");
		    communityEventForm.setRefundMethod(o.getRefundMethod());
			if (StringUtils.isNotEmpty(o.getRefundInfo())) {
				try {
					RefundInfoVo refundInfoVo = mapper.readValue(o.getRefundInfo(), RefundInfoVo.class);
					communityEventForm.setAccountName(refundInfoVo.getAccountName());
					communityEventForm.setBankAccount(refundInfoVo.getBankAccount());
					communityEventForm.setBankName(refundInfoVo.getBankName());
					if(StringUtils.isNotEmpty(refundInfoVo.getReceiptImageIds())) {
						getAssetFormList(refundInfoVo.getReceiptImageIds(),communityEventForm.getReceiptImageList());
					}
					if(StringUtils.isNotEmpty(refundInfoVo.getIdCardImageIds())) {
						getAssetFormList(refundInfoVo.getIdCardImageIds(),communityEventForm.getIdCardImageList());
					}
					if(StringUtils.isNotEmpty(refundInfoVo.getBankImageIds())) {
						getAssetFormList(refundInfoVo.getBankImageIds(),communityEventForm.getBankImageList());
					}
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
			o.getReceivablesList().forEach(r->{
				communityEventForm.getReceivablesList().add(getReceivablesForm(r));
			});
			res.getEventFormList().add(communityEventForm);

		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public void getAssetFormList(String imageIds,List<AssetForm> list) {
		String[] ids = imageIds.split(",");
		for (String id : ids) {
			AssetForm asset = getAsset(Integer.parseInt(id));
			if (null != asset) {
				list.add(asset);
			}
		}
	}
	
	public String queryCondition(CommunityEventsReq req){
		Object userObj = getPrincipal(true);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityEventsEntity a " 
				+ "inner join a.property b inner join b.building d inner join d.district c inner join a.eventCategory e ");
	
		hql.append(" where").append(null != req.getState() ? "  a.state=" + req.getState()  :
			"  a.state="+EntityContext.RECORD_STATE_VALID)
			.append(StringUtils.isNotEmpty(req.getEventCategoryIdList()) ?" and a.eventCategory.id in ("+req.getEventCategoryIdList()+")":"")
			.append(null!=req.getPropertyId()?" and a.property.id="+req.getPropertyId():"")
			.append(null!=req.getEventState() ? " and a.eventState="+req.getEventState(): 
				(StringUtils.isNotEmpty(req.getEventStateList()) ?" and a.eventState in ("+req.getEventStateList()+")":""))
			.append(null!=req.getDistrictId() ? " and c.id="+req.getDistrictId():"")
			.append(StringUtils.isNotEmpty(req.getCreateTime()) ?
				" and a.createTime like '%" + req.getCreateTime() + "%'" :"")
			.append((StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())) ?
					" and a.createTime >='" + req.getStartTime() + "' and a.createTime<='"+req.getEndTime()+"'" :"")
			.append(StringUtils.isNotEmpty(req.getTitle()) ?
				" and a.title like '%" + req.getTitle() + "%'" :"")
			.append(null != req.getEventCategoryState() ? " and  e.state=" + req.getEventCategoryState()  :
					"")
			.append(null!=req.getRefundMethod() ? " and a.refundMethod"+req.getRefundMethod():"")
			.append(StringUtils.isNotEmpty(req.getOrderCode()) ?
				" and a.orderCode like '%" + req.getOrderCode() + "%'" :"")
			
			.append(StringUtils.isNotEmpty(req.getInformationAuditor()) ?
				" and a.diary like '%" + req.getInformationAuditor() + "%'" :"")
			.append(StringUtils.isNotEmpty(req.getInformationAuditionTime()) ?
					" and a.diary like '%" + req.getInformationAuditionTime() + "%'" :"")
			.append(null!=req.getHaveCashPledge()? " and a.haveCashPledge="+req.getHaveCashPledge():"")
			.append(null!=req.getEventType() ? " and a.eventType="+req.getEventType() :" and (a.eventType=0 or a.eventType is null)");
		if(null!=userObj && userObj instanceof CommunityMemberEntity) {
			CommunityMemberEntity member = (CommunityMemberEntity)userObj;
			hql.append(" and a.member.id="+member.getId());
		}
		if(null!=req.getDepartment()) {
			switch(req.getDepartment()) {
			case 0:
				hql.append(" and SUBSTRING(a.checkAcceptAuditState, 1, 1)='1' and a.eventState!=11");break;
			case 1:
				hql.append(" and SUBSTRING(a.checkAcceptAuditState, 2, 1)='1' and a.eventState!=11");break;
			case 2:
				hql.append(" and SUBSTRING(a.checkAcceptAuditState, 3, 1)='1' and a.eventState!=11");break;
			case 3:
				hql.append(" and SUBSTRING(a.checkAcceptAuditState, 4, 1)='1' and a.eventState!=11");break;
			default:
				break;	
			}
		}
		hql.append(" ORDER BY a.createTime DESC");
		
		return hql.toString();
	}
	
	public CommunityReceivablesForm getReceivablesForm(CommunityReceivablesEntity communityReceivables) {
		CommunityReceivablesForm communityReceivablesForm = new CommunityReceivablesForm();
		communityReceivablesForm.setCommunityReceivablesId(communityReceivables.getId());
		communityReceivablesForm.setChargeCategory(communityReceivables.getChargeCategory());
		communityReceivablesForm.setChargeSource(communityReceivables.getChargeSource());
		communityReceivablesForm.setComment(communityReceivables.getComment());
		communityReceivablesForm.setReceivablesNO(communityReceivables.getReceivablesNO());
		communityReceivablesForm.setEndTime(null != communityReceivables.getEndTime()
				? DateUtil.formatLongFormat(communityReceivables.getEndTime())
				: "");
		communityReceivablesForm.setPayItemsName(communityReceivables.getPayItemsName());
		communityReceivablesForm.setPaymentPeriod(null != communityReceivables.getPaymentPeriod()
				? DateUtil.formatLongFormat(communityReceivables.getPaymentPeriod())
				: "");
		communityReceivablesForm.setQuantity(communityReceivables.getQuantity());
		communityReceivablesForm.setReceivableAmount(null != communityReceivables.getReceivableAmount()
				? communityReceivables.getReceivableAmount().toString()
				: "");
		communityReceivablesForm.setReceivableDate(null != communityReceivables.getReceivableDate()
				? DateUtil.formatLongFormat(communityReceivables.getReceivableDate())
				: "");
		communityReceivablesForm.setReceivedAmount(null != communityReceivables.getReceivedAmount()
				? communityReceivables.getReceivedAmount().toString()
				: "");
		communityReceivablesForm.setLockMark(communityReceivables.getLockMark());
		;
		communityReceivablesForm
				.setNotReceivedAmount(CommonUtil.subtractAmount(communityReceivables.getReceivableAmount(),
						communityReceivables.getReceivedAmount()).toString());
		communityReceivablesForm.setSourceNotes(communityReceivables.getSourceNotes());
		communityReceivablesForm.setStartTime(null != communityReceivables.getStartTime()
				? DateUtil.formatLongFormat(communityReceivables.getStartTime())
				: "");
		for (CommunityReceivablesChangesEntity receivablesChanges : communityReceivables
				.getReceivablesChangesList()) {
			CommunityReceivablesChangesForm form = new CommunityReceivablesChangesForm();
			form.setCommunityReceivablesChangesId(receivablesChanges.getId());
			form.setChangeAmount(receivablesChanges.getChangeAmount().toString());
			form.setChangeDate(DateUtil.format(receivablesChanges.getChangeDate(), 1));
			form.setComment(receivablesChanges.getComment());
			communityReceivablesForm.getReceivablesChangesList().add(form);
		}
		return communityReceivablesForm;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Audit(operate = "新增事件")
	public IResponse addCommunityEvents(CommunityEventsReq req) {
		AddCommunityEventsRes res = new AddCommunityEventsRes();
		Object userObj = getPrincipal(true);
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		if(userObj instanceof CommunityMemberEntity ) {
			if (null!=req.getEventState() && null!=req.getEventCategoryId() ) {
				if(req.getEventState() != EntityContext.EVENT_STATE__AUDIT_INITIAL && 
						req.getEventState() != EntityContext.EVENT_STATE__AUDIT_WAITING ) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				CommunityEventCategoryEntity eventCategory = 
						communityEventCategoryDao.get(req.getEventCategoryId());
				if(null == eventCategory) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				ObjectMapper mapper = new ObjectMapper();
				CommunityEventsEntity event = new CommunityEventsEntity();
				try {
					event.setItemsdetail(null!=req.getItemsdetail() ? mapper.writeValueAsString(req.getItemsdetail()).replaceAll("null", "\"\""): "");
				} catch (JsonProcessingException e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
				}
				event.setCentent(StringUtils.isNotEmpty(req.getCentent()) ? req.getCentent() : "");
				event.setEventType(eventCategory.getCategoryType());
				event.setEventCategory(eventCategory);
				event.setTitle(req.getTitle());
				event.setEventState(req.getEventState());
				event.setProperty(null!= req.getPropertyId() ? communityPropertyDao.get(req.getPropertyId()) : null);
				if(null!=req.getEventType()&&1==req.getEventType()) {
					event.setOrderCode(CodeUtil.getSNCode("zx"));
				}
				
				CommunityMemberEntity member = (CommunityMemberEntity)userObj;
				if(req.getEventState()==1) {
					JSONObject json = null; 
					List<OperationDiaryForm> diaryFormList = null;
					if(StringUtils.isNoneEmpty(event.getDiary())) {
						json = new JSONObject(event.getDiary());
						try {
							diaryFormList =mapper.readValue(json.get("diary").toString(), ArrayList.class);
						} catch (JsonParseException e) {
							e.printStackTrace();
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (JSONException e) {
							e.printStackTrace();
						} catch (IOException e) {
							e.printStackTrace();
						}
						
					}else {
						json = new JSONObject();
						diaryFormList = new ArrayList<OperationDiaryForm>();
					}
					String str = (null==event.getEventType() ||
							(null!=event.getEventType()&&0==event.getEventType())) ? "受理":"审核";
					OperationDiaryForm diaryForm = new OperationDiaryForm("提交"+str,
							"业主提交"+str+"！",DateUtil.formatLongFormat(new Date()),member.getUserName(),member.getId(),"M");
					diaryFormList.add(diaryForm);
					event.setDiary(json.put("diary", diaryFormList).toString());
				}
				event.setMember(member);
				event.setLastModifyTime(new Timestamp(new Date().getTime()));
				event.setState(EntityContext.RECORD_STATE_VALID);
				try {
					event.setStartTime(StringUtils.isNotEmpty(req.getStartTime()) ? 
							new Timestamp(DateUtil.parseLongFormat(req.getStartTime()+" 00:00:00").getTime()) : null);
					event.setEndTime(StringUtils.isNotEmpty(req.getEndTime()) ? 
							new Timestamp(DateUtil.parseLongFormat(req.getEndTime()+" 23:59:59").getTime()) : null);
				} catch (ParseException e) {
					e.printStackTrace();
				}
				if(null!=eventCategory.getIsSend()&&eventCategory.getIsSend()==1) {
					String info = createExamineAndApprove(event,req);
					if(StringUtils.isNoneEmpty(info)) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(info);
						return res;
					}
				}
				if(StringUtils.isNotEmpty(req.getDecorationItemsId())) {
					String[] decorationItemsIds = req.getDecorationItemsId().split(",");
					Map<String,Object> parameterMap = new HashMap<String,Object>();
					Map<String,String> cententMap = new HashMap<String,String>();
					Map<String,String> map = new HashMap<>();
					List<Map<String,String>> list = new ArrayList<>();
					for (String id : decorationItemsIds) {
						CommunityDecorationItemsEntity communityDecorationItems = communityDecorationItemsDao.get(Integer.parseInt(id));
						Map<String,Object> defaultParameterMap = new HashMap<String,Object>();
						if(null!=communityDecorationItems) {
							try {
								if(StringUtils.isNoneEmpty(communityDecorationItems.getParameter())) {
									defaultParameterMap =mapper.readValue(communityDecorationItems.getParameter(), Map.class);
									if(defaultParameterMap.containsKey("payItemsList")) {
										List<Map<String,String>> payItemsList = (List<Map<String, String>>) defaultParameterMap.get("payItemsList");
										for(Map<String,String> payItemsListMap : payItemsList) {
											if(!map.containsKey(payItemsListMap.get("payItemsId"))) {
												map.put(payItemsListMap.get("payItemsId"), payItemsListMap.get("payItemsId"));
												list.add(payItemsListMap);
											}
										}
									}
								}
							} catch (JsonParseException e) {
								e.printStackTrace();
							} catch (JsonMappingException e) {
								e.printStackTrace();
							} catch (JSONException e) {
								e.printStackTrace();
							} catch (IOException e) {
								e.printStackTrace();
							}
							if(cententMap.containsKey("itemIds")) {
								String itemIds = cententMap.get("itemIds")+","+communityDecorationItems.getId();
								String itemName = cententMap.get("itemName")+","+communityDecorationItems.getItemName();
								cententMap.put("itemIds",itemIds);
								cententMap.put("itemName",itemName);
							}else {
								cententMap.put("itemIds",communityDecorationItems.getId().toString());
								cententMap.put("itemName",communityDecorationItems.getItemName());
							}
						}

					}
					parameterMap.put("payItemsList", list);
					try {
						event.setParameterReq(mapper.writeValueAsString(parameterMap));
						event.setCentent(mapper.writeValueAsString(cententMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}    
				}
				communityEventsDao.save(event);
				res.setEventId(event.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		}else {
			res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
			return res;
		}
		return res;
	}
	
	@SuppressWarnings("unchecked")
	public String createExamineAndApprove(CommunityEventsEntity event,CommunityEventsReq req) {
		String info="";
		ObjectMapper mapper = new ObjectMapper();
		if(StringUtils.isNoneEmpty(event.getEventCategory().getDefaultParameter())) {
			try {
				Map<String,Object> reqMap = mapper.readValue(event.getEventCategory().getDefaultParameter(), Map.class);
				if(StringUtils.isNoneEmpty(req.getWidgetValue())) {
					Map<String,Object> widgetValueMap = mapper.readValue(req.getWidgetValue(), Map.class);
					String resGroupSecretToke = "";
					boolean resGroupSecretStatus = true;
					for(String key : widgetValueMap.keySet()) {
						if(key.startsWith("Im_")) {
							List<String> fileIdList = new ArrayList<>();
							if(resGroupSecretStatus) {
								resGroupSecretToke=getToken("resGroupSecret");
								resGroupSecretStatus = false;
							}
							String srt = widgetValueMap.get(key).toString();
							if (StringUtils.isNotEmpty(srt)) {
								String[] imgIds = srt.split(",");
								for (String imgId : imgIds) {
									AssetEntity asset = assetDao.get(Integer.parseInt(imgId));
									Map<String, String> fileMap = new HashMap<>();
							        fileMap.put("file", contextInfo.getAssetFilePath() + File.separator +asset.getImageFile());
							        Map<String, String> textMap = new HashMap<String, String>();
							        textMap.put("bizkey", "bizkey");
							        String responseStr = formUpload(parameterMap.get("uploadfileUrl").toString(),textMap
							         		,fileMap,"",resGroupSecretToke);
							        JSONObject responseJson = new JSONObject(responseStr);
									if(responseJson.has("success") && JsonUtil.getJSONNodeByPath(responseJson,"success")
												.toLowerCase().equals("true")){
										JSONObject jsonobj = new JSONObject(responseStr);
									    JSONArray list =  (JSONArray) jsonobj.get("data");
								        list.forEach(o->{
								        	JSONObject j = (JSONObject) o; 
								        	fileIdList.add(j.getString("fileId"));
								        });
									}else {
										info="数据分派给云之家出现异常！"+(StringUtils.isNotEmpty(responseStr) ? JsonUtil.getJSONNodeByPath(responseJson,"error") :"");
										return info;
									}
								}
							}
							widgetValueMap.put(key, fileIdList);
						}
					}
					reqMap.put("widgetValue", widgetValueMap);
					String token = getToken("team");
					
			        try {
						String responseStr = HttpClientUtil.jsonPost(parameterMap.get("createInstUrl").toString().endsWith("?accessToken=")?
								parameterMap.get("createInstUrl").toString()+token:
									parameterMap.get("createInstUrl").toString()+"?accessToken="+token,"utf-8",mapper.writeValueAsString(reqMap),null);
						JSONObject responseJson = new JSONObject(responseStr);
						if(responseJson.has("success") && JsonUtil.getJSONNodeByPath(responseJson,"success")
								.toLowerCase().equals("true")){
							event.setSendstatus(1);
						}else {
							event.setSendstatus(2);
							info="数据分派给云之家出现异常！"+(StringUtils.isNotEmpty(responseStr) ? JsonUtil.getJSONNodeByPath(responseJson,"error") :"");
							return info;
						}
						event.setParameterReq(mapper.writeValueAsString(reqMap));
						event.setParameterRes(responseStr);
					}  catch (Exception e) {
						e.printStackTrace();
					}
				}
			} catch (JsonMappingException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				info="数据分派给云之家出现异常！";
			} catch (JsonProcessingException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				info="数据分派给云之家出现异常！";
			}
		}
		return info;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Audit(operate = "修改事件")
	public IResponse modifyCommunityEvents(CommunityEventsReq req) {
		ModifyCommunityEventsRes res = new ModifyCommunityEventsRes();
		Object userObj = getPrincipal(true);
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		if(userObj instanceof CommunityMemberEntity ) {
			if (null!=req.getEventsId()  
					&& null!=req.getEventState() && null!=req.getEventCategoryId() ) {
				if(req.getEventState() != EntityContext.EVENT_STATE__AUDIT_INITIAL  &&
						req.getEventState() != EntityContext.EVENT_STATE__AUDIT_WAITING ) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				CommunityEventsEntity event = communityEventsDao.get(req.getEventsId());
				CommunityEventCategoryEntity eventCategory = 
						communityEventCategoryDao.get(req.getEventCategoryId());
				if(null== event || null == eventCategory || (0!=event.getEventState()
						&&event.getEventState() != EntityContext.EVENT_STATE_AUDIT_NO_PASS && event.getEventState() !=15)
						) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				
				event.setCentent(StringUtils.isNotEmpty(req.getCentent()) ? req.getCentent() : event.getCentent());
				ObjectMapper mapper = new ObjectMapper();
				try {
					event.setItemsdetail(null!=req.getItemsdetail() ? mapper.writeValueAsString(req.getItemsdetail()).replaceAll("null", "\"\""): "");
				} catch (JsonProcessingException e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
				}
				event.setEventCategory(eventCategory);
				event.setTitle(req.getTitle());
				event.setEventState(1==req.getEventState()||0==req.getEventState() ?
						req.getEventState():event.getEventState());
				event.setLastModifyTime(new Timestamp(new Date().getTime()));
				event.setProperty(null!= req.getPropertyId() ? communityPropertyDao.get(req.getPropertyId()) : null);
				if(req.getEventState()==1) {
					JSONObject json = null; 
					List<OperationDiaryForm> diaryFormList = null;
					if(StringUtils.isNoneEmpty(event.getDiary())) {
						json = new JSONObject(event.getDiary());
						try {
							diaryFormList =mapper.readValue(json.get("diary").toString(), ArrayList.class);
						} catch (JsonParseException e) {
							e.printStackTrace();
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (JSONException e) {
							e.printStackTrace();
						} catch (IOException e) {
							e.printStackTrace();
						}
						
					}else {
						json = new JSONObject();
						diaryFormList = new ArrayList<OperationDiaryForm>();
					}
					CommunityMemberEntity member = (CommunityMemberEntity)userObj;
					String str = (null==event.getEventType() ||
							(null!=event.getEventType()&&0==event.getEventType())) ? "受理":"审核";
					OperationDiaryForm diaryForm = new OperationDiaryForm("提交"+str,
							"业主提交"+str+"！",DateUtil.formatLongFormat(new Date()),member.getUserName(),member.getId(),"M");
					diaryFormList.add(diaryForm);
					event.setDiary(json.put("diary", diaryFormList).toString());
				}
				
				event.setState(EntityContext.RECORD_STATE_VALID);
				//communityEventsDao.save(event);

				res.setEventId(event.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		}else {
			res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
			return res;
		}

		return res;
	}

	@Override
	@Audit(operate = "删除事件")
	public IResponse deleteCommunityEvents(CommunityEventsReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getEventsId()) {
			CommunityEventsEntity event = communityEventsDao.get(req.getEventsId());
			if (null != event) {
				if(event.getEventState()!=0 || event.getEventState()!=6) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO+"此类数据不能删除！");
					return res;
				}
				event.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getCommunityEventsInfo(CommunityEventsReq req) {
		GetCommunityEventsInfo res = new GetCommunityEventsInfo();
		if (null != req.getEventsId()) {
			CommunityEventsEntity event = communityEventsDao.get(req.getEventsId());
			if (null != event) {
				DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
				List<Object> itemsdetailList = new ArrayList<Object>();
				ObjectMapper mapper = new ObjectMapper();
				Map<String,Object> diaryMap = new HashMap<String,Object>();
				try {
					if(StringUtils.isNoneEmpty(event.getItemsdetail())) {
						itemsdetailList =mapper.readValue(event.getItemsdetail(), ArrayList.class);
					}
					if(StringUtils.isNoneEmpty(event.getDiary())) {
						diaryMap =mapper.readValue(event.getDiary(), Map.class);
					}
				} catch (JsonParseException e) {
					e.printStackTrace();
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JSONException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
				CommunityEventsForm communityEventsForm = new CommunityEventsForm(event.getId(),sdf.format(event.getCreateTime()),
						sdf.format(event.getLastModifyTime()),event.getAuditOpinion(),
						event.getCentent(),event.getTitle(),event.getEventState(),event.getState(),diaryMap) ;
				communityEventsForm.setOrderCode(StringUtils.isNotEmpty(event.getOrderCode()) ? event.getOrderCode():"");
				communityEventsForm.setEventType(event.getEventType());
				communityEventsForm.getImageList().add(getAsset(event.getImage(),2));
				if(null != event.getEventCategory()) {
					CommunityEventCategoryForm eventCategoryForm = new CommunityEventCategoryForm(event.getEventCategory().getId(),
							event.getEventCategory().getCategoryName(),event.getEventCategory().getCategoryLevel(),event.getEventCategory().getIsDispatching(),
							sdf.format(event.getEventCategory().getCreateTime()),sdf.format(event.getEventCategory().getLastModifyTime()),event.getEventCategory().getIcon());
					event.getEventCategory().getEventCategoryItemsList().forEach(p->{
						List<Object> optionslList = new ArrayList<Object>();
						try {
							if(StringUtils.isNoneEmpty(p.getOptions())) {
								optionslList =mapper.readValue(p.getOptions(), ArrayList.class);
							}
						} catch (JsonParseException e) {
							e.printStackTrace();
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (JSONException e) {
							e.printStackTrace();
						} catch (IOException e) {
							e.printStackTrace();
						}
						CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(p.getId(),
								p.getItemName(),p.getItemkey(),p.getOrders(),p.getDataType(),optionslList,p.getIsRequiredng()) ;
						eventCategoryItemsForm.setIsCascade(p.getIsCascade());
						eventCategoryItemsForm.setCascadeValue(StringUtils.isNotEmpty(p.getCascadeValue()) ? p.getCascadeValue():"");
						eventCategoryForm.getEventCategoryItemList().add(eventCategoryItemsForm);
					});
					CommunityMemberForm memberForm = new CommunityMemberForm();
					memberForm.setMemberId(event.getMember().getId());
					memberForm.setEmail(event.getMember().getEmail());
					memberForm.setHomePhone(event.getMember().getHomePhone());
					memberForm.setNickName(event.getMember().getNickName());
					memberForm.setOfficePhone(event.getMember().getOfficePhone());
					memberForm.setPhone(event.getMember().getPhone());
					memberForm.setRegistName(event.getMember().getRegistName());
					memberForm.setSex(event.getMember().getSex());
					memberForm.setSmartcardId(event.getMember().getSmartcardId());
					memberForm.setHeadImage(StringUtils.isNotEmpty(event.getMember().getHeadImage()) ? event.getMember().getHeadImage() : "");
					memberForm.setUserName(StringUtils.isNotEmpty(event.getMember().getUserName()) ? event.getMember().getUserName() : "");
					communityEventsForm.setMember(memberForm);
					communityEventsForm.setCommunityEventCategoryForm(eventCategoryForm);
					communityEventsForm.setProperty(getCommunityPropertyForm(event.getProperty(),null,null));
				}
				List<OperationDiaryForm> diaryFormList = null;
				JSONObject json = null; 
				if(StringUtils.isNoneEmpty(event.getDiary())) {
					json = new JSONObject(event.getDiary());
					try {
						diaryFormList =mapper.readValue(json.get("diary").toString(), new TypeReference<List<OperationDiaryForm>>(){});
					} catch (JSONException | IOException e) {
						logger.info("操作日志json转对象失败！{}", e);
					}
                    if (diaryFormList != null) {
                        for(OperationDiaryForm operationDiary : diaryFormList) {
                            if(operationDiary.getTitle().equals("物业审核装修申请资料")) {
                                communityEventsForm.setInformationAuditionTime(operationDiary.getTime());
                                communityEventsForm.setInformationAuditor(operationDiary.getAuthor());

                            }
                        }
                    }
                }
				communityEventsForm.setSendstatus(event.getSendstatus());
				communityEventsForm.setParameterReq(event.getParameterReq());
				communityEventsForm.setItemsdetailList(itemsdetailList);
				communityEventsForm.setQrCodeForm(getAsset(event.getQrCode()));
				communityEventsForm.setConstructionPermitForm(getAsset(event.getConstructionPermit()));
				event.getReceivablesList().forEach(r->{
					communityEventsForm.getReceivablesList().add(getReceivablesForm(r));
				});
				communityEventsForm.setCheckAcceptItems(event.getCheckAcceptItems());
				communityEventsForm.setAcceptanceTime(null != event.getAcceptanceTime()? 
						DateUtil.formatShortFormat(event.getAcceptanceTime()) : "");
				if (StringUtils.isNotEmpty(event.getRefundInfo())) {
					try {
						RefundInfoVo refundInfoVo = mapper.readValue(event.getRefundInfo(), RefundInfoVo.class);
						communityEventsForm.setAccountName(refundInfoVo.getAccountName());
						communityEventsForm.setBankAccount(refundInfoVo.getBankAccount());
						communityEventsForm.setBankName(refundInfoVo.getBankName());
						if(StringUtils.isNotEmpty(refundInfoVo.getReceiptImageIds())) {
							getAssetFormList(refundInfoVo.getReceiptImageIds(),communityEventsForm.getReceiptImageList());
						}
						if(StringUtils.isNotEmpty(refundInfoVo.getIdCardImageIds())) {
							getAssetFormList(refundInfoVo.getIdCardImageIds(),communityEventsForm.getIdCardImageList());
						}
						if(StringUtils.isNotEmpty(refundInfoVo.getBankImageIds())) {
							getAssetFormList(refundInfoVo.getBankImageIds(),communityEventsForm.getBankImageList());
						}
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
				}
				res.setCommunityEventsForm(communityEventsForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	@Audit(operate = "设置事件状态")
	public IResponse setEventstate(CommunityEventsReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
		ObjectMapper mapper = new ObjectMapper();
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		if (null != req.getEventsId()) {
			CommunityEventsEntity event = communityEventsDao.get(req.getEventsId());
			if (null != event && null!=req.getEventState()) {
				JSONObject json = null; 
				List<OperationDiaryForm> diaryFormList = null;
				if(StringUtils.isNoneEmpty(event.getDiary())) {
					json = new JSONObject(event.getDiary());
					try {
						diaryFormList =mapper.readValue(json.get("diary").toString(), ArrayList.class);
					} catch (JSONException | IOException e) {
						logger.info("json转对象出现错误：{}",e);
					}

                }else {
					json = new JSONObject();
					diaryFormList = new ArrayList<OperationDiaryForm>();
				}
				String str = (null==event.getEventType() ||
						(null!=event.getEventType()&&0==event.getEventType())) ? "受理":"审核";
				if(event.getEventState()==0 || event.getEventState()==3 || event.getEventState()==15 ) {//在初始化状态或审核不通过时
					if(req.getEventState() != 1) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
						return res;
					}
					if(!(userObj instanceof CommunityMemberEntity)) {
						res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
						res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
						return res;
					}
					CommunityMemberEntity member = (CommunityMemberEntity)userObj;
					OperationDiaryForm diaryForm = new OperationDiaryForm("提交"+str,
							"业主提交"+str+"！",DateUtil.formatLongFormat(new Date()),member.getUserName(),member.getId(),"M");
					diaryFormList.add(diaryForm);
					event.setLastModifyTime(new Timestamp(new Date().getTime()));

					try {
						event.setItemsdetail(null!=req.getItemsdetail() ? 
								mapper.writeValueAsString(req.getItemsdetail()).replaceAll("null", "\"\""): "");
					} catch (JsonProcessingException e1) {
						e1.printStackTrace();
					}
					event.setEventState(req.getEventState());
				}else if(event.getEventState()==1){//在初审核状态
					String auditOpinion = StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "无;";
					if(req.getEventState() == 14 && userObj instanceof PlatformUserEntity ) {//审核通过，如果其它除装修其它流程审批也需要二级审核，需要把状态2去掉: if(req.getEventState() == 14 && userObj instanceof PlatformUserEntity )
						PlatformUserEntity user = (PlatformUserEntity) userObj;
						OperationDiaryForm diaryForm = null;
						if (null != event.getEventCategory() && event.getEventCategory().getIsDispatching() == 0) {
							if (null != event.getEventCategory().getCategoryType() && event.getEventCategory().getCategoryType() == 1) {
								event.setEventState(14);
								diaryForm = new OperationDiaryForm("装修申请资料初审",
										"初审通过！"+str+"意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
								//以下两行为发送前端例子,发送websocket消息并持久化
								messageService.sendMessage(new MessageReq(0,3458,1,"初审通过","https://zhuanlan.zhihu.com/p/643567135"));
								messageService.sendMessage(new MessageReq(1,1,1,"终审通知","https://zhuanlan.zhihu.com/p/643567135"));
								Map<String,Object> defaultParameterMap = new HashMap<String,Object>();
								if(StringUtils.isNoneEmpty(event.getParameterReq()) && null!=req.getPayItemsList()) {
									try {
										defaultParameterMap =mapper.readValue(event.getParameterReq(), Map.class);
									} catch (JsonMappingException e) {
										// TODO Auto-generated catch block
										e.printStackTrace();
									} catch (JsonProcessingException e) {
										// TODO Auto-generated catch block
										e.printStackTrace();
									}
									if(defaultParameterMap.containsKey("payItemsList")) {
										List<Map<String,String>> payItemsList = (List<Map<String, String>>) defaultParameterMap.get("payItemsList");
										for(Map<String,String> map : req.getPayItemsList()) {
											
											Iterator<Map<String,String>> it = payItemsList.iterator();
											String payItemsIdReq = map.containsKey("payItemsId") ? map.get("payItemsId") :" ";
											while(it.hasNext()){
												Map<String,String> payItemsListMap = it.next();
												String payItemsId = payItemsListMap.containsKey("payItemsId") ? payItemsListMap.get("payItemsId") :"";
												if(payItemsIdReq.equals(payItemsId)) {
											        it.remove();
											    }
											}
											map.put("containId", map.get("payItemsId"));
											payItemsList.add(map);
										}
										defaultParameterMap.put("payItemsList", payItemsList);
				
									}
								}else if(StringUtils.isEmpty(event.getParameterReq()) && null!=req.getPayItemsList()) {
									List<Map<String,String>> payItemsList = new ArrayList<>();
									for(Map<String,String> map : req.getPayItemsList()) {
										map.put("containId", map.get("payItemsId"));
										payItemsList.add(map);
									}
									defaultParameterMap.put("payItemsList", payItemsList);
								}
								try {
									event.setParameterReq(mapper.writeValueAsString(defaultParameterMap));
								} catch (JsonProcessingException e) {
									e.printStackTrace();
								}
							} else {
								event.setEventState(5);
								diaryForm = new OperationDiaryForm("物业"+str,
										"物业"+str+"通过并且订单完成！"+str+"意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
							}
							diaryFormList.add(diaryForm);
							event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
						} else if (null != event.getEventCategory() && event.getEventCategory().getIsDispatching() == 1) {
							diaryForm = new OperationDiaryForm("物业"+str,
									"物业"+str+"通过！"+str+"意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
							diaryFormList.add(diaryForm);
							event.setEventState(req.getEventState());
						} else {
							res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
							res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
							return res;
						}
						event.setLastModifyTime(new Timestamp(new Date().getTime()));
					}else if(req.getEventState() == 2 && userObj instanceof PlatformUserEntity){//一级审核时，审核通过，如果装修资料审核外的其它审核流程改成二级审核时要把这段代码删除
						PlatformUserEntity user = (PlatformUserEntity)userObj;
						OperationDiaryForm diaryForm = null;
						if(null!=event.getEventCategory() && event.getEventCategory().getIsDispatching()==0) {
							if(null!=event.getEventCategory().getCategoryType() && event.getEventCategory().getCategoryType()==1) {
								event.setEventState(7);
								diaryForm = new OperationDiaryForm("装修申请资料"+str,
										str+"通过！"+str+"意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
							}else {
								event.setEventState(5);
								diaryForm = new OperationDiaryForm("物业"+str,
										"物业"+str+"通过并且订单完成！"+str+"意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
							}
							diaryFormList.add(diaryForm);
							event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
						} else {
							res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
							res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
							return res;
						}
						event.setLastModifyTime(new Timestamp(new Date().getTime()));
					}else if(req.getEventState() == 15 && userObj instanceof PlatformUserEntity ) {//二级审核时，初审核不通过
						PlatformUserEntity user = (PlatformUserEntity)userObj;
						OperationDiaryForm diaryForm = null;
						if(null!=event.getEventCategory().getCategoryType() && event.getEventCategory().getCategoryType()==1) {
							diaryForm = new OperationDiaryForm("装修申请资料初审",
									"初审不通过！意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
						}else {
							diaryForm = new OperationDiaryForm("物业初审",
									"初审不通过！意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
						}
						diaryFormList.add(diaryForm);
						event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
						event.setEventState(req.getEventState());
						event.setLastModifyTime(new Timestamp(new Date().getTime()));
					}else if(req.getEventState() == 3 && userObj instanceof PlatformUserEntity ) {//一级审核时，审核不通过，如果装修资料审核外的其它审核流程改成二级审核时要把这段代码删除
						PlatformUserEntity user = (PlatformUserEntity)userObj;
						OperationDiaryForm diaryForm = null;
						if(null!=event.getEventCategory().getCategoryType() && event.getEventCategory().getCategoryType()==1) {
							diaryForm = new OperationDiaryForm("装修申请资料"+str,
									str+"不通过！意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
						}else {
							diaryForm = new OperationDiaryForm("物业"+str,
									str+"不通过！意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
						}

						diaryFormList.add(diaryForm);
						event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
						event.setEventState(req.getEventState());
						event.setLastModifyTime(new Timestamp(new Date().getTime()));
					}else if(req.getEventState() == 6 && userObj instanceof CommunityMemberEntity ){
						CommunityMemberEntity member = (CommunityMemberEntity)userObj;
						OperationDiaryForm diaryForm = new OperationDiaryForm("撤消",
								"业主撤消订单！",DateUtil.formatLongFormat(new Date()),member.getUserName(),member.getId(),"M");
						diaryFormList.add(diaryForm);
						event.setEventState(req.getEventState());
						event.setLastModifyTime(new Timestamp(new Date().getTime()));
					}else if(req.getEventState() == 0 && userObj instanceof PlatformUserEntity ) {//驳回
						PlatformUserEntity user = (PlatformUserEntity)userObj;
						OperationDiaryForm diaryForm = null;
						diaryForm = new OperationDiaryForm("物业"+str,
								"物业驳回此申请！"+str+"意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
						diaryFormList.add(diaryForm);
						event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
						event.setEventState(0);
						event.setLastModifyTime(new Timestamp(new Date().getTime()));
					}else {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
						return res;
					}
				}else if(event.getEventState()==14){//在复审核状态
					String auditOpinion = StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "无;";
					if(req.getEventState() == 2 && userObj instanceof PlatformUserEntity ) {//审核通过
						PlatformUserEntity user = (PlatformUserEntity)userObj;
						OperationDiaryForm diaryForm = null;
						if(null!=event.getEventCategory() && event.getEventCategory().getIsDispatching()==0) {
							if(null!=event.getEventCategory().getCategoryType() && event.getEventCategory().getCategoryType()==1) {
								Map<String,Object> defaultParameterMap = new HashMap<String,Object>();
								try {
									if(StringUtils.isNoneEmpty(event.getParameterReq())) {
										defaultParameterMap =mapper.readValue(event.getParameterReq(), Map.class);
										boolean state = false;
										if(defaultParameterMap.containsKey("payItemsList")) {
											List<Map<String,String>> payItemsList = (List<Map<String, String>>) defaultParameterMap.get("payItemsList");
											for(Map<String,String> payItemsListMap : payItemsList) {
												CommunityReceivablesReq receivablesReq = new CommunityReceivablesReq();
												if(payItemsListMap.containsKey("isEachCharge") && payItemsListMap.get("isEachCharge").equals("0")) {
													List<CommunityReceivablesEntity> list= communityReceivablesDao.getListByHql("select a from CommunityReceivablesEntity a where "
															+ " a.receivableAmount!=a.receivedAmount  and a.payItem.id in("+payItemsListMap.get("containId")+") and a.estate.id="
															+event.getProperty().getId(), "");
													if(null ==list || (null!=list&&list.size()==0)) {
														receivablesReq.setReceivableAmount(payItemsListMap.get("price"));
														receivablesReq.setReceivableDate(DateUtil.formatShortFormat(new Date()));
														receivablesReq.setEstateId(event.getProperty().getId());
														receivablesReq.setPayItemId(Integer.valueOf(payItemsListMap.get("payItemsId")));
														AddCommunityReceivablesRes receivablesRes = addCommunityReceivables(receivablesReq, event);
														if(!receivablesRes.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
															res.setRet(receivablesRes.getRet());
															res.setRetInfo(receivablesRes.getRetInfo());
															return res;
														}
													}
												}else if(payItemsListMap.containsKey("isEachCharge") && payItemsListMap.get("isEachCharge").equals("1")){
													receivablesReq.setReceivableAmount(payItemsListMap.get("price"));
													receivablesReq.setReceivableDate(DateUtil.formatShortFormat(new Date()));
													receivablesReq.setEstateId(event.getProperty().getId());
													receivablesReq.setPayItemId(Integer.valueOf(payItemsListMap.get("payItemsId")));
													AddCommunityReceivablesRes receivablesRes = addCommunityReceivables(receivablesReq, event);
													if(!receivablesRes.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
														res.setRet(receivablesRes.getRet());
														res.setRetInfo(receivablesRes.getRetInfo());
														return res;
													}
													
												}else {
													res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
													res.setRetInfo("参数有误，请检查事件类型参数（收费项目信息）");
													return res;
												}
												if(StringUtils.isNoneEmpty(payItemsListMap.get("payItemsId"))) {
													CommunityPayItemsEntity communityPayItems = communityPayItemsDao.
															get(Integer.valueOf(payItemsListMap.get("payItemsId")));
													if (null != communityPayItems) {
														if(communityPayItems.getChargeCategory()==7) {
															state=true;
														}
													}
												}
											}
				
											event.setHaveCashPledge(state ? 1 : 0);
										}else{
											res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
											res.setRetInfo("请配置事件类型参数（收费项目信息）");
											return res;
										}
									}else {
										res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
										res.setRetInfo("请配置事件类型参数（收费项目信息）");
										return res;
									}
								} catch (JSONException | IOException e) {
									logger.info("系统错误：{}",e);
								}
                                event.setEventState(7);
								diaryForm = new OperationDiaryForm("装修申请资料复审",
										"复审通过！"+str+"意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
							}else {
								event.setEventState(5);
								diaryForm = new OperationDiaryForm("物业"+str,
										"物业"+str+"通过并且订单完成！"+str+"意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
							}

							diaryFormList.add(diaryForm);
							event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
						}else if(null!=event.getEventCategory() && event.getEventCategory().getIsDispatching()==1){
							diaryForm = new OperationDiaryForm("物业"+str,
									"物业"+str+"通过！"+str+"意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
							diaryFormList.add(diaryForm);
							event.setEventState(req.getEventState());
						}else {
							res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
							res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
							return res;
						}
						event.setLastModifyTime(new Timestamp(new Date().getTime()));
					}else if(req.getEventState() == 3 && userObj instanceof PlatformUserEntity ) {//终审核不通过
						PlatformUserEntity user = (PlatformUserEntity)userObj;
						OperationDiaryForm diaryForm = null;
						if(null!=event.getEventCategory().getCategoryType() && event.getEventCategory().getCategoryType()==1) {
							diaryForm = new OperationDiaryForm("装修申请资料复审",
									"复审不通过！意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
						}else {
							diaryForm = new OperationDiaryForm("物业复审",
									"复审不通过！意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
						}

						diaryFormList.add(diaryForm);
						event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
						event.setEventState(1);
						event.setLastModifyTime(new Timestamp(new Date().getTime()));
						//如果装修资料审核外的其它审核流程改成二级审核时,去除这段代码注释
//					}else if(req.getEventState() == 15 && userObj instanceof PlatformUserEntity ) {//初审核不通过
//						PlatformUserEntity user = (PlatformUserEntity)userObj;
//						OperationDiaryForm diaryForm = null;
//						if(null!=event.getEventCategory().getCategoryType() && event.getEventCategory().getCategoryType()==1) {
//							diaryForm = new OperationDiaryForm("装修申请资料初审",
//									"初审不通过！意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
//						}else {
//							diaryForm = new OperationDiaryForm("物业初审",
//									"初审不通过！意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
//						}
//						diaryFormList.add(diaryForm);
//						event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
//						event.setEventState(req.getEventState());
//						event.setLastModifyTime(new Timestamp(new Date().getTime()));
//					}else if(req.getEventState() == 6 && userObj instanceof CommunityMemberEntity ){
//						CommunityMemberEntity member = (CommunityMemberEntity)userObj;
//						OperationDiaryForm diaryForm = new OperationDiaryForm("撤消",
//								"业主撤消订单！",DateUtil.formatLongFormat(new Date()),member.getUserName(),member.getId(),"M");
//						diaryFormList.add(diaryForm);
//						event.setEventState(req.getEventState());
//						event.setLastModifyTime(new Timestamp(new Date().getTime()));
//					}else if(req.getEventState() == 0 && userObj instanceof PlatformUserEntity ) {//驳回
//						PlatformUserEntity user = (PlatformUserEntity)userObj;
//						OperationDiaryForm diaryForm = null;
//						diaryForm = new OperationDiaryForm("物业审核",
//								"物业驳回此申请！审核意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
//						diaryFormList.add(diaryForm);
//						event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
//						event.setEventState(0);
//						event.setLastModifyTime(new Timestamp(new Date().getTime()));
					}else if(req.getEventState() == 1 && userObj instanceof PlatformUserEntity ) {//驳回
						PlatformUserEntity user = (PlatformUserEntity)userObj;
						OperationDiaryForm diaryForm = null;
						diaryForm = new OperationDiaryForm("复审驳回",
								"驳回初审！"+str+"意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
						diaryFormList.add(diaryForm);
						event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
						event.setEventState(1);
						event.setLastModifyTime(new Timestamp(new Date().getTime()));
					}
					else {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
						return res;
					}
				}else if(event.getEventState()==2 && req.getEventState()!=6){//在审核通过状态
					if(null == req.getHandlerId()) {
						res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
						return res;
					}
					CommunityUserEntity user = communityUserDao.get(req.getHandlerId());
					if(null==user || (req.getEventState() != 4 && null!=event.getEventCategory() && 
							event.getEventCategory().getIsDispatching()==0)) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
						return res;
					}

					if(!(userObj instanceof PlatformUserEntity)) {
						res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
						res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
						return res;
					}
					event.setHandler(user);
					event.setServicingTime(new Timestamp(new Date().getTime()));
					event.setEventState(req.getEventState());
					OperationDiaryForm diaryForm = new OperationDiaryForm("派单",
							"物业派单给师傅！",DateUtil.formatLongFormat(new Date()),user.getUserName(),user.getId(),"U");
					diaryFormList.add(diaryForm);
					event.setLastModifyTime(new Timestamp(new Date().getTime()));
				}else if(event.getEventState()==4 && req.getEventState()!=6){//在派单状态下
					if(req.getEventState() != 5) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
						return res;
					}
					if(!(userObj instanceof PlatformUserEntity)) {
						res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
						res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
						return res;
					}
					PlatformUserEntity user = (PlatformUserEntity)userObj;
					OperationDiaryForm diaryForm = new OperationDiaryForm("订单完成",
							"订单完成！",DateUtil.formatLongFormat(new Date()),user.getUserName(),user.getId(),"U");
					diaryFormList.add(diaryForm);
					event.setEventState(req.getEventState());
					event.setLastModifyTime(new Timestamp(new Date().getTime()));
				}else if(event.getEventState()==8 && req.getEventState()==9){//装修结束

					if(userObj instanceof PlatformUserEntity) {
						PlatformUserEntity user = (PlatformUserEntity)userObj;
						OperationDiaryForm diaryForm = new OperationDiaryForm("装修完成",
								"后台人员操作装修已完成！",DateUtil.formatLongFormat(new Date()),user.getUserName(),user.getId(),"U");
						diaryFormList.add(diaryForm);
					}else if(userObj instanceof CommunityMemberEntity) {
						CommunityMemberEntity member = (CommunityMemberEntity)userObj;
						OperationDiaryForm diaryForm = new OperationDiaryForm("装修完成",
								"业主选择装修已完成！",DateUtil.formatLongFormat(new Date()),member.getUserName(),member.getId(),"M");
						diaryFormList.add(diaryForm);
					}
					event.setLastModifyTime(new Timestamp(new Date().getTime()));
					event.setEventState(req.getEventState());
				}else if((event.getEventState()==9 && req.getEventState()==10) || 
						(event.getEventState()==11 && req.getEventState()==10)){//提交验收申请
					if(userObj instanceof CommunityMemberEntity) {
						CommunityMemberEntity member = (CommunityMemberEntity)userObj;
						OperationDiaryForm diaryForm = new OperationDiaryForm("提交验收申请",
								"业主提交验收申请操作！",DateUtil.formatLongFormat(new Date()),member.getUserName(),member.getId(),"M");
						diaryFormList.add(diaryForm);
						
						try {
							event.setAcceptanceTime(StringUtils.isNotEmpty(req.getAcceptanceTime()) ? 
									new Timestamp(DateUtil.parseLongFormat(req.getAcceptanceTime()).getTime()) : 
										new Timestamp(new Date().getTime()));
						} catch (ParseException e) {
							e.printStackTrace();
						}
						if(event.getEventState()==9) {
							String checkAcceptItems="";
					        try {
					        	//str = mapper.writeValueAsString(getEventCategoryList(event.getEventCategory().getId()));
					        	checkAcceptItems = mapper.writeValueAsString(getEventCategoryList(event.getEventCategory().getId()));
					        } catch (JsonProcessingException e) {
					            e.printStackTrace();
					        }
					        event.setCheckAcceptItems(checkAcceptItems);
					        event.setCheckAcceptAuditState("1110");
						}else {
							event.setCheckAcceptAuditState(event.getCheckAcceptAuditState().replaceAll("3", "1"));
						}
				        event.setEventState(req.getEventState());
				        event.setLastModifyTime(new Timestamp(new Date().getTime()));
					}
				}else if(req.getEventState()==6 && userObj instanceof PlatformUserEntity){
					String auditOpinion = StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "无;";
					PlatformUserEntity user = (PlatformUserEntity)userObj;
					OperationDiaryForm diaryForm = new OperationDiaryForm("物业撤消",
							"物业撤消到上一级状态！"+str+"意见："+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
					diaryFormList.add(diaryForm);
					if(event.getEventState()==2 || event.getEventState()==3 || event.getEventState()==4 || event.getEventState()==4) {
						event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
						event.setEventState(1);
					}else {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
						return res;
					}
				}else if(event.getEventState()==12 && req.getEventState()==13){
					if(!(userObj instanceof CommunityMemberEntity)) {
						res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
						res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
						return res;
					}
					if(null==req.getRefundMethod()) {
						res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
						return res;
					}
					CommunityMemberEntity member = (CommunityMemberEntity)userObj;
					OperationDiaryForm diaryForm = new OperationDiaryForm("提交退款申请",
							"业主提交退款申请！",DateUtil.formatLongFormat(new Date()),member.getUserName(),member.getId(),"M");
					diaryFormList.add(diaryForm);
					RefundInfoVo refundInfoVo = new RefundInfoVo((StringUtils.isNoneEmpty(req.getBankName())?req.getBankName():"") , 
							(StringUtils.isNoneEmpty(req.getBankAccount())?req.getBankAccount():""), 
							(StringUtils.isNoneEmpty(req.getAccountName())?req.getAccountName():""),
							(StringUtils.isNoneEmpty(req.getReceiptImageIds())?req.getReceiptImageIds():""),
							(StringUtils.isNoneEmpty(req.getIdCardImageIds())?req.getIdCardImageIds():""),
							(StringUtils.isNoneEmpty(req.getBankImageIds())?req.getBankImageIds():""));
					event.setEventState(req.getEventState());
					event.setRefundMethod(req.getRefundMethod());
					event.setLastModifyTime(new Timestamp(new Date().getTime()));
					try {
						event.setRefundInfo(mapper.writeValueAsString(refundInfoVo));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
				}else if(event.getEventState()==13 && req.getEventState()==12 && userObj instanceof PlatformUserEntity){
					PlatformUserEntity user = (PlatformUserEntity)userObj;
					OperationDiaryForm diaryForm = new OperationDiaryForm("驳回退款申请",
							"业主退款申请被驳回！",DateUtil.formatLongFormat(new Date()),user.getUserName(),user.getId(),"U");
					diaryFormList.add(diaryForm);
					event.setEventState(req.getEventState());
				}else {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				
				event.setDiary(json.put("diary", diaryFormList).toString());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	@SuppressWarnings("unchecked")
	@Override
	@Audit(operate = "设置事件状态")
	public IResponse audit(CommunityEventsReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		if (null != req.getEventsId()) {
			CommunityEventsEntity event = communityEventsDao.get(req.getEventsId());
			if (null != event && null!=req.getEventState()) {
				event.setImage(null!=req.getImageId() ? assetDao.get(req.getImageId()) : null);
				JSONObject json = null; 
				List<OperationDiaryForm> diaryFormList = null;
				if(StringUtils.isNoneEmpty(event.getDiary())) {
					json = new JSONObject(event.getDiary());
					try {
						diaryFormList =mapper.readValue(json.get("diary").toString(), ArrayList.class);
					} catch (JSONException | IOException e) {
						logger.info("json转对象出现错误：{}",e);
					}

                }else {
					json = new JSONObject();
					diaryFormList = new ArrayList<OperationDiaryForm>();
				}
				if(null!=event.getEventType()&&event.getEventType()==1) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				String str = (null==event.getEventType() ||
						(null!=event.getEventType()&&0==event.getEventType())) ? "受理":"审核";
				if(userObj instanceof PlatformUserEntity){
						PlatformUserEntity user = (PlatformUserEntity)userObj;
						OperationDiaryForm diaryForm = null;

						String auditOpinion = StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "无;";
						event.setEventState(req.getEventState());
						
						String auditLevel = StringUtils.isNotEmpty(req.getAuditLevel()) ? req.getAuditLevel() : str;
						String auditContent = StringUtils.isNotEmpty(req.getAuditContent()) ? req.getAuditContent()+str+"意见：" : str+"意见：";
						diaryForm = new OperationDiaryForm(auditLevel+"“"+event.getTitle()+"”申请",
								auditContent+auditOpinion,DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");

						diaryFormList.add(diaryForm);
						event.setAuditOpinion(StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "");
						event.setDiary(json.put("diary", diaryFormList).toString());

						event.setLastModifyTime(new Timestamp(new Date().getTime()));
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	
	public Map<String,String> getEventCategoryList(Integer parentEventCategoryId) {
		List<CommunityEventCategoryForm> eventCategoryList = new ArrayList<CommunityEventCategoryForm>();
		Map<String,String> map = new HashMap<>();
		StringBuilder hql = new StringBuilder("select distinct a from CommunityEventCategoryEntity a ");
		hql.append(" where  a.state="+EntityContext.RECORD_STATE_VALID)
			.append(" and a.parentEventCategory.id="+parentEventCategoryId)
			.append(" and a.categoryType=1");
		List<CommunityEventCategoryEntity> list = communityEventCategoryDao.getListByHql(hql.toString(),null);
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		ObjectMapper mapper = new ObjectMapper();
		list.forEach(o -> {
			CommunityEventCategoryForm eventCategoryForm = new CommunityEventCategoryForm(o.getId(),
					o.getCategoryName(),o.getCategoryLevel(),o.getIsDispatching(),
					sdf.format(o.getCreateTime()),sdf.format(o.getLastModifyTime()),o.getIcon());
			eventCategoryForm.setOrders(o.getOrders());
			o.getEventCategoryItemsList().forEach(p->{
				List<Object> optionslList = new ArrayList<Object>();
				try {
					if(StringUtils.isNoneEmpty(p.getOptions())) {
						optionslList =mapper.readValue(p.getOptions(), ArrayList.class);
					}
				} catch (JsonParseException e) {
					e.printStackTrace();
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JSONException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
				CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(p.getId(),
						p.getItemName(),p.getItemkey(),p.getOrders(),p.getDataType(),optionslList,p.getIsRequiredng()) ;
				eventCategoryItemsForm.setReflectionField(p.getReflectionField());
				eventCategoryItemsForm.setSubEventCategoryItemsList(getSubEventCategoryItemsList(p.getSubEventCategoryItemsList()));
				eventCategoryItemsForm.setIsCascade(p.getIsCascade());
				eventCategoryItemsForm.setCascadeValue(StringUtils.isNotEmpty(p.getCascadeValue()) ? p.getCascadeValue():"");
				eventCategoryForm.getEventCategoryItemList().add(eventCategoryItemsForm);
			});
			Map<Integer, List<CommunityEventsEntity>> collect1 = (Map<Integer, List<CommunityEventsEntity>>) o.getCommunityEventsList()
			    .parallelStream().collect(groupingBy(CommunityEventsEntity::getEventState));
			eventCategoryForm.setPendingCount(collect1.containsKey(1) ? collect1.get(1).size() : 0);
			eventCategoryForm.setCompleteCount(collect1.containsKey(5) ? collect1.get(5).size() : 0);
			eventCategoryForm.setCategoryType(eventCategoryForm.getCategoryType());
			eventCategoryForm.getEventCategoryItemList().sort(comparingInt(CommunityEventCategoryItemsForm::getOrders));
			eventCategoryForm.setCategoryType(o.getCategoryType());
			
			if(null != o.getParentEventCategory()) {
				CommunityEventCategoryForm parentEventCategoryForm = new CommunityEventCategoryForm(
					o.getParentEventCategory().getId(),o.getParentEventCategory().getCategoryName(),
					o.getParentEventCategory().getCategoryLevel(),o.getParentEventCategory().getIsDispatching(),
					sdf.format(o.getParentEventCategory().getCreateTime()),
					sdf.format(o.getParentEventCategory().getLastModifyTime()),o.getParentEventCategory().getIcon());
				eventCategoryForm.setParentEventCategoryForm(parentEventCategoryForm);
			}

			eventCategoryForm.setSubEventCategoryList(getSubEventCategoryList(o.getSubEventCategoryList(),20));
			eventCategoryForm.setIsSend(o.getIsSend());
			eventCategoryForm.setDefaultParameter(o.getDefaultParameter());
			eventCategoryForm.getSubEventCategoryList().sort(comparingInt(CommunityEventCategoryForm::getOrders));
			//eventCategoryList.add(eventCategoryForm);
			String str ="";
			try {
				str = mapper.writeValueAsString(eventCategoryForm);
			} catch (JsonProcessingException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			if(eventCategoryForm.getCategoryName().contains("工程")) {
				map.put("engineeringDept", str);
			}else if(eventCategoryForm.getCategoryName().contains("客服")) {
				map.put("customerServiceDept", str);
			} else if(eventCategoryForm.getCategoryName().contains("环境")) {
				map.put("environmentDept", str);
			}
		});
		return map;
	}
	
	@SuppressWarnings("unchecked")
	public List<CommunityEventCategoryForm> getSubEventCategoryList(List<CommunityEventCategoryEntity> list,Integer depth){
		List<CommunityEventCategoryForm> subEventCategoryList = new ArrayList<CommunityEventCategoryForm>();
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		depth--;
		for(CommunityEventCategoryEntity o : list){
			CommunityEventCategoryForm eventCategoryForm = new CommunityEventCategoryForm(o.getId(),
					o.getCategoryName(),o.getCategoryLevel(),o.getIsDispatching(),
					sdf.format(o.getCreateTime()),sdf.format(o.getLastModifyTime()),o.getIcon());
			eventCategoryForm.setOrders(o.getOrders());
			Map<Integer, List<CommunityEventsEntity>> collect1 = (Map<Integer, List<CommunityEventsEntity>>) o.getCommunityEventsList()
				    .parallelStream().collect(groupingBy(CommunityEventsEntity::getEventState));
			eventCategoryForm.setPendingCount(collect1.containsKey(1) ? collect1.get(1).size() : 0);
			o.getEventCategoryItemsList().forEach(p->{
				List<Object> optionslList = new ArrayList<Object>();
				try {
					if(StringUtils.isNoneEmpty(p.getOptions())) {
						optionslList =mapper.readValue(p.getOptions(), ArrayList.class);
					}
				} catch (JsonParseException e) {
					e.printStackTrace();
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JSONException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
				CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(p.getId(),
						p.getItemName(),p.getItemkey(),p.getOrders(),p.getDataType(),optionslList,p.getIsRequiredng()) ;
				eventCategoryItemsForm.setReflectionField(p.getReflectionField());
				eventCategoryItemsForm.setSubEventCategoryItemsList(getSubEventCategoryItemsList(p.getSubEventCategoryItemsList()));
				eventCategoryItemsForm.setIsCascade(p.getIsCascade());
				eventCategoryItemsForm.setCascadeValue(StringUtils.isNotEmpty(p.getCascadeValue()) ? p.getCascadeValue():"");
				eventCategoryForm.getEventCategoryItemList().add(eventCategoryItemsForm);
			});
			eventCategoryForm.setCategoryType(depth);
			eventCategoryForm.setCategoryType(o.getCategoryType());
			eventCategoryForm.setIsSend(o.getIsSend());
			eventCategoryForm.setDefaultParameter(o.getDefaultParameter());
			eventCategoryForm.getEventCategoryItemList().sort(comparingInt(CommunityEventCategoryItemsForm::getOrders));
			eventCategoryForm.getSubEventCategoryList().sort(comparingInt(CommunityEventCategoryForm::getOrders));
			if(null != depth && depth>=1) {
				eventCategoryForm.setSubEventCategoryList(getSubEventCategoryList(o.getSubEventCategoryList(),depth));
			}
			subEventCategoryList.add(eventCategoryForm);
		}
		
		return subEventCategoryList;
	}
	
	@SuppressWarnings("unchecked")
	public List<CommunityEventCategoryItemsForm> getSubEventCategoryItemsList(List<CommunityEventCategoryItemsEntity> list){
		List<CommunityEventCategoryItemsForm> subItemsList = new ArrayList<CommunityEventCategoryItemsForm>();
		for(CommunityEventCategoryItemsEntity o : list){
			List<Object> optionslList = new ArrayList<Object>();
			try {
				if(StringUtils.isNoneEmpty(o.getOptions())) {
					optionslList =mapper.readValue(o.getOptions(), ArrayList.class);
				}
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JSONException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(o.getId(),
					o.getItemName(),o.getItemkey(),o.getOrders(),o.getDataType(),optionslList,o.getIsRequiredng());
			eventCategoryItemsForm.setItemTag(StringUtils.isNotEmpty(o.getItemTag()) ? o.getItemTag() :"");
			eventCategoryItemsForm.setItemTag(StringUtils.isNotEmpty(o.getItemTag()) ? o.getItemTag() :"");
			eventCategoryItemsForm.setReflectionField(o.getReflectionField());
			eventCategoryItemsForm.setIsCascade(o.getIsCascade());
			eventCategoryItemsForm.setCascadeValue(StringUtils.isNotEmpty(o.getCascadeValue()) ? o.getCascadeValue():"");
			eventCategoryItemsForm.setSubEventCategoryItemsList(getSubEventCategoryItemsList(o.getSubEventCategoryItemsList()));
			subItemsList.add(eventCategoryItemsForm);
		}
		return subItemsList;
	}
	
	public IResponse addCommunityEventReceivables(CommunityEventsReq req) {
		AddCommunityReceivablesRes res = new AddCommunityReceivablesRes();
		Object userObj = getPrincipal(true);
		ObjectMapper mapper = new ObjectMapper();
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		if(!(userObj instanceof CommunityMemberEntity)) {
			res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
			return res;
		}
		CommunityMemberEntity member = (CommunityMemberEntity)userObj;
		if (null != req.getEventsId()) {
			CommunityEventsEntity event = communityEventsDao.get(req.getEventsId());
			if (null != event && (null!=event.getEventState() && event.getEventState()==7) && !member.getId().toString().
					equals(event.getMember().getId().toString())) {
				if(null!=event.getEventCategory().getCategoryType() && event.getEventCategory().getCategoryType()==1) {
					Map<String,Object> defaultParameterMap = new HashMap<String,Object>();
					try {
						if(StringUtils.isNoneEmpty(event.getParameterReq())) {
							defaultParameterMap =mapper.readValue(event.getParameterReq(), Map.class);
							if(defaultParameterMap.containsKey("payItemsList")) {
								List<Map<String,String>> payItemsList = (List<Map<String, String>>) defaultParameterMap.get("payItemsList");
								boolean state = false;
								for(Map<String,String> payItemsListMap : payItemsList) {
									CommunityReceivablesReq receivablesReq = new CommunityReceivablesReq();
									if(payItemsListMap.containsKey("isEachCharge") && payItemsListMap.get("isEachCharge").equals("0")) {
										List<CommunityReceivablesEntity> list= communityReceivablesDao.getListByHql("select a from CommunityReceivablesEntity a where "
												+ " a.receivableAmount!=a.receivedAmount  and a.payItem.id in("+payItemsListMap.get("containId")+") and a.estate.id="
												+event.getProperty().getId(), "");
										if(null ==list || (null!=list&&list.size()==0)) {
											receivablesReq.setReceivableAmount(payItemsListMap.get("price"));
											receivablesReq.setReceivableDate(DateUtil.formatShortFormat(new Date()));
											receivablesReq.setEstateId(event.getProperty().getId());
											receivablesReq.setPayItemId(Integer.valueOf(payItemsListMap.get("payItemsId")));
											AddCommunityReceivablesRes receivablesRes = addCommunityReceivables(receivablesReq, event);
											if(!receivablesRes.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
												res.setRet(receivablesRes.getRet());
												res.setRetInfo(receivablesRes.getRetInfo());
												return res;
											}
										}
									}else if(payItemsListMap.containsKey("isEachCharge") && payItemsListMap.get("isEachCharge").equals("1")){
										receivablesReq.setReceivableAmount(payItemsListMap.get("price"));
										receivablesReq.setReceivableDate(DateUtil.formatShortFormat(new Date()));
										receivablesReq.setEstateId(event.getProperty().getId());
										receivablesReq.setPayItemId(Integer.valueOf(payItemsListMap.get("payItemsId")));
										AddCommunityReceivablesRes receivablesRes = addCommunityReceivables(receivablesReq, event);
										if(!receivablesRes.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
											res.setRet(receivablesRes.getRet());
											res.setRetInfo(receivablesRes.getRetInfo());
											return res;
										}
									}else {
										res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
										res.setRetInfo("参数有误，请检查事件类型参数（收费项目信息）");
										return res;
									}
									if(StringUtils.isNoneEmpty(payItemsListMap.get("payItemsId"))) {
										CommunityPayItemsEntity communityPayItems = communityPayItemsDao.
												get(Integer.valueOf(payItemsListMap.get("payItemsId")));
										if (null != communityPayItems) {
											if(communityPayItems.getChargeCategory()==7) {
												state=true;
											}
										}
									}
								}
								event.setHaveCashPledge(state ? 1 : 0);
								JSONObject json = null; 
								List<OperationDiaryForm> diaryFormList = null;
								if(StringUtils.isNoneEmpty(event.getDiary())) {
									json = new JSONObject(event.getDiary());
									try {
										diaryFormList =mapper.readValue(json.get("diary").toString(), ArrayList.class);
									} catch (JsonParseException e) {
										e.printStackTrace();
									} catch (JsonMappingException e) {
										e.printStackTrace();
									} catch (JSONException e) {
										e.printStackTrace();
									} catch (IOException e) {
										e.printStackTrace();
									}
									
								}else {
									json = new JSONObject();
									diaryFormList = new ArrayList<OperationDiaryForm>();
								}
			                    GregorianCalendar gc = new GregorianCalendar();
			                    gc.setTimeInMillis(new Date().getTime());
			                    gc.add(Calendar.MONTH, 3);
			                    
								OperationDiaryForm diaryForm = new OperationDiaryForm("申请延期",
										"业主申请延期操作！原开始时间："+DateUtil.formatLongFormat(event.getStartTime())+",改成开始时间："+
												DateUtil.formatLongFormat(new Date())
										+"原结束时间："+DateUtil.formatLongFormat(event.getEndTime())+",改成结束时间："
										+DateUtil.formatLongFormat(new Timestamp(gc.getTimeInMillis())),
										DateUtil.formatLongFormat(new Date()),member.getUserName(),member.getId(),"M");
								event.setStartTime(new Timestamp(new Date().getTime()));
								event.setEndTime(new Timestamp(gc.getTimeInMillis()));
								diaryFormList.add(diaryForm);
								event.setDiary(json.put("diary", diaryFormList).toString());
							}else{
								res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
								res.setRetInfo("请配置事件类型参数（收费项目信息）");
								return res;
							}
						}else {
							res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
							res.setRetInfo("请配置事件类型参数（收费项目信息）");
							return res;
						}
					} catch (JsonParseException e) {
						e.printStackTrace();
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (JSONException e) {
						e.printStackTrace();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	public AddCommunityReceivablesRes addCommunityReceivables(CommunityReceivablesReq req,CommunityEventsEntity event) {
		AddCommunityReceivablesRes res = new AddCommunityReceivablesRes();

		if (StringUtils.isEmpty(req.getReceivableAmount())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "应收金额不能为空！");
			return res;
		}
		if (StringUtils.isEmpty(req.getReceivableDate())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "应收日期不能为空！");
			return res;
		}
		if (null == req.getEstateId()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "单元ID不能为空！");
			return res;
		}
		if (null == req.getPayItemId()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "收费项目ID不能为空！");
			return res;
		}
		BigDecimal receivableAmount = BigDecimal.ZERO;
		try {
			receivableAmount = new BigDecimal(req.getReceivableAmount());
			if (receivableAmount.compareTo(BigDecimal.ZERO) == 0) {
				res.setRet(ResponseContext.RES_DATA_ERROR_INFO);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_CODE + "应收金额不能为0！");
				return res;
			}
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_DATA_ERROR_INFO);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_CODE + "应收金额格式有误，应为货币数值！");
			return res;
		}
		CommunityEstateEntity communityEstateEntity = communityEstateDao.get(req.getEstateId());
		if (communityEstateEntity == null) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到对应的单元信息！");
			return res;
		}

		CommunityPayItemsEntity communityPayItemsEntity = communityPayItemsDao.get(req.getPayItemId());
		if (communityPayItemsEntity == null) {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到收费项目信息！");
			return res;
		}
		if (communityPayItemsEntity.getPayDate() == 0) {
			req.setChargeSource("其它收费");
		}
		CommunityReceivablesEntity communityReceivables = new CommunityReceivablesEntity();
		communityReceivables.setChargeCategory(
				CommunityPayItemsEntity.getChargeCategoryStr(communityPayItemsEntity.getChargeCategory()));

		communityReceivables.setChargeSource(req.getChargeSource());
		communityReceivables.setLockMark(0);
		communityReceivables.setState(1);
		communityReceivables.setComment(req.getComment());
		Date now = new Date();
		try {
			communityReceivables.setEndTime(
					StringUtils.isNotEmpty(req.getEndTime()) ? DateUtil.parseLongFormat(req.getEndTime()) : now);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		communityReceivables.setOldData(req.getOldData());
		communityReceivables.setOldId(req.getOldId());
		communityReceivables.setReceivablesNO(StringUtils.isNotEmpty(req.getReceivablesNO()) ? req.getReceivablesNO()
				: generateSnLastValue(RECEIVABLESNO_SN_TYPE).toString());
		communityReceivables.setPayItemsName(communityPayItemsEntity.getItemsName());

		try {
			communityReceivables.setPaymentPeriod(
					StringUtils.isNotEmpty(req.getPaymentPeriod()) ? DateUtil.parseLongFormat(req.getPaymentPeriod())
							: now);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		communityReceivables.setReceivableAmount(receivableAmount);
		try {
			communityReceivables.setReceivableDate(StringUtils.isNotEmpty(req.getReceivableDate())
					? (req.getReceivableDate().trim().length() > 10 ? DateUtil.parseLongFormat(req.getReceivableDate())
							: DateUtil.parseLongFormat(req.getReceivableDate() + " 00:00:00"))
					: now);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		communityReceivables.setReceivedAmount(
				StringUtils.isNotEmpty(req.getReceivedAmount()) ? new BigDecimal(req.getReceivedAmount())
						: BigDecimal.ZERO);
		communityReceivables.setSourceNotes(req.getSourceNotes());
		try {
			communityReceivables.setStartTime(
					StringUtils.isNotEmpty(req.getStartTime()) ? DateUtil.parseLongFormat(req.getStartTime()) : now);
		} catch (ParseException e) {
			e.printStackTrace();
		}

		communityReceivables.setEstate(communityEstateEntity);
		communityReceivables.setPayItem(communityPayItemsEntity);
		communityReceivablesDao.save(communityReceivables);
		event.getReceivablesList().add(communityReceivables);

		CommunityCache.putReceivableCache(communityReceivables, CommunityCache.receivableCalEstateList);

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}
	
	public IResponse updateYunzhijiaParameter() {
		GenericResponse res = new GenericResponse();
		if(null == parameterMap ) {
			parameterMap = new HashMap<>();
		}
		getParameter();
		if(parameterMap.containsKey("retInfo")) {
			res.setRet(ResponseContext.RES_NULL_ERROR_INFO);
			res.setRetInfo(parameterMap.get("retInfo"));
			return res;
		}
		String token = getToken("team");
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public String getToken(String scope){
		Map<String,Object> map = null;
		Cache cache = cacheManager.getCache("yunzhijiaTokenCache");
		if(null != cache) {
			map = cache.get(scope,Map.class);
			if(null!= map) {
				Date date = (Date) map.get("expiresTime");
				if(!new Date().before(date)) {
					Map<String, Object> reqMap = getParameterMap(scope);
					reqMap.put("refreshToken", map.get("refreshToken"));
					map = getAccessToken(scope,parameterMap.get("refreshTokenUrl").toString(),reqMap);
					if(!map.containsKey("retInfo")) {
						return map.get("token").toString();
					}else {
						return "";
					}
				}else {
					return map.get("token").toString();
				}
			}else {
				Map<String, Object> reqMap = getParameterMap(scope);
				if(scope.equals("team")) {
					reqMap.put("secret", parameterMap.get("getAccessTokenSecretTeam"));
					reqMap.put("appId", parameterMap.get("appId"));
				}else if(scope.equals("resGroupSecret")) {
					reqMap.put("secret", parameterMap.get("getResGroupSecretTokenSecret"));
				}
				map =  getAccessToken(scope,parameterMap.get("getAccessTokenUrl").toString(),reqMap);
				if(!map.containsKey("retInfo")) {
					return map.get("token").toString();
				}else {
					return "";
				}
			}

		}else {
			Map<String, Object> reqMap = getParameterMap(scope);
			if(scope.equals("team")) {
				reqMap.put("secret", parameterMap.get("getAccessTokenSecretTeam"));
				reqMap.put("appId", parameterMap.get("appId"));
			}else if(scope.equals("resGroupSecret")) {
				reqMap.put("secret", parameterMap.get("getResGroupSecretTokenSecret"));
			}
			map =  getAccessToken(scope,parameterMap.get("getAccessTokenUrl").toString(),reqMap);
			if(!map.containsKey("retInfo")) {
				return map.get("token").toString();
			}else {
				return "";
			}
		}

	}
	
	public Map<String, Object> getParameterMap(String scope) {
		if(null == parameterMap || null!=parameterMap&&parameterMap.containsKey("retInfo")) {
			parameterMap = new HashMap<>();
			getParameter();
		}
		Map<String, Object> map = new HashMap<>();
		map.put("eid", parameterMap.get("eid"));
		map.put("scope", scope);
		map.put("timestamp", System.currentTimeMillis());
		return map;
	}
	
	public void getParameter() {
		parameterMap.remove("retInfo");
		Map<String,String> verifyParameterMap = new HashMap<>();
		verifyParameterMap.put("getAccessTokenUrl", "");
		verifyParameterMap.put("appId", "");
		verifyParameterMap.put("eid", "");
		verifyParameterMap.put("getAccessTokenSecretTeam", "");
		verifyParameterMap.put("refreshTokenUrl", "");
		verifyParameterMap.put("createInstUrl", "");
		verifyParameterMap.put("getResGroupSecretTokenSecret", "");
		verifyParameterMap.put("uploadfileUrl", "");
		List<DictionaryDataEntity> dictionaryDataList = dictionaryDataDao
				.getListByHql("select distinct a from DictionaryDataEntity a "
						+ " inner join a.dictionary b where b.directoryCode='yunzhijia'  and a.state=1", "");
		if (null != dictionaryDataList && dictionaryDataList.size() > 0) {
			dictionaryDataList.forEach(o->{
				switch(o.getDataName()) {
				case "getAccessTokenUrl":
					parameterMap.put("getAccessTokenUrl", o.getDataKey());
					verifyParameterMap.remove("getAccessTokenUrl");
					break;
				case "appId":
					parameterMap.put("appId", o.getDataKey());
					verifyParameterMap.remove("appId", "");
					break;
				case "eid":
					parameterMap.put("eid", o.getDataKey());
					verifyParameterMap.remove("eid", "");
					break;
				case "getAccessTokenSecretTeam":
					parameterMap.put("getAccessTokenSecretTeam", o.getDataKey());
					verifyParameterMap.remove("getAccessTokenSecretTeam", "");
					break;
				case "refreshTokenUrl":
					parameterMap.put("refreshTokenUrl", o.getDataKey());
					verifyParameterMap.remove("refreshTokenUrl", "");
					break;
				case "createInstUrl":
					parameterMap.put("createInstUrl", o.getDataKey());
					verifyParameterMap.remove("createInstUrl", "");
					break;
				case "getResGroupSecretTokenSecret":
					parameterMap.put("getResGroupSecretTokenSecret", o.getDataKey());
					verifyParameterMap.remove("getResGroupSecretTokenSecret", "");
					break;
				case "uploadfileUrl":
					parameterMap.put("uploadfileUrl", o.getDataKey());
					verifyParameterMap.remove("uploadfileUrl", "");
					break;
				default:
					break;	
				}
			});
		} else {
			String retInfo = "请在字典里配置云之家相关参数！";
			parameterMap.put("retInfo", retInfo);
		}
		if(verifyParameterMap.size()>0) {
			for(String key : verifyParameterMap.keySet()) {
				String retInfo = "请在字典里配置云之家"+key+"参数！";
				parameterMap.put("retInfo", retInfo);
				break;
			}
		}
	}
	
	
	
	public Map<String,Object> getAccessToken(String scope,String url,Map<String, Object> map) {
		String retInfo="";
		Cache cache = cacheManager.getCache("yunzhijiaTokenCache");
		Map<String,Object> tokenMap = new HashMap<>();
		if(null == parameterMap || null!=parameterMap&&parameterMap.containsKey("retInfo")) {
			parameterMap = new HashMap<>();
			getParameter();
			if(parameterMap.containsKey("retInfo")) {
				tokenMap.put("retInfo", parameterMap.get("retInfo"));
				return tokenMap;
			}
		}
        try {
			String responseStr = HttpClientUtil.post(url,map);
			JSONObject responseJson = new JSONObject(responseStr);
			if(responseJson.has("success") && JsonUtil.getJSONNodeByPath(responseJson,"success")
					.toLowerCase().equals("true")){
				try {
					String expireIn =  JsonUtil.getJSONNodeByPath(responseJson,"/data/expireIn");
			        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			        LocalDateTime ldt = LocalDateTime.parse(DateUtil.format(new Date(),1),dtf);
			        ldt=ldt.plusSeconds(StringUtils.isNotEmpty(expireIn)&&Integer.valueOf(expireIn)>400 ? (Integer.valueOf(expireIn)-400):0); 
			        DateTimeFormatter fa = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			        String datetime2 = ldt.format(fa);
			        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			            Date expiresTime = sdf.parse( datetime2);
			            tokenMap.put("expiresTime", expiresTime);
					String token = JsonUtil.getJSONNodeByPath(responseJson,"/data/accessToken");
					tokenMap.put("token", token);
					tokenMap.put("refreshToken", JsonUtil.getJSONNodeByPath(responseJson,"/data/refreshToken"));
					cache.put(scope, tokenMap);
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}else if(responseJson.has("success") && 
					JsonUtil.getJSONNodeByPath(responseJson,"success")
					.toLowerCase().equals("false")){
				retInfo=JsonUtil.getJSONNodeByPath(responseJson,"/error");
				tokenMap.put("retInfo", retInfo);
				return tokenMap;
			}else {
				retInfo="获取TOKEN异常！";
				tokenMap.put("retInfo", retInfo);
				return tokenMap;
			}
		}  catch (Exception e) {
			e.printStackTrace();
		}
        return tokenMap;
	}

	
	
	public static String formUpload(String urlStr, Map<String, String> textMap, Map<String, String> fileMap,
			String contentType,String token) {
		String res = "";
		HttpURLConnection conn = null;
		// boundary就是request头和上传文件内容的分隔符
		String BOUNDARY = "---------------------------123821742118716";
		try {
			URL url = new URL(urlStr);
			conn = (HttpURLConnection) url.openConnection();
			conn.setConnectTimeout(72000);
			conn.setReadTimeout(72000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Connection", "Keep-Alive");
			conn.setRequestProperty("x-accessToken", token);
			conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
			OutputStream out = new DataOutputStream(conn.getOutputStream());
			// text
			if (textMap != null) {
				StringBuffer strBuf = new StringBuffer();
				Iterator iter = textMap.entrySet().iterator();
				while (iter.hasNext()) {
					Map.Entry entry = (Map.Entry) iter.next();
					String inputName = (String) entry.getKey();
					String inputValue = (String) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
					strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"\r\n\r\n");
					strBuf.append(inputValue);
				}
				out.write(strBuf.toString().getBytes());
			}
			// file
			if (fileMap != null) {
				Iterator iter = fileMap.entrySet().iterator();
				while (iter.hasNext()) {
					Map.Entry entry = (Map.Entry) iter.next();
					String inputName = (String) entry.getKey();
					String inputValue = (String) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					File file = new File(inputValue);
					String filename = file.getName();

					// 没有传入文件类型，同时根据文件获取不到类型，默认采用application/octet-stream
					contentType = new MimetypesFileTypeMap().getContentType(file);
					// contentType非空采用filename匹配默认的图片类型
					if (!"".equals(contentType)) {
						if (filename.endsWith(".png")) {
							contentType = "image/png";
						} else if (filename.endsWith(".jpg") || filename.endsWith(".jpeg")
								|| filename.endsWith(".jpe")) {
							contentType = "image/jpeg";
						} else if (filename.endsWith(".gif")) {
							contentType = "image/gif";
						} else if (filename.endsWith(".ico")) {
							contentType = "image/image/x-icon";
						}else if(filename.endsWith(".tar")) {
							contentType = "application/x-tar";
						}
					}
					if (contentType == null || "".equals(contentType)) {
						contentType = "application/octet-stream";
					}
					StringBuffer strBuf = new StringBuffer();
					strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
					strBuf.append("Content-Disposition: form-data; name=\"" + inputName + "\"; filename=\"" + filename
							+ "\"\r\n");
					strBuf.append("Content-Type:" + contentType + "\r\n\r\n");
					out.write(strBuf.toString().getBytes());
					DataInputStream in = new DataInputStream(new FileInputStream(file));
					int bytes = 0;
					byte[] bufferOut = new byte[1024];
					while ((bytes = in.read(bufferOut)) != -1) {
						out.write(bufferOut, 0, bytes);
					}
					in.close();
				}
			}
			byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
			out.write(endData);
			out.flush();
			out.close();
			// 读取返回数据
			StringBuffer strBuf = new StringBuffer();
			BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			String line = null;
			while ((line = reader.readLine()) != null) {
				strBuf.append(line).append("\n");
			}
			res = strBuf.toString();
			reader.close();
			reader = null;
		} catch (Exception e) {
			System.out.println("发送POST请求出错。" + urlStr);
			e.printStackTrace();
		} finally {
			if (conn != null) {
				conn.disconnect();
				conn = null;
			}
		}
		return res;
	}

	
	public IResponse generateConstructionPermit(CommunityEventsReq req ,CommunityEventsEntity event) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		if(null == event) {
			if(null==req.getEventsId()) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}
			event = communityEventsDao.get(req.getEventsId());
			if(null==event) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			event.setEventState(8);
		}else {
			if(event.getEventState()!=8) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo("只有在“付款”后的申请才能生成！");
				return res;
			}
		}
		String wxPagePath="";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
		Date date = new Date();
		String today = sdf.format(date);
		List<DictionaryDataEntity> wxDictionaryDataList = dictionaryDataDao
				.getListByHql("select distinct a from DictionaryDataEntity a "
						+ " inner join a.dictionary b where b.directoryCode='appletPagePath' and a.dataName='wxInspectionRecordPath' and a.state=1", "");
		if (null != wxDictionaryDataList && wxDictionaryDataList.size() > 0) {
			wxPagePath = wxDictionaryDataList.get(0).getDataKey();
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("请在数据字典里配置“微信交款通知单页面路径”,directoryCode为\"appletPagePath\"和dataName为\"wxInspectionRecordPath\"！");
			return res;
		}
//		WxParameterEntity wxParameter = new WxParameterEntity();
//		String key="";
//		try {
//			wxParameter.setDataValue("{\"eventsId\":\""+event.getId()+"\",\"type\":\"3\"}");
//			wxParameter.setScene(0);
//			wxParameterDao.save(wxParameter);
//			key = DigestUtil.sm3Digest(System.currentTimeMillis() + wxParameter.getId().toString()).substring(0,32);
//		} catch (Exception e1) {
//			e1.printStackTrace();
//		}
//		wxParameter.setKeyName(key);
		String dir = contextInfo.getAssetFilePath()+ File.separator +"images"+ File.separator + today;
		String url = contextInfo.getAssetFileUrl()+"/"+"images"+ "/" + today + "/";
		// 生成微信小程序 
		if(StringUtils.isNotEmpty(wxPagePath)) {
			BufferedImage image = null;
			try {
				// 从缓存中获取小程序accessToken
				IWeiXinApiService weiXinApiService = (IWeiXinApiService) SpringHandler.getBean("weiXinApiService");
				//String accessToken = weiXinApiService.getAccessToken(1);
				String accessToken = weiXinApiService.getAccessToken(1,"phmj");
				// 生成小程序码，sence和page参数需要与小程序前端研发对接
//				byte[] result = WeiXinApiUtil.getWxaCodeUnlimit(key, wxPagePath,
//						accessToken);
				byte[] result = WeiXinApiUtil.getWxaCodeUnlimit("eventsId="+event.getId(), wxPagePath,
						accessToken);
				if (result.length < 1024) {
					String errmsg = new String(result);
					logger.error("获取微信小程序码失败！{}", errmsg);
					String retMsg = null;
					if(errmsg.contains("40001")) {
						logger.error("accessToken已经失效，重新访问微信服务器刷新aceessToken!");
						weiXinApiService.getAccessToken(1,"phmj", true);
						retMsg = "微信服务器凭证失效，系统已经尝试恢复，请重新操作一次！";
					}
					else {
						retMsg = "获取微信小程序码失败，请检查微信相关配置或稍后再试！";
					}
//					wxParameterDao.delete(wxParameter);
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo(retMsg);
					return res;
				} else {
					ByteArrayInputStream bais = new ByteArrayInputStream(result);
					image = ImageIO.read(bais);
				}
				
		        String transName = String.valueOf(DigestUtil.getMD5Str(System.currentTimeMillis()+""));
		        String newFileName=transName+".PNG";
				String newFile= dir + File.separator+newFileName;
				//避免有问题的图片造成机顶盒重启问题
				//modifyImageFormat(file.getInputStream(),newFile,formatName);
				if(isOSLinux()){
					Runtime.getRuntime().exec("chmod 644 " + newFile);
				}
		        
            	try {
            		ImageIO.write(image, "png", new FileOutputStream(newFile));
            	} catch (FileNotFoundException e) {
            		// TODO Auto-generated catch block
            		e.printStackTrace();
            	} catch (IOException e) {
            		// TODO Auto-generated catch block
            		e.printStackTrace();
            	}

				url = url  +newFileName;
		        AssetEntity asset = new AssetEntity();
		        asset.setAssetState(EntityContext.ASSET_STATE_UP);
		        asset.setImageFile(url);
				asset.setAssetCode(transName);
				asset.setAssetName(newFileName);
				asset.setAssetType(0);
				asset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_INVALID);
				asset.setIsCover(EntityContext.IS_COVER);
				asset.setSummaryShort("");
				assetDao.save(asset);
				event.setQrCode(asset);
			} catch (Exception e1) {

				e1.printStackTrace();
			}
		}
		//constructionPermit.ftl
		Map<String, Object> params = new HashMap<String, Object>();
		CommunityEstateEntity estate = (CommunityEstateEntity) event.getProperty();
		String[] uc = estate.getUnitCode().split("-");
		if(uc.length==3) {
			for(int i=0;i<uc.length;i++) {
				switch(i) {
				case 0:
					params.put("districtName", uc[i]);
					break;
				case 1:
					params.put("buildingName", uc[i]);
					break;
				case 2:
					params.put("roomNumber", uc[i]);
					break;
				default:
					break;	
				}
			}
		}else {
			params.put("districtName", "");
			params.put("buildingName", "");
		}
		params.put("unitCode", estate.getUnitCode());
		List<Object> itemsdetailList = new ArrayList<Object>();
		try {
			if(StringUtils.isNoneEmpty(event.getItemsdetail())) {
				itemsdetailList =mapper.readValue(event.getItemsdetail(), ArrayList.class);
				for(Object o :itemsdetailList) {
					Map<String,Object> m = (Map)o;
					for(String k :m.keySet()){
						if(k.equals("basicInformation")) {
							List<Map<String,String>> list = (List<Map<String, String>>) m.get(k);
							for(Map<String,String> m1:list) {
								if(m1.get("itemkey").equals("name")) {
									params.put("name", m1.get("value"));
								}else if(m1.get("itemkey").equals("phone")) {
									params.put("phone", m1.get("value"));
								}else if(m1.get("itemkey").equals("companyName")) {
									params.put("companyName", m1.get("value"));
								}
							}
						}
					}
				}
			}
		} catch (JsonParseException e) {
			e.printStackTrace();
		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		
		Map<String,String> cententMap = new HashMap<String,String>();
		try {
			if(StringUtils.isNoneEmpty(event.getCentent())&&event.getCentent().contains("itemName")) {
				cententMap =mapper.readValue(event.getCentent(), Map.class);
				if(cententMap.containsKey("itemName")) {
					params.put("centent", cententMap.get("itemName"));
				}else {
					params.put("centent", event.getCentent());
				}
			}else {
				params.put("centent", event.getCentent());
			}
		} catch (JsonParseException e) {
			e.printStackTrace();
		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		
		if(null!=event.getStartTime()) {
			String startTime = DateUtil.formatShortFormat(event.getStartTime());
			String[] dt = startTime.split("-");
			params.put("startYear", dt[0]);
			params.put("startMonth", dt[1]);
			params.put("startDay", dt[2]);
		}
		if(null!=event.getEndTime()) {
			String endTime = DateUtil.formatShortFormat(event.getEndTime());
			String[] dt = endTime.split("-");
			params.put("endYear", dt[0]);
			params.put("endMonth", dt[1]);
			params.put("endDay", dt[2]);
		}
		String time = DateUtil.formatShortFormat(new Date());
		String[] dt = time.split("-");
		params.put("createTimeYear", dt[0]);
		params.put("createTimeMonth", dt[1]);
		params.put("createTimeDay", dt[2]);
		
		
//		String dir = contextInfo.getAssetFilePath() + File.separator + "constructionPermit" + File.separator + today
//				+ File.separator;
		createMultilayerFile(dir);
		File templateFile = new File(
				contextInfo.getAssetFilePath() + File.separator + "constructionPermit.png");
		if (!templateFile.exists()) {
			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			res.setRetInfo("模板不存在，请重新上传模板！");
			return res;
		}

		// 使用ImageIO读取本地图片
        BufferedImage bi=null;
		try {
			bi = ImageIO.read(templateFile);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        // 确认图片已成功读取
        if (bi != null) {
        	//得到它的绘制环境，也就是画笔
        	Graphics2D g2=(Graphics2D)bi.getGraphics();
        	g2.setFont(new Font("黑体",Font.PLAIN,18));//设置字体
        	g2.setColor(Color.BLACK);//设置颜色
        	g2.drawString(params.containsKey("districtName") ?  (String)params.get("districtName") :"", 50, 78);
        	g2.drawString(params.containsKey("buildingName") ? (String) params.get("buildingName") :"", 140, 78);
        	//g2.drawString("5-1-0202", 200, 78);
        	g2.drawString(params.containsKey("roomNumber") ? (String) params.get("roomNumber") :"", 230, 78);
        	
        	g2.setFont(new Font("黑体",Font.PLAIN,15));//设置字体
        	g2.drawString(params.containsKey("unitCode") ? (String) params.get("unitCode") :"", 930, 77);
        	
        	g2.setFont(new Font("黑体",Font.PLAIN,18));
        	g2.drawString(params.containsKey("companyName") ? (String)params.get("companyName") :"", 170, 106);
        	g2.drawString(params.containsKey("name") ? (String)params.get("name") :"", 420, 106);
        	g2.drawString(params.containsKey("phone") ? (String)params.get("phone") :"", 640, 106);
        	
        	
        	g2.drawString(params.containsKey("centent") ? (String)params.get("centent") :"", 160, 135);
        	
        	g2.setFont(new Font("黑体",Font.PLAIN,15));//设置字体
        	g2.drawString(params.containsKey("startYear") ? (String)params.get("startYear") :"", 115, 605);
        	g2.drawString(params.containsKey("startMonth") ? (String)params.get("startMonth") :"", 185, 605);
        	g2.drawString(params.containsKey("startDay") ? (String)params.get("startDay") :"", 240, 605);
        	
        	g2.drawString(params.containsKey("endYear") ? (String)params.get("endYear") :"", 325, 605);
        	g2.drawString(params.containsKey("endMonth") ? (String)params.get("endMonth") :"", 420, 605);
        	g2.drawString(params.containsKey("endDay") ? (String)params.get("endDay") :"", 490, 605);
        	
        	g2.setFont(new Font("黑体",Font.PLAIN,13));//设置字体
        	g2.drawString((String)params.get("createTimeYear"), 955, 685);
        	g2.drawString((String)params.get("createTimeMonth"), 1000, 685);
        	g2.drawString((String)params.get("createTimeDay"), 1030, 685);
        	
        	
			
	        String transName = String.valueOf(DigestUtil.getMD5Str(System.currentTimeMillis()+""));
	        String newFileName=transName+".PNG";
			String newFile= dir + File.separator+newFileName;
			//避免有问题的图片造成机顶盒重启问题
			//modifyImageFormat(file.getInputStream(),newFile,formatName);

        	try {
    			if(isOSLinux()){
    				Runtime.getRuntime().exec("chmod 644 " + newFile);
    			}
        		ImageIO.write(bi, "png", new FileOutputStream(newFile));
		        AssetEntity asset = new AssetEntity();
		        url = contextInfo.getAssetFileUrl()+"/"+"images"+ "/" + today + "/";
		        url = url  +newFileName;
		        asset.setAssetState(EntityContext.ASSET_STATE_UP);
		        asset.setImageFile(url);
				asset.setAssetCode(transName);
				asset.setAssetName(newFileName);
				asset.setAssetType(0);
				asset.setPackageFlag(EntityContext.ASSET_PACKAGE_FLAG_INVALID);
				asset.setIsCover(EntityContext.IS_COVER);
				asset.setSummaryShort("");
				assetDao.save(asset);
				event.setConstructionPermit(asset);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        	} catch (FileNotFoundException e) {
        		// TODO Auto-generated catch block
        		e.printStackTrace();
        	} catch (IOException e) {
        		// TODO Auto-generated catch block
        		e.printStackTrace();
        	}

        } else {
            System.out.println("图片转换失败，文件可能不是有效的图片格式或路径错误。");
        }

		return res;
	}
	
	private static boolean createMultilayerFile(String dir) {
		try {
			File dirPath = new File(dir);
			if (!dirPath.exists()) {
				dirPath.mkdirs();
			}
		} catch (Exception e) {
			System.out.println("创建多层目录操作出错: " + e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	public void saveAsset() {
		
	}
	public  boolean isOSLinux() {
		Properties prop = System.getProperties();
		String os = prop.getProperty("os.name");
		boolean statu = false;
		if (os != null && os.toLowerCase().indexOf("linux") > -1) {
			statu=true;
		} 
		return statu;
	}
	
	
//	public static void main(String[] args) {
//	     try {
//	            // 图片文件路径
//	            String imagePath = "E:\\file\\111.png";
//	            
//	            // 使用ImageIO读取本地图片
//	            BufferedImage bi = ImageIO.read(new File(imagePath));
//	            // 确认图片已成功读取
//	            if (bi != null) {
//	            	//得到它的绘制环境，也就是画笔
//	            	Graphics2D g2=(Graphics2D)bi.getGraphics();
//	            	g2.setFont(new Font("黑体",Font.PLAIN,18));//设置字体
//	            	g2.setColor(Color.BLACK);//设置颜色
//	            	g2.drawString("5", 50, 78);
//	            	g2.drawString("1", 140, 78);
//	            	//g2.drawString("5-1-0202", 200, 78);
//	            	g2.drawString("202", 230, 78);
//	            	
//	            	g2.setFont(new Font("黑体",Font.PLAIN,15));//设置字体
//	            	g2.drawString("5-1-0202", 930, 77);
//	            	
//	            	g2.setFont(new Font("黑体",Font.PLAIN,18));
//	            	g2.drawString("自装", 170, 106);
//	            	g2.drawString("李焕清", 420, 106);
//	            	g2.drawString("19200101001", 640, 106);
//	            	
//	            	
//	            	g2.drawString("1、阳光房封窗、拆墙；2、安装衣柜；3、安装空调；4、安装纱窗；5、安装防盗网；", 160, 135);
//	            	
//	            	g2.setFont(new Font("黑体",Font.PLAIN,15));//设置字体
//	            	g2.drawString("2024", 115, 605);
//	            	g2.drawString("10", 185, 605);
//	            	g2.drawString("01", 240, 605);
//	            	
//	            	g2.drawString("2024", 325, 605);
//	            	g2.drawString("12", 420, 605);
//	            	g2.drawString("31", 490, 605);
//	            	
//	            	g2.setFont(new Font("黑体",Font.PLAIN,13));//设置字体
//	            	g2.drawString("2024", 955, 685);
//	            	g2.drawString("9", 1005, 685);
//	            	g2.drawString("30", 1030, 685);
//	            	
//	            	try {
//	            		ImageIO.write(bi, "png", new FileOutputStream("E:\\file\\111_1.png"));
//	            	} catch (FileNotFoundException e) {
//	            	// TODO Auto-generated catch block
//	            	e.printStackTrace();
//	            	} catch (IOException e) {
//	            	// TODO Auto-generated catch block
//	            	e.printStackTrace();
//	            	}
//
//	            } else {
//	                System.out.println("图片转换失败，文件可能不是有效的图片格式或路径错误。");
//	            }
//	        } catch (IOException e) {
//	            e.printStackTrace();
//	        }
//		
//	}
	
	@Audit(operate = "部门验收装修申请项目")
	public IResponse departmentAudit(CommunityEventsReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getEventsId() && null!=req.getDepartment() && null!=req.getDepartmentAuditState()) {
			CommunityEventsEntity event = communityEventsDao.get(req.getEventsId());
			if (null != event) {
				if(null!=req.getDepartment()) {
					switch(req.getDepartment()) {
					case 0:
						setDepartmentAuditState(res,1,req,event);
						return res;
					case 1:
						setDepartmentAuditState(res,2,req,event);
						return res;
					case 2:
						setDepartmentAuditState(res,3,req,event);
						return res;
					case 3:
						setDepartmentAuditState(res,4,req,event);
						return res;
					default:
						break;	
					}
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	public GenericResponse setDepartmentAuditState(GenericResponse res,int index,CommunityEventsReq req,CommunityEventsEntity event) {
//		String state = event.getCheckAcceptAuditState().substring(event.getCheckAcceptAuditState().length()-index-1, 
//				index);
//		if((StringUtils.isEmpty(state) || !state.equals("1")) && event.getEventState()==10) {
//			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
//			return res;
//		}
		Map<String,String> map = new HashMap<>();
		StringBuilder checkAcceptAuditState = new StringBuilder();
		String auditOpinion = StringUtils.isNotEmpty(req.getAuditOpinion()) ? req.getAuditOpinion() : "无;";
		JSONObject json = null; 
		List<OperationDiaryForm> diaryFormList = null;
		if(StringUtils.isNoneEmpty(event.getDiary())) {
			json = new JSONObject(event.getDiary());
			try {
				diaryFormList =mapper.readValue(json.get("diary").toString(), ArrayList.class);
			} catch (JSONException | IOException e) {
				logger.info("json转对象出现错误：{}",e);
			}

        }else {
			json = new JSONObject();
			diaryFormList = new ArrayList<OperationDiaryForm>();
		}
		try {
			map = mapper.readValue(event.getCheckAcceptItems(), Map.class);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		Object userObj = getPrincipal(true);
		OperationDiaryForm diaryForm = null;
		//event.setCheckAcceptAuditState(event.getCheckAcceptAuditState());
		if(event.getEventState() == 10 && userObj instanceof PlatformUserEntity ) {//审核通过，如果其它除装修其它流程审批也需要二级审核，需要把状态2去掉: if(req.getEventState() == 14 && userObj instanceof PlatformUserEntity )
			PlatformUserEntity user = (PlatformUserEntity) userObj;
			if(index==1) {
				checkAcceptAuditState.append(req.getDepartmentAuditState());
				checkAcceptAuditState.append(event.getCheckAcceptAuditState().
						substring(index, event.getCheckAcceptAuditState().length()));
				map.put("engineeringDept",StringUtils.isNotEmpty(req.getCheckAcceptItems()) ? 
						req.getCheckAcceptItems() :map.get("engineeringDept"));
				if(2!=req.getDepartmentAuditState()) {
					event.setEventState(11);
					diaryForm = new OperationDiaryForm("工程验收",
							"验收不通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
				}else {
					diaryForm = new OperationDiaryForm("工程验收",
							"验收通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
				}

				event.setLastModifyTime(new Timestamp(new Date().getTime()));
				event.setCheckAcceptAuditState(checkAcceptAuditState.toString());
			} else if(index==2) {
				checkAcceptAuditState.append(event.getCheckAcceptAuditState().
						substring(0, 1));
				checkAcceptAuditState.append(req.getDepartmentAuditState());
				checkAcceptAuditState.append(event.getCheckAcceptAuditState().
							substring(2, event.getCheckAcceptAuditState().length()));
				map.put("customerServiceDept",StringUtils.isNotEmpty(req.getCheckAcceptItems()) ? 
						req.getCheckAcceptItems() :map.get("customerServiceDept"));
				if(2!=req.getDepartmentAuditState()) {
					event.setEventState(11);
					diaryForm = new OperationDiaryForm("客服验收",
							"验收不通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
				}else {
					diaryForm = new OperationDiaryForm("客服验收",
							"验收通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
				}
				event.setLastModifyTime(new Timestamp(new Date().getTime()));
				event.setCheckAcceptAuditState(checkAcceptAuditState.toString());
			}else if(index==3){
				checkAcceptAuditState.append(event.getCheckAcceptAuditState().
						substring(0, 2));
				checkAcceptAuditState.append(req.getDepartmentAuditState());
				checkAcceptAuditState.append(event.getCheckAcceptAuditState().
							substring(3, event.getCheckAcceptAuditState().length()));
				map.put("environmentDept",StringUtils.isNotEmpty(req.getCheckAcceptItems()) ? 
						req.getCheckAcceptItems() :map.get("environmentDept"));
				if(2!=req.getDepartmentAuditState()) {
					event.setEventState(11);
					diaryForm = new OperationDiaryForm("环境验收",
							"验收不通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
				}else {
					diaryForm = new OperationDiaryForm("环境验收",
							"验收通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
				}
				event.setLastModifyTime(new Timestamp(new Date().getTime()));
				event.setCheckAcceptAuditState(checkAcceptAuditState.toString());
			} else if(index==4) {
				checkAcceptAuditState.append(event.getCheckAcceptAuditState().
						substring(0, index-1));
				checkAcceptAuditState.append(req.getDepartmentAuditState());
				switch(req.getDepartmentAuditState()) {
				case 2:
					event.setEventState(12);
					event.setCheckAcceptAuditState(checkAcceptAuditState.toString());
					diaryForm = new OperationDiaryForm("总经办验收",
							"验收通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
					if(event.getHaveCashPledge()==0) {
						event.setEventState(5);
					}
					break;
				case 3:
					event.setCheckAcceptAuditState("1110");
					diaryForm = new OperationDiaryForm("总经办验收",
							"验收工程、环境和客服不通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
					break;
				case 4:
					event.setCheckAcceptAuditState("1220");
					diaryForm = new OperationDiaryForm("总经办验收",
							"验收工程不通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
					break;
				case 5:
					event.setCheckAcceptAuditState("2120");
					diaryForm = new OperationDiaryForm("总经办验收",
							"验收客服不通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
					break;
				case 6:
					event.setCheckAcceptAuditState("2210");
					diaryForm = new OperationDiaryForm("总经办验收",
							"验收环境不通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
					break;
				case 7:
					event.setCheckAcceptAuditState("1120");
					diaryForm = new OperationDiaryForm("总经办验收",
							"验收工程和客服不通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
					break;
				case 8:
					event.setCheckAcceptAuditState("1210");
					diaryForm = new OperationDiaryForm("总经办验收",
							"验收工程和环境不通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
					break;
				case 9:
					event.setCheckAcceptAuditState("2110");
					diaryForm = new OperationDiaryForm("总经办验收",
							"验收客服和环境不通过！审核意见：" + auditOpinion, DateUtil.formatLongFormat(new Date()), user.getName(), user.getId(), "U");
					break;
				default:

					break;	
				}


				
				event.setLastModifyTime(new Timestamp(new Date().getTime()));
				event.setAuditOpinion(auditOpinion);
			}
			diaryFormList.add(diaryForm);
			event.setDiary(json.put("diary", diaryFormList).toString());
			
	//		checkAcceptAuditState.delete(0, checkAcceptAuditState.length());
	//		checkAcceptAuditState.append(event.getCheckAcceptAuditState().
	//					substring(0, 3));
	//		if(checkAcceptAuditState.toString().equals("2220")) {
	//			event.setCheckAcceptAuditState("2221");
	//		
			if(index!=4) {
				boolean auditState=true;
				for(int i=0;i<3;i++) {
					String str =checkAcceptAuditState.substring(i, i+1);
					if(str.equals("1") || str.equals("0")|| str.equals("3")) {
						auditState = false;
					}
				}
				if(auditState) {
					checkAcceptAuditState.delete(0, checkAcceptAuditState.length());
					checkAcceptAuditState.append(event.getCheckAcceptAuditState().
							substring(0, 3));
					checkAcceptAuditState.append("1");
					event.setCheckAcceptAuditState(checkAcceptAuditState.toString());
					event.setLastModifyTime(new Timestamp(new Date().getTime()));
				}
				try {
					event.setCheckAcceptItems(mapper.writeValueAsString(map));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
		}
		
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public IResponse exportEventsRefund(CommunityEventsReq req, HttpServletResponse response)  {
		ExportExcelRes res = new ExportExcelRes();
		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("orderCode", "编码");
		heardMap.put("unitCode", "单元");
		heardMap.put("userName", "姓名");
		heardMap.put("phone", "联系电话");
		heardMap.put("centent", "申请项目");
		heardMap.put("haveCashPledge", "是否存在押金");
		heardMap.put("lastModifyTime", "申请退款时间");
		heardMap.put("refundMethod", "退款方式");
		heardMap.put("bankName", "银行名称");
		heardMap.put("bankAccount", "银行号码");
		heardMap.put("accountName", "开户人");

		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();

		Map<Integer, MeterRecordVo> resultList = new TreeMap<>();
		req.setEventStateList("13");
		req.setEventType(1);
		String hql = queryCondition(req);
		List<CommunityEventsEntity> list = communityEventsDao.getListByHql(hql, "");

		list.forEach(o -> {
			Map<String, Object> map = new HashMap<String, Object>();
			CommunityEstateEntity property = (CommunityEstateEntity) o.getProperty();
			map.put("orderCode", StringUtils.isNotEmpty(o.getOrderCode()) ? o.getOrderCode():"");
			map.put("unitCode", property.getUnitCode());
			map.put("userName",o.getMember().getUserName());
			map.put("phone", o.getMember().getPhone());
			map.put("lastModifyTime", null != o.getLastModifyTime() ? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
			Map<String,String> cententMap = new HashMap<String,String>();
			switch(o.getHaveCashPledge()) {
			case 0:
				map.put("haveCashPledge", "无押金");break;
			case 1:
				map.put("haveCashPledge", "押金未退");break;
			case 2:
				map.put("haveCashPledge", "已退押金");break;
			default:
				map.put("haveCashPledge", "");break;	
			}
			switch(o.getRefundMethod()) {
			case 0:
				map.put("refundMethod", "抵扣物业费");break;
			case 1:
				map.put("refundMethod", "现金退回");break;
			case 2:
				map.put("refundMethod", "银行卡");break;
			default:
				map.put("refundMethod", "");break;	
			}
			try {
				if(StringUtils.isNoneEmpty(o.getCentent())&&o.getCentent().contains("itemName")) {
					cententMap =mapper.readValue(o.getCentent(), Map.class);
					if(cententMap.containsKey("itemName")) {
						map.put("centent", cententMap.get("itemName"));
					}else {
						map.put("centent", o.getCentent());
					}
				}else {
					map.put("centent", o.getCentent());
				}
				if (StringUtils.isNotEmpty(o.getRefundInfo())) {
					RefundInfoVo refundInfoVo = mapper.readValue(o.getRefundInfo(), RefundInfoVo.class);
					map.put("accountName",refundInfoVo.getAccountName());
					map.put("bankAccount",refundInfoVo.getBankAccount());
					map.put("bankName",refundInfoVo.getBankName());
				}else {
					map.put("accountName","");
					map.put("bankAccount","");
					map.put("bankName","");
				}
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JSONException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			dataList.add(map);
		});

		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

		return res;
	}
	
	
	
	@Audit(operate = "导入已完成退款的装修申请")
	public IResponse importEventsRefund(HttpServletRequest request) {
		try {
			return eventsRefundHandler(uploadFile(request));
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	

	@SuppressWarnings({ "resource", "unused" })
	private GenericResponse eventsRefundHandler(File excelFile) throws IOException {
		GenericResponse res  = new GenericResponse();
		FileInputStream inputStream = new FileInputStream(excelFile);
		Workbook workBook = null;
		if (excelFile.getName().endsWith("xls")) {
			workBook = new HSSFWorkbook(inputStream);
		} else if (excelFile.getName().endsWith("xlsx")) {
			workBook = new XSSFWorkbook(inputStream);
		} else {
			logger.info(excelFile + "不是excel类型文件！！！");
			return null;
		}
		Object userObj = getPrincipal(false);

		Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
		StringBuilder info = new StringBuilder();
		int[] resultInt = new int[] { 0 };
		
		sheet.forEach(o -> {
			if (o.getRowNum() > 0) {				
				String orderCode = cell2Str(o.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				if(StringUtils.isNotEmpty(orderCode)){
					CommunityEventsEntity events = communityEventsDao.findUnique(
							"select a from CommunityEventsEntity a where a.orderCode='"+orderCode+"' ", "") ;
					if(null != events && events.getEventState()==13) {
						events.setEventState(5);
						events.setHaveCashPledge(2);
						events.setLastModifyTime(new Timestamp(new Date().getTime()));
					}
				}
			}
		});
		
		res.setRetInfo(StringUtils.isNotEmpty(info.toString()) ? info.toString() : ResponseContext.RES_SUCCESS_CODE_INFO);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);

		return res;
	}
	
	@Override
	@Audit(operate = "设置已完成退款的装修申请")
	public IResponse setEventsRefund(CommunityEventsReq req) {
		GenericResponse res = new GenericResponse();

		if (StringUtils.isNotEmpty(req.getEventsIdList())) {
			String[] eventsIds = req.getEventsIdList().split(",");
			for (String eventsId : eventsIds) {
				CommunityEventsEntity events = communityEventsDao.get(Integer.parseInt(eventsId));
				if(null != events && events.getEventState()==13) {
					events.setEventState(5);
					events.setHaveCashPledge(2);
					events.setLastModifyTime(new Timestamp(new Date().getTime()));
				}
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	private String cell2Str(Cell cell) {

		String str = "";
		if (cell.getCellType() == CellType.STRING) {
			str = StringUtils.isNotEmpty(cell.getStringCellValue()) ? cell.getStringCellValue() :"";
		} else if (cell.getCellType() == CellType.NUMERIC) {
			str = String.valueOf(cell.getNumericCellValue());
			if(str.contains("E10")) {
				DecimalFormat decimalFormat=new DecimalFormat("#");
				str = decimalFormat.format(Double.valueOf(str));
			}
		}else if(cell.getCellType() == CellType.FORMULA){
			str = cell.getCellFormula();
		}else if(cell.getCellType() == CellType.BOOLEAN){
			str=cell.getBooleanCellValue()+"";
		}else if(cell.getCellType() == CellType.ERROR){
			str=cell.getErrorCellValue()+"";
		}else if(cell.getCellType() == CellType.BLANK || cell.getCellType() == CellType. _NONE){
			str="";
		}else {
			//Date date = cell.getDateCellValue();
			//str=DateUtil.format(cell.getDateCellValue(),1);
			str="";
		}
		return str;
	}
	public File uploadFile(HttpServletRequest request) throws IllegalStateException, IOException {
		MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
		MultipartFile file = multiRequest.getFile("file");
		String tmpFile = System.getProperty("java.io.tmpdir");
		//String tmpFile = "F:/temp";
		tmpFile = tmpFile + File.separator + System.currentTimeMillis() + "-"
				+ DigestUtil.getMD5Str(file.getOriginalFilename()) + "-" + CodeUtil.getId(10000)
				+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
		file.transferTo(new File(tmpFile));
		File excelFile = new File(tmpFile);
		
		return excelFile;
	}
	
	public IResponse initializeItemsAttachment(CommunityEventsReq req) {
		GenericResponse res = new GenericResponse();
        if(null!=req.getDecorationItemsId()&&
        		StringUtils.isNotEmpty(req.getAttachmentIdList()) && StringUtils.isNotEmpty(req.getDistrictIdList()) 
        		&& StringUtils.isNotEmpty(req.getEstateTypeList())) {
        	String[] decorationItemsId = req.getDecorationItemsId().split(",");
        	for(String decorationItemId : decorationItemsId) {
        		CommunityDecorationItemsEntity decorationItems = communityDecorationItemsDao.get(Integer.valueOf(decorationItemId));
    			if(null != decorationItems) {
    	        	String[] attachmentIdList = req.getAttachmentIdList().split(",");
    	        	String[] districtIdList =  req.getDistrictIdList().split(",");
    	        	String[] estateTypeList= req.getEstateTypeList().split(",");
    				for (String id : attachmentIdList) {
    					CommunityDecorationAttachmentEntity communityDecorationAttachment = communityDecorationAttachmentDao.get(Integer.valueOf(id)) ;
    					if(null!=communityDecorationAttachment) {
    						if(null!=districtIdList) {
    							for (String districtId : districtIdList) {
    								CommunityDistrictEntity district = communityDistrictDao.get(Integer.valueOf(districtId));
    								if(null != estateTypeList) {
    									for (String estateType : estateTypeList) {
    										CommunityCecorationItemsAttachmentEntity itemsAttachment = new CommunityCecorationItemsAttachmentEntity();
    										itemsAttachment.setAttachment(communityDecorationAttachment);
    										itemsAttachment.setDecorationItems(decorationItems);
    										itemsAttachment.setDistrict(district);
    										itemsAttachment.setEstateType(estateType);
    										communityCecorationItemsAttachmentDao.save(itemsAttachment);
    									}
    								}
    							}
    						}
    					}
    					
    				}
    				res.setRet(ResponseContext.RES_SUCCESS_CODE);
    				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
    			}else {
                    res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
    				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
    			}
        	}
			

        }else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
	}
}
