package com.foshan.service.community.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityMeterRecordEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityMeterForm;
import com.foshan.form.community.CommunityMeterRecordForm;
import com.foshan.form.community.request.CommunityMeterRecordReq;
import com.foshan.form.community.response.communityMeterRecord.AddCommunityMeterRecordRes;
import com.foshan.form.community.response.communityMeterRecord.GetCommunityMeterRecordInfoRes;
import com.foshan.form.community.response.communityMeterRecord.GetCommunityMeterRecordListRes;
import com.foshan.form.community.response.communityMeterRecord.ModifyCommunityMeterRecordRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityMeterRecordService;
import com.foshan.util.DateUtil;

@Transactional
@Service("communityMeterRecordService")
public class CommunityMeterRecordServiceImpl extends GenericCommunityService implements ICommunityMeterRecordService{

	@Override
	public IResponse getCommunityMeterRecordList(CommunityMeterRecordReq req) {
		GetCommunityMeterRecordListRes res = new GetCommunityMeterRecordListRes();
		Page<CommunityMeterRecordEntity> page = new Page<CommunityMeterRecordEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityMeterRecordEntity a inner join a.communityMeter b ");
		hql.append(StringUtils.isNotEmpty(req.getCategoryList())  ? " inner join b.meterAttributes c where 1=1":" where 1=1");
		hql.append(null!=req.getMeterId() ? " and b.id="+req.getMeterId() : "")
			.append(StringUtils.isNotEmpty(req.getMeterName()) ? " and b.meterName like'%"+req.getMeterName()+"%'":"")
			.append(StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime()) ? 
					" and a.recordDate>='"+req.getStartTime()+"' and a.recordDate<='"+req.getEndTime()+"'":"")
			.append(StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isEmpty(req.getEndTime()) ? 
					" and a.recordDate>='"+req.getStartTime()+"' ":"")
			.append(StringUtils.isNotEmpty(req.getMeterCode()) ? " and b.meterCode like'%"+req.getMeterCode()+"%'":"")
			.append(StringUtils.isNotEmpty(req.getCategoryList()) ? " and c.category in("+req.getCategoryList()+")" : "")
			.append(null!=req.getExceptionState() ? " and exceptionState="+req.getExceptionState() :"")
			.append(null!=req.getState() ? " and a.state="+req.getState() : " and a.state="+EntityContext.RECORD_STATE_VALID)
			.append(null!=req.getIsCommon() ? " and b.isCommon="+req.getIsCommon():"")
			.append(StringUtils.isNotEmpty(req.getPropertyIdList()) ? " and b.propertyIdList.id in("+req.getPropertyIdList()+")":"");
		
		hql.append(" ORDER BY a.recordDate DESC,b.meterName ASC");
		page = communityMeterRecordDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityMeterRecordForm communityMeterRecordForm = new CommunityMeterRecordForm();
			communityMeterRecordForm.setCommunityMeterRecordId(o.getId());
            communityMeterRecordForm.setRecordDate(null != o.getRecordDate()? DateUtil.formatLongFormat(o.getRecordDate()) : "");
            communityMeterRecordForm.setIsZero(o.getIsZero());
            communityMeterRecordForm.setOldData(o.getOldData());
            communityMeterRecordForm.setOldId(o.getOldId());
            communityMeterRecordForm.setRecordNum(null!=o.getRecordNum() ? o.getRecordNum().toString() : "");
            communityMeterRecordForm.setRecorder(o.getRecorder());
            communityMeterRecordForm.setLastModifyTime(null != o.getLastModifyTime()? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
            communityMeterRecordForm.setExceptionState(o.getExceptionState());
            communityMeterRecordForm.setExceptionRemark(o.getExceptionRemark());
            communityMeterRecordForm.setAdditionalAmount(null!=o.getAdditionalAmount() ? o.getAdditionalAmount().toString() : "");
            communityMeterRecordForm.setAdditionalInstructions(StringUtils.isNotEmpty(o.getAdditionalInstructions()) 
            		? o.getAdditionalInstructions() :"");
            if(null!=o.getLastRecordId()) {
            	CommunityMeterRecordForm lastMeterRecordForm = new CommunityMeterRecordForm();
            	CommunityMeterRecordEntity lastMeterRecord = communityMeterRecordDao.get(o.getLastRecordId());
            	lastMeterRecordForm.setLastNum(null!=lastMeterRecord ? lastMeterRecord.getRecordNum().toString() : "");
            	lastMeterRecordForm.setLastDate(null != lastMeterRecord.getRecordDate()? DateUtil.formatLongFormat(lastMeterRecord.getRecordDate()) : "");
            	lastMeterRecordForm.setCommunityMeterRecordId(lastMeterRecord.getId());
            	communityMeterRecordForm.setLastRecord(lastMeterRecordForm);
            }
            List<CommunityMeterRecordEntity> meterRecordList = communityMeterRecordDao.getListByHql("select distinct a from CommunityMeterRecordEntity a where a.lastRecordId="+o.getId()+
            		" and  a.state="+EntityContext.RECORD_STATE_VALID);
            communityMeterRecordForm.setHaveSub(null!=meterRecordList&&meterRecordList.size()>0 ? 1 : 0);
            
            if(null != o.getCommunityMeter()) {
            	CommunityMeterForm communityMeterForm = new CommunityMeterForm();
    			communityMeterForm.setCommunityMeterId(o.getCommunityMeter().getId());
                communityMeterForm.setMeterCode(o.getCommunityMeter().getMeterCode());
                communityMeterForm.setMeterName(o.getCommunityMeter().getMeterName());
                if(null!=req.getIsRent() && req.getIsRent()==1) {
    				CommunityEstateEntity estate = (CommunityEstateEntity)o.getCommunityMeter().getPropertyList().get(0);
    				communityMeterForm.setUnitCode(estate.getUnitCode());
                }
                communityMeterRecordForm.setCommunityMeterForm(communityMeterForm);
            }
			res.getCommunityMeterRecordList().add(communityMeterRecordForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "新增抄表记录")
	public IResponse addCommunityMeterRecord(CommunityMeterRecordReq req) {
		AddCommunityMeterRecordRes res = new AddCommunityMeterRecordRes();
		if (null!=req.getMeterId() && null != req.getIsZero()) {
			CommunityMeterRecordEntity communityMeterRecord = new CommunityMeterRecordEntity();
			CommunityMeterEntity meter = communityMeterDao.get(req.getMeterId());
			if(null == meter) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
            try {
                communityMeterRecord.setRecordDate(StringUtils.isNotEmpty(req.getRecordDate()) ? 
                    DateUtil.parseLongFormat(req.getRecordDate()) : null);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            
            StringBuilder info = new StringBuilder();
			CommunityMeterRecordEntity parentMeterRecord = communityMeterRecordDao.getUniqueBySql
					(" SELECT *  FROM `t_community_meter_record` WHERE meterId="+req.getMeterId()+
							" AND state="+EntityContext.RECORD_STATE_VALID+" ORDER BY recordDate DESC LIMIT 0,1" , "");
            BigDecimal num = StringUtils.isNotEmpty(req.getRecordNum()) ?  new BigDecimal(req.getRecordNum()) : new BigDecimal(0);
			if(null != parentMeterRecord){
				communityMeterRecord.setLastRecordId(parentMeterRecord.getId());
				if(num.compareTo(parentMeterRecord.getRecordNum()) == 1) {
					BigDecimal difference = num.subtract(parentMeterRecord.getRecordNum());
					if(difference.divide(parentMeterRecord.getRecordNum(),2,  RoundingMode.HALF_UP).compareTo(new BigDecimal(0.1)) == 1) {
						info.append("【表(编号："+meter.getMeterCode()+"，名称："+meter.getMeterName()+")异常；相比上个月数据超出10%以上！】");
						communityMeterRecord.setExceptionState(1);
						communityMeterRecord.setExceptionRemark(info.toString());
					}
				}else if(num.compareTo(parentMeterRecord.getRecordNum()) == -1) {
					BigDecimal difference = parentMeterRecord.getRecordNum().subtract(num);
					if(difference.divide(parentMeterRecord.getRecordNum(),2,  RoundingMode.HALF_UP).compareTo(new BigDecimal(0.1)) == 1) {
						info.append("【表(编号："+meter.getMeterCode()+"，名称："+meter.getMeterName()+")异常；相比上个月数据减少10%以下！】");
						communityMeterRecord.setExceptionState(1);
						communityMeterRecord.setExceptionRemark(info.toString());
					}
				}else {
					communityMeterRecord.setExceptionState(0);
					communityMeterRecord.setExceptionRemark("");
				}
			}else {
				communityMeterRecord.setExceptionState(0);
				communityMeterRecord.setExceptionRemark("");
			}
            communityMeterRecord.setIsZero(req.getIsZero());
            communityMeterRecord.setOldData(req.getOldData());
            communityMeterRecord.setAdditionalAmount(StringUtils.isNotEmpty(req.getAdditionalAmount()) ? new BigDecimal(req.getAdditionalAmount()) : new BigDecimal(0));
            communityMeterRecord.setAdditionalInstructions(req.getAdditionalInstructions());
            communityMeterRecord.setOldId(req.getOldId());
            communityMeterRecord.setRecordNum(StringUtils.isNotEmpty(req.getRecordNum()) ?  new BigDecimal(req.getRecordNum()) : null);
            communityMeterRecord.setRecorder(req.getRecorder());
            communityMeterRecord.setState(EntityContext.RECORD_STATE_VALID);
            communityMeterRecord.setLastModifyTime(new Timestamp(new Date().getTime()));
            communityMeterRecord.setCommunityMeter(meter);
            
			communityMeterRecordDao.save(communityMeterRecord);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(StringUtils.isNotEmpty(info.toString()) ? info.toString() : ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "修改抄表记录")
	public IResponse modifyCommunityMeterRecord(CommunityMeterRecordReq req) {
		ModifyCommunityMeterRecordRes res = new ModifyCommunityMeterRecordRes();
		if (null!=req.getCommunityMeterRecordId() && null != req.getIsZero()) {
			CommunityMeterRecordEntity communityMeterRecord = communityMeterRecordDao.get(req.getCommunityMeterRecordId());
			if(null != communityMeterRecord){
				List<CommunityMeterRecordEntity> subRecordList = communityMeterRecordDao.getListByHql
						("select a from CommunityMeterRecordEntity a where a.lastRecordId="+
						req.getCommunityMeterRecordId() +" and a.state=1", "");

                try {
                    communityMeterRecord.setRecordDate(StringUtils.isNotEmpty(req.getRecordDate()) ? 
                        DateUtil.parseLongFormat(req.getRecordDate()) : null);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                CommunityMeterEntity meter = communityMeterRecord.getCommunityMeter();
                StringBuilder info = new StringBuilder();
    			CommunityMeterRecordEntity parentMeterRecord = communityMeterRecordDao.getUniqueBySql
    					(" SELECT *  FROM `t_community_meter_record` WHERE meterId="+req.getMeterId()+
    							" and id!="+communityMeterRecord.getId()+" AND state="+EntityContext.RECORD_STATE_VALID+" ORDER BY recordDate DESC LIMIT 0,1" , "");
				if(null==subRecordList || subRecordList.size()<=0) {
	                BigDecimal num = StringUtils.isNotEmpty(req.getRecordNum()) ?  new BigDecimal(req.getRecordNum()) : new BigDecimal(0);
	    			if(null != parentMeterRecord){
	    				//communityMeterRecord.setLastRecordId(parentMeterRecord.getId());
	    				if(num.compareTo(parentMeterRecord.getRecordNum()) == 1) {
	    					BigDecimal difference = num.subtract(parentMeterRecord.getRecordNum());
	    					if(difference.divide(parentMeterRecord.getRecordNum(),2, RoundingMode.HALF_UP).compareTo(new BigDecimal(0.1)) == 1) {
	    						info.append("【表(编号："+meter.getMeterCode()+"，名称："+meter.getMeterName()+")异常；相比上个月数据超出10%以上！】");
	    						communityMeterRecord.setExceptionState(null!=req.getExceptionState() ? req.getExceptionState() : 1);
	    						communityMeterRecord.setExceptionRemark(StringUtils.isNotEmpty(req.getExceptionRemark()) ? req.getExceptionRemark() : info.toString());
	    					}
	    				}else if(num.compareTo(parentMeterRecord.getRecordNum()) == -1) {
	    					BigDecimal difference = parentMeterRecord.getRecordNum().subtract(num);
	    					if(difference.divide(parentMeterRecord.getRecordNum(),2,  RoundingMode.HALF_UP).compareTo(new BigDecimal(0.1)) == 1) {
	    						info.append("【表(编号："+meter.getMeterCode()+"，名称："+meter.getMeterName()+")异常；相比上个月数据减少10%以下！】");
	    						communityMeterRecord.setExceptionState(null!=req.getExceptionState() ? req.getExceptionState() : 1);
	    						communityMeterRecord.setExceptionRemark(StringUtils.isNotEmpty(req.getExceptionRemark()) ? req.getExceptionRemark() : info.toString());
	    					}
	    				}else {
							communityMeterRecord.setExceptionState(null!=req.getExceptionState() ? req.getExceptionState() : 0);
							communityMeterRecord.setExceptionRemark(StringUtils.isNotEmpty(req.getExceptionRemark()) ? req.getExceptionRemark() : "");
	    				}
	    			}else {
						communityMeterRecord.setExceptionState(0); 
						communityMeterRecord.setExceptionRemark(StringUtils.isNotEmpty(req.getExceptionRemark()) ? req.getExceptionRemark() : "");
	    			}
	    			communityMeterRecord.setRecordNum(StringUtils.isNotEmpty(req.getRecordNum()) ?  new BigDecimal(req.getRecordNum()) : null);
				}else {
					communityMeterRecord.setExceptionState(null!=req.getExceptionState() ? req.getExceptionState() : 0);
					communityMeterRecord.setExceptionRemark(StringUtils.isNotEmpty(req.getExceptionRemark()) ? req.getExceptionRemark() : "");
				}
                
                communityMeterRecord.setIsZero(req.getIsZero());
                communityMeterRecord.setOldData(req.getOldData());
                communityMeterRecord.setOldId(req.getOldId());
                communityMeterRecord.setRecorder(req.getRecorder());
                communityMeterRecord.setLastModifyTime(new Timestamp(new Date().getTime()));
                communityMeterRecord.setCommunityMeter(null!=req.getMeterId() ? communityMeterDao.get(req.getMeterId()) : communityMeterRecord.getCommunityMeter());
                communityMeterRecord.setAdditionalAmount(null!=req.getAdditionalAmount() ? new BigDecimal(req.getAdditionalAmount()) :  communityMeterRecord.getAdditionalAmount());
                communityMeterRecord.setAdditionalInstructions(StringUtils.isNotEmpty(req.getAdditionalInstructions()) ? 
                		req.getAdditionalInstructions() : communityMeterRecord.getAdditionalInstructions());
				res.setCommunityMeterRecordId(communityMeterRecord.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO+(StringUtils.isNotEmpty(info.toString()) ? info.toString() : ""));
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除抄表记录")
	public IResponse deleteCommunityMeterRecord(CommunityMeterRecordReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getCommunityMeterRecordIdList())) {
			String[] meterRecordIds = req.getCommunityMeterRecordIdList().split(",");
			for (String meterRecordId : meterRecordIds) {
				CommunityMeterRecordEntity communityMeterRecord = communityMeterRecordDao.get(Integer.parseInt(meterRecordId));
				if (null != communityMeterRecord) {
					//communityMeterRecordDao.deleteById(req.getCommunityMeterRecordId());
					communityMeterRecord.setState(EntityContext.RECORD_STATE_INVALID);
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
	                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				}
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityMeterRecordInfo(CommunityMeterRecordReq req) {
		GetCommunityMeterRecordInfoRes res = new GetCommunityMeterRecordInfoRes();
		if (null != req.getCommunityMeterRecordId()) {
			CommunityMeterRecordEntity communityMeterRecord = communityMeterRecordDao.get(req.getCommunityMeterRecordId());
			if (null != communityMeterRecord) {
				CommunityMeterRecordForm communityMeterRecordForm = new CommunityMeterRecordForm();
				communityMeterRecordForm.setCommunityMeterRecordId(communityMeterRecord.getId());
                communityMeterRecordForm.setRecordDate(null != communityMeterRecord.getRecordDate()? DateUtil.formatLongFormat(communityMeterRecord.getRecordDate()) : "");
                communityMeterRecordForm.setIsZero(communityMeterRecord.getIsZero());
                communityMeterRecordForm.setOldData(communityMeterRecord.getOldData());
                communityMeterRecordForm.setOldId(communityMeterRecord.getOldId());
                communityMeterRecordForm.setRecordNum(null!=communityMeterRecord.getRecordNum() ? communityMeterRecord.getRecordNum().toString() : "");
                communityMeterRecordForm.setRecorder(communityMeterRecord.getRecorder());
                communityMeterRecordForm.setExceptionState(communityMeterRecord.getExceptionState());
                communityMeterRecordForm.setExceptionRemark(communityMeterRecord.getExceptionRemark());
                if(null != communityMeterRecord.getCommunityMeter()) {
                	CommunityMeterForm communityMeterForm = new CommunityMeterForm();
        			communityMeterForm.setCommunityMeterId(communityMeterRecord.getCommunityMeter().getId());
                    communityMeterForm.setMeterCode(communityMeterRecord.getCommunityMeter().getMeterCode());
                    communityMeterForm.setMeterName(communityMeterRecord.getCommunityMeter().getMeterName());
                    if(null!=req.getIsRent() && req.getIsRent()==1) {
        				CommunityEstateEntity estate = (CommunityEstateEntity)communityMeterRecord.
        						getCommunityMeter().getPropertyList().get(0);
        				communityMeterForm.setUnitCode(estate.getUnitCode());
                    }
                    communityMeterRecordForm.setCommunityMeterForm(communityMeterForm);
                }
                if(null != communityMeterRecord.getLastRecordId()) {
                	CommunityMeterRecordForm lastMeterRecordForm = new CommunityMeterRecordForm();
                	CommunityMeterRecordEntity lastMeterRecord = communityMeterRecordDao.get(communityMeterRecord.getLastRecordId());
                	lastMeterRecordForm.setLastNum(null!=lastMeterRecord ? lastMeterRecord.getRecordNum().toString() : "");
                	lastMeterRecordForm.setLastDate(null != lastMeterRecord.getRecordDate()? DateUtil.formatLongFormat(lastMeterRecord.getRecordDate()) : "");
                	lastMeterRecordForm.setCommunityMeterRecordId(lastMeterRecord.getId());
                	communityMeterRecordForm.setLastRecord(lastMeterRecordForm);
                }
                communityMeterRecordForm.setAdditionalAmount(null!=communityMeterRecord.getAdditionalAmount() ? communityMeterRecord.getAdditionalAmount().toString() :"");
                communityMeterRecordForm.setAdditionalInstructions(StringUtils.isNotEmpty(communityMeterRecord.getAdditionalInstructions()) ? 
                		communityMeterRecord.getAdditionalInstructions() :"");
				res.setCommunityMeterRecordForm(communityMeterRecordForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}