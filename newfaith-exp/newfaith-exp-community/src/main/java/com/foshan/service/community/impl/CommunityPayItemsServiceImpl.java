package com.foshan.service.community.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityEstateForm;
import com.foshan.form.community.CommunityMemberPropertyForm;
import com.foshan.form.community.CommunityPayItemsForm;
import com.foshan.form.community.CommunityPayItemsPriceForm;
import com.foshan.form.community.request.CommunityPayItemsReq;
import com.foshan.form.community.response.communityPayItems.AddCommunityPayItemsRes;
import com.foshan.form.community.response.communityPayItems.GetCommunityPayItemsInfoRes;
import com.foshan.form.community.response.communityPayItems.GetCommunityPayItemsListByPropertyRes;
import com.foshan.form.community.response.communityPayItems.GetCommunityPayItemsListRes;
import com.foshan.form.community.response.communityPayItems.ModifyCommunityPayItemsRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityPayItemsService;
import com.foshan.util.DateUtil;
import com.foshan.util.community.CommunityCache;

@Transactional
@Service("communityPayItemsService")
public class CommunityPayItemsServiceImpl extends GenericCommunityService implements ICommunityPayItemsService {


	@Override
	public IResponse getCommunityPayItemsList(CommunityPayItemsReq req) {
		GetCommunityPayItemsListRes res = new GetCommunityPayItemsListRes();
		Page<CommunityPayItemsEntity> page = new Page<CommunityPayItemsEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 200);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityPayItemsEntity a  ");
		if(null != req.getPropertyId() && null!=req.getBindingState() && req.getBindingState() == 0) {
			hql.append(" where a.id not in(select distinct a1.id from CommunityPayItemsEntity a1 inner join a1.propertyList b1 where b1.id=" + req.getPropertyId()+")");
		}else {
			hql.append(null != req.getPropertyId() ? " inner join a.propertyList b where b.id=" + req.getPropertyId()
			: " where 1=1 ");
		}
		hql.append(null != req.getState() ? " and a.state=" + req.getState()
						: " and a.state=" + EntityContext.RECORD_STATE_VALID)
				.append(null != req.getChargeCategory() ? " and a.chargeCategory ='" + req.getChargeCategory() + "'"
						: StringUtils.isNotEmpty(req.getChargeCategoryList()) ? "  and a.chargeCategory in("+req.getChargeCategoryList()+")":"")
				.append(StringUtils.isNotEmpty(req.getItemsName())
						? " and a.itemsName like'%" + req.getItemsName() + "%'"
						: "");
		if (req.getFetchType() == null || req.getFetchType().intValue() == 0) {
			hql.append("");
		} else if (req.getFetchType().intValue() == 1) {
			hql.append(" and a.payDate > 0");
		} else if (req.getFetchType().intValue() == 2) {
			hql.append(" and a.payDate = 0");
		}

		hql.append(" ORDER BY a.id desc");
		page = communityPayItemsDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			res.getCommunityPayItemsList().add(toCommunityPayItemsForm(o));
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	public CommunityPayItemsForm toCommunityPayItemsForm(CommunityPayItemsEntity payItems) {
		CommunityPayItemsForm communityPayItemsForm = new CommunityPayItemsForm();
		communityPayItemsForm.setCommunityPayItemsId(payItems.getId());
		communityPayItemsForm.setChargeCategory(payItems.getChargeCategory());
		communityPayItemsForm.setComment(payItems.getComment());
		communityPayItemsForm.setEndTime(null != payItems.getEndTime() ? DateUtil.formatLongFormat(payItems.getEndTime()) : "");
		communityPayItemsForm.setPriceUnit(payItems.getPriceUnit());
		communityPayItemsForm.setIsReceivables(payItems.getIsReceivables());
		communityPayItemsForm.setItemsName(payItems.getItemsName());
		communityPayItemsForm.setPayDate(payItems.getPayDate());
		communityPayItemsForm.setGroupFee(payItems.getGroupFee());
		communityPayItemsForm.setPriority(payItems.getPriority());
		communityPayItemsForm.setEnableBenefitPolicy(payItems.getEnableBenefitPolicy());
		communityPayItemsForm.setBenefitStartDate(null != payItems.getBenefitStartDate() ? DateUtil.formatLongFormat(payItems.getBenefitStartDate()) : "");
		communityPayItemsForm.setBenefitOrginPrice(payItems.getBenefitOrginPrice());		
		communityPayItemsForm.setPrice(null != payItems.getPrice() ? payItems.getPrice().toString() : "");
		communityPayItemsForm.setComment(StringUtils.isNotEmpty(payItems.getComment()) ?  payItems.getComment() :"");
		communityPayItemsForm.setFeeCalType(null!=payItems.getFeeCalType() ? payItems.getFeeCalType() : null);
		communityPayItemsForm.setInviceInfo(StringUtils.isNotEmpty(payItems.getInviceInfo()) ?  payItems.getInviceInfo() :"");
		communityPayItemsForm.setIsBreach(null!=payItems.getIsBreach() ? payItems.getIsBreach() : null);
		communityPayItemsForm.setBreachRatio(null!=payItems.getBreachRatio() ? payItems.getBreachRatio().toString() :"");
		communityPayItemsForm.setBreachName(StringUtils.isNotEmpty(payItems.getBreachName()) ?  payItems.getBreachName() :"");
		communityPayItemsForm
				.setStartTime(null != payItems.getStartTime() ? DateUtil.formatLongFormat(payItems.getStartTime()) : "");
		communityPayItemsForm.setIsBankDeposit(payItems.getIsBankDeposit());
		payItems.getPayItemsPriceList().forEach(o->{
			CommunityPayItemsPriceForm payItemsPriceForm = new CommunityPayItemsPriceForm();
			payItemsPriceForm.setStartTime(o.getComment());
			payItemsPriceForm.setPayItemsPriceId(o.getId());
			payItemsPriceForm.setPeriod(o.getPeriod());
			payItemsPriceForm.setPrice(o.getPrice().toString());
			payItemsPriceForm.setCreateTime(null != o.getCreateTime() ? DateUtil.formatLongFormat(o.getCreateTime()) : "");
			payItemsPriceForm.setEndTime(null != o.getEndTime() ? DateUtil.formatLongFormat(o.getEndTime()) : "");
			payItemsPriceForm.setLastModifyTime(null != o.getLastModifyTime() ? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
			payItemsPriceForm.setStartTime(null != o.getStartTime() ? DateUtil.formatLongFormat(o.getStartTime()) : "");
			payItemsPriceForm.setItemsName(o.getPayItems().getItemsName());
			payItemsPriceForm.setChargeCategory(o.getPayItems().getChargeCategory());
			payItemsPriceForm.setPriceUnit(o.getPayItems().getPriceUnit());
			payItemsPriceForm.setFeeCalType(o.getPayItems().getFeeCalType());
			payItemsPriceForm.setPayDate(o.getPayItems().getPayDate());
			payItemsPriceForm.setIsBreach(o.getPayItems().getIsBreach());
			payItemsPriceForm.setComment(StringUtils.isNotEmpty(o.getComment()) ? o.getComment() : "");
			communityPayItemsForm.getPayItemsPriceList().add(payItemsPriceForm);
		});
		return communityPayItemsForm;
	}
	
	public IResponse getCommunityPayItemsListByProperty(CommunityPayItemsReq req) {
		GetCommunityPayItemsListByPropertyRes res = new GetCommunityPayItemsListByPropertyRes();
		if(null == req.getPropertyId()) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		StringBuilder hql = new StringBuilder("select distinct a from CommunityPayItemsEntity a "
				+ " inner join a.propertyList b where b.id=" + req.getPropertyId());
		hql.append(null != req.getState() ? " and a.state=" + req.getState()
			: " and a.state=" + EntityContext.RECORD_STATE_VALID)
			.append(null != req.getChargeCategory() ? " and a.chargeCategory ='" + req.getChargeCategory() + "'"
					: "")
			.append(StringUtils.isNotEmpty(req.getItemsName())
					? " and a.itemsName like'%" + req.getItemsName() + "%'"
					: "");
		if (req.getFetchType() == null || req.getFetchType().intValue() == 0) {
			hql.append("");
		} else if (req.getFetchType().intValue() == 1) {
			hql.append(" and a.payDate > 0");
		} else if (req.getFetchType().intValue() == 2) {
			hql.append(" and a.payDate = 0");
		}

		hql.append(" ORDER BY a.id desc");
		List<CommunityPayItemsEntity> list = communityPayItemsDao.getListByHql(hql.toString(),"");

		list.forEach(o -> {
			res.getCommunityPayItemsList().add(toCommunityPayItemsForm(o));
		});
		
		hql = new StringBuilder("select distinct a from CommunityEstateEntity a  "
				+ " inner join a.memberPropertyList b where b.parentProperty.id="+req.getPropertyId());
		hql.append("and b.isCurrentMember=1");
		List<CommunityEstateEntity> parkingList = communityEstateDao.getListByHql(hql.toString(), "");
		parkingList.forEach(o->{
			CommunityEstateForm communityEstateForm = new CommunityEstateForm();
			communityEstateForm.setEstateId(o.getId());
			communityEstateForm.setPropertyName(o.getPropertyName());
			communityEstateForm.setUnitCode(o.getUnitCode());
			communityEstateForm.setEstateType(o.getEstateType());
			communityEstateForm.setBuildingArea(o.getBuildingArea().toString());
			communityEstateForm.setComment(o.getComment());
			o.getPayItemsList().forEach(p->{
				communityEstateForm.getPayItemsList().add(toCommunityPayItemsForm(p));
			});
			for (CommunityMemberPropertyEntity memberProperty : o.getMemberPropertyList()) {
				if(memberProperty.getIsCurrentMember() ==1) {
					CommunityMemberPropertyForm memberProperForm = new CommunityMemberPropertyForm();
					memberProperForm.setMemberType(memberProperty.getMemberType());
					memberProperForm.setAuditState(memberProperty.getAuditState());
					memberProperForm.setIsCurrentMember(memberProperty.getIsCurrentMember());
					memberProperForm.setBillingDate(null != memberProperty.getBillingDate()
							? DateUtil.formatLongFormat(memberProperty.getBillingDate())
							: "");
					memberProperForm
							.setCreateTime(null != o.getCreateTime() ? DateUtil.formatLongFormat(o.getCreateTime()) : "");
					memberProperForm.setIsCurrentOwner(memberProperty.getIsCurrentOwner());
					memberProperForm.setRecordDate(null != memberProperty.getRecordDate()
							? DateUtil.formatLongFormat(memberProperty.getRecordDate())
							: "");
					memberProperForm.setTerminationDate(null != memberProperty.getTerminationDate()
							? DateUtil.formatLongFormat(memberProperty.getTerminationDate())
							: "");
					memberProperForm.setMemberPropertyId(memberProperty.getId());
					memberProperForm.setEndDate(
							null != memberProperty.getEndDate() ? DateUtil.formatLongFormat(memberProperty.getEndDate())
									: "");
					memberProperForm.setCarInfo(memberProperty.getCarInfo());
					memberProperForm.setComment(memberProperty.getComment());
					communityEstateForm.getMemberPropertyList().add(memberProperForm);
				}
			}
			
			res.getParkingList().add(communityEstateForm);
		});
		hql = new StringBuilder("select distinct a from CommunityEstateEntity a  where a.id="+req.getPropertyId());
		CommunityEstateEntity estate = communityEstateDao.get(req.getPropertyId());
		CommunityEstateForm estateForm = new CommunityEstateForm();
		estateForm.setEstateId(estate.getId());
		estateForm.setPropertyName(estate.getPropertyName());
		estateForm.setUnitCode(estate.getUnitCode());
		estateForm.setEstateType(estate.getEstateType());
		estateForm.setBuildingArea(estate.getBuildingArea().toString());
		estateForm.setComment(estate.getComment());
		res.setEstateForm(estateForm);
		
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "新增收费项目")
	public IResponse addCommunityPayItems(CommunityPayItemsReq req) {
		AddCommunityPayItemsRes res = new AddCommunityPayItemsRes();
		// if () {
		if(req.getEnableBenefitPolicy() != null && req.getEnableBenefitPolicy().intValue() == 1) {
			if(StringUtils.isEmpty(req.getBenefitStartDate())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "优惠开始生效时间不能为空！");
				return res;
			}
			if(req.getBenefitOrginPrice() == null) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "不符合优惠策略单价不能为空！");
				return res;
			}
			else {
				req.getBenefitOrginPrice().setScale(2, RoundingMode.HALF_UP);
			}
		} 
		else {
			req.setEnableBenefitPolicy(0); 
		}
		CommunityPayItemsEntity communityPayItems = new CommunityPayItemsEntity();

		communityPayItems.setChargeCategory(req.getChargeCategory());
		communityPayItems.setComment(req.getComment());
		communityPayItems.setState(EntityContext.RECORD_STATE_VALID);
		communityPayItems.setPriceUnit(req.getPriceUnit());
		communityPayItems.setIsReceivables(null!=req.getIsReceivables() ? req.getIsReceivables() : 1);
		communityPayItems.setItemsName(req.getItemsName());
		communityPayItems.setPayDate(req.getPayDate());
		communityPayItems.setGroupFee(req.getGroupFee());
		communityPayItems.setLastModifyTime(new Timestamp(new Date().getTime()));
		communityPayItems.setPrice(StringUtils.isNotEmpty(req.getPrice()) ? new BigDecimal(req.getPrice()) : null);
		communityPayItems.setBreachName(StringUtils.isNotEmpty(req.getBreachName()) ? req.getBreachName() : "");
		communityPayItems.setIsBankDeposit(req.getIsBankDeposit());
		communityPayItems.setInviceInfo(StringUtils.isNotEmpty(req.getInviceInfo()) ? req.getInviceInfo() : "");
		communityPayItems.setFeeCalType(req.getFeeCalType());
		communityPayItems.setIsBreach(req.getIsBreach());
		communityPayItems.setBreachRatio(StringUtils.isNotEmpty(req.getBreachRatio()) ? 
				new BigDecimal(req.getBreachRatio()) : new BigDecimal(0));
		communityPayItems.setPriority(null!=req.getPriority() ? req.getPriority() : 0);
		communityPayItems.setBenefitOrginPrice(req.getBenefitOrginPrice());	
		communityPayItems.setEnableBenefitPolicy(req.getEnableBenefitPolicy());
		try {
			communityPayItems.setEndTime(
					StringUtils.isNotEmpty(req.getEndTime()) ? DateUtil.parseLongFormat(req.getEndTime()) : null);
			communityPayItems.setStartTime(
					StringUtils.isNotEmpty(req.getStartTime()) ? DateUtil.parseLongFormat(req.getStartTime()) : null);
			communityPayItems.setBenefitStartDate(
					StringUtils.isNotEmpty(req.getBenefitStartDate()) ? DateUtil.parseLongFormat(req.getBenefitStartDate()) : null);
		} catch (ParseException e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "日期格式有误，请按yyyy-MM-dd HH:mm:ss格式提交数据！");
			return res;
		}
		if(communityPayItems.getStartTime() != null && communityPayItems.getBenefitStartDate() != null && communityPayItems.getStartTime().after(communityPayItems.getBenefitStartDate())) {
			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "优惠策略生效时间不能早于收费项目的开始时间！");
			return res;
		}
		if(communityPayItems.getEndTime() != null && communityPayItems.getBenefitStartDate() != null && communityPayItems.getBenefitStartDate().after(communityPayItems.getEndTime())) {
			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "优惠策略生效时间不能晚于收费项目的结束时间！");
			return res;
		}

		communityPayItemsDao.save(communityPayItems);
		CommunityCache.payItemsList.put(communityPayItems.getId(), communityPayItems);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Override
	@Audit(operate = "修改收费项目")
	public IResponse modifyCommunityPayItems(CommunityPayItemsReq req) {
		ModifyCommunityPayItemsRes res = new ModifyCommunityPayItemsRes();
		if (null != req.getCommunityPayItemsId()) {
			CommunityPayItemsEntity communityPayItems = communityPayItemsDao.get(req.getCommunityPayItemsId());
			if (null != communityPayItems) {
				if(req.getEnableBenefitPolicy() != null && req.getEnableBenefitPolicy().intValue() == 1) {
					if(StringUtils.isEmpty(req.getBenefitStartDate())) {
						res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "优惠开始生效时间不能为空！");
						return res;
					}
					if(req.getBenefitOrginPrice() == null) {
						res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "不符合优惠策略时的单价不能为空！");
						return res;
					}
					try {

						Date stratTime = StringUtils.isNotEmpty(req.getStartTime()) ? DateUtil.parseLongFormat(req.getStartTime())
								: communityPayItems.getStartTime();
						Date endTime = StringUtils.isNotEmpty(req.getEndTime()) ? DateUtil.parseLongFormat(req.getEndTime())
								: communityPayItems.getEndTime();
						Date benefitStartDate = StringUtils.isNotEmpty(req.getBenefitStartDate()) ? DateUtil.parseLongFormat(req.getBenefitStartDate())
								: communityPayItems.getBenefitStartDate();
						if(stratTime != null && benefitStartDate != null && stratTime.after(benefitStartDate)) {
							res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "优惠策略生效时间不能早于收费项目的开始时间！");
							return res;
						}
						if(endTime != null && benefitStartDate != null && benefitStartDate.after(endTime)) {
							res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
							res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "优惠策略生效时间不能晚于收费项目的结束时间！");
							return res;
						}
					} catch (ParseException e) {
						e.printStackTrace();
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "日期格式有误，请按yyyy-MM-dd HH:mm:ss格式提交数据！");
						return res;
						
					}
					
				}
				communityPayItems.setChargeCategory(req.getChargeCategory());
				communityPayItems.setComment(req.getComment());
				try {
					communityPayItems.setEndTime(
							StringUtils.isNotEmpty(req.getEndTime()) ? DateUtil.parseLongFormat(req.getEndTime())
									: null);
				} catch (ParseException e) {
					e.printStackTrace();
				}
				communityPayItems.setLastModifyTime(new Timestamp(new Date().getTime()));
				communityPayItems.setPriceUnit(req.getPriceUnit());
				communityPayItems.setIsReceivables(null!=req.getIsReceivables() ? 
						req.getIsReceivables() : communityPayItems.getIsReceivables());
				communityPayItems.setItemsName(req.getItemsName());
				communityPayItems.setPayDate(req.getPayDate());
				communityPayItems.setGroupFee(null != req.getGroupFee() ? req.getGroupFee() : communityPayItems.getGroupFee());
				communityPayItems.setBreachName(StringUtils.isNotEmpty(req.getBreachName()) ? req.getBreachName() : communityPayItems.getBreachName());
				communityPayItems.setState(null != req.getState() ? req.getState() : EntityContext.RECORD_STATE_VALID);
				communityPayItems.setFeeCalType(req.getFeeCalType());
				communityPayItems.setIsBreach(req.getIsBreach());
				communityPayItems.setBreachRatio(StringUtils.isNotEmpty(req.getBreachRatio()) ? new BigDecimal(req.getBreachRatio()) : communityPayItems.getBreachRatio());
				communityPayItems
						.setPrice(StringUtils.isNotEmpty(req.getPrice()) ? new BigDecimal(req.getPrice()) : null);
				communityPayItems.setInviceInfo(StringUtils.isNotEmpty(req.getInviceInfo()) ? req.getInviceInfo() : communityPayItems.getInviceInfo());
				communityPayItems.setIsBankDeposit(null!=req.getIsBankDeposit() ? req.getIsBankDeposit() : communityPayItems.getIsBankDeposit());
				communityPayItems.setPriority(null!=req.getPriority() ? req.getPriority() : communityPayItems.getPriority());
				
				communityPayItems.setEnableBenefitPolicy(null != req.getEnableBenefitPolicy() ? req.getEnableBenefitPolicy() : communityPayItems.getEnableBenefitPolicy());
				communityPayItems.setBenefitOrginPrice(null != req.getBenefitOrginPrice() ? req.getBenefitOrginPrice().setScale(2, RoundingMode.HALF_UP) : communityPayItems.getBenefitOrginPrice());	
				try {
					communityPayItems.setStartTime(
							StringUtils.isNotEmpty(req.getStartTime()) ? DateUtil.parseLongFormat(req.getStartTime())
									: null);
					communityPayItems.setBenefitStartDate(
							StringUtils.isNotEmpty(req.getBenefitStartDate()) ? DateUtil.parseLongFormat(req.getBenefitStartDate()) : communityPayItems.getBenefitStartDate());
				} catch (ParseException e) {
					e.printStackTrace();
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + "日期格式有误，请按yyyy-MM-dd HH:mm:ss格式提交数据！");
					return res;
				}
				CommunityCache.payItemsList.put(communityPayItems.getId(), communityPayItems);
				res.setCommunityPayItemsId(communityPayItems.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除收费项目")
	public IResponse deleteCommunityPayItems(CommunityPayItemsReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityPayItemsId()) {
			CommunityPayItemsEntity communityPayItems = communityPayItemsDao.get(req.getCommunityPayItemsId());
			if (null != communityPayItems) {
				// communityPayItemsDao.deleteById(req.getCommunityPayItemsId());
				communityPayItems.setState(EntityContext.RECORD_STATE_INVALID);
				CommunityCache.payItemsList.remove(communityPayItems.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityPayItemsInfo(CommunityPayItemsReq req) {
		GetCommunityPayItemsInfoRes res = new GetCommunityPayItemsInfoRes();
		if (null != req.getCommunityPayItemsId()) {
			CommunityPayItemsEntity communityPayItems = communityPayItemsDao.get(req.getCommunityPayItemsId());
			if (null != communityPayItems) {
				CommunityPayItemsForm communityPayItemsForm = new CommunityPayItemsForm();
				communityPayItemsForm.setCommunityPayItemsId(communityPayItems.getId());
				communityPayItemsForm.setChargeCategory(communityPayItems.getChargeCategory());
				communityPayItemsForm.setComment(communityPayItems.getComment());
				communityPayItemsForm.setEndTime(null != communityPayItems.getEndTime()
						? DateUtil.formatLongFormat(communityPayItems.getEndTime())
						: "");
				communityPayItemsForm.setPriceUnit(communityPayItems.getPriceUnit());
				communityPayItemsForm.setIsReceivables(communityPayItems.getIsReceivables());
				communityPayItemsForm.setInviceInfo(StringUtils.isNotEmpty(communityPayItems.getInviceInfo()) ?  communityPayItems.getInviceInfo() :"");
				communityPayItemsForm.setItemsName(communityPayItems.getItemsName());
				communityPayItemsForm.setPayDate(communityPayItems.getPayDate());
				communityPayItemsForm.setPriority(communityPayItems.getPriority());
				communityPayItemsForm.setGroupFee(communityPayItems.getGroupFee());
				communityPayItemsForm.setBreachName(StringUtils.isNotEmpty(communityPayItems.getBreachName()) ?  communityPayItems.getBreachName() :"");
				communityPayItemsForm
						.setPrice(null != communityPayItems.getPrice() ? communityPayItems.getPrice().toString() : "");
				communityPayItemsForm.setStartTime(null != communityPayItems.getStartTime()
						? DateUtil.formatLongFormat(communityPayItems.getStartTime())
						: "");
				communityPayItemsForm.setFeeCalType(null!=communityPayItems.getFeeCalType() ? communityPayItems.getFeeCalType() : null);
				communityPayItemsForm.setIsBreach(null!=communityPayItems.getIsBreach() ? communityPayItems.getIsBreach() : null);
				communityPayItemsForm.setBreachRatio(null!=communityPayItems.getBreachRatio() ? communityPayItems.getBreachRatio().toString() :"");
				communityPayItemsForm.setIsBankDeposit(communityPayItems.getIsBankDeposit());
				communityPayItemsForm.setEnableBenefitPolicy(communityPayItems.getEnableBenefitPolicy());
				communityPayItemsForm.setBenefitStartDate(null != communityPayItems.getBenefitStartDate() ? DateUtil.formatLongFormat(communityPayItems.getBenefitStartDate()) : "");
				communityPayItemsForm.setBenefitOrginPrice(communityPayItems.getBenefitOrginPrice());		
				res.setCommunityPayItemsForm(communityPayItemsForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}