package com.foshan.service.community.impl;

import org.springframework.stereotype.Service;

import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityPaymentAccountEntity;
import com.foshan.entity.community.CommunityPropertyEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityPaymentAccountService;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.form.community.request.CommunityPaymentAccountReq;
import com.foshan.form.community.response.communityPaymentAccount.AddCommunityPaymentAccountRes;
import com.foshan.form.community.response.communityPaymentAccount.ModifyCommunityPaymentAccountRes;
import com.foshan.form.community.response.communityPaymentAccount.GetCommunityPaymentAccountInfoRes;
import com.foshan.form.community.response.communityPaymentAccount.GetCommunityPaymentAccountListRes;
import com.foshan.form.community.CommunityEstateForm;
import com.foshan.form.community.CommunityMemberForm;
import com.foshan.form.community.CommunityPaymentAccountForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.IResponse;
import com.foshan.util.DateUtil;
import com.foshan.dao.generic.Page;
import org.apache.commons.lang3.StringUtils;
import java.sql.Timestamp;
import java.util.Date;

@Transactional
@Service("communityPaymentAccountService")
public class CommunityPaymentAccountServiceImpl extends GenericCommunityService implements ICommunityPaymentAccountService{

	@Override
	public IResponse getCommunityPaymentAccountList(CommunityPaymentAccountReq req) {
		GetCommunityPaymentAccountListRes res = new GetCommunityPaymentAccountListRes();
		Page<CommunityPaymentAccountEntity> page = new Page<CommunityPaymentAccountEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityPaymentAccountEntity a ");
		if(null!=req.getBindingEstate() && req.getBindingEstate()==1) {
			hql.append(" inner join a.propertyList b where 1=1 ")
				.append(StringUtils.isNotEmpty(req.getUserCode()) ? " and b.userCode like'%"+req.getUserCode()+"%' ":"" );
		}else {
			hql.append(" where 1=1 ");
		}
		hql.append(null!=req.getMemberId() ? " and a.member.id="+req.getMemberId() :"")
			.append(null!=req.getState() ? " and a.state="+req.getState():" and a.state="+EntityContext.RECORD_STATE_VALID)
			.append(StringUtils.isNotEmpty(req.getAccountName()) ? " and a.accountName like'%"+req.getAccountName()+"%'" :"")
			.append(null!=req.getEstateId() ?  " and a.member.id in"
					+ "(select mp.member.id from CommunityMemberPropertyEntity mp where mp.isCurrentMember=0 and mp.property.id="+
					req.getEstateId()+")":"");
		
		hql.append(" ORDER BY a.id desc");
		page = communityPaymentAccountDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityPaymentAccountForm communityPaymentAccountForm = new CommunityPaymentAccountForm();
			communityPaymentAccountForm.setCommunityPaymentAccountId(o.getId());
            communityPaymentAccountForm.setCreateTime(null != o.getCreateTime()? DateUtil.formatLongFormat(o.getCreateTime()) : "");
            communityPaymentAccountForm.setLastModifyTime(null != o.getLastModifyTime()? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
            communityPaymentAccountForm.setState(o.getState());
            communityPaymentAccountForm.setBankAccount(o.getBankAccount());
            communityPaymentAccountForm.setBankName(o.getBankName());
            communityPaymentAccountForm.setIdNumber(o.getIdNumber());
            communityPaymentAccountForm.setAccountName(o.getAccountName());
            if(null != o.getMember()) {
        		CommunityMemberForm memberForm = new CommunityMemberForm();
    			memberForm.setMemberId(o.getMember().getId());
    			memberForm.setEmail(o.getMember().getEmail());
    			memberForm.setHomePhone(o.getMember().getHomePhone());
    			memberForm.setNickName(o.getMember().getNickName());
    			memberForm.setOfficePhone(o.getMember().getOfficePhone());
    			memberForm.setPhone(o.getMember().getPhone());
    			memberForm.setRegistName(o.getMember().getRegistName());
    			memberForm.setSex(o.getMember().getSex());
    			memberForm.setSmartcardId(o.getMember().getSmartcardId());
    			memberForm.setHeadImage(StringUtils.isNotEmpty(o.getMember().getHeadImage()) ? o.getMember().getHeadImage() : "");
    			memberForm.setUserName(StringUtils.isNotEmpty(o.getMember().getUserName()) ? o.getMember().getUserName() : "");
    			communityPaymentAccountForm.setMemberForm(memberForm);
            }
            if(null != o.getPropertyList()) {
            	o.getPropertyList().forEach(p->{
            		CommunityEstateEntity estate = (CommunityEstateEntity)p;
                	CommunityEstateForm estateForm = new CommunityEstateForm();
                	estateForm.setUnitCode(estate.getUnitCode());
                	estateForm.setPropertyId(estate.getId());
                	communityPaymentAccountForm.getEstateList().add(estateForm);
            	});
            }
			res.getCommunityPaymentAccountList().add(communityPaymentAccountForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "新增账户信息")
	public IResponse addCommunityPaymentAccount(CommunityPaymentAccountReq req) {
		AddCommunityPaymentAccountRes res = new AddCommunityPaymentAccountRes();
		//if () {
			CommunityPaymentAccountEntity communityPaymentAccount = new CommunityPaymentAccountEntity();
            communityPaymentAccount.setLastModifyTime(new Timestamp(new Date().getTime()));
            communityPaymentAccount.setState(null!=req.getState() ? req.getState() : EntityContext.RECORD_STATE_VALID);
            communityPaymentAccount.setBankAccount(StringUtils.isNotEmpty(req.getAccountName()) ? 
            		req.getBankAccount() : "");
            communityPaymentAccount.setBankName(StringUtils.isNotEmpty(req.getAccountName())  ? 
            		req.getBankName() : "");
            communityPaymentAccount.setAccountName(StringUtils.isNotEmpty(req.getAccountName())  ? 
            		req.getAccountName() : "");
            communityPaymentAccount.setMember(null!=req.getMemberId() ? 
            		communityMemberDao.get(req.getMemberId()) : null);
            communityPaymentAccount.setIdNumber(StringUtils.isNotEmpty(req.getIdNumber())  ? 
            		req.getIdNumber() : "");
			communityPaymentAccountDao.save(communityPaymentAccount);
			if(StringUtils.isNotEmpty(req.getPropertyIdList())) {
				String[] propertyIds = req.getPropertyIdList().split(",");
				 for (String propertyId : propertyIds) {
		            	CommunityPropertyEntity property = communityPropertyDao.get(Integer.parseInt(propertyId));
		            	if(null != property) {
		            		property.setPaymentAccount(communityPaymentAccount);
		            	}
		        }
			}

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Override
	@Audit(operate = "修改账户信息")
	public IResponse modifyCommunityPaymentAccount(CommunityPaymentAccountReq req) {
		ModifyCommunityPaymentAccountRes res = new ModifyCommunityPaymentAccountRes();
		if (null!=req.getCommunityPaymentAccountId() ) {
			CommunityPaymentAccountEntity communityPaymentAccount = communityPaymentAccountDao.get(req.getCommunityPaymentAccountId()) ;
			if(null != communityPaymentAccount){
                communityPaymentAccount.setLastModifyTime(new Timestamp(new Date().getTime()));
                communityPaymentAccount.setState(null!=req.getState() ? req.getState() : communityPaymentAccount.getState());
                communityPaymentAccount.setBankAccount(req.getBankAccount());
                communityPaymentAccount.setBankName(req.getBankName());
                communityPaymentAccount.setAccountName(req.getAccountName());
                communityPaymentAccount.setMember(null!=req.getMemberId() ? communityMemberDao.get(req.getMemberId()) : 
                	communityPaymentAccount.getMember());
                communityPaymentAccount.setIdNumber(StringUtils.isNotEmpty(req.getIdNumber())  ? 
                		req.getIdNumber() : communityPaymentAccount.getIdNumber());
                if(StringUtils.isNotEmpty(req.getPropertyIdList())) {
	    			String[] propertyIds = req.getPropertyIdList().split(",");
		   			for (String propertyId : propertyIds) {
		   				CommunityPropertyEntity property = communityPropertyDao.get(Integer.parseInt(propertyId));
	   	            	if(null != property) {
	   	            		property.setPaymentAccount(communityPaymentAccount);
	   	            	}
		   	        }
                }
				res.setCommunityPaymentAccountId(communityPaymentAccount.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除账户信息")
	public IResponse deleteCommunityPaymentAccount(CommunityPaymentAccountReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityPaymentAccountId()) {
		CommunityPaymentAccountEntity communityPaymentAccount = communityPaymentAccountDao.get(req.getCommunityPaymentAccountId());
			if (null != communityPaymentAccount) {
				communityPaymentAccount.setPropertyList(null);
				communityPaymentAccount.setMember(null);
				communityPaymentAccountDao.delete(communityPaymentAccount);
				//communityPaymentAccount.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityPaymentAccountInfo(CommunityPaymentAccountReq req) {
		GetCommunityPaymentAccountInfoRes res = new GetCommunityPaymentAccountInfoRes();
		if (null != req.getCommunityPaymentAccountId()) {
			CommunityPaymentAccountEntity communityPaymentAccount = communityPaymentAccountDao.get(req.getCommunityPaymentAccountId());
			if (null != communityPaymentAccount) {
				CommunityPaymentAccountForm communityPaymentAccountForm = new CommunityPaymentAccountForm();
				communityPaymentAccountForm.setCommunityPaymentAccountId(communityPaymentAccount.getId());
                communityPaymentAccountForm.setCreateTime(null != communityPaymentAccount.getCreateTime()? DateUtil.formatLongFormat(communityPaymentAccount.getCreateTime()) : "");
                communityPaymentAccountForm.setLastModifyTime(null != communityPaymentAccount.getLastModifyTime()? DateUtil.formatLongFormat(communityPaymentAccount.getLastModifyTime()) : "");
                communityPaymentAccountForm.setState(communityPaymentAccount.getState());
                communityPaymentAccountForm.setBankAccount(communityPaymentAccount.getBankAccount());
                communityPaymentAccountForm.setBankName(communityPaymentAccount.getBankName());
                communityPaymentAccountForm.setAccountName(communityPaymentAccount.getAccountName());
                communityPaymentAccountForm.setIdNumber(communityPaymentAccount.getIdNumber());
                if(null != communityPaymentAccount.getMember()) {
            		CommunityMemberForm memberForm = new CommunityMemberForm();
        			memberForm.setMemberId(communityPaymentAccount.getMember().getId());
        			memberForm.setEmail(communityPaymentAccount.getMember().getEmail());
        			memberForm.setHomePhone(communityPaymentAccount.getMember().getHomePhone());
        			memberForm.setNickName(communityPaymentAccount.getMember().getNickName());
        			memberForm.setOfficePhone(communityPaymentAccount.getMember().getOfficePhone());
        			memberForm.setPhone(communityPaymentAccount.getMember().getPhone());
        			memberForm.setRegistName(communityPaymentAccount.getMember().getRegistName());
        			memberForm.setSex(communityPaymentAccount.getMember().getSex());
        			memberForm.setSmartcardId(communityPaymentAccount.getMember().getSmartcardId());
        			memberForm.setUserName(StringUtils.isNotEmpty(communityPaymentAccount.getMember().getUserName()) ? communityPaymentAccount.getMember().getUserName() : "");
        			communityPaymentAccountForm.setMemberForm(memberForm);
                }
                if(null != communityPaymentAccount.getPropertyList()) {
                	communityPaymentAccount.getPropertyList().forEach(p->{
                		CommunityEstateEntity estate = (CommunityEstateEntity)p;
                    	CommunityEstateForm estateForm = new CommunityEstateForm();
                    	estateForm.setUnitCode(estate.getUnitCode());
                    	estateForm.setPropertyId(estate.getId());
                    	communityPaymentAccountForm.getEstateList().add(estateForm);
                	});
                }
				res.setCommunityPaymentAccountForm(communityPaymentAccountForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}