package com.foshan.service.community.task;


import com.foshan.service.community.ICommunityInvoiceService;
import com.foshan.service.quartz.IFaithJob;
import com.foshan.util.SpringHandler;


//@Slf4j
public class InvoiceTask implements IFaithJob {
	//private CommunityContextInfo communityContextInfo = (CommunityContextInfo)SpringHandler.getBean(CommunityContextInfo.class);


	@SuppressWarnings("unchecked")
	public void communityQueryInvoiceResultTask() {
		try {
//			Long start = System.currentTimeMillis();
//			log.info("===>定时任务开始发票相关结果查询！<===");

//			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
//			Session session = sessionFactory.openSession();
//			Transaction tx = session.beginTransaction();
//			List<CommunityInvoiceEntity> invoiceList  = session
//					.createQuery("select a from CommunityInvoiceEntity a where a.invoiceState=2").list();
//			int i=0;
//			for(CommunityInvoiceEntity invoice : invoiceList){
//				getResult(invoice);
//				session.saveOrUpdate(invoice);
//				if (i % 300 == 0) {
//					session.flush();
//					session.clear();
//				}
//				i++;
//			}
//			
//			tx.commit();
//			if (session != null) {
//				session.close();
//			}
			ICommunityInvoiceService communityInvoiceService = (ICommunityInvoiceService) SpringHandler.getBean("communityInvoiceService");
			communityInvoiceService.communityQueryInvoiceResultTask();
			
//			Long end = System.currentTimeMillis();
//			log.info("===>定时任务发票相关结果查询完毕，耗时："+(end - start)+"！<===");
		} catch (Exception ex) {
			
			ex.printStackTrace();
		}
	}
	
	/*public InvoiceRes getResult(CommunityInvoiceEntity communityInvoice) {
		InvoiceRes res = new InvoiceRes();
		InvoiceReq invoiceReq = new InvoiceReq();
		invoiceReq.setBillCode(communityInvoice.getInvoiceSn());
		if(communityInvoice.getInvoiceType()==0 ) {
			invoiceReq.setInvoiceType(communityInvoice.getInvoiceType());
			res = getResult(invoiceReq,"/queryInvoiceResult");
			if(res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
				communityInvoice.setInvoiceState(1);
				communityInvoice.setPlatUrl(res.getPdfUrl());
				communityInvoice.setInvoiceNum(res.getInvoiceNo());
				for(CommunityReceiptReceivablesEntity o : communityInvoice.getReceiptReceivablesList()) {
					o.setInvoiceState(1);
					o.getReceipt().setHaveInvoice(1);
				}
		 		Date date = new Date();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
				String today = sdf.format(date);
				String dir = communityContextInfo.getAssetFilePath()+ File.separator +"invoicePdf"+ 
						File.separator + today+ File.separator;
				FileUtil.getInternetFile(res.getPdfUrl(),dir,communityInvoice.getInvoiceSn()+".pdf");
				communityInvoice.setLocalUrl(communityContextInfo.getAssetFileUrl()+ "/" +"invoicePdf"+ 
						"/" + today+ "/"+communityInvoice.getInvoiceSn()+".pdf");
			}else {
				communityInvoice.setInvoiceState(0);
				communityInvoice.setReturnMessage(res.getRetInfo());
				Map<Integer, List<CommunityReceiptReceivablesEntity>> collect = 
						(Map<Integer, List<CommunityReceiptReceivablesEntity>>)communityInvoice.
						getReceiptReceivablesList().parallelStream().
						collect(groupingBy(CommunityReceiptReceivablesEntity::getId));
				boolean state = true;
				for(CommunityReceiptReceivablesEntity o : communityInvoice.getReceipt().getReceiptReceivablesList()){
					if(collect.containsKey(o.getId())) {
						o.setInvoiceState(0);
					}
					if(o.getInvoiceState()==1) {
						o.getReceipt().setHaveInvoice(1);
						break;
					}else if(o.getInvoiceState()==2){
						o.getReceipt().setHaveInvoice(2);
						state = false;
					}else if(state){
						o.getReceipt().setHaveInvoice(0);
					}
				}
			}
		}else if(communityInvoice.getInvoiceType()== 1){
			invoiceReq.setInvoiceType(communityInvoice.getInvoiceType());
			res = getResult(invoiceReq,"/queryInvoiceResult");
			if(res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
				communityInvoice.setInvoiceState(1);
				communityInvoice.setPlatUrl(res.getPdfUrl());
				communityInvoice.getParentInvoice().setIsRedDashed(1);
				Map<Integer, List<CommunityReceiptReceivablesEntity>> collect = 
						(Map<Integer, List<CommunityReceiptReceivablesEntity>>)communityInvoice.
						getReceiptReceivablesList().parallelStream().
						collect(groupingBy(CommunityReceiptReceivablesEntity::getId));
				boolean state = true;
				for(CommunityReceiptReceivablesEntity o : communityInvoice.getReceipt().getReceiptReceivablesList()){
					if(collect.containsKey(o.getId())) {
						o.setInvoiceState(0);
					}
					if(o.getInvoiceState()==1) {
						o.getReceipt().setHaveInvoice(1);
						break;
					}else if(o.getInvoiceState()==2){
						o.getReceipt().setHaveInvoice(2);
						state = false;
					}else if(state){
						o.getReceipt().setHaveInvoice(0);
					}
				}
		 		Date date = new Date();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
				String today = sdf.format(date);
				String dir = communityContextInfo.getAssetFilePath()+ File.separator +"invoicePdf"+ 
						File.separator + today+ File.separator;
				FileUtil.getInternetFile(res.getPdfUrl(),dir,communityInvoice.getInvoiceSn()+".pdf");
				
				communityInvoice.setLocalUrl(communityContextInfo.getAssetFileUrl()+ "/" +"invoicePdf"+ 
						"/" + today+ "/"+communityInvoice.getInvoiceSn()+".pdf");
			}else {
				communityInvoice.setInvoiceType(2);
				communityInvoice.setInvoiceState(1);
				communityInvoice.setReturnMessage(res.getRetInfo());
			}
		}else if(communityInvoice.getInvoiceType()==2) {
			invoiceReq.setInvoiceType(communityInvoice.getInvoiceType());
			res = getResult(invoiceReq,"/queryInvoiceResult");
			if(res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
				communityInvoice.setInvoiceState(1);
			}else {
				communityInvoice.setInvoiceState(0);
				communityInvoice.setReturnMessage(res.getRetInfo());
			}
		}else if(communityInvoice.getInvoiceType() == 3) {
			res = getResult(invoiceReq,"/queryWithdrawalRedRushApplicationResult");
			if(res.getRet().equals(ResponseContext.RES_SUCCESS_CODE)) {
				communityInvoice.setInvoiceState(1);
			}else {
				communityInvoice.setInvoiceType(2);
				communityInvoice.setInvoiceState(1);
				communityInvoice.setReturnMessage(res.getRetInfo());
			}
		}
		return res;
	}
	
	public InvoiceRes getResult(InvoiceReq invoiceReq,String interfaceName) {
		//InvoiceRes res = new InvoiceRes();
		String postResult="";
		String jsonstr="";
		ObjectMapper mapper = new ObjectMapper();
		try {
			jsonstr = mapper.writeValueAsString(invoiceReq);
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}
		try {
			postResult = HttpClientUtil.jsonPost(communityContextInfo.getInvoiceUrl()+interfaceName, "UTF-8", jsonstr,
					null);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		
		
		InvoiceRes invoiceRes = null;
		try {
			invoiceRes = mapper.readValue(postResult, InvoiceRes.class);
		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		return invoiceRes;
	}*/
}
