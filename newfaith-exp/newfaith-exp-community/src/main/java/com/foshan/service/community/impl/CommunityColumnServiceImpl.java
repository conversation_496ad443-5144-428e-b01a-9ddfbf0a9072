package com.foshan.service.community.impl;

import com.foshan.entity.ColumnEntity;
import com.foshan.entity.RegionEntity;
import com.foshan.entity.ServiceEntity;
import com.foshan.entity.UpshelfColumnEntity;
import com.foshan.entity.community.CommunityAssetEntity;
import com.foshan.entity.community.CommunityColumnEntity;
import com.foshan.entity.community.CommunityEventsEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.RegionForm;
import com.foshan.form.community.CommunityColumnForm;
import com.foshan.form.community.request.CommunityColumnReq;
import com.foshan.form.community.response.communityColumn.GetCommunityColumnListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.column.AddColumnRes;
import com.foshan.form.response.column.ModifyColumnRes;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityColumnService;
import com.foshan.util.CodeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static java.util.stream.Collectors.groupingBy;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Transactional
@Service("communityColumnService")
public class CommunityColumnServiceImpl extends GenericCommunityService implements ICommunityColumnService {

    @Override
    @SuppressWarnings("unchecked")
    public IResponse getCommunityColumnList(CommunityColumnReq req) {
        GetCommunityColumnListRes res = new GetCommunityColumnListRes();

        if (null != req.getColumnId()){

            CommunityColumnEntity column = communityColumnDao.get(req.getColumnId());
            if (null != column){
                res.setColumnId(column.getId());
                res.setParentColumnId(column.getParentColumn() == null ? 0 : column.getParentColumn().getId());
                res.setServiceId(column.getService().getId());
                res.setColumnCode(column.getColumnCode());
                res.setColumnName(column.getColumnName());
                res.setColumnType(column.getColumnType());
                res.setColumnState(column.getColumnState());
                res.setCommendFlag(column.getCommendFlag());
                res.setMappingFolderId(column.getMappingFolderId());
                res.setMappingFolderName(column.getMappingFolderName());
                res.setMappingSystem(column.getMappingSystem());
                res.setTargetType(column.getTargetType());
                res.setColumnImage(getAsset(column.getColumnImage()));
                res.setColumnPhoneImage(getAsset(column.getColumnPhoneImage()));
                res.setIsGlobal(column.getIsGlobal());
                res.setState(column.getState());
                
        		StringBuilder hql = new StringBuilder("select distinct a from CommunityAssetEntity  a   where  a.auditState in(0,1) " + 
                		" and a.id in(select b.resourceId from UpshelfColumnEntity b where  b.column.id ="+req.getColumnId()+")");
        		List<CommunityAssetEntity> list = communityAssetDao.getListByHql(hql.toString(), "");
        		Map<Integer, List<CommunityAssetEntity>> collect1 = (Map<Integer, List<CommunityAssetEntity>>) list
        			    .parallelStream().collect(groupingBy(CommunityAssetEntity::getAuditState));
        		res.setFirstAuditCount(collect1.containsKey(0) ? collect1.get(0).size() : 0);
        		res.setFinalAuditCount(collect1.containsKey(1) ? collect1.get(1).size() : 0);

                Integer serviceId = column.getService().getId();
                if (null != req.getDepth() && req.getDepth() > 1) {
                    // 查找子栏目


                    res.getSubColumnList()
                            .addAll((List<CommunityColumnForm>) communityColumnList(column.getSubColumnList(), serviceId, column.getId(),
                                    req.getDepth(), EntityContext.COUNT_VISIT_OFF, null, null,
                                    req.getTargetType(),req.getColumnState(),req.getColumnName()).get("columnList"));
                } else if (null == req.getDepth()) {
                    // 查找子栏目
                    res.getSubColumnList()
                            .addAll((List<CommunityColumnForm>) communityColumnList(column.getSubColumnList(), serviceId, column.getId(),
                                    contextInfo.getDefaultGetDataDepth(), EntityContext.COUNT_VISIT_OFF, null, null,
                                    req.getTargetType(),req.getColumnState(),req.getColumnName())
                                    .get("columnList"));
                }
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);


            }else{
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO+"要获取列表的栏目不存在！！！");
            }


        }else{
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }

    @Override
    @Audit(operate = "新增社区栏目")
    public IResponse addCommunityColumn(CommunityColumnReq req) {
        AddColumnRes res = new AddColumnRes();

        CommunityColumnEntity column = new CommunityColumnEntity();
        if (null != req.getServiceId() && StringUtils.isNotEmpty(req.getColumnName())) {
            ServiceEntity service = serviceDao.get(req.getServiceId());
            if (null != service) {
                Integer columnId;
                Integer columnCode = (Integer) communityColumnDao.createQuery("select max(substring(a.columnCode,5)+0)+1 from ColumnEntity a").uniqueResult();
                column.setColumnCode(StringUtils.isNotEmpty(req.getColumnCode()) ? req.getColumnCode()
                        : CodeUtil.codeGenerator(null != columnCode ? columnCode : 1, 10, "MANU"));
                column.setColumnName(req.getColumnName());
                column.setColumnInfo(req.getColumnInfo());
                column.setColumnType(
                        null != req.getColumnType() ? req.getColumnType() : EntityContext.COLUMN_TYPE_IMAGE);
                column.setColumnState(
                        null != req.getColumnState() ? req.getColumnState() : EntityContext.RECORD_STATE_VALID);
                column.setCommendFlag(
                        null != req.getCommendFlag() ? req.getCommendFlag() : EntityContext.COMMENT_FLAG_INVALID);
                column.setTargetType(
                        null != req.getTargetType() ? req.getTargetType() : EntityContext.COLUMN_TARGET_TYPE_COMMON);
                column.setMappingFolderId(req.getMappingFolderId());
                column.setMappingSystem(
                        null != req.getMappingSystem() ? req.getMappingSystem() : EntityContext.COLUMN_MAPPING_BO);
                column.setMappingFolderName(req.getMappingFolderName());
                column.setIsGlobal(null != req.getIsGlobal() ? req.getIsGlobal() : EntityContext.RECORD_STATE_INVALID);
                column.setState(null != req.getState() ? req.getState() : EntityContext.RECORD_STATE_VALID);
                column.setService(service);
                column.setOrders(null!=req.getOrders() ? req.getOrders() :0);
                if (null != req.getColumnImageId()) {
                    column.setColumnImage(assetDao.get(req.getColumnImageId()));
                }

                if (null != req.getColumnPhoneImageId()) {
                    column.setColumnPhoneImage(assetDao.get(req.getColumnPhoneImageId()));
                }
                // 设置栏目绑定区域，默认省节点区域
                if (StringUtils.isNotEmpty(req.getRegionIds())) {
                    String[] regionIds = req.getRegionIds().split(",");
                    List<RegionEntity> regionList = new ArrayList<RegionEntity>();
                    for (String regionId : regionIds) {
                        regionList.add(regionDao.get(Integer.parseInt(regionId)));
                    }
                    column.setRegionList(regionList);
                } else {
                    RegionEntity region = regionDao.getUniqueByHql("from RegionEntity a where a.regionLevel=1");
                    column.getRegionList().add(region);
                }

                if (null != req.getParentColumnId()) {
                    ColumnEntity parentColumn = communityColumnDao.get(req.getParentColumnId());
                    if (null != parentColumn) {
                        Integer columnOrder = (Integer) communityColumnDao.createQuery(
                                "select max(a.orders)+1 from ColumnEntity a inner join a.parentColumn b where b.id="
                                        + parentColumn.getId())
                                .uniqueResult();
                        column.setColumnLevel(parentColumn.getColumnLevel() + 1);
                        column.setColumnPath(parentColumn.getColumnPath() + "/" + req.getColumnName());
                        column.setOrders(
                                null != req.getOrders() ? req.getOrders() : null != columnOrder ? columnOrder : 1);
                        column.setParentColumn(parentColumn);
                        // 保存新增栏目，返回栏目ID
                        columnId = (Integer) communityColumnDao.save(column);
                    } else {
                        res.setRet("0001");
                        res.setRetInfo("新增栏目的父栏目不存在！！！");
                        return res;
                    }
                } else {
                    if (null == service.getColumn()) {
                        Integer columnOrder = (Integer) communityColumnDao
                                .createQuery("select max(a.orders)+1 from ColumnEntity a where a.columnLevel=1")
                                .uniqueResult();
                        column.setOrders(
                                null != req.getOrders() ? req.getOrders() : null != columnOrder ? columnOrder : 1);
                        column.setColumnLevel(1);
                        column.setColumnPath(req.getColumnName());
                        columnId = (Integer) communityColumnDao.save(column);
                        service.setColumn(column);
                    } else {
                        res.setRet("0001");
                        res.setRetInfo("当前业务已经存在根栏目，确认后再添加！！！");
                        return res;
                    }
                }


                // 构造返回值
                res.setColumnId(columnId);
                res.setServiceId(req.getServiceId());
                res.setColumnCode(req.getColumnCode());
                res.setColumnName(req.getColumnName());
                res.setParentColumnId(req.getParentColumnId());
                column.getRegionList().forEach(o -> res.getRegionList()
                        .add(new RegionForm(o.getId(), o.getRegionCode(), o.getRegionName(),
                                o.getParentRegion().getId(), o.getParentRegion().getRegionName(),
                                o.getStartRegionCode(), o.getEndRegionCode(), o.getRegionLevel())));
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);


            } else {
                res.setServiceId(req.getServiceId());
                res.setRet("0001");
                res.setRetInfo("业务不存在，不能增加栏目！！！");
                return res;

            }

        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }


        return res;
    }

    @Override
    @Audit(operate = "修改社区栏目")
    public IResponse modifyCommunityColumn(CommunityColumnReq req) {
        ModifyColumnRes res = new ModifyColumnRes();

        if (null != req.getColumnId()) {
            CommunityColumnEntity column = communityColumnDao.get(req.getColumnId());
            if (null != column) {
                String oldColumnName = column.getColumnName();
                column.setColumnCode(
                        StringUtils.isNotEmpty(req.getColumnCode()) ? req.getColumnCode() : column.getColumnCode());
                column.setColumnPath(StringUtils.isNotEmpty(req.getColumnName())
                        ? column.getColumnPath().replaceAll(oldColumnName, req.getColumnName())
                        : column.getColumnPath());
                column.setColumnName(
                        StringUtils.isNotEmpty(req.getColumnName()) ? req.getColumnName() : column.getColumnName());
                column.setColumnInfo(
                        StringUtils.isNotEmpty(req.getColumnInfo()) ? req.getColumnInfo() : column.getColumnInfo());
                column.setColumnType(null != req.getColumnType() ? req.getColumnType() : column.getColumnType());
                column.setColumnState(null != req.getColumnState() ? req.getColumnState() : column.getColumnState());
                column.setState(null != req.getState() ? req.getState() : column.getState());
                column.setCommendFlag(null != req.getCommendFlag() ? req.getCommendFlag() : column.getCommendFlag());
                column.setOrders(null != req.getOrders() ? req.getOrders() : 0);
                column.setTargetType(
                        null != req.getTargetType() ? req.getTargetType() : EntityContext.COLUMN_TARGET_TYPE_COMMON);
                column.setMappingFolderId(StringUtils.isNotEmpty(req.getMappingFolderId()) ? req.getMappingFolderId()
                        : column.getMappingFolderId());
                column.setMappingSystem(
                        null != req.getMappingSystem() ? req.getMappingSystem() : EntityContext.COLUMN_MAPPING_BO);
                column.setMappingFolderName(
                        StringUtils.isNotEmpty(req.getMappingFolderName()) ? req.getMappingFolderName()
                                : column.getMappingFolderName());
                column.setIsGlobal(null != req.getIsGlobal() ? req.getIsGlobal() : column.getIsGlobal());
                if (null != req.getColumnImageId()
                        && (null == column.getColumnImage() || (null != column.getColumnImage()
                        && !column.getColumnImage().getId().equals(req.getColumnImageId())))) {
                    column.setColumnImage(assetDao.get(req.getColumnImageId()));
                }
                if (null != req.getColumnPhoneImageId()
                        && ((null == column.getColumnPhoneImage()) || (null != column.getColumnPhoneImage()
                        && !column.getColumnPhoneImage().getId().equals(req.getColumnPhoneImageId())))) {
                    column.setColumnPhoneImage(assetDao.get(req.getColumnPhoneImageId()));
                }
                if (null != req.getParentColumnId()) {
                    ColumnEntity parentColumn = communityColumnDao.get(req.getParentColumnId());
                    if (null != parentColumn) {
                        if (null == column.getParentColumn() || !req.getParentColumnId().equals(column.getParentColumn().getId())) {
                            column.setParentColumn(parentColumn);
                            column.setColumnPath(parentColumn.getColumnPath() + "|"
                                    + (StringUtils.isNotEmpty(req.getColumnName()) ? req.getColumnName()
                                    : column.getColumnName()));
                            column.setColumnLevel(parentColumn.getColumnLevel() + 1);
                        }
                    } else {
                        res.setRet("0001");
                        res.setRetInfo("要修改栏目的父栏目不存在！！！");
                        return res;
                    }
                } else {
                    if (null != column.getParentColumn()) {
                        column.setParentColumn(null);
                        column.setColumnLevel(1);
                        column.setColumnPath(StringUtils.isNotEmpty(req.getColumnName()) ? req.getColumnName()
                                : column.getColumnName());
                    }
                }

                // 修改栏目绑定区域
                if (StringUtils.isNotEmpty(req.getRegionIds())) {
                    column.setRegionList(null);
                    String[] regionIds = req.getRegionIds().split(",");
                    List<RegionEntity> regionList = new ArrayList<RegionEntity>();
                    for (String regionId : regionIds) {
                        regionList.add(regionDao.get(Integer.parseInt(regionId)));
                    }
                    column.setRegionList(regionList);
                }

                communityColumnDao.saveOrUpdate(column);
                res.setColumnCode(req.getColumnCode());
                res.setColumnName(req.getColumnName());
                res.setParentColumnId(req.getParentColumnId());
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

            } else {
                res.setRet("0001");
                res.setRetInfo("要修改的栏目不存在！！！");
            }

        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }


        return res;
    }

    @Override
    @Audit(operate = "删除社区栏目")
    public IResponse deleteCommunityColumn(CommunityColumnReq req) {
        GenericResponse res = new GenericResponse();

        if (null != req.getColumnId()){
            CommunityColumnEntity column = communityColumnDao.get(req.getColumnId());
            if (null != column){
                column.getParentColumn().getSubColumnList().remove(column);
                column.setParentColumn(null);
                communityColumnDao.delete(column);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

            }else{
                res.setRet("0001");
                res.setRetInfo("栏目不存在！！！");
            }
        }else{
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }

    @Override
    @SuppressWarnings("unchecked")
    public IResponse getCommunityColumnInfo(CommunityColumnReq req) {
        GetCommunityColumnListRes res = new GetCommunityColumnListRes();

        if (null != req.getColumnId()){
            CommunityColumnEntity column = communityColumnDao.get(req.getColumnId());
            if (null != column){
                res.setTargetType(null != column.getTargetType()?column.getTargetType():null);
                res.setColumnId(column.getId());
                res.setParentColumnId(column.getParentColumn() == null ? 0 : column.getParentColumn().getId());
                res.setServiceId(column.getService().getId());
                res.setColumnCode(column.getColumnCode());
                res.setColumnName(column.getColumnName());
                res.setColumnType(column.getColumnType());
                res.setColumnState(column.getColumnState());
                res.setCommendFlag(column.getCommendFlag());
                res.setMappingFolderId(column.getMappingFolderId());
                res.setMappingFolderName(column.getMappingFolderName());
                res.setMappingSystem(column.getMappingSystem());
                res.setTargetType(column.getTargetType());
                res.setColumnImage(getAsset(column.getColumnImage()));
                res.setColumnPhoneImage(getAsset(column.getColumnPhoneImage()));
                res.setOrders(column.getOrders());
                res.setColumnInfo(column.getColumnInfo());
                res.setIsGlobal(column.getIsGlobal());
                res.setState(column.getState());
                Integer serviceId = column.getService().getId();
                req.setDepth(1);
                // 查找子栏目
                res.getSubColumnList()
                        .addAll((List<CommunityColumnForm>) communityColumnList(column.getSubColumnList(), serviceId, column.getId(),
                                req.getDepth(), EntityContext.COUNT_VISIT_OFF, null, null,
                                req.getTargetType(),req.getColumnState(),req.getColumnName()).get("columnList"));

                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);


            }else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO+"要获取列表的栏目不存在！！！");
            }

        }else{
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }

    @Override
    public IResponse getServiceCommunityRootColumn(CommunityColumnReq req) {
        return null;
    }


    @Override
    public IResponse getCommunityColumnAssetList(CommunityColumnReq req) {
        return null;
    }
}
