package com.foshan.service.community.impl;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.foshan.dao.generic.Page;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityEventNotificationEntity;
import com.foshan.entity.community.CommunityEventsEntity;
import com.foshan.entity.community.vo.MeterRecordVo;
import com.foshan.entity.community.vo.RefundInfoVo;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityEventNotificationForm;
import com.foshan.form.community.request.CommunityEventNotificationReq;
import com.foshan.form.community.request.CommunityEventsReq;
import com.foshan.form.community.response.communityEventNotification.GetCommunityEventNotificationInfoRes;
import com.foshan.form.community.response.communityEventNotification.GetCommunityEventNotificationListRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.ICommunityEventNotificationService;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;

@Transactional
@Service("communityEventNotificationService")
public  class CommunityEventNotificationServiceImpl extends GenericCommunityService implements ICommunityEventNotificationService {

	@Override
	public IResponse getCommunityEventNotificationList(CommunityEventNotificationReq req) {
		GetCommunityEventNotificationListRes res = new GetCommunityEventNotificationListRes();
		Page<CommunityEventNotificationEntity> page = new Page<CommunityEventNotificationEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityEventNotificationEntity a inner join a.event b"
				+ " where a.state="+EntityContext.RECORD_STATE_VALID );
		hql.append(null!=req.getCategory() ? " and a.category="+req.getCategory() : "")
			.append(null!=req.getEventsId() ? " and a.event.id="+req.getEventsId() : "")
			.append(StringUtils.isNotEmpty(req.getTitle()) ? " and a.title like'"+req.getTitle()+"'" :"")
			.append(StringUtils.isNotEmpty(req.getOrderCode()) ? " and b.orderCode like'%"+req.getOrderCode()+"%'" :"")
			.append(StringUtils.isNotEmpty(req.getSubmitter()) ? " and a.title like'%"+req.getSubmitter()+"%'" :"");
		
		hql.append(" ORDER BY a.createTime desc");
		page = communityEventNotificationDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityEventNotificationForm eventNotificationForm = new CommunityEventNotificationForm();
			eventNotificationForm.setCategory(o.getCategory());
			eventNotificationForm.setEventsId(o.getEvent().getId());
			eventNotificationForm.setFileForm(getAsset(o.getNotificationFile()));
			eventNotificationForm.setNotificationId(o.getId());
			eventNotificationForm.setCreateTime(null != o.getCreateTime()? 
					DateUtil.formatLongFormat(o.getCreateTime()) : "");
			eventNotificationForm.setLastModifyTime(null != o.getLastModifyTime()? 
					DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
			eventNotificationForm.setState(o.getState());
			eventNotificationForm.setSubmitter(StringUtils.isNotEmpty(o.getSubmitter()) ? o.getSubmitter() :"");
			eventNotificationForm.setTitle(StringUtils.isNotEmpty(o.getTitle()) ? o.getTitle() :"" );
			eventNotificationForm.setOrderCode(o.getEvent().getOrderCode());
			CommunityEstateEntity estate = (CommunityEstateEntity) o.getEvent().getProperty();
			eventNotificationForm.setUnitcode(estate.getUnitCode());
			try {
				if(StringUtils.isNoneEmpty(o.getEvent().getCentent())&&o.getEvent().getCentent().contains("itemName")) {
					Map<String,String> cententMap = new HashMap<String,String>();
					cententMap =mapper.readValue(o.getEvent().getCentent(), Map.class);
					if(cententMap.containsKey("itemName")) {
						eventNotificationForm.setCentent(cententMap.get("itemName"));
					}else {
						eventNotificationForm.setCentent(o.getEvent().getCentent());
					}
				}else {
					eventNotificationForm.setCentent(o.getEvent().getCentent());
				}

			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JSONException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			
			res.getEventNotificationList().add(eventNotificationForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addCommunityEventNotification(CommunityEventNotificationReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		if( userObj instanceof PlatformUserEntity){
			PlatformUserEntity user = (PlatformUserEntity)userObj;
			if (null != req.getEventsId() && null!=req.getFileId() && null!=req.getCategory()) {
				CommunityEventsEntity event = communityEventsDao.get(req.getEventsId());
				if(null == event) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				CommunityEventNotificationEntity notification = new CommunityEventNotificationEntity();
				notification.setCategory(req.getCategory());
				notification.setEvent(event);
				notification.setState(EntityContext.RECORD_STATE_VALID);
				notification.setLastModifyTime(new Timestamp(new Date().getTime()));
				notification.setNotificationFile(assetDao.get(req.getFileId()));
				notification.setSubmitter(user.getName());
				String str = "";
				switch(req.getCategory()) {
				case 0:
					str = "暂停施工通知书";
					break;
				case 1:
					str = "安全告知函";
					break;
				case 2:
					str = "复工通知书";
					break;
				case 3:
					str = "违规整改告知函";
					break;
				default:
					break;	
				}
				CommunityEstateEntity estate = (CommunityEstateEntity) event.getProperty();
				notification.setTitle(StringUtils.isNotEmpty(req.getTitle()) ? req.getTitle():estate.getUnitCode()+str);
				
				communityEventNotificationDao.save(notification);
				
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}	
		}

		return res;
	}

	@Override
	public IResponse modifyCommunityEventNotification(CommunityEventNotificationReq req) {
		GenericResponse res = new GenericResponse();
		if (null!=req.getNotificationId()) {
			Object userObj = getPrincipal(true);
			if (null == userObj) {
				res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
				return res;
			}
			if( userObj instanceof PlatformUserEntity){
				PlatformUserEntity user = (PlatformUserEntity)userObj;
				CommunityEventNotificationEntity notification = communityEventNotificationDao.get(req.getNotificationId()) ;
				if(null != notification){
					notification.setNotificationFile(null!=req.getFileId() ? assetDao.get(req.getFileId())
							: notification.getNotificationFile());
					notification.setTitle(StringUtils.isNotEmpty(req.getTitle()) ? req.getTitle():notification.getTitle());
					notification.setSubmitter(user.getName());
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				}else{
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				}
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteCommunityEventNotification(CommunityEventNotificationReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getNotificationId()) {
			CommunityEventNotificationEntity notification = communityEventNotificationDao.get(req.getNotificationId());
			if (null != notification) {
				notification.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse getCommunityEventNotificationInfo(CommunityEventNotificationReq req) {
		GetCommunityEventNotificationInfoRes res = new GetCommunityEventNotificationInfoRes();
		if (null != req.getNotificationId()) {
			CommunityEventNotificationEntity notification = communityEventNotificationDao.get(req.getNotificationId());
			if (null != notification) {
				CommunityEventNotificationForm eventNotificationForm = new CommunityEventNotificationForm();
				eventNotificationForm.setCategory(notification.getCategory());
				eventNotificationForm.setEventsId(notification.getEvent().getId());
				eventNotificationForm.setFileForm(getAsset(notification.getNotificationFile()));
				eventNotificationForm.setNotificationId(notification.getId());
				eventNotificationForm.setCreateTime(null != notification.getCreateTime()? 
						DateUtil.formatLongFormat(notification.getCreateTime()) : "");
				eventNotificationForm.setLastModifyTime(null != notification.getLastModifyTime()? 
						DateUtil.formatLongFormat(notification.getLastModifyTime()) : "");
				eventNotificationForm.setState(notification.getState());
				eventNotificationForm.setSubmitter(StringUtils.isNotEmpty(notification.getSubmitter()) ? notification.getSubmitter() :"");
				eventNotificationForm.setTitle(StringUtils.isNotEmpty(notification.getTitle()) ? notification.getTitle() :"" );
				eventNotificationForm.setOrderCode(notification.getEvent().getOrderCode());
				CommunityEstateEntity estate = (CommunityEstateEntity) notification.getEvent().getProperty();
				eventNotificationForm.setUnitcode(estate.getUnitCode());
				try {
					if(StringUtils.isNoneEmpty(notification.getEvent().getCentent())&&notification.getEvent().getCentent().contains("itemName")) {
						Map<String,String> cententMap = new HashMap<String,String>();
						cententMap =mapper.readValue(notification.getEvent().getCentent(), Map.class);
						if(cententMap.containsKey("itemName")) {
							eventNotificationForm.setCentent(cententMap.get("itemName"));
						}else {
							eventNotificationForm.setCentent(notification.getEvent().getCentent());
						}
					}else {
						eventNotificationForm.setCentent(notification.getEvent().getCentent());
					}

				} catch (JsonParseException e) {
					e.printStackTrace();
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JSONException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
				res.setEventNotificationForm(eventNotificationForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse exportCommunityEventNotification(CommunityEventNotificationReq req, HttpServletResponse response) {
		ExportExcelRes res = new ExportExcelRes();
		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("title", "标题");
		heardMap.put("orderCode", "编码");
		heardMap.put("unitCode", "单元");
		heardMap.put("userName", "业主姓名");
		heardMap.put("phone", "业主电话");
		heardMap.put("centent", "申请项目");
		heardMap.put("submitter", "提交人");
		heardMap.put("category", "分类");


		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();

		Map<Integer, MeterRecordVo> resultList = new TreeMap<>();
		StringBuilder hql = new StringBuilder("select distinct a from CommunityEventNotificationEntity a inner join a.event b"
				+ " where a.state="+EntityContext.RECORD_STATE_VALID );
		hql.append(null!=req.getCategory() ? " and a.category="+req.getCategory() : "")
			.append(null!=req.getEventsId() ? " and a.event.id="+req.getEventsId() : "")
			.append(StringUtils.isNotEmpty(req.getTitle()) ? " and a.title like'"+req.getTitle()+"'" :"")
			.append(StringUtils.isNotEmpty(req.getOrderCode()) ? " and a.title like'"+req.getOrderCode()+"'" :"")
			.append(StringUtils.isNotEmpty(req.getSubmitter()) ? " and a.title like'"+req.getSubmitter()+"'" :"");
		
		hql.append(" ORDER BY a.createTime desc");
		List<CommunityEventNotificationEntity> list = communityEventNotificationDao.getListByHql(hql.toString(), "");

		list.forEach(o -> {
			Map<String, Object> map = new HashMap<String, Object>();
			CommunityEstateEntity property = (CommunityEstateEntity) o.getEvent().getProperty();
			map.put("orderCode", StringUtils.isNotEmpty(o.getEvent().getOrderCode()) ? o.getEvent().getOrderCode():"");
			map.put("title", StringUtils.isNotEmpty(o.getTitle()) ? o.getTitle():"");
			map.put("submitter", StringUtils.isNotEmpty(o.getSubmitter()) ? o.getSubmitter():"");
			map.put("unitCode", property.getUnitCode());
			map.put("userName",o.getEvent().getMember().getUserName());
			map.put("phone", o.getEvent().getMember().getPhone());
			Map<String,String> cententMap = new HashMap<String,String>();
			switch(o.getCategory()) {
			case 0:
				map.put("category", "暂停施工通知书");
				break;
			case 1:
				map.put("category", "安全告知函");
				break;
			case 2:
				map.put("category", "复工通知书");
				break;
			case 3:
				map.put("category", "违规整改告知函");
				break;
			default:
				map.put("category", "");
				break;	
			}

			try {
				if(StringUtils.isNoneEmpty(o.getEvent().getCentent())&&o.getEvent().getCentent().contains("itemName")) {
					cententMap =mapper.readValue(o.getEvent().getCentent(), Map.class);
					if(cententMap.containsKey("itemName")) {
						map.put("centent", cententMap.get("itemName"));
					}else {
						map.put("centent", o.getEvent().getCentent());
					}
				}else {
					map.put("centent", o.getEvent().getCentent());
				}

			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JSONException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			dataList.add(map);
		});

		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

		return res;
	}
	
}
