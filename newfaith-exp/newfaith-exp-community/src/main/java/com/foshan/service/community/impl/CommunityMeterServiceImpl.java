package com.foshan.service.community.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityBuildingEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMeterAttributesEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityMeterFormulaEntity;
import com.foshan.entity.community.CommunityPropertyEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityMeterAttributesForm;
import com.foshan.form.community.CommunityMeterForm;
import com.foshan.form.community.CommunityMeterFormulaForm;
import com.foshan.form.community.CommunityPayItemsForm;
import com.foshan.form.community.request.CommunityMeterReq;
import com.foshan.form.community.response.communityMeter.AddCommunityMeterRes;
import com.foshan.form.community.response.communityMeter.GetCommunityMeterInfoRes;
import com.foshan.form.community.response.communityMeter.GetCommunityMeterListRes;
import com.foshan.form.community.response.communityMeter.ModifyCommunityMeterRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityMeterService;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.community.CommunityCache;

@Transactional
@Service("communityMeterService")
public class CommunityMeterServiceImpl extends GenericCommunityService implements ICommunityMeterService {

	@Override
	public IResponse getCommunityMeterList(CommunityMeterReq req) {
		GetCommunityMeterListRes res = new GetCommunityMeterListRes();
		Page<CommunityMeterEntity> page = new Page<CommunityMeterEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

		page = communityMeterDao.queryPage(page, queryHql(req));

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		if (null != req.getDepth() && req.getDepth() > 1) {
			res.getCommunityMeterList().addAll(getMeterList(page.getResultList(), req.getDepth(), req.getIsRent()));
		} else if (null == req.getDepth()) {
			res.getCommunityMeterList()
					.addAll(getMeterList(page.getResultList(), contextInfo.getDefaultGetDataDepth(), req.getIsRent()));
		}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	protected String queryHql(CommunityMeterReq req) {
		StringBuilder hql = new StringBuilder("select distinct a from CommunityMeterEntity a  ");
		hql.append(null != req.getMeterAttributesId() || StringUtils.isNotEmpty(req.getCategoryList())
				? " inner join a.meterAttributes b "
				: "").append(null != req.getFormulaId() ? " inner join a.meterFormula c" : "")
				.append(null != req.getExceptionState() ? " inner join a.meterRecordList d" : "")
				.append(StringUtils.isNotEmpty(req.getPropertyIdList()) ? " inner join a.propertyList e " : "");
		hql.append(" where 1=1 ").append(null != req.getIsCommon() ? " and a.isCommon=" + req.getIsCommon() : "")
				.append(StringUtils.isNotEmpty(req.getMeterName())
						? " and a.meterName like'%" + req.getMeterName() + "%'"
						: "")
				.append(StringUtils.isNotEmpty(req.getLastAllocateDate())?
						" and a.lastAllocateDate like'%"+req.getLastAllocateDate()+ "%'":"")
				.append(StringUtils.isNotEmpty(req.getMeterCode())
						? " and a.meterCode like'%" + req.getMeterCode() + "%'"
						: "")
				.append(null != req.getIsDisabled() ? " and a.isDisabled=" + req.getIsDisabled() : "")
				.append(null != req.getAllocationMethod() ? " and a.allocationMethod=" + req.getAllocationMethod() : "")
				.append(null != req.getIsApportioned() ? " and a.isApportioned=" + req.getIsApportioned() : "")
				.append(null != req.getParentMeterId() ? " and a.parentMeter.id=" + req.getParentMeterId()
						: " and a.parentMeter is null")
				.append(null != req.getMeterAttributesId() ? " and b.id=" + req.getMeterAttributesId() : "")
				.append(null != req.getFormulaId() ? " and c.id=" + req.getFormulaId() : "")
				.append(null != req.getAllocationPeriod() ? " and a.allocationPeriod=" + req.getAllocationPeriod() : "")
				.append(StringUtils.isNotEmpty(req.getCategoryList())
						? " and b.category in(" + req.getCategoryList() + ")"
						: "")
				.append(StringUtils.isNotEmpty(req.getPayItemsName())
						? " and a.payItems.itemsName like'%" + req.getPayItemsName() + "%'"
						: "")
				.append(null != req.getExceptionState() ? " and d.exceptionState=" + req.getExceptionState() : "")
				.append(null != req.getState() ? " and a.state=" + req.getState()
						: " and a.state=" + EntityContext.RECORD_STATE_VALID)
				.append(StringUtils.isNotEmpty(req.getPropertyIdList())
						? " and e.id in(" + req.getPropertyIdList() + ")"
						: "");
		hql.append(" ORDER BY a.meterName asc");
		return hql.toString();
	}

	@SuppressWarnings("unchecked")
	public IResponse exportCommunityMeterList(HttpServletResponse response, CommunityMeterReq req) {
		ExportExcelRes res = new ExportExcelRes();
		List<CommunityMeterEntity> list = communityMeterDao.getListByHql(queryHql(req), "");

		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("serialNumber", "序号");
		heardMap.put("category", "表类别");
		heardMap.put("meterName", "表名称");
		heardMap.put("meterCode", "表编号");
		heardMap.put("itemsName", "收费项目");
		heardMap.put("allocationMethod", "分摊类别");
		heardMap.put("attributeName", "表属性名称");
		heardMap.put("allocationPeriod", "分摊周期（月）");
		heardMap.put("lastAllocateDate", "上次分摊日期");
		

		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		ObjectMapper mapper = new ObjectMapper();
		Map<String, String> bankNoMap = null;
		try {
			bankNoMap = mapper.readValue(communityContextInfo.bankNo, Map.class);
		} catch (JsonMappingException e1) {
			e1.printStackTrace();
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}
		int serialNumber = 0;
		for (CommunityMeterEntity o : list) {
			Map<String, Object> map = new HashMap<String, Object>();
			serialNumber++;
			map.put("serialNumber", serialNumber + "");
			map.put("meterName", o.getMeterName());
			map.put("meterCode", o.getMeterCode());
			if (null != o.getPayItems()) {
				map.put("itemsName", o.getPayItems().getItemsName());
			} else {
				map.put("itemsName", "");
			}
			if (null != o.getMeterAttributes()) {
				Integer category = o.getMeterAttributes().getCategory();
				switch (category) {
				case 1:
					map.put("category", "电表");
					break;
				case 2:
					map.put("category", "水表");
					break;
				case 3:
					map.put("category", "临时表");
					break;
				case 4:
					map.put("category", "代收水表");
					break;
				case 5:
					map.put("category", "代收电表");
					break;
				default:
					map.put("category", "其他");
					break;
				}
				map.put("attributeName", o.getMeterAttributes().getAttributeName());
			} else {
				map.put("category", "");
				map.put("attributeName", "");
			}
			map.put("lastAllocateDate", null != o.getLastAllocateDate() ? DateUtil.formatLongFormat(o.getLastAllocateDate()) : "");
			map.put("allocationPeriod", null!=o.getAllocationPeriod() ? o.getAllocationPeriod().toString()+"":"");
			
			Integer allocationMethod = o.getAllocationMethod();
			if (null != allocationMethod) {
				switch (allocationMethod) {
				case 1:
					map.put("allocationMethod", "按面积分摊");
					break;
				case 2:
					map.put("allocationMethod", "自定义分摊");
					break;
				default:
					map.put("allocationMethod", "其他");
					break;
				}
			}

			dataList.add(map);
		}
		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "总表明细信息",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}
		return res;
	}

	@SuppressWarnings("unchecked")
	protected List<CommunityMeterForm> getMeterList(List<CommunityMeterEntity> meterList, Integer depth,
			Integer isRent) {
		List<CommunityMeterForm> meterFormList = new ArrayList<CommunityMeterForm>();
		depth--;
		for (CommunityMeterEntity o : meterList) {
			CommunityMeterForm communityMeterForm = new CommunityMeterForm();
			communityMeterForm.setCommunityMeterId(o.getId());
			communityMeterForm.setAllocationMethod(o.getAllocationMethod());
			communityMeterForm.setComment(o.getComment());
			communityMeterForm.setExpirationDates(
					null != o.getExpirationDates() ? DateUtil.formatLongFormat(o.getExpirationDates()) : "");
			communityMeterForm.setInitialData(null != o.getInitialData() ? o.getInitialData().toString() : "");
			communityMeterForm.setInstallationsite(o.getInstallationsite());
			communityMeterForm.setIsCommon(o.getIsCommon());
			communityMeterForm.setIsApportioned(o.getIsApportioned());
			communityMeterForm.setLevel(o.getLevel());
			communityMeterForm.setMeterCode(o.getMeterCode());
			communityMeterForm.setMeterName(o.getMeterName());
			communityMeterForm.setOldData(o.getOldData());
			communityMeterForm.setOldId(o.getOldId());
			communityMeterForm.setChargeCategory(o.getChargeCategory());
			communityMeterForm.setIsDisabled(o.getIsDisabled());
			communityMeterForm.setPayItemsName(o.getPayItemsName());
			communityMeterForm.setState(o.getState());
			communityMeterForm.setAllocationPeriod(o.getAllocationPeriod());
			communityMeterForm.setLastAllocateDate(
					null != o.getLastAllocateDate() ? DateUtil.formatLongFormat(o.getLastAllocateDate()) : "");
			if (null != o.getMeterAttributes()) {
				CommunityMeterAttributesForm communityMeterAttributesForm = new CommunityMeterAttributesForm();
				communityMeterAttributesForm.setCommunityMeterAttributesId(o.getMeterAttributes().getId());
				communityMeterAttributesForm.setUnitPrice(o.getMeterAttributes().getUnitPrice().toString());
				communityMeterAttributesForm.setAttributeName(o.getMeterAttributes().getAttributeName());
				communityMeterAttributesForm.setMeasureUnit(o.getMeterAttributes().getMeasureUnit());
				communityMeterAttributesForm.setCategory(o.getMeterAttributes().getCategory());
				communityMeterAttributesForm.setRanges(o.getMeterAttributes().getRanges());
				communityMeterForm.setMeterAttributesForm(communityMeterAttributesForm);
			}
			if (null != isRent && isRent == 1 && o.getPropertyList().size() > 0) {
				CommunityEstateEntity estate = (CommunityEstateEntity) o.getPropertyList().get(0);
				communityMeterForm.setUnitCode(estate.getUnitCode());
			}
			CommunityMeterFormulaForm communityMeterFormulaForm = new CommunityMeterFormulaForm();
			if (null != o.getMeterFormula()) {
				communityMeterFormulaForm.setCommunityMeterFormulaId(o.getMeterFormula().getId());
				communityMeterFormulaForm.setFormulaName(o.getMeterFormula().getFormulaName());
				Map<String, BigDecimal> paras = null;
				try {
					paras = mapper.readValue(o.getMeterFormula().getDynamicParameter(),
							new HashMap<String, BigDecimal>().getClass());
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				communityMeterFormulaForm.setDynamicParameter(paras);
			} else {
				communityMeterFormulaForm.setFormulaName("");
			}
			if (null != o.getPayItems()) {
				CommunityPayItemsForm communityPayItemsForm = new CommunityPayItemsForm();
				communityPayItemsForm.setCommunityPayItemsId(o.getPayItems().getId());
				communityPayItemsForm.setChargeCategory(o.getPayItems().getChargeCategory());
				communityPayItemsForm.setItemsName(o.getPayItems().getItemsName());
				communityMeterForm.setPayItemsForm(communityPayItemsForm);
			}
			communityMeterForm.setMeterFormulaForm(communityMeterFormulaForm);
			// 查询子栏目
			if (null != o.getSubMeterList() && o.getSubMeterList().size() > 0 && depth >= 1) {
				communityMeterForm.getSubMeterList().addAll(getMeterList(o.getSubMeterList(), depth, isRent));
			}
			meterFormList.add(communityMeterForm);
		}

		return meterFormList;
	}

	@Override
	@Audit(operate = "新增表")
	public IResponse addCommunityMeter(CommunityMeterReq req) {
		AddCommunityMeterRes res = new AddCommunityMeterRes();
		if (null != req.getMeterAttributesId()) {
			CommunityMeterFormulaEntity meterFormula = null;
			if (null != req.getFormulaId()) {
				meterFormula = communityMeterFormulaDao.get(req.getFormulaId());
			}
			CommunityMeterAttributesEntity meterAttributes = communityMeterAttributesDao
					.get(req.getMeterAttributesId());
			CommunityMeterEntity parentMeter = null;
			if (null == meterAttributes) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			if (null != req.getParentMeterId()) {
				parentMeter = communityMeterDao.get(req.getParentMeterId());
				if (null == parentMeter) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
			}
			StringBuilder hql = new StringBuilder("select distinct a from CommunityMeterEntity a where a.meterCode='"
					+ req.getMeterCode() + "' and state=1");
			List<CommunityMeterEntity> list = communityMeterDao.getListByHql(hql.toString(), "");
			if (null != list && list.size() > 0) {
				res.setRet(ResponseContext.RES_DATA_DUPLICATE_CODE);
				res.setRetInfo("新增失败，表编号有重复！");
				return res;
			}

			CommunityMeterEntity communityMeter = new CommunityMeterEntity();
			communityMeter.setAllocationMethod(req.getAllocationMethod());
			communityMeter.setComment(req.getComment());
			try {
				communityMeter.setExpirationDates(StringUtils.isNotEmpty(req.getExpirationDates())
						? DateUtil.parseLongFormat(req.getExpirationDates())
						: null);
			} catch (ParseException e) {
				e.printStackTrace();
			}
			communityMeter.setInitialData(
					StringUtils.isNotEmpty(req.getInitialData()) ? new BigDecimal(req.getInitialData()) : null);
			communityMeter.setInstallationsite(req.getInstallationsite());
			communityMeter.setIsCommon(req.getIsCommon());
			communityMeter.setIsApportioned(req.getIsApportioned());
			communityMeter.setAllocationPeriod(null != req.getAllocationPeriod() ? req.getAllocationPeriod() : 1);
			communityMeter.setLevel(req.getLevel());
			communityMeter.setMeterCode(req.getMeterCode());
			communityMeter.setMeterName(req.getMeterName());
			communityMeter.setOldData(req.getOldData());
			communityMeter.setOldId(req.getOldId());
			communityMeter.setMeterFormula(meterFormula);
			communityMeter.setLastModifyTime(new Timestamp(new Date().getTime()));
			communityMeter.setMeterAttributes(meterAttributes);
			communityMeter.setParentMeter(parentMeter);
			communityMeter.setChargeCategory(req.getChargeCategory());
			communityMeter.setIsDisabled(req.getIsDisabled());
			communityMeter.setPayItemsName(req.getPayItemsName());
			communityMeter
					.setPayItems(null != req.getPayItemId() ? communityPayItemsDao.get(req.getPayItemId()) : null);
			communityMeter.setState(EntityContext.RECORD_STATE_VALID);
			communityMeterDao.save(communityMeter);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "修改表")
	public IResponse modifyCommunityMeter(CommunityMeterReq req) {
		ModifyCommunityMeterRes res = new ModifyCommunityMeterRes();
		if (null != req.getCommunityMeterId() && null != req.getMeterAttributesId()) {
			CommunityMeterEntity communityMeter = communityMeterDao.get(req.getCommunityMeterId());
			CommunityMeterFormulaEntity meterFormula = null != req.getFormulaId()
					? communityMeterFormulaDao.get(req.getFormulaId())
					: null;
			CommunityMeterAttributesEntity meterAttributes = communityMeterAttributesDao
					.get(req.getMeterAttributesId());
			if (null != communityMeter || null != meterAttributes) {
				CommunityMeterEntity parentMeter = null;
				if (null != req.getParentMeterId()) {
					parentMeter = communityMeterDao.get(req.getParentMeterId());
					if (null == parentMeter) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
						return res;
					}
				}
				communityMeter.setAllocationMethod(req.getAllocationMethod());
				communityMeter.setComment(req.getComment());
				try {
					communityMeter.setExpirationDates(StringUtils.isNotEmpty(req.getExpirationDates())
							? DateUtil.parseLongFormat(req.getExpirationDates())
							: null);
				} catch (ParseException e) {
					e.printStackTrace();
				}
				communityMeter.setInitialData(
						StringUtils.isNotEmpty(req.getInitialData()) ? new BigDecimal(req.getInitialData()) : null);
				communityMeter.setInstallationsite(req.getInstallationsite());
				communityMeter.setIsCommon(req.getIsCommon());
				communityMeter.setLevel(req.getLevel());
				communityMeter.setIsApportioned(req.getIsApportioned());
				communityMeter.setAllocationPeriod(null != req.getAllocationPeriod() ? req.getAllocationPeriod()
						: communityMeter.getIsApportioned().equals(1) ? 1 : null);
				communityMeter.setMeterCode(req.getMeterCode());
				communityMeter.setMeterName(req.getMeterName());
				communityMeter.setMeterFormula(meterFormula);
				communityMeter.setMeterAttributes(meterAttributes);
				communityMeter.setParentMeter(parentMeter);
				communityMeter.setChargeCategory(req.getChargeCategory());
				communityMeter.setLastModifyTime(new Timestamp(new Date().getTime()));
				communityMeter.setIsDisabled(req.getIsDisabled());
				communityMeter.setPayItemsName(req.getPayItemsName());
				communityMeter.setPayItems(null != req.getPayItemId() ? communityPayItemsDao.get(req.getPayItemId())
						: communityMeter.getPayItems());
				try {
					communityMeter.setLastAllocateDate(StringUtils.isNotEmpty(req.getLastAllocateDate())
							? DateUtil.parseLongFormat(req.getLastAllocateDate())
							: null);
				} catch (ParseException e) {
					e.printStackTrace();
				}
				CommunityCache.meterList.put(communityMeter.getId(), communityMeter);
				res.setCommunityMeterId(communityMeter.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除表")
	public IResponse deleteCommunityMeter(CommunityMeterReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityMeterId()) {
			CommunityMeterEntity communityMeter = communityMeterDao.get(req.getCommunityMeterId());
			if (null != communityMeter) {
				// communityMeterDao.deleteById(req.getCommunityMeterId());
				communityMeter.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityMeterInfo(CommunityMeterReq req) {
		GetCommunityMeterInfoRes res = new GetCommunityMeterInfoRes();
		if (null != req.getCommunityMeterId()) {
			CommunityMeterEntity communityMeter = communityMeterDao.get(req.getCommunityMeterId());
			if (null != communityMeter) {
				CommunityMeterForm communityMeterForm = new CommunityMeterForm();
				if (null != communityMeter.getParentMeter()) {
					CommunityMeterForm parentMeterForm = new CommunityMeterForm();
					parentMeterForm.setCommunityMeterId(communityMeter.getParentMeter().getId());
					parentMeterForm.setMeterName(communityMeter.getParentMeter().getMeterName());
					communityMeterForm.setParentMeterForm(parentMeterForm);
				}
				communityMeterForm.setIsApportioned(communityMeter.getIsApportioned());
				communityMeterForm.setAllocationPeriod(
						null != communityMeter.getAllocationPeriod() ? communityMeter.getAllocationPeriod() : null);
				communityMeterForm.setCommunityMeterId(communityMeter.getId());
				communityMeterForm.setAllocationMethod(communityMeter.getAllocationMethod());
				communityMeterForm.setComment(communityMeter.getComment());
				communityMeterForm.setExpirationDates(null != communityMeter.getExpirationDates()
						? DateUtil.formatLongFormat(communityMeter.getExpirationDates())
						: "");
				communityMeterForm.setInitialData(
						null != communityMeter.getInitialData() ? communityMeter.getInitialData().toString() : "");
				communityMeterForm.setInstallationsite(communityMeter.getInstallationsite());
				communityMeterForm.setIsCommon(communityMeter.getIsCommon());
				communityMeterForm.setLevel(communityMeter.getLevel());
				communityMeterForm.setMeterCode(communityMeter.getMeterCode());
				communityMeterForm.setMeterName(communityMeter.getMeterName());
				communityMeterForm.setOldData(communityMeter.getOldData());
				communityMeterForm.setOldId(communityMeter.getOldId());

				communityMeterForm.setChargeCategory(communityMeter.getChargeCategory());
				communityMeterForm.setIsDisabled(communityMeter.getIsDisabled());
				communityMeterForm.setPayItemsName(communityMeter.getPayItemsName());
				communityMeterForm.setAllocationPeriod(communityMeter.getAllocationPeriod());
				communityMeterForm.setLastAllocateDate(null != communityMeter.getLastAllocateDate()
						? DateUtil.formatLongFormat(communityMeter.getLastAllocateDate())
						: "");
				if (null != req.getDepth() && req.getDepth() > 1) {
					communityMeterForm.getSubMeterList()
							.addAll(getMeterList(communityMeter.getSubMeterList(), req.getDepth(), req.getIsRent()));
				} else if (null == req.getDepth()) {
					communityMeterForm.getSubMeterList().addAll(getMeterList(communityMeter.getSubMeterList(),
							contextInfo.getDefaultGetDataDepth(), req.getIsRent()));
				}
				if (null != communityMeter.getMeterAttributes()) {
					CommunityMeterAttributesForm communityMeterAttributesForm = new CommunityMeterAttributesForm();
					communityMeterAttributesForm
							.setCommunityMeterAttributesId(communityMeter.getMeterAttributes().getId());
					communityMeterAttributesForm
							.setAttributeName(communityMeter.getMeterAttributes().getAttributeName());
					communityMeterForm.setMeterAttributesForm(communityMeterAttributesForm);
				}
				if (null != communityMeter.getMeterFormula()) {
					CommunityMeterFormulaForm communityMeterFormulaForm = new CommunityMeterFormulaForm();
					communityMeterFormulaForm.setCommunityMeterFormulaId(communityMeter.getMeterFormula().getId());
					communityMeterFormulaForm.setFormulaName(communityMeter.getMeterFormula().getFormulaName());
					communityMeterForm.setMeterFormulaForm(communityMeterFormulaForm);
				}
				if (null != communityMeter.getPayItems()) {
					CommunityPayItemsForm communityPayItemsForm = new CommunityPayItemsForm();
					communityPayItemsForm.setCommunityPayItemsId(communityMeter.getPayItems().getId());
					communityPayItemsForm.setChargeCategory(communityMeter.getPayItems().getChargeCategory());
					communityPayItemsForm.setItemsName(communityMeter.getPayItems().getItemsName());
					communityMeterForm.setPayItemsForm(communityPayItemsForm);
				}
				res.setCommunityMeterForm(communityMeterForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	@Audit(operate = "绑定表和单元")
	public IResponse bindingMeterAndEstate(CommunityMeterReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getCommunityMeterIdList())
				&& (StringUtils.isNotEmpty(req.getBuildingIdList()) || StringUtils.isNotEmpty(req.getDistrictIdList())
						|| StringUtils.isNotEmpty(req.getPropertyIdList()))) {
			List<CommunityMeterEntity> meterList = communityMeterDao.getListByHql(
					"select a from CommunityMeterEntity a where a.id in(" + req.getCommunityMeterIdList() + ")");
			if (null != meterList) {

//				 StringBuilder hql = new
//				 StringBuilder("select distinct a from CommunityEstateEntity a  " +
//				 "inner join a.meterList b where b.id="+req.getCommunityMeterId());
//				 List<CommunityEstateEntity> estateList =
//				 communityEstateDao.getListByHql(hql.toString(), ""); 
//				 estateList.forEach(o->{
//				 o.getMeterList().remove(communityMeter); });

				if (StringUtils.isNotEmpty(req.getBuildingIdList())) {
					String[] buildingIds = req.getBuildingIdList().split(",");
					for (String id : buildingIds) {
						CommunityBuildingEntity building = communityBuildingDao.get(Integer.valueOf(id));
						if (null != building.getPropertyList()) {
							building.getPropertyList().forEach(p -> {
								meterList.forEach(m -> {
									if (!m.getPropertyList().contains(p)) {
										m.getPropertyList().add(p);
									}
								});
							});
						}
					}
				} else if (StringUtils.isNotEmpty(req.getDistrictIdList())) {
					String[] districtIds = req.getDistrictIdList().split(",");
					for (String id : districtIds) {
						CommunityDistrictEntity district = communityDistrictDao.get(Integer.valueOf(id));
						if (null != district.getBuildingList()) {
							district.getBuildingList().forEach(o -> {
								if (null != o.getPropertyList()) {
									o.getPropertyList().forEach(p -> {
										meterList.forEach(m -> {
											if (!m.getPropertyList().contains(p)) {
												m.getPropertyList().add(p);
											}
										});
									});
								}
							});
						}
					}
				} else if (StringUtils.isNotEmpty(req.getPropertyIdList())) {
					String[] propertyId = req.getPropertyIdList().split(",");
					for (String id : propertyId) {
						CommunityPropertyEntity property = communityPropertyDao.get(Integer.valueOf(id));
//						if (null != property && !communityMeter.getPropertyList().contains(property)) {
//							communityMeter.getPropertyList().add(property);
//						}
						meterList.forEach(m -> {
							if (!m.getPropertyList().contains(property)) {
								m.getPropertyList().add(property);
							}
						});
					}
				}

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	@Audit(operate = "解绑表和单元")
	public IResponse unbindingMeterAndEstate(CommunityMeterReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityMeterId()
				&& (StringUtils.isNotEmpty(req.getBuildingIdList()) || StringUtils.isNotEmpty(req.getDistrictIdList())
						|| StringUtils.isNotEmpty(req.getPropertyIdList()))) {
			CommunityMeterEntity communityMeter = communityMeterDao.get(req.getCommunityMeterId());
			if (null != communityMeter) {
				if (StringUtils.isNotEmpty(req.getBuildingIdList())) {
					String[] buildingIds = req.getBuildingIdList().split(",");
					for (String id : buildingIds) {
						CommunityBuildingEntity building = communityBuildingDao.get(Integer.valueOf(id));
						if (null != building.getPropertyList()) {
							building.getPropertyList().forEach(p -> {
								if (null != p && communityMeter.getPropertyList().contains(p)) {
									communityMeter.getPropertyList().remove(p);
								}
							});
						}
					}
				} else if (StringUtils.isNotEmpty(req.getDistrictIdList())) {
					String[] districtIds = req.getDistrictIdList().split(",");
					for (String id : districtIds) {
						CommunityDistrictEntity district = communityDistrictDao.get(Integer.valueOf(id));
						if (null != district.getBuildingList()) {
							district.getBuildingList().forEach(o -> {
								if (null != o.getPropertyList()) {
									o.getPropertyList().forEach(p -> {
										if (null != p && communityMeter.getPropertyList().contains(p)) {
											communityMeter.getPropertyList().remove(p);
										}
									});
								}
							});
						}
					}
				} else if (StringUtils.isNotEmpty(req.getPropertyIdList())) {
					String[] propertyId = req.getPropertyIdList().split(",");
					for (String id : propertyId) {
						CommunityPropertyEntity property = communityPropertyDao.get(Integer.valueOf(id));
						if (null != property && communityMeter.getPropertyList().contains(property)) {
							communityMeter.getPropertyList().remove(property);
						}
					}
				}

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	@Audit(operate = "复制表分摊范围")
	public IResponse copeMeterRange(CommunityMeterReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getBeCopiedMeterIdList())
				&& StringUtils.isNotEmpty(req.getCommunityMeterIdList())) {
			List<CommunityMeterEntity> beCopiedMeterList = communityMeterDao.getListByHql(
					"select a from CommunityMeterEntity " + "a where a.id in(" + req.getBeCopiedMeterIdList() + ")");
			List<CommunityMeterEntity> meterList = communityMeterDao.getListByHql(
					"select a from CommunityMeterEntity " + "a where a.id in(" + req.getCommunityMeterIdList() + ")");
			if (null != meterList && null != beCopiedMeterList) {
				beCopiedMeterList.forEach(o -> {
					o.getPropertyList().forEach(p -> {
						meterList.forEach(m -> {
							if (!m.getPropertyList().contains(p)) {
								m.getPropertyList().add(p);
							}
						});
					});
				});
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

}