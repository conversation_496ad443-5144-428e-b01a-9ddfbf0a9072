package com.foshan.service.community;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityMeterReq;
import com.foshan.form.response.IResponse;

public interface ICommunityMeterService {
    public IResponse getCommunityMeterList(CommunityMeterReq req);
	public IResponse addCommunityMeter(CommunityMeterReq req);
	public IResponse modifyCommunityMeter(CommunityMeterReq req);
	public IResponse deleteCommunityMeter(CommunityMeterReq req);
	public IResponse getCommunityMeterInfo(CommunityMeterReq req);
	public IResponse bindingMeterAndEstate(CommunityMeterReq req);
	public IResponse unbindingMeterAndEstate(CommunityMeterReq req) ;
	public IResponse exportCommunityMeterList(HttpServletResponse response,CommunityMeterReq req);
	public IResponse copeMeterRange(CommunityMeterReq req);
}

