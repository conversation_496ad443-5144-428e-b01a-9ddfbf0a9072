package com.foshan.service.community.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.entity.community.CommunityFormulaTempleteEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityFormulaTempleteForm;
import com.foshan.form.community.request.CommunityFormulaTempleteReq;
import com.foshan.form.community.response.communityFormulaTemplete.CommunityFormulaTempleteRes;
import com.foshan.form.community.response.communityFormulaTemplete.GetCommunityFormulaTempleteListRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityFormulaTempleteService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional
@Service("communityFormulaTempleteService")
public class CommunityFormulaTempleteServiceImpl extends GenericCommunityService
		implements ICommunityFormulaTempleteService {

	@SuppressWarnings("unchecked")
	@Override
	@Audit(operate = "新增公式模版")
	public IResponse addFormulaTemplete(CommunityFormulaTempleteReq req) {
		// TODO Auto-generated method stub
		CommunityFormulaTempleteRes res = new CommunityFormulaTempleteRes();
		if (StringUtils.isNotEmpty(req.getTempleteName()) && StringUtils.isNotEmpty(req.getTempleteInfo())) {
			try {
				CommunityFormulaTempleteEntity templete = new CommunityFormulaTempleteEntity();
				templete.setLastModifyTime(Timestamp.valueOf(LocalDateTime.now()));
				templete.setTempleteInfo(req.getTempleteInfo());
				templete.setTempleteName(req.getTempleteName());
				if (StringUtils.isNotEmpty(req.getDynamicParameter())) {
					Map<String, BigDecimal> paras = mapper.readValue(req.getDynamicParameter(),
							new TreeMap<String, BigDecimal>().getClass());
					templete.setDynamicParameter(mapper.writeValueAsString(paras));
				}
				templete.setState(EntityContext.RECORD_STATE_VALID);
				Integer templeteId = (Integer) communityFormulaTempleteDao.save(templete);
				CommunityFormulaTempleteForm templeteForm = new CommunityFormulaTempleteForm();
				templeteForm.setTempleteId(templeteId.intValue());
				templeteForm.setTempleteName(templete.getTempleteName());
				templeteForm.setTempleteInfo(templete.getTempleteInfo());
				templeteForm.setDynamicParameter(StringUtils.isNotEmpty(templete.getDynamicParameter()) ? 
						mapper.readValue(templete.getDynamicParameter(), new TreeMap<String, BigDecimal>().getClass()) :null);
				res.setTempleteForm(templeteForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} catch (Exception ex) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + ":" + ex.getMessage());
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Audit(operate = "修改公式模版")
	public IResponse modifyFormulaTemplete(CommunityFormulaTempleteReq req) {
		// TODO Auto-generated method stub
		CommunityFormulaTempleteRes res = new CommunityFormulaTempleteRes();
		if (null != req.getTempleteId()) {
			CommunityFormulaTempleteEntity templete = communityFormulaTempleteDao.get(req.getTempleteId());
			if (null != templete) {
				try {
					templete.setTempleteName(StringUtils.isNotEmpty(req.getTempleteName()) ? req.getTempleteName()
							: templete.getTempleteName());
					templete.setTempleteInfo(StringUtils.isNotEmpty(req.getTempleteInfo()) ? req.getTempleteInfo()
							: templete.getTempleteInfo());
					if (StringUtils.isNotEmpty(req.getDynamicParameter())) {
						Map<String, BigDecimal> paras = mapper.readValue(req.getDynamicParameter(),
								new TreeMap<String, BigDecimal>().getClass());
						templete.setDynamicParameter(mapper.writeValueAsString(paras));
					}
					communityFormulaTempleteDao.update(templete);
					CommunityFormulaTempleteForm templeteForm = new CommunityFormulaTempleteForm();
					templeteForm.setTempleteId(templete.getId());
					templeteForm.setTempleteName(templete.getTempleteName());
					templeteForm.setTempleteInfo(templete.getTempleteInfo());
					templeteForm.setDynamicParameter(StringUtils.isNotEmpty(templete.getDynamicParameter()) ? 
							mapper.readValue(templete.getDynamicParameter(), new TreeMap<String, BigDecimal>().getClass()) : null);
					res.setTempleteForm(templeteForm);
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} catch (Exception ex) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO + ":" + ex.getMessage());
				}
			} else {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除公式模版")
	public IResponse deleteFormulaTemplete(CommunityFormulaTempleteReq req) {
		// TODO Auto-generated method stub
		CommunityFormulaTempleteRes res = new CommunityFormulaTempleteRes();
		if (null != req.getTempleteId()) {
			communityFormulaTempleteDao.executeUpdate(
					"update CommunityFormulaTempleteEntity a set a.state=0 where a.id=" + req.getTempleteId());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getFormulaTempleteInfo(CommunityFormulaTempleteReq req) {
		// TODO Auto-generated method stub
		CommunityFormulaTempleteRes res = new CommunityFormulaTempleteRes();
		StringBuilder sql = new StringBuilder("from CommunityFormulaTempleteEntity a where a.state=1 ")
				.append(null != req.getTempleteId() ? " and a.id=" + req.getTempleteId() : "")
				.append(StringUtils.isNotEmpty(req.getTempleteName())
						? " and a.templeteName='" + req.getTempleteName() + "'"
						: "");
		CommunityFormulaTempleteEntity templete = communityFormulaTempleteDao.getUniqueByHql(sql.toString());
		if (null != templete) {
			try {
				CommunityFormulaTempleteForm templeteForm = new CommunityFormulaTempleteForm();
				templeteForm.setTempleteId(templete.getId());
				templeteForm.setTempleteInfo(templete.getTempleteInfo());
				templeteForm.setTempleteName(templete.getTempleteName());
				Map<String, BigDecimal> paras = mapper.readValue(templete.getDynamicParameter(),
						new HashMap<String, BigDecimal>().getClass());
				templeteForm.setDynamicParameter(paras);
				res.setTempleteForm(templeteForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} catch (Exception ex) {
				log.error(templete.getTempleteName() + "(id=" + templete.getId() + ")数据错误！");
				res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE + ex.getMessage());
				res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
			}

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
		}

		return res;
	}

	@SuppressWarnings({ "unchecked" })
	@Override
	public IResponse getFormulaTempleteList(CommunityFormulaTempleteReq req) {
		// TODO Auto-generated method stub
		GetCommunityFormulaTempleteListRes res = new GetCommunityFormulaTempleteListRes();
		StringBuilder hql = new StringBuilder("select a from CommunityFormulaTempleteEntity a where a.state=1");
		hql.append(StringUtils.isNotEmpty(req.getTempleteName()) ? " and a.templeteName like'%"+req.getTempleteName()+"%'":"");
		List<CommunityFormulaTempleteEntity> templeteList = communityFormulaTempleteDao
				.getListByHql(hql.toString());
		templeteList.forEach(o -> {
			try {
				CommunityFormulaTempleteForm templeteForm = new CommunityFormulaTempleteForm();
				templeteForm.setTempleteId(o.getId());
				templeteForm.setTempleteInfo(o.getTempleteInfo());
				templeteForm.setTempleteName(o.getTempleteName());
				Map<String, BigDecimal> paras = mapper.readValue(o.getDynamicParameter(),
						new HashMap<String, BigDecimal>().getClass());
				templeteForm.setDynamicParameter(paras);
				res.getTempleteList().add(templeteForm);
			} catch (Exception ex) {
				log.error(o.getTempleteName() + "(id=" + o.getId() + ")数据错误！");
			}
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

}
