package com.foshan.service.community.impl.statistics;

import static java.util.stream.Collectors.groupingBy;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jdbc.Work;

import com.foshan.form.community.statistics.CommunityReceiptVo;
import com.foshan.form.community.statistics.ReceiptDjbVo;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityCache;

import cn.hutool.core.util.ArrayUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ReceiptUtil {

	public static String getReceiptSql(Integer[] districtIds,Integer[]  buildingIds,String[] chargeCategorys, String[] payItemsNames,
			String receivableStartDate, String receivableEndDate, String receiptStartDate, String receiptEndDate) {

		StringBuilder sql = new StringBuilder("SELECT districtcode,CONCAT('select districtName,receiptCode,")
				.append("unitCode,buildingName,receivableDate,receivableMonth,receiptDate,paymentMethod,agent,',")
				.append("GROUP_CONCAT(CONCAT('MAX(CASE payitemsname WHEN ''',payitemsname,''' then currentAmount else '''' end) as ',payitemsname)),")
				.append("' from (select a.districtorder,a.districtname,a.receiptcode,a.unitcode,a.buildingname,a.buildingorder,a.receivabledate,a.receivablemonth,',")
				.append("'a.receiptDate,a.paymentMethod,a.agent,a.payitemsname,SUM(a.currentAmount) AS currentAmount,a.districtCode ',")
				.append("'FROM v_receipt a WHERE a.districtcode=''',districtcode,'''',")
				.append(null != districtIds && districtIds.length > 0
						? "' and districtid in(" + StringUtils.join(districtIds, ",") + ") ',"
						: "")
				.append(null != buildingIds && buildingIds.length > 0
						? "' and buildingId in(" + StringUtils.join(buildingIds, ",") + ") ',"
						: "")
				.append(null != chargeCategorys && chargeCategorys.length > 0
						? "' and chargeCategory in(''" + StringUtils.join(chargeCategorys, "'',''") + "'') ',"
						: "")
				.append(null != payItemsNames && payItemsNames.length > 0
						? "' and payItemsName in(''" + StringUtils.join(payItemsNames, "'',''") + "'') ',"
						: "")
				.append("' and a.receivableDate>=''" + receivableStartDate + " 00:00:00'' ',")
				.append("'AND a.receivableDate<=''" + receivableEndDate + " 23:59:59'' AND a.receiptDate>=''"
						+ receiptStartDate + " 00:00:00'' ',")
				.append("'AND a.receiptDate<=''" + receiptEndDate
						+ " 23:59:59'' GROUP BY a.receiptCode,a.payitemsname,a.receivabledate) aa ',")
				.append("'GROUP BY aa.receiptCode,aa.receivabledate ORDER BY aa.districtorder,aa.buildingorder,aa.unitcode,aa.receivabledate'")
				.append(") AS subsql FROM(SELECT DISTINCT a.districtcode,a.payitemsname ")
				.append("FROM v_receipt a WHERE 1=1 ")
				.append(null != districtIds && districtIds.length > 0
						? "and districtid in(" + StringUtils.join(districtIds, ",") + ") "
						: " ")
				.append(null != buildingIds && buildingIds.length > 0
						? " and buildingId in(" + StringUtils.join(buildingIds, ",") + ") "
						: " ")
				.append(null != chargeCategorys && chargeCategorys.length > 0
						? "and chargeCategory in('" + StringUtils.join(chargeCategorys, "','") + "') "
						: " ")
				.append(null != payItemsNames && payItemsNames.length > 0
						? "and payItemsName in('" + StringUtils.join(payItemsNames, "','") + "') "
						: " ")
				.append(" and a.receivableDate>='" + receivableStartDate + " 00:00:00' ")
				.append("AND a.receivableDate<='" + receivableEndDate + " 23:59:59' AND a.receiptDate>='"
						+ receiptStartDate + " 00:00:00' ")
				.append("AND a.receiptDate<='" + receiptEndDate
						+ " 23:59:59' GROUP BY a.receiptCode,a.payitemsname) aa GROUP BY districtcode");

		return sql.toString();
	}

	public static String getReceiptDjbSql(String startDate, String endDate) {

		StringBuilder sql = new StringBuilder("SELECT receiptdate,receiptcode,unitcode,")
				.append("MAX(CASE payitemsname WHEN 'IC卡工本费\\远程卡' THEN receivedamount ELSE '' END ) AS 'IC卡工本费\\远程卡',")
				.append("MAX(CASE payitemsname WHEN 'IC卡押金' THEN receivedamount ELSE '' END ) AS IC卡押金,")
				.append("MAX(CASE payitemsname WHEN '别墅管理费' THEN receivedamount ELSE '' END ) AS 别墅管理费,")
				.append("MAX(CASE payitemsname WHEN '场地费' THEN receivedamount ELSE '' END ) AS 场地费,")
				.append("MAX(CASE payitemsname WHEN '车位管理费' THEN receivedamount ELSE '' END ) AS 车位管理费,")
				.append("MAX(CASE payitemsname WHEN '出租车位管理服务费' THEN receivedamount ELSE '' END ) AS 出租车位管理服务费,")
				.append("MAX(CASE payitemsname WHEN '出租车位租金' THEN receivedamount ELSE '' END ) AS 出租车位租金,")
				.append("MAX(CASE payitemsname WHEN '穿梭车车费' THEN receivedamount ELSE '' END ) AS 穿梭车车费,")
				.append("MAX(CASE payitemsname WHEN '代收代缴房屋办证费' THEN receivedamount ELSE '' END ) AS 代收代缴房屋办证费,")
				.append("MAX(CASE payitemsname WHEN '电费' THEN receivedamount ELSE '' END ) AS 电费,")
				.append("MAX(CASE payitemsname WHEN '复印费' THEN receivedamount ELSE '' END ) AS 复印费,")
				.append("MAX(CASE payitemsname WHEN '工本费' THEN receivedamount ELSE '' END ) AS 工本费,")
				.append("MAX(CASE payitemsname WHEN '工程维修' THEN receivedamount ELSE '' END ) AS 工程维修,")
				.append("MAX(CASE payitemsname WHEN '公共开支分摊' THEN receivedamount ELSE '' END ) AS 公共开支分摊,")
				.append("MAX(CASE payitemsname WHEN '公共设施保证金' THEN receivedamount ELSE '' END ) AS 公共设施保证金,")
				.append("MAX(CASE payitemsname WHEN '还借款' THEN receivedamount ELSE '' END ) AS 还借款,")
				.append("MAX(CASE payitemsname WHEN '花园管理费' THEN receivedamount ELSE '' END ) AS 花园管理费,")
				.append("MAX(CASE payitemsname WHEN '花园及停车位管理费' THEN receivedamount ELSE '' END ) AS 花园及停车位管理费,")
				.append("MAX(CASE payitemsname WHEN '惠福电动车充电桩收入' THEN receivedamount ELSE '' END ) AS 惠福电动车充电桩收入,")
				.append("MAX(CASE payitemsname WHEN '家政清洁费' THEN receivedamount ELSE '' END ) AS 家政清洁费,")
				.append("MAX(CASE payitemsname WHEN '借用宿舍钥匙押金' THEN receivedamount ELSE '' END ) AS 借用宿舍钥匙押金,")
				.append("MAX(CASE payitemsname WHEN '精灵屋门票' THEN receivedamount ELSE '' END ) AS 精灵屋门票,")
				.append("MAX(CASE payitemsname WHEN '临时停车收费' THEN receivedamount ELSE '' END ) AS 临时停车收费,")
				.append("MAX(CASE payitemsname WHEN '露天车位管理服务费' THEN receivedamount ELSE '' END ) AS 露天车位管理服务费,")
				.append("MAX(CASE payitemsname WHEN '绿化服务费' THEN receivedamount ELSE '' END ) AS 绿化服务费,")
				.append("MAX(CASE payitemsname WHEN '砂石等费用' THEN receivedamount ELSE '' END ) AS 砂石等费用,")
				.append("MAX(CASE payitemsname WHEN '商铺物业管理费' THEN receivedamount ELSE '' END ) AS 商铺物业管理费,")
				.append("MAX(CASE payitemsname WHEN '商铺租金' THEN receivedamount ELSE '' END ) AS 商铺租金,")
				.append("MAX(CASE payitemsname WHEN '商业街临时停车费' THEN receivedamount ELSE '' END ) AS 商业街临时停车费,")
				.append("MAX(CASE payitemsname WHEN '社区活动收入' THEN receivedamount ELSE '' END ) AS 社区活动收入,")
				.append("MAX(CASE payitemsname WHEN '施工服务费' THEN receivedamount ELSE '' END ) AS 施工服务费,")
				.append("MAX(CASE payitemsname WHEN '水电周转保证金' THEN receivedamount ELSE '' END ) AS 水电周转保证金,")
				.append("MAX(CASE payitemsname WHEN '水费' THEN receivedamount ELSE '' END ) AS 水费,")
				.append("MAX(CASE payitemsname WHEN '诉讼费' THEN receivedamount ELSE '' END ) AS 诉讼费,")
				.append("MAX(CASE payitemsname WHEN '宿舍物业管理费' THEN receivedamount ELSE '' END ) AS 宿舍物业管理费,")
				.append("MAX(CASE payitemsname WHEN '宿舍租金' THEN receivedamount ELSE '' END ) AS 宿舍租金,")
				.append("MAX(CASE payitemsname WHEN '损害财产赔偿款' THEN receivedamount ELSE '' END ) AS 损害财产赔偿款,")
				.append("MAX(CASE payitemsname WHEN '往来款' THEN receivedamount ELSE '' END ) AS 往来款,")
				.append("MAX(CASE payitemsname WHEN '违约金' THEN receivedamount ELSE '' END ) AS 违约金,")
				.append("MAX(CASE payitemsname WHEN '物业管理费' THEN receivedamount ELSE '' END ) AS 物业管理费,")
				.append("MAX(CASE payitemsname WHEN '物业管理费保证金' THEN receivedamount ELSE '' END ) AS 物业管理费保证金,")
				.append("MAX(CASE payitemsname WHEN '小卖部食品' THEN receivedamount ELSE '' END ) AS 小卖部食品,")
				.append("MAX(CASE payitemsname WHEN '游泳池门票' THEN receivedamount ELSE '' END ) AS 游泳池门票,")
				.append("MAX(CASE payitemsname WHEN '有偿服务费' THEN receivedamount ELSE '' END ) AS 有偿服务费,")
				.append("MAX(CASE payitemsname WHEN '员工餐费' THEN receivedamount ELSE '' END ) AS 员工餐费,")
				.append("MAX(CASE payitemsname WHEN '重型机械进场押金' THEN receivedamount ELSE '' END ) AS 重型机械进场押金,")
				.append("MAX(CASE payitemsname WHEN '装修保证金' THEN receivedamount ELSE '' END ) AS 装修保证金,")
				.append("MAX(CASE payitemsname WHEN '装修垃圾清运费' THEN receivedamount ELSE '' END ) AS 装修垃圾清运费,")
				.append("MAX(CASE payitemsname WHEN '租金' THEN receivedamount ELSE '' END ) AS 租金,")
				.append("MAX(CASE payitemsname WHEN '租金保证金' THEN receivedamount ELSE '' END ) AS 租金保证金,")
				.append("MAX(CASE payitemsname WHEN '江南邻里充电桩' THEN receivedamount ELSE '' END ) AS 江南邻里充电桩,")
				.append("MAX(CASE amountmethod WHEN 'POS机' THEN amount ELSE '' END ) AS POS机,")
				.append("MAX(CASE amountmethod WHEN '现金' THEN amount ELSE '' END ) AS 现金,")
				.append("MAX(CASE amountmethod WHEN '工商银行' THEN amount ELSE '' END ) AS 工商银行,")
				.append("MAX(CASE amountmethod WHEN '农村信用合作社' THEN amount ELSE '' END ) AS 农村信用合作社,")
				.append("MAX(CASE amountmethod WHEN '建行' THEN amount ELSE '' END ) AS 建行,")
				.append("MAX(CASE amountmethod WHEN '农行' THEN amount ELSE '' END ) AS 农行,")
				.append("MAX(CASE amountmethod WHEN '支付宝小程序支付' THEN amount ELSE '' END ) AS 支付宝,")
				.append("MAX(CASE amountmethod WHEN '银行代收' THEN amount ELSE '' END ) AS 银行代收,")
				.append("paymentmethod FROM v_shouju_djb ")
				.append("where receiptdate>='" + startDate + "' and receiptdate<='" + endDate + "' ")
				.append("GROUP BY receiptcode order by receiptdate,receiptcode");
		return sql.toString();
	}

	public static Map<String, Object> getReceiptList(Integer[] districtIds,Integer[] buildingIds, String[] chargeCategorys,
			String[] payItemsNames, String receivableStartDate, String receivableEndDate, String receiptStartDate,
			String receiptEndDate) {
		Map<String, Object> res = new HashMap<>();

		String[] title = { "楼盘名称", "单据编号", "单元编号", "楼阁", "应收日期", "应收月份", "收款日期", "收款方式", "经办人" };
		List<CommunityReceiptVo> dataList = new LinkedList<>();

		Set<String> feeTitle = new TreeSet<>();

		// 获取动态sql
		String groupSql = getReceiptSql(districtIds,buildingIds, chargeCategorys, payItemsNames, receivableStartDate,
				receivableEndDate, receiptStartDate, receiptEndDate);

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {
				Statement st = conn.createStatement();
				ResultSet group_rs = st.executeQuery(groupSql);

				while (group_rs.next()) {
					String districtCode = group_rs.getString("districtCode");
					String subSql = group_rs.getString("subSql");
					ResultSet subrs = st.executeQuery(subSql);

					while (subrs.next()) {
						ResultSetMetaData rsmd = subrs.getMetaData();
						CommunityReceiptVo vo = new CommunityReceiptVo();
						vo.setDistrictCode(districtCode.length() == 1 ? "0" + districtCode : districtCode);
						vo.setDistrictName(subrs.getString("districtName"));
						vo.setReceiptCode(subrs.getString("receiptCode"));
						vo.setBuildingName(subrs.getString("buildingName"));
						vo.setUnitCode(subrs.getString("unitCode"));
						vo.setReceivableDate(subrs.getString("receivableDate"));
						vo.setReceivableMonth(subrs.getString("receivableMonth"));
						vo.setReceiptDate(subrs.getString("receiptDate"));
						vo.setPaymentmethod(subrs.getString("paymentMethod"));
						vo.setAgent(subrs.getString("agent"));

						Map<String, String> feeMap = new TreeMap<>();
						for (int i = 10; i <= rsmd.getColumnCount(); i++) {
							feeTitle.add(rsmd.getColumnName(i));
							feeMap.put(rsmd.getColumnName(i), subrs.getString(i));
						}
						vo.setFeeMap(feeMap);
						dataList.add(vo);
					}

				}

			}
		});
		session.close();

		for (String fee : feeTitle) {
			title = ArrayUtil.insert(title, title.length, fee);
		}

		res.put("title", title);
		res.put("data", dataList);

		return res;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static void transReceiptExcel(HttpServletResponse response) {

		// 获取缓存数据并进行处理
		Map<String, Map<String, Object>> temp1 = CommunityCache.statisticsCache.get("收款透视");
		String key = "";
		for (String o : temp1.keySet()) {
			key = o;
		}

		Map<String, Object> temp2 = temp1.get(key);
		String[] titles = (String[]) temp2.get("title");
		titles = ArrayUtil.insert(titles, titles.length, "合计");

		List<CommunityReceiptVo> dataList = (List<CommunityReceiptVo>) temp2.get("data");

		// 将缓存的队列数据按楼盘编码进行排序
		Map<String, List<CommunityReceiptVo>> districtReceivableMap = (HashMap<String, List<CommunityReceiptVo>>) dataList
				.stream().collect(groupingBy(CommunityReceiptVo::getNewDistrictCode));

		if (districtReceivableMap.size() > 1) {
			districtReceivableMap.put("000全区", dataList);
		}

		TreeSet<String> keys = new TreeSet<>(Comparator.naturalOrder());
		districtReceivableMap.keySet().forEach(o -> {
			keys.add(o);
		});

		XSSFWorkbook wb = new XSSFWorkbook();
		Map<String, XSSFCellStyle> cellStyle = ExcelUtil.getExcelStyle(wb);

		for (String o : keys) {

			Sheet sheet = wb.createSheet(o.substring(3));

			LinkedList<CommunityReceiptVo> tempList = new LinkedList(districtReceivableMap.get(o));

			// 构建表头
			Row title1 = sheet.createRow(0);
			createTitle(sheet, cellStyle, title1, titles);

			// 获取每楼盘下单元单元编号进行排序
//			List<CommunityReceiptVo> unitList = tempList.stream()
//					.sorted(Comparator.comparing(CommunityReceiptVo::getUnitCode)).collect(Collectors.toList());

			int rowNumber = 1;
			for (CommunityReceiptVo vo : tempList) {
				Row row = sheet.createRow(rowNumber);
				// 楼盘名称
				Cell cell1 = row.createCell(0, CellType.STRING);
				cell1.setCellStyle(cellStyle.get("cell_left"));
				cell1.setCellValue(vo.getDistrictName());

				// 单据编号
				Cell cell2 = row.createCell(1, CellType.STRING);
				cell2.setCellStyle(cellStyle.get("cell_left"));
				cell2.setCellValue(vo.getReceiptCode());

				// 单元编号
				Cell cell3 = row.createCell(2, CellType.STRING);
				cell3.setCellStyle(cellStyle.get("cell_left"));
				cell3.setCellValue(vo.getUnitCode());

				// 楼阁
				Cell cell4 = row.createCell(3, CellType.STRING);
				cell4.setCellStyle(cellStyle.get("cell"));
				cell4.setCellValue(vo.getBuildingName());

				// 应收日期
				Cell cell5 = row.createCell(4, CellType.STRING);
				cell5.setCellStyle(cellStyle.get("cell_left"));
				cell5.setCellValue(vo.getReceivableDate());

				// 应收月份
				Cell cell6 = row.createCell(5, CellType.STRING);
				cell6.setCellStyle(cellStyle.get("cell_left"));
				cell6.setCellValue(vo.getReceivableMonth());

				// 收款日期
				Cell cell_add1 = row.createCell(6, CellType.STRING);
				cell_add1.setCellStyle(cellStyle.get("cell_left"));
				cell_add1.setCellValue(vo.getReceiptDate());

				// 收款方式
				Cell cell6_add2 = row.createCell(7, CellType.STRING);
				cell6_add2.setCellStyle(cellStyle.get("cell_left"));
				cell6_add2.setCellValue(vo.getPaymentmethod());

				// 经办人
				Cell cell6_add3 = row.createCell(8, CellType.STRING);
				cell6_add3.setCellStyle(cellStyle.get("cell_left"));
				cell6_add3.setCellValue(vo.getAgent());

				if (sheet.getSheetName().equals("全区")) {
					for (int i = 1; i <= titles.length - 9; i++) {
						Cell dcell = row.createCell(i + 8, CellType.NUMERIC);
						dcell.setCellStyle(cellStyle.get("cell_right2"));
						String payItemsName = titles[i + 8];
						payItemsName = payItemsName.startsWith("合计") ? "合计" : payItemsName;

						if (vo.getFeeMap().containsKey(payItemsName)
								&& StringUtils.isNotEmpty(vo.getFeeMap().get(payItemsName))) {
							dcell.setCellValue(new BigDecimal(vo.getFeeMap().get(payItemsName)).doubleValue());
						} else if (payItemsName.equals("合计")) {
							dcell.setCellStyle(cellStyle.get("total_double"));
							dcell.setCellFormula(
									"sum(" + row.getCell(9).getAddress() + ":" + row.getCell(i + 7).getAddress() + ")");
						} else {
							dcell.setBlank();
						}
					}

				} else {
					Map<String, String> feeMap = vo.getFeeMap();
					int i = 1;
					for (String payItemsName : feeMap.keySet()) {

						Cell t1 = title1.createCell(i + 8, CellType.STRING);
						t1.setCellStyle(cellStyle.get("title"));
						t1.setCellValue(payItemsName);

						Cell dcell = row.createCell(i + 8, CellType.NUMERIC);
						dcell.setCellStyle(cellStyle.get("cell_right2"));
						if (StringUtils.isNotEmpty(vo.getFeeMap().get(payItemsName))) {
							dcell.setCellValue(new BigDecimal(feeMap.get(payItemsName)).doubleValue());
						} else {
							dcell.setBlank();
						}

						i++;
					}

					// 合计
					if (null == title1.getCell(i + 8)) {
						Cell titleCell = title1.createCell(i + 8, CellType.STRING);
						titleCell.setCellStyle(cellStyle.get("title"));
						titleCell.setCellValue("合计");
					}

					Cell dcell = row.createCell(i + 8, CellType.NUMERIC);
					dcell.setCellStyle(cellStyle.get("total_double"));
					dcell.setCellFormula(
							"sum(" + row.getCell(9).getAddress() + ":" + row.getCell(i + 7).getAddress() + ")");
				}
				rowNumber++;
			}

			// 合计行
			Row total = sheet.createRow(rowNumber);
			Cell totalStr = total.createCell(0, CellType.STRING);
			totalStr.setCellValue("合计");
			totalStr.setCellStyle(cellStyle.get("total_double"));

			CellRangeAddress region3 = new CellRangeAddress(rowNumber, rowNumber, 0, 8);
			sheet.addMergedRegion(region3);
			ExcelUtil.addMergeCellBorder(region3, sheet);

			int totalRowColumns = title1.getLastCellNum();
			for (int i = 9; i < totalRowColumns; i++) {
				Cell tt = total.createCell(i, CellType.NUMERIC);
				tt.setCellStyle(cellStyle.get("total_double"));
				tt.setCellFormula("sum(" + sheet.getRow(1).getCell(i).getAddress() + ":"
						+ sheet.getRow(rowNumber - 1).getCell(i).getAddress() + ")");
			}

			sheet.autoSizeColumn(0);
			sheet.autoSizeColumn(1);
			sheet.autoSizeColumn(2);
			sheet.autoSizeColumn(3);
			sheet.autoSizeColumn(4);
			sheet.autoSizeColumn(5);
			sheet.autoSizeColumn(6);
			sheet.autoSizeColumn(7);
			sheet.autoSizeColumn(8);
			// 冻结首行
			sheet.createFreezePane(0, 1, 0, 1);
			// 冻结首列
			sheet.createFreezePane(9, 1, 9, 1);
			// 增加筛选框
			CellRangeAddress c = CellRangeAddress.valueOf(sheet.getRow(0).getCell(0).getAddress() + ":"
					+ sheet.getRow(0).getCell(sheet.getRow(0).getLastCellNum() - 1).getAddress());
			sheet.setAutoFilter(c);

		}

		String fileName = "收款透视" + DateUtil.formatByStyle(new Date(), "_yyyy_MM_dd_HH_mm_ss") + ".xlsx";

		try {
			ExcelExportUtil.export(response, wb, fileName);
		} catch (IOException e) {
			log.error(e.getMessage());
		}

	}

	private static void createTitle(Sheet sheet, Map<String, XSSFCellStyle> cellStyle, Row title1, String[] titles) {

		int columnCount = titles.length - 1;

		for (int i = 0; i <= columnCount; i++) {
			if (sheet.getSheetName().equals("全区")) {
				Cell tt = title1.createCell(i, CellType.STRING);
				tt.setCellValue(titles[i]);
				tt.setCellStyle(cellStyle.get("title"));
			} else {
				if (i <= 8) {
					Cell tt = title1.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
					tt.setCellStyle(cellStyle.get("title"));
				}
			}

		}
	}

	public static void transReceiptDjbExcel(String sheetName, List<ReceiptDjbVo> voList, HttpServletResponse response) {

		XSSFWorkbook wb = getDjbExcel(sheetName);

		if (wb.getSheetAt(wb.getNumberOfSheets() - 1).getSheetName().equals(sheetName)) {
			wb.removeSheetAt(wb.getNumberOfSheets() - 1);
		}

		Sheet sheet = wb.getSheet(sheetName);
		if(null!=sheet) {
			int i =wb.getSheetIndex(sheet);
			wb.removeSheetAt(i);
		}
		sheet=wb.createSheet(sheetName);

		// 获取Excel样式表
		Map<String, XSSFCellStyle> cellStyle = ExcelUtil.getExcelStyle(wb);

		// 创建表头
		createDjbTitle(sheet, cellStyle);

		// 按收据日期分组
		Map<String, List<ReceiptDjbVo>> tempMap = (HashMap<String, List<ReceiptDjbVo>>) voList.stream()
				.collect(groupingBy(ReceiptDjbVo::getReceiptDate));

		// 转成TreeMap
		Map<String, List<ReceiptDjbVo>> djbMap = new TreeMap<>();
		djbMap.putAll(tempMap);

		int idx = 5;

		for (String receiptDate : djbMap.keySet()) {
			List<ReceiptDjbVo> subList = djbMap.get(receiptDate);

			for (ReceiptDjbVo vo : subList) {
				Row row = sheet.createRow(idx);
				// 收据日期
				Cell cell1 = row.createCell(0, CellType.STRING);
				cell1.setCellStyle(cellStyle.get("cell_left"));
				cell1.setCellValue(vo.getReceiptDate());

				// 单据编号
				Cell cell2 = row.createCell(1, CellType.STRING);
				cell2.setCellStyle(cellStyle.get("cell_left"));
				cell2.setCellValue(vo.getReceiptCode());

				// 摘要
				Cell cell3 = row.createCell(2, CellType.STRING);
				cell3.setCellStyle(cellStyle.get("cell_left"));
				cell3.setCellValue(vo.getUnitCode());

				int i = 2;
				// IC卡工本费\远程卡
				Cell dcell1 = row.createCell(i + 1, CellType.NUMERIC);
				dcell1.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee1())) {
					dcell1.setCellValue(new BigDecimal(vo.getFee1()).doubleValue());
				} else {
					dcell1.setBlank();
				}

				// IC卡押金
				Cell dcell2 = row.createCell(i + 2, CellType.NUMERIC);
				dcell2.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee2())) {
					dcell2.setCellValue(new BigDecimal(vo.getFee2()).doubleValue());
				} else {
					dcell2.setBlank();
				}

				// 别墅管理费
				Cell dcell3 = row.createCell(i + 3, CellType.NUMERIC);
				dcell3.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee3())) {
					dcell3.setCellValue(new BigDecimal(vo.getFee3()).doubleValue());
				} else {
					dcell3.setBlank();
				}

				// 场地费
				Cell dcell4 = row.createCell(i + 4, CellType.NUMERIC);
				dcell4.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee4())) {
					dcell4.setCellValue(new BigDecimal(vo.getFee4()).doubleValue());
				} else {
					dcell4.setBlank();
				}
				
				// 车位管理费
				Cell dcell5 = row.createCell(i + 5, CellType.NUMERIC);
				dcell5.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee5())) {
					dcell5.setCellValue(new BigDecimal(vo.getFee5()).doubleValue());
				} else {
					dcell5.setBlank();
				}

				// 出租车位管理服务费
				Cell dcell6 = row.createCell(i + 6, CellType.NUMERIC);
				dcell6.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee6())) {
					dcell6.setCellValue(new BigDecimal(vo.getFee6()).doubleValue());
				} else {
					dcell6.setBlank();
				}

				// 出租车位租金
				Cell dcell7 = row.createCell(i + 7, CellType.NUMERIC);
				dcell7.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee7())) {
					dcell7.setCellValue(new BigDecimal(vo.getFee7()).doubleValue());
				} else {
					dcell7.setBlank();
				}

				// 穿梭车车费
				Cell dcell8 = row.createCell(i + 8, CellType.NUMERIC);
				dcell8.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee8())) {
					dcell8.setCellValue(new BigDecimal(vo.getFee8()).doubleValue());
				} else {
					dcell8.setBlank();
				}

				// 代收代缴房屋办证费
				Cell dcell9 = row.createCell(i + 9, CellType.NUMERIC);
				dcell9.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee9())) {
					dcell9.setCellValue(new BigDecimal(vo.getFee9()).doubleValue());
				} else {
					dcell9.setBlank();
				}

				// 电费
				Cell dcell10 = row.createCell(i + 10, CellType.NUMERIC);
				dcell10.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee10())) {
					dcell10.setCellValue(new BigDecimal(vo.getFee10()).doubleValue());
				} else {
					dcell10.setBlank();
				}

				// 复印费
				Cell dcell11 = row.createCell(i + 11, CellType.NUMERIC);
				dcell11.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee11())) {
					dcell11.setCellValue(new BigDecimal(vo.getFee11()).doubleValue());
				} else {
					dcell11.setBlank();
				}

				// 工本费
				Cell dcell12 = row.createCell(i + 12, CellType.NUMERIC);
				dcell12.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee12())) {
					dcell12.setCellValue(new BigDecimal(vo.getFee12()).doubleValue());
				} else {
					dcell12.setBlank();
				}

				// 工程维修
				Cell dcell13 = row.createCell(i + 13, CellType.NUMERIC);
				dcell13.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee13())) {
					dcell13.setCellValue(new BigDecimal(vo.getFee13()).doubleValue());
				} else {
					dcell13.setBlank();
				}

				// 公共开支分摊
				Cell dcell14 = row.createCell(i + 14, CellType.NUMERIC);
				dcell14.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee14())) {
					dcell14.setCellValue(new BigDecimal(vo.getFee14()).doubleValue());
				} else {
					dcell14.setBlank();
				}

				// 公共设施保证金
				Cell dcell15 = row.createCell(i + 15, CellType.NUMERIC);
				dcell15.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee15())) {
					dcell15.setCellValue(new BigDecimal(vo.getFee15()).doubleValue());
				} else {
					dcell15.setBlank();
				}

				// 还借款
				Cell dcell16 = row.createCell(i + 16, CellType.NUMERIC);
				dcell16.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee16())) {
					dcell16.setCellValue(new BigDecimal(vo.getFee16()).doubleValue());
				} else {
					dcell16.setBlank();
				}

				// 花园管理费
				Cell dcell17 = row.createCell(i + 17, CellType.NUMERIC);
				dcell17.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee17())) {
					dcell17.setCellValue(new BigDecimal(vo.getFee17()).doubleValue());
				} else {
					dcell17.setBlank();
				}

				// 花园及停车位管理费
				Cell dcell18 = row.createCell(i + 18, CellType.NUMERIC);
				dcell18.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee18())) {
					dcell18.setCellValue(new BigDecimal(vo.getFee18()).doubleValue());
				} else {
					dcell18.setBlank();
				}

				// 惠福电动车充电桩收入
				Cell dcell19 = row.createCell(i + 19, CellType.NUMERIC);
				dcell19.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee19())) {
					dcell19.setCellValue(new BigDecimal(vo.getFee19()).doubleValue());
				} else {
					dcell19.setBlank();
				}

				// 家政清洁费
				Cell dcell20 = row.createCell(i + 20, CellType.NUMERIC);
				dcell20.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee20())) {
					dcell20.setCellValue(new BigDecimal(vo.getFee20()).doubleValue());
				} else {
					dcell20.setBlank();
				}

				// 借用宿舍钥匙押金
				Cell dcell21 = row.createCell(i + 21, CellType.NUMERIC);
				dcell21.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee21())) {
					dcell21.setCellValue(new BigDecimal(vo.getFee21()).doubleValue());
				} else {
					dcell21.setBlank();
				}

				// 精灵屋门票
				Cell dcell22 = row.createCell(i + 22, CellType.NUMERIC);
				dcell22.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee22())) {
					dcell22.setCellValue(new BigDecimal(vo.getFee22()).doubleValue());
				} else {
					dcell22.setBlank();
				}

				// 临时停车收费
				Cell dcell23 = row.createCell(i + 23, CellType.NUMERIC);
				dcell23.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee23())) {
					dcell23.setCellValue(new BigDecimal(vo.getFee23()).doubleValue());
				} else {
					dcell23.setBlank();
				}

				// 露天车位管理服务费
				Cell dcell24 = row.createCell(i + 24, CellType.NUMERIC);
				dcell24.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee24())) {
					dcell24.setCellValue(new BigDecimal(vo.getFee24()).doubleValue());
				} else {
					dcell24.setBlank();
				}

				// 绿化服务费
				Cell dcell25 = row.createCell(i + 25, CellType.NUMERIC);
				dcell25.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee25())) {
					dcell25.setCellValue(new BigDecimal(vo.getFee25()).doubleValue());
				} else {
					dcell25.setBlank();
				}

				// 砂石等费用
				Cell dcell26 = row.createCell(i + 26, CellType.NUMERIC);
				dcell26.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee26())) {
					dcell26.setCellValue(new BigDecimal(vo.getFee26()).doubleValue());
				} else {
					dcell26.setBlank();
				}

				// 商铺物业管理费
				Cell dcell27 = row.createCell(i + 27, CellType.NUMERIC);
				dcell27.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee27())) {
					dcell27.setCellValue(new BigDecimal(vo.getFee27()).doubleValue());
				} else {
					dcell27.setBlank();
				}

				// 商铺租金
				Cell dcell28 = row.createCell(i + 28, CellType.NUMERIC);
				dcell28.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee28())) {
					dcell28.setCellValue(new BigDecimal(vo.getFee28()).doubleValue());
				} else {
					dcell28.setBlank();
				}

				// 商业街临时停车费
				Cell dcell29 = row.createCell(i + 29, CellType.NUMERIC);
				dcell29.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee29())) {
					dcell29.setCellValue(new BigDecimal(vo.getFee29()).doubleValue());
				} else {
					dcell29.setBlank();
				}

				// 社区活动收入
				Cell dcell30 = row.createCell(i + 30, CellType.NUMERIC);
				dcell30.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee30())) {
					dcell30.setCellValue(new BigDecimal(vo.getFee30()).doubleValue());
				} else {
					dcell30.setBlank();
				}

				// 施工服务费
				Cell dcell31 = row.createCell(i + 31, CellType.NUMERIC);
				dcell31.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee31())) {
					dcell31.setCellValue(new BigDecimal(vo.getFee31()).doubleValue());
				} else {
					dcell31.setBlank();
				}

				// 水电周转保证金
				Cell dcell32 = row.createCell(i + 32, CellType.NUMERIC);
				dcell32.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee32())) {
					dcell32.setCellValue(new BigDecimal(vo.getFee32()).doubleValue());
				} else {
					dcell32.setBlank();
				}

				// 水费
				Cell dcell33 = row.createCell(i + 33, CellType.NUMERIC);
				dcell33.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee33())) {
					dcell33.setCellValue(new BigDecimal(vo.getFee33()).doubleValue());
				} else {
					dcell33.setBlank();
				}

				// 诉讼费
				Cell dcell34 = row.createCell(i + 34, CellType.NUMERIC);
				dcell34.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee34())) {
					dcell34.setCellValue(new BigDecimal(vo.getFee34()).doubleValue());
				} else {
					dcell34.setBlank();
				}

				// 宿舍物业管理费
				Cell dcell35 = row.createCell(i + 35, CellType.NUMERIC);
				dcell35.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee35())) {
					dcell35.setCellValue(new BigDecimal(vo.getFee35()).doubleValue());
				} else {
					dcell35.setBlank();
				}

				// 宿舍租金
				Cell dcell36 = row.createCell(i + 36, CellType.NUMERIC);
				dcell36.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee36())) {
					dcell36.setCellValue(new BigDecimal(vo.getFee36()).doubleValue());
				} else {
					dcell36.setBlank();
				}

				// 损害财产赔偿款
				Cell dcell37 = row.createCell(i + 37, CellType.NUMERIC);
				dcell37.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee37())) {
					dcell37.setCellValue(new BigDecimal(vo.getFee37()).doubleValue());
				} else {
					dcell37.setBlank();
				}

				// 往来款
				Cell dcell38 = row.createCell(i + 38, CellType.NUMERIC);
				dcell38.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee38())) {
					dcell38.setCellValue(new BigDecimal(vo.getFee38()).doubleValue());
				} else {
					dcell38.setBlank();
				}

				// 违约金
				Cell dcell39 = row.createCell(i + 39, CellType.NUMERIC);
				dcell39.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee39())) {
					dcell39.setCellValue(new BigDecimal(vo.getFee39()).doubleValue());
				} else {
					dcell39.setBlank();
				}

				// 物业管理费
				Cell dcell40 = row.createCell(i + 40, CellType.NUMERIC);
				dcell40.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee40())) {
					dcell40.setCellValue(new BigDecimal(vo.getFee40()).doubleValue());
				} else {
					dcell40.setBlank();
				}

				// 物业管理费保证金
				Cell dcell41 = row.createCell(i + 41, CellType.NUMERIC);
				dcell41.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee41())) {
					dcell41.setCellValue(new BigDecimal(vo.getFee41()).doubleValue());
				} else {
					dcell41.setBlank();
				}

				// 小卖部食品
				Cell dcell42 = row.createCell(i + 42, CellType.NUMERIC);
				dcell42.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee42())) {
					dcell42.setCellValue(new BigDecimal(vo.getFee42()).doubleValue());
				} else {
					dcell42.setBlank();
				}

				// 游泳池门票
				Cell dcell43 = row.createCell(i + 43, CellType.NUMERIC);
				dcell43.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee43())) {
					dcell43.setCellValue(new BigDecimal(vo.getFee43()).doubleValue());
				} else {
					dcell43.setBlank();
				}

				// 有偿服务费
				Cell dcell44 = row.createCell(i + 44, CellType.NUMERIC);
				dcell44.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee44())) {
					dcell44.setCellValue(new BigDecimal(vo.getFee44()).doubleValue());
				} else {
					dcell44.setBlank();
				}

				// 员工餐费
				Cell dcell45 = row.createCell(i + 45, CellType.NUMERIC);
				dcell45.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee45())) {
					dcell45.setCellValue(new BigDecimal(vo.getFee45()).doubleValue());
				} else {
					dcell45.setBlank();
				}

				// 重型机械进场押金
				Cell dcell46 = row.createCell(i + 46, CellType.NUMERIC);
				dcell46.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee46())) {
					dcell46.setCellValue(new BigDecimal(vo.getFee46()).doubleValue());
				} else {
					dcell46.setBlank();
				}

				// 装修保证金
				Cell dcell47 = row.createCell(i + 47, CellType.NUMERIC);
				dcell47.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee47())) {
					dcell47.setCellValue(new BigDecimal(vo.getFee47()).doubleValue());
				} else {
					dcell47.setBlank();
				}

				// 装修垃圾清运费
				Cell dcell48 = row.createCell(i + 48, CellType.NUMERIC);
				dcell48.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee48())) {
					dcell48.setCellValue(new BigDecimal(vo.getFee48()).doubleValue());
				} else {
					dcell48.setBlank();
				}

				// 租金
				Cell dcell49 = row.createCell(i + 49, CellType.NUMERIC);
				dcell49.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee49())) {
					dcell49.setCellValue(new BigDecimal(vo.getFee49()).doubleValue());
				} else {
					dcell49.setBlank();
				}
				
				// 租金保证金
				Cell dcell50 = row.createCell(i + 50, CellType.NUMERIC);
				dcell50.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee50())) {
					dcell50.setCellValue(new BigDecimal(vo.getFee50()).doubleValue());
				} else {
					dcell50.setBlank();
				}
				
				// 江南邻里充电桩
				Cell dcell51 = row.createCell(i + 51, CellType.NUMERIC);
				dcell51.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getFee51())) {
					dcell51.setCellValue(new BigDecimal(vo.getFee51()).doubleValue());
				} else {
					dcell51.setBlank();
				}

				int j = 53;
				// POS机
				Cell mcell1 = row.createCell(j + 1, CellType.NUMERIC);
				mcell1.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getMethod1())) {
					mcell1.setCellValue(new BigDecimal(vo.getMethod1()).doubleValue());
				} else {
					mcell1.setBlank();
				}

				// 现金
				Cell mcell2 = row.createCell(j + 2, CellType.NUMERIC);
				mcell2.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getMethod2())) {
					mcell2.setCellValue(new BigDecimal(vo.getMethod2()).doubleValue());
				} else {
					mcell2.setBlank();
				}

				// 工商银行
				Cell mcell3 = row.createCell(j + 3, CellType.NUMERIC);
				mcell3.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getMethod3())) {
					mcell3.setCellValue(new BigDecimal(vo.getMethod3()).doubleValue());
				} else {
					mcell3.setBlank();
				}

				// 农村信用合作社
				Cell mcell4 = row.createCell(j + 4, CellType.NUMERIC);
				mcell4.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getMethod4())) {
					mcell4.setCellValue(new BigDecimal(vo.getMethod4()).doubleValue());
				} else {
					mcell4.setBlank();
				}

				// 建行
				Cell mcell5 = row.createCell(j + 5, CellType.NUMERIC);
				mcell5.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getMethod5())) {
					mcell5.setCellValue(new BigDecimal(vo.getMethod5()).doubleValue());
				} else {
					mcell5.setBlank();
				}

				// 农行
				Cell mcell6 = row.createCell(j + 6, CellType.NUMERIC);
				mcell6.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getMethod6())) {
					mcell6.setCellValue(new BigDecimal(vo.getMethod6()).doubleValue());
				} else {
					mcell6.setBlank();
				}
				
				// 支付宝
				Cell mcell7 = row.createCell(j + 7, CellType.NUMERIC);
				mcell7.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getMethod7())) {
					mcell7.setCellValue(new BigDecimal(vo.getMethod7()).doubleValue());
				} else {
					mcell7.setBlank();
				}
				
				// 银行代收
				Cell mcell8 = row.createCell(j + 8, CellType.NUMERIC);
				mcell8.setCellStyle(cellStyle.get("cell_right1"));
				if (StringUtils.isNotEmpty(vo.getMethod8())) {
					mcell8.setCellValue(new BigDecimal(vo.getMethod8()).doubleValue());
				} else {
					mcell8.setBlank();
				}

				// 收款方式
				Cell cell62 = row.createCell(62, CellType.STRING);
				cell62.setCellStyle(cellStyle.get("cell_left"));
				cell62.setCellValue(vo.getPaymentMethod());

				idx++;
			}

			// 相同日期单元格合并
			CellRangeAddress receiptDateCellRange = new CellRangeAddress((idx - subList.size()), (idx - 1), 0, 0);
			sheet.addMergedRegion(receiptDateCellRange);
			ExcelUtil.addMergeCellBorder(receiptDateCellRange, sheet);
		}
		// 合计
		Row t3 = sheet.createRow(2);
		for (int i = 2; i <= 62; i++) {
			Cell t3_1 = t3.createCell(i, CellType.NUMERIC);
			t3_1.setCellStyle(cellStyle.get("total_kilo_int"));
			if (i >= 3 && i < 62 && null != sheet.getRow(5)) {
				if(null!=sheet.getRow(5).getCell(i) && null!=sheet.getRow(sheet.getLastRowNum()).getCell(i)) {
					t3_1.setCellFormula("sum(" + sheet.getRow(5).getCell(i).getAddress() + ":"
							+ sheet.getRow(sheet.getLastRowNum()).getCell(i).getAddress() + ")");
				}else {
					t3_1.setBlank();
				}

			} else {
				t3_1.setBlank();
			}
		}

		Row t2 = sheet.getRow(1);
		Cell total1 = t2.createCell(0, CellType.NUMERIC);
		total1.setCellStyle(cellStyle.get("total_kilo_int"));
		total1.setCellFormula("sum(" + sheet.getRow(2).getCell(54).getAddress() + ":"
				+ sheet.getRow(2).getCell(61).getAddress() + ")");

		CellRangeAddress region2 = new CellRangeAddress(1, 1, 0, 1);
		sheet.addMergedRegion(region2);
		ExcelUtil.addMergeCellBorder(region2, sheet);

		Cell total2 = t3.createCell(0, CellType.NUMERIC);
		total2.setCellStyle(cellStyle.get("cell_right2"));
		total2.setCellFormula("sum(" + sheet.getRow(3).getCell(3).getAddress() + ":"
				+ sheet.getRow(3).getCell(53).getAddress() + ")-sum(" + sheet.getRow(3).getCell(54).getAddress() + ":"
				+ sheet.getRow(3).getCell(61).getAddress() + ")");

		CellRangeAddress region3 = new CellRangeAddress(2, 2, 0, 1);
		sheet.addMergedRegion(region3);
		ExcelUtil.addMergeCellBorder(region3, sheet);

		sheet.setColumnWidth(0, 10 * 256);
		sheet.autoSizeColumn(1);
		sheet.autoSizeColumn(2);
		sheet.autoSizeColumn(62);

		// 冻结首行
		sheet.createFreezePane(0, 5, 0, 5);
		// 冻结首列
		sheet.createFreezePane(3, 5, 3, 5);

		// 增加筛选框
		CellRangeAddress c = CellRangeAddress.valueOf(sheet.getRow(4).getCell(0).getAddress() + ":"
				+ sheet.getRow(4).getCell(sheet.getRow(4).getLastCellNum() - 1).getAddress());
		sheet.setAutoFilter(c);

		// 构建汇总sheet数据
		createTotal(wb, sheetName, cellStyle);

		// 保存及返回文件
		String fileName = sheetName.substring(0, sheetName.indexOf("年") + 1) + "收据登记表.xlsx";
		OutputStream os = null;
		try {
			os = new FileOutputStream("/data/resource/communityFile/djb/" + fileName);
			wb.write(os);
			os.close();
			ExcelExportUtil.export(response, wb, fileName);

		} catch (IOException e) {
			e.printStackTrace();
			log.error(e.getMessage());
		}finally {
			if(os != null) {
				try {
					os.close();
					wb.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			
		} 
	}

	private static void createDjbTitle(Sheet sheet, Map<String, XSSFCellStyle> cellStyle) {

		String[] title1 = { "日期", "收据编号", "摘要", "收费项目", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
				"", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "",
				"", "", "", "", "", "", "金额", "", "", "", "", "", "", "", "", "收款方式" };
		String[] title2 = { "", "", "", "IC卡工本费\\远程卡", "IC卡押金", "别墅管理费", "场地费", "车位管理费", "出租车位管理服务费", "出租车位租金", "穿梭车车费",
				"代收代缴房屋办证费", "电费", "复印费", "工本费", "工程维修", "公共开支分摊", "公共设施保证金", "还借款", "花园管理费", "花园及停车位管理费", "惠福电动车充电桩收入",
				"家政清洁费", "借用宿舍钥匙押金", "精灵屋门票", "临时停车收费", "露天车位管理服务费", "绿化服务费", "砂石等费用", "商铺物业管理费", "商铺租金", "商业街临时停车费",
				"社区活动收入", "施工服务费", "水电周转保证金", "水费", "诉讼费", "宿舍物业管理费", "宿舍租金", "损害财产赔偿款", "往来款", "违约金", "物业管理费",
				"物业管理费保证金", "小卖部食品", "游泳池门票", "有偿服务费", "员工餐费", "重型机械进场押金", "装修保证金", "装修垃圾清运费", "租金", "租金保证金","江南邻里充电桩", "POS机",
				"现金", "工商银行", "农村信用合作社", "建行", "农行", "支付宝","银行代收", "" };

		// 首行
		Row t1 = sheet.createRow(0);
		Cell t1_1 = t1.createCell(0, CellType.STRING);
		t1_1.setCellStyle(cellStyle.get("title2"));
		t1_1.setCellValue("佛山普华美居物业服务有限公司");
		CellRangeAddress region1 = new CellRangeAddress(0, 0, 0, title1.length - 1);
		sheet.addMergedRegion(region1);

		// 第二行
		Row t2 = sheet.createRow(1);

		Cell t2_2 = t2.createCell(2, CellType.STRING);
		t2_2.setCellStyle(cellStyle.get("title2"));
		t2_2.setCellValue("每日收据使用情况登记表");
		CellRangeAddress region3 = new CellRangeAddress(1, 1, 2, 60);
		sheet.addMergedRegion(region3);

		Cell t2_3 = t2.createCell(59, CellType.STRING);
		t2_3.setCellStyle(cellStyle.get("title1"));
		t2_3.setBlank();
		CellRangeAddress region4 = new CellRangeAddress(1, 1, 61, 62);
		sheet.addMergedRegion(region4);

//		CellRangeAddress region5 = new CellRangeAddress(2, 2, 0, 1);
//		sheet.addMergedRegion(region5);

		// 第四行
		Row t4 = sheet.createRow(3);
		for (int i = 0; i < title1.length; i++) {
			Cell t4_1 = t4.createCell(i, CellType.STRING);
			t4_1.setCellStyle(cellStyle.get("title"));
			t4_1.setCellValue(title1[i]);
		}

		// 第五行
		Row t5 = sheet.createRow(4);
		for (int i = 0; i < title2.length; i++) {
			Cell t5_1 = t5.createCell(i, CellType.STRING);
			t5_1.setCellStyle(cellStyle.get("title"));
			t5_1.setCellValue(title2[i]);
		}

		CellRangeAddress region6 = new CellRangeAddress(3, 4, 0, 0);
		sheet.addMergedRegion(region6);

		CellRangeAddress region7 = new CellRangeAddress(3, 4, 1, 1);
		sheet.addMergedRegion(region7);

		CellRangeAddress region8 = new CellRangeAddress(3, 4, 2, 2);
		sheet.addMergedRegion(region8);

		CellRangeAddress region9 = new CellRangeAddress(3, 3, 3, 53);
		sheet.addMergedRegion(region9);

		CellRangeAddress region10 = new CellRangeAddress(3, 3, 54, 61);
		sheet.addMergedRegion(region10);

		CellRangeAddress region11 = new CellRangeAddress(3, 4, 62, 62);
		sheet.addMergedRegion(region11);

	}

	private static void createTotal(XSSFWorkbook wb, String sheetName, Map<String, XSSFCellStyle> cellStyle) {
		XSSFSheet total = wb.getSheet("汇总");

		LocalDate sheetDate = LocalDate.parse(sheetName + "01日", DateTimeFormatter.ofPattern("yyyy年MM月dd日"));

		if (null == total.getRow(1) || null == total.getRow(1).getCell(1)
				|| StringUtils.isEmpty(total.getRow(1).getCell(1).getStringCellValue())) {
			Row row = total.createRow(1);
			Cell yearCell = row.createCell(1);
			yearCell.setCellStyle(cellStyle.get("title"));
			yearCell.setCellValue(sheetDate.getYear() + "年");

			for (int i = 1; i <= 12; i++) {
				Row monthRow = total.createRow(i + 1);
				Cell monthCell = monthRow.createCell(0);
				monthCell.setCellStyle(cellStyle.get("title3"));
				monthCell.setCellValue(i + "月");

				Cell monthValueCell = monthRow.createCell(1);
				monthValueCell.setCellStyle(cellStyle.get("cell_right2"));
				monthValueCell.setBlank();
			}
		}

		//开始获取月度收据金额总和，需求为除去YHSK类型的其余所有收据合计数
		Row row = total.getRow(sheetDate.getMonthValue() + 1);
		Cell monthValueCell = row.getCell(1);
		
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {

			@Override
			public void execute(Connection conn) throws SQLException {
				BigDecimal totalAmount=new BigDecimal("0");
							
				Statement st = conn.createStatement();
				ResultSet rs = st.executeQuery("select sum(amount) as totalAmount from t_community_receipt where receiptDate>='"
						+ sheetDate.with(TemporalAdjusters.firstDayOfMonth()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) 
						+ " 00:00:00' and receiptDate<='"
						+ sheetDate.with(TemporalAdjusters.lastDayOfMonth()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
						+ " 23:59:50' and receiptCode not like '%YHSK%'");

				while (rs.next()) {
					totalAmount = new BigDecimal(rs.getDouble("totalAmount"));
				}

				monthValueCell.setCellValue(totalAmount.setScale(2, RoundingMode.HALF_UP).doubleValue());
			}
		});
		session.close();


		Row totalRow = total.createRow(0);
		Cell totalCell = totalRow.createCell(1);
		totalCell.setCellStyle(cellStyle.get("total_double"));
		totalCell.setCellFormula("sum(B3:B14)");

		total.setColumnWidth(1, 12 * 256);
	}

	private static XSSFWorkbook getDjbExcel(String newSheet) {

		XSSFWorkbook wb = new XSSFWorkbook();
		wb.createSheet("汇总");

		File file = new File(
				"/data/resource/communityFile/djb/" + newSheet.substring(0, newSheet.indexOf("年") + 1) + "收据登记表.xlsx");

		if (!file.getParentFile().exists()) {
			file.getParentFile().mkdirs();
			return wb;
		}

		try {
			IOUtils.setByteArrayMaxOverride(1000000000);
			wb = new XSSFWorkbook(new FileInputStream(file));

			if (file.exists()) {
				if (wb.getNumberOfSheets() == 1) {
					if (wb.getSheetIndex("汇总") == 0) {
						return wb;
					} else {
						if ((LocalDate.parse(newSheet + "01日", DateTimeFormatter.ofPattern("yyyy年MM月dd日"))
								.minusMonths(1)
								.isEqual(LocalDate.parse(
										wb.getSheetAt(wb.getNumberOfSheets() - 1).getSheetName() + "01日",
										DateTimeFormatter.ofPattern("yyyy年MM月dd日"))))
								|| wb.getSheetIndex(newSheet) == 0) {
							wb.createSheet("汇总");
							wb.setSheetOrder("汇总", 0);
						} else {
							wb = new XSSFWorkbook();
							wb.createSheet("汇总");
						}
					}
				} else {
					if (wb.getSheetIndex("汇总") == 0 && ((wb.getSheetIndex(newSheet) < 0 && LocalDate
							.parse(newSheet + "01日", DateTimeFormatter.ofPattern("yyyy年MM月dd日")).minusMonths(1)
							.isEqual(LocalDate.parse(wb.getSheetAt(wb.getNumberOfSheets() - 1).getSheetName() + "01日",
									DateTimeFormatter.ofPattern("yyyy年MM月dd日"))))
							|| wb.getSheetIndex(newSheet) > 0)) {
						return wb;
					} else {
						wb = new XSSFWorkbook();
						wb.createSheet("汇总");
					}
				}
			} else {
				wb = new XSSFWorkbook();
				wb.createSheet("汇总");
			}
		} catch (Exception ex) {
			log.error(ex.getMessage());
			wb = new XSSFWorkbook();
			wb.createSheet("汇总");
		}

		return wb;
	}

}
