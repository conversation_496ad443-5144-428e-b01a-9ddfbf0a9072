package com.foshan.service.community;

import com.foshan.form.community.request.CommunityContractReq;
import com.foshan.form.response.IResponse;

public interface ICommunityContractService {
    public IResponse getCommunityContractList(CommunityContractReq req);
	public IResponse addCommunityContract(CommunityContractReq req);
	public IResponse modifyCommunityContract(CommunityContractReq req);
	public IResponse deleteCommunityContract(CommunityContractReq req);
	public IResponse getCommunityContractInfo(CommunityContractReq req);
}
