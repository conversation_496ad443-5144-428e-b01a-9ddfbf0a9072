package com.foshan.service.community.shiro.realmhandler;

import java.util.Map;

import com.foshan.util.SpringHandler;

/**
 * 会员登录鉴权处理器上下文，根据处理器注解类型和传入类型实例化处理器，
 * @see RealmHandlerProcessor，通过RealmHandlerProcessor将实际处理器类注册到RealmHandlerContext。
 * @see MemberServiceImpl,MemberServiceImpl的memberLogin方法中匹配对应类型获取处理器实例进行登陆凭证处理。
 * @see ShiroMemberRealm，ShiroMemberRealm匹配对应类型获取处理器实例进行身份验证信息获取。
 * RealmHandlerProcessor将扫描到的handler注册到本类实例
 * <AUTHOR>
 *
 */
public class RealmHandlerContext {
	@SuppressWarnings("rawtypes")
	private Map<String, Class> handlerMap;
	
	@SuppressWarnings("rawtypes")
	public RealmHandlerContext(Map<String, Class> handlerMap) {
		this.handlerMap = handlerMap;
	}
	
	@SuppressWarnings("rawtypes")
	public AbstractMemberRealmHandler getInstance(String type) {
		Class clazz = handlerMap.get(type);
		if(clazz == null) {
			throw new IllegalArgumentException("无此类型的MemberRealmHandler：" + type);
		}
		return (AbstractMemberRealmHandler) SpringHandler.getBean(clazz);
	}
}
