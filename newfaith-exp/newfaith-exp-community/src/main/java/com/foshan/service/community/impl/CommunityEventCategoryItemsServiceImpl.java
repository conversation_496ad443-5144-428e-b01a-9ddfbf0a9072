package com.foshan.service.community.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import com.foshan.service.annotation.Audit;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityEventCategoryEntity;
import com.foshan.entity.community.CommunityEventCategoryItemsEntity;
import com.foshan.form.community.CommunityEventCategoryItemsForm;
import com.foshan.form.community.request.CommunityEventCategoryItemsReq;
import com.foshan.form.community.response.communityEventCategoryItems.AddCommunityEventCategoryItemsRes;
import com.foshan.form.community.response.communityEventCategoryItems.GetCommunityEventCategoryItemsInfo;
import com.foshan.form.community.response.communityEventCategoryItems.GetCommunityEventCategoryItemsListRes;
import com.foshan.form.community.response.communityEventCategoryItems.ModifyCommunityEventCategoryItemsRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.ICommunityEventCategoryItemsService;

@Transactional
@Service("communityEventCategoryItemsService")
public class CommunityEventCategoryItemsServiceImpl extends GenericCommunityService implements ICommunityEventCategoryItemsService{
	@SuppressWarnings("unchecked")
	@Override
	public IResponse getCommunityEventCategoryItemsList(CommunityEventCategoryItemsReq req) {
		GetCommunityEventCategoryItemsListRes res = new GetCommunityEventCategoryItemsListRes();
		Page<CommunityEventCategoryItemsEntity> page = new Page<CommunityEventCategoryItemsEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

		StringBuilder hql = new StringBuilder("select distinct a from CommunityEventCategoryItemsEntity a");
	
		hql.append(StringUtils.isNotEmpty(req.getDecorationItemsIdList()) ? " inner join  a.decorationItemsList b where b.id in("+req.getDecorationItemsIdList()+")": " where 1=1 ")
			.append(null!=req.getEventCategoryId() ?" and a.eventCategory.id="+req.getEventCategoryId():"")
			.append(StringUtils.isNotEmpty(req.getItemkey()) ?
				" and a.itemkey like '%" + req.getItemkey() + "%'" :"")
			.append(StringUtils.isNotEmpty(req.getItemName()) ?
				" and a.itemName like '%" + req.getItemName() + "%'" :"")
			.append(StringUtils.isNotEmpty(req.getItemTag()) ? " and a.itemTag like'%"+req.getItemTag()+"%'":"")
			
			.append(StringUtils.isNotEmpty(req.getDecorationItemsNameList()) ? "and a.decorationItemsList.itemName in('"+req.getDecorationItemsNameList().replaceAll(",", "','")+"')":"");
		hql.append(" ORDER BY a.orders ASC");
		page = communityEventCategoryItemsDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		ObjectMapper mapper = new ObjectMapper();
		page.getResultList().forEach(o -> {
			
			List<Object> optionslList = new ArrayList<Object>();
			try {
				if(StringUtils.isNoneEmpty(o.getOptions())) {
					optionslList =mapper.readValue(o.getOptions(), ArrayList.class);
				}
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JSONException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(o.getId(),
					o.getItemName(),o.getItemkey(),o.getOrders(),o.getDataType(),optionslList,o.getIsRequiredng());
			eventCategoryItemsForm.setItemTag(StringUtils.isNotEmpty(o.getItemTag()) ? o.getItemTag() :"");
			eventCategoryItemsForm.setItemTag(StringUtils.isNotEmpty(o.getItemTag()) ? o.getItemTag() :"");
			eventCategoryItemsForm.setReflectionField(o.getReflectionField());
			eventCategoryItemsForm.setSubEventCategoryItemsList(getSubEventCategoryItemsList(o.getSubEventCategoryItemsList()));
			eventCategoryItemsForm.setIsCascade(o.getIsCascade());
			eventCategoryItemsForm.setCascadeValue(StringUtils.isNotEmpty(o.getCascadeValue()) ? o.getCascadeValue() :"");
			res.getEventCategoryItemsList().add(eventCategoryItemsForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@SuppressWarnings("unchecked")
	public List<CommunityEventCategoryItemsForm> getSubEventCategoryItemsList(List<CommunityEventCategoryItemsEntity> list){
		List<CommunityEventCategoryItemsForm> subItemsList = new ArrayList<CommunityEventCategoryItemsForm>();
		for(CommunityEventCategoryItemsEntity o : list){
			List<Object> optionslList = new ArrayList<Object>();
			try {
				if(StringUtils.isNoneEmpty(o.getOptions())) {
					optionslList =mapper.readValue(o.getOptions(), ArrayList.class);
				}
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JSONException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(o.getId(),
					o.getItemName(),o.getItemkey(),o.getOrders(),o.getDataType(),optionslList,o.getIsRequiredng());
			eventCategoryItemsForm.setItemTag(StringUtils.isNotEmpty(o.getItemTag()) ? o.getItemTag() :"");
			eventCategoryItemsForm.setItemTag(StringUtils.isNotEmpty(o.getItemTag()) ? o.getItemTag() :"");
			eventCategoryItemsForm.setReflectionField(o.getReflectionField());
			eventCategoryItemsForm.setIsCascade(o.getIsCascade());
			eventCategoryItemsForm.setCascadeValue(StringUtils.isNotEmpty(o.getCascadeValue()) ? o.getCascadeValue() :"");
			subItemsList.add(eventCategoryItemsForm);
		}
		return subItemsList;
	}

	@Audit(operate = "新增事件类型项目")
	@Override
	public IResponse addCommunityEventCategoryItems(CommunityEventCategoryItemsReq req) {
		AddCommunityEventCategoryItemsRes res = new AddCommunityEventCategoryItemsRes();
		if (StringUtils.isNotEmpty(req.getItemName()) 
				&& null!=req.getOrders() && StringUtils.isNotEmpty(req.getItemkey()) && 
				null!=req.getEventCategoryId() && null!=req.getDataType()) {
			if((req.getDataType()==4 ||req.getDataType()==5)  && !StringUtils.isNotEmpty(req.getOptions())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}
			CommunityEventCategoryEntity eventCategory = 
					communityEventCategoryDao.get(req.getEventCategoryId()) ;
			if(null == eventCategory){
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			CommunityEventCategoryItemsEntity eventCategoryItems = new CommunityEventCategoryItemsEntity();
			eventCategoryItems.setEventCategory(eventCategory);
			eventCategoryItems.setItemkey(req.getItemkey());
			eventCategoryItems.setItemName(req.getItemName());
			eventCategoryItems.setOrders(req.getOrders());
			eventCategoryItems.setDataType(req.getDataType());
			eventCategoryItems.setOptions(StringUtils.isNotEmpty(req.getOptions()) ? req.getOptions() :"");
			eventCategoryItems.setIsRequiredng(null!=req.getIsRequiredng() ? req.getIsRequiredng() : 0);
			eventCategoryItems.setItemTag(StringUtils.isNotEmpty(req.getItemTag()) ? req.getItemTag() :"");
			eventCategoryItems.setReflectionField(StringUtils.isNotEmpty(req.getReflectionField()) ? req.getReflectionField() :"");
			eventCategoryItems.setIsCascade(null!=req.getIsCascade()?req.getIsCascade():0);
			eventCategoryItems.setCascadeValue(StringUtils.isNotEmpty(req.getCascadeValue()) ? req.getCascadeValue() :"");

			communityEventCategoryItemsDao.save(eventCategoryItems);

			res.setEventCategoryItemsId(eventCategoryItems.getId());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Audit(operate = "修改事件类型项目")
	@Override
	public IResponse modifyCommunityEventCategoryItems(CommunityEventCategoryItemsReq req) {
		ModifyCommunityEventCategoryItemsRes res = new ModifyCommunityEventCategoryItemsRes();
		if (null!=req.getEventCategoryItemsId() && StringUtils.isNotEmpty(req.getItemName()) 
				&& null!=req.getOrders() && StringUtils.isNotEmpty(req.getItemkey()) && 
				null!=req.getEventCategoryId() && null!=req.getDataType()) {
			if((req.getDataType()==4 ||req.getDataType()==5) && !StringUtils.isNotEmpty(req.getOptions())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}
			CommunityEventCategoryEntity eventCategory = 
					communityEventCategoryDao.get(req.getEventCategoryId()) ;
			CommunityEventCategoryItemsEntity eventCategoryItems = communityEventCategoryItemsDao.get(req.getEventCategoryItemsId());
			if(null==eventCategoryItems || null == eventCategory){
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			eventCategoryItems.setEventCategory(eventCategory);
			eventCategoryItems.setItemkey(req.getItemkey());
			eventCategoryItems.setItemName(req.getItemName());
			eventCategoryItems.setOrders(req.getOrders());
			eventCategoryItems.setDataType(req.getDataType());
			eventCategoryItems.setOptions(StringUtils.isNotEmpty(req.getOptions()) ? req.getOptions() :eventCategoryItems.getOptions());
			eventCategoryItems.setIsRequiredng(null!=req.getIsRequiredng() ? req.getIsRequiredng() : eventCategoryItems.getIsRequiredng());
			eventCategoryItems.setItemTag(StringUtils.isNotEmpty(req.getItemTag()) ? req.getItemTag() :eventCategoryItems.getItemTag());
			eventCategoryItems.setReflectionField(StringUtils.isNotEmpty(req.getReflectionField()) ? 
					req.getReflectionField() :eventCategoryItems.getReflectionField());
			eventCategoryItems.setIsCascade(null!=req.getIsCascade()?req.getIsCascade():eventCategoryItems.getIsCascade());
			eventCategoryItems.setCascadeValue(StringUtils.isNotEmpty(req.getCascadeValue()) ? req.getCascadeValue() :eventCategoryItems.getCascadeValue());
			
			res.setEventCategoryItemsId(eventCategoryItems.getId());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Audit(operate = "删除事件类型项目")
	@Override
	public IResponse deleteCommunityEventCategoryItems(CommunityEventCategoryItemsReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getEventCategoryItemsId()) {
			communityEventCategoryItemsDao.deleteById(req.getEventCategoryItemsId());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getCommunityEventCategoryItemsInfo(CommunityEventCategoryItemsReq req) {
		GetCommunityEventCategoryItemsInfo res = new GetCommunityEventCategoryItemsInfo();
		if (null != req.getEventCategoryItemsId()) {
			CommunityEventCategoryItemsEntity eventCategoryItems = communityEventCategoryItemsDao.get(req.getEventCategoryItemsId());
			if (null != eventCategoryItems) {
				List<Object> optionslList = new ArrayList<Object>();
				ObjectMapper mapper = new ObjectMapper();
				try {
					if(StringUtils.isNoneEmpty(eventCategoryItems.getOptions())) {
						optionslList =mapper.readValue(eventCategoryItems.getOptions(), ArrayList.class);
					}
				} catch (JsonParseException e) {
					e.printStackTrace();
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JSONException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
				CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(eventCategoryItems.getId(),
						eventCategoryItems.getItemName(),eventCategoryItems.getItemkey(),eventCategoryItems.getOrders(),
						eventCategoryItems.getDataType(),optionslList,eventCategoryItems.getIsRequiredng()) ;
				eventCategoryItemsForm.setItemTag(StringUtils.isNotEmpty(eventCategoryItems.getItemTag()) ? eventCategoryItems.getItemTag() :"");
				eventCategoryItemsForm.setReflectionField(eventCategoryItems.getReflectionField());
				eventCategoryItemsForm.setSubEventCategoryItemsList(getSubEventCategoryItemsList(eventCategoryItems.getSubEventCategoryItemsList()));
				eventCategoryItemsForm.setIsCascade(eventCategoryItems.getIsCascade());
				eventCategoryItemsForm.setCascadeValue(StringUtils.isNotEmpty(eventCategoryItems.getCascadeValue()) ? eventCategoryItems.getCascadeValue() :"");
				
				res.setCommunityEventCategoryItemsForm(eventCategoryItemsForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
}
