package com.foshan.service.community;

import java.text.ParseException;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityAddBreachReq;
import com.foshan.form.community.request.CommunityAddReceivablesForEstateReq;
import com.foshan.form.community.request.CommunityDeleteReceivablesByDetails;
import com.foshan.form.community.request.CommunityMeterAllocationReq;
import com.foshan.form.community.request.CommunityPreReceivablesReq;
import com.foshan.form.community.request.CommunityReceivablesReq;
import com.foshan.form.community.request.ExportExcelReq;
import com.foshan.form.request.WxParameterReq;
import com.foshan.form.response.IResponse;

public interface ICommunityReceivablesService {
    public IResponse getCommunityReceivablesList(CommunityReceivablesReq req);
    public IResponse getAdminCommunityReceivablesList(CommunityReceivablesReq req);
	public IResponse addCommunityReceivables(CommunityReceivablesReq req);
	public IResponse modifyCommunityReceivables(CommunityReceivablesReq req);
	public IResponse deleteCommunityReceivables(CommunityReceivablesReq req);
	public IResponse getCommunityReceivablesInfo(CommunityReceivablesReq req);
	public IResponse exportSendDisc(CommunityReceivablesReq req,HttpServletResponse response);
	public IResponse getExportSendDiscList(CommunityReceivablesReq req);
	public IResponse addPreReceivables(CommunityPreReceivablesReq req);
	public IResponse addBreachReceivables(CommunityAddBreachReq req);
	public IResponse addManagerReceivables(CommunityAddReceivablesForEstateReq req);
	public IResponse deleteManagerReceivablesByDetail(CommunityDeleteReceivablesByDetails req);
	public IResponse createPayingInSlipPdf(CommunityReceivablesReq req);
	public IResponse getCommunityReceivablesBill(WxParameterReq req);
	public IResponse addRentReceivables(CommunityAddReceivablesForEstateReq req);
	public IResponse rentTrialMeterOperation(CommunityAddReceivablesForEstateReq req);
	public IResponse getRentTrialMeterOperationList(CommunityMeterAllocationReq req) ;
	public IResponse submitRentMeterAllocation();
	public IResponse exportRentTrialMeterOperation(ExportExcelReq req, HttpServletResponse response) throws ParseException;
	public IResponse sendReceivablesSms(CommunityReceivablesReq req);
	public IResponse createPaymentQrCode(CommunityReceivablesReq req);
	public IResponse createPayingInSlipPdfTest(CommunityReceivablesReq req);
	//public IResponse updateReservedField();
}

