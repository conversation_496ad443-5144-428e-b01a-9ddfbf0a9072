package com.foshan.service.community;

import javax.servlet.http.HttpServletRequest;

import com.foshan.form.community.request.ImportExcelReq;
import com.foshan.form.response.IResponse;

public interface IImportExcelService {
	public IResponse importUnitInfo(HttpServletRequest request,ImportExcelReq req);
	public IResponse importMeterRecord(HttpServletRequest request,Integer replace);
	public IResponse importBackDisk(HttpServletRequest request,Integer templateType,Integer bankDepositBatchId,String depositDate);
	public IResponse importSmartcardId(HttpServletRequest request); 
	public IResponse importMeter(HttpServletRequest request); 
	public IResponse importMeterCode(HttpServletRequest request,ImportExcelReq req);
	public IResponse importBuilding(HttpServletRequest request,ImportExcelReq req);
	public IResponse importParkingRelation(HttpServletRequest request);
	public IResponse importInitializeAgreementRelation(HttpServletRequest request) ;
	
}
