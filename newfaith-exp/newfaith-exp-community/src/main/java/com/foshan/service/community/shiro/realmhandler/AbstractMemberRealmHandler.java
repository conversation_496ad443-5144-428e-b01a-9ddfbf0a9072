package com.foshan.service.community.shiro.realmhandler;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.subject.Subject;

import com.foshan.dao.IDepartmentDao;
import com.foshan.dao.IRoleDao;
import com.foshan.dao.community.ICommunityMemberDao;
import com.foshan.dao.community.ICommunityRoleDao;
import com.foshan.form.community.request.CommunityMemberLoginReq;
import com.foshan.form.response.IResponse;
import com.foshan.util.ContextInfo;
import com.foshan.util.community.CommunityContextInfo;

/**
 * 登陆鉴权处理抽象类，其实现类根据具体逻辑实现登陆凭证处理和身份校验信息获取，实际逻辑处理器的实现所在包：com.foshan.service.shop.shiro.realmhandler.handler
 * <AUTHOR>
 *
 */
public abstract class AbstractMemberRealmHandler {
	
	@Resource(name = "communityMemberDao")
	protected ICommunityMemberDao communityMemberDao;
	
	@Resource(name = "communityRoleDao")
	protected ICommunityRoleDao communityRoleDao;
	
	@Resource(name = "roleDao")
	protected IRoleDao roleDao;
	
	@Resource(name = "departmentDao")
	protected IDepartmentDao departmentDao;
	
	@Resource
	protected ContextInfo contextInfo;
	
	@Resource
	protected CommunityContextInfo communityContextInfo;
	
//	@Autowired
//	protected IWeiXinApiService weiXinApiService;
	
	public abstract SimpleAuthenticationInfo handleAuthenticationInfo(AuthenticationToken token ,String realmName);
	public abstract IResponse memberLogin(CommunityMemberLoginReq req, HttpServletRequest request, Subject curUser);
}
