package com.foshan.service.community.impl;

//import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
//import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

//import javax.imageio.ImageIO;
import javax.persistence.LockModeType;
import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.TransformerException;

import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
//import org.xml.sax.SAXException;
import org.xml.sax.SAXException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.DictionaryDataEntity;
import com.foshan.entity.SnEntity;
import com.foshan.entity.community.EmergencyMessageEntity;
//import com.foshan.entity.context.EntityContext;
import com.foshan.form.UploadForm;
import com.foshan.form.community.EmergencyMessageForm;
//import com.foshan.form.community.ReservedFieldForm;
import com.foshan.form.community.request.EmergencyMessageReq;
import com.foshan.form.community.response.emergencyMessage.GetEmergencyMessageListRes;
//import com.foshan.form.request.UploadReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
//import com.foshan.form.response.upload.UploadRes;
import com.foshan.service.community.IEmergencyMessageService;
//import com.foshan.service.community.task.CommunityReceivableTask;
//import com.foshan.util.CodeUtil;
import com.foshan.util.CompressionFileUtil;
import com.foshan.util.DateUtil;
//import com.foshan.util.DigestUtil;
import com.foshan.util.HttpClientUtil;
//import com.foshan.util.PicUtil;
import com.foshan.util.XmlUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional
@Service("emergencyMessageService")
public class EmergencyMessageServiceImpl extends GenericCommunityService implements IEmergencyMessageService {
	private static String version="";
	private static String emergencyMessageId="";
	private static String ebmVersion = "";
	private static String sendUrl = "";
	private static String coupleBackUrl = "";
    
    public IResponse gainEmergencyMessageParameter() {
    	GenericResponse res = new GenericResponse();
		List<DictionaryDataEntity> dictionaryDataList = dictionaryDataDao
				.getListByHql("select distinct a from DictionaryDataEntity a "
						+ " inner join a.dictionary  b where b.directoryCode='emergencyMessageParameter' and a.state=1", "");
		if (null != dictionaryDataList && dictionaryDataList.size() > 0) {
			for(DictionaryDataEntity d : dictionaryDataList) {
				if(d.getDataName().equals("EBDVersion")) {
					version = d.getDataKey();
				}else if(d.getDataName().equals("emergencyMessageId")){
					emergencyMessageId = d.getDataKey();
				}else if(d.getDataName().equals("EBMVersion")) {
					ebmVersion = d.getDataKey();
				}else if(d.getDataName().equals("sendUrl")) {
					sendUrl = d.getDataKey();
				}else if(d.getDataName().equals("coupleBackUrl")) {
					coupleBackUrl = d.getDataKey();
				}
			}
			if(StringUtils.isEmpty(version) || StringUtils.isEmpty(emergencyMessageId)
					|| StringUtils.isEmpty(ebmVersion)
					|| StringUtils.isEmpty(sendUrl)|| StringUtils.isEmpty(coupleBackUrl)) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo("请在数据字典里配置“康养相关参数（emergencyMessageId、EBDVersion、EBMVersion、sendUrl和coupleBackUrl）”,directoryCode为\"emergencyMessageParameter\"！");
				return res;
			}
    		res.setRet(ResponseContext.RES_SUCCESS_CODE);
    		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("请在数据字典里配置“康养相关参数（emergencyMessageId、EBDVersion、EBMVersion、sendUrl和coupleBackUrl）”,directoryCode为\\\"emergencyMessageParameter\\\"！");
			return res;
		}
		return res;
    }
	
	private Integer getSnLastValue(String snType) {
		Integer result = 0;
		String hql = "select sn from SnEntity sn where sn.snType = :type";
		@SuppressWarnings("unchecked")
		List<SnEntity> resultList = snDao.createQuery(hql).setLockMode(LockModeType.PESSIMISTIC_WRITE)
				.setParameter("type", snType).list();
		if (null!=resultList && resultList.size() > 0) {
			SnEntity sn = resultList.get(0);
			if(sn.getResetPolicy()==1 && !DateUtil.format(new Date(),0).equals(
					DateUtil.formatShortFormat(sn.getLastModifyTime()))) {
				sn.setLastValue(result);
			}
			result = sn.getLastValue();
			sn.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
			sn.setLastValue(result + 1);
		}

		return result;
	}
	
	public String getSerialNumber() {
		String num = getSnLastValue("MSGPACKAGENO")+"";
		return autoCompletionSerialNumber(num,16);
	}
	
	public String getMsgNumber() {
		String num = getSnLastValue("MSGNO")+"";
		return autoCompletionSerialNumber(num,4);
	}
	
	
	
	public String autoCompletionSerialNumber(String serialNumber,int length) {
		if(serialNumber.length()<length) {
			return autoCompletionSerialNumber("0"+serialNumber, length);
		}
		return serialNumber;
	}
	
	@SuppressWarnings("unchecked")
	public IResponse createXml(EmergencyMessageReq req) {
		GenericResponse res = new GenericResponse();
		if(null!=req.getMsgBasicInfo() && null!=req.getMsgBasicInfo().getMsgType() && 
				StringUtils.isNotEmpty(req.getMsgBasicInfo().getSenderName()) 
				&& StringUtils.isNotEmpty(req.getMsgBasicInfo().getEventType()) && 
				null!=req.getMsgBasicInfo().getSeverity()
				&& StringUtils.isNotEmpty(req.getMsgBasicInfo().getStartTime()) && 
				StringUtils.isNotEmpty(req.getMsgBasicInfo().getEndTime())
				) {
			EmergencyMessageEntity emergencyMessage = new EmergencyMessageEntity();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
			String today = sdf.format(new Date());
			String dir = contextInfo.getAssetFilePath() + File.separator + "emergencyMsg" + File.separator + today
					+ File.separator;
			createMultilayerFile(dir);
			Document document;
			List<File> listFile = new ArrayList<>();
			try {
				String msgNumber = getMsgNumber();
				String serialNumber = "10"+emergencyMessageId+getSerialNumber();
				document = XmlUtil.createDocument();
				Element ebd = XmlUtil.rootElement(document, "EBD");
				XmlUtil.addAttrtoElement(document, ebd, "EBDVersion", "1");
				XmlUtil.addAttrtoElement(document, ebd, "EBDID", serialNumber);
				XmlUtil.addAttrtoElement(document, ebd, "EBDType", "EBM");
				Element src = XmlUtil.docCreateChildElement(document, ebd,"SRC");
				XmlUtil.addAttrtoElement(document, src, "EBRID", emergencyMessageId);
				XmlUtil.addAttrtoElement(document, src, "URL", coupleBackUrl);
				Element dest = XmlUtil.docCreateChildElement(document, ebd,"DEST");
				XmlUtil.addAttrtoElement(document, dest, "EBRID", emergencyMessageId);
				XmlUtil.addAttrtoElement(document, ebd, "EBDTime", DateUtil.format(new Date(),1));
				Element ebm = XmlUtil.docCreateChildElement(document, ebd,"EBM");
				XmlUtil.addAttrtoElement(document, ebm, "EBMVersion", ebmVersion);
				String msgCode = emergencyMessageId+DateUtil.format(new Date(),0).replaceAll("-", "")+msgNumber;
				XmlUtil.addAttrtoElement(document, ebm, "EBMID", msgCode);
				emergencyMessage.setMsgCode(msgCode);
				if(req.getMsgBasicInfo().getMsgType() == 1) {
					Element msgBasicInfo = XmlUtil.docCreateChildElement(document, ebm,"MsgBasicInfo");
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "MsgType", req.getMsgBasicInfo().getMsgType()+"");
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "SenderName", req.getMsgBasicInfo().getSenderName()+"");
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "SenderCode", emergencyMessageId);
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "SendTime", DateUtil.format(new Date(),1));
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "EventType", req.getMsgBasicInfo().getEventType());
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "Severity", req.getMsgBasicInfo().getSeverity()+"");
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "StartTime", req.getMsgBasicInfo().getStartTime());
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "EndTime", req.getMsgBasicInfo().getEndTime());
					emergencyMessage.setMsgType(req.getMsgBasicInfo().getMsgType());
					emergencyMessage.setSenderName(req.getMsgBasicInfo().getSenderName());
					emergencyMessage.setSenderCode(emergencyMessageId);
					emergencyMessage.setEventType(req.getMsgBasicInfo().getEventType());
					emergencyMessage.setSeverity(req.getMsgBasicInfo().getSeverity());
					try {
						emergencyMessage.setStartTime(new Timestamp(DateUtil.parse(req.getMsgBasicInfo().getStartTime(),1).getTime()));
						emergencyMessage.setEndTime(new Timestamp(DateUtil.parse(req.getMsgBasicInfo().getEndTime(),1).getTime()));
					} catch (ParseException e) {
						e.printStackTrace();
					}
					
				}else if(req.getMsgBasicInfo().getMsgType() == 2){
					Element relatedInfo = XmlUtil.docCreateChildElement(document, ebm,"RelatedInfo");
					EmergencyMessageEntity parentEmergencyMessage = emergencyMessageDao.get(req.getEmergencyMessageId());
					if(null == parentEmergencyMessage) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
						return res;
					}
					XmlUtil.addAttrtoElement(document, relatedInfo, "EBMID", parentEmergencyMessage.getMsgCode());
					emergencyMessage.setMsgType(req.getMsgBasicInfo().getMsgType());
					emergencyMessage.setParentEmergencyMessage(parentEmergencyMessage);
					Element msgBasicInfo = XmlUtil.docCreateChildElement(document, ebm,"MsgBasicInfo");
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "MsgType", req.getMsgBasicInfo().getMsgType()+"");
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "SenderName", parentEmergencyMessage.getSenderName()+"");
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "SenderCode", parentEmergencyMessage.getSenderCode());
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "SendTime", DateUtil.format(parentEmergencyMessage.getSendTime(),1));
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "EventType", parentEmergencyMessage.getEventType());
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "Severity", parentEmergencyMessage.getSeverity()+"");
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "StartTime", DateUtil.formatLongFormat(parentEmergencyMessage.getStartTime()));
					XmlUtil.addAttrtoElement(document, msgBasicInfo, "EndTime",  DateUtil.formatLongFormat(parentEmergencyMessage.getEndTime()));
				}
				
				if(req.getMsgBasicInfo().getMsgType()!=2 && 
						StringUtils.isNotEmpty(req.getMsgContent().getMsgTitle()) && 
						StringUtils.isNotEmpty(req.getMsgContent().getMsgDesc()) && 
						StringUtils.isNotEmpty(req.getMsgContent().getAreaCode())) {
					if(null!=req.getMsgContent()) {
						Element msgContent = XmlUtil.docCreateChildElement(document, ebm,"MsgContent");
						XmlUtil.addAttrtoElement(document, msgContent, "LanguageCode", 
								StringUtils.isNotEmpty(req.getMsgContent().getLanguageCode()) ? req.getMsgContent().getLanguageCode() :"zho");
						XmlUtil.addAttrtoElement(document, msgContent, "MsgTitle", req.getMsgContent().getMsgTitle());
						XmlUtil.addAttrtoElement(document, msgContent, "MsgDesc", req.getMsgContent().getMsgDesc());
						XmlUtil.addAttrtoElement(document, msgContent, "AreaCode", req.getMsgContent().getAreaCode());
						
						emergencyMessage.setMsgTitle(req.getMsgContent().getMsgTitle());
						emergencyMessage.setMsgDesc(req.getMsgContent().getMsgDesc());
						emergencyMessage.setAreaCode(req.getMsgContent().getAreaCode());
						if(null!=req.getMsgContent().getFileId()) {
							AssetEntity asset = assetDao.get(req.getMsgContent().getFileId());
							emergencyMessage.setAsset(asset);
							if (null != asset && StringUtils.isNotEmpty(asset.getImageFile()) &&
									(asset.getImageFile().endsWith(".mp3")||asset.getImageFile().endsWith(".MP3"))) {
								String[] str = asset.getImageFile().split("/");
								String fileName = str[str.length-1];
								String filePath = asset.getImageFile().replaceFirst(contextInfo.assetFileUrl, "");
								File file = new File(contextInfo.getAssetFilePath() +File.separator + filePath);
								
								String newPath = asset.getImageFile().replace(fileName, 
										"EBDR_"+DateUtil.format(new Date(),1).replaceAll("-", "")
										.replaceAll(":", "").replaceAll(":", "").replaceAll(" ", "")+msgNumber+".MP3");
								asset.setImageFile(newPath);
								newPath = newPath.replaceFirst(contextInfo.assetFileUrl, "");
						        File newf = new File(contextInfo.getAssetFilePath() + File.separator+newPath);
						        file.renameTo(newf);
								Element auxiliary = XmlUtil.docCreateChildElement(document, msgContent,"Auxiliary");
								XmlUtil.addAttrtoElement(document, auxiliary, "AuxiliaryType", "2");
								XmlUtil.addAttrtoElement(document, auxiliary, "AuxiliaryDesc", "EBDR_"+msgNumber+".MP3");
								XmlUtil.addAttrtoElement(document, auxiliary, "Size", newf.length()+"");
								listFile.add(newf);
							}else {
								res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
								res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
								return res;
							}
						}else if( null!=req.getMsgContent().getAuxiliary() && StringUtils.isNotEmpty(req.getMsgContent().getAuxiliary().getAuxiliaryDesc())){
							Element auxiliary = XmlUtil.docCreateChildElement(document, msgContent,"Auxiliary");
							XmlUtil.addAttrtoElement(document, auxiliary, "AuxiliaryType", null!=req.getMsgContent().getAuxiliary().getAuxiliaryType() 
									? req.getMsgContent().getAuxiliary().getAuxiliaryType()+"" :"2");
							XmlUtil.addAttrtoElement(document, auxiliary, "AuxiliaryDesc", req.getMsgContent().getAuxiliary().getAuxiliaryDesc());
							XmlUtil.addAttrtoElement(document, auxiliary, "Size", null!=req.getMsgContent().getAuxiliary().getSize() ? 
									req.getMsgContent().getAuxiliary().getSize()+"" : "");
						}
					}
				}
				try {
					String xmlFileName="EBDB_"+serialNumber+".xml";
					String tarFileName = "EBDT_"+serialNumber+".tar";
					XmlUtil.saveXml(dir+xmlFileName, document,"utf-8");
				    File xmlFile = new File(dir+xmlFileName);
			        listFile.add(xmlFile);
			        String fileZip = dir+tarFileName;
			        packTar(listFile, new File(fileZip));
//			        xmlFile.delete();
			        emergencyMessage.setTarFilePath(fileZip);
			        Map<String, String> map = new HashMap<>();
			        map.put("filename", fileZip);
			        log.info("请求应急广播接口：" + sendUrl+",参数："+map.toString());
			        String str = HttpClientUtil.formUpload(sendUrl,null,map,"application/x-tar");
			        log.info("请求应急广播接口返回：" + str);
			        if(str.contains("<?xml ")) {
			        	Map<String, String> dataMap = new HashMap<>();
			        	try {
							map = XmlUtil.xmlToMap("<?xml "+str.split("<?xml ")[1], dataMap);
						} catch (UnsupportedEncodingException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						} catch (SAXException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						} catch (IOException e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
			        	emergencyMessageDao.save(emergencyMessage);
			    		res.setRet(ResponseContext.RES_SUCCESS_CODE);
			    		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			        }else if(str.contains("\"status\"")){
						try {
							ObjectMapper mapper = new ObjectMapper();
							Map<String, String> dataMap  =  mapper.readValue(str, Map.class);
				    		res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
				    		res.setRetInfo("应急广播返回："+dataMap.get("message"));
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
			    		res.setRet(ResponseContext.RES_SUCCESS_CODE);
			    		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			        }else {
			    		res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
			    		res.setRetInfo("调用应急广播接口异常！");
			        }

		    		return res;
				} catch (FileNotFoundException | TransformerException e) {
					e.printStackTrace();
				}
			} catch (ParserConfigurationException e) {
				e.printStackTrace();
			}
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		return res;
	}

	private boolean createMultilayerFile(String dir) {
		try {
			File dirPath = new File(dir);
			if (!dirPath.exists()) {
				dirPath.mkdirs();
			}
		} catch (Exception e) {
			System.out.println("创建多层目录操作出错: " + e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}
	
	 /**
     * 将一组文件打成 tar 包
     *
     * @param sources 要打包的原文件数组
     * @param target  tar 文件
     */
    public static  Boolean packTar(List<File> sources, File target) {
        FileOutputStream out;
        TarArchiveOutputStream os = null;
        InputStream inputStream = null;
        try {
        	out = new FileOutputStream(target);
        	 os = new TarArchiveOutputStream(out);
            for (File file : sources) {
                // file.getName() 此处如果不填写文件名，则会按照原路径压缩文件
                os.putArchiveEntry(new TarArchiveEntry(file, file.getName()));
                inputStream = new FileInputStream(file);
                IOUtils.copy(inputStream, os);
                os.closeArchiveEntry();

            }
            return true;
        } catch (FileNotFoundException e) {
        	e.printStackTrace();
            return false;
        } catch (Exception e) {
        	e.printStackTrace();
            return false;
        }finally {
        	try {
        		if(null != inputStream) inputStream.close();
        		if(null !=null) {
        			os.flush();
					os.close();
        		}
        		if(null != inputStream) inputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
        }
    }

	@Override
	public IResponse receiveFeedback(HttpServletRequest request, MultipartFile multipartFile) {
		GenericResponse res = new GenericResponse();
		if(null == multipartFile) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("反馈包不能为空！");
			return res;
		}
		List<UploadForm> uploadList = new ArrayList<>();
//		for (MultipartFile tempFile : multipartFile) {
			try {
				UploadForm uploadForm = new UploadForm();
				uploadForm=disposeFile(multipartFile,uploadForm);
				uploadList.add(uploadForm);
			} catch (IOException e) {
				res.setRet("0003");
				res.setRetInfo("服务器异常！！！");
				e.printStackTrace();
			}
//		}
		if(uploadList.size()<=0) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
		String today = sdf.format(date);
		String dir = contextInfo.getAssetFilePath()+ File.separator +"emergencyMsg"+ 
				File.separator + today;
		for(UploadForm upload : uploadList) {
			if(StringUtils.isNotEmpty(upload.getFilePath())) {
				String filePath = dir+ File.separator+upload.getFileName()+"_unCompress";
		        Map<String, String> data = new HashMap<>();
		        createMultilayerFile(filePath);
				try {
					CompressionFileUtil.unCompress(upload.getFilePath(), filePath);
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				List<File> files = getAllFile(new File(filePath));
				for(File file : files) {
					if(file.getPath().endsWith(".xml")||file.getPath().endsWith(".XML")) {
						data = readXMLFile(file.getPath());
						if(data.containsKey("EBMID") && StringUtils.isNotEmpty(data.get("EBMID"))) {
							EmergencyMessageEntity parentEmergencyMessage = emergencyMessageDao.getUniqueByHql(
									"select a from EmergencyMessageEntity a where a.msgCode='"+data.get("EBMID")+"'", "");
							if(null != parentEmergencyMessage) {
								EmergencyMessageEntity emergencyMessage = new EmergencyMessageEntity();
								emergencyMessage.setAreaCode(data.get(""));
								String coverageRate = data.get("CoverageRate");
								String areaCode = data.get("AreaCode");
								String resBrdStat = data.get("ResBrdStat");
								String brdStateCode = data.get("BrdStateCode");
								String brdStateDesc = data.get("BrdStateDesc");
								emergencyMessage.setParentEmergencyMessage(parentEmergencyMessage);
								emergencyMessage.setCoverageRate(coverageRate);
								emergencyMessage.setAreaCode(areaCode);
								emergencyMessage.setResBrdStat(resBrdStat);
								emergencyMessage.setStateCode(Integer.valueOf(brdStateCode));
								emergencyMessage.setStateDesc(brdStateDesc);
								emergencyMessage.setTarFilePath(upload.getFilePath());
								try {
									emergencyMessage.setSendTime(new Timestamp(DateUtil.parse(data.get("RptTime"),1).getTime()));
								} catch (ParseException e) {
									e.printStackTrace();
								}
								
								parentEmergencyMessage.setParentEmergencyMessage(parentEmergencyMessage);
								parentEmergencyMessage.setCoverageRate(coverageRate);
								parentEmergencyMessage.setAreaCode(areaCode);
								parentEmergencyMessage.setResBrdStat(resBrdStat);
								parentEmergencyMessage.setStateCode(Integer.valueOf(brdStateCode));
								parentEmergencyMessage.setStateDesc(brdStateDesc);
								
								emergencyMessageDao.save(emergencyMessage);
							}else {
//								res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//								res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
//								return res;
							}
						}
					}
				}
		        
			}
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public UploadForm disposeFile(MultipartFile file,UploadForm uploadForm) throws IOException{
		String dir = "";
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
		String today = sdf.format(date);
		String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")+1).toLowerCase();
		String[] filename = file.getOriginalFilename().split("\\.");

		if(suffix.equals("tar") || suffix.equals("TAR")){
			dir = contextInfo.getAssetFilePath()+ File.separator +"emergencyMsg"+ File.separator + today;
		}else{
			return null;
		}
		createMultilayerFile(dir);
		if (file != null) {
//			String transName = DigestUtil.getMD5Str(System.currentTimeMillis()+file.getOriginalFilename()+ CodeUtil.getId(10000)) ;
//			String fileName = transName+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
			
			String fileUpload = dir + File.separator + filename[0]+"_rb"+"."+suffix;
			File tempFile = new File(fileUpload);
			file.transferTo(tempFile);
			if(isOSLinux()){
				Runtime.getRuntime().exec("chmod 644 " + fileUpload);
			}
			uploadForm.setFilePath(fileUpload);
			uploadForm.setFileName(filename[0]);
		}
		return uploadForm;
	}

	public  boolean isOSLinux() {
		Properties prop = System.getProperties();
		String os = prop.getProperty("os.name");
		boolean statu = false;
		if (os != null && os.toLowerCase().indexOf("linux") > -1) {
			statu=true;
		} 
		return statu;
	}
	
	//读取 XML 文件
	public  Map<String, String> readXMLFile(String path){
		Map<String, String> data = new HashMap<>();
		try {
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			DocumentBuilder builder = dbf.newDocumentBuilder();
			Document doc = builder.parse(path); // 获取到xml文件
			doc.getDocumentElement().normalize();
			NodeList nodeList = doc.getDocumentElement().getChildNodes();
			for (int idx = 0; idx < nodeList.getLength(); ++idx) {
			    Node node = nodeList.item(idx);
			    NodeList childNodesList = node.getChildNodes();
			    if (node.getNodeType() == Node.ELEMENT_NODE ) {
			        org.w3c.dom.Element element = (org.w3c.dom.Element) node;
			        if(null!=element.getChildNodes() && element.getChildNodes().getLength()>0) {
			        	getChildNodes(element.getChildNodes(),data);
			        }else {  
			        	data.put(element.getNodeName(), element.getTextContent());
			        }

			    }else if(null!=childNodesList && childNodesList.getLength()>0) {
			    	getChildNodes(childNodesList,data);
			    }
			}
			try {
			    //stream.close();
			} catch (Exception ex) {
			    // do nothing
			}
			return data;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return data;
	}
	
	public void getChildNodes(NodeList nodeList,Map<String, String> data) {
		for (int idx = 0; idx < nodeList.getLength(); ++idx) {
		    Node node = nodeList.item(idx);
		    if (node.getNodeType() == Node.ELEMENT_NODE ) {
		        org.w3c.dom.Element element = (org.w3c.dom.Element) node;
		        if(null!=element.getChildNodes() && element.getChildNodes().getLength()>0) {
		        	if(!element.getNodeName().equals("ResBrdInfo")) {
		        		getChildNodes(element.getChildNodes(),data);
		        	}
		        	
		        }else {
		        data.put(element.getNodeName(), element.getTextContent());}
		    }else{
		    	data.put(node.getParentNode().getNodeName(), node.getTextContent());
		    }
		    
		}
	}
	
   public List<File> getAllFile(File dirFile) {
        // 如果文件夹不存在或着不是文件夹，则返回 null
        if (Objects.isNull(dirFile) || !dirFile.exists() || dirFile.isFile())
            return null;
        File[] childrenFiles = dirFile.listFiles();
        if (Objects.isNull(childrenFiles) || childrenFiles.length == 0)
            return null;
        List<File> files = new ArrayList<>();
        for (File childFile : childrenFiles) {
            // 如果是文件，直接添加到结果集合
            if (childFile.isFile()) {
                files.add(childFile);
            }
            //以下几行代码取消注释后可以将所有子文件夹里的文件也获取到列表里
//	            else {
//	                // 如果是文件夹，则将其内部文件添加进结果集合
//	                List<File> cFiles = getAllFile(childFile);
//	                if (Objects.isNull(cFiles) || cFiles.isEmpty()) continue;
//	                files.addAll(cFiles);
//	            }
        }
        return files;
    }



	@Override
	public IResponse getEmergencyMessageList(EmergencyMessageReq req) {
		GetEmergencyMessageListRes res = new GetEmergencyMessageListRes();
		Page<EmergencyMessageEntity> page = new Page<EmergencyMessageEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from EmergencyMessageEntity a where ");
		hql.append(null!=req.getParentEmergencyMessageId() ? " and a.parentEmergencyMessage.id="+req.getParentEmergencyMessageId() : " a.parentEmergencyMessage is null ")
				.append(null!=req.getMsgType() ? " and a.msgType="+req.getMsgType() : "");
				
		page = emergencyMessageDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			EmergencyMessageForm emergencyMessage = getEmergencyMessageForm(o);
			o.getSubEmergencyMessageList().forEach(s->{
				emergencyMessage.getSubEmergencyMessageList().add(getEmergencyMessageForm(s));
			});
			
			res.getEmergencyMessageList().add(emergencyMessage);
		});

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public EmergencyMessageForm getEmergencyMessageForm(EmergencyMessageEntity e) {
		EmergencyMessageForm emergencyMessage = new EmergencyMessageForm();
		BeanUtils.copyProperties(e,emergencyMessage);
		emergencyMessage.setEmergencyMessageId(e.getId());
		emergencyMessage.setAreaCode(StringUtils.isNotEmpty(e.getAreaCode()) ?  e.getAreaCode() :"");
		emergencyMessage.setCoverageRate(StringUtils.isNotEmpty(e.getCoverageRate()) ?  e.getCoverageRate() :"");
		emergencyMessage.setEventType(e.getEventType());
		emergencyMessage.setMsgCode(e.getMsgCode());
		emergencyMessage.setMsgDesc(StringUtils.isNotEmpty(e.getMsgDesc()) ?  e.getMsgDesc() :"");
		emergencyMessage.setMsgTitle(StringUtils.isNotEmpty(e.getMsgTitle()) ?  e.getMsgTitle() :"");
		emergencyMessage.setMsgType(e.getMsgType());
		emergencyMessage.setResBrdStat(StringUtils.isNotEmpty(e.getResBrdStat()) ?  e.getResBrdStat() :"");
		emergencyMessage.setSenderCode(StringUtils.isNotEmpty(e.getSenderCode()) ?  e.getSenderCode() :"");
		emergencyMessage.setSenderName(StringUtils.isNotEmpty(e.getSenderName()) ?  e.getSenderName() :"");
		emergencyMessage.setSeverity(e.getSeverity());
		emergencyMessage.setStateCode(e.getStateCode());
		emergencyMessage.setStateDesc(StringUtils.isNotEmpty(e.getStateDesc()) ?  e.getStateDesc() :"");
		emergencyMessage.setTarFilePath(StringUtils.isNotEmpty(e.getTarFilePath()) ?  e.getTarFilePath() :"");
		emergencyMessage.setSendTime(null!=e.getSendTime() ?  DateUtil.format(e.getSendTime(),1) :"");  
		emergencyMessage.setStartTime(null!=e.getStartTime() ? DateUtil.format(e.getStartTime(),1) :"");
		emergencyMessage.setEndTime(null!=e.getEndTime() ? DateUtil.format(e.getEndTime(),1) : "");
		emergencyMessage.setFeedbackTime(null!=e.getFeedbackTime() ? DateUtil.format(e.getFeedbackTime(),1) : "");
		return emergencyMessage;
	}
	
}
