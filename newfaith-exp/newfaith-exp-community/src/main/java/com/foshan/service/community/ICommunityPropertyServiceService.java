package com.foshan.service.community;

import java.text.ParseException;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityPropertyServiceReq;
import com.foshan.form.response.IResponse;

public interface ICommunityPropertyServiceService {
    public IResponse getCommunityPropertyServiceList(CommunityPropertyServiceReq req);
	public IResponse addCommunityPropertyService(CommunityPropertyServiceReq req);
	public IResponse modifyCommunityPropertyService(CommunityPropertyServiceReq req);
	public IResponse deleteCommunityPropertyService(CommunityPropertyServiceReq req);
	public IResponse getCommunityPropertyServiceInfo(CommunityPropertyServiceReq req);
	public IResponse createPropertyServicePdf(CommunityPropertyServiceReq req);
	public IResponse exportCommunityPropertyServiceList(CommunityPropertyServiceReq req, HttpServletResponse response) throws ParseException;
}
