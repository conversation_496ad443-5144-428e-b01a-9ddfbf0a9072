package com.foshan.service.community.impl;

import com.foshan.dao.generic.Page;
import com.foshan.entity.ColumnEntity;
import com.foshan.entity.UpshelfColumnEntity;
import com.foshan.entity.community.CommunityAnnouncementsEntity;
import com.foshan.form.community.CommunityAnnouncementsForm;
import com.foshan.form.community.request.communityAnnouncementsReq.CommunityAnnouncementsReq;
import com.foshan.form.community.request.communityAnnouncementsReq.DeleteCommunityAnnouncementsReq;
import com.foshan.form.community.request.communityAnnouncementsReq.GetCommunityAnnouncementsListReq;
import com.foshan.form.community.request.communityAnnouncementsReq.AuditCommunityAnnouncementsReq;
import com.foshan.form.community.response.communityAnnouncements.CommunityAnnouncementsRes;
import com.foshan.form.community.response.communityAnnouncements.GetCommunityAnnouncementsListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityAnnouncementsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

@Transactional
@Service("communityAnnouncementsService")
public class CommunityAnnouncementsServiceImpl extends GenericCommunityService implements ICommunityAnnouncementsService {

    @Audit(operate = "修改公告")
    @Override
    public IResponse modifyCommunityAnnouncements(CommunityAnnouncementsReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getAnnouncementId()) {
            CommunityAnnouncementsEntity announcementsEntity = communityAnnouncementsDao.get(req.getAnnouncementId());
            if (null != announcementsEntity) {
                announcementsEntity.setContent(StringUtils.isNotEmpty(req.getContent()) ? req.getContent() : announcementsEntity.getContent());
                announcementsEntity.setPublisher(StringUtils.isNotEmpty(req.getPublisher()) ? req.getPublisher() : announcementsEntity.getPublisher());
                announcementsEntity.setAnnouncementsType(null != req.getAnnouncementsType() ? req.getAnnouncementsType() : announcementsEntity.getAnnouncementsType());
                announcementsEntity.setStatus(0);
                announcementsEntity.setTitle(req.getTitle());

                String hql = "select distinct a from UpshelfColumnEntity a where a.type = 11 and a.resourceId =" + req.getAnnouncementId();
                UpshelfColumnEntity upshelfColumnEntity = upshelfColumnDao.findUnique(hql);

                upshelfColumnEntity.setIsTop(null != req.getIsTop() ? req.getIsTop() : upshelfColumnEntity.getIsTop());
                upshelfColumnEntity.setOrderNumber(null != req.getAnnouncementsOrders() ? req.getAnnouncementsOrders() : upshelfColumnEntity.getOrderNumber());
                upshelfColumnDao.saveOrUpdate(upshelfColumnEntity);
                communityAnnouncementsDao.saveOrUpdate(announcementsEntity);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
                return res;
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Audit(operate = "新增公告")
    @Override
    public IResponse addCommunityAnnouncements(CommunityAnnouncementsReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getColumnId() && StringUtils.isNotEmpty(req.getContent()) && StringUtils.isNotEmpty(req.getTitle())) {
            CommunityAnnouncementsEntity announcementsEntity = new CommunityAnnouncementsEntity();
            announcementsEntity.setPublisher(StringUtils.isNotEmpty(req.getPublisher()) ? req.getPublisher() : "");
            announcementsEntity.setStatus(0);
            announcementsEntity.setContent(req.getContent());
            announcementsEntity.setAnnouncementsType(null != req.getAnnouncementsType() ? req.getAnnouncementsType() : 0);
            announcementsEntity.setTitle(req.getTitle());
            communityAnnouncementsDao.save(announcementsEntity);

            UpshelfColumnEntity upshelfColumnEntity = new UpshelfColumnEntity();
            ColumnEntity column = columnDao.get(req.getColumnId());

            if (null != column) {
                upshelfColumnEntity.setColumn(column);
                upshelfColumnEntity.setResourceId(announcementsEntity.getId());
                upshelfColumnEntity.setType(11);
                upshelfColumnEntity.setOrderNumber(null != req.getAnnouncementsOrders() ? req.getAnnouncementsOrders() : null);
                upshelfColumnEntity.setIsTop(req.getIsTop());
                upshelfColumnDao.save(upshelfColumnEntity);
            } else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "无此栏目");
                return res;
            }
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "公告标题、内容和栏目不能为空");
        }
        return res;
    }

    @Override
    public IResponse getCommunityAnnouncementsInfo(DeleteCommunityAnnouncementsReq req) {
        CommunityAnnouncementsRes res = new CommunityAnnouncementsRes();
        CommunityAnnouncementsForm announcementsForm;
        if (null != req.getAnnouncementId()) {
            CommunityAnnouncementsEntity announcementsEntity = communityAnnouncementsDao.get(req.getAnnouncementId());
            if (null != announcementsEntity) {
                announcementsForm = getCommunityAnnouncementsForm(announcementsEntity);

                res.setCommunityAnnouncementsForm(announcementsForm);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Audit(operate = "删除公告")
    @Override
    public IResponse deleteCommunityAnnouncements(DeleteCommunityAnnouncementsReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getAnnouncementId()) {
            CommunityAnnouncementsEntity announcementsEntity = communityAnnouncementsDao.get(req.getAnnouncementId());
            if (null != announcementsEntity) {
                upshelfColumnDao.delete((UpshelfColumnEntity)upshelfColumnDao.findUnique("select distinct a from UpshelfColumnEntity a where a.type = 11 and a.resourceId ="+req.getAnnouncementId()));
                communityAnnouncementsDao.delete(announcementsEntity);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Audit(operate = "审批公告")
    @Override
    public IResponse auditCommunityAnnouncements(AuditCommunityAnnouncementsReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getAnnouncementId() && null != req.getStatus()) {
            CommunityAnnouncementsEntity announcementsEntity = communityAnnouncementsDao.get(req.getAnnouncementId());
            if (null != announcementsEntity) {
                announcementsEntity.setReviewer(StringUtils.isNoneEmpty(req.getReviewer()) ? req.getReviewer() : "");
                announcementsEntity.setStatus(req.getStatus());
                communityAnnouncementsDao.saveOrUpdate(announcementsEntity);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }

    @Override
    public IResponse getCommunityAnnouncementsList(GetCommunityAnnouncementsListReq req) {
        GetCommunityAnnouncementsListRes res = new GetCommunityAnnouncementsListRes();
        Page<UpshelfColumnEntity> page = new Page<>();
        if (null != req.getColumnId()) {
            page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
            page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
            page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
            String hql = "select distinct a from UpshelfColumnEntity a where a.type = 11 and a.column.id =" + req.getColumnId() + " ORDER BY a.upshelfTime DESC ,a.orderNumber DESC";

            page = upshelfColumnDao.queryPage(page, hql);
            res.setTotalResult(page.getTotalCount());
            res.setPageSize(page.getPageSize());
            res.setCurrentPage(page.getCurrentPage());
            res.setTotal(page.getTotalPage());
            page.getResultList().forEach(o -> {
                CommunityAnnouncementsForm announcementsForm = getCommunityAnnouncementsForm(communityAnnouncementsDao.get(o.getResourceId()));
                if (null != req.getAnnouncementsType() && !announcementsForm.getAnnouncementsType().equals(req.getAnnouncementsType())){
                       return;
                }
                if (null != req.getStatus() && !announcementsForm.getStatus().equals(req.getStatus())){
                    return;
                }

                announcementsForm.setIsTop(null != o.getIsTop() ? o.getIsTop() : 0);
                announcementsForm.setAnnouncementsOrders(o.getOrderNumber());
                announcementsForm.setCreateTime(null != o.getUpshelfTime() ? o.getUpshelfTime().toString().substring(0, o.getUpshelfTime().toString().indexOf(".")) : "");
                res.getCommunityAnnouncementsFormList().add(announcementsForm);
            });
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    protected CommunityAnnouncementsForm getCommunityAnnouncementsForm(CommunityAnnouncementsEntity announcementsEntity) {
        CommunityAnnouncementsForm announcementsForm = new CommunityAnnouncementsForm();
        announcementsForm.setAnnouncementId(announcementsEntity.getId());
        announcementsForm.setTitle(announcementsEntity.getTitle());
        announcementsForm.setCreateTime(null != announcementsEntity.getCreateTime() ? announcementsEntity.getCreateTime().toString().substring(0, announcementsEntity.getCreateTime().toString().indexOf(".")) : "");
        announcementsForm.setContent(announcementsEntity.getContent());
        announcementsForm.setPublisher(StringUtils.isNoneEmpty(announcementsEntity.getPublisher()) ? announcementsEntity.getPublisher() : "");
        announcementsForm.setReviewer(StringUtils.isNoneEmpty(announcementsEntity.getReviewer()) ? announcementsEntity.getReviewer() : "");
        announcementsForm.setStatus(null != announcementsEntity.getStatus() ? announcementsEntity.getStatus() : 0);
        announcementsForm.setAnnouncementsType(null != announcementsEntity.getAnnouncementsType() ? announcementsEntity.getAnnouncementsType() : 0);

        return announcementsForm;
    }

}
