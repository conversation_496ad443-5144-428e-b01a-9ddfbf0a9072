package com.foshan.service.community.impl;

import static java.util.Comparator.comparingInt;

import java.text.ParseException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityReservationActivitiesEntity;
import com.foshan.entity.community.CommunityReservationDateEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityReservatPeriodForm;
import com.foshan.form.community.CommunityReservationDateForm;
import com.foshan.form.community.request.CommunityReservationDateReq;
import com.foshan.form.community.response.communityReservationDate.GetCommunityReservationDateListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityReservationDateService;
import com.foshan.util.DateUtil;


@Transactional
@Service("communityReservationDateService")
public class CommunityReservationDateServiceImpl extends GenericCommunityService implements ICommunityReservationDateService{

	@Override
	public IResponse getCommunityReservationDateList(CommunityReservationDateReq req) {
		GetCommunityReservationDateListRes res = new GetCommunityReservationDateListRes();
		Page<CommunityReservationDateEntity> page = new Page<CommunityReservationDateEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityReservationDateEntity a  ");
	
		hql.append(" where 1=1").append(StringUtils.isNotEmpty(req.getStateList()) ? " and  a.state in(" + req.getStateList()+")"  :
			" and a.state="+EntityContext.RECORD_STATE_VALID)
			.append(null!=req.getActivitiesId() ?" and a.activities.id="+req.getActivitiesId():"")
			.append(StringUtils.isNotEmpty(req.getReservationDate())  ? " and a.reservationDate>='"+req.getReservationDate()+"'":"")
			.append(null!=req.getIsStart() ?" and a.isStart="+req.getIsStart():"");
		hql.append(" ORDER BY a.reservationDate ASC");
		page = communityReservationDateDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityReservationDateForm reservationDateForm = new CommunityReservationDateForm(o.getId(),
					DateUtil.format(o.getReservationDate(),0),o.getIsStart(),o.getState());
			o.getPeriodList().forEach(p->{
				if(p.getState() == EntityContext.RECORD_STATE_VALID) {
					CommunityReservatPeriodForm periodForm = new CommunityReservatPeriodForm(p.getId(),p.getMaxNum(),
							p.getOrders(),p.getStartTime(),p.getEndTime(),p.getState(),p.getOverplusNum());
					reservationDateForm.getPeriodFormList().add(periodForm);
				}
			});
			reservationDateForm.getPeriodFormList().sort(comparingInt(CommunityReservatPeriodForm::getOrders));
			res.getReservationDateList().add(reservationDateForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}



	@Override
	@Audit(operate = "修改预约日期")
	public IResponse modifyCommunityReservationDate(CommunityReservationDateReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getReservationDateId()) {
			CommunityReservationDateEntity reservationDate = communityReservationDateDao.get(req.getReservationDateId());
			if (null != reservationDate) {
				reservationDate.setIsStart(null!=req.getIsStart() ? req.getIsStart() : reservationDate.getIsStart());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	@Audit(operate = "删除预约日期")
	public IResponse deleteCommunityReservationDate(CommunityReservationDateReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getReservationDateId()) {
			CommunityReservationDateEntity reservationDate = communityReservationDateDao.get(req.getReservationDateId());
			if (null != reservationDate) {
				reservationDate.setState(EntityContext.RECORD_STATE_INVALID);
				reservationDate.getPeriodList().forEach(p->{
					p.setState(EntityContext.RECORD_STATE_INVALID);
				});
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	@Audit(operate = "新增预约日期")
	public IResponse addCommunityReservationDate(CommunityReservationDateReq req) {
		GenericResponse res = new GenericResponse();
		if (null!=req.getIsStart() && null!=req.getActivitiesId() && StringUtils.isNotEmpty(req.getReservationDate())) {

			CommunityReservationActivitiesEntity activities = communityReservationActivitiesDao.get(req.getActivitiesId());
			CommunityReservationDateEntity reservationDate =communityReservationDateDao.findUnique(
					"from CommunityReservationDateEntity a where a.activities.id="+req.getActivitiesId()+
					" and a.reservationDate='"+req.getReservationDate()+"'", "");
			if( null==activities || null!=reservationDate && 
					reservationDate.getState() == EntityContext.RECORD_STATE_VALID) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO+"活动不存在或预约日期已存在！");
				return res;
			}if(null!=reservationDate) {
				reservationDate.setState(EntityContext.RECORD_STATE_VALID);
			}else {
				reservationDate = new CommunityReservationDateEntity();
			}
			reservationDate.setActivities(activities);
			reservationDate.setIsStart(req.getIsStart());
			try {
				long s = DateUtil.parse(req.getReservationDate(),0).getTime();
				reservationDate.setReservationDate(new java.sql.Date(s));
			} catch (ParseException e) {
				e.printStackTrace();
			}
			reservationDate.setState(EntityContext.RECORD_STATE_VALID);
			communityReservationDateDao.save(reservationDate);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}


}
