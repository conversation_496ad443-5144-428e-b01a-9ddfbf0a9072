package com.foshan.service.community.impl;

import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.RoleEntity;
import com.foshan.entity.community.CommunityContractEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.AssetForm;
import com.foshan.form.community.CommunityContractForm;
import com.foshan.form.community.CommunityMemberForm;
import com.foshan.form.community.CommunityMemberPropertyForm;
import com.foshan.form.community.CommunityPayItemsPriceForm;
import com.foshan.form.community.request.CommunityContractReq;
import com.foshan.form.community.request.CommunityMemberPropertyReq;
import com.foshan.form.community.response.contract.AddCommunityContractRes;
import com.foshan.form.community.response.contract.GetCommunityContractInfoRes;
import com.foshan.form.community.response.contract.GetCommunityContractListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityContractService;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;

	
@Transactional
@Service("communityContractService")
public class CommunityContractServiceImpl extends GenericCommunityService implements ICommunityContractService{

	@Override
	public IResponse getCommunityContractList(CommunityContractReq req) {
		GetCommunityContractListRes res = new GetCommunityContractListRes();
		
		Page<CommunityContractEntity> page = new Page<CommunityContractEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityContractEntity a ");
		hql.append(StringUtils.isNotEmpty(req.getPropertyIdList()) ? " inner join a.memberPropertyList b inner join b.property c ":"")
			.append(StringUtils.isNotEmpty(req.getUserName()) ? " inner join a.member e" :"")
			.append("where a.state="+EntityContext.RECORD_STATE_VALID)
			.append(StringUtils.isNotEmpty(req.getContractName()) ? " and a.contractName like'%"+req.getContractName()+"%'":"")
			.append(StringUtils.isNotEmpty(req.getContractCode()) ? " and a.contractCode like'%"+req.getContractCode()+"%'":"")
			.append(StringUtils.isNotEmpty(req.getPropertyIdList()) ? " and c.id in("+req.getPropertyIdList()+")":"")
			.append(StringUtils.isNotEmpty(req.getStartTime()) ? 
					" and a.startTime>='"+req.getStartTime()+"' " : "")
			.append(StringUtils.isNotEmpty(req.getEndTime()) ? 
					" and a.endTime<='"+req.getEndTime()+"'" : "")
			.append(StringUtils.isNotEmpty(req.getUserName()) ? " and e.userName like'%"+req.getUserName()+"%'" :"");
		hql.append(" ORDER BY a.lastModifyTime desc");
		page = communityContractDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			res.getContractList().add(getContractForm(o,req.getMemberId()));
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public CommunityContractForm getContractForm(CommunityContractEntity contract,Integer memberId) {
		CommunityContractForm communityContractForm = new CommunityContractForm();
		communityContractForm.setContractId(contract.getId());
        communityContractForm.setContractName(contract.getContractName());
		communityContractForm.setCreateTime(DateUtil.formatLongFormat(contract.getCreateTime()));
		communityContractForm.setLastModifyTime(DateUtil.formatLongFormat(contract.getLastModifyTime()));
		communityContractForm.setContractCode(contract.getContractCode());
		communityContractForm.setStartTime(DateUtil.formatLongFormat(contract.getStartTime()));
		communityContractForm.setEndTime(DateUtil.formatLongFormat(contract.getEndTime()));
		communityContractForm.setRentInfo(contract.getRentInfo());
		communityContractForm.setContractBrief(contract.getContractBrief());
		communityContractForm.setDepositInfo(contract.getDepositInfo());
		communityContractForm.setBusinessCategory(contract.getBusinessCategory());
		communityContractForm.setRentPayType(contract.getRentPayType());
		communityContractForm.setPaytaxNo(contract.getPaytaxNo());
		communityContractForm.setBuyersName(contract.getBuyersName());
		communityContractForm.setInvoiceDescription(contract.getInvoiceDescription());
		communityContractForm.setIsEffective(contract.getIsEffective());
		if(contract.getMember()!=null) {
			CommunityMemberForm memberForm = new CommunityMemberForm();
			memberForm.setMemberId(contract.getMember().getId());
			memberForm.setEmail(contract.getMember().getEmail());
			memberForm.setHomePhone(contract.getMember().getHomePhone());
			memberForm.setNickName(contract.getMember().getNickName());
			memberForm.setOfficePhone(contract.getMember().getOfficePhone());
			memberForm.setPhone(contract.getMember().getPhone());
			memberForm.setRegistName(contract.getMember().getRegistName());
			memberForm.setSex(contract.getMember().getSex());
			memberForm.setSmartcardId(contract.getMember().getSmartcardId());
			memberForm.setIdCard(StringUtils.isNotEmpty(contract.getMember().getIdCard()) ? contract.getMember().getIdCard() : "");
			memberForm.setHomeAddress(StringUtils.isNotEmpty(contract.getMember().getHomeAddress()) ? contract.getMember().getHomeAddress() : "");
			memberForm.setHeadImage(StringUtils.isNotEmpty(contract.getMember().getHeadImage()) ? contract.getMember().getHeadImage() : "");
			memberForm.setUserName(StringUtils.isNotEmpty(contract.getMember().getUserName()) ? contract.getMember().getUserName() : "");
			memberForm.setBirthday(contract.getMember().getBirthday());
			memberForm.setNativePlace(contract.getMember().getNativePlace());
			memberForm.setNation(contract.getMember().getNation());
			memberForm.setNation(contract.getMember().getNation());
			memberForm.setPoliceStation(contract.getMember().getPoliceStation());
			memberForm.setPostalCode(contract.getMember().getPostalCode());
			memberForm.setCompany(contract.getMember().getCompany());
			memberForm.setContactPerson(StringUtils.isNotEmpty(contract.getMember().getContactPerson()) ?
					contract.getMember().getContactPerson() :"");
			memberForm.setEmergencyContact(contract.getMember().getEmergencyContact());
			memberForm.setComment(contract.getMember().getComment());
			memberForm.setIdType(contract.getMember().getIdType());
			memberForm.setIdCard(contract.getMember().getIdCard());
			memberForm.setRelation(
					StringUtils.isNotEmpty(contract.getMember().getRelation()) ? contract.getMember().getRelation() : "");
			communityContractForm.setMemberForm(memberForm);
		}
		
		for(CommunityMemberPropertyEntity o : contract.getMemberPropertyList()) {
			if(null==memberId || (null!=memberId && memberId.toString().equals(o.getMember().getId().toString()))) {
				communityContractForm.getMemberPropertyList().add(getMemberProperForm(o));
			}
		}
		communityContractForm.setAssetForm(getAsset(contract.getAsset()));
		return communityContractForm;
	}
	
	public CommunityMemberPropertyForm getMemberProperForm(CommunityMemberPropertyEntity o) {
		CommunityMemberPropertyForm memberProperForm = new CommunityMemberPropertyForm();
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
//		memberProperForm.setAuditState(o.getAuditState());
		memberProperForm.setCreateTime(sdf.format(o.getCreateTime()));
		if(null != o.getMember()) {
			CommunityMemberForm memberForm = new CommunityMemberForm();
			memberForm.setMemberId(o.getMember().getId());
			memberForm.setEmail(o.getMember().getEmail());
			memberForm.setHomePhone(o.getMember().getHomePhone());
			memberForm.setNickName(o.getMember().getNickName());
			memberForm.setOfficePhone(o.getMember().getOfficePhone());
			memberForm.setPhone(o.getMember().getPhone());
			memberForm.setRegistName(o.getMember().getRegistName());
			memberForm.setSex(o.getMember().getSex());
			memberForm.setSmartcardId(o.getMember().getSmartcardId());
			memberForm.setIdCard(StringUtils.isNotEmpty(o.getMember().getIdCard()) ? o.getMember().getIdCard() : "");
			memberForm.setHomeAddress(StringUtils.isNotEmpty(o.getMember().getHomeAddress()) ? o.getMember().getHomeAddress() : "");
			memberForm.setHeadImage(StringUtils.isNotEmpty(o.getMember().getHeadImage()) ? o.getMember().getHeadImage() : "");
			memberForm.setUserName(StringUtils.isNotEmpty(o.getMember().getUserName()) ? o.getMember().getUserName() : "");
			memberForm.setBirthday(o.getMember().getBirthday());
			memberForm.setNativePlace(o.getMember().getNativePlace());
			memberForm.setNation(o.getMember().getNation());
			memberForm.setNation(o.getMember().getNation());
			memberForm.setPoliceStation(o.getMember().getPoliceStation());
			memberForm.setPostalCode(o.getMember().getPostalCode());
			memberForm.setCompany(o.getMember().getCompany());
			memberForm.setContactPerson(StringUtils.isNotEmpty(o.getMember().getContactPerson()) ?
					o.getMember().getContactPerson() :"");
			memberForm.setEmergencyContact(o.getMember().getEmergencyContact());
			memberForm.setComment(o.getMember().getComment());
			memberForm.setIdType(o.getMember().getIdType());
			memberForm.setIdCard(o.getMember().getIdCard());
			memberForm.setRelation(
					StringUtils.isNotEmpty(o.getMember().getRelation()) ? o.getMember().getRelation() : "");
			memberProperForm.setMemberForm(memberForm);
		}
		memberProperForm.setContractInfo(StringUtils.isNotEmpty(o.getContractInfo()) ? o.getContractInfo() : "");
		memberProperForm.setRentType(o.getRentType());
		memberProperForm.setPropertyForm(getCommunityPropertyForm(o.getProperty(),null,null));
		memberProperForm.setMemberType(o.getMemberType());
		memberProperForm.setAuditState(o.getAuditState());
		memberProperForm.setIsCurrentMember(o.getIsCurrentMember());
		memberProperForm.setBillingDate(null != o.getBillingDate()? 
				DateUtil.formatShortFormat(o.getBillingDate()) : "");
		memberProperForm.setBillingDateModify(null != o.getBillingDateModify()? 
				DateUtil.formatShortFormat(o.getBillingDateModify()) : "");
		memberProperForm.setCreateTime(null != o.getCreateTime()? 
				DateUtil.formatShortFormat(o.getCreateTime()) : "");
		memberProperForm.setIsCurrentOwner(o.getIsCurrentOwner());
		memberProperForm.setRecordDate(null != o.getRecordDate()? 
				DateUtil.formatShortFormat(o.getRecordDate()) : "");
		memberProperForm.setTerminationDate(null != o.getTerminationDate()? 
				DateUtil.formatShortFormat(o.getTerminationDate()) : "");
//		memberProperForm.setAcceptanceDate(null != o.getAcceptanceDate()? DateUtil.formatLongFormat(o.getAcceptanceDate()) : "");
		memberProperForm.setMemberPropertyId(o.getId());
		memberProperForm.setCarInfo(StringUtils.isNotEmpty(o.getCarInfo()) ? 
				o.getCarInfo() : "");
		memberProperForm.setParentPropertyForm(getCommunityPropertyForm(o.getParentProperty(),null,null));
		memberProperForm.setEndDate(null != o.getEndDate() ? 
				DateUtil.formatShortFormat(o.getEndDate()) : "");
		memberProperForm.setComment(StringUtils.isNotEmpty(o.getComment()) ? o.getComment() :"");
		memberProperForm.setBuyersName(StringUtils.isNotEmpty(o.getBuyersName()) ? 
				o.getBuyersName() : "");
		memberProperForm.setBuyersAddress(StringUtils.isNotEmpty(o.getBuyersAddress()) ? 
				o.getBuyersAddress() : "");
		memberProperForm.setBusinessType(StringUtils.isNotEmpty(o.getBusinessType()) ? 
				o.getBusinessType() : "");
		memberProperForm.setPaytaxNo(StringUtils.isNotEmpty(o.getPaytaxNo()) ?
				o.getPaytaxNo() : "");
		memberProperForm.setBuyersBankAccount(StringUtils.isNotEmpty(o.getBuyersBankAccount()) ?
				o.getBuyersBankAccount() : "");
		memberProperForm.setBuyerEmail(StringUtils.isNotEmpty(o.getBuyerEmail()) ?
				o.getBuyerEmail() : "");
		o.getPayItemsPriceList().forEach(p->{
			CommunityPayItemsPriceForm payItemsPriceForm = new CommunityPayItemsPriceForm();
			payItemsPriceForm.setStartTime(p.getComment());
			payItemsPriceForm.setPayItemsPriceId(p.getId());
			payItemsPriceForm.setPeriod(p.getPeriod());
			payItemsPriceForm.setPrice(p.getPrice().toString());
			payItemsPriceForm.setCreateTime(null != p.getCreateTime() ? DateUtil.formatLongFormat(p.getCreateTime()) : "");
			payItemsPriceForm.setEndTime(null != p.getEndTime() ? DateUtil.formatLongFormat(p.getEndTime()) : "");
			payItemsPriceForm.setLastModifyTime(null != p.getLastModifyTime() ? DateUtil.formatLongFormat(p.getLastModifyTime()) : "");
			payItemsPriceForm.setStartTime(null != p.getStartTime() ? DateUtil.formatLongFormat(p.getStartTime()) : "");
			payItemsPriceForm.setItemsName(p.getPayItems().getItemsName());
			payItemsPriceForm.setChargeCategory(p.getPayItems().getChargeCategory());
			payItemsPriceForm.setPriceUnit(p.getPayItems().getPriceUnit());
			payItemsPriceForm.setFeeCalType(p.getPayItems().getFeeCalType());
			payItemsPriceForm.setPayDate(p.getPayItems().getPayDate());
			payItemsPriceForm.setIsBreach(p.getPayItems().getIsBreach());
			memberProperForm.getPayItemsPriceForm().add(payItemsPriceForm);
		});
		
		return memberProperForm;
	}

	protected AssetForm getAsset(AssetEntity asset) {
		
		AssetForm assetForm = new AssetForm();
		if (null != asset) {
			assetForm.setAssetId(asset.getId());
			assetForm.setAssetType(asset.getAssetType());
			assetForm.setImageFile(asset.getImageFile());
			assetForm.setSmallImageFile(asset.getSmallImageFile());
			assetForm.setIsCover(asset.getIsCover());
			assetForm.setPackageFlag(asset.getPackageFlag());
			assetForm.setTimeLength(asset.getTimeLength());
			assetForm.setSummaryShort(asset.getSummaryShort());
			assetForm.setParameterInfo(asset.getParameterInfo());
			assetForm.setAssetCode(asset.getAssetCode());
			assetForm.setAssetName(asset.getAssetName());
			assetForm.setAssetOrders(asset.getAssetOrders());
			assetForm.setPublishedTime(
					asset.getPublishedTime() != null ? 
							new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
							.format(asset.getPublishedTime()): "");
			assetForm.setPackageOrders(asset.getPackageCount());
			assetForm.setServiceCode(asset.getAssetCode());
			assetForm.setValidTime(asset.getValidTime() != null
					? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(asset.getValidTime())
					: "");
			assetForm.setServiceId(asset.getServiceId());
			assetForm.setMiddleImageFile(asset.getMiddleImageFile());
			assetForm.setRecommendCount(asset.getRecommendCount());
			assetForm.setDirector(asset.getDirector());
			assetForm.setActorsDisplay(asset.getActorsDisplay());
			assetForm.setBroadcastCount(asset.getBroadcastCount());
			assetForm.setPreviewAssetId(asset.getPreviewAssetId());
			assetForm.setPreviewProviderId(asset.getPreviewProviderId());
			assetForm.setPackageCount(asset.getSubAssetSet().size());
			asset.getSubAssetSet().forEach(o -> {
				assetForm.getSubAssetList().add(getAsset(o));
			});
		}
		return assetForm;
	}
	
	@Override
	public IResponse addCommunityContract(CommunityContractReq req) {
		AddCommunityContractRes res = new AddCommunityContractRes();
		if (StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime()) ) {
			CommunityContractEntity communityContract = new CommunityContractEntity();
			CommunityMemberEntity member = null;
			if(null != req.getMemberId()) {
				member = communityMemberDao.get(req.getMemberId());
			}else {
				Map<String,Object> map = addCommunityMember(req);
				if(!map.get("ret").toString().equals(ResponseContext.RES_SUCCESS_CODE)) {
					res.setRet((String)map.get("ret"));
					res.setRetInfo((String)map.get("retInfo"));
					return res;
				}
				member = (CommunityMemberEntity) map.get("member");
			}
			
//			if(null == member) {
//				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
//				return res;
//			}
			communityContract.setMember(member);
			communityContract.setBusinessCategory(StringUtils.isNotEmpty(req.getBusinessCategory()) ? req.getBusinessCategory():"");
			communityContract.setBuyersName(StringUtils.isNotEmpty(req.getBuyersName()) ? req.getBuyersName():"");
			communityContract.setContractBrief(StringUtils.isNotEmpty(req.getContractBrief()) ? req.getContractBrief():"");
			communityContract.setContractCode(StringUtils.isNotEmpty(req.getContractCode()) ? req.getContractCode():"");
			communityContract.setContractName(StringUtils.isNotEmpty(req.getContractName()) ? req.getContractName():"");
			communityContract.setDepositInfo(StringUtils.isNotEmpty(req.getDepositInfo()) ? req.getDepositInfo():"");
			communityContract.setInvoiceDescription(StringUtils.isNotEmpty(req.getInvoiceDescription()) ? req.getInvoiceDescription():"");
			communityContract.setIsEffective(EntityContext.RECORD_STATE_VALID);
			communityContract.setLastModifyTime(new Timestamp(new Date().getTime()));
			communityContract.setPaytaxNo(StringUtils.isNotEmpty(req.getPaytaxNo()) ? req.getPaytaxNo():"");
			communityContract.setRentInfo(StringUtils.isNotEmpty(req.getRentInfo()) ? req.getRentInfo():"");
			communityContract.setRentPayType(StringUtils.isNotEmpty(req.getRentPayType()) ? req.getRentPayType():"");
			communityContract.setState(EntityContext.RECORD_STATE_VALID);
			if(StringUtils.isNotEmpty(req.getStartTime())) {
				try {
					communityContract.setStartTime(new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime()));
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
			if(StringUtils.isNotEmpty(req.getEndTime())) {
				try {
					communityContract.setEndTime(new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime()));
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
			communityContract.setAsset(
	                null != req.getAssetId() ? assetDao.get(req.getAssetId()) : null);
			communityContractDao.save(communityContract);
			//communityContractDao.save(null);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse modifyCommunityContract(CommunityContractReq req) {
		GenericResponse res = new GenericResponse();
		if(null != req.getContractId()) {
			CommunityContractEntity communityContract = communityContractDao.get(req.getContractId());
			if(null == communityContract) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			communityContract.setBusinessCategory(StringUtils.isNotEmpty(req.getBusinessCategory()) 
					? req.getBusinessCategory() : communityContract.getBusinessCategory());
			communityContract.setBuyersName(StringUtils.isNotEmpty(req.getBuyersName()) ? 
					req.getBuyersName():communityContract.getBuyersName());
			communityContract.setContractBrief(StringUtils.isNotEmpty(req.getContractBrief()) ? 
					req.getContractBrief():communityContract.getContractBrief());
			communityContract.setContractCode(StringUtils.isNotEmpty(req.getContractCode()) ? 
					req.getContractCode():communityContract.getContractCode());
			communityContract.setContractName(StringUtils.isNotEmpty(req.getContractName()) ? 
					req.getContractName():communityContract.getContractName());
			communityContract.setDepositInfo(StringUtils.isNotEmpty(req.getDepositInfo()) ? 
					req.getDepositInfo():communityContract.getDepositInfo());
			communityContract.setInvoiceDescription(StringUtils.isNotEmpty(req.getInvoiceDescription()) ? 
					req.getInvoiceDescription():communityContract.getInvoiceDescription());
			//communityContract.setIsEffective(EntityContext.RECORD_STATE_VALID);
			communityContract.setLastModifyTime(new Timestamp(new Date().getTime()));
			communityContract.setPaytaxNo(StringUtils.isNotEmpty(req.getPaytaxNo()) ? 
					req.getPaytaxNo():communityContract.getPaytaxNo());
			communityContract.setRentInfo(StringUtils.isNotEmpty(req.getRentInfo()) ? 
					req.getRentInfo():communityContract.getRentInfo());
			communityContract.setRentPayType(StringUtils.isNotEmpty(req.getRentPayType()) ? 
					req.getRentPayType():communityContract.getRentPayType());
			//communityContract.setState(EntityContext.RECORD_STATE_VALID);
			if(StringUtils.isNotEmpty(req.getStartTime())) {
				try {
					communityContract.setStartTime(new Timestamp(DateUtil.parseLongFormat(req.getStartTime()).getTime()));
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
			if(StringUtils.isNotEmpty(req.getEndTime())) {
				try {
					communityContract.setEndTime(new Timestamp(DateUtil.parseLongFormat(req.getEndTime()).getTime()));
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
			communityContract.setAsset(
	                null != req.getAssetId() ? assetDao.get(req.getAssetId()) : communityContract.getAsset());
			
			if(null!=req.getMemberId()) {
				CommunityMemberEntity member = communityMemberDao.get(req.getMemberId());
				communityContract.setMember(null!=member ? member : communityContract.getMember());
				Map<String,Object> map = modifyCommunityMember(req, member);
				map.put("ret", ResponseContext.RES_SUCCESS_CODE);
				if(!map.get("ret").toString().equals(ResponseContext.RES_SUCCESS_CODE)) {
					res.setRet((String)map.get("ret"));
					res.setRetInfo((String)map.get("retInfo"));
					return res;
				}
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			return res;
		}else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
 
		return res;
	}

	@Override
	public IResponse deleteCommunityContract(CommunityContractReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getContractId()) {
			CommunityContractEntity contract = communityContractDao.get(req.getContractId());
			if (null != contract) {
				contract.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityContractInfo(CommunityContractReq req) {
		GetCommunityContractInfoRes res = new GetCommunityContractInfoRes();
		if (null != req.getContractId()) {
			CommunityContractEntity contract = communityContractDao.get(req.getContractId());
			if (null != contract) {
				res.setContractForm(getContractForm(contract,null));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	
	@Audit(operate = "新增会员")
	public Map<String,Object> addCommunityMember(CommunityContractReq req) {
		Map<String,Object> map = new HashMap<String,Object>();
		String phone = (StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : "");
		if (StringUtils.isEmpty(phone) ||
				StringUtils.isEmpty(req.getRoleIdList())) {
			map.put("ret", ResponseContext.RES_NULL_ERROR_CODE);
			map.put("retInfo", ResponseContext.RES_NULL_ERROR_INFO );
			return map;
		}
		CommunityMemberEntity member  = null;
		member= communityMemberDao.getUniqueBySql("SELECT * FROM t_account WHERE phone='"+req.getPhone()+"' and userState = 1",
				"");
		if (null != member) {
			//普通注册流程
			map.put("ret", ResponseContext.RES_NULL_ERROR_CODE);
			map.put("retInfo", "对不起，您注册的用户名或手机号已被注册！" );
			return map;
		} else {
			member = new CommunityMemberEntity();
			String[] rolerId = req.getRoleIdList().split(",");
			for (String id : rolerId) {
				RoleEntity role = roleDao.get(Integer.valueOf(id));
				if (null != role) {
					member.getCommunityRoleList().add(role);
				}
			}
			
			//密码选填
			String password = req.getPassword();
			if(StringUtils.isNotBlank(password)) {
				try {
					member.setPassword(DigestUtil.sm3Digest(req.getPassword()));
					member.setLastPwdModifyTime(new Timestamp(new Date().getTime()));
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
				}
			}
			member.setPhone(phone);
			member.setUserName(StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() :"");
			member.setPhoneVerifyState(EntityContext.RECORD_STATE_VALID);
			member.setIdCard(StringUtils.isNotEmpty(req.getIdCard())?req.getIdCard():"");
			member.setUserState(EntityContext.RECORD_STATE_VALID);
			member.setSex(null != req.getSex() ? req.getSex() : 2);
			member.setRegionCode(StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : "");
			member.setHeadImage(StringUtils.isNotEmpty(req.getHeadImage()) ? req.getHeadImage() :"");
			member.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment(): "");
			member.setComment(req.getComment());
			member.setContactPerson(req.getContactPerson());
			member.setEmergencyContact(req.getEmergencyContact());
			member.setRelation(req.getRelation());
//			member.setBuyersName(req.getBuyersName());
//			member.setBuyersAddress(req.getBuyersAddress());
//			member.setBusinessType(req.getBusinessType());
//			member.setInvoiceType(req.getInvoiceType());
//			member.setPaytaxNo(req.getPaytaxNo());
//			member.setBuyersBankAccount(req.getBuyersBankAccount());
			member.setTvNo(req.getTvNo());
			member.setMac(req.getMac());
			member.setIp(req.getIp());
			member.setMemberType(req.getMemberType());
			member.setWeixinOpenId(req.getWeixinOpenId());
			member.setWeixinAvatar(req.getWeixinAvatar());
			member.setHomeAddress(req.getHomeAddress());
			member.setCompany(req.getCompany());
			member.setPostalCode(req.getPostalCode());
			member.setNation(req.getNation());
			member.setPoliceStation(req.getPoliceStation());
			member.setNativePlace(req.getNativePlace());
			member.setOfficeAddress(req.getOfficeAddress());
			member.setExchangeRegion(req.getExchangeRegion());
			member.setExchangeHall(req.getExchangeHall());
			member.setMiniProgramOpenId(req.getMiniProgramOpenId());
			member.setIsSmartcardMaster(req.getIsSmartcardMaster());
			member.setBirthday(req.getBirthday());
			//member.setPhoneVerifyState(req.getPhoneVerifyState());
			member.setQq(req.getQq());
			member.setHomePhone(req.getHomePhone());
			member.setWeixin(req.getWeixin());
			member.setPhoto(req.getPhoto());
			member.setIdType(req.getIdType());
			member.setIdCard(req.getIdCard());
			
			communityMemberDao.save(member);
			map.put("member", member);
		}
		map.put("ret", ResponseContext.RES_SUCCESS_CODE);
		map.put("retInfo", ResponseContext.RES_SUCCESS_CODE_INFO);
		return map;
	}
	
	@Audit(operate = "修改业主")
	public Map<String,Object> modifyCommunityMember(CommunityContractReq req,CommunityMemberEntity member) {
		Map<String,Object> map = new HashMap<String,Object>();
		if(null!=req.getMemberId() ) {
			if(StringUtils.isNotEmpty(req.getPhone()) && !req.getPhone().equals(member.getPhone())) {
				CommunityMemberEntity m= communityMemberDao.getUniqueBySql(
						"SELECT * FROM t_account WHERE phone='"+req.getPhone()+"' and userState = 1","");
				if (null != m) {
					//普通注册流程
					map.put("ret", ResponseContext.RES_NULL_ERROR_CODE);
					map.put("retInfo", "对不起，您注册的用户名或手机号已被注册！" );
					return map;
				}
			}
			if (null != member) {
				if(StringUtils.isNotEmpty(req.getRoleIdList())) {
					member.setCommunityRoleList(null);
					String[] rolerId = req.getRoleIdList().split(",");
					List<RoleEntity> roleList = new ArrayList<RoleEntity>();
					for (String id : rolerId) {
						RoleEntity role = roleDao.get(Integer.valueOf(id));
						if (null != role) {
							roleList.add(role);
						}
					}
					member.setCommunityRoleList(roleList);
				}
				member.setLastModifyTime(new Timestamp(new Date().getTime()));
				member.setEmail(StringUtils.isNotEmpty(req.getEmail()) ? req.getEmail() : member.getEmail());
				member.setPhone(StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : member.getPhone());
				member.setSex(null != req.getSex() ? req.getSex() : member.getSex());
				member.setSmartcardId(
						StringUtils.isNotEmpty(req.getSmartcardId()) ? req.getSmartcardId() : member.getSmartcardId());
				member.setHeadImage(
						StringUtils.isNotEmpty(req.getHeadImage()) ? req.getHeadImage() : member.getHeadImage());
				member.setRegionCode(
						StringUtils.isNotEmpty(req.getRegionCode()) ? req.getRegionCode() : member.getRegionCode());
				member.setUserName(
						StringUtils.isNotEmpty(req.getUserName()) ? req.getUserName() : member.getUserName());
				member.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment():member.getComment());
				member.setContactPerson(StringUtils.isNotEmpty(req.getContactPerson()) ? req.getContactPerson() :member.getContactPerson());
				member.setEmergencyContact(StringUtils.isNotEmpty(req.getEmergencyContact()) ?req.getEmergencyContact():member.getEmergencyContact());
				member.setRelation(StringUtils.isNotEmpty(req.getRelation()) ? req.getRelation():member.getRelation());
//				member.setBuyersName(StringUtils.isNotEmpty(req.getBuyersName()) ? req.getBuyersName():member.getBuyersName());
//				member.setBuyersAddress(StringUtils.isNotEmpty(req.getBuyersAddress()) ? req.getBuyersAddress():member.getBuyersAddress());
//				member.setBusinessType(StringUtils.isNotEmpty(req.getBusinessType()) ? req.getBusinessType():member.getBusinessType());
//				member.setInvoiceType(StringUtils.isNotEmpty(req.getInvoiceType()) ? req.getInvoiceType():member.getInvoiceType());
//				member.setPaytaxNo(StringUtils.isNotEmpty(req.getPaytaxNo()) ? req.getPaytaxNo():member.getPaytaxNo());
//				member.setBuyersBankAccount(StringUtils.isNotEmpty(req.getBuyersBankAccount()) ? req.getBuyersBankAccount():member.getBuyersBankAccount());
				member.setTvNo(StringUtils.isNotEmpty(req.getTvNo()) ? req.getTvNo():member.getTvNo());
				member.setMac(StringUtils.isNotEmpty(req.getMac()) ?req.getMac():member.getMac());
				member.setIp(StringUtils.isNotEmpty(req.getIp()) ? req.getIp():member.getIp());
				member.setWeixinOpenId(StringUtils.isNotEmpty(req.getWeixinOpenId()) ? req.getWeixinOpenId():member.getWeixinOpenId());
				member.setWeixinAvatar(StringUtils.isNotEmpty(req.getWeixinAvatar()) ? req.getWeixinAvatar():member.getWeixinAvatar());
				member.setHomeAddress(StringUtils.isNotEmpty(req.getHomeAddress()) ? req.getHomeAddress():member.getHomeAddress());
				member.setCompany(StringUtils.isNotEmpty(req.getCompany()) ? req.getCompany():member.getCompany());
				member.setPostalCode(StringUtils.isNotEmpty(req.getPostalCode()) ? req.getPostalCode():member.getPostalCode());
				member.setNation(StringUtils.isNotEmpty(req.getNation()) ? req.getNation():member.getNation());
				member.setPoliceStation(StringUtils.isNotEmpty(req.getPoliceStation()) ? req.getPoliceStation():member.getPoliceStation());
				member.setNativePlace(StringUtils.isNotEmpty(req.getNativePlace()) ? req.getNativePlace():member.getNativePlace());
				member.setOfficeAddress(StringUtils.isNotEmpty(req.getOfficeAddress()) ? req.getOfficeAddress():member.getOfficeAddress());
				member.setExchangeRegion(StringUtils.isNotEmpty(req.getExchangeRegion()) ? req.getExchangeRegion():member.getExchangeRegion());
				member.setExchangeHall(StringUtils.isNotEmpty(req.getExchangeHall()) ? req.getExchangeHall():member.getExchangeHall());
				member.setMiniProgramOpenId(StringUtils.isNotEmpty(req.getMiniProgramOpenId()) ? req.getMiniProgramOpenId():member.getMiniProgramOpenId());
				member.setIsSmartcardMaster(null!=req.getIsSmartcardMaster() ? req.getIsSmartcardMaster():member.getIsSmartcardMaster());
				member.setHomePhone(StringUtils.isNotEmpty(req.getHomePhone()) ? req.getHomePhone():member.getHomePhone());
				member.setBirthday(StringUtils.isNotEmpty(req.getBirthday()) ? req.getBirthday():member.getBirthday());
				member.setPhoneVerifyState(null!=req.getPhoneVerifyState() ? req.getPhoneVerifyState():member.getPhoneVerifyState());
				member.setQq(StringUtils.isNotEmpty(req.getQq()) ? req.getQq():member.getQq());
				member.setWeixin(StringUtils.isNotEmpty(req.getWeixin()) ? req.getWeixin():member.getWeixin());
				member.setPhoto(StringUtils.isNotEmpty(req.getPhoto()) ? req.getPhoto():member.getPhoto());
				member.setIdType(StringUtils.isNotEmpty(req.getIdType()) ? req.getIdType():member.getIdType());
				member.setIdCard(StringUtils.isNotEmpty(req.getIdCard()) ? req.getIdCard():member.getIdCard());
				
				map.put("ret", ResponseContext.RES_SUCCESS_CODE);
				map.put("retInfo", ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				map.put("ret", ResponseContext.RES_NULL_ERROR_CODE);
				map.put("retInfo", "参数有误！！");
			}
		}else {
			map.put("ret", ResponseContext.RES_NULL_ERROR_CODE);
			map.put("retInfo", ResponseContext.RES_NULL_ERROR_INFO );
		}

		return map;
	}
}
