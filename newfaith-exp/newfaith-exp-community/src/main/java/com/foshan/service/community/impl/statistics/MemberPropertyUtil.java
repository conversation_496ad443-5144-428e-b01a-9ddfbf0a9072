package com.foshan.service.community.impl.statistics;

import static java.util.stream.Collectors.groupingBy;

import java.io.IOException;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.foshan.form.community.statistics.MemberPropertyVo;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.community.CommunityCache;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MemberPropertyUtil {
	public static String getMemberPropertySql(Integer[] districtIds, Integer[] buildingIds, Integer[] estateStates,String estateTypeList) {
		StringBuilder sql = new StringBuilder("SELECT a.districtname,b.buildingname,c.unitcode,c.roomnumber,"
				+ "round(c.buildingarea,2) as buildingarea,round(c.usablearea,2) as usablearea,round(c.chargingarea,2) as chargingarea,"
				+ "if(c.additionalarea>0,round(c.additionalarea,2),'') as additionalarea,c.estatetype,"
				+ "if(REPLACE(JSON_EXTRACT(c.reservedField, '$.propertyParkingArea'), '\"', '')>0,round(REPLACE(JSON_EXTRACT(c.reservedField, '$.propertyParkingArea'), '\"', ''),2),'') AS propertyParkingArea,"
				+ "if(REPLACE(JSON_EXTRACT(c.reservedField, '$.propertyParkingNum'), '\"', '')>0,REPLACE(JSON_EXTRACT(c.reservedField, '$.propertyParkingNum'), '\"', ''),'') AS propertyParkingNum,"
				+ "if(REPLACE(JSON_EXTRACT(c.reservedField, '$.defenceParkingArea'), '\"', '')>0,round(REPLACE(JSON_EXTRACT(c.reservedField, '$.defenceParkingArea'), '\"', ''),2),'') AS defenceParkingArea,"
				+ "if(REPLACE(JSON_EXTRACT(c.reservedField, '$.defenceParkingNum'), '\"', '')>0,REPLACE(JSON_EXTRACT(c.reservedField, '$.defenceParkingNum'), '\"', ''),'') AS defenceParkingNum,"
				+ "IF(d.terminationDate IS NOT NULL,'离退',CASE c.estateState WHEN 0 THEN '未收楼' WHEN 1 THEN '已入住' WHEN 2 THEN '装修中' "
				+ "WHEN 3 THEN '出租' WHEN 4 THEN '离退' WHEN 5 THEN '自住' WHEN 6 THEN '空置' END) AS estatestate,"
				+ "e.username,c.acceptanceDate as acceptanceDate,DATE_FORMAT(d.recorddate,'%Y-%m-%d') AS recorddate,"
				+ "DATE_FORMAT(d.billingdate,'%Y-%m-%d') AS billingdate,"
				+ "DATE_FORMAT(d.terminationdate,'%Y-%m-%d') AS terminationdate,"
				+ "case d.membertype when 0 then '业主' WHEN 1 THEN '住户成员' WHEN 2 THEN '租户' ELSE '' end as memberType," + "e.birthday,"
				+ "case e.sex when 0 then '女' when 1 then '男' WHEN 2 THEN '保密' ELSE '' End as sex,e.homePhone,"
				+ "e.idtype,e.idcard,e.contactperson,e.emergencycontact,"
				+ "d.buyersname,d.buyersaddress,d.buyersbankaccount,d.buyeremail,f.bankname,f.bankaccount,f.accountName,a.districtCode,e.homeAddress,"
				+ "b.comment AS estateComment,d.comment AS memberComment, "
				+"IF(REPLACE(JSON_EXTRACT(c.reservedField, '$.waterMeterBase'), '\\\"', '')>0,ROUND(REPLACE(JSON_EXTRACT(c.reservedField, '$.waterMeterBase'), '\\\"', ''),2),'') AS waterMeterBase ,"
				+ "IF(REPLACE(JSON_EXTRACT(c.reservedField, '$.electricMeterBase'), '\\\"', '')>0,ROUND(REPLACE(JSON_EXTRACT(c.reservedField, '$.electricMeterBase'), '\\\"', ''),2),'') AS electricMeterBase "
				+ "FROM t_community_district a "
				+ "inner join t_community_building b on a.id=b.districtId and a.id<>11 ")
				.append(null != districtIds && districtIds.length > 0
						? "and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " ")
				.append(null != buildingIds && buildingIds.length > 0
						? "and b.id in(" + StringUtils.join(buildingIds, ",") + ") "
						: " ")
				.append("inner join t_community_property c on b.id=c.buildingid ")
				.append(null != estateStates && estateStates.length > 0
						? "and c.estateState in(" + StringUtils.join(estateStates, ",") + ") "
						: " ")
				.append(StringUtils.isNotEmpty(estateTypeList) ? " and c.estateType in('"+estateTypeList.replaceAll(",", "','")+"')":"")
				.append("left join t_community_member_property d on d.memberType<>1 and c.id=d.propertyid and d.parentpropertyid is null ")
				.append("left join t_account e on d.memberid=e.id ")
				.append("left join t_community_payment_account f ON c.paymentaccountid=f.id ")
				.append("ORDER BY a.districtorder,b.buildingorder,c.unitcode");

		return sql.toString();
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static void transMemberPropertyExcel(String[] titles, HttpServletResponse response) {
		if (CommunityCache.cache.containsKey("业主信息报表")) {
			Map<String, Collection> temp = CommunityCache.cache.get("业主信息报表");
			LinkedList<MemberPropertyVo> voList = new LinkedList<>();

			for (String o : temp.keySet()) {
				voList = (LinkedList<MemberPropertyVo>) temp.get(o);
			}

			Map<String, List<MemberPropertyVo>> memberPropertyMap = (HashMap<String, List<MemberPropertyVo>>) voList
					.stream().collect(groupingBy(MemberPropertyVo::getNewDistrictCode));

			if (memberPropertyMap.size() > 1) {
				memberPropertyMap.put("000全区", voList);
			}

			TreeSet<String> keys = new TreeSet<>(Comparator.naturalOrder());
			memberPropertyMap.keySet().forEach(o -> {
				keys.add(o);
			});

			XSSFWorkbook wb = new XSSFWorkbook();

			for (String o : keys) {

				LinkedList<MemberPropertyVo> tempList = new LinkedList(memberPropertyMap.get(o));

				Sheet sheet = wb.createSheet(o.substring(3));

				Map<String, XSSFCellStyle> cellStyle = ExcelUtil.getExcelStyle(wb);

				Row title = sheet.createRow(0);
				for (int i = 0; i < titles.length; i++) {
					Cell tt = title.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
					tt.setCellStyle(cellStyle.get("title"));
				}

				int i = 1;
				for (MemberPropertyVo vo : tempList) {
					Row row = sheet.createRow(i);
					// 楼盘名称
					Cell cell1 = row.createCell(0, CellType.STRING);
					cell1.setCellStyle(cellStyle.get("cell_left"));
					cell1.setCellValue(vo.getDistrictName());

					// 楼阁
					Cell cell2 = row.createCell(1, CellType.STRING);
					cell2.setCellStyle(cellStyle.get("cell_left"));
					cell2.setCellValue(vo.getBuildingName());

					// 单元编号
					Cell cell3 = row.createCell(2, CellType.STRING);
					cell3.setCellStyle(cellStyle.get("cell_left"));
					cell3.setCellValue(vo.getUnitCode());

					// 房间号
					Cell cell4 = row.createCell(3, CellType.STRING);
					cell4.setCellStyle(cellStyle.get("cell"));
					cell4.setCellValue(vo.getRoomNumber());

					// 建筑面积
					Cell cell5 = row.createCell(4, CellType.STRING);
					cell5.setCellStyle(cellStyle.get("cell_left"));
					cell5.setCellValue(vo.getBuildingArea());
					
					// 使用面积
					Cell cell6 = row.createCell(5, CellType.STRING);
					cell6.setCellStyle(cellStyle.get("cell_left"));
					cell6.setCellValue(vo.getUsableArea());

					// 收费面积
					Cell cell7 = row.createCell(6, CellType.STRING);
					cell7.setCellStyle(cellStyle.get("cell_left"));
					cell7.setCellValue(vo.getChargingArea());

					// 附加面积
					Cell cell8 = row.createCell(7, CellType.STRING);
					cell8.setCellStyle(cellStyle.get("cell_left"));
					cell8.setCellValue(vo.getAdditionalArea());

					// 单元类型
					Cell cell9 = row.createCell(8, CellType.STRING);
					cell9.setCellStyle(cellStyle.get("cell_left"));
					cell9.setCellValue(vo.getEstateType());

					// 产权车位面积
					Cell cell10 = row.createCell(9, CellType.STRING);
					cell10.setCellStyle(cellStyle.get("cell_left"));
					cell10.setCellValue(vo.getPropertyParkingArea());

					// 产权车位个数
					Cell cell11 = row.createCell(10, CellType.STRING);
					cell11.setCellStyle(cellStyle.get("cell_left"));
					cell11.setCellValue(vo.getPropertyParkingNum());

					// 人防车位面积
					Cell cell12 = row.createCell(11, CellType.STRING);
					cell12.setCellStyle(cellStyle.get("cell_left"));
					cell12.setCellValue(vo.getDefenceParkingArea());

					// 人防车位个数
					Cell cell13 = row.createCell(12, CellType.STRING);
					cell13.setCellStyle(cellStyle.get("cell_left"));
					cell13.setCellValue(vo.getDefenceParkingNum());

					// 单元状态
					Cell cell14 = row.createCell(13, CellType.STRING);
					cell14.setCellStyle(cellStyle.get("cell_left"));
					cell14.setCellValue(vo.getEstateState());

					// 业主姓名
					Cell cell15 = row.createCell(14, CellType.STRING);
					cell15.setCellStyle(cellStyle.get("cell_left"));
					cell15.setCellValue(vo.getUserName());

					// 收楼日期
					Cell cell16 = row.createCell(15, CellType.STRING);
					cell16.setCellStyle(cellStyle.get("cell_left"));
					cell16.setCellValue(vo.getAcceptanceDate());
					
					// 入住日期
					Cell cell17 = row.createCell(16, CellType.STRING);
					cell17.setCellStyle(cellStyle.get("cell_left"));
					cell17.setCellValue(vo.getRecordDate());

					// 计费日期
					Cell cell18 = row.createCell(17, CellType.STRING);
					cell18.setCellStyle(cellStyle.get("cell_left"));
					cell18.setCellValue(vo.getBillingDate());

					// 离退日期
					Cell cell19 = row.createCell(18, CellType.STRING);
					cell19.setCellStyle(cellStyle.get("cell_left"));
					cell19.setCellValue(vo.getTerminationDate());

					// 业主类型
					Cell cell20 = row.createCell(19, CellType.STRING);
					cell20.setCellStyle(cellStyle.get("cell_left"));
					cell20.setCellValue(vo.getMemberType());

					// 生日
					Cell cell21 = row.createCell(20, CellType.STRING);
					cell21.setCellStyle(cellStyle.get("cell_left"));
					cell21.setCellValue(vo.getBirthDay());

					// 性别
					Cell cell22 = row.createCell(21, CellType.STRING);
					cell22.setCellStyle(cellStyle.get("cell_left"));
					cell22.setCellValue(vo.getSex());

					// 联系电话
					Cell cell23 = row.createCell(22, CellType.STRING);
					cell23.setCellStyle(cellStyle.get("cell_left"));
					cell23.setCellValue(vo.getHomePhone());

					// 证件类型
					Cell cell24 = row.createCell(23, CellType.STRING);
					cell24.setCellStyle(cellStyle.get("cell_left"));
					cell24.setCellValue(vo.getIdType());

					// 证件号码
					Cell cell25 = row.createCell(24, CellType.STRING);
					cell25.setCellStyle(cellStyle.get("cell_left"));
					cell25.setCellValue(vo.getIdcard());

					// 紧急联系人
					Cell cell26 = row.createCell(25, CellType.STRING);
					cell26.setCellStyle(cellStyle.get("cell_left"));
					cell26.setCellValue(vo.getContactPerson());

					// 紧急联系人联系方式
					Cell cell27 = row.createCell(26, CellType.STRING);
					cell27.setCellStyle(cellStyle.get("cell_left"));
					cell27.setCellValue(vo.getEmergencyContact());

					// 购方名称
					Cell cell28 = row.createCell(27, CellType.STRING);
					cell28.setCellStyle(cellStyle.get("cell_left"));
					cell28.setCellValue(vo.getBuyersName());

					// 购方地址
					Cell cell29 = row.createCell(28, CellType.STRING);
					cell29.setCellStyle(cellStyle.get("cell_left"));
					cell29.setCellValue(vo.getBuyersAddress());

					// 购方银行账号
					Cell cell30 = row.createCell(29, CellType.STRING);
					cell30.setCellStyle(cellStyle.get("cell_left"));
					cell30.setCellValue(vo.getBuyersBankAccount());

					// 购方邮箱
					Cell cell31 = row.createCell(30, CellType.STRING);
					cell31.setCellStyle(cellStyle.get("cell_left"));
					cell31.setCellValue(vo.getBuyerEmail());

					// 划账银行名称
					Cell cell32 = row.createCell(31, CellType.STRING);
					cell32.setCellStyle(cellStyle.get("cell_left"));
					cell32.setCellValue(vo.getBankName());

					// 划账银行账号
					Cell cell33 = row.createCell(32, CellType.STRING);
					cell33.setCellStyle(cellStyle.get("cell_left"));
					cell33.setCellValue(vo.getBankAccount());
					
					
					
					// 水表底数
					Cell cell34 = row.createCell(33, CellType.STRING);
					cell34.setCellStyle(cellStyle.get("cell_left"));
					cell34.setCellValue(vo.getWaterMeterBase());
					
					// 电表底数
					Cell cell35 = row.createCell(34, CellType.STRING);
					cell35.setCellStyle(cellStyle.get("cell_left"));
					cell35.setCellValue(vo.getElectricMeterBase());

					// 账号名
					Cell cell36 = row.createCell(35, CellType.STRING);
					cell36.setCellStyle(cellStyle.get("cell_left"));
					cell36.setCellValue(vo.getAccountName());

					// 联系地址
					Cell cell37 = row.createCell(36, CellType.STRING);
					cell37.setCellStyle(cellStyle.get("cell_left"));
					cell37.setCellValue(vo.getHomeAddress());
					
					// 单元备注
					Cell cell38 = row.createCell(37, CellType.STRING);
					cell38.setCellStyle(cellStyle.get("cell_left"));
					cell38.setCellValue(vo.getEstateMemo());
					
					// 业主备注
					Cell cell39 = row.createCell(38, CellType.STRING);
					cell39.setCellStyle(cellStyle.get("cell_left"));
					cell39.setCellValue(vo.getMemberMemo());
					
					i++;
				}

				sheet.autoSizeColumn(0, true);
				sheet.autoSizeColumn(2, true);
				sheet.autoSizeColumn(14, true);
				sheet.autoSizeColumn(21, true);
				sheet.autoSizeColumn(30, true);
				sheet.autoSizeColumn(31, true);
				sheet.autoSizeColumn(32, true);
				sheet.autoSizeColumn(33, true);
				sheet.autoSizeColumn(34, true);
				sheet.autoSizeColumn(35, true);
				sheet.setColumnWidth(36, 50 * 256);
				sheet.setColumnWidth(37, 50 * 256);
				sheet.setColumnWidth(38, 50 * 256);

				// 冻结首行
				sheet.createFreezePane(0, 1, 0, 1);
				sheet.createFreezePane(4, 1, 4, 1);
				// 增加筛选框
				CellRangeAddress c = CellRangeAddress.valueOf("A1:AH1");
				sheet.setAutoFilter(c);
			}
			String fileName = "业主信息报表" + DateUtil.formatByStyle(new Date(), "_yyyy_MM_dd_HH_mm_ss") + ".xlsx";

			try {
				ExcelExportUtil.export(response, wb, fileName);
			} catch (IOException e) {
				wb = null;
				log.error(e.getMessage());
			}

		}
	}
}
