package com.foshan.service.community;

import com.foshan.form.community.request.communityAssetReq.*;
import com.foshan.form.response.IResponse;

public interface ICommunityAssetService {
    public IResponse addCommunityAsset(AddCommunityAssetReq req);
    public IResponse deleteCommunityAsset(CommunityAssetReq req);
    public IResponse getCommunityAsset(CommunityAssetReq req);
    public IResponse modifyCommunityAsset(CommunityAssetReq req);
    public IResponse auditCommunityAsset(AuditCommunityAssetReq req);
    public IResponse getCommunityAssetList(GetCommunityAssetListReq req);

    public IResponse addCommunityAssetComment(AddCommunityAssetCommentReq req);
    public IResponse modifyCommunityAssetComment(AddCommunityAssetCommentReq req);
    public IResponse auditCommunityAssetComment(AuditCommunityAssetReq req);
    public IResponse deleteCommunityAssetComment(AddCommunityAssetReq req);
    public IResponse getCommunityAssetCommentList(GetCommunityAssetCommentListReq req);

}
