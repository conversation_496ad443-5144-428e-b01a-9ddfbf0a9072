package com.foshan.service.community.shiro.realmhandler.handler;
import javax.servlet.http.HttpServletRequest;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.subject.Subject;
import org.springframework.stereotype.Component;
//import com.alibaba.fastjson.JSONObject;
import com.foshan.form.community.request.CommunityMemberLoginReq;
import com.foshan.form.response.IResponse;
import com.foshan.service.community.shiro.realmhandler.AbstractMemberRealmHandler;
import com.foshan.service.community.shiro.realmhandler.RealmHandlerType;
/**
 * 会员短信登录鉴权处理
 * <AUTHOR>
 *
 */
@Component
@RealmHandlerType("2")
public class SmsMemberRealmHandler extends AbstractMemberRealmHandler {

	@Override
	public SimpleAuthenticationInfo handleAuthenticationInfo(AuthenticationToken token, String realmName) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public IResponse memberLogin(CommunityMemberLoginReq req, HttpServletRequest request, Subject curUser) {
		// TODO Auto-generated method stub
		return null;
	}

}
