package com.foshan.service.community.impl;

import java.text.SimpleDateFormat;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.foshan.entity.AssetEntity;
import com.foshan.entity.community.CommunityCecorationItemsAttachmentEntity;
import com.foshan.entity.community.CommunityDecorationAttachmentEntity;
import com.foshan.entity.community.CommunityDecorationItemsEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.community.CommunityPropertyEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.service.community.ICommunityDecorationAttachmentService;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.form.community.request.CommunityDecorationAttachmentReq;
import com.foshan.form.community.response.communityDecorationAttachment.AddCommunityDecorationAttachmentRes;
import com.foshan.form.community.response.communityDecorationAttachment.ModifyCommunityDecorationAttachmentRes;
import com.foshan.form.community.response.communityDecorationAttachment.GetCommunityDecorationAttachmentInfoRes;
import com.foshan.form.community.response.communityDecorationAttachment.GetCommunityDecorationAttachmentListRes;
import com.foshan.form.AssetForm;
import com.foshan.form.community.CommunityDecorationAttachmentForm;
import com.foshan.form.community.CommunityDecorationItemsBriefForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.IResponse;
import com.foshan.util.DateUtil;
import com.foshan.dao.generic.Page;

@Transactional
@Service("communityDecorationAttachmentService")
public class CommunityDecorationAttachmentServiceImpl extends GenericCommunityService implements ICommunityDecorationAttachmentService{

	@Override
	public IResponse getCommunityDecorationAttachmentList(CommunityDecorationAttachmentReq req) {
		GetCommunityDecorationAttachmentListRes res = new GetCommunityDecorationAttachmentListRes();
		Page<CommunityDecorationAttachmentEntity> page = new Page<CommunityDecorationAttachmentEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityDecorationAttachmentEntity a ");
//				+ " inner join a.itemsAttachmentList c inner join c.decorationItems b where 1=1 ");
		if(StringUtils.isNotEmpty(req.getDecorationItemsIdList()) || null!=req.getPropertyId()) {
			hql.append(" inner join a.itemsAttachmentList c inner join c.decorationItems b");
			if(null!=req.getPropertyId()) {
				hql.append(" inner join c.district di inner join di.buildingList bu inner join bu.propertyList p where"
						+ " p.estateType=c.estateType and p.id="+req.getPropertyId());
			}else {
				hql.append(" where ");	
			}
			
		}
		hql.append(StringUtils.isNotEmpty(req.getAttachmentName()) ? " and a.attachmentName like'%"+req.getAttachmentName()+"%'":"")
			.append(StringUtils.isNotEmpty(req.getDecorationItemsIdList()) ? " and b.id in("+req.getDecorationItemsIdList() +")": "");
		hql.append(" ORDER BY a.orders desc ");
		page = communityDecorationAttachmentDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityDecorationAttachmentForm communityDecorationAttachmentForm = new CommunityDecorationAttachmentForm();
			communityDecorationAttachmentForm.setCommunityDecorationAttachmentId(o.getId());
            communityDecorationAttachmentForm.setAttachmentName(o.getAttachmentName());
            communityDecorationAttachmentForm.setCreateTime(null != o.getCreateTime()? DateUtil.formatLongFormat(o.getCreateTime()) : "");
            communityDecorationAttachmentForm.setOrders(o.getOrders());
            communityDecorationAttachmentForm.setAssetForm(getAsset(o.getAsset()));
			res.getCommunityDecorationAttachmentList().add(communityDecorationAttachmentForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addCommunityDecorationAttachment(CommunityDecorationAttachmentReq req) {
		AddCommunityDecorationAttachmentRes res = new AddCommunityDecorationAttachmentRes();
		if (null!=req.getAttachmentId()) {
			AssetEntity asset = assetDao.get(req.getAttachmentId());
			if(null==asset) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			CommunityDecorationAttachmentEntity communityDecorationAttachment = new CommunityDecorationAttachmentEntity();
			
            communityDecorationAttachment.setAttachmentName(req.getAttachmentName());
            communityDecorationAttachment.setOrders(null!=req.getOrders() ?req.getOrders():0);
            communityDecorationAttachment.setAsset(asset);
            if(StringUtils.isNotEmpty(req.getDecorationItemsIdList()) && StringUtils.isNotEmpty(req.getDistrictIdList()) 
            		&& StringUtils.isNotEmpty(req.getEstateTypeList())) {
            	String[] decorationItemsIdList = req.getDecorationItemsIdList().split(",");
            	String[] districtIdList =  req.getDistrictIdList().split(",");
            	String[] estateTypeList= req.getEstateTypeList().split(",");
            	communityDecorationAttachmentDao.save(communityDecorationAttachment);
				for (String id : decorationItemsIdList) {
					CommunityDecorationItemsEntity decorationItems = communityDecorationItemsDao.get(Integer.valueOf(id));
					if(null!=decorationItems) {
//						communityDecorationAttachment.getDecorationItemsList().add(decorationItems);
						if(null!=districtIdList) {
							for (String districtId : districtIdList) {
								CommunityDistrictEntity district = communityDistrictDao.get(Integer.valueOf(districtId));
								if(null != estateTypeList) {
									for (String estateType : estateTypeList) {
										CommunityCecorationItemsAttachmentEntity itemsAttachment = new CommunityCecorationItemsAttachmentEntity();
										itemsAttachment.setAttachment(communityDecorationAttachment);
										itemsAttachment.setDecorationItems(decorationItems);
										itemsAttachment.setDistrict(district);
										itemsAttachment.setEstateType(estateType);
										communityCecorationItemsAttachmentDao.save(itemsAttachment);
									}
								}
							}
						}
					}
					
				}
            }
			
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse modifyCommunityDecorationAttachment(CommunityDecorationAttachmentReq req) {
		ModifyCommunityDecorationAttachmentRes res = new ModifyCommunityDecorationAttachmentRes();
		if (null!=req.getCommunityDecorationAttachmentId() ) {
			CommunityDecorationAttachmentEntity communityDecorationAttachment = communityDecorationAttachmentDao.get(req.getCommunityDecorationAttachmentId()) ;
			if(null != communityDecorationAttachment){
				AssetEntity asset = null;
				if(null!=req.getAttachmentId()) {
					asset = assetDao.get(req.getAttachmentId());
					if(null==asset) {
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
						return res;
					}
				}else {
					asset = communityDecorationAttachment.getAsset();
				}
	            if(StringUtils.isNotEmpty(req.getDecorationItemsIdList()) && StringUtils.isNotEmpty(req.getDistrictIdList()) 
	            		&& StringUtils.isNotEmpty(req.getEstateTypeList())) {
	            	List<CommunityCecorationItemsAttachmentEntity> itemsAttachmentList = 
	            			communityCecorationItemsAttachmentDao.getListByHql("select distinct a from "
	            					+ "CommunityCecorationItemsAttachmentEntity a where a.attachment.id="
	            					+communityDecorationAttachment.getId(), "");
	            	if(null!=itemsAttachmentList) {
	            		for(CommunityCecorationItemsAttachmentEntity itemsAttachment : itemsAttachmentList) {
	            			communityCecorationItemsAttachmentDao.delete(itemsAttachment);
	            		}
	            	}
	            	String[] decorationItemsIdList = req.getDecorationItemsIdList().split(",");
	            	String[] districtIdList =  req.getDistrictIdList().split(",");
	            	String[] estateTypeList= req.getEstateTypeList().split(",");
	            	communityDecorationAttachmentDao.save(communityDecorationAttachment);
					for (String id : decorationItemsIdList) {
						CommunityDecorationItemsEntity decorationItems = communityDecorationItemsDao.get(Integer.valueOf(id));
						if(null!=decorationItems) {
//							communityDecorationAttachment.getDecorationItemsList().add(decorationItems);
							if(null!=districtIdList) {
								for (String districtId : districtIdList) {
									CommunityDistrictEntity district = communityDistrictDao.get(Integer.valueOf(districtId));
									if(null != estateTypeList) {
										for (String estateType : estateTypeList) {
											CommunityCecorationItemsAttachmentEntity itemsAttachment = new CommunityCecorationItemsAttachmentEntity();
											itemsAttachment.setAttachment(communityDecorationAttachment);
											itemsAttachment.setDecorationItems(decorationItems);
											itemsAttachment.setDistrict(district);
											itemsAttachment.setEstateType(estateType);
											communityCecorationItemsAttachmentDao.save(itemsAttachment);
										}
									}
								}
							}
						}
						
					}
	            }
				communityDecorationAttachment.setAsset(asset);
                communityDecorationAttachment.setAttachmentName(StringUtils.isNotEmpty(req.getAttachmentName()) ? 
                		req.getAttachmentName():communityDecorationAttachment.getAttachmentName());
                communityDecorationAttachment.setOrders(null!=req.getOrders() ?req.getOrders():communityDecorationAttachment.getOrders());
				res.setCommunityDecorationAttachmentId(communityDecorationAttachment.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteCommunityDecorationAttachment(CommunityDecorationAttachmentReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityDecorationAttachmentId()) {
		CommunityDecorationAttachmentEntity communityDecorationAttachment = communityDecorationAttachmentDao.get(req.getCommunityDecorationAttachmentId());
			if (null != communityDecorationAttachment) {
				//communityDecorationAttachmentDao.deleteById(req.getCommunityDecorationAttachmentId());
				//communityDecorationAttachment.setState(EntityContext.RECORD_STATE_INVALID);
				communityDecorationAttachment.setAsset(null);
				communityDecorationAttachment.setItemsAttachmentList(null);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityDecorationAttachmentInfo(CommunityDecorationAttachmentReq req) {
		GetCommunityDecorationAttachmentInfoRes res = new GetCommunityDecorationAttachmentInfoRes();
		if (null != req.getCommunityDecorationAttachmentId()) {
			CommunityDecorationAttachmentEntity communityDecorationAttachment = communityDecorationAttachmentDao.get(req.getCommunityDecorationAttachmentId());
			if (null != communityDecorationAttachment) {
				CommunityDecorationAttachmentForm communityDecorationAttachmentForm = new CommunityDecorationAttachmentForm();
				communityDecorationAttachmentForm.setCommunityDecorationAttachmentId(communityDecorationAttachment.getId());
                communityDecorationAttachmentForm.setAttachmentName(communityDecorationAttachment.getAttachmentName());
                communityDecorationAttachmentForm.setCreateTime(null != communityDecorationAttachment.getCreateTime()? DateUtil.formatLongFormat(communityDecorationAttachment.getCreateTime()) : "");
                communityDecorationAttachmentForm.setOrders(communityDecorationAttachment.getOrders());
                communityDecorationAttachmentForm.setAssetForm(getAsset(communityDecorationAttachment.getAsset()));
                communityDecorationAttachment.getItemsAttachmentList().forEach(o->{
                	CommunityDecorationItemsBriefForm  decorationItemsBriefForm = new CommunityDecorationItemsBriefForm();
                	decorationItemsBriefForm.setItemName(o.getDecorationItems().getItemName());
                	decorationItemsBriefForm.setCommunityDecorationItemsId(o.getId());
                	communityDecorationAttachmentForm.getDecorationItemsBriefList().add(decorationItemsBriefForm);
                });
                
				res.setCommunityDecorationAttachmentForm(communityDecorationAttachmentForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
protected AssetForm getAsset(AssetEntity asset) {
		
		AssetForm assetForm = new AssetForm();

		if (null != asset) {
			assetForm.setAssetId(asset.getId());
			assetForm.setAssetType(asset.getAssetType());
			assetForm.setImageFile(asset.getImageFile());
			assetForm.setSmallImageFile(asset.getSmallImageFile());
			assetForm.setIsCover(asset.getIsCover());
			assetForm.setPackageFlag(asset.getPackageFlag());
			assetForm.setTimeLength(asset.getTimeLength());
			assetForm.setSummaryShort(asset.getSummaryShort());
			assetForm.setParameterInfo(asset.getParameterInfo());
			assetForm.setAssetCode(asset.getAssetCode());
			assetForm.setAssetName(asset.getAssetName());
			assetForm.setAssetOrders(asset.getAssetOrders());
			assetForm.setPublishedTime(
					asset.getPublishedTime() != null ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(asset.getPublishedTime())
							: "");
			assetForm.setPackageOrders(asset.getPackageCount());
			assetForm.setServiceCode(asset.getAssetCode());
			assetForm.setValidTime(asset.getValidTime() != null
					? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(asset.getValidTime())
					: "");
//			assetForm.setFolderAssetId(asset.getFolderAssetId());
			assetForm.setServiceId(asset.getServiceId());
			assetForm.setMiddleImageFile(asset.getMiddleImageFile());
			assetForm.setRecommendCount(asset.getRecommendCount());
			assetForm.setDirector(asset.getDirector());
			assetForm.setActorsDisplay(asset.getActorsDisplay());
			assetForm.setBroadcastCount(asset.getBroadcastCount());
			assetForm.setPreviewAssetId(asset.getPreviewAssetId());
			assetForm.setPreviewProviderId(asset.getPreviewProviderId());
		}
		return assetForm;
	}

}