package com.foshan.service.community.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityPropertyEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.request.CommunityPropertyReq;
import com.foshan.form.community.response.communityProperty.GetCommunityPropertyListRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.ICommunityPropertyService;

@Transactional
@Service("communityPropertyService")
public class CommunityPropertyServiceImpl extends GenericCommunityService implements ICommunityPropertyService {

	@Override
	public IResponse getCommunityPropertyList(CommunityPropertyReq req) {
		GetCommunityPropertyListRes res = new GetCommunityPropertyListRes();

		Object userObj = getPrincipal(true);
		StringBuilder sql = new StringBuilder("select distinct a.* from t_community_property a  "
				+ " inner join t_community_building e on e.id=a.buildingId inner join t_community_district c on c.id=e.districtId "+ 
				" inner join t_community d on c.communityId=d.id ");
		sql.append(null!= req.getMeterId() && null!=req.getBindingMeter() && req.getBindingMeter()==1 ? 
				" inner join t_community_meter_property mp on a.id=mp.propertyId":"");
		if(null!=userObj && userObj instanceof CommunityMemberEntity) {
			CommunityMemberEntity member = (CommunityMemberEntity)userObj;
			if(null!=req.getUnbindingMember() && req.getUnbindingMember()==1) {
				sql.append("where a.id not in( select distinct a.id from t_community_property a "
						+ "INNER JOIN t_community_member_property b ON b.propertyId=a.id   where b.memberId="+member.getId())
				.append(null != req.getState() ? " and a.state=" + req.getState()  :
					" and a.state="+EntityContext.RECORD_STATE_VALID+")")
				.append(null != req.getState() ? " and a.state=" + req.getState()  :
					" and a.state="+EntityContext.RECORD_STATE_VALID);
			}else {
				sql.append(" INNER JOIN t_community_member_property b ON b.propertyId=a.id   where b.memberId="+member.getId())
					.append(null != req.getState() ? " and a.state=" + req.getState()  :
					" and a.state="+EntityContext.RECORD_STATE_VALID);
			}
		}else if(null!=req.getMemberId()){
			if(null!=req.getUnbindingMember() && req.getUnbindingMember()==1) {
				sql.append(" where a.id not in( select distinct a.id from t_community_property a "
						+ "INNER JOIN t_community_member_property b ON b.propertyId=a.id   where b.memberId="+req.getMemberId())
				.append(null != req.getState() ? " and a.state=" + req.getState()  :
					" and a.state="+EntityContext.RECORD_STATE_VALID+")")
				.append(null != req.getState() ? " and a.state=" + req.getState()  :
					" and a.state="+EntityContext.RECORD_STATE_VALID);
			}else {
				sql.append(" INNER JOIN t_community_member_property b ON b.propertyId=a.id   where b.memberId="+req.getMemberId())
				.append(null != req.getState() ? " and a.state=" + req.getState()  :
					" and a.state="+EntityContext.RECORD_STATE_VALID);
			}
		}else {
			sql.append(" where ").append(null != req.getState() ? "  a.state=" + req.getState()  :
				"  a.state="+EntityContext.RECORD_STATE_VALID);
		}
		sql.append((null!=req.getCommunityId() ? " and d.id="+req.getCommunityId():""))
			.append(null!=req.getBuildingId() ? " and e.id="+req.getBuildingId():"")
			.append(null!=req.getDistrictdId() ? " and c.id="+req.getDistrictdId():"")
			.append(null!= req.getMeterId() ?  
					(null!=req.getBindingMeter() ?( req.getBindingMeter()==1 ? " and mp.meterId="+req.getMeterId() : 
						(req.getBindingMeter()==0 ? " and a.id not in(SELECT propertyId FROM `t_community_meter_property` WHERE meterId="+req.getMeterId()+")":"")) :
						""):"");
		
		if(StringUtils.isNotEmpty(req.getPropertyTypeList())) {
			StringBuilder typeSql = new StringBuilder();
            String[] types = req.getPropertyTypeList().split(",");
            for (String type : types) {
            	if(type.equals("0")) {
            		typeSql.append("'CARD',");
            	}else if(type.equals("1")){
            		typeSql.append("'RESIDENCE',");
            	}else if(type.equals("2")){
            		typeSql.append("'SHOP',");
            	}else if(type.equals("3")){
            		typeSql.append("'PARKING',");
            	}else if(type.equals("4")){
            		typeSql.append("'PUBLIC_AREA',");
            	}else if(type.equals("5")){
            		typeSql.append("'ESTATE',");
            	}
            }
            sql.append(typeSql.length()>0 ? " and a.propertyType IN("+
                typeSql.toString().substring(0, typeSql.toString().length()-1)+")" : "");
		}
		
		int totalCount = communityPropertyDao
				.queryListBysql(sql.toString()).size();
		List<CommunityPropertyEntity> resultList = communityPropertyDao.getListBySql(sql.toString() + " limit "+
				((req.getRequestPage() <= 1 ? 0 : (req.getRequestPage() - 1) * req.getPageSize())+","+
				req.getPageSize()),"");
		
		res.setPageSize(req.getPageSize());
		res.setCurrentPage(req.getRequestPage());
		res.setTotal(totalCount == 0 ? 0 : (totalCount - 1) / req.getPageSize() + 1);
		res.setTotalResult(totalCount);
		if(null != resultList) {
			resultList.forEach(o -> {
				res.getPropertyFormList().add(getCommunityPropertyForm(o,req.getAuditStateList(),req.getRelationList()));

			});
		}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

}
