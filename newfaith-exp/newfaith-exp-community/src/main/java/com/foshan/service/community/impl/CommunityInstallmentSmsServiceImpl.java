package com.foshan.service.community.impl;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.UserEntity;
import com.foshan.entity.community.CommunityBankDepositRecordEntity;
import com.foshan.entity.community.CommunityBuilderEntity;
import com.foshan.entity.community.CommunityBuildingEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityInstallmentSmsEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityUserEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.UserForm;
import com.foshan.form.community.CommunityInstallmentSmsForm;
import com.foshan.form.community.request.CommunityInstallmentSmsReq;
import com.foshan.form.community.response.communityInstallmentSms.GetCommunityInstallmentSmsInfoRes;
import com.foshan.form.community.response.communityInstallmentSms.GetCommunityInstallmentSmsListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.user.GetUserListRes;
import com.foshan.service.community.ICommunityInstallmentSmsService;
import com.foshan.util.DateUtil;

@Transactional
@Service("communityInstallmentSmsService")
public class CommunityInstallmentSmsServiceImpl extends GenericCommunityService implements ICommunityInstallmentSmsService{

	@Override
	public IResponse getCommunityInstallmentSmsList(CommunityInstallmentSmsReq req) {
		GetCommunityInstallmentSmsListRes res = new GetCommunityInstallmentSmsListRes();
        Page<CommunityInstallmentSmsEntity> page = new Page<>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
        StringBuilder hql = new StringBuilder("select distinct a from CommunityInstallmentSmsEntity a inner join a.estate b   where a.state ='1' "
        		+ "and a.parentInstallmentSms is null ");
        hql.append(StringUtils.isNoneEmpty(req.getInstallmentName()) ? " and a.installmentName like'%"+req.getInstallmentName()+"%'":"")
        	.append(null!=req.getPeriods() ? " and a.periods ='"+req.getPeriods()+"'":"")
        	.append(null!=req.getSendStatus() ? "  and a.sendStatus ="+req.getSendStatus():"")
        	.append(StringUtils.isNoneEmpty(req.getUnitCode()) ? " and b.unitCode like'%"+req.getUnitCode()+"%'":"")
        	.append(null!=req.getPayStatus() ? "  and a.payStatus ="+req.getPayStatus():"")
        	.append(null!=req.getUseStatus() ? "  and a.useStatus ="+req.getUseStatus():"");
    	hql.append(" ORDER BY a.createTime desc");
        page = communityInstallmentSmsDao.queryPage(page,hql.toString());
		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			res.getInstallmentSmsFormList().add(getCommunityInstallmentSmsForm(o));
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public CommunityInstallmentSmsForm getCommunityInstallmentSmsForm(CommunityInstallmentSmsEntity o) {
		CommunityInstallmentSmsForm installmentSmsForm = new CommunityInstallmentSmsForm();
		installmentSmsForm.setAmount(o.getAmount().toString());
		installmentSmsForm.setInstallmentName(o.getInstallmentName());
		installmentSmsForm.setInstallmentSmsId(o.getId());
		installmentSmsForm.setPayStatus(o.getPayStatus());
		installmentSmsForm.setPeriods(o.getPeriods());
		installmentSmsForm.setSendDate(null != o.getSendDate() ? DateUtil.format(o.getSendDate(),2) :"");
		installmentSmsForm.setSendStatus(o.getSendStatus());
		installmentSmsForm.setUseStatus(o.getUseStatus());
		installmentSmsForm.setCreateTime(null != o.getCreateTime() ? DateUtil.format(o.getCreateTime(),1) :"");
		installmentSmsForm.setLastModifyTime(null != o.getLastModifyTime() ? DateUtil.format(o.getLastModifyTime(),1) :"");
		installmentSmsForm.setUnitCode(null!=o.getEstate()? o.getEstate().getUnitCode():"");
		installmentSmsForm.setEstateId(null!=o.getEstate()? o.getEstate().getId():null);
		if(null!=o.getSubInstallmentSmsList()) {
			for(CommunityInstallmentSmsEntity p : o.getSubInstallmentSmsList()) {
				installmentSmsForm.getSubInstallmentSmsList().add(getCommunityInstallmentSmsForm(p));
			}
		}
		o.getUserList().forEach(p->{
			UserForm user = new UserForm();
			user.setUserId(p.getId());
			user.setName(StringUtils.isNoneEmpty(p.getName()) ? p.getName():"");
			user.setPhone(StringUtils.isNoneEmpty(p.getPhone()) ? p.getPhone():"");
			installmentSmsForm.getUserList().add(user);
		});
		return installmentSmsForm;
	}
	
	

	@Override
	public IResponse addCommunityInstallmentSms(CommunityInstallmentSmsReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getEstateId() ) {
			CommunityEstateEntity estate = communityEstateDao.get(req.getEstateId());
			if(null == estate) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			
			CommunityInstallmentSmsEntity installmentSms = new CommunityInstallmentSmsEntity();
			installmentSms.setAmount(StringUtils.isNotEmpty(req.getAmount()) ? new BigDecimal(req.getAmount())
					: new BigDecimal("0"));
			installmentSms.setInstallmentName(StringUtils.isNotEmpty(req.getInstallmentName()) ? req.getInstallmentName() :"");
			installmentSms.setPayStatus(0);
			installmentSms.setPeriods(req.getPeriods());
			installmentSms.setSendStatus(0);
			installmentSms.setUseStatus(req.getUseStatus());

            installmentSms.setState(EntityContext.RECORD_STATE_VALID);
            installmentSms.setLastModifyTime(new Timestamp(new Date().getTime()));
            installmentSms.setEstate(estate);
            if(StringUtils.isNotEmpty(req.getUserIdList())) {
    			String[] userIds = req.getUserIdList().split(",");
    			for (String userId : userIds) {
    				UserEntity user = userDao.get(Integer.parseInt(userId));
    				if (null != user) {
    					installmentSms.getUserList().add(user);
    				}
    			}
            }
            try {
            	installmentSms.setSendDate(StringUtils.isNotEmpty(req.getSendDate()) ? DateUtil.parse(req.getSendDate(), 2) : null);
  			} catch (ParseException e) {
  				e.printStackTrace();
  			}
            communityInstallmentSmsDao.save(installmentSms);
            if(null!=req.getSubInstallmentSmsList()) {
            	for(CommunityInstallmentSmsForm installmentSmsForm : req.getSubInstallmentSmsList()) {
            		CommunityInstallmentSmsEntity subInstallmentSms = new CommunityInstallmentSmsEntity();
            		subInstallmentSms.setAmount(StringUtils.isNotEmpty(installmentSmsForm.getAmount()) ? new BigDecimal(installmentSmsForm.getAmount())
        					: new BigDecimal("0"));
            		subInstallmentSms.setInstallmentName(StringUtils.isNotEmpty(installmentSmsForm.getInstallmentName()) ? installmentSmsForm.getInstallmentName() :"");
            		subInstallmentSms.setPayStatus(0);
            		subInstallmentSms.setPeriods(installmentSmsForm.getPeriods());
            		subInstallmentSms.setSendStatus(0);
            		subInstallmentSms.setUseStatus(req.getUseStatus());

            		subInstallmentSms.setState(EntityContext.RECORD_STATE_VALID);
            		subInstallmentSms.setLastModifyTime(new Timestamp(new Date().getTime()));
            		subInstallmentSms.setEstate(estate);
                    try {
                    	subInstallmentSms.setSendDate(StringUtils.isNotEmpty(installmentSmsForm.getSendDate()) ? DateUtil.parse(installmentSmsForm.getSendDate(), 2) : null);
          			} catch (ParseException e) {
          				e.printStackTrace();
          			}
                    subInstallmentSms.setParentInstallmentSms(installmentSms);
                    communityInstallmentSmsDao.save(subInstallmentSms);
            	}
            }
            
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse modifyCommunityInstallmentSms(CommunityInstallmentSmsReq req) {
		GenericResponse res = new GenericResponse();
		if (null!=req.getInstallmentSmsId() ) {
			CommunityInstallmentSmsEntity installmentSms = communityInstallmentSmsDao.get(req.getInstallmentSmsId()) ;
			if(null != installmentSms){
				installmentSms.setAmount(StringUtils.isNotEmpty(req.getAmount()) ? new BigDecimal(req.getAmount())
						: installmentSms.getAmount());
				installmentSms.setInstallmentName(StringUtils.isNotEmpty(req.getInstallmentName()) ? 
						req.getInstallmentName() :installmentSms.getInstallmentName());
				//installmentSms.setPayStatus(0);
				installmentSms.setPeriods(null!=req.getPeriods() ? req.getPeriods() : installmentSms.getPeriods());
				//installmentSms.setSendStatus(0);
				installmentSms.setUseStatus(null!=req.getUseStatus()?req.getUseStatus():installmentSms.getUseStatus());

	            installmentSms.setLastModifyTime(new Timestamp(new Date().getTime()));
	            if(StringUtils.isNotEmpty(req.getUserIdList())) {
	    			String[] userIds = req.getUserIdList().split(",");
	    			installmentSms.getUserList().clear();
	    			for (String userId : userIds) {
	    				UserEntity user = userDao.get(Integer.parseInt(userId));
	    				if (null != user) {
	    					installmentSms.getUserList().add(user);
	    				}
	    			}
	            }
	            try {
	            	installmentSms.setSendDate(StringUtils.isNotEmpty(req.getSendDate()) ? DateUtil.parse(req.getSendDate(), 2) : 
	            		installmentSms.getSendDate());
	  			} catch (ParseException e) {
	  				e.printStackTrace();
	  			}

	            if(null!=req.getSubInstallmentSmsList()) {
	            	boolean newSatus = true;
	            	for(CommunityInstallmentSmsForm installmentSmsForm : req.getSubInstallmentSmsList()) {
	            		CommunityInstallmentSmsEntity subInstallmentSms = null;
	            		if(null != installmentSmsForm.getInstallmentSmsId()) {
	            			subInstallmentSms = communityInstallmentSmsDao.get(installmentSmsForm.getInstallmentSmsId()) ;
	                		subInstallmentSms.setAmount(StringUtils.isNotEmpty(installmentSmsForm.getAmount()) ? 
	                				new BigDecimal(installmentSmsForm.getAmount())
		        					: subInstallmentSms.getAmount());
		            		subInstallmentSms.setInstallmentName(StringUtils.isNotEmpty(installmentSmsForm.getInstallmentName()) ? 
		            				installmentSmsForm.getInstallmentName() :subInstallmentSms.getInstallmentName());
		            		subInstallmentSms.setPeriods(installmentSmsForm.getPeriods());
		            		subInstallmentSms.setLastModifyTime(new Timestamp(new Date().getTime()));
		                    try {
		                    	subInstallmentSms.setSendDate(StringUtils.isNotEmpty(installmentSmsForm.getSendDate()) ? 
		                    			DateUtil.parse(installmentSmsForm.getSendDate(), 2) : subInstallmentSms.getSendDate());
		          			} catch (ParseException e) {
		          				e.printStackTrace();
		          			}
	            		}else {
	            			if(newSatus) {
	            				newSatus=false;
	            				Iterator<CommunityInstallmentSmsEntity> it =  installmentSms.getSubInstallmentSmsList().iterator();;
	            		        while(it.hasNext()){
	            		        	CommunityInstallmentSmsEntity i = it.next();
	            					i.setParentInstallmentSms(null);
	            					i.setUserList(null);
	            					i.setEstate(null);
	            					communityInstallmentSmsDao.deleteById(i.getId());
	            		        }
	            		        installmentSms.getSubInstallmentSmsList().clear();
	            			}
	            			subInstallmentSms = new CommunityInstallmentSmsEntity();
	                		subInstallmentSms.setAmount(StringUtils.isNotEmpty(installmentSmsForm.getAmount()) ? new BigDecimal(installmentSmsForm.getAmount())
		        					: new BigDecimal("0"));
		            		subInstallmentSms.setInstallmentName(StringUtils.isNotEmpty(installmentSmsForm.getInstallmentName()) ? installmentSmsForm.getInstallmentName() :"");
		            		subInstallmentSms.setPayStatus(0);
		            		subInstallmentSms.setPeriods(installmentSmsForm.getPeriods());
		            		subInstallmentSms.setSendStatus(0);
		            		subInstallmentSms.setUseStatus(req.getUseStatus());

		            		subInstallmentSms.setState(EntityContext.RECORD_STATE_VALID);
		            		subInstallmentSms.setLastModifyTime(new Timestamp(new Date().getTime()));
		                    try {
		                    	subInstallmentSms.setSendDate(StringUtils.isNotEmpty(installmentSmsForm.getSendDate()) ? DateUtil.parse(installmentSmsForm.getSendDate(), 2) : null);
		          			} catch (ParseException e) {
		          				e.printStackTrace();
		          			}
		                    subInstallmentSms.setEstate(installmentSms.getEstate());
		                    subInstallmentSms.setParentInstallmentSms(installmentSms);
		                    communityInstallmentSmsDao.save(subInstallmentSms);
	            		}
	
	            	}
	            }
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteCommunityInstallmentSms(CommunityInstallmentSmsReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getInstallmentSmsId()) {
			CommunityInstallmentSmsEntity installmentSms = communityInstallmentSmsDao.get(req.getInstallmentSmsId());
			if (null != installmentSms) {
				for(CommunityInstallmentSmsEntity subInstallmentSms : installmentSms.getSubInstallmentSmsList()) {
					subInstallmentSms.setState(EntityContext.RECORD_STATE_INVALID);
				}
				installmentSms.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityInstallmentSmsInfo(CommunityInstallmentSmsReq req) {
		GetCommunityInstallmentSmsInfoRes res = new GetCommunityInstallmentSmsInfoRes();
		if (null != req.getInstallmentSmsId()) {
			CommunityInstallmentSmsEntity installmentSms = communityInstallmentSmsDao.get(req.getInstallmentSmsId());
			if (null != installmentSms) {
				res.setInstallmentSms(getCommunityInstallmentSmsForm(installmentSms));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	@Override
	public IResponse setInstallmentSmsStatus(CommunityInstallmentSmsReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getInstallmentSmsId()) {
			CommunityInstallmentSmsEntity installmentSms = communityInstallmentSmsDao.get(req.getInstallmentSmsId());
			if (null != installmentSms) {
				installmentSms.setPayStatus(null!=req.getPayStatus() ? req.getPayStatus() : installmentSms.getPayStatus());
				installmentSms.setUseStatus(null!=req.getUseStatus() ? req.getUseStatus() : installmentSms.getUseStatus());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	
	public void taskOfDay() {
		StringBuilder hql = new StringBuilder("select distinct a from CommunityInstallmentSmsEntity a   "
				+ " where a.parentInstallmentSms is not null and a.sendDate='"+DateUtil.format(new Date(), 0)+"'");
		List<CommunityInstallmentSmsEntity> list = communityInstallmentSmsDao.getListByHql(hql.toString(), "");
		if(null!=list) {
			for(CommunityInstallmentSmsEntity installmentSms : list) {
				
			}
		}
	}
	
	@Override
	public IResponse sendSmsAgain(CommunityInstallmentSmsReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getInstallmentSmsId()) {
			CommunityInstallmentSmsEntity installmentSms = communityInstallmentSmsDao.get(req.getInstallmentSmsId());
			if (null != installmentSms && null!=installmentSms.getParentInstallmentSms()) {

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	public IResponse getStewardList(CommunityInstallmentSmsReq req) {
		GetUserListRes res = new GetUserListRes();
		StringBuilder hql = new StringBuilder("select distinct a.* from t_user a   ");
		hql.append(null!=req.getSelected()&&req.getSelected()==1 ? " inner join t_community_installment_sms_user b on b.userId=a.id  "
				+ "where b.installmentSmsId="+req.getInstallmentSmsId()+" and  a.userState ='1'":" where a.userState ='1'")
		.append(StringUtils.isNotEmpty(req.getUserName())?" and a.name like'%"+req.getUserName()+"%'":"")
		.append(StringUtils.isNotEmpty(req.getUserPhone())? " and a.phone like'%"+req.getUserPhone()+"%'":"");
		List<UserEntity> list = userDao.getListBySql(hql.toString(), "");
		if(null!=list) {
			for(UserEntity p : list){
				UserForm user = new UserForm();
				user.setUserId(p.getId());
				user.setName(StringUtils.isNoneEmpty(p.getName()) ? p.getName():"");
				user.setPhone(StringUtils.isNoneEmpty(p.getPhone()) ? p.getPhone():"");
				res.getUsers().add(user);
			}
		}
		
		
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
}
