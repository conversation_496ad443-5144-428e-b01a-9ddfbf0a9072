package com.foshan.service.community;

import java.text.ParseException;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityMeterAttributesReq;
import com.foshan.form.community.request.CommunityMeterRecordReq;
import com.foshan.form.community.request.CommunityReceiptReq;
import com.foshan.form.community.request.ExportExcelReq;
import com.foshan.form.response.IResponse;

public interface IExportExcelService {
	public byte[] exportMonthData(ExportExcelReq req,HttpServletResponse response);
	public IResponse exportMonthArrearage(ExportExcelReq req,HttpServletResponse response)throws ParseException;
	public IResponse exportMonthUnsoldArrearage(ExportExcelReq req,HttpServletResponse response) throws ParseException;
	public IResponse collectionOfMonthManagementFees(ExportExcelReq req,HttpServletResponse response);
	public IResponse exportOutstandingReceivable(ExportExcelReq req,HttpServletResponse response);
	public IResponse exportExcelBySql(ExportExcelReq req,HttpServletResponse response);
	public IResponse exportCommunityMeterProperty(ExportExcelReq req,HttpServletResponse response) throws ParseException;
	public IResponse exportCommunityMeterRecord(CommunityMeterRecordReq req,HttpServletResponse response)throws ParseException;
	public IResponse exportCommunityMeterAttributes(CommunityMeterAttributesReq req,HttpServletResponse response)throws ParseException;
	public IResponse exportCommunitypayItems(HttpServletResponse response)throws ParseException;
	public IResponse exportAccountInfo(HttpServletResponse response) throws ParseException;
	public IResponse exportCalTotalAllocation(ExportExcelReq req,HttpServletResponse response) throws ParseException;
	public IResponse exportCalItemAllocation(ExportExcelReq req,HttpServletResponse response) throws ParseException;
	public IResponse exportReceipt(CommunityReceiptReq req,HttpServletResponse response) throws ParseException;
	public IResponse exportMeterAllocationItemList(ExportExcelReq req, HttpServletResponse response);
	public IResponse getReceiptView(CommunityReceiptReq req);
	
}
