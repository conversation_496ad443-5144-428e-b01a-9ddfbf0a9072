package com.foshan.service.community;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityBankDepositBatchReq;
import com.foshan.form.response.IResponse;

public interface ICommunityBankDepositBatchService {
    public IResponse getCommunityBankDepositBatchList(CommunityBankDepositBatchReq req);
	public IResponse addCommunityBankDepositBatch(CommunityBankDepositBatchReq req);
	public IResponse modifyCommunityBankDepositBatch(CommunityBankDepositBatchReq req);
	public IResponse deleteCommunityBankDepositBatch(CommunityBankDepositBatchReq req);
	public IResponse getCommunityBankDepositBatchInfo(CommunityBankDepositBatchReq req);
	public IResponse addBankDepositEstatePayItems(CommunityBankDepositBatchReq req);
	public IResponse deleteBankDepositEstatePayItems(CommunityBankDepositBatchReq req);
	public IResponse  getBankDepositEstatePayItemsList(CommunityBankDepositBatchReq req);
	public IResponse modifyBankDepositEstatePayItems(CommunityBankDepositBatchReq req);
	public IResponse  exportBankDepositEstatePayItems(CommunityBankDepositBatchReq req,HttpServletResponse response) ;
	public IResponse  setBatchState(CommunityBankDepositBatchReq req);
	public IResponse  rollbackBatchState(CommunityBankDepositBatchReq req);
}

