package com.foshan.service.community;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityKingdeeReq;
import com.foshan.form.community.request.CommunityStatisticsReq;
import com.foshan.form.community.request.ReceiptStatisticsReq;
import com.foshan.form.community.request.ReceivablesChangesReq;
import com.foshan.form.community.request.RefundReq;
import com.foshan.form.response.IResponse;

public interface ICommunityStatisticsService {
        public IResponse getBaseStatistics();
        public IResponse getEventStatistics();
        public IResponse getReceivablesStatistics(CommunityStatisticsReq req);
        public IResponse getCommunityReceivables(CommunityStatisticsReq req);
        public IResponse getNoReceivables(CommunityStatisticsReq req);
        public IResponse getMemberProperty(CommunityStatisticsReq req);
        public IResponse getReceipt(ReceiptStatisticsReq req);
        public IResponse getCommunityReceivablesChanges(ReceivablesChangesReq req);
        public IResponse getRefund(RefundReq req);
        public IResponse getBuildingOccupancy(CommunityStatisticsReq req);
        public IResponse getReceiptDjbFileList();
        public void exportCommunityReceivablesExcel(HttpServletResponse response);
        public void exportNoReceivablesExcel(HttpServletResponse response);
        public void exportNoReceivablesMeasuresExcel(CommunityStatisticsReq req,HttpServletResponse response);
        public void exportNoReceivablesSummaryExcel(CommunityStatisticsReq req,HttpServletResponse response);
        public void exportMemberPropertyExcel(HttpServletResponse response);
        public void exportReceiptExcel(HttpServletResponse response);
        public void exportCommunityReceivablesChangesExcel(HttpServletResponse response);
        public void exportRefundExcel(HttpServletResponse response); 
        public void exportEverydayReceiptExcel(CommunityStatisticsReq req,HttpServletResponse response); 
        public void exportBuildingOccupancyExcel(HttpServletResponse response);
        public void exportReceiptDjbExcel(CommunityStatisticsReq req,HttpServletResponse response);
        public void downloadReceiptDjbFile(CommunityStatisticsReq req,HttpServletResponse response);


}
