package com.foshan.service.community.impl;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.foshan.entity.community.CommunityBuilderEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityBuilderService;
import com.hazelcast.spring.cache.HazelcastCacheManager;

import org.springframework.transaction.annotation.Transactional;
import com.foshan.form.community.request.CommunityBuilderReq;
import com.foshan.form.community.response.communityBuilder.AddCommunityBuilderRes;
import com.foshan.form.community.response.communityBuilder.ModifyCommunityBuilderRes;
import com.foshan.form.community.response.communityBuilder.GetCommunityBuilderInfoRes;
import com.foshan.form.community.response.communityBuilder.GetCommunityBuilderListRes;
import com.foshan.form.RegionForm;
import com.foshan.form.community.CommunityBuilderForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.IResponse;
import com.foshan.dao.generic.Page;

@Transactional
@Service("communityBuilderService")
public class CommunityBuilderServiceImpl extends GenericCommunityService implements ICommunityBuilderService{
	@Resource
	private HazelcastCacheManager cacheManager;

	@Override
	public IResponse getCommunityBuilderList(CommunityBuilderReq req) {
		GetCommunityBuilderListRes res = new GetCommunityBuilderListRes();
		Page<CommunityBuilderEntity> page = new Page<CommunityBuilderEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityBuilderEntity a where 1=1 ");
		hql.append(StringUtils.isNotEmpty(req.getBuilderName()) ? " and a.builderName like'%"+req.getBuilderName()+"%'":"");
		hql.append(" ORDER BY a.id desc");
		page = communityBuilderDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		page.getResultList().forEach(o -> {
			CommunityBuilderForm communityBuilderForm = new CommunityBuilderForm();
			communityBuilderForm.setCommunityBuilderId(o.getId());
            communityBuilderForm.setBuilderName(o.getBuilderName());
            communityBuilderForm.setCreateTime(sdf.format(o.getCreateTime()));
            communityBuilderForm.setLastModifyTime(sdf.format(o.getLastModifyTime()));
            communityBuilderForm.setState(o.getState());
            communityBuilderForm.setBuilderAddress(o.getBuilderAddress());
            communityBuilderForm.setLegalRepresentative(o.getLegalRepresentative());
            
            if(null != o.getRegion()) {
            	RegionForm regionForm = new RegionForm(o.getRegion().getId(),o.getRegion().getRegionCode() ,o.getRegion().getRegionName() 
            			, o.getRegion().getStartRegionCode(), o.getRegion().getEndRegionCode());
            	communityBuilderForm.setRegionForm(regionForm);
            }
			res.getCommunityBuilderList().add(communityBuilderForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "新增开发商")
	public IResponse addCommunityBuilder(CommunityBuilderReq req) {
		AddCommunityBuilderRes res = new AddCommunityBuilderRes();
		//if () {
			CommunityBuilderEntity communityBuilder = new CommunityBuilderEntity();
			
			communityBuilder.setBuilderName(StringUtils.isNotEmpty(req.getBuilderName()) ? req.getBuilderName() : "");
			communityBuilder.setBuilderAddress(StringUtils.isNotEmpty(req.getBuilderAddress()) ? req.getBuilderAddress() : "");
			communityBuilder.setLegalRepresentative(StringUtils.isNotEmpty(req.getLegalRepresentative()) ? req.getLegalRepresentative() : "");
			communityBuilder.setLastModifyTime(new Timestamp(new Date().getTime()));
			communityBuilder.setState(EntityContext.RECORD_STATE_VALID);
			communityBuilder.setRegion(regionDao.get(req.getRegionId()));
			communityBuilderDao.save(communityBuilder);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Override
	@Audit(operate = "修改开发商")
	public IResponse modifyCommunityBuilder(CommunityBuilderReq req) {
		ModifyCommunityBuilderRes res = new ModifyCommunityBuilderRes();
		if (null!=req.getCommunityBuilderId() ) {
			CommunityBuilderEntity communityBuilder = communityBuilderDao.get(req.getCommunityBuilderId()) ;
			if(null != communityBuilder){
				communityBuilder.setBuilderName(StringUtils.isNotEmpty(req.getBuilderName()) ? req.getBuilderName() : communityBuilder.getBuilderName());
				communityBuilder.setLastModifyTime(new Timestamp(new Date().getTime()));
				communityBuilder.setBuilderAddress(StringUtils.isNotEmpty(req.getBuilderAddress()) ? req.getBuilderAddress() : "");
				communityBuilder.setLegalRepresentative(StringUtils.isNotEmpty(req.getLegalRepresentative()) ? req.getLegalRepresentative() : "");
				communityBuilder.setState(null!=req.getState() ? req.getState() : communityBuilder.getState());
				communityBuilder.setRegion(regionDao.get(req.getRegionId()));
				res.setCommunityBuilderId(communityBuilder.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除开发商")
	public IResponse deleteCommunityBuilder(CommunityBuilderReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityBuilderId()) {
		CommunityBuilderEntity communityBuilder = communityBuilderDao.get(req.getCommunityBuilderId());
			if (null != communityBuilder) {
				communityBuilderDao.deleteById(req.getCommunityBuilderId());
				//communityBuilder.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityBuilderInfo(CommunityBuilderReq req) {
		GetCommunityBuilderInfoRes res = new GetCommunityBuilderInfoRes();
		if (null != req.getCommunityBuilderId()) {
			CommunityBuilderEntity communityBuilder = communityBuilderDao.get(req.getCommunityBuilderId());
			if (null != communityBuilder) {
				CommunityBuilderForm communityBuilderForm = new CommunityBuilderForm();
				DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
				communityBuilderForm.setCommunityBuilderId(communityBuilder.getId());
                communityBuilderForm.setBuilderName(communityBuilder.getBuilderName());
                communityBuilderForm.setCreateTime(sdf.format(communityBuilder.getCreateTime()));
                communityBuilderForm.setLastModifyTime(sdf.format(communityBuilder.getLastModifyTime()));
                communityBuilderForm.setState(communityBuilder.getState());
                communityBuilderForm.setBuilderAddress(communityBuilder.getBuilderAddress());
                communityBuilderForm.setLegalRepresentative(communityBuilder.getLegalRepresentative());
	            if(null != communityBuilder.getRegion()) {
	            	RegionForm regionForm = new RegionForm(communityBuilder.getRegion().getId(),communityBuilder.getRegion().getRegionCode() ,
	            			communityBuilder.getRegion().getRegionName() , communityBuilder.getRegion().getStartRegionCode(), communityBuilder.getRegion().getEndRegionCode());
	            	communityBuilderForm.setRegionForm(regionForm);
	            }
	            res.setCommunityBuilderForm(communityBuilderForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}