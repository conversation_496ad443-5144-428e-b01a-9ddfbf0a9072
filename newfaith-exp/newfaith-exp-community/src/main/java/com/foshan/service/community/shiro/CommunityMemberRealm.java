package com.foshan.service.community.shiro;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;

import com.foshan.dao.community.ICommunityMemberDao;
import com.foshan.entity.PermissionEntity;
import com.foshan.entity.RoleEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityRoleEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.service.community.shiro.realmhandler.AbstractMemberRealmHandler;
import com.foshan.service.community.shiro.realmhandler.RealmHandlerContext;



/**
 * 党员realm，处理用户的身份校验和鉴权。
 * 身份校验通过不同的登陆类型获取处理器实例来进行身份校验信息获取。
 * 
 * <AUTHOR>
 *
 */
public class CommunityMemberRealm extends AuthorizingRealm {

	@Resource(name = "communityMemberDao")
	private ICommunityMemberDao communityMemberDao;
	
	@Resource(name = "realmHandlerContext")
	private RealmHandlerContext realmHandlerContext;

	@Override
	protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
		Object memberObj = principals.asList().get(1);
		if (memberObj instanceof CommunityMemberEntity) {
			//String phone = (String) principals.fromRealm(getName()).iterator().next();
			String phone = ((CommunityMemberEntity)memberObj).getPhone();
			String openid = ((CommunityMemberEntity)memberObj).getWeixinOpenId();
			String hql = "from CommunityMemberEntity where userState = " + EntityContext.RECORD_STATE_VALID + " and (phone = '" + phone + "' or weixinOpenId = '" + openid + "')";
			CommunityMemberEntity member = communityMemberDao.getUniqueByHql(hql);
			List<RoleEntity> roles = null != member ? member.getCommunityRoleList() : null;
			Set<String> permissions = new HashSet<String>();
			
			if(null != roles && roles.size()>0){
				roles.forEach(r->{
					List<PermissionEntity> perms = r.getPermissionList();
					if(perms.size()>0){
						perms.forEach(p->{
							permissions.add(p.getPermissionName());
						});
					}
				});
			}
			SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
			if(permissions.size()>0){
				permissions.forEach(o->{
					info.addStringPermission(o);
				});
			}
			return info;
		}
		return null;
	}

	@Override
	protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
		PhoneToken pt = null;
		if (token instanceof PhoneToken) {
			pt = (PhoneToken) token;
		} else {
			return null;
		}
		
		AbstractMemberRealmHandler memberRealmHandler = realmHandlerContext.getInstance(pt.getType());
		return memberRealmHandler.handleAuthenticationInfo(token, getName());
		
	}

	@Override
	public boolean supports(AuthenticationToken token) {
		// TODO Auto-generated method stub
		return token != null && token instanceof PhoneToken;
	}
}
