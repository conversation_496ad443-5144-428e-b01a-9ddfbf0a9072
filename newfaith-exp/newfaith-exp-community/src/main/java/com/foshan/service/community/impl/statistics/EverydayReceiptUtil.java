package com.foshan.service.community.impl.statistics;

import static java.util.stream.Collectors.groupingBy;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.foshan.form.community.statistics.EverydayReceiptVo;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EverydayReceiptUtil {

	public static String getEverdayReceiptSql(String startDate) {
		StringBuilder data = new StringBuilder(
				"SELECT aa.paymentmethod,CONCAT('SELECT aa.districtcode,aa.unitcode,aa.receiptcode,aa.buildingorder,',")
				.append("GROUP_CONCAT(DISTINCT CONCAT('MAX(CASE aa.payitemsname WHEN ''',aa.payitemsname,''' then aa.amount else 0 end) as ',aa.payitemsname)),")
				.append("' from v_everyday aa where',' aa.paymentmethod=''',aa.paymentmethod,''' and aa.receiptdate=''','"
						+ startDate + "',''' group by aa.receiptcode') AS subSql ")
				.append("FROM v_everyday aa WHERE aa.receiptdate='" + startDate + "' ")
				.append("GROUP BY aa.paymentmethod");
		return data.toString();
	}

	public static void transEverydayReceiptExcel(List<EverydayReceiptVo> voList, String startDate,
			HttpServletResponse response) {
		XSSFWorkbook wb = new XSSFWorkbook();
		XSSFFormulaEvaluator eva = new XSSFFormulaEvaluator(wb);
		Map<String, XSSFCellStyle> cellStyle = ExcelUtil.getExcelStyle(wb);
		int feeMapSize = 0;

		// 按收款方式对收款列表进行分组
		Map<String, List<EverydayReceiptVo>> paymentMethodMap = (HashMap<String, List<EverydayReceiptVo>>) voList
				.stream().collect(groupingBy(EverydayReceiptVo::getPaymentMethod));

		// 对每种收款方式列表数据进行处理
		for (String paymentMethod : paymentMethodMap.keySet()) {

			// 根据收款方式创建excel表格sheet，每一种收款方式一个sheet
			Sheet sheet = wb.createSheet(paymentMethod);

			// 获取该收款方式下的收款列表
			List<EverydayReceiptVo> paymentVoList = paymentMethodMap.get(paymentMethod);

			// 将收款列表根据楼盘编号进行二次分组
			Map<String, List<EverydayReceiptVo>> unitMap = (HashMap<String, List<EverydayReceiptVo>>) paymentVoList
					.stream().collect(groupingBy(EverydayReceiptVo::getDistrictCode));

			/*
			 * 在当前sheet创建表头格式固定列数据
			 */
			// 创建第一行表头
			Row titleRow1 = sheet.createRow(0);
			titleRow1.setHeight((short)(40*20));
			Cell rt1 = titleRow1.createCell(0, CellType.STRING);
			rt1.setCellValue("");
			rt1.setCellStyle(cellStyle.get("title2"));
			
			Cell rt2 = titleRow1.createCell(1, CellType.STRING);
			rt2.setCellValue(paymentMethod + "收款移交表");
			rt2.setCellStyle(cellStyle.get("title2"));

			// 创建第二行表头
			Row titleRow2 = sheet.createRow(1);
			titleRow2.setHeight((short)(30*20));
			Cell tcell1 = titleRow2.createCell(0, CellType.STRING);
			tcell1.setCellValue("单元编号");
			tcell1.setCellStyle(cellStyle.get("title"));
			Cell tcell2 = titleRow2.createCell(1, CellType.STRING);
			tcell2.setCellValue("单据编号");
			tcell2.setCellStyle(cellStyle.get("title"));

			// 开始根据列表数据进行报表数据表体创建
			int rowNumber = 2;
			for (String districtCode : unitMap.keySet()) {
				// 获取每楼盘下单元收款列表并按单元编号进行排序
				List<EverydayReceiptVo> unitList = unitMap.get(districtCode).stream()
						.sorted(Comparator.comparing(EverydayReceiptVo::getNewUnitCode)).collect(Collectors.toList());
				// 循环处理当前楼盘下单元收款数据
				for (EverydayReceiptVo vo : unitList) {
					Row dataRow = sheet.createRow(rowNumber);
					dataRow.setHeight((short)(25*20));
					// 单元编号
					Cell cell1 = dataRow.createCell(0, CellType.STRING);
					cell1.setCellStyle(cellStyle.get("cell_left"));
					cell1.setCellValue(vo.getUnitCode());

					// 单据编号
					Cell cell2 = dataRow.createCell(1, CellType.STRING);
					cell2.setCellStyle(cellStyle.get("cell_left"));
					cell2.setCellValue(vo.getReceiptCode());

					// 获取单元收款数据动态收费项目列表
					Map<String, String> feeMap = vo.getFeeMap();
					feeMapSize = feeMap.size();
					int payitemColumn = 2;
					for (String payitemsname : feeMap.keySet()) {
						Cell tcell = null == titleRow2.getCell(payitemColumn)
								? titleRow2.createCell(payitemColumn, CellType.STRING)
								: titleRow2.getCell(payitemColumn);
						tcell.setCellStyle(cellStyle.get("title"));
						tcell.setCellValue(payitemsname);

						Cell dcell = dataRow.createCell(payitemColumn, CellType.NUMERIC);
						dcell.setCellStyle(cellStyle.get("cell_right2"));
						dcell.setCellValue(new BigDecimal(feeMap.get(payitemsname)).doubleValue());
						Double cellValue = dcell.getNumericCellValue();
						if (null == cellValue || cellValue == 0) {
							dcell.setBlank();
						}
						payitemColumn++;
					}

					// 单元合计
					Cell unitTotal = dataRow.createCell(feeMapSize + 2, CellType.NUMERIC);
					unitTotal.setCellStyle(cellStyle.get("total_double"));
					unitTotal.setCellFormula("SUM(" + dataRow.getCell(2).getAddress() + ":"
							+ dataRow.getCell(feeMapSize + 1).getAddress() + ")");

					rowNumber++;
				}

				// 为每个楼盘收款列表增加小计行
				Row subTotalRow = sheet.createRow(rowNumber);
				subTotalRow.setHeight((short)(25*20));
				Cell xj = subTotalRow.createCell(0, CellType.STRING);
				xj.setCellStyle(cellStyle.get("total_double"));
				xj.setCellValue("小计");

				Cell xj1 = subTotalRow.createCell(1, CellType.STRING);
				xj1.setCellStyle(cellStyle.get("total_double"));
				xj1.setCellValue("");

				for (int j = 0; j < feeMapSize; j++) {
					Cell cell = subTotalRow.createCell(j + 2, CellType.NUMERIC);
					cell.setCellStyle(cellStyle.get("underLineTotal"));
					Cell startCell = sheet.getRow(rowNumber - unitList.size()).getCell(j + 2);
					Cell endCell = sheet.getRow(rowNumber - 1).getCell(j + 2);
					cell.setCellFormula("SUM(" + startCell.getAddress() + ":" + endCell.getAddress() + ")");
					if (eva.evaluate(cell).getNumberValue() == 0) {
						cell.setBlank();
					}
				}

				Cell xjTotal = subTotalRow.createCell(feeMapSize + 2, CellType.NUMERIC);
				xjTotal.setCellStyle(cellStyle.get("underLineTotal"));
				Cell startCell = subTotalRow.getCell(2);
				Cell endCell = subTotalRow.getCell(feeMapSize + 1);
				xjTotal.setCellFormula("SUM(" + startCell.getAddress() + ":" + endCell.getAddress() + ")");
				rowNumber++;
			}

			// 合并表头第一行单元格
			CellRangeAddress region1 = new CellRangeAddress(0, 0, 1, feeMapSize + 1);
			sheet.addMergedRegion(region1);
//			ExcelUtil.addMergeCellBorderNoBorder(region1, sheet);

			// 创建第一行表头末尾固定列
			Cell rt3 = titleRow1.createCell(feeMapSize + 2, CellType.STRING);
			rt3.setCellValue(startDate);
			rt3.setCellStyle(cellStyle.get("title2"));

			// 创建第一行表头末尾固定列
			Cell tcell3 = titleRow2.createCell(feeMapSize + 2, CellType.STRING);
			tcell3.setCellValue("合计");
			tcell3.setCellStyle(cellStyle.get("title"));

			// 增加最后合计行
			// 合计行
			Row totalRow = sheet.createRow(rowNumber);
			totalRow.setHeight((short)(25*20));
			Cell totalRow_1 = totalRow.createCell(0, CellType.STRING);
			totalRow_1.setCellStyle(cellStyle.get("total_double"));
			totalRow_1.setCellValue("合计");

			Cell totalRow_2 = totalRow.createCell(1, CellType.STRING);
			totalRow_2.setCellStyle(cellStyle.get("total_double"));
			totalRow_2.setCellValue("");

			for (int j = 0; j < feeMapSize + 1; j++) {
				Cell cell = totalRow.createCell(j + 2, CellType.NUMERIC);
				cell.setCellStyle(cellStyle.get("underLineTotal"));
				Cell startCell = sheet.getRow(2).getCell(j + 2);
				Cell endCell = sheet.getRow(rowNumber - 1).getCell(j + 2);
				cell.setCellFormula("SUM(" + startCell.getAddress() + ":" + endCell.getAddress() + ")/2");
			}

			// 签字行
			Row writeRow = sheet.createRow(rowNumber + 3);
			
			Cell write1 = writeRow.createCell(1, CellType.STRING);
			write1.setCellValue("移交人：_________");

			Cell write2 = writeRow.createCell(feeMapSize, CellType.STRING);
			write2.setCellValue("接收人：_________");
			
			
			
			sheet.autoSizeColumn(0);
			sheet.autoSizeColumn(1);
			sheet.setColumnWidth(totalRow.getLastCellNum()-1, 20*256);
			
		}

		String fileName = "每日收款移交表" + DateUtil.formatByStyle(new Date(), "_yyyy_MM_dd_HH_mm_ss") + ".xlsx";
		try {
			ExcelExportUtil.export(response, wb, fileName);
		} catch (IOException e) {
			wb = null;
			log.error(e.getMessage());
		}

	}

}
