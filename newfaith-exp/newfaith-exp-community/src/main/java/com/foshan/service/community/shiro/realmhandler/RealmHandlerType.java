package com.foshan.service.community.shiro.realmhandler;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * RealmHandler类型注解，通过该类型区分不同情况的登陆处理逻辑，实际逻辑处理器的实现所在包：com.foshan.service.shop.shiro.realmhandler.handler
 * <AUTHOR>
 *
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface RealmHandlerType {
	String value();
}
