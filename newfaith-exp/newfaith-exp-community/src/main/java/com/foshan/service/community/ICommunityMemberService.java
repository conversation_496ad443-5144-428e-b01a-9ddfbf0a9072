package com.foshan.service.community;


import com.foshan.form.community.request.CommunityMemberReq;
import com.foshan.form.community.request.MergeMemberInfoReq;
import com.foshan.form.response.IResponse;
import com.foshan.service.annotation.Audit;

public interface ICommunityMemberService {
	public IResponse getCommunityMemberList(CommunityMemberReq req);
	public IResponse addCommunityMember(CommunityMemberReq req);
	public IResponse modifyCommunityMember(CommunityMemberReq req);
	public IResponse deleteCommunityMember(CommunityMemberReq req);
	public IResponse getCommunityMemberInfo(CommunityMemberReq req);
	public IResponse mergeCommunityMember(MergeMemberInfoReq req);

}
