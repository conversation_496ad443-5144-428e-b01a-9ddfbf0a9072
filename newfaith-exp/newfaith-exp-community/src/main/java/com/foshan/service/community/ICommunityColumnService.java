package com.foshan.service.community;

import com.foshan.form.community.request.CommunityColumnReq;
import com.foshan.form.response.IResponse;

public interface ICommunityColumnService {
    public IResponse getCommunityColumnList(CommunityColumnReq req);
    public IResponse addCommunityColumn(CommunityColumnReq req);
    public IResponse modifyCommunityColumn(CommunityColumnReq req);
    public IResponse deleteCommunityColumn(CommunityColumnReq req);
    public IResponse getCommunityColumnInfo(CommunityColumnReq req);
    public IResponse getServiceCommunityRootColumn(CommunityColumnReq req);
    public IResponse getCommunityColumnAssetList(CommunityColumnReq req);
}
