package com.foshan.service.community.impl;

import java.sql.Timestamp;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.DictionaryEntity;
import com.foshan.entity.community.CommunityDictionaryEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.DictionaryForm;
import com.foshan.form.community.request.CommunityDictionaryReq;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.dictionary.GetDictionaryInfoRes;
import com.foshan.form.response.dictionary.GetDictionaryListRes;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityDictionaryService;
import com.foshan.util.DateUtil;

@Transactional
@Service("communityDictionaryService")
public class CommunityDictionaryServiceImpl extends GenericCommunityService implements ICommunityDictionaryService{


	@Override
	@Audit(operate = "新增社区字典")
	public IResponse addCommunityDictionary(CommunityDictionaryReq req) {
		GenericResponse res = new GenericResponse();
		//if (null!=req.getPropertyId() && null!=req.getIsGeneratedBills()) {
			CommunityDictionaryEntity dictionary = new CommunityDictionaryEntity();
			dictionary.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment() : "");
			dictionary.setDirectoryCode(StringUtils.isNotEmpty(req.getDirectoryCode()) ? req.getDirectoryCode() : "");
			dictionary.setDirectoryName(StringUtils.isNotEmpty(req.getDirectoryName()) ? req.getDirectoryName() : "");
			dictionary.setLastModifyTime(new Timestamp(new Date().getTime()));	
			dictionary.setState(EntityContext.RECORD_STATE_VALID);
			communityDictionaryDao.save(dictionary);

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		//} else {
		//	res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
		//	res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		//}
		return res;
	}

	@Override
	@Audit(operate = "删除社区字典")
	public IResponse deleteCommunityDictionary(CommunityDictionaryReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getDictionaryId()) {
			CommunityDictionaryEntity dictionary = communityDictionaryDao.get(req.getDictionaryId());
			if(null != dictionary){
				//dictionaryDao.delete(dictionary);
				dictionary.setState(EntityContext.RECORD_STATE_INVALID);
				
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "修改社区字典")
	public IResponse modifyCommunityDictionary(CommunityDictionaryReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getDictionaryId()) {
			CommunityDictionaryEntity dictionary = communityDictionaryDao.get(req.getDictionaryId());
			if(null != dictionary){
				dictionary.setComment(StringUtils.isNotEmpty(req.getComment()) ? 
						req.getComment() : dictionary.getComment());
				dictionary.setDirectoryCode(StringUtils.isNotEmpty(req.getDirectoryCode()) ? 
						req.getDirectoryCode() : dictionary.getDirectoryCode());
				dictionary.setDirectoryName(StringUtils.isNotEmpty(req.getDirectoryName()) ? 
						req.getDirectoryName() : dictionary.getDirectoryName());
				dictionary.setLastModifyTime(new Timestamp(new Date().getTime()));	
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse getCommunityDictionaryInfo(CommunityDictionaryReq req) {
		GetDictionaryInfoRes res = new GetDictionaryInfoRes();
		if (null != req.getDictionaryId()) {
			CommunityDictionaryEntity dictionary = communityDictionaryDao.get(req.getDictionaryId());
			if(null != dictionary){
				res.setDictionaryForm(getDictionaryForm(dictionary));
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	public DictionaryForm getDictionaryForm(DictionaryEntity dictionary) {
		DictionaryForm dictionaryForm = new DictionaryForm();
		dictionaryForm.setDictionaryId(dictionary.getId());
		dictionaryForm.setComment(dictionary.getComment());
		dictionaryForm.setDirectoryCode(dictionary.getDirectoryCode());
		dictionaryForm.setDirectoryName(dictionary.getDirectoryName());
		dictionaryForm.setCreateTime(null != dictionary.getCreateTime()? 
				DateUtil.formatLongFormat(dictionary.getCreateTime()) : "");
		dictionaryForm.setLastModifyTime(null != dictionary.getLastModifyTime() ? 
				DateUtil.formatLongFormat(dictionary.getLastModifyTime()) : "");
		dictionaryForm.setState(dictionary.getState());
		return dictionaryForm;
	}

	@Override
	public IResponse getCommunityDictionaryList(CommunityDictionaryReq req) {
		GetDictionaryListRes res = new GetDictionaryListRes();
		Page<CommunityDictionaryEntity> page = new Page<CommunityDictionaryEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityDictionaryEntity a where 1=1 ");
		hql.append(null!=req.getState() ? " and a.state="+req.getState() : " and a.state =1")
			.append(StringUtils.isNotEmpty(req.getDirectoryCode()) ? " and a.directoryCode like'%"+req.getDirectoryCode()+"%'":"")
			.append(StringUtils.isNotEmpty(req.getDirectoryName()) ? " and a.directoryName like'%"+req.getDirectoryName()+"%'":"");
	
		page = communityDictionaryDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			res.getDictionaryList().add(getDictionaryForm(o));
		});

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}


}
