package com.foshan.service.community;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityReceiptReq;
import com.foshan.form.response.IResponse;

public interface ICommunityReceiptService {
    public IResponse getCommunityReceiptList(CommunityReceiptReq req);
	public IResponse addCommunityReceipt(CommunityReceiptReq req);
	public IResponse modifyCommunityReceipt(CommunityReceiptReq req);
	public IResponse deleteCommunityReceipt(CommunityReceiptReq req);
	public IResponse getCommunityReceiptInfo(CommunityReceiptReq req);
	public IResponse createCommunityReceiptPdf(CommunityReceiptReq req);
	public IResponse addPrintNum(CommunityReceiptReq req);
	public IResponse exportCommunityReceiptList(CommunityReceiptReq req, HttpServletResponse response);
}

