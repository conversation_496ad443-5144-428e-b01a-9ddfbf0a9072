package com.foshan.service.community.impl;

import org.springframework.stereotype.Service;
import com.foshan.entity.community.CommunityDecorationItemsEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.service.community.ICommunityDecorationItemsService;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.form.community.request.CommunityDecorationItemsReq;
import com.foshan.form.community.response.communityDecorationItems.AddCommunityDecorationItemsRes;
import com.foshan.form.community.response.communityDecorationItems.ModifyCommunityDecorationItemsRes;
import com.foshan.form.community.response.communityDecorationItems.GetCommunityDecorationItemsInfoRes;
import com.foshan.form.community.response.communityDecorationItems.GetCommunityDecorationItemsListRes;
import com.foshan.form.community.CommunityDecorationItemsForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.IResponse;
import com.foshan.util.DateUtil;
import com.foshan.dao.generic.Page;
import org.apache.commons.lang3.StringUtils;
import java.sql.Timestamp;
import java.util.Date;

@Transactional
@Service("communityDecorationItemsService")
public class CommunityDecorationItemsServiceImpl extends GenericCommunityService implements ICommunityDecorationItemsService{

	@Override
	public IResponse getCommunityDecorationItemsList(CommunityDecorationItemsReq req) {
		GetCommunityDecorationItemsListRes res = new GetCommunityDecorationItemsListRes();
		Page<CommunityDecorationItemsEntity> page = new Page<CommunityDecorationItemsEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityDecorationItemsEntity a ");
		if((null!=req.getAssociatedAttachment()&&1==req.getAssociatedAttachment()) ||null!=req.getPropertyId()) {
			hql.append(" inner join a.itemsAttachmentList ia inner join ia.attachment b " );
			if(null!=req.getPropertyId()) {
				hql.append("inner join ia.district di inner join di.buildingList bu inner join bu.propertyList p ")
					.append(null!=req.getPropertyId() ? " where p.id="+req.getPropertyId() +" and p.estateType=ia.estateType ":"");
			}else {
				hql.append("  where ");
			}
			if(null!=req.getAssociatedAttachment()&&1==req.getAssociatedAttachment()) {
				hql.append(" b.id="+req.getCommunityDecorationAttachmentId());
			}
			
		}else {
			hql.append("  where a.state=1 ");
		}

		hql.append(null!=req.getAssociatedAttachment()&&0==req.getAssociatedAttachment() ? 
				" and a.id not in(select c.id from CommunityDecorationItemsEntity  c  inner join c.attachmentList d where d.id="
				+req.getCommunityDecorationAttachmentId()+")":"" )
			.append(StringUtils.isNotEmpty(req.getItemName()) ? " and a.itemName like'%"+req.getItemName()+"%'":"")
			.append(" ORDER BY a.id desc");
		page = communityDecorationItemsDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityDecorationItemsForm communityDecorationItemsForm = new CommunityDecorationItemsForm();
			communityDecorationItemsForm.setCommunityDecorationItemsId(o.getId());
            communityDecorationItemsForm.setCreateTime(null != o.getCreateTime()? DateUtil.formatLongFormat(o.getCreateTime()) : "");
            communityDecorationItemsForm.setLastModifyTime(null != o.getLastModifyTime()? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
            communityDecorationItemsForm.setState(o.getState());
            communityDecorationItemsForm.setBeginUse(o.getBeginUse());
            communityDecorationItemsForm.setItemName(o.getItemName());
            communityDecorationItemsForm.setOrders(o.getOrders());
			res.getCommunityDecorationItemsList().add(communityDecorationItemsForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	public IResponse addCommunityDecorationItems(CommunityDecorationItemsReq req) {
		AddCommunityDecorationItemsRes res = new AddCommunityDecorationItemsRes();
		//if () {
			CommunityDecorationItemsEntity communityDecorationItems = new CommunityDecorationItemsEntity();
			
            communityDecorationItems.setLastModifyTime(new Timestamp(new Date().getTime()));
            communityDecorationItems.setState(null!=req.getState() ? req.getState() : EntityContext.RECORD_STATE_VALID);
            communityDecorationItems.setBeginUse(null!=req.getBeginUse()?req.getBeginUse():0);
            communityDecorationItems.setItemName(StringUtils.isNotEmpty(req.getItemName()) ?req.getItemName():"");
            communityDecorationItems.setOrders(null!=req.getOrders() ? req.getOrders():0);
			communityDecorationItemsDao.save(communityDecorationItems);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}

	@Override
	public IResponse modifyCommunityDecorationItems(CommunityDecorationItemsReq req) {
		ModifyCommunityDecorationItemsRes res = new ModifyCommunityDecorationItemsRes();
		if (null!=req.getCommunityDecorationItemsId() ) {
			CommunityDecorationItemsEntity communityDecorationItems = communityDecorationItemsDao.get(req.getCommunityDecorationItemsId()) ;
			if(null != communityDecorationItems){
                communityDecorationItems.setLastModifyTime(new Timestamp(new Date().getTime()));
                //communityDecorationItems.setState(req.getState());
                communityDecorationItems.setBeginUse(null!=req.getBeginUse()?req.getBeginUse():communityDecorationItems.getBeginUse());
                communityDecorationItems.setItemName(StringUtils.isNotEmpty(req.getItemName()) ?req.getItemName():communityDecorationItems.getItemName());
                communityDecorationItems.setOrders(null!=req.getOrders() ? req.getOrders():communityDecorationItems.getOrders());
				res.setCommunityDecorationItemsId(communityDecorationItems.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse deleteCommunityDecorationItems(CommunityDecorationItemsReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityDecorationItemsId()) {
		CommunityDecorationItemsEntity communityDecorationItems = communityDecorationItemsDao.get(req.getCommunityDecorationItemsId());
			if (null != communityDecorationItems) {
				//communityDecorationItemsDao.deleteById(req.getCommunityDecorationItemsId());
				communityDecorationItems.setItemsAttachmentList(null);
				communityDecorationItems.setEventCategory(null);
				communityDecorationItems.setEventCategoryItemsList(null);
				communityDecorationItems.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityDecorationItemsInfo(CommunityDecorationItemsReq req) {
		GetCommunityDecorationItemsInfoRes res = new GetCommunityDecorationItemsInfoRes();
		if (null != req.getCommunityDecorationItemsId()) {
			CommunityDecorationItemsEntity communityDecorationItems = communityDecorationItemsDao.get(req.getCommunityDecorationItemsId());
			if (null != communityDecorationItems) {
				CommunityDecorationItemsForm communityDecorationItemsForm = new CommunityDecorationItemsForm();
				communityDecorationItemsForm.setCommunityDecorationItemsId(communityDecorationItems.getId());
                communityDecorationItemsForm.setCreateTime(null != communityDecorationItems.getCreateTime()? DateUtil.formatLongFormat(communityDecorationItems.getCreateTime()) : "");
                communityDecorationItemsForm.setLastModifyTime(null != communityDecorationItems.getLastModifyTime()? DateUtil.formatLongFormat(communityDecorationItems.getLastModifyTime()) : "");
                communityDecorationItemsForm.setState(communityDecorationItems.getState());
                communityDecorationItemsForm.setBeginUse(communityDecorationItems.getBeginUse());
                communityDecorationItemsForm.setItemName(communityDecorationItems.getItemName());
                communityDecorationItemsForm.setOrders(communityDecorationItems.getOrders());
				res.setCommunityDecorationItemsForm(communityDecorationItemsForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}