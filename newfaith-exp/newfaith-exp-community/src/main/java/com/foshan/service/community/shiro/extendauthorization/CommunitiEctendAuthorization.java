package com.foshan.service.community.shiro.extendauthorization;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.foshan.model.permssion.PrincipalModel;
import com.foshan.service.permission.IExtendAuthorization;

@Component
public class CommunitiEctendAuthorization implements IExtendAuthorization {

	@Override
	public List<String> addStringPermissions(PrincipalModel principal) {
		List<String> stringPermissions = new ArrayList<String>();
		stringPermissions.add("CommunityEventCategory:1");
		return stringPermissions;
	}

}
