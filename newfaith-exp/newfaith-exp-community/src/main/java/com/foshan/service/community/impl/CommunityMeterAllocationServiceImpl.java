package com.foshan.service.community.impl;

import static java.util.stream.Collectors.groupingBy;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jdbc.Work;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityMeterAllocationEntity;
import com.foshan.entity.community.CommunityMeterAllocationItemEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.vo.AllocationItemVo;
import com.foshan.entity.community.vo.AllocationVo;
import com.foshan.entity.community.vo.CarBillingDateVo;
import com.foshan.entity.community.vo.ExportAllocationVo;
import com.foshan.entity.community.vo.MeterRecordVo;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityMeterAllocationForm;
import com.foshan.form.community.CommunityMeterAllocationItemForm;
import com.foshan.form.community.CommunityReceivablesForm;
import com.foshan.form.community.request.CommunityMeterAllocationReq;
import com.foshan.form.community.response.communityMeterAllocation.ExportAllocationListRes;
import com.foshan.form.community.response.communityMeterAllocation.MeterAllocationRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityMeterAllocationService;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityCache;
import com.foshan.util.community.CommunityPage;
import com.foshan.util.community.JepUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional
@Service("communityMeterAllocationService")
public class CommunityMeterAllocationServiceImpl extends GenericCommunityService
		implements ICommunityMeterAllocationService {
	private static Map<Integer, CommunityMeterAllocationEntity> allocationCache = new HashMap<>();
	public static List<AllocationItemVo> allocationItemCache = new ArrayList<>();

	public static LocalDate allocationDate = LocalDate.now();

	@SuppressWarnings("unchecked")
	@Override
	@Audit(operate = "总表分摊试算")
	public IResponse calTotalAllocation(CommunityMeterAllocationReq req) {
		MeterAllocationRes res = new MeterAllocationRes();
		Map<Integer, MeterRecordVo> resultList = new TreeMap<>();

		if (StringUtils.isNotEmpty(req.getAllocationDate())) {
			allocationDate = LocalDate.parse(req.getAllocationDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		}

		StringBuilder recordSql = new StringBuilder("select meterId,meterCode,metername,allocationMethod,"
				+ "payItemsName,additionalAmount,additionalInstructions,expirationDates,meterAttributesId,"
				+ "formulaId,allocationPeriod,templeteId,rate,unitPrice,dynamicParameter,formulaName,"
				+ "templeteInfo,templeteName,lastNum,recordNum,allocationStartYear,allocationStartMonth,"
				+ "allocationEndYear,allocationEndMonth,estateCount,totalChargeArea,totalAddArea,"
				+ "allocationNum,allocationAmount,allocationId from v_meter_record a " + " where a.allocationEndyear="
				+ allocationDate.getYear() + " and a.allocationEndmonth=" + allocationDate.getMonthValue());

		if (CommunityCache.allocationList.size() == 0
				|| (null != req.getRefreshFlag() && req.getRefreshFlag().equals(1))) {
			CommunityCache.allocationList.clear();
			List<Object[]> recordList = communityMeterRecordDao.createSQLQuery(recordSql.toString()).list();
			for (Object[] temp : recordList) {
				MeterRecordVo o = MeterRecordVo.getMeterRecordVo(temp);
				if(null==o) {
					log.info("============>111111111111");
				}
				CommunityCache.allocationList.put(o.getMeterId(), o);
			}
		}

		if (req.getMeterIds().length > 0) {
			for (Integer meterId : req.getMeterIds()) {
				if (CommunityCache.allocationList.containsKey(meterId) && !CommunityCache.allocationList.get(meterId)
						.getAllocationNum().equals(new BigDecimal("0.00"))) {
					resultList.put(meterId, CommunityCache.allocationList.get(meterId));
				}
			}
		} else {
			for(Integer meterId:CommunityCache.allocationList.keySet()) {
				if(!CommunityCache.allocationList.get(meterId).getAllocationNum().equals(new BigDecimal("0.00"))) {
					resultList.put(meterId, CommunityCache.allocationList.get(meterId));
				}
			}
		}

		if (resultList.size() > 0) {

			clearMeterAllocation(allocationDate);

			CommunityPage<Map<Integer, MeterRecordVo>> page = CommunityCache.getPage(resultList, req.getPageSize(),
					req.getRequestPage());

			res.setCurrentPage(page.getCurrentPage());
			res.setPageSize(page.getPageSize());
			res.setTotal(page.getTotalPage());
			res.setTotalResult(page.getTotalCount());
			page.getResultList().keySet().forEach(o -> {
				MeterRecordVo vo = page.getResultList().get(o);

				if (!vo.getAllocationNum().equals(new BigDecimal("0.00"))) {

					CommunityMeterAllocationForm allocationForm = new CommunityMeterAllocationForm();
					BeanUtils.copyProperties(vo, allocationForm);
					allocationForm.setAdditionalAmount(vo.getAdditionalAmount().setScale(2, RoundingMode.HALF_UP));
					allocationForm.setUnitPrice(vo.getUnitPrice().setScale(8, RoundingMode.HALF_UP));
					allocationForm.setAllocationNum(vo.getAllocationNum().setScale(2, RoundingMode.HALF_UP));
					allocationForm.setAllocationAmount(vo.getAllocationAmount().setScale(4, RoundingMode.HALF_UP));
					if (StringUtils.isNotEmpty(vo.getDynamicParameter())) {
						try {
							Map<String, BigDecimal> paras = mapper.readValue(vo.getDynamicParameter(),
									new HashMap<String, BigDecimal>().getClass());
							paras.put("总表用量", vo.getAllocationNum());
							paras.put("单价", vo.getUnitPrice());
							allocationForm.setDynamicParameter(paras);
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
					}

					allocationForm.setAllocationDate(
							(LocalDate.of(vo.getAllocationStartYear(), vo.getAllocationStartMonth(), 15)
									.with(TemporalAdjusters.firstDayOfMonth()).toString()));
					allocationForm.setAllocationEndDate(
							(LocalDate.of(vo.getAllocationEndYear(), vo.getAllocationEndMonth(), 15)
									.with(TemporalAdjusters.lastDayOfMonth()).toString()));

					res.getAllocationList().add(allocationForm);
				} 
			});
		}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	@Audit(operate = "总表分摊结果提交")
	public IResponse submitTotalAllocation(CommunityMeterAllocationReq req) {
		GenericResponse res = new GenericResponse();

		Map<Integer, MeterRecordVo> resultList = new TreeMap<>();

		if (req.getMeterIds().length > 0) {
			for (Integer meterId : req.getMeterIds()) {
				if (CommunityCache.allocationList.containsKey(meterId)) {
					resultList.put(meterId, CommunityCache.allocationList.get(meterId));

				}
			}
		} else {
			resultList = CommunityCache.allocationList;
		}

		if (resultList.size() > 0) {

			try {
				clearMeterAllocation(allocationDate);
			} catch (Exception ex) {
				ex.printStackTrace();
			}
			for (Integer o : resultList.keySet()) {
				MeterRecordVo vo = CommunityCache.allocationList.get(o);
				CommunityMeterAllocationEntity allocation = new CommunityMeterAllocationEntity();
				BeanUtils.copyProperties(vo, allocation);
				allocation.setState(EntityContext.RECORD_STATE_VALID);
				allocation.setId(vo.getAllocationId());
				allocation.setAdditionalAmount(vo.getAdditionalAmount().setScale(2, RoundingMode.HALF_UP));
				allocation.setUnitPrice(vo.getUnitPrice().setScale(8, RoundingMode.HALF_UP));
				allocation.setAllocationNum(vo.getAllocationNum().setScale(2, RoundingMode.HALF_UP));
				allocation.setAllocationAmount(vo.getAllocationAmount().setScale(2, RoundingMode.HALF_UP));
				allocation.setMeter(CommunityCache.meterList.get(vo.getMeterId()));
				if (StringUtils.isNotEmpty(vo.getDynamicParameter())) {
					try {
						Map<String, BigDecimal> paras = mapper.readValue(vo.getDynamicParameter(),
								new HashMap<String, BigDecimal>().getClass());
						paras.put("总表用量", vo.getAllocationNum());
						paras.put("单价", vo.getUnitPrice());
						allocation.setDynamicParameter(mapper.writeValueAsString(paras));
						vo.setDynamicParameter(allocation.getDynamicParameter());
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
				allocation.setAllocationDate(
						Date.from((LocalDate.of(vo.getAllocationStartYear(), vo.getAllocationStartMonth(), 15)
								.with(TemporalAdjusters.firstDayOfMonth())).atStartOfDay(ZoneId.systemDefault())
								.toInstant()));
				allocation.setAllocationEndDate(
						Date.from((LocalDate.of(vo.getAllocationEndYear(), vo.getAllocationEndMonth(), 15)
								.with(TemporalAdjusters.lastDayOfMonth())).atStartOfDay(ZoneId.systemDefault())
								.toInstant()));
				allocation.setAllocationYear(vo.getAllocationEndYear());
				allocation.setAllocationMonth(vo.getAllocationEndMonth());

				if (!allocation.getAllocationNum().equals(new BigDecimal("0.00"))) {
					communityMeterAllocationDao.save(allocation);
					vo.setAllocationId(allocation.getId());
					allocationCache.put(allocation.getId(), allocation);
				} else {
					//CommunityMeterEntity meter = allocation.getMeter();
					CommunityMeterEntity meter = communityMeterDao.get(vo.getMeterId());
					meter.setLastAllocateDate(Date.from((allocationDate.with(TemporalAdjusters.lastDayOfMonth()))
							.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					communityMeterDao.update(meter);
					log.info("============>" + meter.getId() + ":::" + meter.getMeterName());
				}
			}

			// 处理本月分摊抄表数据
			clearMeterRecord(req);
			transMeterRecord(req);

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet("0001");
			res.setRetInfo("请先进行分摊费用预计算！！！");
		}

		return res;
	}

	@Override
	public IResponse getTotalAllocationList(CommunityMeterAllocationReq req) {
		MeterAllocationRes res = new MeterAllocationRes();
		if (StringUtils.isNotEmpty(req.getAllocationDate())) {

			Page<CommunityMeterAllocationEntity> page = new Page<CommunityMeterAllocationEntity>();
			page.setPageSize(req.getPageSize());
			page.setBeginCount((req.getRequestPage() - 1) * req.getPageSize());
			page.setCurrentPage(req.getRequestPage());

			LocalDate allocationDate = LocalDate.parse(req.getAllocationDate(),
					DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			StringBuilder sql = new StringBuilder("from CommunityMeterAllocationEntity a where a.allocationYear="
					+ allocationDate.getYear() + " and a.allocationMonth=" + allocationDate.getMonthValue())
					.append(null != req.getMeterIds() && req.getMeterIds().length > 0
							? " and  a.meter.id in(" + StringUtils.join(req.getMeterIds(), ",") + ")"
							: "");

			page = communityMeterAllocationDao.queryPage(page, sql.toString());
			res.setPageSize(req.getPageSize());
			res.setCurrentPage(req.getRequestPage());
			res.setTotal(page.getTotalPage());
			res.setTotalResult(page.getTotalCount());
			page.getResultList().forEach(o -> {
				CommunityMeterAllocationForm form = new CommunityMeterAllocationForm();
				form.setAdditionalAmount(o.getAdditionalAmount());
				form.setAllocationAmount(o.getAllocationAmount());
				form.setAllocationDate(DateUtil.formatShortFormat(o.getAllocationDate()));
				form.setAllocationEndDate(DateUtil.formatShortFormat(o.getAllocationEndDate()));
				form.setAllocationId(o.getId());
				form.setAllocationNum(o.getAllocationNum());
				CommunityMeterEntity meter = o.getMeter();
				if (null != meter) {
					form.setMeterCode(meter.getMeterCode());
					form.setMeterId(meter.getId());
					form.setMeterName(meter.getMeterName());
					form.setPayItemsName(meter.getPayItemsName());
					form.setTempleteId(o.getTempleteId());
					form.setUnitPrice(o.getUnitPrice());
				}
				res.getAllocationList().add(form);
			});
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getMeterAllocationItemList(CommunityMeterAllocationReq req) {
		MeterAllocationRes res = new MeterAllocationRes();
		if (null != req.getMeterIds() && req.getMeterIds().length >= 1
				&& StringUtils.isNotEmpty(req.getAllocationDate())) {

			Page<CommunityMeterAllocationItemEntity> page = new Page<>();
			page.setPageSize(req.getPageSize());
			page.setBeginCount((req.getRequestPage() - 1) * req.getPageSize());
			page.setCurrentPage(req.getRequestPage());

			LocalDate allocationDate = LocalDate.parse(req.getAllocationDate(),
					DateTimeFormatter.ofPattern("yyyy-MM-dd"));

			StringBuilder sql = new StringBuilder(
					"from CommunityMeterAllocationItemEntity a where a.id.allocationEndYear=" + allocationDate.getYear()
							+ " and a.id.allocationEndMonth=" + allocationDate.getMonthValue())
					.append(null != req.getMeterIds() && req.getMeterIds().length > 0
							? " and  a.meterId in(" + StringUtils.join(req.getMeterIds(), ",") + ")"
							: "");

			page = communityMeterAllocationItemDao.queryPage(page, sql.toString());
			res.setPageSize(req.getPageSize());
			res.setCurrentPage(req.getRequestPage());
			res.setTotal(page.getTotalPage());
			res.setTotalResult(page.getTotalCount());

			page.getResultList().forEach(o -> {
				CommunityMeterAllocationItemForm itemForm = new CommunityMeterAllocationItemForm();
				itemForm.setAllocationAmount(o.getAllocationAmount());
				itemForm.setAllocationId(o.getId().getAllocationId());
				itemForm.setAllocationNum(o.getAllocationNum());
				itemForm.setEstateId(o.getEstateId());
				itemForm.setMeterName(getMeter(o.getMeterId()).getMeterName());
				itemForm.setPayItemsName(o.getPayItemsName());
				itemForm.setUnitCode(o.getId().getUnitCode());
				res.getAllocationItemList().add(itemForm);
			});

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "单元总表分摊计算")
	public IResponse calItemAllocation(CommunityMeterAllocationReq req) {
		MeterAllocationRes res = new MeterAllocationRes();

		if (StringUtils.isNotEmpty(req.getAllocationDate())) {
			allocationDate = LocalDate.parse(req.getAllocationDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		}

		log.info("开始缓存车位数据！");
		initParkingBillingDate();
		log.info("车位数据缓存结束！！！");

		allocationItemCache = getAllocationItemList(req, allocationDate);

		CommunityPage<Set<AllocationItemVo>> page = CommunityCache.getPage(new HashSet<>(allocationItemCache),
				req.getPageSize(), req.getRequestPage());
		res.setCurrentPage(page.getCurrentPage());
		res.setPageSize(page.getPageSize());
		res.setTotal(page.getTotalPage());
		res.setTotalResult(page.getTotalCount());
		page.getResultList().forEach(o -> {
			res.getAllocationItemList()
					.add(new CommunityMeterAllocationItemForm(o.getAllocationNum(), o.getAllocationAmount(),
							o.getAllocationId(), o.getEstateId(), o.getUnitCode(), o.getPayItemsName()));
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "单元总表分摊刷新")
	public IResponse refreshItemAllocation(CommunityMeterAllocationReq req) {
		GenericResponse res = new GenericResponse();

		if (StringUtils.isNotEmpty(req.getAllocationDate())) {
			allocationDate = LocalDate.parse(req.getAllocationDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		}

		log.info("开始缓存车位数据！");
		initParkingBillingDate();
		log.info("车位数据缓存结束！！！");

		allocationItemCache = getAllocationItemList(req, allocationDate);

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "单元总表分摊结果提交")
	public IResponse submitItemAllocation(CommunityMeterAllocationReq req) {
		GenericResponse res = new GenericResponse();

		if (allocationItemCache.size() > 0) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {

					StringBuilder sql1 = new StringBuilder(
							"delete from t_community_meter_allocation_item where allocationEndYear="
									+ allocationDate.getYear() + " and allocationEndMonth="
									+ allocationDate.getMonthValue());
					PreparedStatement stmt1 = conn.prepareStatement(sql1.toString());

					StringBuilder sql = new StringBuilder(
							"insert into t_community_meter_allocation_item(state,allocationAmount,allocationId,"
									+ "allocationNum,estateId,unitCode,allocationStartMonth,allocationStartYear,payItemsName,"
									+ "allocationEndYear,allocationEndMonth,meterId,allocationPeriod,templeteId,specialAllocationFlag) "
									+ "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE "
									+ "allocationId=?,unitCode=?,allocationStartMonth=?,allocationStartYear=?,"
									+ "allocationEndMonth=?,allocationEndYear=?");
					Integer transCount = 0;
					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);

					stmt1.addBatch();
					stmt1.executeBatch();
					conn.commit();

					for (AllocationItemVo item : allocationItemCache) {
						if (item.getAllocationAmount().doubleValue() != 0) {
							stmt.setInt(1, 1);
							stmt.setBigDecimal(2, item.getAllocationAmount());
							stmt.setInt(3, item.getAllocationId());
							stmt.setBigDecimal(4, item.getAllocationNum());
							stmt.setInt(5, item.getEstateId());
							stmt.setString(6, getEstate(item.getEstateId()).getUnitCode());
							stmt.setInt(7, item.getAllocationStartMonth());
							stmt.setInt(8, item.getAllocationStartYear());
							stmt.setString(9, item.getPayItemsName());
							stmt.setInt(10, item.getAllocationEndYear());
							stmt.setInt(11, item.getAllocationEndMonth());
							stmt.setInt(12, item.getMeterId());
							stmt.setInt(13, item.getAllocationPeriod());
							if (null != item.getTempleteId()) {
								stmt.setInt(14, item.getTempleteId());
							} else {
								stmt.setNull(14, Types.INTEGER);
							}
							stmt.setInt(15, item.getSpecialAllocationFlag());
							stmt.setInt(16, item.getAllocationId());
							stmt.setString(17, getEstate(item.getEstateId()).getUnitCode());
							stmt.setInt(18, item.getAllocationStartMonth());
							stmt.setInt(19, item.getAllocationStartYear());
							stmt.setInt(20, item.getAllocationEndMonth());
							stmt.setInt(21, item.getAllocationEndYear());
							stmt.addBatch();
							transCount++;
							if (transCount % 2000 == 0) {
								stmt.executeBatch();
								conn.commit();
								log.info("保存" + transCount + "条分摊数据成功:");
							}
						}
					}
					stmt.executeBatch();
					conn.commit();
					log.info("保存" + transCount + "条分摊数据成功:");
				}
			});
			session.close();
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_DATA_NULL_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
		}
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse exportAllocationList(CommunityMeterAllocationReq req) {
		ExportAllocationListRes res = new ExportAllocationListRes();

		if (StringUtils.isNotEmpty(req.getAllocationDate())) {
			allocationDate = LocalDate.parse(req.getAllocationDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		}

		String[] title = { "楼盘名称", "楼阁", "单元编号", "楼层", "房号", "姓名", "收费面积", "产权车位面积", "产权车位个数", "人防车位面积", "人防车位个数",
				"入住日期", "二区车库公共电费", "三区车库公共电费", "公共电费分摊", "景观大道路灯电费", "空中花园公共电费", "车库公共电费", "车库公共水费分摊", "公共水费分摊",
				"合计" };
		res.setTitle(title);

		StringBuilder sql = new StringBuilder("select * from v_export_allocation_list " + "where allocationdate='"
				+ allocationDate.getYear() + "-" + allocationDate.getMonthValue() + "' ORDER BY districtOrder,buildingOrder, FLOOR, roomNumber ASC");

		List<Object[]> allocationList = communityMeterAllocationDao.createSQLQuery(sql.toString()).list();

		allocationList.forEach(o -> {
			ExportAllocationVo vo = null;
			if (null != o) {
				try {
					vo = new ExportAllocationVo();
					vo.setDistrictName(o[0].toString());
					vo.setBuildingName(o[1].toString());
					vo.setUnitCode(o[2].toString());
					vo.setFloor(o[3].toString());
					vo.setRoomnumber(o[4].toString());
					vo.setUsername(o[5].toString());
					vo.setChargingarea(o[6].toString());
					vo.setPropertyParkingArea(
							Double.valueOf(o[7].toString()).doubleValue() == 0 ? "" : o[7].toString());
					vo.setPropertyParkingNum(Double.valueOf(o[8].toString()).doubleValue() == 0 ? "" : o[8].toString());
					vo.setDefenceParkingArea(Double.valueOf(o[9].toString()).doubleValue() == 0 ? "" : o[9].toString());
					vo.setDefenceParkingNum(
							Double.valueOf(o[10].toString()).doubleValue() == 0 ? "" : o[10].toString());
					vo.setRecorddate(o[11].toString().substring(0, 10));
					vo.setFee1(Double.valueOf(o[12].toString()).doubleValue() == 0 ? "" : o[12].toString());
					vo.setFee2(Double.valueOf(o[13].toString()).doubleValue() == 0 ? "" : o[13].toString());
					vo.setFee3(Double.valueOf(o[14].toString()).doubleValue() == 0 ? "" : o[14].toString());
					vo.setFee4(Double.valueOf(o[15].toString()).doubleValue() == 0 ? "" : o[15].toString());
					vo.setFee5(Double.valueOf(o[16].toString()).doubleValue() == 0 ? "" : o[16].toString());
					vo.setFee6(Double.valueOf(o[17].toString()).doubleValue() == 0 ? "" : o[17].toString());
					vo.setFee7(Double.valueOf(o[18].toString()).doubleValue() == 0 ? "" : o[18].toString());
					vo.setFee8(Double.valueOf(o[19].toString()).doubleValue() == 0 ? "" : o[19].toString());
					vo.setTotalFee(o[20].toString());
				} catch (Exception ex) {
					log.error(ex.getMessage() + ":ExportAllocationVo(" + o[0].toString() + "" + o[1].toString() + ""
							+ o[2].toString() + ")数据转换失败");
					vo = null;
				}
			}
			if (null != vo) {
				res.getExpportAllocationList().add(vo);
			}
		});

//		try {
//			transExcel(res);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@SuppressWarnings("unchecked")
	@Override
	public IResponse submitAllocationReceivables(CommunityMeterAllocationReq req) {
		GenericResponse res = new GenericResponse();

		if (StringUtils.isNotEmpty(req.getAllocationDate())) {
			allocationDate = LocalDate.parse(req.getAllocationDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		}

		List<AllocationItemVo> itemList = communityMeterAllocationItemDao.createQuery(
				"select new com.foshan.entity.community.vo.AllocationItemVo(ROUND(SUM(a.allocationNum),2) AS allocationNum,"
						+ "ROUND(SUM(a.allocationAmount),2) AS allocationAmount,a.payItemsName,a.estateId,a.id.allocationStartYear,"
						+ "a.id.allocationStartMonth,a.id.allocationEndYear,a.id.allocationEndMonth,a.allocationPeriod,a.meterId,a.specialAllocationFlag) "
						+ "from CommunityMeterAllocationItemEntity a "
						+ "where a.specialAllocationFlag=0 and a.id.allocationEndYear=" + allocationDate.getYear()
						+ " and a.id.allocationEndMonth=" + allocationDate.getMonthValue()
						+ " group by a.estateId,a.payItemsName,a.templeteId")
				.list();

		List<Integer> meterIdList = communityMeterAllocationItemDao.createSQLQuery(
				"select distinct meterid from t_community_meter_allocation_item where allocationEndYear="
						+ allocationDate.getYear() + " and allocationEndMonth=" + allocationDate.getMonthValue())
				.list();

		log.info("itemList.size()=" + itemList.size());

		// 将总表分摊费用按单元id进行分组
		Map<Integer, List<AllocationItemVo>> collect1 = (Map<Integer, List<AllocationItemVo>>) itemList.parallelStream()
				.collect(groupingBy(AllocationItemVo::getEstateId));

		log.info("生成" + collect1.size() + "单元应收数据！！！");
		Set<CommunityReceivablesForm> receivablesSet = new HashSet<>();

		collect1.keySet().forEach(estateId -> {

			List<AllocationItemVo> temp = collect1.get(estateId);
			// 将每个单元下的分摊费用按费用类别进行求和计算，得到分摊费用清单Map
			Map<String, BigDecimal> tempMap = temp.stream().collect(Collectors.groupingBy(d -> payItemsNamePeriod(d),
					Collectors.reducing(BigDecimal.ZERO, AllocationItemVo::getAllocationAmount, BigDecimal::add)));

			Map<String, BigDecimal> sourceItem = temp.stream().collect(Collectors.groupingBy(d -> payItemsNamePeriod(d),
					Collectors.reducing(BigDecimal.ZERO, AllocationItemVo::getAllocationNum, BigDecimal::add)));

			Map<String, CommunityReceivablesForm> receivableMap = new HashMap<>();

			// 根据费用清单Map生成相应的应收款账单，并保存在列表中
			tempMap.keySet().forEach(o -> {
				String[] key = o.split("_");
				String payItemsName = key[0];
				Integer allocationStartYear = Integer.parseInt(key[2]);
				Integer allocationStartMonth = Integer.parseInt(key[3]);
				Integer allocationEndYear = Integer.parseInt(key[4]);
				Integer allocationEndMonth = Integer.parseInt(key[5]);

				/*
				 * 根据收费项目判断是否已经存在不同分摊周期的同一名称收费项目数据，如果存在则获取已经存在的数据
				 */
				CommunityReceivablesForm receivables = null;
				if (receivableMap.containsKey(payItemsName)) {
					receivables = receivableMap.get(payItemsName);
				} else {
					receivables = new CommunityReceivablesForm();
				}

				receivables.setChargeCategory(
						CommunityPayItemsEntity.getChargeCategoryStr(CommunityCache.getChargeCategory(payItemsName)));

				/*
				 * 写入收费开始时间数据，如果已经存在开始时间，则判断时间早晚，将最早的时间写入
				 */
				LocalDate startTime = LocalDate.of(allocationStartYear, allocationStartMonth, 15)
						.with(TemporalAdjusters.firstDayOfMonth());
				if (null == receivables.getStartTime() || startTime.isBefore(startTime)) {
					receivables.setStartTime(startTime.toString());
				}
				receivables.setEndTime(LocalDate.of(allocationEndYear, allocationEndMonth, 15)
						.with(TemporalAdjusters.lastDayOfMonth()).toString());
				receivables.setChargeSource(payItemsName.indexOf("电费") > 0 ? "电表分摊" : "水表分摊");
				receivables.setEstateId(estateId);
				receivables.setPayItemsName(payItemsName);
				receivables.setPaymentPeriod(receivables.getEndTime());
				receivables.setReceivableAmount((tempMap.get(o)
						.add(StringUtils.isNotEmpty(receivables.getReceivableAmount())
								? new BigDecimal(receivables.getReceivableAmount())
								: new BigDecimal("0")))
						.toString());
				receivables.setSourceNotes(sourceItem.get(o).toString());
				receivables.setReceivableDate(receivables.getEndTime());
				receivables.setReceivedAmount("0");
				receivables.setReceivablesNO(
						generateSnLastValue(CommunityReceivablesServiceImpl.RECEIVABLESNO_SN_TYPE).toString());

				if (Double.valueOf(receivables.getReceivableAmount()) > 0) {
					receivableMap.put(payItemsName, receivables);
					receivablesSet.add(receivables);
				}
			});
		});

		// 批量保存应收款账单
		batchSaveAlloctionItem(receivablesSet);

		// 批量更改总表分摊时间
		batchUpdateMeterAllocationDate(meterIdList,
				allocationDate.with(TemporalAdjusters.lastDayOfMonth()).toString() + " 00:00:00");
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	private static String payItemsNamePeriod(AllocationItemVo item) {
		return item.getPayItemsName() + "_" + item.getAllocationPeriod() + "_" + item.getAllocationStartYear() + "_"
				+ item.getAllocationStartMonth() + "_" + item.getAllocationEndYear() + "_"
				+ item.getAllocationEndMonth();
	}

	@SuppressWarnings("unchecked")
	private List<AllocationItemVo> getAllocationItemList(CommunityMeterAllocationReq req, LocalDate allocationDate) {

		StringBuilder itemSql = new StringBuilder(
				"select id,payItemsName,templeteId,unitPrice,dynamicParameter,templeteInfo,"
						+ "allocationStartYear,allocationStartMonth,allocationEndYear,allocationEndMonth,totalChargeArea,allocationNum,"
						+ "allocationId,chargingArea,usableArea,meterId,memberId,billingDate,propertyParkingArea,propertyParkingNum,"
						+ "defenceParkingArea,defenceParkingNum,unitCode,allocationPeriod,specialAllocationFlag from v_allocation_item where memberid is not null")
				.append(null != req.getMeterIds() && req.getMeterIds().length > 0
						? " and meterId in(" + StringUtils.join(req.getMeterIds(), ",") + ")"
						: "")
				.append(StringUtils.isNotEmpty(req.getUnitCode()) ? " and unitCode='" + req.getUnitCode() + "' " : "")
				.append(null != req.getEstateId() ? " and id=" + req.getEstateId() : "")
				.append(StringUtils.isNotEmpty(req.getPayItemsName())
						? " and payItemsName='" + req.getPayItemsName() + "'"
						: "")
				.append(" and allocationEndyear=" + allocationDate.getYear() + " and allocationEndmonth="
						+ allocationDate.getMonthValue() + " and billingdate<='" + allocationDate.getYear() + "-"
						+ allocationDate.getMonthValue() + "-10 00:00:00'");

		log.info("开始获取总表单元分摊数据！");
		List<Object[]> itemList = communityMeterAllocationDao.createSQLQuery(itemSql.toString()).list();

		List<AllocationItemVo> result = new ArrayList<>();
		itemList.forEach(o -> {
			AllocationVo itemVo = AllocationVo.getAllocationItemVo(o);
			try {
				AllocationItemVo item = new AllocationItemVo();
				item.setAllocationId(itemVo.getAllocationId());
				item.setEstateId(itemVo.getEstateId());
				item.setUnitCode(itemVo.getUnitCode());
				item.setMeterId(itemVo.getMeterId());
				item.setPayItemsName(itemVo.getPayItemsName());
				item.setAllocationStartMonth(itemVo.getAllocationStartMonth());
				item.setAllocationStartYear(itemVo.getAllocationStartYear());
				item.setAllocationEndMonth(itemVo.getAllocationEndMonth());
				item.setAllocationEndYear(itemVo.getAllocationEndYear());
				item.setAllocationPeriod(itemVo.getAllocationPeriod());
				item.setTempleteId(null != itemVo.getTempleteId() ? itemVo.getTempleteId() : null);

				item.setSpecialAllocationFlag(itemVo.getSpecialAllocationFlag().equals(0) ? 0
						: itemVo.getSpecialAllocationFlag().equals(3) && (itemVo.getPayItemsName().equals("二区车库公共电费")
								|| itemVo.getPayItemsName().equals("三区车库公共电费")
								|| itemVo.getPayItemsName().equals("车库公共电费")
								|| itemVo.getPayItemsName().equals("车库公共水费分摊"))
										? 1
										: (itemVo.getSpecialAllocationFlag().equals(1)
												&& itemVo.getPayItemsName().indexOf("车库公共电费电费") >= 0)
												|| (itemVo.getSpecialAllocationFlag().equals(2)
														&& itemVo.getPayItemsName().indexOf("车库公共水费") >= 0) ? 1 : 0);
				if (null != itemVo.getTempleteId()) {
					String calFormula = itemVo.getTempleteInfo();

					LocalDate allocationStartDate = LocalDate.of(itemVo.getAllocationStartYear(),
							itemVo.getAllocationStartMonth(), 1);
					LocalDate allocationEndDate = LocalDate
							.of(itemVo.getAllocationEndYear(), itemVo.getAllocationEndMonth(), 15)
							.with(TemporalAdjusters.lastDayOfMonth());

					List<CarBillingDateVo> carList = CommunityCache.carBillingDate.get(item.getEstateId());

					if (itemVo.getAllocationPeriod() >= 1 && null != carList && carList.size() >= 1) {

						BigDecimal totalNum = new BigDecimal("0");
						BigDecimal totalAmount = new BigDecimal("0");

						for (CarBillingDateVo car : carList) {
							if ((itemVo.getTempleteId().equals(1) && car.getEstateType().equals("产权车位"))
									|| (itemVo.getTempleteId().equals(2) && car.getEstateType().equals("人防车位"))
									|| itemVo.getTempleteId().equals(3)) {

								Map<String, BigDecimal> paras = mapper.readValue(itemVo.getDynamicParameter(),
										new HashMap<String, BigDecimal>().getClass());
								paras.put("总表用量", itemVo.getAllocationNum());
								paras.put("单价", itemVo.getUnitPrice());
								paras.put("收费面积", car.getChargingArea());

								LocalDate carStart = car.getBillingDate().toInstant().atZone(ZoneId.systemDefault())
										.toLocalDate();
								LocalDate carEnd = null != car.getTerminationDate() ? car.getTerminationDate()
										.toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : null;
								// 获取该车位应分摊月份数
								Long month = DateUtil.calAllocationMonth(
										carStart.isBefore(allocationStartDate) ? allocationStartDate : carStart,
										null != carEnd
												? (carEnd.isAfter(allocationEndDate) ? allocationStartDate : carEnd)
												: allocationEndDate);

								/*
								 * 当车位分摊月份大于0且小于分摊周期则按分摊月份数进行分摊，否则按全部分摊周期进行分摊
								 */
								if (month != 0) {
									if (month < itemVo.getAllocationPeriod()) {
										totalNum = totalNum
												.add(JepUtil.cal(calFormula, paras, 2)
														.divide(new BigDecimal(itemVo.getAllocationPeriod().toString()),
																4, RoundingMode.HALF_UP)
														.multiply(new BigDecimal(month + ""))
														.multiply(StringUtils.isNotEmpty(car.getComment())
																&& car.getComment().equals("子母车位")
																&& car.getEstateType().equals("人防车位")
																		? new BigDecimal("2")
																		: new BigDecimal("1")));

										BigDecimal amount = JepUtil.cal(calFormula + "*单价", paras, 2)
												.divide(new BigDecimal(itemVo.getAllocationPeriod().toString()), 2,
														RoundingMode.HALF_UP)
												.multiply(new BigDecimal(month + "")).setScale(2, RoundingMode.HALF_UP);

										if (StringUtils.isNotEmpty(car.getComment()) && car.getComment().equals("子母车位")
												&& car.getEstateType().equals("人防车位")) {
											amount = amount.multiply(new BigDecimal("2"));
										}

										totalAmount = totalAmount.add(amount);
									} else {
										totalNum = totalNum.add(JepUtil.cal(calFormula, paras, 2)
												.multiply(StringUtils.isNotEmpty(car.getComment())
														&& car.getComment().equals("子母车位")
														&& car.getEstateType().equals("人防车位") ? new BigDecimal("2")
																: new BigDecimal("1")));

										BigDecimal amount = JepUtil.cal(calFormula + "*单价", paras, 2);

										if (StringUtils.isNotEmpty(car.getComment()) && car.getComment().equals("子母车位")
												&& car.getEstateType().equals("人防车位")) {
											amount = amount.setScale(2, RoundingMode.HALF_UP)
													.multiply(new BigDecimal("2"));
										}

										totalAmount = totalAmount.add(amount);
									}
								}
							}
						}

						item.setAllocationNum(totalNum);
						item.setAllocationAmount(totalAmount);
					} else {
						Map<String, BigDecimal> paras = mapper.readValue(itemVo.getDynamicParameter(),
								new HashMap<String, BigDecimal>().getClass());
						paras.put("总表用量", itemVo.getAllocationNum());
						paras.put("单价", itemVo.getUnitPrice());
						paras.put("收费面积", itemVo.getPropertyParkingArea());
						item.setAllocationNum(JepUtil.cal(calFormula, paras, 2));
						item.setAllocationAmount(JepUtil.cal(calFormula + "*单价", paras, 4));
					}
				} else {
					Map<String, BigDecimal> paras = new HashMap<>();
					paras.put("分摊总数", itemVo.getAllocationNum());
					paras.put("总分摊面积", itemVo.getTotalChargeArea());
					paras.put("收费面积", itemVo.getChargingArea());
					paras.put("单价", itemVo.getUnitPrice());
					StringBuilder calFormula = new StringBuilder("分摊总数/总分摊面积*收费面积");
					BigDecimal estateAllocationNum = JepUtil.cal(calFormula.toString(), paras, 2);
					BigDecimal estateAllocationAmount = JepUtil.cal(calFormula.append("*单价").toString(), paras, 4);

					LocalDate estateBillingDate = itemVo.getBillingDate().toInstant().atZone(ZoneId.systemDefault())
							.toLocalDate();

					Long month = DateUtil.calAllocationMonth(estateBillingDate, allocationDate);

					if (month != 0 && month < itemVo.getAllocationPeriod()) {

						item.setAllocationNum(
								estateAllocationNum.divide(new BigDecimal(itemVo.getAllocationPeriod().toString()), 4,
										RoundingMode.HALF_UP).multiply(new BigDecimal(month + "")));
						item.setAllocationAmount(
								estateAllocationAmount.divide(new BigDecimal(itemVo.getAllocationPeriod().toString()),
										4, RoundingMode.HALF_UP).multiply(new BigDecimal(month + "")));
					} else {
						item.setAllocationNum(estateAllocationNum);
						item.setAllocationAmount(estateAllocationAmount);
					}

				}

				if (item.getAllocationAmount().doubleValue() != 0) {
					result.add(item);
				}

			} catch (Exception ex) {
				ex.printStackTrace();
				log.error(ex.getMessage());
			}
		});
		log.info("总表单元分摊数据总量：" + result.size());
		return result;
	}

	@SuppressWarnings("unchecked")
	private void batchSaveAlloctionItem(Set<CommunityReceivablesForm> receivablesSet) {

		List<Object[]> cheweiNumList = communityPropertyDao.createSQLQuery(
				"SELECT id,IFNULL(REPLACE(JSON_EXTRACT(reservedField,'$.defenceParkingNum'),'\\\"',''),0) AS defenceParkingNum "
						+ "FROM t_community_property WHERE IFNULL(REPLACE(JSON_EXTRACT(reservedField,'$.defenceParkingNum'),'\\\"',''),0)>1")
				.list();
		Map<Integer, Integer> numMap = new HashMap<>();

		cheweiNumList.forEach(o -> {
			numMap.put(Integer.parseInt(o[0].toString()), Integer.parseInt(o[1].toString()));
		});

		if (receivablesSet.size() > 0) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {
					StringBuilder sql = new StringBuilder(
							"insert into t_community_receivables(chargeCategory,chargeSource,endTime,"
									+ "payItemsName,payItemId,paymentPeriod,receivableAmount,receivableDate,"
									+ "receivedAmount,startTime,estateId,subestateId,sourceNotes,receivablesNO) "
									+ "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE "
									+ "payItemsName=?,receivableAmount=?,receivableDate=?,estateId=?,subestateid=?");
					Integer transCount = 0;
					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					for (CommunityReceivablesForm item : receivablesSet) {
						stmt.setString(1, item.getChargeCategory());
						stmt.setString(2, item.getChargeSource());
						stmt.setString(3, item.getEndTime());
						stmt.setString(4, item.getPayItemsName());
						if (null != CommunityCache.payItemsNameList.get(item.getPayItemsName())) {
							stmt.setInt(5, CommunityCache.payItemsNameList.get(item.getPayItemsName()));
						} else {
							stmt.setNull(5, Types.INTEGER);
						}
						stmt.setString(6, item.getPaymentPeriod());
						BigDecimal receivableAmount = new BigDecimal(item.getReceivableAmount());
//						if (item.getPayItemsName().equals("车库公共电费") && numMap.containsKey(item.getEstateId())) {
//							BigDecimal carNum = new BigDecimal(numMap.get(item.getEstateId()) + "");
//							receivableAmount = receivableAmount.multiply(carNum);
//						}
						stmt.setBigDecimal(7, receivableAmount.setScale(2, RoundingMode.HALF_UP));
						stmt.setString(8, item.getReceivableDate());
						stmt.setBigDecimal(9, BigDecimal.ZERO);
						stmt.setString(10, item.getStartTime());
						stmt.setInt(11, item.getEstateId());
						stmt.setInt(12, 0);
						stmt.setString(13, item.getSourceNotes());
						stmt.setString(14, item.getReceivablesNO());
						stmt.setString(15, item.getPayItemsName());
						stmt.setBigDecimal(16, receivableAmount.setScale(2, RoundingMode.HALF_UP));
						stmt.setString(17, item.getReceivableDate());
						stmt.setInt(18, item.getEstateId());
						stmt.setInt(19, 0);
						stmt.addBatch();
						transCount++;
						if (transCount % 400 == 0) {
							stmt.executeBatch();
							conn.commit();
						}
					}
					stmt.executeBatch();
					conn.commit();
					log.info("生成" + transCount + "条应收款成功:");
				}
			});
			session.close();
		}
	}

	private void batchUpdateMeterAllocationDate(List<Integer> meterIdList, String allocateDateStr) {

		if (meterIdList.size() > 0) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {
					StringBuilder sql = new StringBuilder(
							"update t_community_meter set lastAllocateDate='" + allocateDateStr + "' where id=?");
					Integer transCount = 0;
					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					for (Integer meterId : meterIdList) {
						stmt.setInt(1, meterId);
						stmt.addBatch();
						transCount++;
						if (transCount % 400 == 0) {
							stmt.executeBatch();
							conn.commit();
						}
					}
					stmt.executeBatch();
					conn.commit();
					log.info("更改" + transCount + "条总表分摊时间成功！！！");
				}
			});
			session.close();
		}
	}

	private void clearMeterAllocation(LocalDate allocationDate) {
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) {
				try {
					PreparedStatement stmt1 = conn.prepareStatement(
							"delete from t_community_meter_allocation where allocationYear=" + allocationDate.getYear()
									+ " and allocationMonth=" + allocationDate.getMonthValue());
					conn.setAutoCommit(false);
					stmt1.addBatch();
					stmt1.executeBatch();
					conn.commit();
				} catch (Exception ex) {
					log.error(ex.getMessage());
					ex.printStackTrace();
				}
			}
		});

		session.close();
	}

	private void clearMeterRecord(CommunityMeterAllocationReq req) {
		// 处理临时表数据
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) {
				try {
//					PreparedStatement stmt1 = conn.prepareStatement(
//							"delete from tmp_meter_record " + "where allocationEndyear=" + allocationDate.getYear()
//									+ " and allocationEndmonth=" + allocationDate.getMonthValue()
//									+ (req.getMeterIds().length > 0
//											? " and meterid in(" + StringUtils.join(req.getMeterIds(), ",") + ")"
//											: ""));

					PreparedStatement stmt1 = conn
							.prepareStatement("delete from tmp_meter_record " + "where allocationEndyear<="
									+ allocationDate.getYear() + " or (allocationEndyear=" + allocationDate.getYear()
									+ " and allocationEndmonth<=" + allocationDate.getMonthValue() + ") "
									+ (req.getMeterIds().length > 0
											? " and meterid in(" + StringUtils.join(req.getMeterIds(), ",") + ")"
											: ""));

					conn.setAutoCommit(false);
					stmt1.addBatch();
					stmt1.executeBatch();
					conn.commit();
					log.info("临时表数据清理完毕！！！");
				} catch (Exception ex) {
					log.error(ex.getMessage());
					ex.printStackTrace();
				}
			}
		});

		session.close();
	}

	@SuppressWarnings("unchecked")
	private void transMeterRecord(CommunityMeterAllocationReq req) {

		List<Object[]> recordList = communityMeterRecordDao
				.createSQLQuery("select meterId,meterCode,metername,allocationMethod,payItemsName,"
						+ "additionalAmount,additionalInstructions,expirationDates,meterAttributesId,"
						+ "formulaId,allocationPeriod,templeteId,rate,unitPrice,dynamicParameter,formulaName,"
						+ "templeteInfo,templeteName,lastNum,recordNum,allocationStartYear,allocationStartMonth,"
						+ "allocationEndYear,allocationEndMonth,estateCount,totalChargeArea,totalAddArea,"
						+ "allocationNum,allocationAmount,allocationId from v_meter_record where allocationamount<>0 and  allocationEndyear="
						+ allocationDate.getYear() + " and allocationEndmonth=" + allocationDate.getMonthValue()
						+ (req.getMeterIds().length > 0
								? " and meterId in(" + StringUtils.join(req.getMeterIds(), ",") + ")"
								: ""))
				.list();

		// 处理临时表数据
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) {
				try {
					PreparedStatement stmt = conn.prepareStatement(
							"insert into tmp_meter_record(meterId,meterCode,metername,allocationMethod,payItemsName,"
									+ "additionalAmount,additionalInstructions,expirationDates,meterAttributesId,"
									+ "formulaId,allocationPeriod,templeteId,rate,unitPrice,dynamicParameter,formulaName,"
									+ "templeteInfo,templeteName,lastNum,recordNum,allocationStartYear,allocationStartMonth,"
									+ "allocationEndYear,allocationEndMonth,estateCount,totalChargeArea,totalAddArea,"
									+ "allocationNum,allocationAmount,allocationId) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?"
									+ ",?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE meterId=?,allocationStartYear=?,"
									+ "allocationStartMonth=?,allocationEndYear=?,allocationEndMonth=?");
					conn.setAutoCommit(false);
					for (Object[] o : recordList) {
						stmt.setInt(1, Integer.parseInt(o[0].toString()));
						stmt.setString(2, o[1].toString());
						stmt.setString(3, o[2].toString());
						stmt.setInt(4, Integer.parseInt(o[3].toString()));
						stmt.setString(5, o[4].toString());
						stmt.setBigDecimal(6, new BigDecimal(o[5].toString()));
						stmt.setString(7, null != o[6] ? o[6].toString() : "");
						if (null != o[7]) {
							stmt.setDate(8, new java.sql.Date(DateUtil.parseLongFormat(o[7].toString()).getTime()));
						} else {
							stmt.setNull(8, Types.DATE);
						}
						stmt.setInt(9, Integer.parseInt(o[8].toString()));
						if (null != o[9]) {
							stmt.setInt(10, Integer.parseInt(o[9].toString()));
						} else {
							stmt.setNull(10, Types.INTEGER);
						}
						// allocationPeriod
						stmt.setInt(11, Integer.parseInt(o[10].toString()));
						if (null != o[11]) {
							stmt.setInt(12, Integer.parseInt(o[11].toString()));
						} else {
							stmt.setNull(12, Types.INTEGER);
						}
						stmt.setBigDecimal(13, new BigDecimal(o[12].toString()));
						stmt.setBigDecimal(14, new BigDecimal(o[13].toString()));
						stmt.setString(15, null != o[14] ? o[14].toString() : "");
						stmt.setString(16, null != o[15] ? o[15].toString() : "");
						stmt.setString(17, null != o[16] ? o[16].toString() : "");
						stmt.setString(18, null != o[17] ? o[17].toString() : "");
						stmt.setBigDecimal(19, new BigDecimal(o[18].toString()));
						stmt.setBigDecimal(20, new BigDecimal(o[19].toString()));
						stmt.setInt(21, Integer.parseInt(o[20].toString()));
						stmt.setInt(22, Integer.parseInt(o[21].toString()));
						stmt.setInt(23, Integer.parseInt(o[22].toString()));
						stmt.setInt(24, Integer.parseInt(o[23].toString()));
						stmt.setInt(25, Integer.parseInt(o[24].toString()));
						stmt.setBigDecimal(26, new BigDecimal(o[25].toString()));
						stmt.setBigDecimal(27, new BigDecimal(o[26].toString()));
						stmt.setBigDecimal(28, new BigDecimal(o[27].toString()));
						stmt.setBigDecimal(29, new BigDecimal(o[28].toString()));
						if (null != o[29]) {
							stmt.setInt(30, Integer.parseInt(o[29].toString()));
						} else {
							stmt.setNull(30, Types.INTEGER);
						}
						stmt.setInt(31, Integer.parseInt(o[0].toString()));
						stmt.setInt(32, Integer.parseInt(o[20].toString()));
						stmt.setInt(33, Integer.parseInt(o[21].toString()));
						stmt.setInt(34, Integer.parseInt(o[22].toString()));
						stmt.setInt(35, Integer.parseInt(o[23].toString()));
						stmt.addBatch();
					}
					stmt.executeBatch();
					conn.commit();

					log.info("本月分摊抄表数据处理完毕！！！");
				} catch (Exception ex) {
					log.error(ex.getMessage());
					ex.printStackTrace();
				}
			}
		});

		session.close();
	}

	@SuppressWarnings("unchecked")
	private void initParkingBillingDate() {
		List<Object[]> dateList = communityPropertyDao.createSQLQuery("select * from v_chewei_shijian").list();
		CommunityCache.carBillingDate.clear();
		dateList.forEach(o -> {
			Integer parentPropertyId = Integer.parseInt(o[0].toString());
			List<CarBillingDateVo> carList = new ArrayList<>();
			if (CommunityCache.carBillingDate.containsKey(parentPropertyId)) {
				carList = CommunityCache.carBillingDate.get(parentPropertyId);
			}
			carList.add(CarBillingDateVo.getCarBillingDateVo(o));
			CommunityCache.carBillingDate.put(parentPropertyId, carList);
		});
	}

	public void transExcel(CommunityMeterAllocationReq req, HttpServletResponse response) {

		// 1、获取对应的Excel文件，工作簿文件
		XSSFWorkbook wb = new XSSFWorkbook();
		ExportAllocationListRes res = (ExportAllocationListRes) exportAllocationList(req);
		Sheet sheet = wb.createSheet(allocationDate.getYear() + "年" + allocationDate.getMonthValue() + "月公摊水电分摊");

		XSSFCellStyle titleStyle = wb.createCellStyle();
		titleStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
		titleStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中对齐
		XSSFFont titleFont = wb.createFont();
		titleFont.setFontName("微软雅黑");
		titleFont.setFontHeightInPoints((short) 9);
		titleFont.setBold(true);
		titleStyle.setFont(titleFont);
		titleStyle.setBorderTop(BorderStyle.THIN);
		titleStyle.setBorderBottom(BorderStyle.THIN);
		titleStyle.setBorderLeft(BorderStyle.THIN);
		titleStyle.setBorderRight(BorderStyle.THIN);

		XSSFFont cellFont = wb.createFont();
		cellFont.setFontName("微软雅黑");
		cellFont.setFontHeightInPoints((short) 9);

		XSSFCellStyle cellStyle = wb.createCellStyle();
		cellStyle.setFont(cellFont);
		cellStyle.setAlignment(HorizontalAlignment.CENTER);
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setBorderRight(BorderStyle.THIN);

		XSSFCellStyle cellStyle_left = wb.createCellStyle();
		cellStyle_left.setFont(cellFont);
		cellStyle_left.setAlignment(HorizontalAlignment.LEFT);
		cellStyle_left.setBorderTop(BorderStyle.THIN);
		cellStyle_left.setBorderBottom(BorderStyle.THIN);
		cellStyle_left.setBorderLeft(BorderStyle.THIN);
		cellStyle_left.setBorderRight(BorderStyle.THIN);

		XSSFCellStyle cellStyle_right = wb.createCellStyle();
		cellStyle_right.setFont(cellFont);
		cellStyle_right.setAlignment(HorizontalAlignment.RIGHT);
		cellStyle_right.setBorderTop(BorderStyle.THIN);
		cellStyle_right.setBorderBottom(BorderStyle.THIN);
		cellStyle_right.setBorderLeft(BorderStyle.THIN);
		cellStyle_right.setBorderRight(BorderStyle.THIN);

		Row title = sheet.createRow(0);
		for (int i = 0; i < res.getTitle().length; i++) {
			Cell tt = title.createCell(i, CellType.STRING);
			tt.setCellValue(res.getTitle()[i]);
			tt.setCellStyle(titleStyle);
		}

		List<ExportAllocationVo> allocationVoList = res.getExpportAllocationList();

		int i = 1;
		for (ExportAllocationVo vo : allocationVoList) {
			Row row = sheet.createRow(i);

			// 楼盘名称
			Cell cell1 = row.createCell(0, CellType.STRING);
			cell1.setCellValue(vo.getDistrictName());
			cell1.setCellStyle(cellStyle_left);

			// 楼阁
			Cell cell2 = row.createCell(1, CellType.STRING);
			cell2.setCellValue(vo.getBuildingName());
			cell2.setCellStyle(cellStyle_left);

			// 单元编号
			Cell cell3 = row.createCell(2, CellType.STRING);
			cell3.setCellValue(vo.getUnitCode());
			cell3.setCellStyle(cellStyle_left);

			// 楼层
			Cell cell4 = row.createCell(3, CellType.STRING);
			cell4.setCellValue(vo.getFloor());
			cell4.setCellStyle(cellStyle);

			// 房号
			Cell cell5 = row.createCell(4, CellType.STRING);
			cell5.setCellValue(vo.getRoomnumber());
			cell5.setCellStyle(cellStyle);

			// 姓名
			Cell cell6 = row.createCell(5, CellType.STRING);
			cell6.setCellValue(vo.getUsername());
			cell6.setCellStyle(cellStyle_left);

			// 收费面积
			Cell cell7 = row.createCell(6, CellType.STRING);
			cell7.setCellValue(vo.getChargingarea());
			cell7.setCellStyle(cellStyle_left);

			// 产权车位面积
			Cell cell8 = row.createCell(7, CellType.STRING);
			cell8.setCellValue(vo.getPropertyParkingArea());
			cell8.setCellStyle(cellStyle_right);

			// 产权车位个数
			Cell cell9 = row.createCell(8, CellType.STRING);
			cell9.setCellValue(vo.getPropertyParkingNum());
			cell9.setCellStyle(cellStyle);

			// 人防车位面积
			Cell cell10 = row.createCell(9, CellType.STRING);
			cell10.setCellValue(vo.getDefenceParkingArea());
			cell10.setCellStyle(cellStyle_right);

			// 人防车位个数
			Cell cell11 = row.createCell(10, CellType.STRING);
			cell11.setCellValue(vo.getDefenceParkingNum());
			cell11.setCellStyle(cellStyle);

			// 入住日期
			Cell cell12 = row.createCell(11, CellType.STRING);
			cell12.setCellValue(vo.getRecorddate());
			cell12.setCellStyle(cellStyle);

			// 二区车库公共电费
			Cell cell13 = row.createCell(12, CellType.NUMERIC);
			cell13.setCellValue(StringUtils.isNotEmpty(vo.getFee1()) ? new BigDecimal(vo.getFee1()).doubleValue() : 0);
			cell13.setCellStyle(cellStyle_right);

			// 三区车库公共电费
			Cell cell14 = row.createCell(13, CellType.NUMERIC);
			cell14.setCellValue(StringUtils.isNotEmpty(vo.getFee2()) ? new BigDecimal(vo.getFee2()).doubleValue() : 0);
			cell14.setCellStyle(cellStyle_right);

			// 公共电费分摊
			Cell cell15 = row.createCell(14, CellType.NUMERIC);
			cell15.setCellValue(StringUtils.isNotEmpty(vo.getFee3()) ? new BigDecimal(vo.getFee3()).doubleValue() : 0);
			cell15.setCellStyle(cellStyle_right);

			// 景观大道路灯电费
			Cell cell16 = row.createCell(15, CellType.NUMERIC);
			cell16.setCellValue(StringUtils.isNotEmpty(vo.getFee4()) ? new BigDecimal(vo.getFee4()).doubleValue() : 0);
			cell16.setCellStyle(cellStyle_right);

			// 空中花园公共电费
			Cell cell17 = row.createCell(16, CellType.NUMERIC);
			cell17.setCellValue(StringUtils.isNotEmpty(vo.getFee5()) ? new BigDecimal(vo.getFee5()).doubleValue() : 0);
			cell17.setCellStyle(cellStyle_right);

			// 车库公共电费
			Cell cell18 = row.createCell(17, CellType.NUMERIC);
			cell18.setCellValue(StringUtils.isNotEmpty(vo.getFee6()) ? new BigDecimal(vo.getFee6()).doubleValue() : 0);
			cell18.setCellStyle(cellStyle_right);

			// 车库公共水费分摊
			Cell cell19 = row.createCell(18, CellType.NUMERIC);
			cell19.setCellValue(StringUtils.isNotEmpty(vo.getFee7()) ? new BigDecimal(vo.getFee7()).doubleValue() : 0);
			cell19.setCellStyle(cellStyle_right);

			// 公共水费分摊
			Cell cell20 = row.createCell(19, CellType.NUMERIC);
			cell20.setCellValue(StringUtils.isNotEmpty(vo.getFee8()) ? new BigDecimal(vo.getFee8()).doubleValue() : 0);
			cell20.setCellStyle(cellStyle_right);

			// 合计
			Cell cell21 = row.createCell(20, CellType.NUMERIC);
			cell21.setCellValue(new BigDecimal(vo.getTotalFee()).doubleValue());
			cell21.setCellStyle(cellStyle_right);
			i++;
		}

		Row total = sheet.createRow(i);

		Cell totalStr = total.createCell(11, CellType.STRING);
		totalStr.setCellValue("合计");
		totalStr.setCellStyle(cellStyle_right);

		Cell total1 = total.createCell(12, CellType.NUMERIC);

		total1.setCellFormula("SUM(M2:M" + i + ")");
		total1.setCellStyle(cellStyle_right);

		Cell total2 = total.createCell(13, CellType.NUMERIC);
		total2.setCellFormula("SUM(N2:N" + i + ")");
		total2.setCellStyle(cellStyle_right);

		Cell total3 = total.createCell(14, CellType.NUMERIC);
		total3.setCellFormula("SUM(O2:O" + i + ")");
		total3.setCellStyle(cellStyle_right);

		Cell total4 = total.createCell(15, CellType.NUMERIC);
		total4.setCellFormula("SUM(P2:P" + i + ")");
		total4.setCellStyle(cellStyle_right);

		Cell total5 = total.createCell(16, CellType.NUMERIC);
		total5.setCellFormula("SUM(Q2:Q" + i + ")");
		total5.setCellStyle(cellStyle_right);

		Cell total6 = total.createCell(17, CellType.NUMERIC);
		total6.setCellFormula("SUM(R2:R" + i + ")");
		total6.setCellStyle(cellStyle_right);

		if (allocationVoList.size() > 0) {
			Cell total7 = total.createCell(18, CellType.NUMERIC);
			total7.setCellFormula("SUM(S2:S" + i + ")");
			total7.setCellStyle(cellStyle_right);

			Cell total8 = total.createCell(19, CellType.NUMERIC);
			total8.setCellFormula("SUM(T2:T" + i + ")");
			total8.setCellStyle(cellStyle_right);

			Cell total9 = total.createCell(20, CellType.NUMERIC);
			total9.setCellFormula("SUM(U2:U" + i + ")");
			total9.setCellStyle(cellStyle_right);
		}

		try {
			ExcelExportUtil.export(response, wb,
					allocationDate.getYear() + "年" + allocationDate.getMonthValue() + "月公摊水电分摊.xlsx");
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}
	}

}
