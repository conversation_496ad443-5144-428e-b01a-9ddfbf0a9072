package com.foshan.service.community.impl.statistics;

import static java.util.stream.Collectors.groupingBy;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jdbc.Work;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.vo.ReservedFieldVo;
import com.foshan.form.community.statistics.CommunityNoReceivablesMeasuresVo;
import com.foshan.form.community.statistics.CommunityNoReceivablesSummaryVo;
import com.foshan.form.community.statistics.CommunityNoReceivablesVo;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityCache;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CommunityNoReceivablesUtil {

	public static String getNoReceivablesSql(Integer[] districtIds,Integer[] buildingIds, String[] chargeCategorys, String[] payItemsNames,
			String endDate) {
		StringBuilder sql = new StringBuilder(
				"select distinct districtcode,CONCAT('select aa.districtName,aa.buildingName,aa.buildingType,")
				.append("aa.unitCode,aa.userName,aa.homePhone,aa.buildingArea,aa.amount3,aa.amount36,aa.amount6,',")
				.append("'aa.totalAmount,aa.bankName,aa.bankAccount,aa.accountName,aa.districtCode,aa.estateState,',")
				.append("GROUP_CONCAT(DISTINCT CONCAT('MAX(CASE payitemsname WHEN ''',payitemsname,")
				.append("''' then CONCAT(DATE_FORMAT(startDate,''%Y.%m''),''～'',DATE_FORMAT(endDate,''%Y.%m'')) ELSE '''' END) as ',payitemsname,")
				.append("'属期,MAX(CASE payitemsname WHEN ''',payitemsname,")
				.append("''' THEN fee ELSE '''' END)  AS ',payitemsname)),")
				.append("' FROM (SELECT aaa.districtname,aaa.districtorder,aaa.districtCode,aaa.buildingname,aaa.buildingType,")
				.append("aaa.buildingOrder,aaa.unitcode,aaa.username,aaa.idcard,aaa.homephone,aaa.buildingarea,',")
				.append("'aaa.payitemsname,aaa.bankname,aaa.bankaccount,aaa.accountname,aaa.estateState,',")
				.append("'MIN(aaa.receivabledate) AS startDate,MAX(aaa.receivabledate) AS endDate,")
				.append("SUM(aaa.amount1) AS fee,aaa.amount3,aaa.amount36,aaa.amount6,aaa.totalAmount FROM (")
				.append("SELECT a.id as districtid,a.districtName,a.districtOrder,a.districtCode,")
				.append("b.buildingName,b.buildingType,b.buildingOrder,c.unitCode,g.username,g.idcard,g.homephone,c.buildingArea,d.chargecategory,")
				.append("CASE c.estateState WHEN 0 THEN ''未收楼'' WHEN 1 THEN ''已入住'' WHEN 2 THEN ''装修中'' ")
				.append("WHEN 3 THEN ''出租'' WHEN 4 THEN ''离退'' WHEN 5 THEN ''自住'' WHEN 6 THEN ''空置'' END AS estatestate,")
				.append("h.bankname,h.bankaccount,h.accountname,d.payitemsname,")
				.append("d.receivabledate,d.receivableamount,d.receivedamount,")
				.append("(d.receivableamount-d.receivedamount) AS amount1,totalamount,amount3,amount36,amount6 ")
				.append("FROM t_community_district a INNER JOIN t_community_building b ON a.id<>11")
				.append((null != districtIds && districtIds.length > 0
						? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " "))
				.append((null != buildingIds && buildingIds.length > 0
						? " and b.id in(" + StringUtils.join(buildingIds, ",") + ") "
						: " "))
				.append("and a.districtcode=''',districtCode,''' ")
				.append("AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid ")
				.append("INNER JOIN t_community_property c ON b.id=c.buildingid ")
				.append("LEFT JOIN t_community_payment_account h ON c.paymentaccountid=h.id ")
				.append("INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivableDate<=LAST_DAY(''" + endDate + "'') ")
				.append((null != chargeCategorys && chargeCategorys.length > 0
						? " and d.chargeCategory in(''" + StringUtils.join(chargeCategorys, "'',''") + "'') "
						: " "))
				.append((null != payItemsNames && payItemsNames.length > 0
						? " and d.payItemsName in(''" + StringUtils.join(payItemsNames, "'',''") + "'') "
						: " "))
				.append("INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id ")
				.append("INNER JOIN t_community_member_property f ON c.id=f.propertyid AND f.iscurrentowner=1 AND f.iscurrentmember=1 ")
				.append("INNER JOIN t_account g ON f.memberid=g.id ")
				.append("INNER JOIN (SELECT aa.id,SUM(aa.amount1) AS totalAmount ")
				.append("FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,")
				.append("(d.receivableamount-d.receivedamount) AS amount1 ")
				.append("FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e ")
				.append("WHERE a.id<>11 ")
				.append((null != districtIds && districtIds.length > 0
						? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " "))
				.append((null != buildingIds && buildingIds.length > 0
						? " and b.id in(" + StringUtils.join(buildingIds, ",") + ") "
							: " "))
				.append("and a.districtcode=''',districtCode,''' ")
				.append((null != chargeCategorys && chargeCategorys.length > 0
						? "and d.chargeCategory in(''" + StringUtils.join(chargeCategorys, "'',''") + "'') "
						: " "))
				.append((null != payItemsNames && payItemsNames.length > 0
						? "and d.payItemsName in(''" + StringUtils.join(payItemsNames, "'',''") + "'') "
						: " "))
				.append("AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) ")
				.append("AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id ")
				.append("AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY(''" + endDate)
				.append("'')) aa GROUP BY aa.unitCode) a1 ON c.id=a1.id ")
				.append("left JOIN (SELECT aa.id,SUM(aa.amount1) AS amount3 ")
				.append("FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,")
				.append("(d.receivableamount-d.receivedamount) AS amount1 ")
				.append("FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e ")
				.append("WHERE a.id<>11 ")
				.append((null != districtIds && districtIds.length > 0
						? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " "))
				.append((null != buildingIds && buildingIds.length > 0
						? " and b.id in(" + StringUtils.join(buildingIds, ",") + ") "
						: " "))
				.append(" and a.districtcode=''',districtCode,''' ")
				.append((null != chargeCategorys && chargeCategorys.length > 0
						? "and d.chargeCategory in(''" + StringUtils.join(chargeCategorys, "'',''") + "'') "
						: " "))
				.append((null != payItemsNames && payItemsNames.length > 0
						? "and d.payItemsName in(''" + StringUtils.join(payItemsNames, "'',''") + "'') "
						: " "))
				.append("AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid ")
				.append("AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id ")
				.append("AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivabledate>=DATE_ADD(LAST_DAY(''" + endDate + "''),INTERVAL-3 MONTH) ")
				.append("AND d.receivableDate<=LAST_DAY(''" + endDate)
				.append("'')) aa GROUP BY aa.unitCode) a2 ON a1.id=a2.id ")
				.append("LEFT JOIN(SELECT aa.id,SUM(aa.amount1) AS amount36 ")
				.append("FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 ")
				.append("FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e ")
				.append("WHERE a.id<>11 ")
				.append((null != districtIds && districtIds.length > 0
						? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " "))
				.append((null != buildingIds && buildingIds.length > 0
						? " and b.id in(" + StringUtils.join(buildingIds, ",") + ") "
						: " "))
				.append(" and a.districtcode=''',districtCode,''' ")
				.append((null != chargeCategorys && chargeCategorys.length > 0
						? "and d.chargeCategory in(''" + StringUtils.join(chargeCategorys, "'',''") + "'') "
						: " "))
				.append((null != payItemsNames && payItemsNames.length > 0
						? "and d.payItemsName in(''" + StringUtils.join(payItemsNames, "'',''") + "'') "
						: " "))
				.append("AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid ")
				.append("AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id ")
				.append("AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivabledate>=DATE_ADD(LAST_DAY(''" + endDate + "''), INTERVAL-6 MONTH) ")
				.append("AND d.receivabledate<DATE_ADD(LAST_DAY(''" + endDate + "''), INTERVAL-3 MONTH)")
				.append(") aa GROUP BY aa.unitCode) a3 ON a1.id=a3.id ")
				.append("LEFT JOIN(SELECT aa.id,SUM(aa.amount1) AS amount6 ")
				.append("FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,")
				.append("(d.receivableamount-d.receivedamount) AS amount1 ")
				.append("FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e ")
				.append("WHERE a.id<>11 ")
				.append((null != districtIds && districtIds.length > 0
						? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " "))
				.append((null != buildingIds && buildingIds.length > 0
						? " and b.id in(" + StringUtils.join(buildingIds, ",") + ") "
						: " "))
				.append(" and a.districtcode=''',districtCode,''' ")
				.append((null != chargeCategorys && chargeCategorys.length > 0
						? "and d.chargeCategory in(''" + StringUtils.join(chargeCategorys, "'',''") + "'') "
						: " "))
				.append((null != payItemsNames && payItemsNames.length > 0
						? "and d.payItemsName in(''" + StringUtils.join(payItemsNames, "'',''") + "'') "
						: " "))
				.append("AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid ")
				.append("AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id ")
				.append("AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivabledate<DATE_ADD(LAST_DAY(''" + endDate + "''),INTERVAL-6 MONTH) ")
				.append(") aa GROUP BY aa.unitCode) a4 ON a1.id=a4.id ")
				.append(") aaa GROUP BY aaa.unitcode,aaa.payitemsname) aa GROUP BY aa.unitcode order by aa.districtorder,aa.buildingOrder,aa.unitCode'")
				.append(") AS subsql FROM t_community_district a ")
				.append("INNER JOIN t_community_building b ON a.id<>11 ")
				.append(null != districtIds && districtIds.length > 0
						? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " ")
				.append((null != buildingIds && buildingIds.length > 0
						? " and b.id in(" + StringUtils.join(buildingIds, ",") + ") "
						: " "))
				.append(" AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid ")
				.append("INNER JOIN t_community_property c ON b.id=c.buildingid ")
				.append("INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivableDate<=LAST_DAY('" + endDate + "') ")
				.append((null != chargeCategorys && chargeCategorys.length > 0
						? "and d.chargeCategory in('" + StringUtils.join(chargeCategorys, "','") + "') "
						: " "))
				.append((null != payItemsNames && payItemsNames.length > 0
						? "and d.payItemsName in('" + StringUtils.join(payItemsNames, "','") + "') "
						: " "))
				.append("INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id ")
				.append("INNER JOIN t_community_member_property f ON c.id=f.propertyid AND f.iscurrentowner=1 AND f.iscurrentmember=1 ")
				.append("GROUP BY a.districtcode");

		return sql.toString();
	}

	public static String getNoReceivablesMeasuresSql(Integer[] districtIds, String endDate) {
		StringBuilder sql = new StringBuilder("SELECT aa.districtcode,aa.districtname,aa.unitcode,aa.username,").append(
				"CONCAT(DATE_FORMAT(MIN(aa.startdate),'%Y-%m'),'~',DATE_FORMAT(MAX(aa.enddate),'%Y-%m')) AS period,")
				.append("SUM(aa.arrears) AS totalArrears,")
				.append("MAX(CASE aa.chargecategory WHEN '违约金' THEN aa.arrears ELSE '' END ) AS subarrears,")
				.append("GROUP_CONCAT(aa.chargecategory) AS totalChargecategory,aa.noReceivablesMeasures,aa.measures1,aa.measures2,aa.estateid ")
				.append("FROM(SELECT a.districtcode,a.districtname,a.districtOrder,a.buildingOrder,a.unitcode,a.username,a.chargecategory,")
				.append("SUM(a.receivableamount-a.receivedamount) AS arrears,")
				.append("MIN(a.receivabledate) AS startdate,")
				.append("MAX(a.receivabledate) AS enddate,a.noReceivablesMeasures,a.measures1,a.measures2,a.estateid ")
				.append("FROM (" + getNoReceivablesViewSql(districtIds, null, null, endDate, null) + ") a ")
				.append("WHERE a.receivabledate<='" + endDate + "' ")
				.append("GROUP BY a.unitcode,a.chargecategory) aa ")
				.append("GROUP BY aa.unitcode order by aa.districtorder,aa.buildingOrder,aa.unitCode");

		return sql.toString();
	}

	public static String getNoReceivablesSummarySql(String endDate) {
		StringBuilder sql = new StringBuilder("SELECT aa.districtname,aa.管理费数量,aa.管理费欠费,aa.公摊费数量,aa.公摊费欠费,").append(
				"aa.代收水电数量,aa.代收水电欠费,aa.违约金数量,aa.违约金欠费,aa.车位费数量,aa.车位费欠费,bb.amount3num,bb.amount3,cc.amount6num,cc.amount6 ")
				.append("FROM(SELECT districtid,districtname,districtorder,buildingtype,")
				.append("MAX(CASE payitemsname WHEN '管理费' THEN unitnum ELSE '' END)  AS 管理费数量,")
				.append("MAX(CASE payitemsname WHEN '管理费' THEN arrears ELSE '' END)  AS 管理费欠费,")
				.append("MAX(CASE payitemsname WHEN '公摊费' THEN unitnum ELSE '' END)  AS 公摊费数量,")
				.append("MAX(CASE payitemsname WHEN '公摊费' THEN arrears ELSE '' END)  AS 公摊费欠费,")
				.append("MAX(CASE payitemsname WHEN '代收水电' THEN unitnum ELSE '' END)  AS 代收水电数量,")
				.append("MAX(CASE payitemsname WHEN '代收水电' THEN arrears ELSE '' END)  AS 代收水电欠费,")
				.append("MAX(CASE payitemsname WHEN '违约金' THEN unitnum ELSE '' END)  AS 违约金数量,")
				.append("MAX(CASE payitemsname WHEN '违约金' THEN arrears ELSE '' END)  AS 违约金欠费,")
				.append("MAX(CASE payitemsname WHEN '车位费' THEN unitnum ELSE '' END)  AS 车位费数量,")
				.append("MAX(CASE payitemsname WHEN '车位费' THEN arrears ELSE '' END)  AS 车位费欠费 ")
				.append("FROM(SELECT districtid,districtname,districtorder,buildingtype,COUNT(unitcode) AS unitnum,payitemsname,SUM(arrears) AS arrears ")
				.append("FROM(SELECT districtid,districtname,districtorder,buildingtype,unitcode,payitemsname,SUM(arrears) AS arrears ")
				.append("FROM(SELECT districtid,CONCAT(districtname,IF(districtid IN(5,10),IF(buildingtype=0,'住宅','别墅'),'')) AS districtname,districtorder,buildingtype,unitcode,")
				.append("IF((districtid IN(4,7,8,9) AND payitemsname IN('别墅管理费','花园管理费')) OR ")
				.append("((districtid=3 OR (districtid IN(5,10) AND buildingtype=1)) AND payitemsname IN('别墅管理费','花园及停车位管理费')) OR ")
				.append("((districtid=2 OR (districtid IN(5,10) AND buildingtype=0)) AND payitemsname IN('物业管理费','车位管理费')) OR ")
				.append("(districtid=1 AND payitemsname='车位管理费') OR ")
				.append("(districtid=6 AND payitemsname IN('物业管理费','花园管理费','车位管理费','露天车位管理服务费')),'管理费',")
				.append("IF(((districtid IN(3,4,8,9) OR (districtid IN(5,10) AND buildingtype=1)) AND payitemsname IN('公共电费分摊','景观大道路灯电费')) OR ")
				.append("((districtid IN(2,7) OR ")
				.append("(districtid IN(5,10) AND buildingtype=0)) AND payitemsname IN('公共水费分摊','公共电费分摊','景观大道路灯电费','车库公共水费分摊','车库公共电费','空中花园公共电费')) OR ")
				.append("(districtid=1 AND payitemsname IN('二区车库公共电费','公共电费分摊','车库公共水费分摊','车库公共电费')) OR ")
				.append("(districtid=6 AND payitemsname IN('二区车库公共电费','公共电费分摊','景观大道路灯电费')),'公摊费',")
				.append("IF(payitemsname IN('代收水费','代收电费'),'代收水电',IF(chargecategory='违约金','违约金',payitemsname)))) AS payitemsname,arrears ")
				.append("FROM(SELECT a.id AS districtid,a.districtName,a.districtorder,b.buildingType,c.unitCode,d.chargecategory,")
				.append("d.payitemsname,(d.receivableamount-d.receivedamount) AS arrears FROM t_community_district a ")
				.append("INNER JOIN t_community_building b ON a.id<=10 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid ")
				.append("INNER JOIN t_community_property c ON b.id=c.buildingid ")
				.append("INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY('"
						+ endDate + "') ")
				.append("INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84))  AND d.payitemid=e.id ")
				.append("WHERE (districtid IN(4,7,8,9) AND payitemsname IN('别墅管理费','花园管理费')) OR ")
				.append("((districtid=3 OR (districtid IN(5,10) AND buildingtype=1)) AND payitemsname IN('别墅管理费','花园及停车位管理费')) OR ")
				.append("((districtid=2 OR (districtid IN(5,10) AND buildingtype=0)) AND payitemsname IN('物业管理费','车位管理费')) OR ")
				.append("(districtid=1 AND payitemsname='车位管理费') OR ")
				.append("(districtid=6 AND payitemsname IN('物业管理费','花园管理费','车位管理费','露天车位管理服务费')) OR ")
				.append("((districtid IN(3,4,8,9) OR (districtid IN(5,10) AND buildingtype=1)) AND payitemsname IN('公共电费分摊','景观大道路灯电费')) OR ")
				.append("((districtid IN(2,7) OR(districtid IN(5,10) AND buildingtype=0)) AND payitemsname IN('公共水费分摊','公共电费分摊','景观大道路灯电费','车库公共水费分摊','车库公共电费','空中花园公共电费')) OR ")
				.append("(districtid=1 AND payitemsname IN('二区车库公共电费','公共电费分摊','车库公共水费分摊','车库公共电费')) OR ")
				.append("(districtid=6 AND payitemsname IN('二区车库公共电费','公共电费分摊','景观大道路灯电费')) OR ")
				.append("d.payitemsname IN('代收水费','代收电费') OR d.chargecategory='违约金' ")
				.append("UNION ALL SELECT a.id AS districtid,a.districtName,a.districtorder,b.buildingType,c.unitCode,d.chargecategory,")
				.append("'车位费' AS payitemsname,(d.receivableamount-d.receivedamount) AS arrears FROM t_community_district a ")
				.append("INNER JOIN t_community_building b ON a.id<=10 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid ")
				.append("INNER JOIN t_community_property c ON b.id=c.buildingid ")
				.append("INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY('"
						+ endDate + "') ")
				.append("INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84))  AND d.payitemid=e.id ")
				.append("WHERE d.payitemsname='车位管理费') a) a GROUP BY districtid,districtname,buildingtype,unitcode,payitemsname ")
				.append(") a GROUP BY districtid,buildingtype,payitemsname) b GROUP BY districtid,buildingtype) aa ")
				.append("LEFT JOIN(SELECT districtid,buildingtype,COUNT(amount3) AS amount3num,SUM(amount3) AS amount3 ")
				.append("FROM(SELECT districtid,buildingtype,unitcode,SUM(amount1) AS amount3 ")
				.append("FROM(SELECT a.id AS districtid,a.districtName,a.districtorder,b.buildingType,c.unitCode,(d.receivableamount-d.receivedamount) AS amount1 ")
				.append("FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e ")
				.append("WHERE a.id<=10 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid ")
				.append("AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivabledate>=DATE_ADD(LAST_DAY('" + endDate + "'), INTERVAL-3 MONTH) ")
				.append("AND d.receivableDate<=LAST_DAY('" + endDate + "') ")
				.append(") a GROUP BY unitcode ) a GROUP BY districtid,buildingtype")
				.append(") bb ON aa.districtid=bb.districtid AND aa.buildingtype=bb.buildingtype ")
				.append("LEFT JOIN(SELECT districtid,buildingtype,COUNT(amount6) AS amount6num,SUM(amount6) AS amount6 ")
				.append("FROM(SELECT districtid,buildingtype,unitcode,SUM(amount1) AS amount6 ")
				.append("FROM(SELECT a.id AS districtid,a.districtName,a.districtorder,b.buildingType,c.unitCode,(d.receivableamount-d.receivedamount) AS amount1 ")
				.append("FROM  t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e ")
				.append("WHERE a.id<=10 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid ")
				.append("AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivabledate<DATE_ADD(LAST_DAY('" + endDate + "'), INTERVAL-3 MONTH) ")
				.append(") a GROUP BY unitcode ) a GROUP BY districtid,buildingtype ")
				.append(") cc ON aa.districtid=cc.districtid AND aa.buildingtype=cc.buildingtype ")
				.append("ORDER BY districtorder");

		return sql.toString();
	}

	public static String getNoReceivablesViewSql(Integer[] districtIds, String[] chargeCategorys,
			String[] payItemsNames, String endDate, String districtCode) {
		StringBuilder sql = new StringBuilder(
				"SELECT a.id as districtid,a.districtName,a.districtOrder,a.districtCode,")
				.append("b.buildingName,b.buildingType,b.buildingOrder,c.unitCode,g.username,g.idcard,g.homephone,c.buildingArea,d.chargecategory,")
				.append("CASE c.estateState WHEN 0 THEN '未收楼' WHEN 1 THEN '已入住' WHEN 2 THEN '装修中' ")
				.append("WHEN 3 THEN '出租' WHEN 4 THEN '离退' WHEN 5 THEN '自住' WHEN 6 THEN '空置' END AS estatestate,")
				.append("h.bankname,h.bankaccount,h.accountname,d.payitemsname,")
				.append("d.receivabledate,d.receivableamount,d.receivedamount,")
				.append("(d.receivableamount-d.receivedamount) AS amount1,totalamount,amount3,amount36,amount6,")
				.append("IF(LENGTH(REPLACE(JSON_EXTRACT(reservedField,'$.noReceivablesMeasures'),'[]',''))=0 OR ")
				.append("JSON_EXTRACT(reservedField,'$.noReceivablesMeasures') IS NULL,'',")
				.append("REPLACE(REPLACE(REPLACE(JSON_EXTRACT(reservedField,'$.noReceivablesMeasures'),")
				.append("'[',''),']',''),'\"','')) AS noReceivablesMeasures,")
				.append("IF(LENGTH(REPLACE(JSON_EXTRACT(reservedField,'$.measures1'),'[]',''))=0 OR ")
				.append("JSON_EXTRACT(reservedField,'$.measures1') IS NULL,'',")
				.append("REPLACE(REPLACE(REPLACE(JSON_EXTRACT(reservedField,'$.measures1'),")
				.append("'[',''),']',''),'\"','')) AS measures1,")
				.append("IF(LENGTH(REPLACE(JSON_EXTRACT(reservedField,'$.measures2'),'[]',''))=0 OR ")
				.append("JSON_EXTRACT(reservedField,'$.measures2') IS NULL,'',")
				.append("REPLACE(REPLACE(REPLACE(JSON_EXTRACT(reservedField,'$.measures2'),")
				.append("'[',''),']',''),'\"','')) AS measures2,c.id as estateid ")
				.append("FROM t_community_district a INNER JOIN t_community_building b ON a.id<>11 ")
				.append((null != districtIds && districtIds.length > 0
						? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " "))
				.append((StringUtils.isNotEmpty(districtCode) ? " and a.districtcode='" + districtCode + "' " : " "))
				.append("AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid ")
				.append("INNER JOIN t_community_property c ON b.id=c.buildingid ")
				.append("LEFT JOIN t_community_payment_account h ON c.paymentaccountid=h.id ")
				.append("INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivableDate<=LAST_DAY('" + endDate + "') ")
				.append((null != chargeCategorys && chargeCategorys.length > 0
						? "and d.chargeCategory in('" + StringUtils.join(chargeCategorys, "','") + "') "
						: " "))
				.append((null != payItemsNames && payItemsNames.length > 0
						? "and d.payItemsName in('" + StringUtils.join(payItemsNames, "','") + "') "
						: " "))
				.append("INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id ")
				.append("INNER JOIN t_community_member_property f ON c.id=f.propertyid AND f.iscurrentowner=1 AND f.iscurrentmember=1 ")
				.append("INNER JOIN t_account g ON f.memberid=g.id ")
				.append("INNER JOIN (SELECT aa.id,SUM(aa.amount1) AS totalAmount ")
				.append("FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,")
				.append("(d.receivableamount-d.receivedamount) AS amount1 ")
				.append("FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e ")
				.append("WHERE a.id<>11 ")
				.append((null != districtIds && districtIds.length > 0
						? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " "))
				.append((StringUtils.isNotEmpty(districtCode) ? " and a.districtcode='" + districtCode + "' " : " "))
				.append((null != chargeCategorys && chargeCategorys.length > 0
						? "and d.chargeCategory in('" + StringUtils.join(chargeCategorys, "','") + "') "
						: " "))
				.append((null != payItemsNames && payItemsNames.length > 0
						? "and d.payItemsName in('" + StringUtils.join(payItemsNames, "','") + "') "
						: " "))
				.append("AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) ")
				.append("AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid ")
				.append("AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id ")
				.append("AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivableDate<=LAST_DAY('" + endDate + "')) aa GROUP BY aa.unitCode) a1 ON c.id=a1.id ")
				.append("left JOIN (SELECT aa.id,SUM(aa.amount1) AS amount3 ")
				.append("FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,")
				.append("(d.receivableamount-d.receivedamount) AS amount1 ")
				.append("FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e ")
				.append("WHERE a.id<>11 ")
				.append((null != districtIds && districtIds.length > 0
						? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " "))
				.append((StringUtils.isNotEmpty(districtCode) ? " and a.districtcode='" + districtCode + "' " : " "))
				.append((null != chargeCategorys && chargeCategorys.length > 0
						? "and d.chargeCategory in('" + StringUtils.join(chargeCategorys, "','") + "') "
						: " "))
				.append((null != payItemsNames && payItemsNames.length > 0
						? "and d.payItemsName in('" + StringUtils.join(payItemsNames, "','") + "') "
						: " "))
				.append("AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) ")
				.append("AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid ")
				.append("AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id ")
				.append("AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivabledate>=DATE_ADD(LAST_DAY('" + endDate + "'),INTERVAL-3 MONTH) ")
				.append("AND d.receivableDate<=LAST_DAY('" + endDate
						+ "')) aa GROUP BY aa.unitCode) a2 ON a1.id=a2.id ")
				.append("left JOIN (SELECT aa.id,SUM(aa.amount1) AS amount36 ")
				.append("FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,")
				.append("(d.receivableamount-d.receivedamount) AS amount1 ")
				.append("FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e ")
				.append("WHERE a.id<>11 ")
				.append((null != districtIds && districtIds.length > 0
						? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " "))
				.append((StringUtils.isNotEmpty(districtCode) ? " and a.districtcode='" + districtCode + "' " : " "))
				.append((null != chargeCategorys && chargeCategorys.length > 0
						? "and d.chargeCategory in('" + StringUtils.join(chargeCategorys, "','") + "') "
						: " "))
				.append((null != payItemsNames && payItemsNames.length > 0
						? "and d.payItemsName in('" + StringUtils.join(payItemsNames, "','") + "') "
						: " "))
				.append("AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) ")
				.append("AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid ")
				.append("AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id ")
				.append("AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivabledate>=DATE_ADD(LAST_DAY('" + endDate + "'),INTERVAL-6 MONTH) ")
				.append("AND d.receivabledate<DATE_ADD(LAST_DAY('" + endDate + "'), INTERVAL-3 MONTH)")
				.append(") aa GROUP BY aa.unitCode) a3 ON a1.id=a3.id ")
				.append("left JOIN(SELECT aa.id,SUM(aa.amount1) AS amount6 ")
				.append("FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,")
				.append("(d.receivableamount-d.receivedamount) AS amount1 ")
				.append("FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e ")
				.append("WHERE a.id<>11 ")
				.append((null != districtIds && districtIds.length > 0
						? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " "))
				.append((StringUtils.isNotEmpty(districtCode) ? " and a.districtcode='" + districtCode + "' " : " "))
				.append((null != chargeCategorys && chargeCategorys.length > 0
						? "and d.chargeCategory in('" + StringUtils.join(chargeCategorys, "','") + "') "
						: " "))
				.append((null != payItemsNames && payItemsNames.length > 0
						? "and d.payItemsName in('" + StringUtils.join(payItemsNames, "','") + "') "
						: " "))
				.append("AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) ")
				.append("AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid ")
				.append("AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id ")
				.append("AND d.receivableamount-d.receivedamount<>0 ")
				.append("AND d.receivabledate<DATE_ADD(LAST_DAY('").append(endDate + "'),INTERVAL-6 MONTH) ")
				.append(") aa GROUP BY aa.unitCode) a4 ON a1.id=a4.id ");
		return sql.toString();
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public static void transNoReceivablesMeasuresExcel(String[] titles, List<CommunityNoReceivablesMeasuresVo> voList,
			String endDate, HttpServletResponse response) {

		Map<String, List<CommunityNoReceivablesMeasuresVo>> noReceivabelsMap = (HashMap<String, List<CommunityNoReceivablesMeasuresVo>>) voList
				.stream().collect(groupingBy(CommunityNoReceivablesMeasuresVo::getNewDistrictCode));

		List<CommunityEstateEntity> resultList = new ArrayList<>();

		TreeSet<String> keys = new TreeSet<>(Comparator.naturalOrder());
		noReceivabelsMap.keySet().forEach(o -> {
			keys.add(o);
		});

		XSSFWorkbook wb = new XSSFWorkbook();
		Map<String, XSSFCellStyle> cellStyle = ExcelUtil.getExcelStyle(wb);

		for (String o : keys) {

			LinkedList<CommunityNoReceivablesMeasuresVo> tempList = new LinkedList(noReceivabelsMap.get(o));

			Sheet sheet = wb.createSheet(o.substring(3));

			// 创建表头
			Row title1 = sheet.createRow(0);
			title1.setHeight((short) (30 * 20));
			Cell title_cell1 = title1.createCell(0);
			title_cell1.setCellStyle(cellStyle.get("title2"));
			title_cell1.setCellValue("应收未收报表(" + sheet.getSheetName() + ")");

			CellRangeAddress region3 = new CellRangeAddress(0, 0, 0, 10);
			sheet.addMergedRegion(region3);
			ExcelUtil.addMergeCellBorder(region3, sheet);

			Row title2 = sheet.createRow(2);
			for (int i = 0; i < titles.length; i++) {
				Cell tt = title2.createCell(i, CellType.STRING);
				tt.setCellValue(titles[i]);
				tt.setCellStyle(cellStyle.get("title"));
			}

			int column = 3;
			int sn = 1;
			for (CommunityNoReceivablesMeasuresVo vo : tempList) {
				Row row = sheet.createRow(column);

				Map<String,Object> result = parseNoReceivablesMeasures(vo,
						CommunityCache.estateList.get(vo.getEstateId()));
				if (null != result.get("estate")) {
					resultList.add((CommunityEstateEntity) result.get("estate"));
				}

				// 序号
				Cell cell1 = row.createCell(0, CellType.STRING);
				cell1.setCellStyle(cellStyle.get("cell_right2"));
				cell1.setCellValue(sn + "");

				// 单元编号
				Cell cell2 = row.createCell(1, CellType.STRING);
				cell2.setCellStyle(cellStyle.get("cell_left"));
				cell2.setCellValue(vo.getUnitCode());

				// 姓名
				Cell cell3 = row.createCell(2, CellType.STRING);
				cell3.setCellStyle(cellStyle.get("cell_left"));
				cell3.setCellValue(vo.getUserName());

				// 时段
				Cell cell4 = row.createCell(3, CellType.STRING);
				cell4.setCellStyle(cellStyle.get("cell_left"));
				cell4.setCellValue(vo.getPeriod());

				// 合计
				Cell cell5 = row.createCell(4, CellType.NUMERIC);
				cell5.setCellStyle(cellStyle.get("cell_right2"));
				cell5.setCellValue(new BigDecimal(vo.getTotalArrears()).doubleValue());

				// 违约金欠费金额
				Cell cell6 = row.createCell(5, CellType.NUMERIC);
				cell6.setCellStyle(cellStyle.get("cell_right2"));
				if (StringUtils.isNotEmpty(vo.getTotalSubArrears())
						&& new BigDecimal(vo.getTotalSubArrears()).doubleValue() != 0) {
					cell6.setCellValue(new BigDecimal(vo.getTotalSubArrears()).doubleValue());
				} else {
					cell6.setBlank();
				}

				// 原因
				Cell cell7 = row.createCell(6, CellType.STRING);
				cell7.setCellStyle(cellStyle.get("cell_left"));
				cell7.setCellValue("欠" + vo.getTotalChargecategory().replace(",", "、"));

				// 措施
				Cell cell8 = row.createCell(7, CellType.STRING);
				cell8.setCellStyle(cellStyle.get("cell_left"));

				cell8.setCellValue(vo.getNoReceivablesMeasures());

				// 进展
				Cell cell9 = row.createCell(8, CellType.STRING);
				cell9.setCellStyle(cellStyle.get("cell_left"));
				cell9.setCellValue(vo.getMeasures1());

				// 继续电话催费
				Cell cell10 = row.createCell(9, CellType.STRING);
				cell10.setCellStyle(cellStyle.get("cell_left"));
				cell10.setCellValue(vo.getMeasures2());

				// 减免违约金
				Cell cell11 = row.createCell(10, CellType.STRING);
				cell11.setCellStyle(cellStyle.get("cell_left"));
				cell11.setBlank();

				column++;
				sn++;
			}

			// 合计
			Row total = sheet.createRow(1);
			Cell t1 = total.createCell(0, CellType.STRING);
			t1.setCellStyle(cellStyle.get("total_double"));
			t1.setCellValue(sheet.getRow(column - 1).getCell(0).getStringCellValue());

			Cell t2 = total.createCell(1, CellType.STRING);
			t2.setCellStyle(cellStyle.get("total_double"));
			t2.setBlank();

			Cell t3 = total.createCell(2, CellType.STRING);
			t3.setCellStyle(cellStyle.get("total_double"));
			t3.setBlank();

			Cell t4 = total.createCell(3, CellType.STRING);
			t4.setCellStyle(cellStyle.get("total_double"));
			t4.setBlank();

			Cell t5 = total.createCell(4, CellType.NUMERIC);
			t5.setCellStyle(cellStyle.get("total_double"));
			t5.setCellFormula("SUM(" + sheet.getRow(3).getCell(4).getAddress() + ":"
					+ sheet.getRow(column - 1).getCell(4).getAddress() + ")");

			Cell t6 = total.createCell(5, CellType.NUMERIC);
			t6.setCellStyle(cellStyle.get("total_double"));
			t6.setCellFormula("SUM(" + sheet.getRow(3).getCell(5).getAddress() + ":"
					+ sheet.getRow(column - 1).getCell(5).getAddress() + ")");

			Cell t7 = total.createCell(6, CellType.STRING);
			t7.setCellStyle(cellStyle.get("total_double"));
			t7.setBlank();

			Cell t8 = total.createCell(7, CellType.STRING);
			t8.setCellStyle(cellStyle.get("total_double"));
			t8.setBlank();

			Cell t9 = total.createCell(8, CellType.STRING);
			t9.setCellStyle(cellStyle.get("total_double"));
			t9.setBlank();

			Cell t10 = total.createCell(9, CellType.STRING);
			t10.setCellStyle(cellStyle.get("total_double"));
			t10.setBlank();

			Cell t11 = total.createCell(10, CellType.STRING);
			t11.setCellStyle(cellStyle.get("total_double"));
			t11.setBlank();

			sheet.setColumnWidth(0, 5 * 256);
			sheet.autoSizeColumn(1, true);
			sheet.setColumnWidth(2, 10 * 256);
			sheet.autoSizeColumn(3, true);
			sheet.setColumnWidth(4, 10 * 256);
			sheet.setColumnWidth(5, 10 * 256);
			sheet.autoSizeColumn(6, true);
			sheet.setColumnWidth(7, 25 * 256);
			sheet.setColumnWidth(8, 25 * 256);
			sheet.setColumnWidth(9, 25 * 256);
			sheet.setColumnWidth(10, 25 * 256);

		}

		// 开始构建汇总页
		Sheet totalSheet = wb.createSheet("汇总");

		Row total_title = totalSheet.createRow(0);
		Cell title0 = total_title.createCell(0);
		title0.setCellStyle(cellStyle.get("title"));
		title0.setBlank();

		Cell title1 = total_title.createCell(1);
		title1.setCellStyle(cellStyle.get("title"));
		title1.setCellValue("总额");

		Cell title2 = total_title.createCell(2);
		title2.setCellStyle(cellStyle.get("title"));
		title2.setCellValue("违约金金额");

		Cell title3 = total_title.createCell(3);
		title3.setCellStyle(cellStyle.get("title"));
		title3.setCellValue("欠本金");

		Cell title4 = total_title.createCell(4);
		title4.setCellStyle(cellStyle.get("title"));
		title4.setBlank();

		Cell title5 = total_title.createCell(5);
		title5.setCellStyle(cellStyle.get("title"));
		title5.setBlank();

		for (int i = 1; i < wb.getNumberOfSheets(); i++) {
			Row r = totalSheet.createRow(i + 1);

			Sheet ss = wb.getSheetAt(i - 1);

			Cell cell1 = r.createCell(0, CellType.STRING);
			cell1.setCellStyle(cellStyle.get("cell_left"));
			cell1.setCellValue(ss.getSheetName());

			Cell cell2 = r.createCell(1, CellType.NUMERIC);
			cell2.setCellStyle(cellStyle.get("cell_right2"));
			cell2.setCellFormula(ss.getSheetName() + "!" + ss.getRow(1).getCell(4).getAddress());

			Cell cell3 = r.createCell(2, CellType.NUMERIC);
			cell3.setCellStyle(cellStyle.get("cell_right2"));
			cell3.setCellFormula(ss.getSheetName() + "!" + ss.getRow(1).getCell(5).getAddress());

			Cell cell4 = r.createCell(3, CellType.NUMERIC);
			cell4.setCellStyle(cellStyle.get("cell_right2"));
			cell4.setCellFormula(r.getCell(1).getAddress() + "-" + r.getCell(2).getAddress());

			Cell cell5 = r.createCell(4, CellType.STRING);
			cell5.setCellStyle(cellStyle.get("cell_left"));
			cell5.setBlank();

			Cell cell6 = r.createCell(5, CellType.STRING);
			cell6.setCellStyle(cellStyle.get("cell_left"));
			cell6.setBlank();
		}

		// 构建合计行
		Row total = totalSheet.createRow(1);
		Cell t1 = total.createCell(0, CellType.STRING);
		t1.setCellStyle(cellStyle.get("cell_left"));
		t1.setCellValue("合计");

		Cell t2 = total.createCell(1, CellType.NUMERIC);
		t2.setCellStyle(cellStyle.get("cell_right2"));
		t2.setCellFormula("sum(" + totalSheet.getRow(2).getCell(1).getAddress() + ":"
				+ totalSheet.getRow(wb.getNumberOfSheets()).getCell(1).getAddress() + ")");

		Cell t3 = total.createCell(2, CellType.NUMERIC);
		t3.setCellStyle(cellStyle.get("cell_right2"));
		t3.setCellFormula("sum(" + totalSheet.getRow(2).getCell(2).getAddress() + ":"
				+ totalSheet.getRow(wb.getNumberOfSheets()).getCell(2).getAddress() + ")");

		Cell t4 = total.createCell(3, CellType.NUMERIC);
		t4.setCellStyle(cellStyle.get("cell_right2"));
		t4.setCellFormula(
				totalSheet.getRow(1).getCell(1).getAddress() + "-" + totalSheet.getRow(1).getCell(2).getAddress());

		Cell t5 = total.createCell(4, CellType.STRING);
		t5.setCellStyle(cellStyle.get("cell_left"));
		t5.setCellValue("继续电话催缴");

		Cell t6 = total.createCell(5, CellType.STRING);
		t6.setCellStyle(cellStyle.get("cell_left"));
		t6.setCellValue("建议减免滞纳金");

		totalSheet.setColumnWidth(0, 15 * 256);
		totalSheet.setColumnWidth(1, 15 * 256);
		totalSheet.setColumnWidth(2, 15 * 256);
		totalSheet.setColumnWidth(3, 15 * 256);
		totalSheet.setColumnWidth(4, 15 * 256);
		totalSheet.setColumnWidth(5, 15 * 256);

		wb.setSheetOrder("汇总", 0);
		wb.setActiveSheet(0);

		String fileName = "截止" + endDate.replaceFirst("-", "年").replaceFirst("-", "月") + "应收未收措施表.xlsx";

		try {
			ExcelExportUtil.export(response, wb, fileName);
		} catch (IOException e) {
			log.error(e.getMessage());
		}

	}

	public static void transNoReceivablesSummaryExcel(String[] titles1, String[] titles2,
			List<CommunityNoReceivablesSummaryVo> voList, String endDate, HttpServletResponse response) {

		XSSFWorkbook wb = new XSSFWorkbook();
		Map<String, XSSFCellStyle> cellStyle = ExcelUtil.getExcelStyle(wb);

		Sheet sheet = wb.createSheet("截止" + endDate + "欠费分析");

		// 创建表头
		Row title1 = sheet.createRow(0);
		for (int i = 0; i < titles1.length; i++) {
			Cell tt = title1.createCell(i, CellType.STRING);
			tt.setCellValue(titles1[i]);
			tt.setCellStyle(cellStyle.get("title"));

			if (i >= 1 && i <= 7) {
				CellRangeAddress region1 = new CellRangeAddress(0, 0, 2 * i - 1, 2 * i);
				sheet.addMergedRegion(region1);
				ExcelUtil.addMergeCellBorder(region1, sheet);
			}
		}

		Row title2 = sheet.createRow(1);
		for (int i = 0; i < titles2.length; i++) {
			Cell tt = title2.createCell(i, CellType.STRING);
			tt.setCellValue(titles2[i]);
			tt.setCellStyle(cellStyle.get("title"));
		}

		CellRangeAddress region1 = new CellRangeAddress(0, 1, 0, 0);
		sheet.addMergedRegion(region1);
		ExcelUtil.addMergeCellBorder(region1, sheet);

		int rowNum = 2;
		for (CommunityNoReceivablesSummaryVo vo : voList) {
			Row row = sheet.createRow(rowNum);

			// 楼盘
			Cell cell1 = row.createCell(0, CellType.STRING);
			cell1.setCellStyle(cellStyle.get("cell_left"));
			cell1.setCellValue(vo.getDistrictName());

			// 管理费户数
			Cell cell2 = row.createCell(1, CellType.NUMERIC);
			cell2.setCellStyle(cellStyle.get("cell_right1"));
			if (StringUtils.isNotEmpty(vo.getItem1())) {
				cell2.setCellValue(Integer.parseInt(vo.getItem1()));
			} else {
				cell2.setBlank();
			}

			// 管理费金额
			Cell cell3 = row.createCell(2, CellType.NUMERIC);
			cell3.setCellStyle(cellStyle.get("cell_right2"));
			if (StringUtils.isNotEmpty(vo.getFee1())) {
				cell3.setCellValue(new BigDecimal(vo.getFee1()).doubleValue());
			} else {
				cell3.setBlank();
			}

			// 公摊费户数
			Cell cell4 = row.createCell(3, CellType.NUMERIC);
			cell4.setCellStyle(cellStyle.get("cell_right1"));
			if (StringUtils.isNotEmpty(vo.getItem2())) {
				cell4.setCellValue(Integer.parseInt(vo.getItem2()));
			} else {
				cell4.setBlank();
			}

			// 公摊费金额
			Cell cell5 = row.createCell(4, CellType.NUMERIC);
			cell5.setCellStyle(cellStyle.get("cell_right2"));
			if (StringUtils.isNotEmpty(vo.getFee2())) {
				cell5.setCellValue(new BigDecimal(vo.getFee2()).doubleValue());
			} else {
				cell5.setBlank();
			}

			// 代收水电户数
			Cell cell6 = row.createCell(5, CellType.NUMERIC);
			cell6.setCellStyle(cellStyle.get("cell_right1"));
			if (StringUtils.isNotEmpty(vo.getItem3())) {
				cell6.setCellValue(Integer.parseInt(vo.getItem3()));
			} else {
				cell6.setBlank();
			}

			// 代收水电金额
			Cell cell7 = row.createCell(6, CellType.NUMERIC);
			cell7.setCellStyle(cellStyle.get("cell_right2"));
			if (StringUtils.isNotEmpty(vo.getFee3())) {
				cell7.setCellValue(new BigDecimal(vo.getFee3()).doubleValue());
			} else {
				cell7.setBlank();
			}

			// 违约金户数
			Cell cell8 = row.createCell(7, CellType.NUMERIC);
			cell8.setCellStyle(cellStyle.get("cell_right1"));
			if (StringUtils.isNotEmpty(vo.getItem4())) {
				cell8.setCellValue(Integer.parseInt(vo.getItem4()));
			} else {
				cell8.setBlank();
			}

			// 违约金金额
			Cell cell9 = row.createCell(8, CellType.NUMERIC);
			cell9.setCellStyle(cellStyle.get("cell_right2"));
			if (StringUtils.isNotEmpty(vo.getFee4())) {
				cell9.setCellValue(new BigDecimal(vo.getFee4()).doubleValue());
			} else {
				cell9.setBlank();
			}

			// 车位费户数
			Cell cell10 = row.createCell(9, CellType.NUMERIC);
			cell10.setCellStyle(cellStyle.get("cell_right1"));
			if (StringUtils.isNotEmpty(vo.getItem5())) {
				cell10.setCellValue(Integer.parseInt(vo.getItem5()));
			} else {
				cell10.setBlank();
			}

			// 车位费金额
			Cell cell11 = row.createCell(10, CellType.NUMERIC);
			cell11.setCellStyle(cellStyle.get("cell_right2"));
			if (StringUtils.isNotEmpty(vo.getFee5())) {
				cell11.setCellValue(new BigDecimal(vo.getFee5()).doubleValue());
			} else {
				cell11.setBlank();
			}

			// 3个月内户数
			Cell cell12 = row.createCell(11, CellType.NUMERIC);
			cell12.setCellStyle(cellStyle.get("cell_right1"));
			if (StringUtils.isNotEmpty(vo.getAmount3Num())) {
				cell12.setCellValue(Integer.parseInt(vo.getAmount3Num()));
			} else {
				cell12.setBlank();
			}

			// 3个月内金额
			Cell cell13 = row.createCell(12, CellType.NUMERIC);
			cell13.setCellStyle(cellStyle.get("cell_right2"));
			if (StringUtils.isNotEmpty(vo.getAmount3())) {
				cell13.setCellValue(new BigDecimal(vo.getAmount3()).doubleValue());
			} else {
				cell13.setBlank();
			}

			// 3个月以上户数
			Cell cell14 = row.createCell(13, CellType.NUMERIC);
			cell14.setCellStyle(cellStyle.get("cell_right1"));
			if (StringUtils.isNotEmpty(vo.getAmount6Num())) {
				cell14.setCellValue(Integer.parseInt(vo.getAmount6Num()));
			} else {
				cell14.setBlank();
			}

			// 3个月以上金额
			Cell cell15 = row.createCell(14, CellType.NUMERIC);
			cell15.setCellStyle(cellStyle.get("cell_right2"));
			if (StringUtils.isNotEmpty(vo.getAmount6())) {
				cell15.setCellValue(new BigDecimal(vo.getAmount6()).doubleValue());
			} else {
				cell15.setBlank();
			}

			rowNum++;

		}

		// 合计行
		Row total = sheet.createRow(rowNum);
		Cell totalStr = total.createCell(0, CellType.STRING);
		totalStr.setCellValue("合计");
		totalStr.setCellStyle(cellStyle.get("total_double"));

		int totalRowColumns = title2.getLastCellNum();
		for (int i = 1; i < totalRowColumns; i++) {
			Cell tt = total.createCell(i, CellType.NUMERIC);
			if (i % 2 == 0) {
				tt.setCellStyle(cellStyle.get("total_double"));
			} else {
				tt.setCellStyle(cellStyle.get("total_int"));
			}
			tt.setCellFormula("sum(" + sheet.getRow(2).getCell(i).getAddress() + ":"
					+ sheet.getRow(rowNum - 1).getCell(i).getAddress() + ")");
			sheet.autoSizeColumn(i);
		}

		sheet.autoSizeColumn(0);
		String fileName = "截止" + endDate.replaceFirst("-", "年").replaceFirst("-", "月") + "应收未收统计分析表.xlsx";

		try {
			ExcelExportUtil.export(response, wb, fileName);
		} catch (IOException e) {
			log.error(e.getMessage());
		}

	}

	public static Map<String, Object> getNoReceivableList(Integer[] districtIds,Integer[] buildingIds, String[] chargeCategorys,
			String[] payItemsNames, String endDate) {
		Map<String, Object> res = new HashMap<>();

		String[] title = { "楼盘名称", "楼阁", "单元编号", "姓名", "电话", "建筑面积", "房屋状态", "划账银行", "银行账户", "账户名" };
		List<CommunityNoReceivablesVo> dataList = new LinkedList<>();

		Set<String> feeTitle = new TreeSet<>();

		// 获取动态sql
		String groupSql = getNoReceivablesSql(districtIds, buildingIds,chargeCategorys, payItemsNames, endDate);

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {
				Statement st = conn.createStatement();
				ResultSet group_rs = st.executeQuery(groupSql);

				while (group_rs.next()) {
					String districtCode = group_rs.getString("districtCode");
					String subSql = group_rs.getString("subSql");
					ResultSet subrs = st.executeQuery(subSql);

					while (subrs.next()) {
						ResultSetMetaData rsmd = subrs.getMetaData();
						CommunityNoReceivablesVo vo = new CommunityNoReceivablesVo();
						vo.setDistrictCode(districtCode.length() == 1 ? "0" + districtCode : districtCode);
						vo.setDistrictName(subrs.getString("districtName"));
						vo.setBuildingName(subrs.getString("buildingName"));
						vo.setUnitCode(subrs.getString("unitCode"));
						vo.setUserName(subrs.getString("userName"));
						vo.setHomephone(subrs.getString("homePhone"));
						vo.setBuildingArea(subrs.getString("buildingArea"));
						vo.setEstateState(subrs.getString("estateState"));
						vo.setBankNmae(subrs.getString("bankName"));
						vo.setBankAccount(subrs.getString("bankAccount"));
						vo.setAccountName(subrs.getString("accountName"));
						vo.setAmount3(subrs.getString("amount3"));
						vo.setAmount36(subrs.getString("amount36"));
						vo.setAmount6(subrs.getString("amount6"));
						vo.setTotalAmount(subrs.getString("totalAmount"));
						vo.setBuildingType(subrs.getString("buildingType"));

						Map<String, String> itemMap = new TreeMap<>();
						for (int i = 17; i <= rsmd.getColumnCount(); i += 2) {
							itemMap.put(rsmd.getColumnName(i), subrs.getString(i));
						}
						Map<String, String> feeMap = new TreeMap<>();
						for (int i = 18; i <= rsmd.getColumnCount(); i += 2) {
							feeTitle.add(rsmd.getColumnName(i));
							feeMap.put(rsmd.getColumnName(i), subrs.getString(i));
						}
						vo.setItemMap(itemMap);
						vo.setFeeMap(feeMap);
						dataList.add(vo);
					}
				}
			}
		});

		session.close();

		for (String fee : feeTitle) {
			title = ArrayUtil.insert(title, title.length, fee);
		}

		res.put("title", title);
		res.put("data", dataList);

		return res;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static void transNoReceivablesExcel(HttpServletResponse response) {
		// 获取缓存数据并进行处理
		Map<String, Map<String, Object>> temp1 = CommunityCache.statisticsCache.get("应收未收报表");
		String key = "";
		for (String o : temp1.keySet()) {
			key = o;
		}

		Map<String, Object> temp2 = temp1.get(key);
		String[] titles = (String[]) temp2.get("title");
		for (int i = 1; i <= 3; i++) {
			titles = ArrayUtil.insert(titles, titles.length, "欠款账龄");
		}

		titles = ArrayUtil.insert(titles, titles.length, "合计");

		List<CommunityNoReceivablesVo> dataList = (List<CommunityNoReceivablesVo>) temp2.get("data");

		// 将缓存的队列数据按楼盘编码进行排序
		Map<String, List<CommunityNoReceivablesVo>> noReceivalbesMap = (HashMap<String, List<CommunityNoReceivablesVo>>) dataList
				.stream().collect(groupingBy(CommunityNoReceivablesVo::getNewDistrictCode));

		if (noReceivalbesMap.size() > 1) {
			noReceivalbesMap.put("000全区", dataList);
		}

		TreeSet<String> keys = new TreeSet<>(Comparator.naturalOrder());
		noReceivalbesMap.keySet().forEach(o -> {
			keys.add(o);
		});

		XSSFWorkbook wb = new XSSFWorkbook();
		Map<String, XSSFCellStyle> cellStyle = ExcelUtil.getExcelStyle(wb);

		for (String o : keys) {
			Sheet sheet = wb.createSheet(o.substring(3));

			LinkedList<CommunityNoReceivablesVo> tempList = new LinkedList(noReceivalbesMap.get(o));

			// 构建表头
			Row title1 = sheet.createRow(0);
			Row title2 = sheet.createRow(1);
			createTitle(sheet, cellStyle, title1, title2, titles);

			// 获取每楼盘下单元单元编号进行排序
//			List<CommunityNoReceivablesVo> unitList = tempList.stream()
//					.sorted(Comparator.comparing(CommunityNoReceivablesVo::getUnitCode)).collect(Collectors.toList());

			int rowNumber = 2;
			for (CommunityNoReceivablesVo vo : tempList) {
				Row row = sheet.createRow(rowNumber);
				// 楼盘名称
				Cell cell0 = row.createCell(0, CellType.STRING);
				cell0.setCellStyle(cellStyle.get("cell_left"));
				cell0.setCellValue(vo.getDistrictName());

				// 楼阁
				Cell cell1 = row.createCell(1, CellType.STRING);
				cell1.setCellStyle(cellStyle.get("cell_left"));
				cell1.setCellValue(vo.getBuildingName());

				// 单元编号
				Cell cell2 = row.createCell(2, CellType.STRING);
				cell2.setCellStyle(cellStyle.get("cell_left"));
				cell2.setCellValue(vo.getUnitCode());

				// 姓名
				Cell cell3 = row.createCell(3, CellType.STRING);
				cell3.setCellStyle(cellStyle.get("cell_left"));
				cell3.setCellValue(vo.getUserName());

				// 电话
				Cell cell4 = row.createCell(4, CellType.STRING);
				cell4.setCellStyle(cellStyle.get("cell_left"));
				cell4.setCellValue(vo.getHomephone());

				// 建筑面积
				Cell cell5 = row.createCell(5, CellType.STRING);
				cell5.setCellStyle(cellStyle.get("cell_left"));
				cell5.setCellValue(vo.getBuildingArea());

				// 房屋状态
				Cell cell6 = row.createCell(6, CellType.STRING);
				cell6.setCellStyle(cellStyle.get("cell"));
				cell6.setCellValue(vo.getEstateState());

				// 划账银行
				Cell cell7 = row.createCell(7, CellType.STRING);
				cell7.setCellStyle(cellStyle.get("cell_left"));
				cell7.setCellValue(vo.getBankNmae());

				// 银行账户
				Cell cell8 = row.createCell(8, CellType.STRING);
				cell8.setCellStyle(cellStyle.get("cell_left"));
				cell8.setCellValue(vo.getBankAccount());

				// 账户名
				Cell cell9 = row.createCell(9, CellType.STRING);
				cell9.setCellStyle(cellStyle.get("cell_left"));
				cell9.setCellValue(vo.getAccountName());

				Map<String, String> itemMap = vo.getItemMap();
				Map<String, String> feeMap = vo.getFeeMap();

				if (sheet.getSheetName().equals("全区")) {
					for (int i = 1; i <= titles.length - 14; i++) {
						Cell item = row.createCell(2 * i + 8, CellType.STRING);
						item.setCellStyle(cellStyle.get("cell_left"));
						Cell data = row.createCell(2 * i + 9, CellType.NUMERIC);
						data.setCellStyle(cellStyle.get("cell_right2"));
						String payItemsName = titles[i + 9];
						if (feeMap.containsKey(payItemsName) && itemMap.containsKey(payItemsName + "属期")
								&& StringUtils.isNotEmpty(feeMap.get(payItemsName))) {
							item.setCellValue(itemMap.get(payItemsName + "属期"));
							data.setCellValue(new BigDecimal(feeMap.get(payItemsName)).doubleValue());
						} else {
							item.setBlank();
							data.setBlank();
						}
					}

					Cell d3_1 = row.createCell(2 * (titles.length - 14) + 10, CellType.NUMERIC);
					d3_1.setCellStyle(cellStyle.get("cell_right2"));
					Cell d3_2 = row.createCell(2 * (titles.length - 14) + 11, CellType.NUMERIC);
					d3_2.setCellStyle(cellStyle.get("cell_right2"));
					Cell d3_3 = row.createCell(2 * (titles.length - 14) + 12, CellType.NUMERIC);
					d3_3.setCellStyle(cellStyle.get("cell_right2"));
					if (null != vo.getAmount3() && new BigDecimal(vo.getAmount3()).doubleValue() > 0) {
						d3_1.setCellValue(new BigDecimal(vo.getAmount3()).doubleValue());
					} else {
						d3_1.setBlank();
					}

					if (null != vo.getAmount36() && new BigDecimal(vo.getAmount36()).doubleValue() > 0) {
						d3_2.setCellValue(new BigDecimal(vo.getAmount36()).doubleValue());
					} else {
						d3_2.setBlank();
					}

					if (null != vo.getAmount6() && new BigDecimal(vo.getAmount6()).doubleValue() > 0) {
						d3_3.setCellValue(new BigDecimal(vo.getAmount6()).doubleValue());
					} else {
						d3_3.setBlank();
					}

					Cell subtotal = row.createCell(2 * (titles.length - 14) + 13, CellType.NUMERIC);
					subtotal.setCellStyle(cellStyle.get("total_double"));
					subtotal.setCellValue(new BigDecimal(vo.getTotalAmount()).doubleValue());
				} else {

					Object[] payItemsNmaes = feeMap.keySet().toArray();
					for (int i = 1; i <= feeMap.size(); i++) {
						if (null == title1.getCell(2 * i + 8)) {
							Cell t1 = title1.createCell(2 * i + 8, CellType.STRING);
							t1.setCellStyle(cellStyle.get("title"));
							t1.setCellValue(payItemsNmaes[i - 1].toString());

							CellRangeAddress region = new CellRangeAddress(0, 0, 2 * i + 8, 2 * i + 9);
							sheet.addMergedRegion(region);
							ExcelUtil.addMergeCellBorder(region, sheet);

							Cell t2 = title2.createCell(2 * i + 8, CellType.STRING);
							t2.setCellStyle(cellStyle.get("title"));
							t2.setCellValue("属期");

							Cell t3 = title2.createCell(2 * i + 9, CellType.STRING);
							t3.setCellStyle(cellStyle.get("title"));
							t3.setCellValue("金额");
						}

						Cell item = row.createCell(2 * i + 8, CellType.STRING);
						item.setCellStyle(cellStyle.get("cell_left"));
						Cell data = row.createCell(2 * i + 9, CellType.NUMERIC);
						data.setCellStyle(cellStyle.get("cell_right2"));

						if (feeMap.containsKey(payItemsNmaes[i - 1].toString())
								&& itemMap.containsKey(payItemsNmaes[i - 1].toString() + "属期")
								&& StringUtils.isNotEmpty(feeMap.get(payItemsNmaes[i - 1].toString()))) {
							item.setCellValue(itemMap.get(payItemsNmaes[i - 1].toString() + "属期"));
							data.setCellValue(
									new BigDecimal(feeMap.get(payItemsNmaes[i - 1].toString())).doubleValue());
						} else {
							item.setBlank();
							data.setBlank();
						}
					}

					if (null == title1.getCell(2 * feeMap.size() + 10)) {
						Cell t1 = title1.createCell(2 * feeMap.size() + 10, CellType.STRING);
						t1.setCellStyle(cellStyle.get("title"));
						t1.setCellValue("欠款账龄");

						CellRangeAddress region1 = new CellRangeAddress(0, 0, 2 * feeMap.size() + 10,
								2 * feeMap.size() + 12);
						sheet.addMergedRegion(region1);
						ExcelUtil.addMergeCellBorder(region1, sheet);

						Cell t3_1 = title2.createCell(2 * feeMap.size() + 10, CellType.STRING);
						t3_1.setCellStyle(cellStyle.get("title"));
						t3_1.setCellValue("三个月");
						Cell t3_2 = title2.createCell(2 * feeMap.size() + 11, CellType.STRING);
						t3_2.setCellStyle(cellStyle.get("title"));
						t3_2.setCellValue("三至六个月");
						Cell t3_3 = title2.createCell(2 * feeMap.size() + 12, CellType.STRING);
						t3_3.setCellStyle(cellStyle.get("title"));
						t3_3.setCellValue("半年以上");

						Cell t2 = title1.createCell(2 * feeMap.size() + 13, CellType.STRING);
						t2.setCellStyle(cellStyle.get("title"));
						t2.setCellValue("合计");

						CellRangeAddress region2 = new CellRangeAddress(0, 1, 2 * feeMap.size() + 13,
								2 * feeMap.size() + 13);
						sheet.addMergedRegion(region2);
						ExcelUtil.addMergeCellBorder(region2, sheet);
					}

					Cell d3_1 = row.createCell(2 * feeMap.size() + 10, CellType.NUMERIC);
					d3_1.setCellStyle(cellStyle.get("cell_right2"));
					Cell d3_2 = row.createCell(2 * feeMap.size() + 11, CellType.NUMERIC);
					d3_2.setCellStyle(cellStyle.get("cell_right2"));
					Cell d3_3 = row.createCell(2 * feeMap.size() + 12, CellType.NUMERIC);
					d3_3.setCellStyle(cellStyle.get("cell_right2"));
					if (null != vo.getAmount3() && new BigDecimal(vo.getAmount3()).doubleValue() > 0) {
						d3_1.setCellValue(new BigDecimal(vo.getAmount3()).doubleValue());
					} else {
						d3_1.setBlank();
					}

					if (null != vo.getAmount36() && new BigDecimal(vo.getAmount36()).doubleValue() > 0) {
						d3_2.setCellValue(new BigDecimal(vo.getAmount36()).doubleValue());
					} else {
						d3_2.setBlank();
					}

					if (null != vo.getAmount6() && new BigDecimal(vo.getAmount6()).doubleValue() > 0) {
						d3_3.setCellValue(new BigDecimal(vo.getAmount6()).doubleValue());
					} else {
						d3_3.setBlank();
					}

					Cell subtotal = row.createCell(2 * feeMap.size() + 13, CellType.NUMERIC);
					subtotal.setCellStyle(cellStyle.get("total_double"));
					subtotal.setCellValue(new BigDecimal(vo.getTotalAmount()).doubleValue());
				}

				rowNumber++;
			}

			// 合计行
			Row total = sheet.createRow(rowNumber);
			Cell totalStr = total.createCell(0, CellType.STRING);
			totalStr.setCellValue("合计");
			totalStr.setCellStyle(cellStyle.get("total_double"));

			CellRangeAddress region3 = new CellRangeAddress(rowNumber, rowNumber, 0, 9);
			sheet.addMergedRegion(region3);
			ExcelUtil.addMergeCellBorder(region3, sheet);

			int totalRowColumns = title2.getLastCellNum();
			for (int i = 1; i <= totalRowColumns - 10; i++) {
				Cell tt = total.createCell(i + 9, CellType.NUMERIC);
				tt.setCellStyle(cellStyle.get("total_double"));

				if ((i <= totalRowColumns - 14 && i % 2 == 0) || i > totalRowColumns - 14) {
					tt.setCellFormula("sum(" + sheet.getRow(2).getCell(i + 9).getAddress() + ":"
							+ sheet.getRow(rowNumber - 1).getCell(i + 9).getAddress() + ")");
					sheet.setColumnWidth(i + 9, 12 * 256);
				} else {
					tt.setBlank();
					sheet.setColumnWidth(i + 9, 16 * 256);
				}
			}
		}

		String fileName = "应收未收报表" + DateUtil.formatByStyle(new Date(), "_yyyy_MM_dd_HH_mm_ss") + ".xlsx";

		try {
			ExcelExportUtil.export(response, wb, fileName);
		} catch (IOException e) {
			log.error(e.getMessage());
		}
	}

	private static void createTitle(Sheet sheet, Map<String, XSSFCellStyle> cellStyle, Row title1, Row title2,
			String[] titles) {
		int columnCount = titles.length - 4;

		for (int i = 0; i < columnCount; i++) {
			if (i <= 9) {
				Cell tt = title1.createCell(i, CellType.STRING);
				tt.setCellValue(titles[i]);
				tt.setCellStyle(cellStyle.get("title"));

				CellRangeAddress region = new CellRangeAddress(0, 1, i, i);
				sheet.addMergedRegion(region);
				ExcelUtil.addMergeCellBorder(region, sheet);
			} else {
				if (sheet.getSheetName().equals("全区")) {
					Cell t1 = title1.createCell((2 * (i - 9) - 1) + 9, CellType.STRING);
					t1.setCellValue(titles[i]);
					t1.setCellStyle(cellStyle.get("title"));

					CellRangeAddress region = new CellRangeAddress(0, 0, (2 * (i - 9) - 1) + 9, 2 * (i - 9) + 9);
					sheet.addMergedRegion(region);
					ExcelUtil.addMergeCellBorder(region, sheet);

					Cell t2_1 = title2.createCell((2 * (i - 9) - 1) + 9, CellType.STRING);
					Cell t2_2 = title2.createCell(2 * (i - 9) + 9, CellType.STRING);
					t2_1.setCellValue("属期");
					t2_2.setCellValue("金额");
					t2_1.setCellStyle(cellStyle.get("title"));
					t2_2.setCellStyle(cellStyle.get("title"));

				}
			}
		}

		if (sheet.getSheetName().equals("全区")) {

			Cell t1 = title1.createCell(2 * (titles.length - 14) + 10, CellType.STRING);
			t1.setCellStyle(cellStyle.get("title"));
			t1.setCellValue("欠款账龄");

			CellRangeAddress region1 = new CellRangeAddress(0, 0, 2 * (titles.length - 14) + 10,
					2 * (titles.length - 14) + 12);
			sheet.addMergedRegion(region1);
			ExcelUtil.addMergeCellBorder(region1, sheet);

			Cell t3_1 = title2.createCell(2 * (titles.length - 14) + 10, CellType.STRING);
			t3_1.setCellStyle(cellStyle.get("title"));
			t3_1.setCellValue("三个月");
			Cell t3_2 = title2.createCell(2 * (titles.length - 14) + 11, CellType.STRING);
			t3_2.setCellStyle(cellStyle.get("title"));
			t3_2.setCellValue("三至六个月");
			Cell t3_3 = title2.createCell(2 * (titles.length - 14) + 12, CellType.STRING);
			t3_3.setCellStyle(cellStyle.get("title"));
			t3_3.setCellValue("半年以上");

			Cell t2 = title1.createCell(2 * (titles.length - 14) + 13, CellType.STRING);
			t2.setCellStyle(cellStyle.get("title"));
			t2.setCellValue("合计");

			CellRangeAddress region2 = new CellRangeAddress(0, 1, 2 * (titles.length - 14) + 13,
					2 * (titles.length - 14) + 13);
			sheet.addMergedRegion(region2);
			ExcelUtil.addMergeCellBorder(region2, sheet);
		}
	}

	private void batchUpdateEstateDate(List<CommunityEstateEntity> estateList) {

		if (estateList.size() > 0) {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			session.doWork(new Work() {
				@Override
				public void execute(Connection conn) throws SQLException {
					StringBuilder sql = new StringBuilder("update t_community_property set reservedField=? where id=?");
					Integer transCount = 0;
					PreparedStatement stmt = conn.prepareStatement(sql.toString());
					conn.setAutoCommit(false);
					for (CommunityEstateEntity estate : estateList) {
						stmt.setString(1, estate.getReservedField());
						stmt.setInt(2, estate.getId());
						stmt.addBatch();
						transCount++;
						if (transCount % 400 == 0) {
							stmt.executeBatch();
							conn.commit();
						}
					}
					stmt.executeBatch();
					conn.commit();
					log.info("更改" + transCount + "条单元应收措施数据成功！！！");
				}
			});
			session.close();
		}
	}

	@SuppressWarnings("unchecked")
	private static Map<String, Object> parseNoReceivablesMeasures(CommunityNoReceivablesMeasuresVo vo,
			CommunityEstateEntity estate) {
		Map<String, Object> result = new HashMap<>();
		
		ObjectMapper mapper = new ObjectMapper();

		String[] period = vo.getPeriod().split("~");
		// 计算欠费相差月份
		LocalDate startDate = LocalDate.parse(period[0] + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		LocalDate endDate = LocalDate.parse(period[1] + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
		
		LocalDate measureDate=endDate.plusMonths(1);
				
		Period next = Period.between(startDate, endDate);
		Integer periodMonth = next.getMonths() + 1;

		String totalChargeCategory = vo.getTotalChargecategory();
		List<String> categoryList = (List<String>) Convert.toList(totalChargeCategory.replaceAll("、", ","));
		ReservedFieldVo fieldVo = null;
		if (null != estate) {
			try {
				fieldVo = mapper.readValue(estate.getReservedField(), ReservedFieldVo.class);

				if ((periodMonth == 1 || !categoryList.contains("管理费")) && !fieldVo.getNoReceivablesMeasures()
						.contains(measureDate.getYear() + "年" + measureDate.getMonthValue()  + "月电话催缴")) {
					fieldVo.getNoReceivablesMeasures()
							.add(measureDate.getYear() + "年" + measureDate.getMonthValue() + "月继续电话催缴");
				} else if (((periodMonth > 1 && periodMonth <= 2) || periodMonth == 12)
						&& !fieldVo.getNoReceivablesMeasures()
								.contains(measureDate.getYear() + "年" + measureDate.getMonthValue() + "月发催缴通知书")) {
					fieldVo.getNoReceivablesMeasures()
							.add(measureDate.getYear() + "年" + measureDate.getMonthValue() + "月发催缴通知书");
				} else if (periodMonth == 3 && !fieldVo.getMeasures1()
						.contains(measureDate.getYear() + "年" + measureDate.getMonthValue() + "月发律师信")) {
					fieldVo.getMeasures1().add(measureDate.getYear() + "年" + measureDate.getMonthValue() + "月发律师信");
				} else if (periodMonth > 3 && !fieldVo.getMeasures2()
						.contains(measureDate.getYear() + "年" + measureDate.getMonthValue()  + "月准备资料起诉")) {
					fieldVo.getMeasures2()
							.add(measureDate.getYear() + "年" + measureDate.getMonthValue() + "月准备资料起诉");
				}

				estate.setReservedField(mapper.writeValueAsString(fieldVo));

			} catch (JsonProcessingException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		result.put("estate", estate);
		result.put("fieldVo", fieldVo);
		return result;
	}

	public static void main(String[] args) throws JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();

		CommunityNoReceivablesMeasuresVo vo = new CommunityNoReceivablesMeasuresVo();
		vo.setDistrictCode("01");
		vo.setDistrictName("3区");
		vo.setUnitCode("3-01-0101");
		vo.setUserName("123");
		vo.setPeriod("2023-11~2023-12");
		vo.setTotalArrears("123.45");
		vo.setTotalSubArrears("0");
//		vo.setTotalChargecategory("分摊费、管理费、违约金");
		vo.setTotalChargecategory("分摊费、违约金");

		CommunityEstateEntity estate = new CommunityEstateEntity();
		estate.setUnitCode("3-01-0101");

		ReservedFieldVo rf = new ReservedFieldVo();
		rf.setNoReceivablesMeasures(new LinkedList<String>());
		rf.setMeasures1(new LinkedList<String>());
		rf.setMeasures2(new LinkedList<String>());

		estate.setReservedField(mapper.writeValueAsString(rf));

		System.out.println(mapper.writeValueAsString(estate));
		Map<String,Object> result = parseNoReceivablesMeasures(vo, estate);
		System.out.println(mapper.writeValueAsString(result.get("fieldVo")));

	}

}
