package com.foshan.service.community.task;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.ParseException;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.persistence.LockModeType;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.jdbc.Work;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.foshan.entity.DictionaryDataEntity;
import com.foshan.entity.SnEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityPayItemsPriceEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.community.context.CommunityContext;
import com.foshan.entity.community.vo.CalBreachAmountChangeAmountHistoryResultVo;
import com.foshan.entity.community.vo.CancelBenefitAmountChangeDetailItem;
import com.foshan.entity.community.vo.CancelBenefitDetailItemVo;
import com.foshan.entity.community.vo.CancelBenefitDetailVo;
import com.foshan.entity.community.vo.EstateReceivablesVo;
import com.foshan.form.sms.request.SmsReq;
import com.foshan.service.community.impl.CommunityReceivablesServiceImpl;
import com.foshan.service.quartz.IFaithJob;
import com.foshan.service.sms.ISmsService;
import com.foshan.util.DateUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.JsonUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityCache;
import com.foshan.util.community.CommunityContextInfo;
import com.foshan.util.community.CommunityDateUtil;
import com.foshan.util.community.JepUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CommunityReceivableTask implements IFaithJob {
	public static final String RECEIVABLESNO_SN_TYPE = "RECEIVABLESNO";
//	public static Integer benefitPayItemId = 190;
//	public static Date benefitStartDate = null;
	public static Date cutoverDate = null;

//	public static BigDecimal orginPrice = new BigDecimal("3.18");

	@SuppressWarnings("unchecked")
	public static void calManagerFee() {
//		CommunityContextInfo communityContextInfo = (CommunityContextInfo)SpringHandler.getBean("communityContextInfo");
//		benefitPayItemId = communityContextInfo.getBenefitPayitemId();
//		benefitStartDate = communityContextInfo.getBenefitProlicyStartTime();
//		orginPrice = communityContextInfo.getOrginPrice();
//		LocalDate curDate =   LocalDate.now();
		LocalDate curDate = LocalDate.of(2025, 7, 1);
		LocalDate startDate = curDate.with(TemporalAdjusters.firstDayOfMonth());
		LocalDate endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());
		List<CommunityReceivablesEntity> receivablesList = new ArrayList<>();

		Integer rentFlag = 0; // 都不生成租金收费

		StringBuilder baseSql = new StringBuilder(
				"SELECT a.id,a.unitcode,a.additionalArea,a.chargingArea,d.parentPropertyId,a.paymentaccountId,"
						+ "b.payItemsId,c.chargeCategory,c.comment,c.endtime,c.feecaltype,c.itemsname,c.price,c.priceunit,c.starttime,"
						+ "a.acceptanceDate,d.billingDate,c.payDate,d.isCurrentMember,d.iscurrentowner,d.memberid,d.terminationDate,d.endDate,a.rentState,d.id AS memberPropertyId "
						+ "FROM t_community_property a LEFT JOIN t_community_member_property d ON a.id=d.propertyid, "
						+ "t_community_property_pay_items b,t_community_pay_items c "
						+ "WHERE a.id = b.propertyid AND b.payitemsid = c.id AND c.state=1 "
						+ "AND c.chargeCategory != 4 AND c.chargeCategory != 5 AND c.chargeCategory != 9 "
						+ "AND d.iscurrentowner = 1 AND d.isCurrentMember =1 "
						+ "AND a.specialFeeFlag IN (0,2,3,5) AND c.starttime <= '"
						+ startDate.toString() + " 00:00:00' AND c.endTime>='" + endDate.toString() + " 00:00:00'");

		StringBuilder estateObjectSql = new StringBuilder(baseSql.toString());
		estateObjectSql.append((rentFlag.intValue() == 0 || rentFlag.intValue() == 3) ? " AND c.payDate != 1 " : "");
		// 把车位收费也加进来
//		estateObjectSql.append(" union ");
//		estateObjectSql.append(baseSql.toString());
//		estateObjectSql.append((rentFlag.intValue() == 0 || rentFlag.intValue() == 2) ? " AND c.payDate != 1 " : "");

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		List<Object[]> resultList = session.createNativeQuery(estateObjectSql.toString()).list();
		log.info("处理数据数量：" + resultList.size());
		int i = 1;
		Set<String> groupFeeSet = new HashSet<String>(); 
		try {
			for (Object[] temp : resultList) {
				EstateReceivablesVo o = EstateReceivablesVo.getEstateReceivablesVo(temp);
				if ((null == o.getParentPropertyId() || (null != o.getParentPropertyId()
						&& (null == o.getEndDate() || o.getEndDate().isAfter(endDate) || o.getEndDate().isEqual(endDate))))
						&& (((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) >= 0
								&& (int) (endDate.toEpochDay() - o.getBillingDate().toEpochDay()) >= 0)
								|| ((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) <= 0))) {
					
					CommunityReceivablesEntity receivables = new CommunityReceivablesEntity();
					receivables.setPayItemsName(o.getItemsName());
					receivables.setChargeCategory(CommunityPayItemsEntity.getChargeCategoryStr(o.getChargeCategory()));
					receivables.setChargeSource("收费设定");
					receivables.setState(1);
					receivables.setComment(o.getUnitCode() + "：" + startDate.format(DateTimeFormatter.ofPattern("yyyy年MM月"))
							+ (null != o.getParentPropertyId() ? "车位费" : "管理费"));
					CommunityEstateEntity estate = null;
					if (CommunityCache.estateList.size() == 0 || !CommunityCache.estateList
							.containsKey(null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId())) {
						estate = (CommunityEstateEntity) session
								.createQuery("from CommunityEstateEntity a where a.id="
										+ (null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId()))
								.uniqueResult();
						CommunityCache.estateList.put(estate.getId(), estate);
					} else {
						estate = CommunityCache.estateList
								.get(null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId());
					}
					if (estate.getSpecialFeeFlag().equals(0) || estate.getSpecialFeeFlag().equals(2)
							|| estate.getSpecialFeeFlag().equals(3)) {
						receivables.setEstate(estate);
						receivables.setSubEstateId(null != o.getParentPropertyId() ? o.getId() : null);
						CommunityPayItemsEntity payItem = null;
						if (CommunityCache.payItemsList.size() == 0
								|| !CommunityCache.payItemsList.containsKey(o.getPayItemsId())) {
							payItem = (CommunityPayItemsEntity) session
									.createQuery("from CommunityPayItemsEntity a where a.id=" + o.getPayItemsId())
									.uniqueResult();
							CommunityCache.payItemsList.put(payItem.getId(), payItem);
						} else {
							payItem = CommunityCache.payItemsList.get(o.getPayItemsId());
						}
						
						receivables.setPayItem(payItem);
						BigDecimal receivableAmount = new BigDecimal("0");
						/*
						 * 管理费根据单元的计费日期开始计算管理费 车位管理费根据车位租用时间开始计算
						 */
						LocalDate billingDate = o.getBillingDate().with(TemporalAdjusters.firstDayOfMonth())
								.isEqual(startDate) ? o.getBillingDate() : startDate;

						if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_CHARGE)) {
							receivableAmount = JepUtil.cal("单价*收费面积/本月天数*计费天数", 4, o.getPrice(), o.getChargingArea(),
									CommunityDateUtil.monthDays(endDate.toString()),
									CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
						} else if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_ADD)) {
							receivableAmount = JepUtil.cal("单价*附加面积/本月天数*计费天数", 4, o.getPrice(), o.getAdditionalArea(),
									CommunityDateUtil.monthDays(endDate.toString()),
									CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
						} else {
							receivableAmount = JepUtil.cal("单价/本月天数*计费天数", 4, o.getPrice(),
									CommunityDateUtil.monthDays(endDate.toString()),
									CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
						}

						receivables.setReceivableAmount(receivableAmount.setScale(2, RoundingMode.HALF_UP));
						receivables.setReceivedAmount(new BigDecimal("0"));
						receivables.setSourceNotes(o.getItemsName() + "(" + o.getUnitCode() + ")");
						receivables.setLockMark(0);
						if (o.getPayDate() != null && o.getPayDate().intValue() > 0 && o.getPayDate().intValue() < 32) {
							// paydate[1,28]按生成应收日期,但当>28时需要判断闰收及大小月，后面有需求再优化。
							try {
								receivables.setReceivableDate(Date
										.from(LocalDate.of(startDate.getYear(), startDate.getMonthValue(), o.getPayDate())
												.atStartOfDay(ZoneId.systemDefault()).toInstant()));
							} catch (DateTimeException e) {
								// 如果paydate不正确，划超出当月范围，应收日期为月未最后一日
								receivables.setReceivableDate(
										Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
							}
						} else {
							// paydate > 31 应收日期为月未最后一日 ,未实现生成下个月2日起逻辑
							receivables
									.setReceivableDate(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						}
						if ((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) >= 0
								&& (int) (endDate.toEpochDay() - o.getBillingDate().toEpochDay()) >= 0) {
							receivables
									.setStartTime(Date.from(billingDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						} else {
							receivables.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						}

						receivables.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						receivables.setPaymentPeriod(receivables.getReceivableDate());
						
						if(payItem.getGroupFee() != null) {
							if (getSameGroupReceivableFromDatabase(receivables,session) != null) {
								//如查应表中存在已经生成的互斥收费项目的应收，则跳过不生成该应收。
								continue;
							}
						}
						
						Object databaseRecord = getManageReceivableFromDatabase(o, receivables, session);
						if (receivables.getReceivableAmount().doubleValue() > 0 && databaseRecord == null) {
							// 已经生成的互斥收费项目的应收，则跳过不生成该应收。
							// 判断标准相同单元、相同应收日期、相同子单元、相同互斥ID的项目
							if(receivables.getPayItem().getGroupFee() != null) {
								String identify = receivables.getEstate().getId().toString() 
										  + "_" + DateUtil.format(receivables.getReceivableDate(), 0)
										  + "_" + ((null != receivables.getSubEstateId() && !receivables.getSubEstateId().equals(0)) ? receivables.getSubEstateId().toString(): "")
										  + "_" +  receivables.getPayItem().getGroupFee().toString();
										 
								if(groupFeeSet.contains(identify)) {
									continue;
								}
								else {
									groupFeeSet.add(identify);
								}
							}
							if(payItem.getEnableBenefitPolicy().intValue() == 1) {
								// 管理费优惠计算
								BigDecimal orginAmount = JepUtil.cal("单价*收费面积/本月天数*计费天数", 4, payItem.getBenefitOrginPrice(), o.getChargingArea(),
										CommunityDateUtil.monthDays(endDate.toString()),
										CommunityDateUtil.subDate(billingDate.toString(), endDate.toString()));
								calBenefitReceivables(receivables, orginAmount.setScale(2, RoundingMode.HALF_UP), session);
							}
							receivablesList.add(receivables);
						}
					}
				}
				log.debug("searched:" + (i++));
			}
			log.info("完成{}/{}条应收记录费用计算！", i, receivablesList.size());
		}
		catch(Exception e) {
			log.info("计算管理费过程发生异常：{}",e);
		}
	
		Transaction transaction = session.beginTransaction();
		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {
				StringBuilder sql = new StringBuilder(
						"insert into t_community_receivables(chargeCategory,chargeSource,comment,endTime,"
								+ "payItemsName,paymentPeriod,receivableAmount,receivableDate,"
								+ "receivedAmount,sourceNotes,startTime,estateId,payItemId,oldId,subEstateId,receivablesNO,benefitValue) "
								+ "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE "
								+ "payItemsName=?,receivableDate=?,estateId=?,oldid=?,subEstateId=?");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (CommunityReceivablesEntity o : receivablesList) {
					stmt.setString(1, o.getChargeCategory());
					stmt.setString(2, o.getChargeSource());
					stmt.setString(3, o.getComment());
					stmt.setString(4, DateUtil.formatLongFormat(o.getEndTime()));
					stmt.setString(5, o.getPayItemsName());
					stmt.setString(6, DateUtil.formatLongFormat(o.getPaymentPeriod()));
					stmt.setBigDecimal(7, o.getReceivableAmount());
					stmt.setString(8, DateUtil.formatLongFormat(o.getReceivableDate()));
					stmt.setBigDecimal(9, o.getReceivedAmount());
					stmt.setString(10, o.getSourceNotes());
					stmt.setString(11, DateUtil.formatLongFormat(o.getStartTime()));
					stmt.setInt(12, o.getEstate().getId());
					stmt.setInt(13, o.getPayItem().getId());
					stmt.setString(14, " ");
					if (null != o.getSubEstateId()) {
						stmt.setInt(15, o.getSubEstateId());
					} else {
						stmt.setNull(15, Types.INTEGER);
					}
					// stmt.setString(16 , o.getReceivablesNO());
					stmt.setString(16,
							getSnLastValue(CommunityReceivablesServiceImpl.RECEIVABLESNO_SN_TYPE, session).toString());
					stmt.setString(17, o.getBenefitValue());
					stmt.setString(18, o.getPayItemsName());
					stmt.setString(19, DateUtil.formatLongFormat(o.getReceivableDate()));
					stmt.setInt(20, o.getEstate().getId());
					stmt.setString(21, " ");
					if (null != o.getSubEstateId()) {
						stmt.setInt(22, o.getSubEstateId());
					} else {
						stmt.setNull(22, Types.INTEGER);
					}
					stmt.addBatch();
					transCount++;
					if (transCount % 500 == 0) {
						stmt.executeBatch();
						conn.commit();
					}
					log.debug("saved:" + transCount);
				}
				stmt.executeBatch();
				conn.commit();
				log.info("应收账单自动生成完成！共生成并写入[{}]条应收记录", transCount);
			}
		});

		transaction.commit();
		session.close();
	}

	// 计算约违约金定时任务
	@SuppressWarnings("unchecked")
	public void calBreachFee() {
		log.info("begin the task to process breach receivables records!");
		CommunityContextInfo communityContextInfo = (CommunityContextInfo)SpringHandler.getBean("communityContextInfo");
		cutoverDate = communityContextInfo.getCutoverDate();

		Date nowDate = new Date();
//		Date nowDate = null;
//		try {
//			nowDate = DateUtil.parse("2025-01-21 02:30:00", 1);
//		} catch (ParseException e1) {
//			// TODO Auto-generated catch block
//			e1.printStackTrace();
//		}
		// 今天算昨天的违约金
		Date breachEndDate = DateUtil.resetStartOfDay(DateUtil.getOffsetDateByDay(nowDate, -1));

		try {
			SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
			Session session = sessionFactory.openSession();

			// 查询需要计算违约金应收记录
			StringBuilder queryHql = new StringBuilder(
					"SELECT r FROM CommunityReceivablesEntity r INNER JOIN r.payItem i INNER JOIN r.estate e ");
			queryHql.append("WHERE ");
			// 存在应收未收
			// 收费项目中设定了生成违约金的应收
			queryHql.append("i.isBreach = 1 AND i.breachRatio > 0 ");
			// 单元不在排除名单中
			queryHql.append("AND e.specialFeeFlag <> 3 AND e.specialFeeFlag <> 4 ");
			
			queryHql.append("AND ((r.receivableDate <= '" + DateUtil.format(DateUtil.getMonthDay(breachEndDate, 1, 0), 0)
			+ " 00:00:00') OR r.benefitValue IS NOT NULL) ");
			
			List<CommunityReceivablesEntity> receivablesList = session.createQuery(queryHql.toString()).list();

			log.info("the number of process breach receivables records:{}", receivablesList.size());
			// 把需要计算违约金的应收加载到内存
			log.info("loading breach receivables to memory!");
			// 按单元分组
			Map<Integer, List<CommunityReceivablesEntity>> estateReceivableMap = new HashMap<Integer, List<CommunityReceivablesEntity>>();
			receivablesList.forEach(r -> {
				List<CommunityReceivablesEntity> estateReceivablesList = null;
				if (estateReceivableMap.get(r.getEstate().getId()) == null) {
					estateReceivablesList = new ArrayList<CommunityReceivablesEntity>();
					estateReceivablesList.add(r);
					estateReceivableMap.put(r.getEstate().getId(), estateReceivablesList);
				} else {
					estateReceivableMap.get(r.getEstate().getId()).add(r);
				}
			});

			// 把违约金的应收加载到内存
			Map<Integer, CommunityReceivablesEntity> manageIdBreachReceivableMap = new HashMap<Integer, CommunityReceivablesEntity>();
			List<CommunityReceivablesEntity> breachReceivablesList = session
					.createQuery("select m from CommunityReceivablesEntity m where m.breachReceivablesId is not null")
					.list();
			breachReceivablesList.forEach(m -> {
				manageIdBreachReceivableMap.put(m.getBreachReceivablesId(), m);
			});

			// 获取违约金对应的payitem
			List<CommunityReceivablesEntity> newBreachReceivablesEntityList = new ArrayList<CommunityReceivablesEntity>();
			CommunityPayItemsEntity breachPayItem = (CommunityPayItemsEntity) session
					.createQuery("select i from CommunityPayItemsEntity i where i.chargeCategory = 5 and i.state = 1")
					.uniqueResult();
			log.info("caculate breach receivables!");
			// 按每个单元计算，生成违约金
			estateReceivableMap.keySet().forEach(o -> {
				List<CommunityReceivablesEntity> result = new ArrayList<CommunityReceivablesEntity>();
				for (CommunityReceivablesEntity receivable : estateReceivableMap.get(o)) {
					if(receivable.getBenefitValue() == null) {
						// 查出对应管理费应收是否已经生成违约金
						CommunityReceivablesEntity currentBreachReceivablesEntity = calSingleBreachAmount(o,receivable,breachEndDate,breachPayItem,manageIdBreachReceivableMap,session);
						if(currentBreachReceivablesEntity != null) {
							result.add(currentBreachReceivablesEntity);
						}
					}
					else {
						StringBuilder detailComment = new StringBuilder();
						Date startTime = null;
						Date endTime =  null;
						try {
							 startTime = DateUtil.parse("3023-12-31 00:00:00", 1);
							 endTime = DateUtil.parse("1900-12-31 23:59:59", 1);
						} catch (ParseException e) {
							log.info("日期转换出现异常：{}", e);
						}
						CancelBenefitDetailVo benefitVo  = JsonUtil.jsonToObject(receivable.getBenefitValue(), CancelBenefitDetailVo.class);
						if(null == benefitVo){
							log.info("计算管理费[id={}]违约金时出现异常：benefitValue格式错误", receivable.getId());
							continue;
						}
						//计算当月管理费产生的违续金
						receivable.setReceivableAmount(receivable.getReceivableAmount().subtract(benefitVo.getTotalSubManageAmount()));
						CommunityReceivablesEntity currentBreachReceivablesEntity = calSingleBreachAmount(o, receivable, breachEndDate, breachPayItem, manageIdBreachReceivableMap, session);
						if(currentBreachReceivablesEntity == null) {
							//还原价格
							receivable.setReceivableAmount(receivable.getReceivableAmount().add(benefitVo.getTotalSubManageAmount()));
							continue;
						}
						if(currentBreachReceivablesEntity.getReceivableAmount().compareTo(BigDecimal.ZERO) > 0 ) {
							startTime = currentBreachReceivablesEntity.getStartTime().before(startTime) ? currentBreachReceivablesEntity.getStartTime() : startTime;
							endTime = currentBreachReceivablesEntity.getEndTime().after(endTime) ? currentBreachReceivablesEntity.getEndTime() : endTime;
						}
						detailComment.append("。其中").append(DateUtil.formatByStyle(receivable.getReceivableDate(), "yyyy年MM月") + "产生违约金" + currentBreachReceivablesEntity.getReceivableAmount().setScale(2, RoundingMode.HALF_UP) + "元");
						//是否需要取消优惠后的违约金额差额

						BigDecimal totalSubBreachAmount =  BigDecimal.ZERO;
						//取消优惠后计算违约金额是多少
						for(CancelBenefitDetailItemVo itemVo : benefitVo.getItems()) {
							 CommunityReceivablesEntity cancelBenefitReceivable = (CommunityReceivablesEntity) session
									.createQuery("from CommunityReceivablesEntity a where a.id=" + itemVo.getReceivablesId())
									.uniqueResult();
							 if(null != cancelBenefitReceivable) {
								 // 获取优惠计算情况管理费产生的违约金额
								 BigDecimal originalBreachAmount = BigDecimal.ZERO;
								 CommunityReceivablesEntity originalBreachReceivablesEntity = calSingleBreachAmount(o, cancelBenefitReceivable, breachEndDate, breachPayItem, manageIdBreachReceivableMap, session);
								 if(originalBreachReceivablesEntity != null) {
									 originalBreachAmount = originalBreachReceivablesEntity.getReceivableAmount();
								 }
								 
								 // 管理费取消优惠后产生的违约金额
								 session.evict(cancelBenefitReceivable);
								 cancelBenefitReceivable.setReceivableAmount(cancelBenefitReceivable.getReceivableAmount().add(itemVo.getSubManageAmount()));
								 CommunityReceivablesEntity currentBenefitBreachReceivablesEntity = calSingleBreachAmount(o, cancelBenefitReceivable, breachEndDate, breachPayItem, manageIdBreachReceivableMap, session, itemVo.getChangeAmountDetail());
								 if(currentBenefitBreachReceivablesEntity != null) {
									 startTime = currentBenefitBreachReceivablesEntity.getStartTime().before(startTime) ? currentBenefitBreachReceivablesEntity.getStartTime() : startTime;
								     endTime = currentBenefitBreachReceivablesEntity.getEndTime().after(endTime) ? currentBenefitBreachReceivablesEntity.getEndTime() : endTime;
									 
									 // 计算取消优惠后的违约金的差额
									 itemVo.setSubBreachAmount(currentBenefitBreachReceivablesEntity.getReceivableAmount().subtract(originalBreachAmount)); 
									 totalSubBreachAmount = totalSubBreachAmount.add(itemVo.getSubBreachAmount());
								 }
							 }
						}	
						benefitVo.setTotalSubBreachAmount(totalSubBreachAmount);
//						String districtAddress = "16";
//						String districtAddress = receivable.getEstate().getBuilding().getDistrict().getAddress();
//						districtAddress = districtAddress == null ? "" : districtAddress;
						String districtAddressSql = "SELECT t.address,p.unitCode FROM `t_community_district` t INNER JOIN `t_community_building` b ON b.`districtId` =  t.id INNER JOIN `t_community_property` p ON p.`buildingId` = b.id WHERE p.id = " + o;
						List<Object[]> estateDetailList = session.createNativeQuery(districtAddressSql).list();
		
						String districtAddress = "" ;
						String unitCode = "";
						if(estateDetailList.size() > 0) {
							districtAddress = estateDetailList.get(0)[0] == null ? "" : estateDetailList.get(0)[0].toString();
							unitCode = estateDetailList.get(0)[1] == null ? "" : estateDetailList.get(0)[1].toString();
						}
						
						//更新json
						detailComment.append("，由于不符合" + districtAddress + "区的优惠政策，补收此前月份违约金差额" + benefitVo.getTotalSubBreachAmount().setScale(2, RoundingMode.HALF_UP)+ "元!");
						String strBenefitJson = JsonUtil.objectToJSON(benefitVo);
						currentBreachReceivablesEntity.setEndTime(endTime);
						currentBreachReceivablesEntity.setStartTime(startTime);
						currentBreachReceivablesEntity.setPaymentPeriod(breachEndDate.before(startTime) ? startTime : breachEndDate);
						currentBreachReceivablesEntity.setReceivableDate(breachEndDate.before(startTime) ? startTime : breachEndDate);
						
						currentBreachReceivablesEntity.setBenefitValue(strBenefitJson);
						currentBreachReceivablesEntity.setReceivableAmount(currentBreachReceivablesEntity.getReceivableAmount().add(benefitVo.getTotalSubBreachAmount()));
						
						currentBreachReceivablesEntity.setComment(unitCode + "："
								+ DateUtil.formatByStyle(receivable.getReceivableDate(), "yyyy年MM月") + receivable.getPayItem().getItemsName()
								+ "违约金"+ detailComment.toString());
						//还原管理费应收金额
						receivable.setReceivableAmount(receivable.getReceivableAmount().add(benefitVo.getTotalSubManageAmount()));
						receivable.setBenefitValue(strBenefitJson);
						if((currentBreachReceivablesEntity.getReceivableAmount().compareTo(BigDecimal.ZERO) == 0
								|| currentBreachReceivablesEntity.getReceivableAmount().setScale(2, RoundingMode.HALF_UP).compareTo(BigDecimal.ZERO) == 0)
								&& currentBreachReceivablesEntity.getId() == null) {
							continue;
						}
						result.add(currentBreachReceivablesEntity);
				   }
				}

				newBreachReceivablesEntityList.addAll(result);
				log.info("the number of processed breach receivables:{}", newBreachReceivablesEntityList.size());
			});

			// 保存计算结果到数据库
			log.info("save result of caculate to database!");
			Transaction tx = session.beginTransaction();
			// 更新用户违约金，如果该单元不存在违约金记录则插入，否则更新，已经发生过收款的违约金不受影响，数量较多时需要每300条记录批量提交
			for (int i = 0; i < newBreachReceivablesEntityList.size(); i++) {
				CommunityReceivablesEntity receivable = newBreachReceivablesEntityList.get(i);
				if(receivable.getId() != null 
						&& receivable.getReceivableAmount().compareTo(BigDecimal.ZERO) == 0
						&& receivable.getReceivedAmount().compareTo(BigDecimal.ZERO) == 0
						&& receivable.getLockMark() == 0) {
					// 删除违约金额为0，且未发生过收款的应收
					receivable.setEstate(null);
					receivable.setPayItem(null);
					session.delete(receivable);
				}
				else {
					if (receivable.getId() == null) {
						receivable.setReceivablesNO(
								getSnLastValue(CommunityReceivablesServiceImpl.RECEIVABLESNO_SN_TYPE, session).toString());
					}
					
					session.saveOrUpdate(receivable);
				}

				if (i % 300 == 0) {
					session.flush();
					session.clear();
				}
			}
			tx.commit();
			session.close();

		} catch (Exception ex) {
			log.info("计算违约金过程出现异常：{}", ex);
		}
		log.info("finish the task of process breach receivables records!");
	}
	
	// 生成短租应收,每天生成
	public static void calShortRentFee() {
		log.info("开始生成短租租金应收！");
		LocalDate startDate = LocalDate.now();
		LocalDate endDate = startDate;
		generateRentFee(startDate, endDate, 1);
		log.info("生成短租租金应收结束！");
	}
	
	// 生成长租应收,每月第1天生成
	public static void calLongRentFee() {
		log.info("开始生成长租租金应收！");
		LocalDate curDate =   LocalDate.now();
//		LocalDate curDate = LocalDate.of(2022, 12, 1);
		LocalDate startDate = curDate.with(TemporalAdjusters.firstDayOfMonth());
		LocalDate endDate = startDate.with(TemporalAdjusters.lastDayOfMonth());
		generateRentFee(startDate, endDate, 0);
		log.info("生成长租租金应收结束！");

	}

	// 生成应收前，从数据库中查找确认该应收是否已经存在
	private static Object getManageReceivableFromDatabase(EstateReceivablesVo o, CommunityReceivablesEntity receivables,
			Session session) {
		StringBuilder dbSql = new StringBuilder("select a.* from t_community_receivables a where a.estateid="
				+ (null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId()) + " and a.payItemId = "
				+ receivables.getPayItem().getId() + " and a.receivableDate='"
				+ DateUtil.format(receivables.getReceivableDate(), 0) + "'")
				.append(null != receivables.getSubEstateId() ? " and a.subEstateId=" + receivables.getSubEstateId()
						: "");

		return session.createNativeQuery(dbSql.toString()).uniqueResult();
	}
	
	// 生成应收前，从数据库中查找确认该应收是否已
	private static Object getSameGroupReceivableFromDatabase(CommunityReceivablesEntity receivables,
			Session session) {
		StringBuilder dbSql = new StringBuilder("select a.* from t_community_receivables a inner join t_community_pay_items i on a.payItemId = i.id where a.estateid=" + (receivables.getEstate().getId()) 
				+ " and a.receivableDate='" + DateUtil.format(receivables.getReceivableDate(), 0) + "'"
				+ " and i.groupFee = " + receivables.getPayItem().getGroupFee())
				.append(null != receivables.getSubEstateId() ? " and a.subEstateId=" + receivables.getSubEstateId()
						: "");

		return session.createNativeQuery(dbSql.toString()).uniqueResult();
	}

	private static Integer getSnLastValue(String snType, Session session) {
		Integer result = 0;
		String hql = "select sn from SnEntity sn where sn.snType = :type";
		@SuppressWarnings("unchecked")
		List<SnEntity> resultList = (List<SnEntity>) session.createQuery(hql)
				.setLockMode(LockModeType.PESSIMISTIC_WRITE).setParameter("type", snType).list();
		if (resultList.size() > 0) {
			result = resultList.get(0).getLastValue();
		}
		resultList.get(0).setLastModifyTime(new Timestamp(System.currentTimeMillis()));
		;
		resultList.get(0).setLastValue(result + 1);
		return result;
	}

	/**
	 * 2023-1-1（已经废弃）
	 * 十六区优惠策略:16区从2023-1-1起，5区从2024-1-1起执行优惠策略，按2.38元单价计算物业管理费若连续三个月存在欠费情况，则第四个月开始按原有的3.18元单价进行计算，
	 * 之前的未缴月份也需补交按3.18计算部分金额，连同违约金也需要计算。若缴清费用，则下一个月恢复优惠按2.38元进行计算物业费。
	 * 
	 * 2025-1-4
	 * 优惠策略修改为：16区从2023-1-1起，5区从2024-1-1起执行优惠策略，当前月的历史管理费应收中只要存在3个月或以上欠费的情况，取消优惠，按原价3.18元单价进行计算，
	 * 之前的未缴月份也需补交按3.18计算部分金额，连同违约金也需要计算。该策略从2025年1月1日生效。
	 * @param receivables
	 * @param orginAmount 不符合优惠时的应收款金额
	 * @param session
	 */
	private static void calBenefitReceivables(CommunityReceivablesEntity receivables, BigDecimal orginAmount, Session session) {
		LocalDate curDate = LocalDate.now();
//		LocalDate curDate = LocalDate.of(2025, 1, 21);
		if(CommunityDateUtil.dateToLocalDate(receivables.getReceivableDate()).with(TemporalAdjusters.firstDayOfMonth()).isAfter(curDate.with(TemporalAdjusters.firstDayOfMonth()))) {
			// 预收情况下不考虑取消优惠逻辑
			return;
		}
		if(receivables.getPayItem().getEnableBenefitPolicy()!= null && receivables.getPayItem().getEnableBenefitPolicy() == CommunityContext.RECORD_STATE_VALID && receivables.getPayItem().getBenefitStartDate() != null && (!receivables.getPayItem().getBenefitStartDate().after(receivables.getReceivableDate()))) {
			//LocalDate beforeDay = CommunityDateUtil.dateToLocalDate(receivables.getReceivableDate()).plus(-3, ChronoUnit.MONTHS).with(TemporalAdjusters.firstDayOfMonth());
			LocalDate beforeDay = CommunityDateUtil.dateToLocalDate(receivables.getPayItem().getBenefitStartDate());
			String benefitSql =  "SELECT * FROM t_community_receivables t WHERE (t.payItemId = " + receivables.getPayItem().getId() + ") AND t.estateId = " + receivables.getEstate().getId() + " AND t.receivableDate >= '" + beforeDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00")) + "'";
			@SuppressWarnings("unchecked")
			List<CommunityReceivablesEntity> benefitReceivablesList = (List<CommunityReceivablesEntity>) session
					.createNativeQuery(benefitSql.toString()).addEntity(CommunityReceivablesEntity.class)
					.list();
			Boolean isBenefit = false;

			String districtAddressSql =  "SELECT t.address FROM `t_community_district` t INNER JOIN `t_community_building` b ON b.`districtId` =  t.id INNER JOIN `t_community_property` p ON p.`buildingId` = b.id WHERE p.id = " + receivables.getEstate().getId();
			Object districtAddressObj = session.createNativeQuery(districtAddressSql).uniqueResult();
			String districtAddress = districtAddressObj == null ? "" : (String)districtAddressObj;
			if(benefitReceivablesList.size() < 3) {
				// 本月应收符合优惠政策
				receivables.setComment(receivables.getComment() + ";符合" + districtAddress + "区优惠政策");
				return;
			}
			Map<Integer,CancelBenefitDetailItemVo> hasCatchup =  new HashMap<Integer,CancelBenefitDetailItemVo>();
//*****2025年1月4日御江南提出新的优惠策略修改部分*****
//			for(CommunityReceivablesEntity benefitReceivables : benefitReceivablesList) {
//				// 前3个月存完成交费情况
//				if(benefitReceivables.getReceivableAmount().compareTo(benefitReceivables.getReceivedAmount()) <= 0) {
//					isBenefit = true;
//					break;
//				}
//				if(benefitReceivables.getBenefitValue() != null) {
//					CancelBenefitDetailVo benefitVo  = JsonUtil.jsonToObject(benefitReceivables.getBenefitValue(), CancelBenefitDetailVo.class);
//					for(CancelBenefitDetailItemVo item : benefitVo.getItems()) {
//						hasCatchup.put(item.getReceivablesId(), item);
//					}
//				}
//			}
			
			// 2023年1月1日后未完成付款应收记录数
			Integer unPaidNum = 0;
			for(CommunityReceivablesEntity benefitReceivables : benefitReceivablesList) {
				// 统计未完成付款的应收记录数
				if(benefitReceivables.getReceivableAmount().compareTo(benefitReceivables.getReceivedAmount()) > 0) {
					unPaidNum++;
				}
				// 统计已经补收的记录，防止后面计算重复补收
				if(benefitReceivables.getBenefitValue() != null) {
					CancelBenefitDetailVo benefitVo  = JsonUtil.jsonToObject(benefitReceivables.getBenefitValue(), CancelBenefitDetailVo.class);
					for(CancelBenefitDetailItemVo item : benefitVo.getItems()) {
						hasCatchup.put(item.getReceivablesId(), item);
					}
				}
			}
			isBenefit = unPaidNum < 3;
////*****2025年1月4日御江南提出新的优惠策略修改部分*****			
			if(isBenefit == false) {
				// 取消优惠
				BigDecimal catchUpAmount = BigDecimal.ZERO;
				List<CancelBenefitDetailItemVo> items =  new ArrayList<CancelBenefitDetailItemVo>();
				// 按3.18计算
				receivables.setReceivableAmount(orginAmount); 
				receivables.setComment(receivables.getComment() + ";不符合" + districtAddress + "区优惠政策");
				for(CommunityReceivablesEntity benefitReceivables : benefitReceivablesList) {
					//if(benefitReceivables.getReceivableAmount().compareTo(receivables.getReceivableAmount()) < 0 && benefitReceivables.getBenefitValue() == null) {
					if(!hasCatchup.containsKey(benefitReceivables.getId()) && benefitReceivables.getBenefitValue() == null
							&& benefitReceivables.getReceivableAmount().compareTo(benefitReceivables.getReceivedAmount())>0) {
						// 3个月内曾经优惠的需要补差额,已经补缴的不需要重复计算
						// 补多少差额
						BigDecimal subAmount = receivables.getReceivableAmount().subtract(benefitReceivables.getReceivableAmount()); 
						// 判断是否住满整个月，如果不是，补缴金额为：整月补缴金额*入住天数/当月总天数
						BigDecimal divide = CommunityDateUtil.subDate(CommunityDateUtil.formatShortFormat(benefitReceivables.getStartTime()), CommunityDateUtil.formatShortFormat(benefitReceivables.getEndTime()))
								.divide(CommunityDateUtil.monthDays(CommunityDateUtil.formatShortFormat(benefitReceivables.getEndTime())),2, RoundingMode.HALF_UP);
						subAmount = subAmount.multiply(divide).setScale(2, RoundingMode.HALF_UP);
						if(subAmount.compareTo(new BigDecimal("0.05")) > 0) {  //2024年5月起小数精确度从小数点后1位调整到2位
							CancelBenefitDetailItemVo item =  new CancelBenefitDetailItemVo(benefitReceivables.getId(),DateUtil.format(benefitReceivables.getReceivableDate(), 1) ,subAmount);
							items.add(item);
							catchUpAmount = catchUpAmount.add(subAmount);
						}
					}
				}
				if(catchUpAmount.compareTo(BigDecimal.ZERO) > 0) {
					// 修改应收并，记录优惠被取消的详情
					receivables.setReceivableAmount(receivables.getReceivableAmount().add(catchUpAmount).setScale(2, RoundingMode.HALF_UP));
					receivables.setBenefitValue(JsonUtil.objectToJSON(new CancelBenefitDetailVo(catchUpAmount, null, items)));
					receivables.setComment(receivables.getComment() + "，需补回此前月份差额，补回" + catchUpAmount.setScale(2, RoundingMode.HALF_UP)+ "元");
				}
			}
			else {
				// 本月应收符合优惠政策
				receivables.setComment(receivables.getComment() + ";符合" + districtAddress + "区优惠政策");
			}
			// 清除Hibernate会话缓存中用于计算优惠的应收对象，以提高后面插入操作的速度
			for(CommunityReceivablesEntity benefitReceivables : benefitReceivablesList) {
				session.evict(benefitReceivables);
			}
		}
	}
	
	/**
	 * 生成单个管理费应收的违约金
	 * @param estateId                        单元ID
	 * @param receivable                      产生违约金的应收ID
	 * @param breachEndDate                   计算违约金的结束日期
	 * @param breachPayItem                   违约金的payItemId
	 * @param manageIdBreachReceivableMap     违约金缓存Map<key=管理费应收ID，value=对应的违约金>
	 * @param querySession                    hibernate会话
	 * @return 生成对应的违约金应收对象
	 */
	private CommunityReceivablesEntity calSingleBreachAmount(Integer estateId,
			CommunityReceivablesEntity receivable, Date breachEndDate, CommunityPayItemsEntity breachPayItem,
			Map<Integer, CommunityReceivablesEntity> manageIdBreachReceivableMap, Session querySession) {
		return calSingleBreachAmount(estateId,receivable,breachEndDate,breachPayItem,manageIdBreachReceivableMap,querySession, null);
	}

	/**
	 * 生成单个管理费应收的违约金
	 * @param estateId                        单元ID
	 * @param receivable                      产生违约金的应收ID
	 * @param breachEndDate                   计算违约金的结束日期
	 * @param breachPayItem                   违约金的payItemId
	 * @param manageIdBreachReceivableMap     违约金缓存Map<key=管理费应收ID，value=对应的违约金>
	 * @param querySession                    hibernate会话
	 * @param changeList                      List<CancelBenefitAmountChangeDetailItem> 取消优惠后，补收管理费差额部分的动账记录List
	 * @return 生成对应的违约金应收对象
	 */
	private CommunityReceivablesEntity calSingleBreachAmount(Integer estateId,
			CommunityReceivablesEntity receivable, Date breachEndDate, CommunityPayItemsEntity breachPayItem,
			Map<Integer, CommunityReceivablesEntity> manageIdBreachReceivableMap, Session querySession, List<CancelBenefitAmountChangeDetailItem> changeList) {
		
		CommunityReceivablesEntity currentBreachReceivablesEntity = manageIdBreachReceivableMap
				.get(receivable.getId());

		Date startCaculateDate = DateUtil.getNextMonthDay(receivable.getReceivableDate(), 1);
		startCaculateDate = startCaculateDate.before(cutoverDate) ? cutoverDate
				: startCaculateDate;
		startCaculateDate = DateUtil.resetStartOfDay(startCaculateDate);
		CommunityPayItemsEntity payItem = receivable.getPayItem();
		// 目前是按（计算基准时间-1）与最后一次计算违约金的时间之间有没发生动账计算
		if (currentBreachReceivablesEntity == null || changeList != null) {
			currentBreachReceivablesEntity = new CommunityReceivablesEntity();
			currentBreachReceivablesEntity.setSubEstateId(null);
			currentBreachReceivablesEntity.setBreachReceivablesId(receivable.getId());
			currentBreachReceivablesEntity.setEstate(receivable.getEstate());

			currentBreachReceivablesEntity.setChargeCategory(
					CommunityPayItemsEntity.getChargeCategoryStr(breachPayItem.getChargeCategory()));
			currentBreachReceivablesEntity.setChargeSource("系统违约金");
			currentBreachReceivablesEntity.setSourceNotes("系统违约金");
			currentBreachReceivablesEntity.setLockMark(0);
			if(changeList == null) {
				currentBreachReceivablesEntity.setComment(receivable.getEstate().getUnitCode() + "："
						+ DateUtil.formatByStyle(receivable.getReceivableDate(), "yyyy年MM月") + payItem.getItemsName()
						+ "违约金");
			}
			currentBreachReceivablesEntity
					.setPayItemsName(StringUtils.isNotEmpty(payItem.getBreachName()) ? payItem.getBreachName()
							: (payItem.getItemsName() + "违约金"));

			currentBreachReceivablesEntity.setStartTime(startCaculateDate);
			currentBreachReceivablesEntity.setReceivedAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
			currentBreachReceivablesEntity
					.setReceivableAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
			currentBreachReceivablesEntity.setPayItem(breachPayItem);

		} else {
//			if(changeList != null) {
//				communityReceivablesDao.evict(currentBreachReceivablesEntity);  // 不生效，只对新建对象处理
//			}
			// 发生过收款的违约金，手工不受影响,已经锁盘的违约金暂不更新，手工锁定的不受影响
			if (currentBreachReceivablesEntity.getLockMark().intValue() == 1
					|| currentBreachReceivablesEntity.getReceivedAmount().compareTo(BigDecimal.ZERO) > 0) {
				return null;
			}
			currentBreachReceivablesEntity.setSubEstateId(null);
			currentBreachReceivablesEntity.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
		}
		CalBreachAmountChangeAmountHistoryResultVo calBreachAmountResult = new CalBreachAmountChangeAmountHistoryResultVo(currentBreachReceivablesEntity.getStartTime(),BigDecimal.ZERO);
		if (!breachEndDate.before(currentBreachReceivablesEntity.getStartTime())) {
			currentBreachReceivablesEntity.setState(1);
			String receiveHistorySql = "SELECT * FROM (SELECT r.receiptDate, rr.currentAmount, rr.receivedAmount FROM t_community_receipt_receivables rr "
					+ " INNER JOIN t_community_receipt r ON rr.receiptId =  r.id" + " WHERE rr.receivablesId = "
					+ receivable.getId() + " AND r.receiptDate <= '"
					+ DateUtil.formatByStyle(breachEndDate, "yyyy-MM-dd 23:59:59") + "'" 
					+ " UNION"
					+ " SELECT k.changeDate as receiptDate, (0-k.changeAmount) as currentAmount, k.receivedAmount as receivedAmount FROM t_community_receivables_changes k "
					+ " WHERE k.receivablesId = " + receivable.getId() + " AND k.changeDate <= '" 
					+ DateUtil.formatByStyle(breachEndDate, "yyyy-MM-dd 23:59:59") + "'" 
					+ " AND k.state = 1) s ORDER BY s.receiptDate ASC";
			// logger.info("1--");
			@SuppressWarnings("unchecked")
			List<Object[]> objList = querySession.createNativeQuery(receiveHistorySql).list();
			if(changeList != null) {
				for(CancelBenefitAmountChangeDetailItem  changeItem : changeList) {
					objList.add(new Object[] { changeItem.getChangeDate(), changeItem.getChangeAmount(), BigDecimal.ZERO } );
				}
				Collections.sort(objList,new Comparator<Object[]>() {
					@Override
					public int compare(Object[] o1, Object[] o2) {
						Date date1 = (Date) o1[0];
						Date date2 = (Date) o2[0];
						return date1.compareTo(date2);
					}
					
				});
			}
			// logger.info("2--");
			// 预收收款日期早于应收日期，如果也产生违约金会有问题。
			Object[] head = new Object[] { receivable.getReceivableDate(), BigDecimal.ZERO, BigDecimal.ZERO };
			Object[] tail = new Object[] { breachEndDate, BigDecimal.ZERO, receivable.getReceivedAmount() };
			objList.add(0, head);
			
			objList.add(objList.size(), tail);
			// 把日期清零
			objList.forEach(o -> {
				o[0] = DateUtil.resetStartOfDay((Date) o[0]);
			});
			// 根据动账记录分段计算违约金，只计算系统割接后的违约金
			// 原始应收未收金额
			calBreachAmountResult = calBreachAmountChangeAmountHistory(receivable, objList, startCaculateDate, breachEndDate);
		}
		
		if ((calBreachAmountResult.getTotalBreachAmount().compareTo(BigDecimal.ZERO) == 0
				|| calBreachAmountResult.getTotalBreachAmount().setScale(2, RoundingMode.HALF_UP).compareTo(BigDecimal.ZERO) == 0)
				&& currentBreachReceivablesEntity.getId() == null && receivable.getBenefitValue() == null) {
			// 没有产生违约金，不生成违约金应收
			return null;
		}

		String endTimeStr = DateUtil.formatByStyle(calBreachAmountResult.getDisplayDate(), "yyyy-MM-dd 23:59:59");
		String receivableDateStr = DateUtil.formatByStyle(calBreachAmountResult.getDisplayDate(), "yyyy-MM-dd 00:00:00");
		try {
			currentBreachReceivablesEntity.setEndTime(DateUtil.parse(endTimeStr, 1));
			currentBreachReceivablesEntity.setPaymentPeriod(DateUtil.parse(receivableDateStr, 1));
			currentBreachReceivablesEntity.setReceivableDate(DateUtil.parse(receivableDateStr, 1));
		} catch (ParseException e) {
			log.info("计算违约金时，发生日期转换异常：{}", e);
		}
		BigDecimal orgnalBreachReceivableAmount = currentBreachReceivablesEntity.getReceivableAmount();
		currentBreachReceivablesEntity.setReceivableAmount(calBreachAmountResult.getTotalBreachAmount().setScale(2, RoundingMode.HALF_UP));
		String logPrefix = currentBreachReceivablesEntity.getId() == null ? "生成违约金:"
				: ("修改违约金[" + currentBreachReceivablesEntity.getId() + "]:");
		log.debug(logPrefix + "违约金额[{}]=>[{}]元", orgnalBreachReceivableAmount,
				currentBreachReceivablesEntity.getReceivableAmount().setScale(2, RoundingMode.HALF_UP));
		return currentBreachReceivablesEntity;
	}
	
	/**
	 * 根据动账历史，计算管理费的违约金
	 * @param receivable		管理费应收对象
	 * @param objList 			动账历史记录Object[0]=动账时间,Date类型；Object[1]=当次动账金额，收款/减免为正，加收为负数,BigDecimal类型；
	 * @param startCaculateDate 开始计算时间
	 * @param breachEndDate     结束计算时间
	 * @return CalBreachAmountChangeAmountHistoryResultVo 对象， displayDate 实际结束计算日期；totalBreachAmount 对应的违约金额
	 */
	private CalBreachAmountChangeAmountHistoryResultVo calBreachAmountChangeAmountHistory(CommunityReceivablesEntity receivable, List<Object[]> objList, Date startCaculateDate, Date breachEndDate) {
		CalBreachAmountChangeAmountHistoryResultVo result = new CalBreachAmountChangeAmountHistoryResultVo(startCaculateDate, null);
		Date displayDate = breachEndDate;
		BigDecimal totalBreachAmount = BigDecimal.ZERO;
		BigDecimal currentAmount = receivable.getReceivableAmount();
		int segementCount = 0;
		BigDecimal currentNeedPayAmount = currentAmount;
		for (int i = 1; i < objList.size(); i++) {
			Date currentDate = (Date) objList.get(i)[0];
			currentNeedPayAmount = currentNeedPayAmount.subtract((BigDecimal) objList.get(i - 1)[1]);
			if (!currentDate.before(startCaculateDate)) {
				Date lastChangeAmountTime = (Date) objList.get(i - 1)[0];
				Date satrtTime = lastChangeAmountTime.before(startCaculateDate) ? startCaculateDate
						: lastChangeAmountTime;
				long subDate = DateUtil.subDate(currentDate, satrtTime);
				if (i == objList.size() - 1) {
					subDate = subDate + 1;
				} else {
					satrtTime = DateUtil.getOffsetDateByDay(satrtTime, 1); // 为了正确显示时间
				}
				// 未到计算日期已经收完本金，在satrtTime收完，即最后一次收款
				if (currentNeedPayAmount.compareTo(BigDecimal.ZERO) == 0) {
					displayDate = DateUtil.getOffsetDateByDay(satrtTime, -1);
					if (displayDate.before(cutoverDate)) {
						displayDate = cutoverDate;
					}
					break;
				}
				BigDecimal dist = BigDecimal.valueOf(subDate);
				BigDecimal incrementalAmount = currentNeedPayAmount.multiply(receivable.getPayItem().getBreachRatio())
						.multiply(dist);
				totalBreachAmount = totalBreachAmount.add(incrementalAmount);

				log.debug("第[{}]段，起始时间[{}]，结束时间[{}],共[{}]天，本金[{}],产生违约金[{}]", ++segementCount,
						DateUtil.format(satrtTime, 1), DateUtil.format(currentDate, 1), subDate,
						currentNeedPayAmount, totalBreachAmount);
			}
		}
		result.setDisplayDate(displayDate);
		// 基于假设用户同一天把该应收的所有欠款都交完，临时处理方案
		result.setTotalBreachAmount(totalBreachAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : totalBreachAmount);
		//result.setTotalBreachAmount(totalBreachAmount);
		return result;
	}

	/**
	 * 生成租金
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @param rentType  租用类型 0-长租；1-短租
	 */
	private static void generateRentFee(LocalDate startDate, LocalDate endDate, Integer rentType) {
		List<CommunityReceivablesEntity> result = new ArrayList<CommunityReceivablesEntity>();

		// 生成出租公寓租金及管理费
		StringBuilder baseSql = new StringBuilder(
				"SELECT a.id,a.unitcode,a.additionalArea,a.chargingArea,d.parentPropertyId,a.paymentaccountId,"
						+ "b.payItemsId,c.chargeCategory,c.comment,c.endtime,c.feecaltype,c.itemsname,c.price,c.priceunit,c.starttime,"
						+ "a.acceptanceDate,d.billingDate,c.payDate,d.isCurrentMember,d.iscurrentowner,d.memberid,d.terminationDate,d.endDate,a.rentState,d.id AS memberPropertyId "
						+ "FROM t_community_property a LEFT JOIN t_community_member_property d ON a.id = d.propertyid, "
						+ "t_community_property_pay_items b,t_community_pay_items c"
						+ " WHERE a.id = b.propertyid AND b.payitemsid = c.id AND c.state = 1"
						+ " AND c.chargeCategory = 9 AND d.iscurrentowner = 1 AND d.isCurrentMember = 1"
						+ " AND d.memberType = 2"
						+ " AND d.rentType = " + rentType
						+ " AND a.rentState IN (1,2)"
						+ " AND a.specialFeeFlag IN (0,1,2,3) AND c.starttime <= '"
						+ startDate.toString() + " 00:00:00' AND c.endTime>='" + endDate.toString() + " 00:00:00'");
		StringBuilder estateObjectSql = new StringBuilder(baseSql.toString());
		// 把车位收费也加进来
		estateObjectSql.append(" union ");
		estateObjectSql.append(baseSql.toString());


		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		@SuppressWarnings("unchecked")
		List<Object[]> resultList = session.createNativeQuery(estateObjectSql.toString()).list();
		// 互斥组
		Set<String> groupFeeSet = new HashSet<String>(); 
		for (Object[] temp : resultList) {
			EstateReceivablesVo o = EstateReceivablesVo.getEstateReceivablesVo(temp);
			if ((null == o.getParentPropertyId() || (null != o.getParentPropertyId()
					&& (null == o.getEndDate() || o.getEndDate().isAfter(endDate) || o.getEndDate().isEqual(endDate))))
					&& (((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) >= 0
							&& (int) (endDate.toEpochDay() - o.getBillingDate().toEpochDay()) >= 0)
							|| ((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) <= 0))) {
				CommunityReceivablesEntity receivables = new CommunityReceivablesEntity();
				CommunityEstateEntity estate =  null;
				if (CommunityCache.estateList.size() == 0 || !CommunityCache.estateList
						.containsKey(null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId())) {
					estate = (CommunityEstateEntity) session
							.createQuery("from CommunityEstateEntity a where a.id="
									+ (null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId()))
							.uniqueResult();
					CommunityCache.estateList.put(estate.getId(), estate);
				} else {
					estate = CommunityCache.estateList
							.get(null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId());
				}
				if (estate.getSpecialFeeFlag().equals(0) || estate.getSpecialFeeFlag().equals(2)
						|| estate.getSpecialFeeFlag().equals(3)) {
					receivables.setEstate(estate);
					
					CommunityPayItemsEntity payItem = null;
					if (CommunityCache.payItemsList.size() == 0
							|| !CommunityCache.payItemsList.containsKey(o.getPayItemsId())) {
						payItem = (CommunityPayItemsEntity) session
								.createQuery("from CommunityPayItemsEntity a where a.id=" + o.getPayItemsId())
								.uniqueResult();
						CommunityCache.payItemsList.put(payItem.getId(), payItem);
					} else {
						payItem = CommunityCache.payItemsList.get(o.getPayItemsId());
					}
					receivables.setPayItem(payItem);
					receivables.setPayItemsName(o.getItemsName());
					receivables.setChargeCategory(CommunityPayItemsEntity.getChargeCategoryStr(o.getChargeCategory()));
					receivables.setChargeSource("收费设定");
					receivables.setSubEstateId(null != o.getParentPropertyId() ? o.getId() : null);
					receivables.setState(1);
					BigDecimal receivableAmount = new BigDecimal("0");
					/*
					 * 管理费根据单元的计费日期开始计算管理费 车位管理费根据车位租用时间开始计算
					 */
					LocalDate billingDate = o.getBillingDate().with(TemporalAdjusters.firstDayOfMonth())
							.isEqual(startDate) ? o.getBillingDate() : startDate;

					//根据租赁合同获取价格列表。
					CommunityMemberPropertyEntity memberProperty = 
							 (CommunityMemberPropertyEntity) session
								.createQuery("from CommunityMemberPropertyEntity a where a.id=" + o.getMemberPropertyId())
								.uniqueResult();

					List<CommunityPayItemsPriceEntity> payItemsPriceList = memberProperty.getPayItemsPriceList().stream().filter(s->s.getPayItems().getId().equals(o.getPayItemsId())).collect(Collectors.toList());
					StringBuilder strMemoBuilder = new  StringBuilder();
					if(payItemsPriceList.size() > 0) {
						// 如果存在租赁合同价格，就按租赁合同价格计算
						for(CommunityPayItemsPriceEntity payItemPrice : payItemsPriceList) {
							// 同一个合同存在多个不同时段的价格,根据时间段分段计算价格.
							BigDecimal subReceivableAmount = new BigDecimal("0");
							// 按当前价格，计费的开始时间
							LocalDate startDate2 =  CommunityDateUtil.dateToLocalDate(payItemPrice.getStartTime());
							// 按当前价格，计费的结束时间
							LocalDate endDate2 =  CommunityDateUtil.dateToLocalDate(payItemPrice.getEndTime());
							// 要求生成时间段和计费时间段的交集
							List<LocalDate> interSection = CommunityDateUtil.getInterSection(billingDate, endDate, startDate2, endDate2);
							if(interSection.size() < 2) {
								continue;
							}
							if(payItemPrice.getPeriod().equals(CommunityContext.COMMUNITY_PAY_ITEMS_PRICE_PERIOD_TYPE_MONTH)) {
								// 月长租
								if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_CHARGE)) {
									subReceivableAmount = JepUtil.cal("单价*收费面积/本月天数*计费天数", 4, payItemPrice.getPrice(), o.getChargingArea(),
											CommunityDateUtil.monthDays(interSection.get(1).toString()),
											CommunityDateUtil.subDate(interSection.get(0).toString(), interSection.get(1).toString()));
								} else if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_ADD)) {
									subReceivableAmount = JepUtil.cal("单价*附加面积/本月天数*计费天数", 4, payItemPrice.getPrice(), o.getAdditionalArea(),
											CommunityDateUtil.monthDays(interSection.get(1).toString()),
											CommunityDateUtil.subDate(interSection.get(0).toString(), interSection.get(1).toString()));
								} else if (o.getFeeCalType().equals(CommunityContext.COMMUNITY_PAY_ITEMS_CAL_TYPE_ONCE)) {
									subReceivableAmount = JepUtil.cal("单价/本月天数*计费天数", 4, payItemPrice.getPrice(),
											CommunityDateUtil.monthDays(interSection.get(1).toString()),
											CommunityDateUtil.subDate(interSection.get(0).toString(), interSection.get(1).toString()));
								}
							}
							else if(payItemPrice.getPeriod().equals(CommunityContext.COMMUNITY_PAY_ITEMS_PRICE_PERIOD_TYPE_DAY)) {
								// 日短租
								subReceivableAmount = payItemPrice.getPrice();
							}
							
							String strMemo = interSection.get(0).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")) + "至" + interSection.get(1).format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")) + "按单价：" + payItemPrice.getPrice().setScale(2) + "结算,金额:" + subReceivableAmount.setScale(2) + ";"; 
							strMemoBuilder.append(strMemo);
							receivableAmount = receivableAmount.add(subReceivableAmount);
						}
					}
					else {
						continue;
					}
					
					receivables.setComment(strMemoBuilder.toString());
					receivables.setReceivableAmount(receivableAmount.setScale(2, RoundingMode.HALF_UP));
					receivables.setReceivedAmount(new BigDecimal("0"));
					receivables.setSourceNotes(o.getItemsName() + "(" + o.getUnitCode() + ")");
					receivables.setLockMark(0);
					if (o.getPayDate() != null && o.getPayDate().intValue() > 0 && o.getPayDate().intValue() < 32) {
						// paydate[1,28]按生成应收日期,但当>28时需要判断闰收及大小月，后面有需求再优化。
						try {
							receivables.setReceivableDate(Date
									.from(LocalDate.of(startDate.getYear(), startDate.getMonthValue(), o.getPayDate())
											.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						} catch (DateTimeException e) {
							// 如果paydate不正确，划超出当月范围，应收日期为月未最后一日
							receivables.setReceivableDate(
									Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
						}
					} else {
						// paydate > 31 应收日期为月未最后一日 ,未实现生成下个月2日起逻辑
						receivables
								.setReceivableDate(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					}
					if ((int) (o.getBillingDate().toEpochDay() - startDate.toEpochDay()) >= 0
							&& (int) (endDate.toEpochDay() - o.getBillingDate().toEpochDay()) >= 0) {
						receivables
								.setStartTime(Date.from(billingDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					} else {
						receivables.setStartTime(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					}

					receivables.setEndTime(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
					receivables.setPaymentPeriod(receivables.getReceivableDate());

					if(payItem.getGroupFee() != null) {
						if (getSameGroupReceivableFromDatabase(receivables,session) != null) {
							//如查应表中存在已经生成的互斥收费项目的应收，则跳过不生成该应收。
							continue;
						}
					}
					// 如查应表中存在已经生成相同应收，则通过修改方式修改应收记录
					StringBuilder dbSql = new StringBuilder(
							"select a.* from t_community_receivables a where a.estateid="
									+ (null != o.getParentPropertyId() ? o.getParentPropertyId() : o.getId())
									+ " and a.payItemId = " + receivables.getPayItem().getId()
									+ " and a.receivableDate='" + DateUtil.format(receivables.getReceivableDate(), 0)
									+ "'")
							.append(null != receivables.getSubEstateId() && !receivables.getSubEstateId().equals(0)
									? " and a.subEstateId=" + receivables.getSubEstateId()
									: "");
					// logger.info("----1");
					CommunityReceivablesEntity dbReceivables = (CommunityReceivablesEntity) session
							.createNativeQuery(dbSql.toString()).addEntity(CommunityReceivablesEntity.class)
							.uniqueResult();
					// logger.info("----2");
					if (receivables.getReceivableAmount().doubleValue() > 0 && null == dbReceivables) {
						// 新增的要生成编号
						receivables.setReceivablesNO(getSnLastValue(RECEIVABLESNO_SN_TYPE, session).toString());
						// 已经生成的互斥收费项目的应收，则跳过不生成该应收。
						// 判断标准相同单元、相同应收日期、相同子单元、互斥ID的项目
						if(receivables.getPayItem().getGroupFee() != null) {
							String identify = receivables.getEstate().getId().toString() 
									  + "_" + DateUtil.format(receivables.getReceivableDate(), 0)
									  + "_" + ((null != receivables.getSubEstateId() && !receivables.getSubEstateId().equals(0)) ? receivables.getSubEstateId().toString(): "")
									  + "_" +  receivables.getPayItem().getGroupFee().toString();
									 
							if(groupFeeSet.contains(identify)) {
								continue;
							}
							else {
								groupFeeSet.add(identify);
							}
						}
						result.add(receivables);
					}
				}
			}
		}

		Transaction transaction = session.beginTransaction();
		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {
				StringBuilder sql = new StringBuilder(
						"insert into t_community_receivables(chargeCategory,chargeSource,comment,endTime,"
								+ "payItemsName,paymentPeriod,receivableAmount,receivableDate,"
								+ "receivedAmount,sourceNotes,startTime,estateId,payItemId,oldId,subEstateId,receivablesNO) "
								+ "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE "
								+ "payItemsName=?,receivableDate=?,estateId=?,oldid=?,subEstateId=?");

				PreparedStatement stmt = conn.prepareStatement(sql.toString());
				conn.setAutoCommit(false);
				Integer transCount = 0;
				for (CommunityReceivablesEntity o : result) {
					stmt.setString(1, o.getChargeCategory());
					stmt.setString(2, o.getChargeSource());
					stmt.setString(3, o.getComment());
					stmt.setString(4, DateUtil.formatLongFormat(o.getEndTime()));
					stmt.setString(5, o.getPayItemsName());
					stmt.setString(6, DateUtil.formatLongFormat(o.getPaymentPeriod()));
					stmt.setBigDecimal(7, o.getReceivableAmount());
					stmt.setString(8, DateUtil.formatLongFormat(o.getReceivableDate()));
					stmt.setBigDecimal(9, o.getReceivedAmount());
					stmt.setString(10, o.getSourceNotes());
					stmt.setString(11, DateUtil.formatLongFormat(o.getStartTime()));
					stmt.setInt(12, o.getEstate().getId());
					stmt.setInt(13, o.getPayItem().getId());
					stmt.setString(14, " ");
					if (null != o.getSubEstateId()) {
						stmt.setInt(15, o.getSubEstateId());
					} else {
						stmt.setNull(15, Types.INTEGER);
					}
					// stmt.setString(16 , o.getReceivablesNO());
					stmt.setString(16,
							getSnLastValue(CommunityReceivablesServiceImpl.RECEIVABLESNO_SN_TYPE, session).toString());
					stmt.setString(17, o.getPayItemsName());
					stmt.setString(18, DateUtil.formatLongFormat(o.getReceivableDate()));
					stmt.setInt(19, o.getEstate().getId());
					stmt.setString(20, " ");
					if (null != o.getSubEstateId()) {
						stmt.setInt(21, o.getSubEstateId());
					} else {
						stmt.setNull(21, Types.INTEGER);
					}
					stmt.addBatch();
					transCount++;
					if (transCount % 500 == 0) {
						stmt.executeBatch();
						conn.commit();
					}
					log.info("save:" + transCount);
				}
				stmt.executeBatch();
				conn.commit();
				log.info("租金应收账单自动生成完成！共生成应收[{}]条", result.size());
			}
		});

		transaction.commit();
	}
	
	
	public  void sendReceivablesSms() {
		CommunityContextInfo communityContextInfo = (CommunityContextInfo)SpringHandler.getBean("communityContextInfo");
		StringBuilder sql = new StringBuilder(
				"SELECT a.estateId,b.unitCode,(SUM(a.receivableAmount)- SUM(a.receivedAmount))AS amount ,"
				+ " d.homePhone ,b.paymentAccountId FROM t_community_receivables a INNER JOIN `t_community_property` b ON b.id=a.estateId "
				+ " INNER JOIN t_community_member_property c ON c.propertyId=b.id   "
				+ " INNER JOIN t_account d ON d.id=c.memberId INNER JOIN `t_community_building` e ON e.id=b.buildingId "
				+ " INNER JOIN  t_community_district f on f.id=e.districtId  INNER JOIN `t_community_pay_items` g ON g.id= a.payItemId "
				+ " WHERE a.receivableAmount!=a.receivedAmount AND c.memberType=0 "
				+ " AND c.isCurrentMember=1 AND e.buildingType!=2 AND e.buildingType!=3  "
				+" and (IFNULL (REPLACE (JSON_EXTRACT (`b`.`reservedField`,'$.isSendMessage'),'\"',''),1))=1"
				+ "  AND a.receivableDate<=DATE_FORMAT(LAST_DAY(NOW()),'%Y-%m-%d') AND payDate!=0 GROUP BY a.estateId");

		String day = "";
		String d = DateUtil.formatShortFormat(new Date()).substring(8, 10);
		if(d.equals("07")) {
			day = "10";
		}else if(d.equals("23")) {
			day = "25";
		}else if(d.startsWith("0")){
			day = d.substring(1, 2);
		}else {
			day=d;
		}
		
//		 LocalDate today = LocalDate.now(); 
//	     LocalDate lastDayOfMonth = today.with(TemporalAdjusters.lastDayOfMonth()); 
	     String today = DateUtil.formatShortFormat(new Date());
	     
	
		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		@SuppressWarnings("unchecked")
		List<Object[]> resultList = session.createNativeQuery(sql.toString()).list();
		for (Object[] temp : resultList) {
			String unitCode = (null != temp[1] ? temp[1].toString() : "");
			String amount = null != temp[2] ? temp[2].toString() : "";
			String homePhone = (null != temp[3] ? temp[3].toString() : "");
			String paymentAccountId = (null != temp[4] ? temp[4].toString() : "");
			String receivableAmount = (new BigDecimal(amount)).setScale(2, RoundingMode.HALF_UP).toString();
			if(StringUtils.isNotEmpty(homePhone)) {
//				if(StringUtils.isNotEmpty(paymentAccountId)){
//					String verifyCode = "尊敬的"+unitCode+"业户，您有"+receivableAmount+"元未结，本月"+
//							day+"日银行扣费，为避免逾期违约，请确保余额充足，祝生活愉快。";
//					String templateData = "{\"uniCode\":\""+unitCode+"\",\"amount\":\""+receivableAmount
//							+"\",\"date\":\""+day+"\"}";
//					sendSms(homePhone.replaceAll("、", ",").replaceAll("/", ",")
//							.replaceAll("，", ",").replaceAll(" ", ",")
//							.replaceAll("\\\\", ",")
//							,templateData,"SMS_463678200",verifyCode,communityContextInfo);
//				}else {
//					String verifyCode = "尊敬的"+unitCode+"业户，您有"+receivableAmount+
//							"元未结，为避免逾期违约，请您与客服中心或管家联系缴费，祝您生活愉快。";
//					String templateData = "{\"uniCode\":\""+unitCode+"\",\"amount\":\""+receivableAmount+"\"}";
//					sendSms(homePhone.replaceAll("、", ",").replaceAll("/", ",")
//							.replaceAll("，", ",").replaceAll(" ", ",")
//							.replaceAll("\\\\", ",")
//							,templateData,"SMS_463130146",verifyCode,communityContextInfo);
//				}
				
				String verifyCode = "尊敬的"+unitCode+"业户，截至"+today+"您有"+receivableAmount
						+"元未缴，逾期违约，请尽快缴清，祝生活愉快!";
				String templateData = "{\"unitCode\":\""+unitCode+"\",\"amount\":\""+receivableAmount
						+"\",\"date\":\""+today+"\"}";
				sendSms(homePhone.replaceAll("、", ",").replaceAll("/", ",")
						.replaceAll("，", ",").replaceAll(" ", ",")
						.replaceAll("\\\\", ",")
						,templateData,"SMS_488075011",verifyCode,communityContextInfo);
			}
			
		}
	}
	
	public  void sendSms(String phone,String templateData,String templateCode,String verifyCode,CommunityContextInfo communityContextInfo) {
		ObjectMapper objectMapper = new ObjectMapper();
		ObjectNode json = objectMapper.createObjectNode();
		// 发送短信
		json.put("messageContent", verifyCode);
		json.put("userNumber", phone);
		json.put("scheduleTime", DateUtil.format(new Date(), 1));
		json.put("f", "1");
		json.put("templateCode", templateCode);
		json.put("templateData", templateData);

		Map<String, String> result = null;
		try {
			objectMapper.setSerializationInclusion(Include.NON_NULL);
			log.info("--------------------------------------调用短信接口地址：" + communityContextInfo.smsInterfaceUrl + ";   参数："
					+ json.toString() + "-------------------------");
			result = HttpClientUtil.post(communityContextInfo.smsInterfaceUrl, objectMapper.writeValueAsString(json), "utf-8");
			log.info("--------------------------------------调用短信接口返回：" + result
					+ "----------------------------------------");
		} catch (Exception e) {
			log.info("请求短信接口发送短信时，发生异常：{}", e);
		}
	}
	
	public void queryCommunityDetails() {
//		ObjectMapper objectMapper = new ObjectMapper();
//		ObjectNode json = objectMapper.createObjectNode();
//		//CommunityContextInfo communityContextInfo = (CommunityContextInfo)SpringHandler.getBean("communityContextInfo");
//
//		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
//		Session session = sessionFactory.openSession();
//		Transaction tx = session.beginTransaction();
//		List<DictionaryDataEntity> dictionaryData  = session
//				.createQuery("select distinct a from DictionaryDataEntity a "
//						+ " inner join a.dictionary b where b.directoryCode='queryCommunityDetails' and a.dataName='querySendDetails' and a.state=1").list();
//		String interfaceUrl = "";
//		if (null != dictionaryData && dictionaryData.size() > 0) {
//			interfaceUrl = dictionaryData.get(0).getDataKey();
//			Map<String, String> result = null;
//			try {
//				objectMapper.setSerializationInclusion(Include.NON_NULL);
//				log.info("--------------------------------------调用短信接口地址：" + interfaceUrl + ";   参数："
//						+ json.toString() + "-------------------------");
//				result = HttpClientUtil.post(interfaceUrl, objectMapper.writeValueAsString(json), "utf-8");
//				log.info("--------------------------------------调用短信接口返回：" + result
//						+ "----------------------------------------");
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}
//		
		ISmsService smsService = (ISmsService) SpringHandler.getBean("smsService");
		SmsReq req = new SmsReq();
		smsService.querySendDetails(req);
	}


}
