package com.foshan.service.community;

import com.foshan.form.community.request.CommunityPayItemsPriceReq;
import com.foshan.form.response.IResponse;

public interface ICommunityPayItemsPriceService {
    public IResponse getCommunityPayItemsPriceList(CommunityPayItemsPriceReq req);
	public IResponse addCommunityPayItemsPrice(CommunityPayItemsPriceReq req);
	public IResponse modifyCommunityPayItemsPrice(CommunityPayItemsPriceReq req);
	public IResponse deleteCommunityPayItemsPrice(CommunityPayItemsPriceReq req);
	public IResponse getCommunityPayItemsPriceInfo(CommunityPayItemsPriceReq req);
	public IResponse contractUnbindingPayItemsPrice(CommunityPayItemsPriceReq req) ;
	public IResponse contractBindingPayItemsPrice(CommunityPayItemsPriceReq req);
}
