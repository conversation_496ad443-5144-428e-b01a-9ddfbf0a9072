package com.foshan.service.community;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityMeterAllocationReq;
import com.foshan.form.response.IResponse;

public interface ICommunityMeterAllocationService {
	public IResponse calTotalAllocation(CommunityMeterAllocationReq req);
	public IResponse submitTotalAllocation(CommunityMeterAllocationReq req);
	public IResponse getTotalAllocationList(CommunityMeterAllocationReq req);
	public IResponse getMeterAllocationItemList(CommunityMeterAllocationReq req);
	public IResponse calItemAllocation(CommunityMeterAllocationReq req);
	public IResponse refreshItemAllocation(CommunityMeterAllocationReq req);
	public IResponse submitItemAllocation(CommunityMeterAllocationReq req);
	public IResponse submitAllocationReceivables(CommunityMeterAllocationReq req);
	public IResponse exportAllocationList(CommunityMeterAllocationReq req);
	public void transExcel(CommunityMeterAllocationReq res,HttpServletResponse response) ;
}
