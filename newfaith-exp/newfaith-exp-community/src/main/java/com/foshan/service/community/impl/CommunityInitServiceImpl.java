package com.foshan.service.community.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.form.community.request.CommunityInitReq;
import com.foshan.form.community.response.init.CommunityInitRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.ICommunityInitService;
import com.foshan.util.community.CommunityCache;
import com.foshan.util.community.CommunityPage;

@Transactional
@Service("communityInitService")
public class CommunityInitServiceImpl extends GenericCommunityService implements ICommunityInitService {
	//private final static Logger loggers = LoggerFactory.getLogger(CommunityInitServiceImpl.class);
	@Override
	public void initEstateList() {
		// TODO Auto-generated method stub
		List<CommunityEstateEntity> estateList = communityEstateDao
				.getListByHql("from CommunityEstateEntity a where a.state=1");
		estateList.forEach(o -> {
			CommunityCache.estateList.put(o.getId(), o);
		});
	}

	@Override
	public void initPayItemsList() {
		// TODO Auto-generated method stub
		List<CommunityPayItemsEntity> payItemsList = communityPayItemsDao
				.getListByHql("from CommunityPayItemsEntity a where a.state=1");
		payItemsList.forEach(o -> {
			CommunityCache.payItemsList.put(o.getId(), o);
			if(o.getChargeCategory().equals(3)||o.getChargeCategory().equals(6)||o.getChargeCategory().equals(7)) {
				CommunityCache.payItemsNameList.put(o.getItemsName(),o.getId());
			}
		});
		
	}

	@Override
	public void initMeterList() {
		// TODO Auto-generated method stub
		List<CommunityMeterEntity> meterList = communityMeterDao.getListByHql("select a from CommunityMeterEntity a");
		meterList.forEach(o -> {
			CommunityCache.meterList.put(o.getId(), o);
		});
	}

	@Override
	public void initReceivables() {
		// TODO Auto-generated method stub
		List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao
				.getListByHql("select a from CommunityReceivablesEntity a where a.receivableAmount-a.receivedAmount>0");
		receivablesList.forEach(o -> {
			CommunityCache.putReceivableCache(o, CommunityCache.receivableCalEstateList);
		});
	}



	@SuppressWarnings("rawtypes")
	@Override
	public IResponse checkInitCache(CommunityInitReq req) {
		// TODO Auto-generated method stub
		CommunityInitRes res = new CommunityInitRes();
		if (StringUtils.isNotEmpty(req.getTarget())) {
			try {
				CommunityPage page = null;
				if (req.getTarget().equals("receivables")) {
					if (null != req.getKey()) {
						page = CommunityCache.getPage(CommunityCache.receivableCalEstateList.get(Integer.parseInt(req.getKey())),
								req.getPageSize(), req.getRequestPage());
					} else {
						page = CommunityCache.getPage(CommunityCache.receivableCalEstateList, req.getPageSize(),
								req.getRequestPage());
					}

				} else if (req.getTarget().equals("estate")) {
					page = CommunityCache.getPage(CommunityCache.estateList, req.getPageSize(), req.getRequestPage());
				} else if (req.getTarget().equals("meter")) {
					page = CommunityCache.getPage(CommunityCache.meterList, req.getPageSize(), req.getRequestPage());
				}

				res.setCurrentPage(page.getCurrentPage());
				res.setPageSize(page.getPageSize());
				res.setTotal(page.getTotalPage());
				res.setTotalResult(page.getTotalCount());
				res.setResult(page.getResultList());
			} catch (Exception ex) {
				res.setRet("9999");
				res.setRetInfo(ex.getMessage());
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCacheInfo(CommunityInitReq req) {
		CommunityInitRes res = new CommunityInitRes();
		if (req.getTarget().equals("estate")) {
			res.setResult(
					StringUtils.isNotEmpty(req.getKey()) ? CommunityCache.estateList.get(Integer.parseInt(req.getKey()))
							: CommunityCache.estateList);
		} else if (req.getTarget().equals("meter")) {
			res.setResult(
					StringUtils.isNotEmpty(req.getKey()) ? CommunityCache.meterList.get(Integer.parseInt(req.getKey()))
							: CommunityCache.meterList);
		} else if (req.getTarget().equals("payItems")) {
			res.setResult(StringUtils.isNotEmpty(req.getKey())
					? CommunityCache.payItemsList.get(Integer.parseInt(req.getKey()))
					: CommunityCache.payItemsList);
		}
		return res;
	}	
}
