package com.foshan.service.community.impl;

import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityPropertyServiceEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.form.community.CommunityPropertyServiceForm;
import com.foshan.form.community.request.CommunityPropertyServiceReq;
import com.foshan.form.community.response.communityPropertyService.CreatePropertyServicePdfRes;
import com.foshan.form.community.response.communityPropertyService.GetCommunityPropertyServiceInfoRes;
import com.foshan.form.community.response.communityPropertyService.GetCommunityPropertyServiceListRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityPropertyServiceService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.FreemarkUtil;
import com.foshan.util.community.CommunityCache;

@Transactional
@Service("communityPropertyServiceService")
public class CommunityPropertyServiceServiceImpl extends GenericCommunityService
		implements ICommunityPropertyServiceService {

	@Override
	public IResponse getCommunityPropertyServiceList(CommunityPropertyServiceReq req) {
		GetCommunityPropertyServiceListRes res = new GetCommunityPropertyServiceListRes();
		Page<CommunityPropertyServiceEntity> page = new Page<CommunityPropertyServiceEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

		page = communityPropertyServiceDao.queryPage(page, queryHql(req));

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityPropertyServiceForm propertyServiceForm = new CommunityPropertyServiceForm(o.getId(),
					o.getEventsCode(), o.getTitle(), null, o.getServiceState(), o.getCentent(), o.getAgent(),
					o.getClient(), o.getPhone(), (null != o.getEstate() ? o.getEstate().getId() : null), o.getAddress(),
					o.getServiceType(), o.getReportContent(), o.getCompetentDepartment(), o.getHandler(), null, null,
					o.getServiceContent(), o.getReceiver(), o.getServiceEvaluation(), o.getLaborCost().toString(),
					o.getMaterialCost().toString(), o.getIsGeneratedBills(), o.getOrderCategory());
			propertyServiceForm.setPayItemsId(o.getPayItemsId());
			propertyServiceForm
					.setReportTime(null != o.getReportTime() ? DateUtil.formatLongFormat(o.getReportTime()) : "");
			propertyServiceForm.setDispatchingTime(
					null != o.getDispatchingTime() ? DateUtil.formatLongFormat(o.getDispatchingTime()) : "");
			propertyServiceForm.setCompletionTime(
					null != o.getCompletionTime() ? DateUtil.formatLongFormat(o.getCompletionTime()) : "");
			propertyServiceForm
					.setCreateTime(null != o.getCreateTime() ? DateUtil.formatLongFormat(o.getCreateTime()) : "");
			propertyServiceForm.setFeedback(o.getFeedback());
			propertyServiceForm.setVisitCommissione(o.getVisitCommissione());
			res.getCommunityPropertyServiceList().add(propertyServiceForm);
		});

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	public String queryHql(CommunityPropertyServiceReq req) {
		StringBuilder hql = new StringBuilder(
				"select distinct a from CommunityPropertyServiceEntity a INNER JOIN a.estate b where 1=1");
		hql.append(null != req.getPropertyId() ? " and b.id=" + req.getPropertyId() : "")
				.append(StringUtils.isNotEmpty(req.getEventsCode())
						? " and a.eventsCode like'%" + req.getEventsCode() + "%'"
						: "")
				.append(StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())
						? " and a.reportTime>='" + req.getStartTime() + "' and a.reportTime<='" + req.getEndTime() + "'"
						: "")
				.append(StringUtils.isNotEmpty(req.getStartTime()) && !StringUtils.isNotEmpty(req.getEndTime())
						? " and a.reportTime>='" + req.getStartTime() + "'"
						: "")
				.append(!StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime())
						? " and a.reportTime<='" + req.getEndTime() + "'"
						: "")
				.append(null != req.getOrderCategory() ? " and a.orderCategory=" + req.getOrderCategory() : "");

		hql.append(" ORDER BY a.reportTime DESC");
		return hql.toString();
	}

	public IResponse exportCommunityPropertyServiceList(CommunityPropertyServiceReq req, HttpServletResponse response)
			throws ParseException {
		ExportExcelRes res = new ExportExcelRes();
		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("eventsCode", "报修号");
		heardMap.put("serviceType", "报修类别");
		heardMap.put("reportTime", "报修时间");
		heardMap.put("serviceState", "服务状态");
		heardMap.put("client", "申请人");
		heardMap.put("phone", "联系电话");
		heardMap.put("address", "维修地点");
		heardMap.put("serviceContent", "维修内容");

		heardMap.put("handler", "维修员");
		heardMap.put("completionTime", "完成时间");
		heardMap.put("laborCost", "人工费");
		heardMap.put("materialCost", "材料费");
		heardMap.put("isGeneratedBills", "是否入账");
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();

		List<CommunityPropertyServiceEntity> list = communityPropertyServiceDao.getListByHql(queryHql(req), "");
		list.forEach(o -> {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("eventsCode", o.getEventsCode());
			map.put("serviceType", o.getServiceType());
			map.put("reportTime", null != o.getReportTime() ? DateUtil.formatLongFormat(o.getReportTime()) : "");
			map.put("serviceState", o.getServiceState());
			map.put("client", o.getClient());
			map.put("phone", o.getPhone());
			map.put("address", o.getAddress());
			map.put("serviceContent", o.getServiceContent());

			map.put("handler", o.getHandler());
			map.put("completionTime",
					null != o.getCompletionTime() ? DateUtil.formatLongFormat(o.getCompletionTime()) : "");
			map.put("laborCost", o.getLaborCost());
			map.put("materialCost", o.getMaterialCost());
			map.put("isGeneratedBills", null != o.getIsGeneratedBills() && o.getIsGeneratedBills() == 0 ? "否"
					: (null != o.getIsGeneratedBills() && o.getIsGeneratedBills() == 1 ? "是" : "其他"));
			dataList.add(map);
		});
		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("300"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

		return res;
	}

	@Override
	@Audit(operate = "新增报修服务")
	public IResponse addCommunityPropertyService(CommunityPropertyServiceReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getPropertyId() && null != req.getIsGeneratedBills()) {
			CommunityPayItemsEntity payItems = null != req.getPayItemsId()
					? communityPayItemsDao.get(req.getPayItemsId())
					: null;

//			if (payItems == null) {
//				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
//				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到收费项目信息！");
//				return res;
//			}

			CommunityEstateEntity estate = communityEstateDao.get(req.getPropertyId());
			if (estate == null) {
				res.setRet(ResponseContext.RES_DATA_NULL_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到对应的单元信息！");
				return res;
			}
			CommunityPropertyServiceEntity propertyService = null;
			boolean state = true;
			String eventsCode = "";
			while (state) {
				eventsCode = "BX" + DateUtil.formatShortFormat(new Date()).replaceAll("-", "")
						+ CodeUtil.generateCode(6);
				propertyService = communityPropertyServiceDao.findUnique(
						"select a from " + "CommunityPropertyServiceEntity a where a.eventsCode='" + eventsCode + "'",
						"");
				if (null == propertyService) {
					state = false;
				}
			}
			propertyService = new CommunityPropertyServiceEntity();
			propertyService.setEventsCode(eventsCode);
			propertyService.setPayItemsId(req.getPayItemsId());
			propertyService.setAddress(StringUtils.isNotEmpty(req.getAddress()) ? req.getAddress() : "");
			propertyService.setAgent(StringUtils.isNotEmpty(req.getAgent()) ? req.getAgent() : "");
			propertyService.setCentent(StringUtils.isNotEmpty(req.getCentent()) ? req.getCentent() : "");
			propertyService.setClient(StringUtils.isNotEmpty(req.getClient()) ? req.getClient() : "");
			propertyService.setCompetentDepartment(
					StringUtils.isNotEmpty(req.getCompetentDepartment()) ? req.getCompetentDepartment() : "");
			try {
				propertyService.setCompletionTime(StringUtils.isNotEmpty(req.getCompletionTime())
						? DateUtil.parseShortFormat(req.getCompletionTime())
						: null);
				propertyService.setDispatchingTime(StringUtils.isNotEmpty(req.getDispatchingTime())
						? DateUtil.parseShortFormat(req.getDispatchingTime())
						: null);
				propertyService.setReportTime(
						StringUtils.isNotEmpty(req.getReportTime()) ? DateUtil.parseShortFormat(req.getReportTime())
								: null);
			} catch (ParseException e) {
				e.printStackTrace();
			}

			propertyService.setHandler(StringUtils.isNotEmpty(req.getHandler()) ? req.getHandler() : "");
			propertyService.setIsGeneratedBills(req.getIsGeneratedBills());
			propertyService.setLaborCost(
					StringUtils.isNotEmpty(req.getLaborCost()) ? new BigDecimal(req.getLaborCost()) : BigDecimal.ZERO);
			propertyService.setMaterialCost(
					StringUtils.isNotEmpty(req.getMaterialCost()) ? new BigDecimal(req.getMaterialCost())
							: BigDecimal.ZERO);
			propertyService.setPhone(StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : "");
			propertyService.setEstate(null != req.getPropertyId() ? communityEstateDao.get(req.getPropertyId()) : null);
			propertyService.setReceiver(StringUtils.isNotEmpty(req.getReceiver()) ? req.getReceiver() : "");
			propertyService
					.setReportContent(StringUtils.isNotEmpty(req.getReportContent()) ? req.getReportContent() : "");
			propertyService
					.setServiceContent(StringUtils.isNotEmpty(req.getServiceContent()) ? req.getServiceContent() : "");
			propertyService.setServiceEvaluation(
					StringUtils.isNotEmpty(req.getServiceEvaluation()) ? req.getServiceEvaluation() : "");
			propertyService.setServiceState(StringUtils.isNotEmpty(req.getServiceState()) ? req.getServiceState() : "");
			propertyService.setServiceType(StringUtils.isNotEmpty(req.getServiceType()) ? req.getServiceType() : "");
			propertyService.setTitle(StringUtils.isNotEmpty(req.getTitle()) ? req.getTitle() : "");

			propertyService.setFeedback(StringUtils.isNotEmpty(req.getFeedback()) ? req.getFeedback() : "");
			propertyService.setVisitCommissione(
					StringUtils.isNotEmpty(req.getVisitCommissione()) ? req.getVisitCommissione() : "");
			propertyService.setOrderCategory(req.getOrderCategory());

			if (req.getIsGeneratedBills() == 1) {
				createReceivables(propertyService, payItems);
			}
			communityPropertyServiceDao.save(propertyService);

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	public void createReceivables(CommunityPropertyServiceEntity propertyService, CommunityPayItemsEntity payItems) {
		// CommunityEstateEntity estate =
		// communityEstateDao.get(propertyService.getPropertyId());
		CommunityReceivablesEntity communityReceivables = new CommunityReceivablesEntity();
		communityReceivables.setChargeCategory("有偿服务费");

		communityReceivables.setChargeSource(propertyService.getEventsCode());
		communityReceivables.setLockMark(0);
		communityReceivables.setComment("维修单号:" + propertyService.getEventsCode() + ";报修内容:"
				+ propertyService.getReportContent() + ";（材料费" + propertyService.getMaterialCost().toString() + "元，人工费"
				+ propertyService.getLaborCost().toString() + "元）");
		Date now = new Date();
		communityReceivables.setEndTime(now);
		communityReceivables.setPayItemsName(payItems.getItemsName());
		communityReceivables.setPaymentPeriod(now);
		communityReceivables.setReceivableAmount(propertyService.getLaborCost().add(propertyService.getMaterialCost()));
		communityReceivables.setReceivableDate(now);
		communityReceivables.setReceivedAmount(BigDecimal.ZERO);
		communityReceivables.setSourceNotes("");
		communityReceivables.setStartTime(now);
		communityReceivables.setEstate(propertyService.getEstate());
		communityReceivables.setState(1);
		communityReceivables.setReceivablesNO(
				generateSnLastValue(CommunityReceivablesServiceImpl.RECEIVABLESNO_SN_TYPE).toString());
		communityReceivables.setPayItem(payItems);
		communityReceivablesDao.save(communityReceivables);

		CommunityCache.putReceivableCache(communityReceivables, CommunityCache.receivableCalEstateList);
	}

	@Override
	@Audit(operate = "修改报修服务")
	public IResponse modifyCommunityPropertyService(CommunityPropertyServiceReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getPropertyServiceId()) {
			CommunityPropertyServiceEntity propertyService = communityPropertyServiceDao
					.get(req.getPropertyServiceId());
			if (null != propertyService && propertyService.getIsGeneratedBills() == 0) {
				CommunityPayItemsEntity payItems = null != req.getPayItemsId()
						? communityPayItemsDao.get(req.getPayItemsId())
						: null;
				if (req.getIsGeneratedBills() == 1 && payItems == null) {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "找不到收费项目信息！");
					return res;
				}
				propertyService.setAddress(
						StringUtils.isNotEmpty(req.getAddress()) ? req.getAddress() : propertyService.getAddress());
				propertyService
						.setAgent(StringUtils.isNotEmpty(req.getAgent()) ? req.getAgent() : propertyService.getAgent());
				propertyService.setCentent(
						StringUtils.isNotEmpty(req.getCentent()) ? req.getCentent() : propertyService.getCentent());
				propertyService.setClient(
						StringUtils.isNotEmpty(req.getClient()) ? req.getClient() : propertyService.getClient());
				propertyService.setCompetentDepartment(
						StringUtils.isNotEmpty(req.getCompetentDepartment()) ? req.getCompetentDepartment()
								: propertyService.getClient());
				try {
					propertyService.setCompletionTime(StringUtils.isNotEmpty(req.getCompletionTime())
							? DateUtil.parseLongFormat(req.getCompletionTime())
							: propertyService.getCompletionTime());
					propertyService.setDispatchingTime(StringUtils.isNotEmpty(req.getDispatchingTime())
							? DateUtil.parseLongFormat(req.getDispatchingTime())
							: propertyService.getDispatchingTime());
					propertyService.setReportTime(
							StringUtils.isNotEmpty(req.getReportTime()) ? DateUtil.parseLongFormat(req.getReportTime())
									: propertyService.getReportTime());
				} catch (ParseException e) {
					e.printStackTrace();
				}
				// propertyService.setEventsCode(StringUtils.isNotEmpty(req.getEventsCode()) ?
				// req.getEventsCode() : propertyService.getEventsCode());
				propertyService.setHandler(
						StringUtils.isNotEmpty(req.getHandler()) ? req.getHandler() : propertyService.getHandler());
				propertyService.setIsGeneratedBills(null != req.getIsGeneratedBills() ? req.getIsGeneratedBills()
						: propertyService.getIsGeneratedBills());
				propertyService
						.setLaborCost(StringUtils.isNotEmpty(req.getLaborCost()) ? new BigDecimal(req.getLaborCost())
								: propertyService.getLaborCost());
				propertyService.setMaterialCost(
						StringUtils.isNotEmpty(req.getMaterialCost()) ? new BigDecimal(req.getMaterialCost())
								: propertyService.getMaterialCost());
				propertyService
						.setPhone(StringUtils.isNotEmpty(req.getPhone()) ? req.getPhone() : propertyService.getPhone());
				// propertyService.setPropertyId(null!=req.getPropertyId() ? req.getPropertyId()
				// : propertyService.getPropertyId());
				propertyService.setReceiver(
						StringUtils.isNotEmpty(req.getReceiver()) ? req.getReceiver() : propertyService.getReceiver());
				propertyService.setReportContent(StringUtils.isNotEmpty(req.getReportContent()) ? req.getReportContent()
						: propertyService.getReportContent());
				propertyService
						.setServiceContent(StringUtils.isNotEmpty(req.getServiceContent()) ? req.getServiceContent()
								: propertyService.getServiceContent());
				propertyService.setServiceEvaluation(
						StringUtils.isNotEmpty(req.getServiceEvaluation()) ? req.getServiceEvaluation()
								: propertyService.getServiceEvaluation());
				propertyService.setServiceState(StringUtils.isNotEmpty(req.getServiceState()) ? req.getServiceState()
						: propertyService.getServiceState());
				propertyService.setServiceType(StringUtils.isNotEmpty(req.getServiceType()) ? req.getServiceType()
						: propertyService.getServiceType());
				propertyService
						.setTitle(StringUtils.isNotEmpty(req.getTitle()) ? req.getTitle() : propertyService.getTitle());
				propertyService.setServiceType(StringUtils.isNotEmpty(req.getServiceType()) ? req.getServiceType()
						: propertyService.getServiceType());
				propertyService.setFeedback(
						StringUtils.isNotEmpty(req.getFeedback()) ? req.getFeedback() : propertyService.getFeedback());
				propertyService.setVisitCommissione(
						StringUtils.isNotEmpty(req.getVisitCommissione()) ? req.getVisitCommissione()
								: propertyService.getVisitCommissione());
				if (req.getIsGeneratedBills() == 1) {
					createReceivables(propertyService, payItems);
				}
				propertyService.setOrderCategory(
						null != req.getOrderCategory() ? req.getOrderCategory() : propertyService.getOrderCategory());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除报修服务")
	public IResponse deleteCommunityPropertyService(CommunityPropertyServiceReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getPropertyServiceId()) {
			CommunityPropertyServiceEntity propertyService = communityPropertyServiceDao
					.get(req.getPropertyServiceId());
			if (null != propertyService && propertyService.getIsGeneratedBills() == 0) {
				propertyService.setEstate(null);
				communityPropertyServiceDao.delete(propertyService);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse getCommunityPropertyServiceInfo(CommunityPropertyServiceReq req) {
		GetCommunityPropertyServiceInfoRes res = new GetCommunityPropertyServiceInfoRes();
		if (null != req.getPropertyServiceId()) {
			CommunityPropertyServiceEntity o = communityPropertyServiceDao.get(req.getPropertyServiceId());
			if (null != o) {
				CommunityPropertyServiceForm propertyServiceForm = new CommunityPropertyServiceForm(o.getId(),
						o.getEventsCode(), o.getTitle(), null, o.getServiceState(), o.getCentent(), o.getAgent(),
						o.getClient(), o.getPhone(), (null != o.getEstate() ? o.getEstate().getId() : null),
						o.getAddress(), o.getServiceType(), o.getReportContent(), o.getCompetentDepartment(),
						o.getHandler(), null, null, o.getServiceContent(), o.getReceiver(), o.getServiceEvaluation(),
						o.getLaborCost().toString(), o.getMaterialCost().toString(), o.getIsGeneratedBills(),
						o.getOrderCategory());
				propertyServiceForm.setFeedback(o.getFeedback());
				propertyServiceForm.setVisitCommissione(o.getVisitCommissione());
				propertyServiceForm.setUnitCode(null != o.getEstate() ? o.getEstate().getUnitCode() : "");
				propertyServiceForm.setPayItemsId(o.getPayItemsId());
				propertyServiceForm
						.setReportTime(null != o.getReportTime() ? DateUtil.formatLongFormat(o.getReportTime()) : "");
				propertyServiceForm.setDispatchingTime(
						null != o.getDispatchingTime() ? DateUtil.formatLongFormat(o.getDispatchingTime()) : "");
				propertyServiceForm.setCompletionTime(
						null != o.getCompletionTime() ? DateUtil.formatLongFormat(o.getCompletionTime()) : "");
				propertyServiceForm
						.setCreateTime(null != o.getCreateTime() ? DateUtil.formatLongFormat(o.getCreateTime()) : "");

				res.setCommunityPropertyServiceForm(propertyServiceForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse createPropertyServicePdf(CommunityPropertyServiceReq req) {
		CreatePropertyServicePdfRes res = new CreatePropertyServicePdfRes();
		if (null != req.getPropertyServiceId()) {
			CommunityPropertyServiceEntity propertyService = communityPropertyServiceDao
					.get(req.getPropertyServiceId());
			if (null != propertyService) {
				Map<String, Object> params = new HashMap<String, Object>();

				params.put("eventsCode", propertyService.getEventsCode());
				params.put("client", propertyService.getClient());
				params.put("agent", propertyService.getAgent());
				params.put("address", propertyService.getAddress());
				params.put("phone", propertyService.getPhone());
//				if(null != propertyService.getPayItemsId()) {
//					CommunityPayItemsEntity communityPayItems = communityPayItemsDao.get(propertyService.getPayItemsId());
//					params.put("chargeCategory", communityPayItems.getChargeCategoryStr(communityPayItems.getChargeCategory()));
//				}else {
//					params.put("chargeCategory", "");
//				}
				params.put("dispatchingTime",
						null != propertyService.getDispatchingTime()
								? DateUtil.formatLongFormat(propertyService.getDispatchingTime())
								: "");

//				params.put("completionTime", null!=propertyService.getCompletionTime() ? 
//						DateUtil.formatLongFormat(propertyService.getCompletionTime()) : "");
//				params.put("serviceContent", StringUtils.isNotEmpty(propertyService.getServiceContent()) ? 
//						propertyService.getServiceContent():"");
//				params.put("handler", StringUtils.isNotEmpty(propertyService.getHandler()) ? 
//						propertyService.getHandler() : "");
//				params.put("laborCost", null!=propertyService.getLaborCost() ? propertyService.getLaborCost().toString() : "");
//				params.put("materialCost", null!=propertyService.getMaterialCost() ? 
//						propertyService.getMaterialCost().toString() : "");
//				params.put("feedback", StringUtils.isNotEmpty(propertyService.getFeedback()) ? 
//						propertyService.getFeedback() : "");
//				params.put("visitCommissione", StringUtils.isNotEmpty(propertyService.getVisitCommissione()) ? 
//						propertyService.getVisitCommissione() : "");

				params.put("reportContent",
						StringUtils.isNotEmpty(propertyService.getReportContent()) ? propertyService.getReportContent()
								: "");
				params.put("centent",
						StringUtils.isNotEmpty(propertyService.getCentent()) ? propertyService.getCentent() : "");

//				if(StringUtils.isNotEmpty(propertyService.getServiceEvaluation()) 
//						&& (propertyService.getServiceEvaluation().equals("非常满意")||
//						propertyService.getServiceEvaluation().equals("很满意"))) {
//					params.put("number", 0);
//				}else if(StringUtils.isNotEmpty(propertyService.getServiceEvaluation()) 
//						&& propertyService.getServiceEvaluation().equals("满意")){
//					params.put("number", 1);
//				}else if(StringUtils.isNotEmpty(propertyService.getServiceEvaluation()) 
//						&& propertyService.getServiceEvaluation().equals("基本满意")){
//					params.put("number", 2);
//				}else if(StringUtils.isNotEmpty(propertyService.getServiceEvaluation()) 
//						&& propertyService.getServiceEvaluation().equals("不满意")){
//					params.put("number", 3);
//				}else {
//					params.put("number", 10);
//				}

				Date date = new Date();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
				String today = sdf.format(date);
				String dir = contextInfo.getAssetFilePath() + File.separator + "propertyService" + File.separator
						+ today + File.separator;
				createMultilayerFile(dir);
				File templateFile = new File(
						contextInfo.getAssetFilePath() + File.separator + "propertyServiceTemplate.ftl");
				if (!templateFile.exists()) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo("模板不存在，请重新上传模板！");
					return res;
				}
				String fileName = propertyService.getEventsCode();

				if (FreemarkUtil.generatePdf(contextInfo.getAssetFilePath(), "propertyServiceTemplate.ftl", params,
						dir + fileName + ".pdf", null)) {
					res.setPdfUrl(contextInfo.getAssetFileUrl() + "/" + "propertyService" + "/" + today + "/" + "/"
							+ fileName + ".pdf");
//					CommunityReceiptForm communityReceiptForm = new CommunityReceiptForm();
//					communityReceiptForm.setCommunityReceiptId(communityReceipt.getId());
//					communityReceiptForm.setPdfUrl(communityReceipt.getPdfUrl());
//					res.setCommunityReceiptForm(communityReceiptForm);
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
				}

				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	private static boolean createMultilayerFile(String dir) {
		try {
			File dirPath = new File(dir);
			if (!dirPath.exists()) {
				dirPath.mkdirs();
			}
		} catch (Exception e) {
			System.out.println("创建多层目录操作出错: " + e.getMessage());
			e.printStackTrace();
			return false;
		}
		return true;
	}

}
