package com.foshan.service.community.impl;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityReservationRecordEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityReservatPeriodEntity;
import com.foshan.entity.community.CommunityReservationActivitiesEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityEstateForm;
import com.foshan.form.community.CommunityMemberForm;
import com.foshan.form.community.CommunityMemberPropertyForm;
import com.foshan.form.community.CommunityReservatPeriodForm;
import com.foshan.form.community.CommunityReservationActivitiesForm;
import com.foshan.form.community.CommunityReservationDateForm;
import com.foshan.form.community.CommunityReservationRecordForm;
import com.foshan.form.community.request.CommunityReservationRecordReq;
import com.foshan.form.community.response.communityReservationRecord.GetCommunityReservationRecordInfoRes;
import com.foshan.form.community.response.communityReservationRecord.GetCommunityReservationRecordListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityReservationRecordService;
import com.foshan.util.DateUtil;

@Transactional
@Service("communityReservationRecordService")
public class CommunityReservationRecordServiceImpl extends GenericCommunityService implements ICommunityReservationRecordService {

	@Override
	public IResponse getCommunityReservationRecordList(CommunityReservationRecordReq req) {
		GetCommunityReservationRecordListRes res = new GetCommunityReservationRecordListRes();
		Page<CommunityReservationRecordEntity> page = new Page<CommunityReservationRecordEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		Object userObj = getPrincipal(true);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityReservationRecordEntity a "
				+ "inner join a.reservatPeriod b inner join b.reservationDate c ");
	
		hql.append(" where").append(null != req.getState() ? "  a.state=" + req.getState()  :
			"  a.state="+EntityContext.RECORD_STATE_VALID)
			.append(null!=req.getActivitiesId() ?" and a.activities.id="+req.getActivitiesId():"")
			.append(null!=req.getReservationState()?" and a.reservationState="+req.getReservationState():"")
			.append(null!=req.getPeriodId() ? " and b.id="+req.getPeriodId():"")
			.append(null!=req.getReservationDateId() ? " and c.id="+req.getReservationDateId():"");
		if(userObj instanceof CommunityMemberEntity) {
			CommunityMemberEntity member = (CommunityMemberEntity)userObj;
			hql.append(" and a.member.id="+member.getId());
		}
		hql.append(" ORDER BY a.createTime DESC");
		page = communityReservationRecordDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		page.getResultList().forEach(o -> {
			CommunityReservationRecordForm reservationRecordForm = new CommunityReservationRecordForm(o.getId(),
					o.getCentent(),o.getReservationNum(),o.getReservationState()) ;
			
			CommunityReservationDateForm reservationDateForm = new CommunityReservationDateForm(o.getReservatPeriod().getReservationDate().getId(),
					DateUtil.format(o.getReservatPeriod().getReservationDate().getReservationDate(),0),o.getReservatPeriod().getReservationDate().getIsStart(),o.getReservatPeriod().getReservationDate().getState());
			CommunityReservatPeriodForm periodForm = new CommunityReservatPeriodForm(o.getReservatPeriod().getId(),o.getReservatPeriod().getMaxNum(),
					o.getReservatPeriod().getOrders(),o.getReservatPeriod().getStartTime(),
					o.getReservatPeriod().getEndTime(),o.getReservatPeriod().getState(),o.getReservatPeriod().getOverplusNum());
			reservationDateForm.getPeriodFormList().add(periodForm);
			reservationRecordForm.setReservationDateForm(reservationDateForm);
			CommunityReservationActivitiesForm activities = new CommunityReservationActivitiesForm(
					o.getActivities().getId(),o.getActivities().getActivitiesName(),o.getActivities().getCentent(),o.getActivities().getAgreement(),
					sdf.format(o.getActivities().getStartTime()),sdf.format(o.getActivities().getEndTime()),o.getActivities().getNeedConfirmAgreement(),
					sdf.format(o.getActivities().getCreateTime()),sdf.format(o.getActivities().getLastModifyTime()),o.getActivities().getState(),o.getActivities().getOrders()) ;
			CommunityEstateForm publicArea = new CommunityEstateForm();
			CommunityEstateEntity property = o.getActivities().getEstate();
			publicArea.setPropertyId(property.getId());
			publicArea.setCreateTime(sdf.format(property.getCreateTime()));
			publicArea.setLastModifyTime(sdf.format(property.getLastModifyTime()));
			publicArea.setPropertyName(property.getPropertyName());
			activities.setPublicArea(publicArea);
			reservationRecordForm.setActivitiesForm(activities);
			CommunityMemberForm memberForm = new CommunityMemberForm();
			memberForm.setMemberId(o.getMember().getId());
			memberForm.setEmail(o.getMember().getEmail());
			memberForm.setHomePhone(o.getMember().getHomePhone());
			memberForm.setNickName(o.getMember().getNickName());
			memberForm.setOfficePhone(o.getMember().getOfficePhone());
			memberForm.setPhone(o.getMember().getPhone());
			memberForm.setRegistName(o.getMember().getRegistName());
			memberForm.setSex(o.getMember().getSex());
			memberForm.setSmartcardId(o.getMember().getSmartcardId());
			memberForm.setHeadImage(StringUtils.isNotEmpty(o.getMember().getHeadImage()) ? o.getMember().getHeadImage() : "");
			memberForm.setUserName(StringUtils.isNotEmpty(o.getMember().getUserName()) ? o.getMember().getUserName() : "");
			
			o.getMember().getMemberPropertyList().forEach(p->{
				CommunityMemberPropertyForm  memberPropertyForm = new CommunityMemberPropertyForm();
//				memberPropertyForm.setAuditState(p.getAuditState());
				memberPropertyForm.setCreateTime(sdf.format(o.getCreateTime()));
//				memberPropertyForm.setInTime(sdf.format(p.getInTime()));
				memberPropertyForm.setMemberPropertyId(p.getId());
				memberPropertyForm.setPropertyForm(getCommunityPropertyForm(p.getProperty(),null,null));
//				memberPropertyForm.setRelation(p.getRelation());
				memberForm.getMemberPropertyFormList().add(memberPropertyForm);
			});
			
			reservationRecordForm.setMember(memberForm);

			res.getReservationRecordList().add(reservationRecordForm);

		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "新增预约记录")
	public IResponse addCommunityReservationRecord(CommunityReservationRecordReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		if(userObj instanceof CommunityMemberEntity ) {
			if (null!=req.getActivitiesId()&& null!=req.getPeriodId()  && null!=req.getReservationNum()) {
				CommunityReservatPeriodEntity period = communityReservatPeriodDao.get(req.getPeriodId());
				CommunityReservationActivitiesEntity activities = communityReservationActivitiesDao.get(req.getActivitiesId());
				if(null == period || null==activities || null==period.getReservationDate() || 
						null==period.getReservationDate().getActivities()||
						!(req.getActivitiesId().equals(period.getReservationDate().getActivities().getId())) || 
						(null!=period && period.getState()!=EntityContext.RECORD_STATE_VALID)) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE );
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				if(period.getReservationDate().getIsStart() != 1) {
					res.setRet("0001");
					res.setRetInfo("活动还没开放预约！");
					return res;
				}
				if(req.getReservationNum()>period.getOverplusNum()) {
					res.setRet("0001");
					res.setRetInfo("对不起，您预约的人数已超过剩余可预约人数！");
					return res;
				}
				CommunityMemberEntity member = (CommunityMemberEntity) userObj;
				CommunityReservationRecordEntity record = new CommunityReservationRecordEntity();
				record.setActivities(activities);
				record.setCentent(StringUtils.isNotEmpty(req.getCentent()) ? req.getCentent() : "");
				record.setLastModifyTime(new Timestamp(new Date().getTime()));
				record.setMember(member);
				record.setReservationNum(req.getReservationNum());
				Integer overplusNum = period.getOverplusNum()-req.getReservationNum();
				period.setOverplusNum(overplusNum>0 ? overplusNum : 0);
				record.setReservationState(0);
				record.setReservatPeriod(period);
				record.setState(EntityContext.RECORD_STATE_VALID);
				
				communityReservationRecordDao.save(record);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			}
		}else {
			res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
			return res;
		}
		return res;
	}

	@Override
	@Audit(operate = "修改预约记录")
	public IResponse modifyCommunityReservationRecord(CommunityReservationRecordReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getRecordId() && null!=req.getReservationState()) {
			CommunityReservationRecordEntity record = communityReservationRecordDao.get(req.getRecordId());
			if (null != record) {
				if(record.getReservationState()==0) {
					if(req.getReservationState()!=1 && req.getReservationState()!=2){
						res.setRet(ResponseContext.RES_DATA_ERROR_CODE );
						res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
						return res;
					}
					record.setReservationState(req.getReservationState());
					if(req.getReservationState() == 2) {
						Integer overplusNum = record.getReservatPeriod().getOverplusNum()+record.getReservationNum();
						record.getReservatPeriod().setOverplusNum(overplusNum);
					}
				}else {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE );
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	@Audit(operate = "删除预约记录")
	public IResponse deleteCommunityReservationRecord(CommunityReservationRecordReq req) {
		
		return null;
	}

	@Override
	public IResponse getCommunityReservationRecordInfo(CommunityReservationRecordReq req) {
		GetCommunityReservationRecordInfoRes res = new GetCommunityReservationRecordInfoRes();
		if (null != req.getRecordId()) {
			CommunityReservationRecordEntity o = communityReservationRecordDao.get(req.getRecordId());
			if (null != o) {
				DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
				CommunityReservationRecordForm reservationRecordForm = new CommunityReservationRecordForm(o.getId(),
						o.getCentent(),o.getReservationNum(),o.getReservationState()) ;
				
				CommunityReservationDateForm reservationDateForm = new CommunityReservationDateForm(o.getReservatPeriod().getReservationDate().getId(),
						DateUtil.format(o.getReservatPeriod().getReservationDate().getReservationDate(),0),o.getReservatPeriod().getReservationDate().getIsStart(),o.getReservatPeriod().getReservationDate().getState());
				CommunityReservatPeriodForm periodForm = new CommunityReservatPeriodForm(o.getReservatPeriod().getId(),o.getReservatPeriod().getMaxNum(),
						o.getReservatPeriod().getOrders(),o.getReservatPeriod().getStartTime(),
						o.getReservatPeriod().getEndTime(),o.getReservatPeriod().getState(),o.getReservatPeriod().getOverplusNum());
				reservationDateForm.getPeriodFormList().add(periodForm);
				reservationRecordForm.setReservationDateForm(reservationDateForm);
				CommunityReservationActivitiesForm activities = new CommunityReservationActivitiesForm(
						o.getActivities().getId(),o.getActivities().getActivitiesName(),o.getActivities().getCentent(),o.getActivities().getAgreement(),
						sdf.format(o.getActivities().getStartTime()),sdf.format(o.getActivities().getEndTime()),o.getActivities().getNeedConfirmAgreement(),
						sdf.format(o.getActivities().getCreateTime()),sdf.format(o.getActivities().getLastModifyTime()),o.getActivities().getState(),o.getActivities().getOrders()) ;
				CommunityEstateForm publicArea = new CommunityEstateForm();
				CommunityEstateEntity property = o.getActivities().getEstate();
				publicArea.setPropertyId(property.getId());
				publicArea.setCreateTime(sdf.format(property.getCreateTime()));
				publicArea.setLastModifyTime(sdf.format(property.getLastModifyTime()));
				publicArea.setPropertyName(property.getPropertyName());
				activities.setPublicArea(publicArea);
				reservationRecordForm.setActivitiesForm(activities);
				CommunityMemberForm memberForm = new CommunityMemberForm();
				memberForm.setMemberId(o.getMember().getId());
				memberForm.setEmail(o.getMember().getEmail());
				memberForm.setHomePhone(o.getMember().getHomePhone());
				memberForm.setNickName(o.getMember().getNickName());
				memberForm.setOfficePhone(o.getMember().getOfficePhone());
				memberForm.setPhone(o.getMember().getPhone());
				memberForm.setRegistName(o.getMember().getRegistName());
				memberForm.setSex(o.getMember().getSex());
				memberForm.setSmartcardId(o.getMember().getSmartcardId());
				memberForm.setHeadImage(StringUtils.isNotEmpty(o.getMember().getHeadImage()) ? o.getMember().getHeadImage() : "");
				memberForm.setUserName(StringUtils.isNotEmpty(o.getMember().getUserName()) ? o.getMember().getUserName() : "");
				o.getMember().getMemberPropertyList().forEach(p->{
					CommunityMemberPropertyForm  memberPropertyForm = new CommunityMemberPropertyForm();
//					memberPropertyForm.setAuditState(p.getAuditState());
					memberPropertyForm.setCreateTime(sdf.format(o.getCreateTime()));
//					memberPropertyForm.setInTime(sdf.format(p.getInTime()));
					memberPropertyForm.setMemberPropertyId(p.getId());
					memberPropertyForm.setPropertyForm(getCommunityPropertyForm(p.getProperty(),null,null));
//					memberPropertyForm.setRelation(p.getRelation());
					memberForm.getMemberPropertyFormList().add(memberPropertyForm);
				});
				reservationRecordForm.setMember(memberForm);
				res.setReservationRecordForm(reservationRecordForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}
