package com.foshan.service.community.impl.statistics;

import static java.util.stream.Collectors.groupingBy;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jdbc.Work;

import com.foshan.form.community.statistics.RefundVo;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityCache;

import cn.hutool.core.util.ArrayUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RefundUtil {
	public static String getRefundSql(Integer[] districtIds, String startDate, String endDate, Integer queryFlag) {
		StringBuilder sql = new StringBuilder("select aa.districtCode,aa.districtName,aa.buildingName,")
				.append("aa.unitcode,aa.roomcode,aa.roomnumber,aa.payername,sum(changeAmount) as totalRefund,")
				.append("sum(arrears) as totalArrears,sum(amount) as totalReceivable,")
				.append(getRefundPayitemsNameColumn())
				.append(" from (" + getBaseRefundSql(districtIds, startDate, endDate, queryFlag) + ") aa ")
				.append("group by aa.unitCode ").append("ORDER BY aa.districtorder,aa.buildingname,aa.unitcode");

		return sql.toString();
	}

	public static String getBaseRefundSql(Integer[] districtIds, String startDate, String endDate, Integer queryFlag) {
		StringBuilder sql = new StringBuilder("").append(
				"SELECT a.districtcode,a.districtname,b.buildingname,c.unitcode,CONCAT(c.floor,c.roomnumber) AS roomcode,")
				.append("c.roomnumber,e.payername,g.payitemsname,sum(e.amount) as amount,")
				.append("sum(d.changeamount) as changeAmount,sum(e.amount-d.changeamount) AS arrears,a.districtorder ")
				.append("FROM t_community_district a ")
				.append("INNER JOIN t_community_building b ON a.id= b.districtid AND a.id<>11 ")
				.append(null != districtIds && districtIds.length > 0
						? "and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " ")
				.append("INNER JOIN t_community_property c ON b.id=c.buildingid ")
				.append("INNER JOIN t_community_receivables_changes d ON c.id=d.estateid AND d.changetype=2 ")
				.append(queryFlag.equals(0)
						? " and d.changedate>='" + startDate + " 00:00:00' and d.changedate<='" + endDate
								+ " 23:59:59' "
						: " ")
				.append("INNER JOIN t_community_receipt e ON d.receiptid=e.id ")
				.append(queryFlag.equals(1)
						? " and e.receiptDate>='" + startDate + " 00:00:00' and e.receiptDate<='" + endDate
								+ " 23:59:59' "
						: " ")
				.append("INNER JOIN t_community_receipt_receivables f ON e.id=f.receiptid ")
				.append("INNER JOIN t_community_receivables g ON f.receivablesid=g.id ")
				.append("group by c.unitcode,g.payitemsname ");

		return sql.toString();
	}

	public static String getRefundPayitemsNameColumn() {
		StringBuilder sql = new StringBuilder(changeColumn()).append(changeRefundColumn())
				.append(changeReceivableColumn());

		return sql.toString().substring(0, sql.length() - 1);
	}

	public static String changeColumn() {
		StringBuilder sql = new StringBuilder(
				"MAX(CASE payitemsname WHEN 'IC卡工本费' THEN changeamount ELSE '' END ) AS IC卡工本费退款金额,"
						+ "MAX(CASE payitemsname WHEN 'IC卡押金' THEN changeamount ELSE '' END ) AS IC卡押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '临时施工押金' THEN changeamount ELSE '' END ) AS 临时施工押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '人员出入证押金' THEN changeamount ELSE '' END ) AS 人员出入证押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '代收代缴房屋契税' THEN changeamount ELSE '' END ) AS 代收代缴房屋契税退款金额,"
						+ "MAX(CASE payitemsname WHEN '代收垃圾清运费' THEN changeamount ELSE '' END ) AS 代收垃圾清运费退款金额,"
						+ "MAX(CASE payItemsName WHEN '公共设施保证金' THEN changeamount ELSE '' END) AS 公共设施保证金退款金额,"
						+ "MAX(CASE payItemsName WHEN '平板电脑押金' THEN changeamount ELSE '' END) AS 平板电脑押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '往来款' THEN changeamount ELSE '' END ) AS 往来款退款金额,"
						+ "MAX(CASE payitemsname WHEN '损害财产维修押金' THEN changeamount ELSE '' END ) AS 损害财产维修押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '搬运押金' THEN changeamount ELSE '' END ) AS 搬运押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '施工保证金' THEN changeamount ELSE '' END ) AS 施工保证金退款金额,"
						+ "MAX(CASE payitemsname WHEN '施工证押金' THEN changeamount ELSE '' END ) AS 施工证押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '水电押金' THEN changeamount ELSE '' END ) AS 水电押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '沙石费用' THEN changeamount ELSE '' END ) AS 沙石费用退款金额,"
						+ "MAX(CASE payitemsname WHEN '灭火器押金' THEN changeamount ELSE '' END ) AS 灭火器押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '留宿管理押金' THEN changeamount ELSE '' END ) AS 留宿管理押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '租金保证金' THEN changeamount ELSE '' END ) AS 租金保证金退款金额,"
						+ "MAX(CASE payitemsname WHEN '管理费押金' THEN changeamount ELSE '' END ) AS 管理费押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '自行车押金' THEN changeamount ELSE '' END ) AS 自行车押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '自行车款' THEN changeamount ELSE '' END ) AS 自行车款退款金额,"
						+ "MAX(CASE payitemsname WHEN '蓝牙卡押金' THEN changeamount ELSE '' END ) AS 蓝牙卡押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '装修保证金' THEN changeamount ELSE '' END ) AS 装修保证金退款金额,"
						+ "MAX(CASE payitemsname WHEN '装修押金' THEN changeamount ELSE '' END ) AS 装修押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '装修许可证押金' THEN changeamount ELSE '' END ) AS 装修许可证押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '证件工本费' THEN changeamount ELSE '' END ) AS 证件工本费退款金额,"
						+ "MAX(CASE payitemsname WHEN '车辆出入证押金' THEN changeamount ELSE '' END ) AS 车辆出入证押金退款金额,"
						+ "MAX(CASE payitemsname WHEN '重型机械进场押金' THEN changeamount ELSE '' END ) AS 重型机械进场押金退款金额,");
		return sql.toString();
	}

	public static String changeRefundColumn() {
		StringBuilder sql = new StringBuilder(
				"MAX(CASE payitemsname WHEN 'IC卡工本费' THEN arrears ELSE '' END ) AS IC卡工本费未退金额,"
						+ "MAX(CASE payitemsname WHEN 'IC卡押金' THEN arrears ELSE '' END ) AS IC卡押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '临时施工押金' THEN arrears ELSE '' END ) AS 临时施工押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '人员出入证押金' THEN arrears ELSE '' END ) AS 人员出入证押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '代收代缴房屋契税' THEN arrears ELSE '' END ) AS 代收代缴房屋契税未退金额,"
						+ "MAX(CASE payitemsname WHEN '代收垃圾清运费' THEN arrears ELSE '' END ) AS 代收垃圾清运费未退金额,"
						+ "MAX(CASE payItemsName WHEN '公共设施保证金' THEN arrears ELSE '' END) AS 公共设施保证金未退金额,"
						+ "MAX(CASE payItemsName WHEN '平板电脑押金' THEN arrears ELSE '' END) AS 平板电脑押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '往来款' THEN arrears ELSE '' END ) AS 往来款未退金额,"
						+ "MAX(CASE payitemsname WHEN '损害财产维修押金' THEN arrears ELSE '' END ) AS 损害财产维修押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '搬运押金' THEN arrears ELSE '' END ) AS 搬运押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '施工保证金' THEN arrears ELSE '' END ) AS 施工保证金未退金额,"
						+ "MAX(CASE payitemsname WHEN '施工证押金' THEN arrears ELSE '' END ) AS 施工证押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '水电押金' THEN arrears ELSE '' END ) AS 水电押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '沙石费用' THEN arrears ELSE '' END ) AS 沙石费用未退金额,"
						+ "MAX(CASE payitemsname WHEN '灭火器押金' THEN arrears ELSE '' END ) AS 灭火器押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '留宿管理押金' THEN arrears ELSE '' END ) AS 留宿管理押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '租金保证金' THEN arrears ELSE '' END ) AS 租金保证金未退金额,"
						+ "MAX(CASE payitemsname WHEN '管理费押金' THEN arrears ELSE '' END ) AS 管理费押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '自行车押金' THEN arrears ELSE '' END ) AS 自行车押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '自行车款' THEN arrears ELSE '' END ) AS 自行车款未退金额,"
						+ "MAX(CASE payitemsname WHEN '蓝牙卡押金' THEN arrears ELSE '' END ) AS 蓝牙卡押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '装修保证金' THEN arrears ELSE '' END ) AS 装修保证金未退金额,"
						+ "MAX(CASE payitemsname WHEN '装修押金' THEN arrears ELSE '' END ) AS 装修押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '装修许可证押金' THEN arrears ELSE '' END ) AS 装修许可证押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '证件工本费' THEN arrears ELSE '' END ) AS 证件工本费未退金额,"
						+ "MAX(CASE payitemsname WHEN '车辆出入证押金' THEN arrears ELSE '' END ) AS 车辆出入证押金未退金额,"
						+ "MAX(CASE payitemsname WHEN '重型机械进场押金' THEN arrears ELSE '' END ) AS 重型机械进场押金未退金额,");
		return sql.toString();
	}

	public static String changeReceivableColumn() {
		StringBuilder sql = new StringBuilder("MAX(CASE payitemsname WHEN 'IC卡工本费' THEN amount ELSE '' END ) AS IC卡工本费,"
				+ "MAX(CASE payitemsname WHEN 'IC卡押金' THEN amount ELSE '' END ) AS IC卡押金,"
				+ "MAX(CASE payitemsname WHEN '临时施工押金' THEN amount ELSE '' END ) AS 临时施工押金,"
				+ "MAX(CASE payitemsname WHEN '人员出入证押金' THEN amount ELSE '' END ) AS 人员出入证押金,"
				+ "MAX(CASE payitemsname WHEN '代收代缴房屋契税' THEN amount ELSE '' END ) AS 代收代缴房屋契税,"
				+ "MAX(CASE payitemsname WHEN '代收垃圾清运费' THEN amount ELSE '' END ) AS 代收垃圾清运费,"
				+ "MAX(CASE payItemsName WHEN '公共设施保证金' THEN amount ELSE '' END) AS 公共设施保证金,"
				+ "MAX(CASE payItemsName WHEN '平板电脑押金' THEN amount ELSE '' END) AS 平板电脑押金,"
				+ "MAX(CASE payitemsname WHEN '往来款' THEN amount ELSE '' END ) AS 往来款,"
				+ "MAX(CASE payitemsname WHEN '损害财产维修押金' THEN amount ELSE '' END ) AS 损害财产维修押金,"
				+ "MAX(CASE payitemsname WHEN '搬运押金' THEN amount ELSE '' END ) AS 搬运押金,"
				+ "MAX(CASE payitemsname WHEN '施工保证金' THEN amount ELSE '' END ) AS 施工保证金,"
				+ "MAX(CASE payitemsname WHEN '施工证押金' THEN amount ELSE '' END ) AS 施工证押金,"
				+ "MAX(CASE payitemsname WHEN '水电押金' THEN amount ELSE '' END ) AS 水电押金,"
				+ "MAX(CASE payitemsname WHEN '沙石费用' THEN amount ELSE '' END ) AS 沙石费用,"
				+ "MAX(CASE payitemsname WHEN '灭火器押金' THEN amount ELSE '' END ) AS 灭火器押金,"
				+ "MAX(CASE payitemsname WHEN '留宿管理押金' THEN amount ELSE '' END ) AS 留宿管理押金,"
				+ "MAX(CASE payitemsname WHEN '租金保证金' THEN amount ELSE '' END ) AS 租金保证金,"
				+ "MAX(CASE payitemsname WHEN '管理费押金' THEN amount ELSE '' END ) AS 管理费押金,"
				+ "MAX(CASE payitemsname WHEN '自行车押金' THEN amount ELSE '' END ) AS 自行车押金,"
				+ "MAX(CASE payitemsname WHEN '自行车款' THEN amount ELSE '' END ) AS 自行车款,"
				+ "MAX(CASE payitemsname WHEN '蓝牙卡押金' THEN amount ELSE '' END ) AS 蓝牙卡押金,"
				+ "MAX(CASE payitemsname WHEN '装修保证金' THEN amount ELSE '' END ) AS 装修保证金,"
				+ "MAX(CASE payitemsname WHEN '装修押金' THEN amount ELSE '' END ) AS 装修押金,"
				+ "MAX(CASE payitemsname WHEN '装修许可证押金' THEN amount ELSE '' END ) AS 装修许可证押金,"
				+ "MAX(CASE payitemsname WHEN '证件工本费' THEN amount ELSE '' END ) AS 证件工本费,"
				+ "MAX(CASE payitemsname WHEN '车辆出入证押金' THEN amount ELSE '' END ) AS 车辆出入证押金,"
				+ "MAX(CASE payitemsname WHEN '重型机械进场押金' THEN amount ELSE '' END ) AS 重型机械进场押金,");
		return sql.toString();
	}

	
	public static String getRefundSql(Integer[] districtIds, String[] chargeCategorys, String[] payItemsNames,
			String startDate, String endDate, Integer refundFlag, Integer arrearsFlag, Integer receivableFlag,
			Integer queryFlag) {

		String base1 = null != refundFlag && refundFlag.equals(1)
				? "GROUP_CONCAT(DISTINCT CONCAT('MAX(CASE payitemsname WHEN ''',g.payitemsname,''' then changeamount else 0 end) as ',g.payitemsname,'已退款金额')),"
				: "";
		String base2 = null != arrearsFlag && arrearsFlag.equals(1)
				? "GROUP_CONCAT(DISTINCT CONCAT('MAX(CASE payitemsname WHEN ''',g.payitemsname,''' then arrears else 0 end) as ',g.payitemsname,'未退金额')),"
				: "";
		String base3 = null != receivableFlag && receivableFlag.equals(1)
				? "GROUP_CONCAT(DISTINCT CONCAT('MAX(CASE payitemsname WHEN ''',g.payitemsname,''' then amount else 0 end) as ',g.payitemsname)),"
				: "";

		String base = base1
				+ (null != refundFlag && refundFlag.equals(1) && null != arrearsFlag && arrearsFlag.equals(1) ? "',',"
						: "")
				+ base2
				+ (null != arrearsFlag && arrearsFlag.equals(1) && null != receivableFlag && receivableFlag.equals(1)
						? "',',"
						: (null != refundFlag && refundFlag.equals(1) && null != receivableFlag
								&& receivableFlag.equals(1) ? "','," : ""))
				+ base3;

		base = base.endsWith(",") ? base.substring(0, base.length() - 1) : base;

		StringBuilder sql = new StringBuilder(
				"SELECT a.districtcode,CONCAT('select aa.districtCode,aa.districtName,aa.buildingName,',")
				.append("'aa.unitcode,aa.roomcode,aa.roomnumber,aa.payername,sum(changeAmount) as totalRefund,',")
				.append("'sum(arrears) as totalArrears,sum(amount) as totalReceivable,',")
				.append("CONCAT(" + base + "),' ',")
				.append("'from ("
						+ "SELECT t.districtCode,t.districtname,t.buildingname,t.buildingorder,t.unitcode,"
						+ "t.roomcode,t.roomnumber,t.payername,t.payitemsname,SUM(t.amount) AS amount,SUM(t.changeamount) AS changeAmount,"
						+ "SUM(t.amount-t.changeamount) AS arrears,t.districtorder FROM ("
						
						+ "SELECT a.districtcode,a.districtname,b.buildingname,b.buildingorder,c.unitcode,CONCAT(c.floor,c.roomnumber) AS roomcode,',")
				.append("'c.roomnumber,e.payername,g.payitemsname,e.amount,IF(d.changeamount IS  NULL,0,d.changeamount) AS changeamount,',")
				.append("'a.districtorder ',")
				.append("'FROM t_community_district a INNER JOIN t_community_building b ON a.id= b.districtid AND a.id<>11 and a.districtcode=',a.districtcode,' ',")
				.append(null != districtIds && districtIds.length > 0
						? "' and districtid in(" + StringUtils.join(districtIds, ",") + ") ',"
						: "")
				.append("'INNER JOIN t_community_property c ON b.id=c.buildingid ',")
				
				.append(" 'INNER JOIN t_community_receivables g ON g.estateId=c.id  INNER JOIN  t_community_pay_items h ON (h.itemsName LIKE ''%押金%'' OR h.itemsName LIKE ''%保证金%'') AND h.id=g.payItemId "
						+ "INNER JOIN  t_community_receipt_receivables f ON g.id=f.receivablesId  INNER JOIN t_community_receipt e ON f.receiptId=e.id ' ,")
				.append(queryFlag.equals(1)
						? " ' and e.receiptDate>=''" + startDate + " 00:00:00'' and e.receiptDate<=''" + endDate
								+ " 23:59:59'' ',"
						: "")
				
				.append("' LEFT JOIN  t_community_receivables_changes d ON g.estateId=d.estateid AND d.changetype=2 AND d.receiptId=e.id  ',")
				.append(queryFlag.equals(0)
						? "' and d.changedate>=''" + startDate + " 00:00:00'' and d.changedate<=''" + endDate
								+ " 23:59:59'' ',"
						: "")
//				.append("'INNER JOIN t_community_receipt_receivables f ON e.id=f.receiptid ',")
//				.append("'INNER JOIN t_community_receivables g ON f.receivablesid=g.id ',")
//				.append(null != chargeCategorys && chargeCategorys.length > 0
//						? "' and g.chargeCategory in(''" + StringUtils.join(chargeCategorys, "'',''") + "'') ',"
//						: "")
//				.append(null != payItemsNames && payItemsNames.length > 0
//						? "' and g.payItemsName in(''" + StringUtils.join(payItemsNames, "'',''") + "'') ',"
//						: " ")
				.append("'GROUP BY e.id "
						
						+ ") t GROUP BY t.unitcode,t.payitemsname "
						
						+ ") aa group by aa.unitcode ',")
				.append("'ORDER BY aa.districtorder,aa.buildingorder,aa.unitcode') AS subsql ")
				.append("FROM t_community_district a ")
				.append("INNER JOIN t_community_building b ON a.id= b.districtid AND a.id<>11 ")
				.append(null != districtIds && districtIds.length > 0
						? "and districtid in(" + StringUtils.join(districtIds, ",") + ") "
						: " ")
				.append("INNER JOIN t_community_property c ON b.id=c.buildingid ")
			
				.append("INNER JOIN t_community_receivables g ON g.estateId=c.id  INNER JOIN  t_community_pay_items h ON (h.itemsName LIKE '%押金%' OR h.itemsName LIKE '%保证金%') AND h.id=g.payItemId "
						+ "INNER JOIN  t_community_receipt_receivables f ON g.id=f.receivablesId  INNER JOIN t_community_receipt e ON f.receiptId=e.id")
				.append(queryFlag.equals(1)
						? " and e.receiptDate>='" + startDate + " 00:00:00' and e.receiptDate<='" + endDate
								+ " 23:59:59' "
						: " ")
				.append(" LEFT JOIN  t_community_receivables_changes d ON g.estateId=d.estateid AND d.changetype=2 AND d.receiptId=e.id  ")
				.append(queryFlag.equals(0) ? " and d.changedate>='"
						+ startDate + " 00:00:00' and d.changedate<='" + endDate + " 23:59:59' " : " ")
//				.append(null != chargeCategorys && chargeCategorys.length > 0
//						? "and g.chargeCategory in('" + StringUtils.join(chargeCategorys, "','") + "') "
//						: " ")
//				.append(null != payItemsNames && payItemsNames.length > 0
//						? "and g.payItemsName in('" + StringUtils.join(payItemsNames, "','") + "') "
//						: " ")
				.append("GROUP BY a.districtcode");

		return sql.toString();
	}

	public static Map<String, Object> getRefundList(Integer[] districtIds, String[] chargeCategorys,
			String[] payItemsNames, String startDate, String endDate, Integer refundFlag, Integer arrearsFlag,
			Integer receivableFlag, Integer queryFlag) {
		Map<String, Object> res = new HashMap<>();

		String[] title = { "楼盘名称", "楼阁", "单元编号", "单元号", "房号", "业主姓名" };
		List<RefundVo> dataList = new LinkedList<>();

		Set<String> feeTitle = new TreeSet<>();

		// 获取动态sql
		String groupSql = getRefundSql(districtIds, chargeCategorys, payItemsNames, startDate, endDate, refundFlag,
				arrearsFlag, receivableFlag, queryFlag);

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {
				Statement st = conn.createStatement();
				ResultSet group_rs = st.executeQuery(groupSql);

				while (group_rs.next()) {
					String districtCode = group_rs.getString("districtCode");
					String subSql = group_rs.getString("subSql");
					ResultSet subrs = st.executeQuery(subSql);

					while (subrs.next()) {
						ResultSetMetaData rsmd = subrs.getMetaData();
						RefundVo vo = new RefundVo();
						vo.setDistrictCode(districtCode.length() == 1 ? "0" + districtCode : districtCode);
						vo.setDistrictName(subrs.getString("districtName"));
						vo.setBuildingName(subrs.getString("buildingName"));
						vo.setRoomCode(subrs.getString("roomCode"));
						vo.setUnitCode(subrs.getString("unitCode"));
						vo.setRoomNumber(subrs.getString("roomNumber"));
						vo.setUserName(subrs.getString("payerName"));
						vo.setTotalArrears(subrs.getString("totalArrears"));
						vo.setTotalReceivable(subrs.getString("totalReceivable"));
						vo.setTotalRefund(subrs.getString("totalRefund"));
						Map<String, String> feeMap = new TreeMap<>();
						for (int i = 11; i <= rsmd.getColumnCount(); i++) {
							feeTitle.add(rsmd.getColumnName(i));
							feeMap.put(rsmd.getColumnName(i), subrs.getString(i));
						}
						vo.setFeeMap(feeMap);
						dataList.add(vo);
					}

				}

			}
		});
		session.close();

		for (String fee : feeTitle) {
			title = ArrayUtil.insert(title, title.length, fee);
		}

		res.put("title", title);
		res.put("data", dataList);

		return res;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static void transRefundExcel(HttpServletResponse response) {

		// 获取缓存数据并进行处理
		Map<String, Map<String, Object>> temp1 = CommunityCache.statisticsCache.get("退款报表");
		String key = "";
		for (String o : temp1.keySet()) {
			key = o;
		}

		String[] paras = key.split("_");
		Integer refundFlag = Integer.parseInt(paras[1]);
		Integer arrearsFlag = Integer.parseInt(paras[2]);
		Integer receivableFlag = Integer.parseInt(paras[3]);

		Map<String, Object> temp2 = temp1.get(key);
		String[] titles = (String[]) temp2.get("title");
		for (int i = 1; i <= (refundFlag + arrearsFlag + receivableFlag); i++) {
			titles = ArrayUtil.insert(titles, titles.length, "合计");
		}

		List<RefundVo> dataList = (List<RefundVo>) temp2.get("data");

		// 将缓存的队列数据按楼盘编码进行排序
		Map<String, List<RefundVo>> districtReceivableMap = (HashMap<String, List<RefundVo>>) dataList.stream()
				.collect(groupingBy(RefundVo::getNewDistrictCode));

		if (districtReceivableMap.size() > 1) {
			districtReceivableMap.put("000全区", dataList);
		}

		TreeSet<String> keys = new TreeSet<>(Comparator.naturalOrder());
		districtReceivableMap.keySet().forEach(o -> {
			keys.add(o);
		});

		XSSFWorkbook wb = new XSSFWorkbook();
		Map<String, XSSFCellStyle> cellStyle = ExcelUtil.getExcelStyle(wb);

		for (String o : keys) {

			Sheet sheet = wb.createSheet(o.substring(3));

			LinkedList<RefundVo> tempList = new LinkedList(districtReceivableMap.get(o));

			// 构建表头
			Row title1 = sheet.createRow(0);
			Row title2 = null;
			if (refundFlag + arrearsFlag + receivableFlag > 1) {
				title2 = sheet.createRow(1);
			}
			createTitle(sheet, cellStyle, title1, title2, titles, refundFlag, arrearsFlag, receivableFlag);

			// 获取每楼盘下单元单元编号进行排序
			List<RefundVo> unitList = tempList.stream().sorted(Comparator.comparing(RefundVo::getUnitCode))
					.collect(Collectors.toList());

			int rowNumber = null != title2 ? 2 : 1;
			for (RefundVo vo : unitList) {
				Row row = sheet.createRow(rowNumber);
				// 楼盘名称
				Cell cell0 = row.createCell(0, CellType.STRING);
				cell0.setCellStyle(cellStyle.get("cell_left"));
				cell0.setCellValue(vo.getDistrictName());

				// 楼阁
				Cell cell1 = row.createCell(1, CellType.STRING);
				cell1.setCellStyle(cellStyle.get("cell_left"));
				cell1.setCellValue(vo.getBuildingName());

				// 单元编号
				Cell cell2 = row.createCell(2, CellType.STRING);
				cell2.setCellStyle(cellStyle.get("cell_left"));
				cell2.setCellValue(vo.getUnitCode());

				// 单元号
				Cell cell3 = row.createCell(3, CellType.STRING);
				cell3.setCellStyle(cellStyle.get("cell_left"));
				cell3.setCellValue(vo.getRoomCode());

				// 房号
				Cell cell4 = row.createCell(4, CellType.STRING);
				cell4.setCellStyle(cellStyle.get("cell_left"));
				cell4.setCellValue(vo.getRoomNumber());

				// 业主姓名
				Cell cell5 = row.createCell(5, CellType.STRING);
				cell5.setCellStyle(cellStyle.get("cell_left"));
				cell5.setCellValue(vo.getUserName());
				sheet.setColumnWidth(5, 10 * 256);

				if (sheet.getSheetName().equals("全区")) {
					for (int i = 1; i < titles.length - 5; i++) {
						Cell dcell = row.createCell(i + 5, CellType.NUMERIC);
						dcell.setCellStyle(cellStyle.get("cell_right2"));
						String payItemsName = "";
						if (refundFlag + arrearsFlag + receivableFlag == 1) {
							payItemsName = title1.getCell(i + 5).getStringCellValue();
						} else if (refundFlag + arrearsFlag + receivableFlag == 2) {
							if (title2.getCell(i + 5).getStringCellValue().equals("已退款金额")) {
								payItemsName = title1.getCell(i + 5).getStringCellValue()
										+ title2.getCell(i + 5).getStringCellValue();
							} else {
								if (i % 2 == 0) {
									payItemsName = title1.getCell(i + 4).getStringCellValue()
											+ title2.getCell(i + 5).getStringCellValue();
								} else {
									payItemsName = title1.getCell(i + 5).getStringCellValue()
											+ title2.getCell(i + 5).getStringCellValue();
								}
								payItemsName = payItemsName.endsWith("未退金额") ? payItemsName
										: payItemsName.substring(0, payItemsName.length() - 2);
							}
						} else {
							if (title2.getCell(i + 5).getStringCellValue().equals("已退款金额")) {
								payItemsName = title1.getCell(i + 5).getStringCellValue()
										+ title2.getCell(i + 5).getStringCellValue();
							} else if (title2.getCell(i + 5).getStringCellValue().equals("未退金额")) {
								payItemsName = title1.getCell(i + 4).getStringCellValue()
										+ title2.getCell(i + 5).getStringCellValue();
							} else {
								payItemsName = title1.getCell(i + 3).getStringCellValue();
							}
						}

						payItemsName = payItemsName.startsWith("合计") ? "合计" : payItemsName;

						if (vo.getFeeMap().containsKey(payItemsName)) {
							dcell.setCellValue(new BigDecimal(vo.getFeeMap().get(payItemsName)).doubleValue());
						} else {
							if (payItemsName.equals("合计")) {
								dcell.setCellStyle(cellStyle.get("total_double"));
								if (refundFlag + arrearsFlag + receivableFlag == 1) {
									if (refundFlag == 1) {
										dcell.setCellValue(new BigDecimal(vo.getTotalRefund()).doubleValue());
									} else if (arrearsFlag == 1) {
										dcell.setCellValue(new BigDecimal(vo.getTotalArrears()).doubleValue());
									} else {
										dcell.setCellValue(new BigDecimal(vo.getTotalReceivable()).doubleValue());
									}
								} else {
									if (title2.getCell(i + 5).getStringCellValue().equals("已退款金额")) {
										dcell.setCellValue(new BigDecimal(vo.getTotalRefund()).doubleValue());
									} else if (title2.getCell(i + 5).getStringCellValue().equals("未退金额")) {
										dcell.setCellValue(new BigDecimal(vo.getTotalArrears()).doubleValue());
									} else {
										dcell.setCellValue(new BigDecimal(vo.getTotalReceivable()).doubleValue());
									}
								}
							} else {
								dcell.setBlank();
							}
						}

						Double cellValue = dcell.getNumericCellValue();
						if (null == cellValue || cellValue == 0) {
							dcell.setBlank();
						}
					}
				} else {
					Map<String, String> feeMap = vo.getFeeMap();
					int i = 1;
					for (String payItemsName : feeMap.keySet()) {

						if (refundFlag + arrearsFlag + receivableFlag == 1) {
							Cell t1 = title1.createCell(i + 5, CellType.STRING);
							t1.setCellStyle(cellStyle.get("title"));
							t1.setCellValue(payItemsName);
						} else {
							if (null != title2 && null == title2.getCell(i + 5)) {
								Cell tcell = title2.createCell(i + 5, CellType.STRING);
								tcell.setCellStyle(cellStyle.get("title"));
								if (refundFlag + arrearsFlag + receivableFlag == 2) {
									if (i % 2 == 0) {
										tcell.setCellValue(
												refundFlag.equals(1) && arrearsFlag.equals(1) ? "未退金额" : "已收金额");
									} else {
										tcell.setCellValue(
												refundFlag.equals(1) ? "已退款金额" : arrearsFlag.equals(1) ? "未退金额" : "已收金额");
									}
								} else {
									if (i % 3 == 0) {
										tcell.setCellValue("已收金额");
									} else {
										if (null != title2.getCell(i + 4)
												&& title2.getCell(i + 4).getStringCellValue().equals("已退款金额")) {
											tcell.setCellValue("未退金额");
										} else {
											tcell.setCellValue("已退款金额");
										}
									}
								}

								if (refundFlag + arrearsFlag + receivableFlag == 2 && i % 2 == 0) {
									Cell t1 = title1.createCell(i + 4, CellType.STRING);
									t1.setCellStyle(cellStyle.get("title"));
									t1.setCellValue(payItemsName.endsWith("已退款金额")
											? payItemsName.substring(0, payItemsName.length() - 4)
											: payItemsName.endsWith("未退金额")
													? payItemsName.substring(0, payItemsName.length() - 4)
													: payItemsName);

									CellRangeAddress region = new CellRangeAddress(0, 0, i + 4, i + 5);
									sheet.addMergedRegion(region);
									ExcelUtil.addMergeCellBorder(region, sheet);
								} else if (refundFlag + arrearsFlag + receivableFlag == 3 && i % 3 == 0) {
									Cell t1 = title1.createCell(i + 3, CellType.STRING);
									t1.setCellStyle(cellStyle.get("title"));
									t1.setCellValue(payItemsName.endsWith("已退款金额")
											? payItemsName.substring(0, payItemsName.length() - 4)
											: payItemsName.endsWith("未退金额")
													? payItemsName.substring(0, payItemsName.length() - 4)
													: payItemsName);

									CellRangeAddress region = new CellRangeAddress(0, 0, i + 3, i + 5);
									sheet.addMergedRegion(region);
									ExcelUtil.addMergeCellBorder(region, sheet);
								}
							}
						}

						Cell dcell = row.createCell(i + 5, CellType.NUMERIC);
						dcell.setCellStyle(cellStyle.get("cell_right2"));
						if (null != title2) {
							if (title2.getCell(i + 5).getStringCellValue().equals("已收金额")) {
								payItemsName = payItemsName.endsWith("已退款金额") || payItemsName.endsWith("未退金额")
										? payItemsName.substring(0, payItemsName.length() - 4)
										: payItemsName;
							} else {
								payItemsName = (payItemsName.endsWith("退款金额") || payItemsName.endsWith("未退金额")
										? payItemsName.substring(0, payItemsName.length() - 4)
										: payItemsName) + title2.getCell(i + 5).getStringCellValue();
							}
						}
						dcell.setCellValue(new BigDecimal(null!=feeMap.get(payItemsName) ? feeMap.get(payItemsName) :"0").doubleValue());

						Double cellValue = dcell.getNumericCellValue();
						if (null == cellValue || cellValue == 0) {
							dcell.setBlank();
						}
						i++;
					}

					// 合计
					createTotal(sheet, row, cellStyle, title1, title2, i, vo, refundFlag, arrearsFlag, receivableFlag);
				}

				rowNumber++;
			}

			// 合计行
			Row total = sheet.createRow(rowNumber);
			Cell totalStr = total.createCell(0, CellType.STRING);
			totalStr.setCellValue("合计");
			totalStr.setCellStyle(cellStyle.get("total_double"));

			CellRangeAddress region3 = new CellRangeAddress(rowNumber, rowNumber, 0, 5);
			sheet.addMergedRegion(region3);
			ExcelUtil.addMergeCellBorder(region3, sheet);

			int totalRowColumns = null != title2 ? title2.getLastCellNum() : title1.getLastCellNum();
			for (int i = 6; i < totalRowColumns; i++) {
				Cell tt = total.createCell(i, CellType.NUMERIC);
				tt.setCellStyle(cellStyle.get("total_double"));
				tt.setCellFormula("sum(" + sheet.getRow(null != title2 ? 2 : 1).getCell(i).getAddress() + ":"
						+ sheet.getRow(rowNumber - 1).getCell(i).getAddress() + ")");
			}

			sheet.autoSizeColumn(0);
			sheet.autoSizeColumn(1);
			sheet.autoSizeColumn(2);
//			sheet.autoSizeColumn(3);
//			sheet.autoSizeColumn(4);
			sheet.autoSizeColumn(5);

			// 冻结首行
			sheet.createFreezePane(0, null != title2 ? 2 : 1, 0, null != title2 ? 2 : 1);
			// 冻结首列
			sheet.createFreezePane(6, null != title2 ? 2 : 1, 6, null != title2 ? 2 : 1);
			// 增加筛选框
			CellRangeAddress c = CellRangeAddress.valueOf(sheet.getRow(null != title2 ? 1 : 0).getCell(0).getAddress()
					+ ":" + sheet.getRow(null != title2 ? 1 : 0)
							.getCell(sheet.getRow(null != title2 ? 1 : 0).getLastCellNum() - 1).getAddress());
			sheet.setAutoFilter(c);

		}
		String fileName = "退款报表" + DateUtil.formatByStyle(new Date(), "_yyyy_MM_dd_HH_mm_ss") + ".xlsx";

		try {
			ExcelExportUtil.export(response, wb, fileName);
		} catch (IOException e) {
			log.error(e.getMessage());
		}

	}

	private static void createTitle(Sheet sheet, Map<String, XSSFCellStyle> cellStyle, Row title1, Row title2,
			String[] titles, Integer refundFlag, Integer arrearsFlag, Integer receivableFlag) {

		int columnCount = (refundFlag + arrearsFlag + receivableFlag) == 3 ? (titles.length - 6) / 3 + 6
				: (refundFlag + arrearsFlag + receivableFlag) == 2 ? (titles.length - 6) / 2 + 6 : titles.length;

		for (int i = 0; i < columnCount; i++) {
			if (refundFlag + arrearsFlag + receivableFlag == 1) {
				if (sheet.getSheetName().equals("全区")) {
					Cell tt = title1.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
					tt.setCellStyle(cellStyle.get("title"));
				} else {
					if (i <= 5) {
						Cell tt = title1.createCell(i, CellType.STRING);
						tt.setCellValue(titles[i]);
						tt.setCellStyle(cellStyle.get("title"));
					}
				}
			} else {
				if (i <= 5) {
					Cell tt = title1.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
					tt.setCellStyle(cellStyle.get("title"));

					CellRangeAddress region = new CellRangeAddress(0, 1, i, i);
					sheet.addMergedRegion(region);
					ExcelUtil.addMergeCellBorder(region, sheet);
				} else {
					if (sheet.getSheetName().equals("全区")) {
						if (refundFlag + arrearsFlag + receivableFlag == 2) {
							Cell t1 = title1.createCell((2 * (i - 5) - 1) + 5, CellType.STRING);
							String payItemsName = titles[(2 * (i - 5) - 1) + 5];
							t1.setCellValue(
									payItemsName.endsWith("已退款金额") ? payItemsName.substring(0, payItemsName.length() - 4)
											: payItemsName.endsWith("未退金额")
													? payItemsName.substring(0, payItemsName.length() - 4)
													: payItemsName);
							t1.setCellStyle(cellStyle.get("title"));

							CellRangeAddress region = new CellRangeAddress(0, 0, (2 * (i - 5) - 1) + 5,
									2 * (i - 5) + 5);
							sheet.addMergedRegion(region);
							ExcelUtil.addMergeCellBorder(region, sheet);

							Cell t2_1 = title2.createCell((2 * (i - 5) - 1) + 5, CellType.STRING);
							Cell t2_2 = title2.createCell(2 * (i - 5) + 5, CellType.STRING);
							if (refundFlag.equals(1) && arrearsFlag.equals(1)) {
								t2_1.setCellValue("已退款金额");
								t2_2.setCellValue("未退金额");
							} else if (refundFlag.equals(1) && receivableFlag.equals(1)) {
								t2_1.setCellValue("已退款金额");
								t2_2.setCellValue("已收金额");
							} else {
								t2_1.setCellValue("未退金额");
								t2_2.setCellValue("已收金额");
							}
							t2_1.setCellStyle(cellStyle.get("title"));
							t2_2.setCellStyle(cellStyle.get("title"));
						} else {
							Cell t1 = title1.createCell((3 * (i - 5) - 2) + 5, CellType.STRING);
							t1.setCellValue(titles[(3 * (i - 5) - 2) + 5]);
							t1.setCellStyle(cellStyle.get("title"));

							CellRangeAddress region = new CellRangeAddress(0, 0, (3 * (i - 5) - 2) + 5,
									3 * (i - 5) + 5);
							sheet.addMergedRegion(region);
							ExcelUtil.addMergeCellBorder(region, sheet);

							Cell t3_1 = title2.createCell((3 * (i - 5) - 2) + 5, CellType.STRING);
							t3_1.setCellValue("已退款金额");
							t3_1.setCellStyle(cellStyle.get("title"));
							Cell t3_2 = title2.createCell((3 * (i - 5) - 1) + 5, CellType.STRING);
							t3_2.setCellValue("未退金额");
							t3_2.setCellStyle(cellStyle.get("title"));
							Cell t3_3 = title2.createCell(3 * (i - 5) + 5, CellType.STRING);
							t3_3.setCellValue("已收金额");
							t3_3.setCellStyle(cellStyle.get("title"));
						}
					}
				}
			}

		}
	}

	private static void createTotal(Sheet sheet, Row row, Map<String, XSSFCellStyle> cellStyle, Row title1, Row title2,
			int column, RefundVo vo, Integer refundFlag, Integer arrearsFlag, Integer receivableFlag) {
		// 合计
		if (refundFlag + arrearsFlag + receivableFlag == 1) {
			if (null == title1.getCell(column + 5)) {
				Cell t1 = title1.createCell(column + 5, CellType.STRING);
				t1.setCellStyle(cellStyle.get("title"));
				t1.setCellValue("合计");
			}

			Cell t2 = row.createCell(column + 5, CellType.NUMERIC);
			t2.setCellStyle(cellStyle.get("total_double"));
			t2.setCellValue(refundFlag.equals(1) ? new BigDecimal(vo.getTotalRefund()).doubleValue()
					: arrearsFlag.equals(1) ? new BigDecimal(vo.getTotalArrears()).doubleValue()
							: new BigDecimal(vo.getTotalReceivable()).doubleValue());
		} else if (refundFlag + arrearsFlag + receivableFlag == 2) {
			if (null == title1.getCell(column + 5)) {
				Cell t1 = title1.createCell(column + 5, CellType.STRING);
				t1.setCellStyle(cellStyle.get("title"));
				t1.setCellValue("合计");

				CellRangeAddress region = new CellRangeAddress(0, 0, column + 5, column + 6);
				sheet.addMergedRegion(region);
				ExcelUtil.addMergeCellBorder(region, sheet);

				Cell t2 = title2.createCell(column + 5, CellType.STRING);
				t2.setCellStyle(cellStyle.get("title"));
				t2.setCellValue(refundFlag.equals(1) ? "已退款金额" : "未退金额");

				Cell t3 = title2.createCell(column + 6, CellType.STRING);
				t3.setCellStyle(cellStyle.get("title"));
				t3.setCellValue(receivableFlag.equals(1) ? "已收金额" : "未退金额");

			}

			Cell t4 = row.createCell(column + 5, CellType.NUMERIC);
			t4.setCellStyle(cellStyle.get("total_double"));
			t4.setCellValue(refundFlag.equals(1) ? new BigDecimal(vo.getTotalRefund()).doubleValue()
					: new BigDecimal(vo.getTotalArrears()).doubleValue());

			Double cellValue1 = t4.getNumericCellValue();
			if (null == cellValue1 || cellValue1 == 0) {
				t4.setBlank();
			}

			Cell t5 = row.createCell(column + 6, CellType.NUMERIC);
			t5.setCellStyle(cellStyle.get("total_double"));
			t5.setCellValue(receivableFlag.equals(1) ? new BigDecimal(vo.getTotalReceivable()).doubleValue()
					: new BigDecimal(vo.getTotalArrears()).doubleValue());

			Double cellValue2 = t5.getNumericCellValue();
			if (null == cellValue2 || cellValue2 == 0) {
				t5.setBlank();
			}
		} else {
			if (null == title1.getCell(column + 5)) {
				Cell t1 = title1.createCell(column + 5, CellType.STRING);
				t1.setCellStyle(cellStyle.get("title"));
				t1.setCellValue("合计");

				CellRangeAddress region = new CellRangeAddress(0, 0, column + 5, column + 7);
				sheet.addMergedRegion(region);
				ExcelUtil.addMergeCellBorder(region, sheet);

				Cell t2 = title2.createCell(column + 5, CellType.STRING);
				t2.setCellStyle(cellStyle.get("title"));
				t2.setCellValue("已退款金额");

				Cell t3 = title2.createCell(column + 6, CellType.STRING);
				t3.setCellStyle(cellStyle.get("title"));
				t3.setCellValue("未退金额");

				Cell t4 = title2.createCell(column + 7, CellType.STRING);
				t4.setCellStyle(cellStyle.get("title"));
				t4.setCellValue("已收金额");
			}

			Cell t4 = row.createCell(column + 5, CellType.NUMERIC);
			t4.setCellStyle(cellStyle.get("total_double"));
			t4.setCellValue(new BigDecimal(vo.getTotalRefund()).doubleValue());

			Double cellValue1 = t4.getNumericCellValue();
			if (null == cellValue1 || cellValue1 == 0) {
				t4.setBlank();
			}

			Cell t5 = row.createCell(column + 6, CellType.NUMERIC);
			t5.setCellStyle(cellStyle.get("total_double"));
			t5.setCellValue(new BigDecimal(vo.getTotalArrears()).doubleValue());

			Double cellValue2 = t5.getNumericCellValue();
			if (null == cellValue2 || cellValue2 == 0) {
				t5.setBlank();
			}

			Cell t6 = row.createCell(column + 7, CellType.NUMERIC);
			t6.setCellStyle(cellStyle.get("total_double"));
			t6.setCellValue(new BigDecimal(vo.getTotalReceivable()).doubleValue());

			Double cellValue3 = t6.getNumericCellValue();
			if (null == cellValue3 || cellValue3 == 0) {
				t6.setBlank();
			}
		}
	}
}
