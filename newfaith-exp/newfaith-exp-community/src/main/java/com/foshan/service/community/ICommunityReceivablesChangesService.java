package com.foshan.service.community;

import com.foshan.form.community.request.CommunityReceivablesChangesReq;
import com.foshan.form.response.IResponse;

public interface ICommunityReceivablesChangesService {
    public IResponse getCommunityReceivablesChangesList(CommunityReceivablesChangesReq req);
	public IResponse addCommunityReceivablesChanges(CommunityReceivablesChangesReq req);
	public IResponse modifyCommunityReceivablesChanges(CommunityReceivablesChangesReq req);
	public IResponse deleteCommunityReceivablesChanges(CommunityReceivablesChangesReq req);
	public IResponse getCommunityReceivablesChangesInfo(CommunityReceivablesChangesReq req);
	public IResponse cancelDisposableRefund(CommunityReceivablesChangesReq req);
}

