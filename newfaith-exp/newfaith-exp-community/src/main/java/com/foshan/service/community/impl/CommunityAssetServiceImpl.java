package com.foshan.service.community.impl;


import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.foshan.dao.generic.Page;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.ColumnEntity;
import com.foshan.entity.UpshelfColumnEntity;
import com.foshan.entity.community.CommunityAssetEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityUserEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityAssetCommentForm;
import com.foshan.form.community.CommunityAssetForm;
import com.foshan.form.community.request.communityAssetReq.AddCommunityAssetCommentReq;
import com.foshan.form.community.request.communityAssetReq.AddCommunityAssetReq;
import com.foshan.form.community.request.communityAssetReq.AuditCommunityAssetReq;
import com.foshan.form.community.request.communityAssetReq.CommunityAssetReq;
import com.foshan.form.community.request.communityAssetReq.GetCommunityAssetCommentListReq;
import com.foshan.form.community.request.communityAssetReq.GetCommunityAssetListReq;
import com.foshan.form.community.response.communityAsset.CommunityAssetRes;
import com.foshan.form.community.response.communityAsset.GetCommunityAssetCommentListRes;
import com.foshan.form.community.response.communityAsset.GetCommunityAssetListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityAssetService;

@Transactional
@Service("communityAssetService")
public class CommunityAssetServiceImpl extends GenericCommunityService implements ICommunityAssetService {
    @Audit(operate = "新增邻里内容")
    @Override
    public IResponse addCommunityAsset(AddCommunityAssetReq req) {
        GenericResponse res = new GenericResponse();

        if (StringUtils.isNotEmpty(req.getColumnId()) && StringUtils.isNoneEmpty(req.getAssetName())) {
            CommunityAssetEntity assetEntity = new CommunityAssetEntity();

            if (null != req.getMemberId()) {
                CommunityMemberEntity member = communityMemberDao.get(req.getMemberId());
                assetEntity.setCommunityMember(member);
            }
            if (null != req.getUserId()) {
                CommunityUserEntity user = communityUserDao.get(req.getUserId());
                assetEntity.setUser(user);
            }

            assetEntity.setContentType(req.getContentType());
            assetEntity.setAssetCode(StringUtils.isNoneEmpty(req.getAssetCode()) ? req.getAssetCode() : String.valueOf(System.currentTimeMillis()));
            assetEntity.setAssetName(StringUtils.isNoneEmpty(req.getAssetName()) ? req.getAssetName() : "");
            assetEntity.setServiceCode(StringUtils.isNoneEmpty(req.getServiceCode()) ? req.getServiceCode() : "");
            assetEntity.setImageFile(StringUtils.isNoneEmpty(req.getImageFile()) ? req.getImageFile() : "");
            assetEntity.setSmallImageFile(StringUtils.isNoneEmpty(req.getSmallImageFile()) ? req.getSmallImageFile() : "");
            assetEntity.setMiddleImageFile(StringUtils.isNoneEmpty(req.getMiddleImageFile()) ? req.getMiddleImageFile() : "");
            assetEntity.setPublishedTime(StringUtils.isNotBlank(req.getPublishedTime()) ? Timestamp.valueOf(req.getPublishedTime()) : null);
            assetEntity.setValidTime(StringUtils.isNotBlank(req.getValidTime()) ? Timestamp.valueOf(req.getValidTime()) : null);
            assetEntity.setTimeLength(req.getTimeLength());
            assetEntity.setAssetType(req.getAssetType());
            assetEntity.setAssetState(req.getAssetState());
            assetEntity.setAssetOrders(req.getAssetOrders());
            assetEntity.setPackageFlag(req.getPackageFlag());
            assetEntity.setPackageCount(req.getPackageCount());
            assetEntity.setPackageOrders(req.getPackageOrders());
            assetEntity.setIsCover(req.getIsCover());
            assetEntity.setSummaryShort(req.getSummaryShort());
            assetEntity.setParameterInfo(req.getParameterInfo());
            assetEntity.setSubTitle(req.getSubTitle());
            if (StringUtils.isNotEmpty(req.getImageIdList())) {
                String[] assetIds = req.getImageIdList().split(",");
                int i = 0;
                for (String assetId : assetIds) {
                    AssetEntity image = assetDao.get(Integer.parseInt(assetId));
                    image.setAssetOrders(i);
                    assetEntity.getSubAssetSet().add(image);
                    i++;
                }
            }
//        assetEntity.setParentAsset();
//        assetEntity.setOutsideColumnEntityList();
            communityAssetDao.save(assetEntity);

            ColumnEntity column = communityColumnDao.get(Integer.parseInt(req.getColumnId()));


            if (null != column) {
                UpshelfColumnEntity upshelfColumnEntity = new UpshelfColumnEntity();
                upshelfColumnEntity.setColumn(column);
                upshelfColumnEntity.setResourceId(assetEntity.getId());
                upshelfColumnEntity.setType(12);
                upshelfColumnEntity.setOrderNumber(req.getOrderNumber());
                upshelfColumnEntity.setIsTop(req.getIsTop());
                upshelfColumnEntity.setAsset(assetEntity);
                upshelfColumnEntity.setTerminalType(req.getTerminalType());
                upshelfColumnDao.save(upshelfColumnEntity);
        		if (null == req.getOrderNumber()) {//如果排序不传默认值，默认此栏目下最大排序+5；
//        			try {
//        				upshelfColumnDao.executeUpdate(
//        					"UPDATE t_upshelf_column SET orderNumber=(SELECT IFNULL(MAX(a.orderNumber),0) "
//        							+ "AS orderNumber FROM t_upshelf_column a WHERE a.columnId="+column.getId()+" AND "
//        							+ "a.id!="+upshelfColumnEntity.getId()+
//        							" ORDER BY a.orderNumber DESC, a.upshelfTime DESC )+5 WHERE id =" + 
//        							upshelfColumnEntity.getId());
//        			} catch (Exception e) {
//        				e.printStackTrace();
//        				upshelfColumnEntity.setOrderNumber(1);
//        			} 
        			upshelfColumnEntity.setOrderNumber(1);
        		}
            } else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "无此栏目");
                return res;
            }

            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "栏目Id,内容不能为空");
        }

        return res;
    }

    @Audit(operate = "删除邻里内容")
    @Override
    public IResponse deleteCommunityAsset(CommunityAssetReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getAssetId()) {
            CommunityAssetEntity assetEntity = communityAssetDao.get(req.getAssetId());
            if (null != assetEntity) {
                String hql = "select distinct a from UpshelfColumnEntity a where a.type = 12 and a.asset.id =" + req.getAssetId();
                UpshelfColumnEntity upshelfColumnEntity = upshelfColumnDao.findUnique(hql);
                if (null != upshelfColumnEntity) {
                    upshelfColumnDao.delete(upshelfColumnEntity);
                }

                communityAssetDao.delete(assetEntity);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO + "无此媒资！");
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }

        return res;
    }

    @Override
    public IResponse getCommunityAsset(CommunityAssetReq req) {
        CommunityAssetRes res = new CommunityAssetRes();

        if (null != req.getAssetId()) {
            CommunityAssetEntity assetEntity = communityAssetDao.get(req.getAssetId());
            if (null != assetEntity) {
                CommunityAssetForm assetForm;
                assetForm = getCommunityAssetForm(assetEntity);
                if(StringUtils.isNotEmpty(req.getColumnId())) {
                    String hql = "select distinct a from UpshelfColumnEntity a where a.type = 12 and a.asset.id =" + req.getAssetId()
                    	+" and a.column.id="+req.getColumnId();
                    UpshelfColumnEntity upshelfColumn = upshelfColumnDao.findUnique(hql);
                    if (null != upshelfColumn) {
                    	assetForm.setOrderNumber(upshelfColumn.getOrderNumber());                   
                    }
                }

                res.setCommunityAssetForm(assetForm);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Audit(operate = "修改邻里内容")
    @Override
    public IResponse modifyCommunityAsset(CommunityAssetReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getAssetId()) {
            CommunityAssetEntity asset = communityAssetDao.get(req.getAssetId());
            if (null != asset) {
                asset.setContentType(null != req.getContentType() ? req.getContentType() : asset.getContentType());

                if (null != req.getMemberId()) {
                    CommunityMemberEntity member = communityMemberDao.get(req.getMemberId());
                    asset.setCommunityMember(null != member ? member : asset.getCommunityMember());
                }
                if (null != req.getUserId()) {
                    CommunityUserEntity user = communityUserDao.get(req.getUserId());
                    asset.setUser(null != user ? user : asset.getUser());
                }
                asset.setAuditState(EntityContext.ASSET_STATE_PENDING);
                asset.setAssetCode(StringUtils.isNotEmpty(req.getAssetCode()) ? req.getAssetCode() : asset.getAssetCode());
                asset.setAssetName(StringUtils.isNotEmpty(req.getAssetName()) ? req.getAssetName() : asset.getAssetName());
                asset.setServiceCode(StringUtils.isNotEmpty(req.getServiceCode()) ? req.getServiceCode() : asset.getAssetCode());
                asset.setImageFile(StringUtils.isNotEmpty(req.getImageFile()) ? req.getImageFile() : asset.getImageFile());
                asset.setSmallImageFile(StringUtils.isNotEmpty(req.getSmallImageFile()) ? req.getSmallImageFile() : asset.getSmallImageFile());
                asset.setMiddleImageFile(StringUtils.isNotEmpty(req.getMiddleImageFile()) ? req.getMiddleImageFile() : asset.getMiddleImageFile());
                asset.setPublishedTime(StringUtils.isNotBlank(req.getPublishedTime()) ?
                        Timestamp.valueOf(req.getPublishedTime()) : asset.getPublishedTime());
                asset.setValidTime(StringUtils.isNotBlank(req.getValidTime()) ?
                        Timestamp.valueOf(req.getValidTime()) : asset.getValidTime());
                asset.setTimeLength(null != req.getTimeLength() ?
                        req.getTimeLength() : asset.getTimeLength());
                asset.setAssetType(null != req.getAssetType() ? req.getAssetType() : asset.getAssetType());
                asset.setPackageFlag(null != req.getPackageFlag() ? req.getPackageFlag() : asset.getPackageFlag());
                asset.setPackageCount(null != req.getPackageCount() ? req.getPackageCount() : asset.getPackageCount());
                asset.setPackageOrders(null != req.getPackageOrders() ? req.getPackageOrders() : asset.getAssetOrders());
                asset.setIsCover(null != req.getIsCover() ? req.getIsCover() : asset.getIsCover());
                asset.setSummaryShort(StringUtils.isNotEmpty(req.getSummaryShort()) ? req.getSummaryShort() : "");
                asset.setParameterInfo(StringUtils.isNotEmpty(req.getParameterInfo()) ? req.getParameterInfo() : asset.getParameterInfo());
                asset.setSubTitle(StringUtils.isNotEmpty(req.getSubTitle()) ? req.getSubTitle() : asset.getSubTitle());
                asset.setRecommendCount(null != req.getRecommendCount() ? req.getRecommendCount() : asset.getRecommendCount());

                asset.setServiceId(StringUtils.isNotEmpty(req.getServiceId()) ? req.getServiceId() : asset.getServiceId());
//                asset.setFolderAssetId(StringUtils.isNotEmpty(req.getFolderAssetId()) ? req.getFolderAssetId() : asset.getFolderAssetId());
                asset.setBroadcastCount(null != req.getBroadcastCount() ? req.getBroadcastCount() : asset.getBroadcastCount());
                asset.setActorsDisplay(null != req.getActorsDisplay() ? req.getActorsDisplay() : asset.getActorsDisplay());
                asset.setDirector(StringUtils.isNotEmpty(req.getDirector()) ? req.getDirector() : asset.getDirector());
                asset.setPreviewAssetId(StringUtils.isNotEmpty(req.getPreviewAssetId()) ? req.getPreviewAssetId() : asset.getPreviewAssetId());
                asset.setPreviewProviderId(StringUtils.isNotEmpty(req.getPreviewProviderId()) ? req.getPreviewProviderId() : asset.getPreviewProviderId());
                if (StringUtils.isNotEmpty(req.getImageIdList())) {
                    String[] assetIds = req.getImageIdList().split(",");
                    asset.getSubAssetSet().clear();
                    int i = 0;
                    for (String assetId : assetIds) {
                        AssetEntity image = assetDao.get(Integer.parseInt(assetId));
                        image.setAssetOrders(i);
                        asset.getSubAssetSet().add(image);
                        i++;
                    }
                }else {
                	asset.getSubAssetSet().clear();
                }
                String hql = "select distinct a from UpshelfColumnEntity a where a.type = 12 and a.asset.id =" + req.getAssetId();
                UpshelfColumnEntity upshelfColumnEntity = upshelfColumnDao.findUnique(hql);
                upshelfColumnEntity.setIsTop(null != req.getIsTop() ? req.getIsTop() : upshelfColumnEntity.getIsTop());
                upshelfColumnEntity.setOrderNumber(null != req.getAssetOrders() ? req.getAssetOrders() : upshelfColumnEntity.getOrderNumber());


                communityAssetDao.saveOrUpdate(asset);
                upshelfColumnDao.saveOrUpdate(upshelfColumnEntity);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
                return res;
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    protected CommunityAssetForm getCommunityAssetForm(CommunityAssetEntity asset) {
        CommunityAssetForm assetForm = new CommunityAssetForm();
        if (null != asset) {
            if (null != asset.getContentType() && 0 == asset.getContentType()) {
                assetForm.setAssetName(StringUtils.isNoneEmpty(asset.getAssetName()) ? asset.getAssetName() : "");
                assetForm.setServiceCode(StringUtils.isNoneEmpty(asset.getServiceCode()) ? asset.getServiceCode() : "");
                assetForm.setImageFile(StringUtils.isNoneEmpty(asset.getImageFile()) ? asset.getImageFile() : "");
                assetForm.setSmallImageFile(StringUtils.isNoneEmpty(asset.getSmallImageFile()) ? asset.getSmallImageFile() : "");
                assetForm.setMiddleImageFile(StringUtils.isNoneEmpty(asset.getMiddleImageFile()) ? asset.getMiddleImageFile() : "");
                assetForm.setContentType(asset.getContentType());
                assetForm.setMemberId(null != asset.getCommunityMember() ? asset.getCommunityMember().getId() : null);
                assetForm.setUserId(null != asset.getUser() ? asset.getUser().getId() : null);
                assetForm.setAssetId(asset.getId());
                assetForm.setUserName(StringUtils.isNoneEmpty(asset.getUserName()) ? asset.getUserName() : "");
                if (null != asset.getSubAssetSet()) {
                    for (AssetEntity subAsset : asset.getSubAssetSet()) {
                        assetForm.getSubAssetList().add(getAsset(subAsset));
                    }
                }
                assetForm.setAssetType(null != asset.getAssetType() ? asset.getAssetType() : null);
                assetForm.setIsCover(null != asset.getIsCover() ? asset.getIsCover() : null);
                assetForm.setPackageFlag(null != asset.getPackageFlag() ? asset.getPackageFlag() : null);
//            assetForm.setTimeLength(asset.getTimeLength());
                assetForm.setParameterInfo(StringUtils.isNotEmpty(asset.getParameterInfo()) ? asset.getParameterInfo() : null);
                assetForm.setAssetCode(StringUtils.isNotEmpty(asset.getAssetCode()) ? asset.getAssetCode() : null);
                assetForm.setAssetOrders(asset.getAssetOrders());
                assetForm.setPublishedTime(
                        asset.getPublishedTime() != null ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(asset.getPublishedTime())
                                : "");
                assetForm.setPackageOrders(null != asset.getPackageOrders() ? asset.getPackageOrders() : null);
                assetForm.setValidTime(asset.getValidTime() != null
                        ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(asset.getValidTime())
                        : "");
                assetForm.setSummaryShort(StringUtils.isNotEmpty(asset.getSummaryShort()) ? asset.getSummaryShort() : "");
                assetForm.setAuditState(null != asset.getAuditState() ? asset.getAuditState() : 0);

            } else {
                assetForm.setAssetType(null != asset.getAssetType() ? asset.getAssetType() : null);
                assetForm.setUserName(StringUtils.isNoneEmpty(asset.getUserName()) ? asset.getUserName() : "");
                assetForm.setAssetName(StringUtils.isNoneEmpty(asset.getAssetName()) ? asset.getAssetName() : "");
                assetForm.setServiceCode(StringUtils.isNoneEmpty(asset.getServiceCode()) ? asset.getServiceCode() : "");
                assetForm.setImageFile(StringUtils.isNoneEmpty(asset.getImageFile()) ? asset.getImageFile() : "");
                assetForm.setPublishedTime(
                        asset.getPublishedTime() != null ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(asset.getPublishedTime())
                                : "");
                assetForm.setSmallImageFile(StringUtils.isNoneEmpty(asset.getSmallImageFile()) ? asset.getSmallImageFile() : "");
                assetForm.setMiddleImageFile(StringUtils.isNoneEmpty(asset.getMiddleImageFile()) ? asset.getMiddleImageFile() : "");
                assetForm.setContentType(asset.getContentType());
                assetForm.setMemberId(null != asset.getCommunityMember() ? asset.getCommunityMember().getId() : null);
                assetForm.setUserId(null != asset.getUser() ? asset.getUser().getId() : null);
                assetForm.setAssetId(asset.getId());
                assetForm.setAssetOrders(asset.getAssetOrders());
                assetForm.setSummaryShort(StringUtils.isNotEmpty(asset.getSummaryShort()) ? asset.getSummaryShort() : "");
                assetForm.setAuditState(null != asset.getAuditState() ? asset.getAuditState() : 0);
                if (null != asset.getSubAssetSet()) {
                    for (AssetEntity subAsset : asset.getSubAssetSet()) {
                        assetForm.getSubAssetList().add(getAsset(subAsset));
                    }
                }
            }

        }
        return assetForm;
    }

    protected List<CommunityAssetForm> getCommunityAssetFormList(List<CommunityAssetEntity> communityAssetList) {
        List<CommunityAssetForm> list = new ArrayList<>();
        communityAssetList.forEach(o -> {
            CommunityAssetForm communityAssetForm = getCommunityAssetForm(o);
            list.add(communityAssetForm);
        });
        return list;
    }

    @Audit(operate = "审核邻里内容")
    @Override
    public GenericResponse auditCommunityAsset(AuditCommunityAssetReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getAssetId() && null != req.getAuditState()) {
            CommunityAssetEntity assetEntity = communityAssetDao.get(req.getAssetId());
            if (null != assetEntity) {
                assetEntity.setAuditState(req.getAuditState());
                assetEntity.setIdea(null != req.getIdea()?req.getIdea():"");
                communityAssetDao.save(assetEntity);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_DATA_NULL_CODE);
                res.setRetInfo(ResponseContext.RES_DATA_NULL_INFO);
            }
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Override
    public GetCommunityAssetListRes getCommunityAssetList(GetCommunityAssetListReq req) {
        GetCommunityAssetListRes res = new GetCommunityAssetListRes();
        if (null != req.getColumnId()) {
            Page<UpshelfColumnEntity> page = new Page<>();
            page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
            page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
            page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
            
    		Object userObj = getPrincipal(true);
    		StringBuilder hql = new StringBuilder("select distinct a from UpshelfColumnEntity a inner join a.column c  where a.type = 12 and a.column.id =" + req.getColumnId() + 
            		(null!=req.getAuditState() ? " and a.resourceId in(select b.id from CommunityAssetEntity b where b.auditState="+req.getAuditState()+" )":""));
    		if (null == userObj ) {
    			hql.append(" and c.isGlobal=1 ");
    		}else if(null != userObj && userObj instanceof CommunityMemberEntity) {
    			hql.append(" and c.isGlobal in(0,1) ");
    		}
    		hql.append(StringUtils.isNoneEmpty(req.getTerminalTypeList()) ? " and a.terminalType in("+req.getTerminalTypeList()+")":"");

            hql.append(" ORDER BY a.orderNumber DESC, a.upshelfTime DESC ");

            page = upshelfColumnDao.queryPage(page, hql.toString());
            res.setTotalResult(page.getTotalCount());
            res.setPageSize(page.getPageSize());
            res.setCurrentPage(page.getCurrentPage());
            res.setTotal(page.getTotalPage());

            page.getResultList().forEach(o -> {
                        CommunityAssetEntity asset = communityAssetDao.get(o.getAsset().getId());
                        if (null != req.getContentType() && asset.getContentType() != req.getContentType()) {
                            return;
                        }
                        CommunityAssetForm communityAssetForm = getCommunityAssetForm(communityAssetDao.get(o.getAsset().getId()));
                        communityAssetForm.setUpshelfColumnId(o.getId());
                        if (null != req.getAssetName()){
	                        if (communityAssetForm.getAssetName().contains(req.getAssetName())){
		                        communityAssetForm.setIsTop(o.getIsTop());
		                        communityAssetForm.setOrderNumber(o.getOrderNumber());
		                        communityAssetForm.setUpshelfTime(null != o.getUpshelfTime() ? 
		                        		o.getUpshelfTime().toString().substring(0, o.getUpshelfTime().toString().indexOf(".")) : "");
		                        res.getCommunityAssetFormList().add(communityAssetForm);
	                        }
                        }else {
                            communityAssetForm.setIsTop(o.getIsTop());
                            communityAssetForm.setOrderNumber(o.getOrderNumber());
                            communityAssetForm.setUpshelfTime(null != o.getUpshelfTime() ? 
                            		o.getUpshelfTime().toString().substring(0, o.getUpshelfTime().toString().indexOf(".")) : "");
                            res.getCommunityAssetFormList().add(communityAssetForm);
                        }
                    }
            );
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            return res;
        }

        if (null != req.getMemberId()) {
            Page<CommunityAssetEntity> page = new Page<>();
            page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
            page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
            page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
            StringBuffer hql = new StringBuffer("select distinct a from CommunityAssetEntity a where a.communityMember.id =" + req.getMemberId());
            hql.append(null != req.getContentType() ? " and a.contentType =" + req.getContentType() : "");
            hql.append(" ORDER BY a.id DESC");

            page = communityAssetDao.queryPage(page, hql.toString());
            res.setTotalResult(page.getTotalCount());
            res.setPageSize(page.getPageSize());
            res.setCurrentPage(page.getCurrentPage());
            res.setTotal(page.getTotalPage());

            page.getResultList().forEach(o -> {
                        CommunityAssetForm communityAssetForm = getCommunityAssetForm(o);
                        res.getCommunityAssetFormList().add(communityAssetForm);
                    }
            );
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            return res;
        }
        if (null != req.getUserId()) {
            Page<CommunityAssetEntity> page = new Page<>();
            page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
            page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
            page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
            StringBuffer hql = new StringBuffer("select distinct a from CommunityAssetEntity a where a.user.id =" + req.getUserId());
            hql.append(null != req.getContentType() ? " and a.contentType =" + req.getContentType() : "");
            hql.append(" ORDER BY a.id DESC");

            page = communityAssetDao.queryPage(page, hql.toString());
            res.setTotalResult(page.getTotalCount());
            res.setPageSize(page.getPageSize());
            res.setCurrentPage(page.getCurrentPage());
            res.setTotal(page.getTotalPage());

            page.getResultList().forEach(o -> {
                        CommunityAssetForm communityAssetForm = getCommunityAssetForm(o);
                        res.getCommunityAssetFormList().add(communityAssetForm);
                    }
            );
            res.setRet(ResponseContext.RES_SUCCESS_CODE);
            res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            return res;
        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;

    }

    @Audit(operate = "新增邻里内容评论")
    @Override
    public IResponse addCommunityAssetComment(AddCommunityAssetCommentReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getAssetId()) {
            CommunityAssetEntity parentAsset = communityAssetDao.get(req.getAssetId());
            if (null != parentAsset) {
                CommunityAssetEntity asset = new CommunityAssetEntity();
                asset.setParentAsset(parentAsset);
                asset.setSummaryShort(req.getSummaryShort());
                asset.setAssetCode(String.valueOf(System.currentTimeMillis()));
                asset.setAuditState(0);
                asset.setContentType(5);
                if (null != req.getMemberId()) {
                    CommunityMemberEntity member = communityMemberDao.get(req.getMemberId());
                    asset.setCommunityMember(member);
                }
                if (null != req.getUserId()) {
                    CommunityUserEntity user = communityUserDao.get(req.getUserId());
                    asset.setUser(user);
                }
                communityAssetDao.save(asset);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            } else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "主贴不存在");
            }

        } else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Audit(operate = "修改邻里内容评论")
    @Override
    public IResponse modifyCommunityAssetComment(AddCommunityAssetCommentReq req) {
        GenericResponse res = new GenericResponse();
        if (null != req.getAssetId()){
            CommunityAssetEntity asset = communityAssetDao.get(req.getAssetId());
            if (null != asset){
                asset.setAuditState(EntityContext.ASSET_STATE_PENDING);
                asset.setSummaryShort(StringUtils.isNotEmpty(req.getSummaryShort())?req.getSummaryShort():asset.getSummaryShort());
                if (null != req.getMemberId()) {
                    CommunityMemberEntity member = communityMemberDao.get(req.getMemberId());
                    asset.setCommunityMember(member);
                }
                if (null != req.getUserId()) {
                    CommunityUserEntity user = communityUserDao.get(req.getUserId());
                    asset.setUser(user);
                }
                communityAssetDao.saveOrUpdate(asset);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "回帖不存在");
            }
        }else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Audit(operate = "审核邻里内容评论")
    @Override
    public IResponse auditCommunityAssetComment(AuditCommunityAssetReq req) {
        GetCommunityAssetListRes res = new GetCommunityAssetListRes();
        if (null != req.getAssetId() && null != req.getAuditState()){
            CommunityAssetEntity asset = communityAssetDao.get(req.getAssetId());
            if (null != asset){
                asset.setAuditState(req.getAuditState());
                asset.setIdea(null != req.getIdea()?req.getIdea():"");
                communityAssetDao.saveOrUpdate(asset);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "回帖不存在");
            }
        }else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;
    }

    @Audit(operate = "删除邻里内容评论")
    @Override
    public IResponse deleteCommunityAssetComment(AddCommunityAssetReq req) {
        GetCommunityAssetListRes res = new GetCommunityAssetListRes();
        if (null != req.getAssetId()){
            CommunityAssetEntity asset = communityAssetDao.get(req.getAssetId());
            if (null != asset){
                communityAssetDao.delete(asset);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
            }else {
                res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
                res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "回帖不存在");
            }
        }else {
            res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
        }
        return res;    }

    @Override
    public IResponse getCommunityAssetCommentList(GetCommunityAssetCommentListReq req) {
        GetCommunityAssetCommentListRes res = new GetCommunityAssetCommentListRes();
    if (null != req.getAssetId()){
        Page<CommunityAssetEntity> page = new Page<>();
        page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
        page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 0) * page.getPageSize());
        page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
        String hql = "select distinct a from CommunityAssetEntity a where a.contentType = 5 and a.parentAsset.id =" + req.getAssetId() + " ORDER BY a.id ASC";

        page = communityAssetDao.queryPage(page,hql);
        res.setTotalResult(page.getTotalCount());
        res.setPageSize(page.getPageSize());
        res.setCurrentPage(page.getCurrentPage());
        res.setTotal(page.getTotalPage());

        page.getResultList().forEach(o -> {

            CommunityAssetCommentForm commentForm = new CommunityAssetCommentForm();
            commentForm.setAssetId(o.getId());
            commentForm.setAuditState(o.getAuditState());
            commentForm.setSummaryShort(o.getSummaryShort());
            commentForm.setMemberId(null != o.getCommunityMember()?o.getCommunityMember().getId():null);
            commentForm.setUserId(null != o.getUser()?o.getUser().getId():null);
            commentForm.setCreateTime(null != o.getPublishedTime() ? o.getPublishedTime().toString().substring(0, o.getPublishedTime().toString().indexOf(".")) : "");
            res.getCommunityAssetFormList().add(commentForm);
        });
        res.setRet(ResponseContext.RES_SUCCESS_CODE);
        res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

    }else {
        res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
        res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
    }
        return res;
    }
}
