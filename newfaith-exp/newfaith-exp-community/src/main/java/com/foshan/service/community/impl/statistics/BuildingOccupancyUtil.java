package com.foshan.service.community.impl.statistics;

import static java.util.stream.Collectors.groupingBy;

import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jdbc.Work;

import com.foshan.form.community.statistics.BuildingOccupancyVo;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityCache;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BuildingOccupancyUtil {

	public static String getBuildingOccupancySql(Integer[] districtIds, String[] chargeCategorys) {
		StringBuilder sql = new StringBuilder("SELECT bb.districtcode,bb.districtname,bb.buildingname,")
				//.append("MAX(CASE estatestate WHEN 6 THEN buildingarea ELSE 0 END)+MAX(CASE estatestate WHEN 1 THEN buildingarea ELSE 0 END) AS 总面积,")
				.append("SUM(bb.buildingarea) AS 总面积,")
				.append("MAX(CASE estatestate WHEN 1 THEN buildingarea ELSE 0 END)  AS 入住面积,")
				.append("MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 2 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 0 THEN estatecount ELSE 0 END)+"
						+ "MAX(CASE estatestate WHEN 5 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END)+"
						+ "MAX(CASE estatestate WHEN 7 THEN estatecount ELSE 0 END) AS 总单元数,")
				.append("MAX(CASE estatestate WHEN 7 THEN estatecount ELSE 0 END)  AS 未售单元数,")
				.append("MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)  AS 入住单元数,")
				.append("MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END)  AS 空置单元数,")
				.append("ROUND(MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)/")
				.append("(MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 2 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 0 THEN estatecount ELSE 0 END)+"
						+ "MAX(CASE estatestate WHEN 5 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END)"
						+ "+MAX(CASE estatestate WHEN 7 THEN estatecount ELSE 0 END) )*100,2) AS 入住率,")
				.append("ROUND(MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END)/")
				.append("(MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 2 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 0 THEN estatecount ELSE 0 END)+"
						+ "MAX(CASE estatestate WHEN 5 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END)"
						+ "+MAX(CASE estatestate WHEN 7 THEN estatecount ELSE 0 END) )*100,2) AS 空置率, ")
				
				.append("MAX(CASE estatestate WHEN 2 THEN estatecount ELSE 0 END)  AS 装修单元数,"
						+ "ROUND(MAX(CASE estatestate WHEN 2 THEN estatecount ELSE 0 END)/(MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 2 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 0 THEN estatecount ELSE 0 END)"
						+ "+MAX(CASE estatestate WHEN 5 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END)"
						+ "+MAX(CASE estatestate WHEN 7 THEN estatecount ELSE 0 END))*100,2) AS 装修率,")
				
				.append("MAX(CASE estatestate WHEN 5 THEN estatecount ELSE 0 END)  AS 自住单元数,"
						+ "ROUND(MAX(CASE estatestate WHEN 5 THEN estatecount ELSE 0 END)/(MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 2 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 0 THEN estatecount ELSE 0 END)"
						+ "+MAX(CASE estatestate WHEN 5 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END)"
						+ "+MAX(CASE estatestate WHEN 7 THEN estatecount ELSE 0 END))*100,2) AS 自住率,")
				
				.append("MAX(CASE estatestate WHEN 0 THEN estatecount ELSE 0 END)  AS 未收楼单元数,"
						+ "ROUND(MAX(CASE estatestate WHEN 0 THEN estatecount ELSE 0 END)/(MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 2 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 0 THEN estatecount ELSE 0 END)"
						+ "+MAX(CASE estatestate WHEN 5 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END)"
						+ "+MAX(CASE estatestate WHEN 7 THEN estatecount ELSE 0 END))*100,2) AS 未收楼率 ")
				
				.append("FROM(SELECT aa.districtcode,aa.districtname,aa.districtorder,aa.buildingname,aa.buildingorder,")
				.append("COUNT(aa.unitcode) AS estatecount,SUM(aa.buildingarea) AS buildingarea,aa.estatestate ")
				.append("FROM(SELECT a.districtcode,a.districtname,a.districtorder,b.id,b.buildingname,b.buildingorder,c.unitcode,")
				//.append("IF(c.estatestate<6,1,IF(iscurrentmember IS NOT NULL,1,6)) AS estatestate,c.estatetype,c.buildingarea,")
				.append("IF(iscurrentmember IS NOT NULL,c.estatestate,7) AS estatestate,c.estatetype,c.buildingarea,")
				.append("d.billingdate,d.membertype,d.iscurrentmember,d.memberid FROM ")
				.append("t_community_district a ")
				.append("INNER JOIN t_community_building b ON a.id=b.districtId AND a.id<>11 ")
				.append((null != districtIds && districtIds.length > 0
				? " and a.id in(" + StringUtils.join(districtIds, ",") + ") "
				: " "))
				.append("INNER JOIN t_community_property c ON b.id=c.buildingid ")
				.append("LEFT JOIN t_community_member_property d ON c.id=d.propertyid AND (d.terminationDate IS NULL OR d.terminationDate>NOW()) ")
				.append("GROUP BY c.unitcode) aa GROUP BY aa.districtcode,aa.buildingname,aa.estatestate ")
				.append(") bb GROUP BY bb.districtcode,bb.buildingname order by bb.districtorder,bb.buildingorder");

		return sql.toString();
	}

	public static Map<String, Object> getBuildingOccupancyList(Integer[] districtIds, String[] chargeCategorys) {
		Map<String, Object> res = new HashMap<>();

		String[] title = { "楼盘名称", "楼阁", "总面积", "总入住面积", "总单元数","未售单元数", "入住单元数", "入住率","装修单元数",
				"装修率","自住单元数","自住率","未收楼单元数","未收楼率", "空置单元数",  "空置率" };
		List<BuildingOccupancyVo> dataList = new LinkedList<>();


		// 获取动态sql
		String sql = getBuildingOccupancySql(districtIds, chargeCategorys);

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();

		session.doWork(new Work() {
			@Override
			public void execute(Connection conn) throws SQLException {
				Statement st = conn.createStatement();
				ResultSet rs = st.executeQuery(sql);

				while (rs.next()) {
					BuildingOccupancyVo vo = new BuildingOccupancyVo();
					String districtCode= rs.getString("districtcode");
					vo.setDistrictCode(districtCode.length() == 1 ? "0" + districtCode : districtCode);
					vo.setDistrictName(rs.getString("districtname"));
					vo.setBuildingName(rs.getString("buildingname"));
					vo.setTotalBuildingArea(rs.getString("总面积"));
					vo.setTotalCheckInArea(rs.getString("入住面积"));
					vo.setTotalEstateCount(rs.getString("总单元数"));
					vo.setUnsoldEstateCount(rs.getString("未售单元数"));
					vo.setCheckInEstateCount(rs.getString("入住单元数"));
					vo.setOcccupancyRate(rs.getString("入住率"));
					vo.setFitmentEstateCount(rs.getString("装修单元数"));
					vo.setFitmentRate(rs.getString("装修率"));
					vo.setSelfOccupationEstateCount(rs.getString("自住单元数"));
					vo.setSelfOccupationRate(rs.getString("自住率"));
					vo.setUncollectedEstateCount(rs.getString("未收楼单元数"));
					vo.setUncollectedRate(rs.getString("未收楼率"));
					vo.setVacancyEstateCount(rs.getString("空置单元数"));
					vo.setVacancyRate(rs.getString("空置率"));
					dataList.add(vo);
				}

			}
		});
		session.close();

		res.put("title", title);
		res.put("data", dataList);

		return res;
	}


	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static void transBuildingOccupancyExcel(HttpServletResponse response) {

		// 获取缓存数据并进行处理
		Map<String, Map<String, Object>> temp1 = CommunityCache.statisticsCache.get("入住率报表");
		String key = "";
		for (String o : temp1.keySet()) {
			key = o;
		}

		Map<String, Object> temp2 = temp1.get(key);
		String[] titles = (String[]) temp2.get("title");

		List<BuildingOccupancyVo> dataList = (List<BuildingOccupancyVo>) temp2.get("data");

		// 将缓存的队列数据按楼盘编码进行排序
		Map<String, List<BuildingOccupancyVo>> districtReceivableMap = (HashMap<String, List<BuildingOccupancyVo>>) dataList
				.stream().collect(groupingBy(BuildingOccupancyVo::getNewDistrictCode));

		if (districtReceivableMap.size() > 1) {
			districtReceivableMap.put("000全区", dataList);
		}

		TreeSet<String> keys = new TreeSet<>(Comparator.naturalOrder());
		keys.addAll(districtReceivableMap.keySet());

		XSSFWorkbook wb = new XSSFWorkbook();
		Map<String, XSSFCellStyle> cellStyle = ExcelUtil.getExcelStyle(wb);

		for (String o : keys) {

			Sheet sheet = wb.createSheet(o.substring(3));

			LinkedList<BuildingOccupancyVo> buildingList = new LinkedList(districtReceivableMap.get(o));

			// 构建表头
			Row title = sheet.createRow(0);

			for (int i = 0; i < titles.length; i++) {
				Cell tt = title.createCell(i, CellType.STRING);
				tt.setCellValue(titles[i]);
				tt.setCellStyle(cellStyle.get("title"));
			}
	
			int rowNumber = 1;
			for (BuildingOccupancyVo vo : buildingList) {
				Row row = sheet.createRow(rowNumber);
				// 楼盘名称
				Cell cell0 = row.createCell(0, CellType.STRING);
				cell0.setCellStyle(cellStyle.get("cell_left"));
				cell0.setCellValue(vo.getDistrictName());

				// 楼阁
				Cell cell1 = row.createCell(1, CellType.STRING);
				cell1.setCellStyle(cellStyle.get("cell_left"));
				cell1.setCellValue(vo.getBuildingName());

				// 总面积
				Cell cell2 = row.createCell(2, CellType.STRING);
				cell2.setCellStyle(cellStyle.get("cell_left"));
				cell2.setCellValue(vo.getTotalBuildingArea());

				// 总入住面积
				Cell cell3 = row.createCell(3, CellType.STRING);
				cell3.setCellStyle(cellStyle.get("cell_left"));
				cell3.setCellValue(vo.getTotalCheckInArea());

				// 总单元数
				Cell cell4 = row.createCell(4, CellType.STRING);
				cell4.setCellStyle(cellStyle.get("cell_right1"));
				cell4.setCellValue(vo.getTotalEstateCount());
				
				// 未售单元数
				Cell cell5 = row.createCell(5, CellType.STRING);
				cell5.setCellStyle(cellStyle.get("cell_right1"));
				cell5.setCellValue(vo.getUnsoldEstateCount());
				

				// 入住单元数
				Cell cell6 = row.createCell(6, CellType.STRING);
				cell6.setCellStyle(cellStyle.get("cell_right1"));
				cell6.setCellValue(vo.getCheckInEstateCount());

				// 入住率
				Cell cell7 = row.createCell(7, CellType.STRING);
				cell7.setCellStyle(cellStyle.get("cell_left"));
				cell7.setCellValue(vo.getOcccupancyRate()+"%");
				
				// 装修单元数
				Cell cell8 = row.createCell(8, CellType.STRING);
				cell8.setCellStyle(cellStyle.get("cell_right1"));
				cell8.setCellValue(vo.getFitmentEstateCount());

				// 装修率
				Cell cell9 = row.createCell(9, CellType.STRING);
				cell9.setCellStyle(cellStyle.get("cell_left"));
				cell9.setCellValue(vo.getFitmentRate()+"%");
				
				// 自住单元数
				Cell cell10 = row.createCell(10, CellType.STRING);
				cell10.setCellStyle(cellStyle.get("cell_right1"));
				cell10.setCellValue(vo.getSelfOccupationEstateCount());

				// 自住率
				Cell cell11 = row.createCell(11, CellType.STRING);
				cell11.setCellStyle(cellStyle.get("cell_left"));
				cell11.setCellValue(vo.getSelfOccupationRate()+"%");
				
				
				// 未收楼单元数
				Cell cell12 = row.createCell(12, CellType.STRING);
				cell12.setCellStyle(cellStyle.get("cell_right1"));
				cell12.setCellValue(vo.getUncollectedEstateCount());

				// 未收楼率
				Cell cell13 = row.createCell(13, CellType.STRING);
				cell13.setCellStyle(cellStyle.get("cell_left"));
				cell13.setCellValue(vo.getUncollectedRate()+"%");
				
				// 空置单元数
				Cell cell14 = row.createCell(14, CellType.STRING);
				cell14.setCellStyle(cellStyle.get("cell_right1"));
				cell14.setCellValue(vo.getVacancyEstateCount());

				// 空置率
				Cell cell15 = row.createCell(15, CellType.STRING);
				cell15.setCellStyle(cellStyle.get("cell_left"));
				cell15.setCellValue(vo.getVacancyRate()+"%");

				rowNumber++;

			}

			//小计行
			Row subTotalRow = sheet.createRow(rowNumber);
			Cell xj = subTotalRow.createCell(1, CellType.STRING);
			xj.setCellStyle(cellStyle.get("total_double"));
			xj.setCellValue("小计");

			rowNumber++;

			// 增加最后合计行
			// 合计行
			Row totalRow2 = sheet.createRow(rowNumber);
			Cell totalRow2_cell1 = totalRow2.createCell(0, CellType.STRING);
			totalRow2_cell1.setCellStyle(cellStyle.get("total_double"));
			totalRow2_cell1.setCellValue("合计");


			sheet.autoSizeColumn(0);
			sheet.autoSizeColumn(1);
			sheet.autoSizeColumn(2);
			sheet.autoSizeColumn(3);
			sheet.autoSizeColumn(4);
			sheet.autoSizeColumn(5);
			sheet.autoSizeColumn(6);
			sheet.autoSizeColumn(7);
			sheet.autoSizeColumn(8);
			sheet.autoSizeColumn(9);
			
			// 冻结首行
			sheet.createFreezePane(0, 1, 0, 1);
		
			CellRangeAddress c = CellRangeAddress.valueOf(sheet.getRow(0).getCell(0).getAddress()
					+ ":" + sheet.getRow(0)
							.getCell(sheet.getRow(0).getLastCellNum() - 1).getAddress());
			sheet.setAutoFilter(c);

		}
		
		
		
		
		String fileName = "入住率报表" + DateUtil.formatByStyle(new Date(), "_yyyy_MM_dd_HH_mm_ss") + ".xlsx";

		try {
			ExcelExportUtil.export(response, wb, fileName);
		} catch (IOException e) {
			log.error(e.getMessage());
		}

	}

}
