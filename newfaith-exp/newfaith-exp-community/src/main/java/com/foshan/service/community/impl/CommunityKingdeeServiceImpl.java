package com.foshan.service.community.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.bouncycastle.util.Arrays;
import org.springframework.stereotype.Service;

import com.foshan.entity.community.CommunityKingdeeEntity;
import com.foshan.entity.community.context.CommunityContext;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.kingdee.CommunityKingdeeForm;
import com.foshan.form.community.kingdee.ReceiptOrderForm;
import com.foshan.form.community.kingdee.ReceivablesOrderForm;
import com.foshan.form.community.request.CommunityKingdeeReq;
import com.foshan.form.community.response.kingdee.GetKingdeeImportHistoryRes;
import com.foshan.form.community.response.kingdee.GetToKingdeeDataListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.ICommunityKingdeeService;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.community.CommunityCache;

@Transactional
@Service("communityKingdeeService")
public class CommunityKingdeeServiceImpl extends GenericCommunityService implements ICommunityKingdeeService {
	private static Map<String, Integer> kingdeeMap = new HashMap<>();

	/**
	 * 获取应收单 根据要入帐的startDate和endDate时间生成应收单excel文件，如果已经存在应收单且还未导入金蝶系统的则
	 * 重新生成应收单，如已经导入金蝶系统，则直接读取文件列表
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse getReceivablesOrder(CommunityKingdeeReq req) {
		GetToKingdeeDataListRes<ReceivablesOrderForm> res = new GetToKingdeeDataListRes<>();
		String fileName = "";
		CommunityKingdeeEntity kingdeeInfo = null;
		LinkedList<Object[]> ysdList = new LinkedList<>();

		if (StringUtils.isNotEmpty(req.getStartDate()) && StringUtils.isNotEmpty(req.getEndDate())) {
			String startDate = req.getStartDate().length() > 10 ? req.getStartDate().substring(0, 10)
					: req.getStartDate();
			String endDate = req.getEndDate().length() > 10 ? req.getEndDate().substring(0, 10) : req.getEndDate();

			if (LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
					.isBefore(LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")))) {

				CommunityKingdeeEntity lastImport = communityKingdeeDao.getUniqueBySql(
						"SELECT * FROM t_community_kingdee WHERE importstate=1 AND filetype='应收单' ORDER BY enddate DESC LIMIT 1");

				if (null != lastImport
						&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
								.isAfter(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
										DateTimeFormatter.ofPattern("yyyy-MM-dd")))
								|| (LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).toEpochDay()
										- LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")).toEpochDay()) > 1)
						&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
								.isEqual(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getStartDate()),
										DateTimeFormatter.ofPattern("yyyy-MM-dd")))
								|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd"))))) {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo("本次导入时间不能早于上次导入结束时间或与上次时间不连续！");
					return res;
				}

				kingdeeInfo = communityKingdeeDao.getUniqueBySql(
						"SELECT * FROM t_community_kingdee WHERE filetype='应收单' ORDER BY id DESC LIMIT 1");

				if (null == kingdeeInfo
						|| kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_FAIL)
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS)
								&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(kingdeeInfo.getStartDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")))
										|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
												.isEqual(LocalDate.parse(
														DateUtil.formatShortFormat(kingdeeInfo.getEndDate()),
														DateTimeFormatter.ofPattern("yyyy-MM-dd")))))
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)
								&& req.getRefreshFlag().equals(1))
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)
								&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(kingdeeInfo.getStartDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")))
										|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
												.isEqual(LocalDate.parse(
														DateUtil.formatShortFormat(kingdeeInfo.getEndDate()),
														DateTimeFormatter.ofPattern("yyyy-MM-dd")))))) {
					CommunityCache.cache.remove("ysd");

					if (null == kingdeeInfo
							|| kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS)) {
						kingdeeInfo = new CommunityKingdeeEntity();
					} else {
						File file = new File(kingdeeInfo.getFileName());
						file.delete();
					}

					ysdList = queryYsdList(startDate, endDate);

					if (ysdList.size() > 0) {
						fileName = saveYsd(ysdList, startDate, endDate);
						kingdeeInfo.setStartDate(
								Date.from(LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
						kingdeeInfo.setEndDate(
								Date.from(LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
						kingdeeInfo.setFileName(fileName);
						kingdeeInfo.setFileType("应收单");
						kingdeeInfo.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_CREATE);
						kingdeeInfo.setState(EntityContext.RECORD_STATE_VALID);

						if (null == kingdeeInfo.getId()) {
							communityKingdeeDao.save(kingdeeInfo);
						} else {
							kingdeeInfo.setImportDate(null);
							communityKingdeeDao.update(kingdeeInfo);
						}

						// 写缓存
						Map<String, Collection> temp = new HashMap<>();
						temp.put(startDate + "_" + endDate, ysdList);
						CommunityCache.cache.put("ysd", temp);
					}
				} else {
					if (CommunityCache.cache.containsKey("ysd")
							&& CommunityCache.cache.get("ysd").containsKey(startDate + "_" + endDate)) {
						ysdList = new LinkedList(CommunityCache.cache.get("ysd").get(startDate + "_" + endDate));
					} else {
						CommunityCache.cache.remove("ysd");
						ysdList = parseYsdFile(kingdeeInfo.getFileName());
						Map<String, Collection> temp = new HashMap<>();
						temp.put(DateUtil.formatShortFormat(kingdeeInfo.getStartDate()) + "_"
								+ DateUtil.formatShortFormat(kingdeeInfo.getEndDate()), ysdList);
						CommunityCache.cache.put("ysd", temp);
					}
				}

				res.setKingdeeId(kingdeeInfo.getId());
				res.setCurrentPage((null == req.requestPage || req.getRequestPage() <= 1) ? 1 : req.getRequestPage());
				res.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
				res.setTotalResult(ysdList.size());
				res.setTotal((res.getTotalResult() - 1) / res.getPageSize() + 1);
				res.setKingdeeId(kingdeeInfo.getId());
				res.setFileName(kingdeeInfo.getFileName());
				res.setImportState(null != kingdeeInfo ? kingdeeInfo.getImportState() : null);
				res.setImportDate(null != kingdeeInfo && null != kingdeeInfo.getImportDate()
						? DateUtil.formatShortFormat(kingdeeInfo.getImportDate())
						: null);
				List<Object[]> subList = new LinkedList<>(ysdList).subList(
						(req.getRequestPage() - 1) * req.getPageSize() <= 0 ? 0
								: (req.getRequestPage() - 1) * req.getPageSize(),
						req.getRequestPage() * req.getPageSize() >= ysdList.size() ? ysdList.size()
								: req.getRequestPage() * req.getPageSize());
				subList.forEach(o -> {
					res.getDataList().add(ReceivablesOrderForm.getYsd(o));
				});
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else

		{
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "(startDate和endDate)");
		}

		return res;
	}

	@SuppressWarnings("resource")
	private LinkedList<Object[]> parseYsdFile(String fileName) {
		LinkedList<Object[]> result = new LinkedList<>();
		try {

			IOUtils.setByteArrayMaxOverride(1000000000);
			Workbook wb = new XSSFWorkbook(fileName);
			Sheet sheet = wb.getSheetAt(wb.getActiveSheetIndex());

			for (int i = 1; i <= sheet.getLastRowNum(); i++) {
				Row row = sheet.getRow(i);
				Object[] o = new Object[row.getLastCellNum() - 2];
				o[0] = row.getCell(1).getStringCellValue();
				o[1] = row.getCell(2).getStringCellValue();
				o[2] = row.getCell(3).getStringCellValue();
				o[3] = row.getCell(4).getStringCellValue();
				o[4] = row.getCell(5).getStringCellValue();
				o[5] = row.getCell(6).getStringCellValue();
				o[6] = row.getCell(7).getStringCellValue();
				o[7] = row.getCell(9).getStringCellValue();
				o[8] = row.getCell(10).getStringCellValue();
				o[9] = row.getCell(11).getNumericCellValue();
				o[10] = row.getCell(12).getNumericCellValue();
				o[11] = row.getCell(13).getNumericCellValue();
				o[12] = row.getCell(14).getNumericCellValue();
				o[13] = row.getCell(15).getNumericCellValue();
				o[14] = row.getCell(16).getNumericCellValue();
				o[15] = row.getCell(17).getNumericCellValue();
				o[16] = row.getCell(18).getNumericCellValue();
				o[17] = row.getCell(19).getNumericCellValue();
				o[18] = row.getCell(20).getNumericCellValue();
				o[19] = row.getCell(21).getNumericCellValue();
				o[20] = row.getCell(22).getNumericCellValue();
				o[21] = row.getCell(23).getNumericCellValue();
				o[22] = row.getCell(24).getNumericCellValue();
				o[23] = row.getCell(25).getNumericCellValue();
				o[24] = row.getCell(26).getNumericCellValue();
				o[25] = row.getCell(27).getNumericCellValue();
				o[26] = row.getCell(28).getNumericCellValue();
				o[27] = row.getCell(29).getNumericCellValue();
				o[28] = row.getCell(30).getNumericCellValue();
				o[29] = row.getCell(31).getNumericCellValue();
				result.add(o);
			}

		} catch (Exception ex) {
			ex.printStackTrace();

		}
		return result;
	}

	@Override
	public IResponse importReceivablesOrder(CommunityKingdeeReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getKingdeeId()) {
			if (!kingdeeMap.containsKey("应收单")) {
				kingdeeMap.put("应收单", req.getKingdeeId());

				CommunityKingdeeEntity kingdee = communityKingdeeDao.get(req.getKingdeeId());
				File ysd = new File(null != kingdee ? kingdee.getFileName() : "");
				if (null != kingdee && ysd.exists()
						&& kingdee.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)) {
					kingdee.setImportDate(new Date());
					if (sendYsd(kingdee.getFileName())) {
						kingdee.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS);
						res.setRet(ResponseContext.RES_SUCCESS_CODE);
						res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					} else {
						kingdee.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_FAIL);
						res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
					}
					communityKingdeeDao.save(kingdee);

				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo("同步文件不存在或该记录已经成功同步,无需再次同步！");
				}
				kingdeeMap.remove("应收单");
			} else {
				res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
				res.setRetInfo("请勿重复操作！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public IResponse getReceivablesOrderChange(CommunityKingdeeReq req) {
		// TODO Auto-generated method stub
		GetToKingdeeDataListRes<ReceivablesOrderForm> res = new GetToKingdeeDataListRes<>();
		String fileName = "";
		CommunityKingdeeEntity kingdeeInfo = null;
		LinkedList<Object[]> ysdChangeList = new LinkedList<>();

		if (StringUtils.isNotEmpty(req.getStartDate()) && StringUtils.isNotEmpty(req.getEndDate())) {
			String startDate = req.getStartDate().length() > 10 ? req.getStartDate().substring(0, 10)
					: req.getStartDate();
			String endDate = req.getEndDate().length() > 10 ? req.getEndDate().substring(0, 10) : req.getEndDate();

			if (LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
					.isBefore(LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")))) {

				CommunityKingdeeEntity lastImport = communityKingdeeDao.getUniqueBySql(
						"SELECT * FROM t_community_kingdee WHERE importstate=1 AND filetype='应收单减免' ORDER BY enddate DESC LIMIT 1");

				if (null != lastImport
						&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
								.isAfter(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
										DateTimeFormatter.ofPattern("yyyy-MM-dd")))
								|| (LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).toEpochDay()
										- LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")).toEpochDay()) > 1)
						&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
								.isEqual(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getStartDate()),
										DateTimeFormatter.ofPattern("yyyy-MM-dd")))
								|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd"))))) {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo("本次导入时间不能早于上次导入结束时间或与上次时间不连续！");
					return res;
				}

				kingdeeInfo = communityKingdeeDao.getUniqueBySql(
						"SELECT * FROM t_community_kingdee WHERE filetype='应收单减免' ORDER BY id DESC LIMIT 1");

				if (null == kingdeeInfo
						|| kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_FAIL)
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS)
								&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(kingdeeInfo.getStartDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")))
										|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
												.isEqual(LocalDate.parse(
														DateUtil.formatShortFormat(kingdeeInfo.getEndDate()),
														DateTimeFormatter.ofPattern("yyyy-MM-dd")))))
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)
								&& req.getRefreshFlag().equals(1))
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)
								&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(kingdeeInfo.getStartDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")))
										|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
												.isEqual(LocalDate.parse(
														DateUtil.formatShortFormat(kingdeeInfo.getEndDate()),
														DateTimeFormatter.ofPattern("yyyy-MM-dd")))))) {
					CommunityCache.cache.remove("ysdchange");

					if (null == kingdeeInfo
							|| kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS)) {
						kingdeeInfo = new CommunityKingdeeEntity();
					} else {
						File file = new File(kingdeeInfo.getFileName());
						file.delete();
					}

					ysdChangeList = queryYsdChangeList(startDate, endDate);

					if (ysdChangeList.size() > 0) {
						fileName = saveYsdChange(ysdChangeList, startDate, endDate);
						kingdeeInfo.setStartDate(
								Date.from(LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
						kingdeeInfo.setEndDate(
								Date.from(LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
						kingdeeInfo.setFileName(fileName);
						kingdeeInfo.setFileType("应收单减免");
						kingdeeInfo.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_CREATE);
						kingdeeInfo.setState(EntityContext.RECORD_STATE_VALID);

						if (null == kingdeeInfo.getId()) {
							communityKingdeeDao.save(kingdeeInfo);
						} else {
							kingdeeInfo.setImportDate(null);
							communityKingdeeDao.update(kingdeeInfo);
						}

						// 写缓存
						Map<String, Collection> temp = new HashMap<>();
						temp.put(startDate + "_" + endDate, ysdChangeList);
						CommunityCache.cache.put("ysdchange", temp);
					}
				} else {
					if (CommunityCache.cache.containsKey("ysdchange")
							&& CommunityCache.cache.get("ysdchange").containsKey(startDate + "_" + endDate)) {
						ysdChangeList = new LinkedList(
								CommunityCache.cache.get("ysdchange").get(startDate + "_" + endDate));
					} else {
						CommunityCache.cache.remove("ysdchange");
						ysdChangeList = parseYsdChangeFile(kingdeeInfo.getFileName());
						Map<String, Collection> temp = new HashMap<>();
						temp.put(DateUtil.formatShortFormat(kingdeeInfo.getStartDate()) + "_"
								+ DateUtil.formatShortFormat(kingdeeInfo.getEndDate()), ysdChangeList);
						CommunityCache.cache.put("ysdchange", temp);
					}
				}

				res.setKingdeeId(kingdeeInfo.getId());
				res.setCurrentPage((null == req.requestPage || req.getRequestPage() <= 1) ? 1 : req.getRequestPage());
				res.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
				res.setTotalResult(ysdChangeList.size());
				res.setTotal((res.getTotalResult() - 1) / res.getPageSize() + 1);
				res.setKingdeeId(kingdeeInfo.getId());
				res.setFileName(kingdeeInfo.getFileName());
				res.setImportState(null != kingdeeInfo ? kingdeeInfo.getImportState() : null);
				res.setImportDate(null != kingdeeInfo && null != kingdeeInfo.getImportDate()
						? DateUtil.formatShortFormat(kingdeeInfo.getImportDate())
						: null);
				List<Object[]> subList = new LinkedList<>(ysdChangeList).subList(
						(req.getRequestPage() - 1) * req.getPageSize() <= 0 ? 0
								: (req.getRequestPage() - 1) * req.getPageSize(),
						req.getRequestPage() * req.getPageSize() >= ysdChangeList.size() ? ysdChangeList.size()
								: req.getRequestPage() * req.getPageSize());
				subList.forEach(o -> {
					res.getDataList().add(ReceivablesOrderForm.getYsd(o));
				});
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else

		{
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "(startDate和endDate)");
		}

		return res;
	}

	@SuppressWarnings("resource")
	private LinkedList<Object[]> parseYsdChangeFile(String fileName) {
		LinkedList<Object[]> result = new LinkedList<>();
		try {

			IOUtils.setByteArrayMaxOverride(1000000000);
			Workbook wb = new XSSFWorkbook(fileName);
			Sheet sheet = wb.getSheetAt(wb.getActiveSheetIndex());

			for (int i = 1; i <= sheet.getLastRowNum(); i++) {
				Row row = sheet.getRow(i);
				Object[] o = new Object[row.getLastCellNum() - 2];
				o[0] = row.getCell(1).getStringCellValue();
				o[1] = row.getCell(2).getStringCellValue();
				o[2] = row.getCell(3).getStringCellValue();
				o[3] = row.getCell(4).getStringCellValue();
				o[4] = row.getCell(5).getStringCellValue();
				o[5] = row.getCell(6).getStringCellValue();
				o[6] = row.getCell(7).getStringCellValue();
				o[7] = row.getCell(9).getStringCellValue();
				o[8] = row.getCell(10).getStringCellValue();
				o[9] = row.getCell(11).getNumericCellValue();
				o[10] = row.getCell(12).getNumericCellValue();
				o[11] = row.getCell(13).getNumericCellValue();
				o[12] = row.getCell(14).getNumericCellValue();
				o[13] = row.getCell(15).getNumericCellValue();
				o[14] = row.getCell(16).getNumericCellValue();
				o[15] = row.getCell(17).getNumericCellValue();
				o[16] = row.getCell(18).getNumericCellValue();
				o[17] = row.getCell(19).getNumericCellValue();
				o[18] = row.getCell(20).getNumericCellValue();
				o[19] = row.getCell(21).getNumericCellValue();
				o[20] = row.getCell(22).getNumericCellValue();
				o[21] = row.getCell(23).getNumericCellValue();
				o[22] = row.getCell(24).getNumericCellValue();
				o[23] = row.getCell(25).getNumericCellValue();
				o[24] = row.getCell(26).getNumericCellValue();
				o[25] = row.getCell(27).getNumericCellValue();
				o[26] = row.getCell(28).getNumericCellValue();
				o[27] = row.getCell(29).getNumericCellValue();
				o[28] = row.getCell(30).getNumericCellValue();
				o[29] = row.getCell(31).getNumericCellValue();
				o[30] = row.getCell(32).getStringCellValue();
				result.add(o);
			}

		} catch (Exception ex) {
			ex.printStackTrace();

		}
		return result;
	}

	@Override
	public IResponse importReceivablesOrderChange(CommunityKingdeeReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getKingdeeId()) {
			if (!kingdeeMap.containsKey("应收单减免")) {
				kingdeeMap.put("应收单减免", req.getKingdeeId());

				CommunityKingdeeEntity kingdee = communityKingdeeDao.get(req.getKingdeeId());
				File ysd = new File(null != kingdee ? kingdee.getFileName() : "");
				if (null != kingdee && ysd.exists()
						&& kingdee.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)) {
					kingdee.setImportDate(new Date());
					if (sendYsdChange(kingdee.getFileName())) {
						kingdee.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS);
						res.setRet(ResponseContext.RES_SUCCESS_CODE);
						res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					} else {
						kingdee.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_FAIL);
						res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
					}
					communityKingdeeDao.save(kingdee);

				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo("同步文件不存在或该记录已经成功同步,无需再次同步！");
				}
				kingdeeMap.remove("应收单减免");
			} else {
				res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
				res.setRetInfo("请勿重复操作！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public IResponse getReceiptOrder(CommunityKingdeeReq req) {
		GetToKingdeeDataListRes<ReceiptOrderForm> res = new GetToKingdeeDataListRes<>();
		String fileName = "";
		CommunityKingdeeEntity kingdeeInfo = null;
		LinkedList<Object[]> sjList = new LinkedList<>();

		if (StringUtils.isNotEmpty(req.getStartDate()) && StringUtils.isNotEmpty(req.getEndDate())) {
			String startDate = req.getStartDate().length() > 10 ? req.getStartDate().substring(0, 10)
					: req.getStartDate();
			String endDate = req.getEndDate().length() > 10 ? req.getEndDate().substring(0, 10) : req.getEndDate();

			if (LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
					.isBefore(LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")))) {

				CommunityKingdeeEntity lastImport = communityKingdeeDao.getUniqueBySql(
						"SELECT * FROM t_community_kingdee WHERE importstate=1 AND filetype='收据' ORDER BY enddate DESC LIMIT 1");

				if (null != lastImport
						&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
								.isAfter(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
										DateTimeFormatter.ofPattern("yyyy-MM-dd")))
								|| (LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).toEpochDay()
										- LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")).toEpochDay()) > 1)
						&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
								.isEqual(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getStartDate()),
										DateTimeFormatter.ofPattern("yyyy-MM-dd")))
								|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd"))))) {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo("本次导入时间不能早于上次导入结束时间或与上次时间不连续！");
					return res;
				}

				kingdeeInfo = communityKingdeeDao.getUniqueBySql(
						"SELECT * FROM t_community_kingdee WHERE filetype='收据' ORDER BY id DESC LIMIT 1");

				if (null == kingdeeInfo
						|| kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_FAIL)
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS)
								&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(kingdeeInfo.getStartDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")))
										|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
												.isEqual(LocalDate.parse(
														DateUtil.formatShortFormat(kingdeeInfo.getEndDate()),
														DateTimeFormatter.ofPattern("yyyy-MM-dd")))))
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)
								&& req.getRefreshFlag().equals(1))
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)
								&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(kingdeeInfo.getStartDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")))
										|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
												.isEqual(LocalDate.parse(
														DateUtil.formatShortFormat(kingdeeInfo.getEndDate()),
														DateTimeFormatter.ofPattern("yyyy-MM-dd")))))) {
					CommunityCache.cache.remove("shouju");

					if (null == kingdeeInfo
							|| kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS)) {
						kingdeeInfo = new CommunityKingdeeEntity();
					} else {
						File file = new File(kingdeeInfo.getFileName());
						file.delete();
					}

					sjList = queryShoujuList(startDate, endDate);

					if (sjList.size() > 0) {
						fileName = saveShouju(sjList, startDate, endDate);
						kingdeeInfo.setStartDate(
								Date.from(LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
						kingdeeInfo.setEndDate(
								Date.from(LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
						kingdeeInfo.setFileName(fileName);
						kingdeeInfo.setFileType("收据");
						kingdeeInfo.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_CREATE);
						kingdeeInfo.setState(EntityContext.RECORD_STATE_VALID);

						if (null == kingdeeInfo.getId()) {
							communityKingdeeDao.save(kingdeeInfo);
						} else {
							kingdeeInfo.setImportDate(null);
							communityKingdeeDao.update(kingdeeInfo);
						}

						// 写缓存
						Map<String, Collection> temp = new HashMap<>();
						temp.put(startDate + "_" + endDate, sjList);
						CommunityCache.cache.put("shouju", temp);
					}
				} else {
					if (CommunityCache.cache.containsKey("shouju")
							&& CommunityCache.cache.get("shouju").containsKey(startDate + "_" + endDate)) {
						sjList = new LinkedList(CommunityCache.cache.get("shouju").get(startDate + "_" + endDate));
					} else {
						CommunityCache.cache.remove("shouju");
						sjList = parseShoujuFile(kingdeeInfo.getFileName());
						Map<String, Collection> temp = new HashMap<>();
						temp.put(DateUtil.formatShortFormat(kingdeeInfo.getStartDate()) + "_"
								+ DateUtil.formatShortFormat(kingdeeInfo.getEndDate()), sjList);
						CommunityCache.cache.put("shouju", temp);
					}
				}

				res.setCurrentPage((null == req.requestPage || req.getRequestPage() <= 1) ? 1 : req.getRequestPage());
				res.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
				res.setTotalResult(sjList.size());
				res.setTotal((res.getTotalResult() - 1) / res.getPageSize() + 1);
				res.setKingdeeId(kingdeeInfo.getId());
				res.setFileName(kingdeeInfo.getFileName());
				res.setImportState(null != kingdeeInfo ? kingdeeInfo.getImportState() : null);
				res.setImportDate(null != kingdeeInfo && null != kingdeeInfo.getImportDate()
						? DateUtil.formatShortFormat(kingdeeInfo.getImportDate())
						: null);
				List<Object[]> subList = new LinkedList<>(sjList).subList(
						(req.getRequestPage() - 1) * req.getPageSize() <= 0 ? 0
								: (req.getRequestPage() - 1) * req.getPageSize(),
						req.getRequestPage() * req.getPageSize() >= sjList.size() ? sjList.size()
								: req.getRequestPage() * req.getPageSize());
				subList.forEach(o -> {
					res.getDataList().add(ReceiptOrderForm.getShouju(o));
				});
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "(startDate和endDate)");
		}

		return res;
	}

	@SuppressWarnings("resource")
	private LinkedList<Object[]> parseShoujuFile(String fileName) {
		LinkedList<Object[]> result = new LinkedList<>();
		try {
			IOUtils.setByteArrayMaxOverride(1000000000);
			Workbook wb = new XSSFWorkbook(fileName);
			Sheet sheet = wb.getSheetAt(wb.getActiveSheetIndex());

			for (int i = 1; i <= sheet.getLastRowNum(); i++) {
				Row row = sheet.getRow(i);

				Object[] o = new Object[row.getLastCellNum() - 2];
				o[0] = row.getCell(1).getStringCellValue();
				o[1] = row.getCell(2).getStringCellValue();
				o[2] = row.getCell(3).getStringCellValue();
				o[3] = row.getCell(4).getStringCellValue();
				o[4] = row.getCell(5).getStringCellValue();
				o[5] = row.getCell(6).getStringCellValue();
				o[6] = row.getCell(7).getStringCellValue();
				o[7] = row.getCell(8).getStringCellValue();
				o[8] = row.getCell(9).getNumericCellValue();
				o[9] = row.getCell(10).getNumericCellValue();
				o[10] = row.getCell(11).getNumericCellValue();
				o[11] = row.getCell(12).getNumericCellValue();
				o[12] = row.getCell(13).getNumericCellValue();
				o[13] = row.getCell(14).getNumericCellValue();
				o[14] = row.getCell(15).getNumericCellValue();
				o[15] = row.getCell(16).getNumericCellValue();
				o[16] = row.getCell(17).getNumericCellValue();
				o[17] = row.getCell(18).getNumericCellValue();
				o[18] = row.getCell(19).getStringCellValue();
				o[19] = row.getCell(20).getStringCellValue();
				o[20] = row.getCell(21).getStringCellValue();
				o[21] = row.getCell(22).getNumericCellValue();
				o[22] = row.getCell(23).getNumericCellValue();
				o[23] = row.getCell(24).getNumericCellValue();

				o[24] = row.getCell(26).getStringCellValue();
				o[25] = row.getCell(27).getStringCellValue();
				o[26] = row.getCell(28).getStringCellValue();
				o[27] = row.getCell(29).getNumericCellValue();
				o[28] = row.getCell(30).getStringCellValue();
				o[29] = row.getCell(31).getNumericCellValue();
				o[30] = row.getCell(32).getStringCellValue();
				o[31] = row.getCell(33).getNumericCellValue();
				o[32] = row.getCell(34).getStringCellValue();
				o[33] = row.getCell(35).getNumericCellValue();
				o[34] = row.getCell(36).getStringCellValue();
				o[35] = row.getCell(37).getNumericCellValue();
				o[36] = row.getCell(38).getStringCellValue();
				o[37] = row.getCell(39).getNumericCellValue();
				o[38] = row.getCell(40).getStringCellValue();
				o[39] = row.getCell(41).getNumericCellValue();
				o[40] = row.getCell(42).getStringCellValue();
				o[41] = row.getCell(43).getNumericCellValue();
				o[42] = row.getCell(44).getStringCellValue();
				o[43] = row.getCell(45).getNumericCellValue();
				o[44] = row.getCell(46).getStringCellValue();
				o[45] = row.getCell(47).getNumericCellValue();
				o[46] = row.getCell(48).getStringCellValue();
				o[47] = row.getCell(49).getNumericCellValue();
				o[48] = row.getCell(50).getStringCellValue();
				o[49] = row.getCell(51).getNumericCellValue();
				o[50] = row.getCell(52).getStringCellValue();
				o[51] = row.getCell(53).getNumericCellValue();
				o[52] = row.getCell(54).getStringCellValue();
				o[53] = row.getCell(55).getNumericCellValue();
				o[54] = row.getCell(56).getStringCellValue();
				o[55] = row.getCell(57).getNumericCellValue();
				o[56] = row.getCell(58).getStringCellValue();
				o[57] = row.getCell(59).getNumericCellValue();
				o[58] = row.getCell(60).getStringCellValue();
				o[59] = row.getCell(61).getNumericCellValue();
				o[60] = row.getCell(62).getStringCellValue();
				o[61] = row.getCell(63).getNumericCellValue();
				o[62] = row.getCell(64).getStringCellValue();
				o[63] = row.getCell(65).getNumericCellValue();
				o[64] = row.getCell(66).getStringCellValue();
				o[65] = row.getCell(67).getNumericCellValue();
				o[66] = row.getCell(68).getStringCellValue();
				o[67] = row.getCell(69).getNumericCellValue();
				o[68] = row.getCell(70).getStringCellValue();
				o[69] = row.getCell(71).getNumericCellValue();
				o[70] = row.getCell(72).getStringCellValue();
				o[71] = row.getCell(73).getNumericCellValue();
				o[72] = row.getCell(74).getStringCellValue();
				o[73] = row.getCell(75).getNumericCellValue();
				o[74] = row.getCell(76).getStringCellValue();
				o[75] = row.getCell(77).getNumericCellValue();
				o[76] = row.getCell(78).getStringCellValue();
				o[77] = row.getCell(79).getNumericCellValue();
				o[78] = row.getCell(80).getStringCellValue();
				o[79] = row.getCell(81).getNumericCellValue();
				o[80] = row.getCell(82).getStringCellValue();
				o[81] = row.getCell(83).getNumericCellValue();
				o[82] = row.getCell(84).getStringCellValue();
				o[83] = row.getCell(85).getNumericCellValue();
				o[84] = row.getCell(86).getStringCellValue();
				o[85] = row.getCell(87).getNumericCellValue();
				o[86] = row.getCell(88).getStringCellValue();
				o[87] = row.getCell(89).getNumericCellValue();
				o[88] = row.getCell(90).getStringCellValue();
				o[89] = row.getCell(91).getNumericCellValue();
				o[90] = row.getCell(92).getStringCellValue();
				o[91] = row.getCell(93).getNumericCellValue();
				o[92] = row.getCell(94).getStringCellValue();
				o[93] = row.getCell(95).getNumericCellValue();
				o[94] = row.getCell(96).getStringCellValue();
				o[95] = row.getCell(97).getNumericCellValue();
				o[96] = row.getCell(98).getStringCellValue();
				o[97] = row.getCell(99).getNumericCellValue();
				o[98] = row.getCell(100).getStringCellValue();
				o[99] = row.getCell(101).getNumericCellValue();
				o[100] = row.getCell(102).getStringCellValue();
				o[101] = row.getCell(103).getNumericCellValue();
				o[102] = row.getCell(104).getStringCellValue();
				o[103] = row.getCell(105).getNumericCellValue();
				o[104] = row.getCell(106).getStringCellValue();
				o[105] = row.getCell(107).getNumericCellValue();
				o[106] = row.getCell(108).getStringCellValue();
				o[107] = row.getCell(109).getNumericCellValue();
				o[108] = row.getCell(110).getStringCellValue();
				o[109] = row.getCell(111).getNumericCellValue();
				o[110] = row.getCell(112).getStringCellValue();
				o[111] = row.getCell(113).getNumericCellValue();
				o[112] = row.getCell(114).getStringCellValue();
				o[113] = row.getCell(115).getNumericCellValue();
				o[114] = row.getCell(116).getStringCellValue();
				o[115] = row.getCell(117).getNumericCellValue();
				o[116] = row.getCell(118).getStringCellValue();
				o[117] = row.getCell(119).getNumericCellValue();
				o[118] = row.getCell(120).getStringCellValue();
				o[119] = row.getCell(121).getNumericCellValue();
				o[120] = row.getCell(122).getStringCellValue();
				o[121] = row.getCell(123).getNumericCellValue();
				o[122] = row.getCell(124).getStringCellValue();
				o[123] = row.getCell(125).getNumericCellValue();
				o[124] = row.getCell(126).getStringCellValue();
				o[125] = row.getCell(127).getNumericCellValue();
				o[126] = row.getCell(128).getStringCellValue();
				o[127] = row.getCell(129).getNumericCellValue();
				o[128] = row.getCell(130).getStringCellValue();
				o[129] = row.getCell(131).getNumericCellValue();
				o[130] = row.getCell(132).getStringCellValue();
				o[131] = row.getCell(133).getNumericCellValue();
				o[132] = row.getCell(134).getStringCellValue();
				o[133] = row.getCell(135).getNumericCellValue();
				o[134] = row.getCell(136).getStringCellValue();
				o[135] = row.getCell(137).getNumericCellValue();
				o[136] = row.getCell(138).getStringCellValue();
				o[137] = row.getCell(139).getNumericCellValue();
				o[138] = row.getCell(140).getStringCellValue();
				o[139] = row.getCell(141).getNumericCellValue();
				o[140] = row.getCell(142).getStringCellValue();
				o[141] = row.getCell(143).getNumericCellValue();
				o[142] = row.getCell(144).getStringCellValue();
				o[143] = row.getCell(145).getNumericCellValue();
				o[144] = row.getCell(146).getStringCellValue();
				o[145] = row.getCell(147).getNumericCellValue();
				o[146] = row.getCell(148).getNumericCellValue();
				o[147] = row.getCell(149).getNumericCellValue();

				result.add(o);
			}

		} catch (Exception ex) {
			ex.printStackTrace();

		}

		return result;
	}

	@Override
	public IResponse importReceiptOrder(CommunityKingdeeReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getKingdeeId()) {

			if (!kingdeeMap.containsKey("收据")) {
				kingdeeMap.put("收据", req.getKingdeeId());
				CommunityKingdeeEntity kingdee = communityKingdeeDao.get(req.getKingdeeId());
				File shouju = new File(null != kingdee ? kingdee.getFileName() : "");
				if (null != kingdee && shouju.exists()
						&& kingdee.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)) {
					kingdee.setImportDate(new Date());
					if (sendShouju(kingdee.getFileName())) {
						kingdee.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS);
						res.setRet(ResponseContext.RES_SUCCESS_CODE);
						res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					} else {
						kingdee.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_FAIL);
						res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
					}
					communityKingdeeDao.save(kingdee);
				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo("同步文件不存在或该记录已经成功同步,无需再次同步！");
				}
				kingdeeMap.remove("收据");
			} else {
				res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
				res.setRetInfo("请勿重复操作！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@SuppressWarnings("rawtypes")
	@Override
	public IResponse getBaseData(CommunityKingdeeReq req) {
		GetToKingdeeDataListRes res = new GetToKingdeeDataListRes();
		String fileName = "";
		CommunityKingdeeEntity kingdeeInfo = null;

		if (StringUtils.isNotEmpty(req.getStartDate())) {

			String startDate = req.getStartDate().length() > 10 ? req.getStartDate().substring(0, 10)
					: req.getStartDate();
			String endDate = LocalDate.now().plusDays(-1).toString();
			if (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
					.isAfter(LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")))) {
				CommunityKingdeeEntity lastImport = communityKingdeeDao.getUniqueBySql(
						"SELECT * FROM t_community_kingdee WHERE importstate=1 AND filetype='基础数据' ORDER BY enddate DESC LIMIT 1");

				if (null != lastImport
						&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
								.isAfter(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
										DateTimeFormatter.ofPattern("yyyy-MM-dd")))
								|| (LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).toEpochDay()
										- LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")).toEpochDay()) > 1)
						&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
								.isEqual(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getStartDate()),
										DateTimeFormatter.ofPattern("yyyy-MM-dd")))
								|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(lastImport.getEndDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd"))))) {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo("本次导入时间不能早于上次导入结束时间或与上次截止时间不连续！");
					return res;
				}

				kingdeeInfo = communityKingdeeDao.getUniqueBySql(
						"SELECT * FROM t_community_kingdee WHERE filetype='基础数据' ORDER BY id DESC LIMIT 1");

				if (null == kingdeeInfo
						|| kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_FAIL)
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS)
								&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(kingdeeInfo.getStartDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")))
										|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
												.isEqual(LocalDate.parse(
														DateUtil.formatShortFormat(kingdeeInfo.getEndDate()),
														DateTimeFormatter.ofPattern("yyyy-MM-dd")))))
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)
								&& req.getRefreshFlag().equals(1))
						|| (kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)
								&& (!LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.isEqual(LocalDate.parse(DateUtil.formatShortFormat(kingdeeInfo.getStartDate()),
												DateTimeFormatter.ofPattern("yyyy-MM-dd")))
										|| !LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
												.isEqual(LocalDate.parse(
														DateUtil.formatShortFormat(kingdeeInfo.getEndDate()),
														DateTimeFormatter.ofPattern("yyyy-MM-dd")))))) {

					if (null == kingdeeInfo
							|| kingdeeInfo.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS)) {
						kingdeeInfo = new CommunityKingdeeEntity();
					} else {
						File file = new File(kingdeeInfo.getFileName());
						file.delete();
					}

					Map<String, List<Object[]>> baseDataMap = queryBaseDataMap(startDate + " 00:00:00",
							endDate + " 23:59:59");

					if (baseDataMap.size() > 0) {
						// 保存收费项目文件
						fileName = saveBaseData(baseDataMap, startDate, endDate);
						kingdeeInfo.setStartDate(
								Date.from(LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
						kingdeeInfo.setEndDate(
								Date.from(LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
										.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
						kingdeeInfo.setFileName(fileName);
						kingdeeInfo.setFileType("基础数据");
						kingdeeInfo.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_CREATE);
						kingdeeInfo.setState(EntityContext.RECORD_STATE_VALID);

						if (null == kingdeeInfo.getId()) {
							communityKingdeeDao.save(kingdeeInfo);
						} else {
							kingdeeInfo.setImportDate(null);
							communityKingdeeDao.update(kingdeeInfo);
						}
					}

				}

				res.setFileName(kingdeeInfo.getFileName());
				res.setKingdeeId(kingdeeInfo.getId());
				res.setImportState(kingdeeInfo.getImportState());
				res.setImportDate(
						null != kingdeeInfo.getImportDate() ? DateUtil.formatShortFormat(kingdeeInfo.getImportDate())
								: null);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "(startDate和endDate)");
		}

		return res;
	}

	@Override
	public IResponse importBaseData(CommunityKingdeeReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getKingdeeId()) {

			if (!kingdeeMap.containsKey("基础数据")) {
				kingdeeMap.put("基础数据", req.getKingdeeId());

				CommunityKingdeeEntity kingdee = communityKingdeeDao.get(req.getKingdeeId());
				File item = new File(null != kingdee ? kingdee.getFileName() : "");
				if (null != kingdee && item.exists()
						&& kingdee.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_CREATE)) {
					kingdee.setImportDate(new Date());
					if (sendBaseData(kingdee.getFileName())) {
						kingdee.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS);
						res.setRet(ResponseContext.RES_SUCCESS_CODE);
						res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
					} else {
						kingdee.setImportState(CommunityContext.KINGDEE_IMPORT_STATE_FAIL);
						res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
						res.setRetInfo(ResponseContext.RES_UNKNOW_ERROR_INFO);
					}
					communityKingdeeDao.save(kingdee);

				} else {
					res.setRet(ResponseContext.RES_DATA_NULL_CODE);
					res.setRetInfo("同步文件不存在或该记录已经成功同步,无需再次同步！");
				}
				kingdeeMap.remove("基础数据");
			} else {
				res.setRet(ResponseContext.RES_UNKNOW_ERROR_CODE);
				res.setRetInfo("请勿重复操作！！！");
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getKingdeeImportHistoryList(CommunityKingdeeReq req) {
		GetKingdeeImportHistoryRes res = new GetKingdeeImportHistoryRes();

		StringBuilder sql = new StringBuilder("select * from t_community_kingdee a where 1=1")
				.append(StringUtils.isNotEmpty(req.getFileType()) ? " and a.fileType='" + req.getFileType() + "'" : "")
				.append(null == req.getImportState() ? " order by id desc limit 10"
						: req.getImportState().equals(1) ? " and importState=1 order by importdate desc limit 10"
								: " and importState=" + req.getImportState()
										+ " order by startdate desc,enddate desc limit 10");

		List<CommunityKingdeeEntity> historyList = communityKingdeeDao.getListBySql(sql.toString());

		historyList.forEach(o -> {
			res.getKingdeeList()
					.add(new CommunityKingdeeForm(o.getId(),
							null != o.getImportDate() ? DateUtil.formatShortFormat(o.getImportDate()) : "",
							DateUtil.formatShortFormat(o.getStartDate()), DateUtil.formatShortFormat(o.getEndDate()),
							o.getFileName(), o.getFileType(), o.getImportState()));
		});

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		return res;
	}

	@Override
	public IResponse deleteKingdeeImportHistory(CommunityKingdeeReq req) {
		GetKingdeeImportHistoryRes res = new GetKingdeeImportHistoryRes();
		if (null != req.getKingdeeId()) {
			CommunityKingdeeEntity history = communityKingdeeDao.get(req.getKingdeeId());
			if (null != history) {
				if (!history.getImportState().equals(CommunityContext.KINGDEE_IMPORT_STATE_SUCCESS)) {
					File file = new File(history.getFileName());
					if (null != file && file.exists()) {
						file.delete();
					}
					communityKingdeeDao.delete(history);
					res.setRet(ResponseContext.RES_SUCCESS_CODE);
					res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
				} else {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo("同步成功数据不能删除！");
				}
			}
		}

		return res;
	}

	@Override
	public void exportKingdeeImportFile(CommunityKingdeeReq req, HttpServletResponse response) {
		if (null != req.getKingdeeId()) {
			CommunityKingdeeEntity history = communityKingdeeDao.get(req.getKingdeeId());
			if (null != history) {
				try {
					IOUtils.setByteArrayMaxOverride(1000000000);
					XSSFWorkbook wb = new XSSFWorkbook(history.getFileName());
					ExcelExportUtil.export(response, wb, history.getFileName().substring(14));
				} catch (Exception ex) {
					ex.printStackTrace();
				}
			}
		}
	}

	private String saveYsd(List<Object[]> ysdList, String startDate, String endDate) {
		String fileName = communityContextInfo.kingdeeFilePath + "/应收单(" + startDate + "_" + endDate + ").xlsx";
		XSSFWorkbook wb = new XSSFWorkbook();
		Sheet sheet = wb.createSheet("应收单");
		String[] titles = { "fid", "单据编号", "日期", "楼盘名称", "单元编号", "姓名", "应收款id", "楼阁名称", "fid", "单据编号", "收费项目", "总金额",
				"物业管理费+含税金额", "车位管理费+含税金额", "别墅管理费+含税金额", "花园管理费+含税金额", "花园及停车位管理费+含税金额", "景观大道路灯电费+含税金额",
				"公共电费分摊+含税金额", "二区车库公共电费+含税金额", "三区车库公共电费+含税金额", "空中花园公共电费+含税金额", "车库公共电费+含税金额", "露天车位管理服务费+含税金额",
				"公共水费分摊+含税金额", "车库公共水费分摊+含税金额", "宿舍物业管理费+含税金额", "宿舍租金+含税金额", "商铺物业管理费+含税金额", "商铺租金+含税金额", "单元id",
				"业主id" };
		// 表头
		Row title = sheet.createRow(0);
		for (int i = 0; i < titles.length; i++) {
			Cell tt = title.createCell(i, CellType.STRING);
			tt.setCellValue(titles[i]);
		}

		int i = 1;
		for (Object[] o : ysdList) {
			Row row = sheet.createRow(i);

			// fid
			Cell cell1 = row.createCell(0, CellType.NUMERIC);
			cell1.setCellValue(i);
			// 单据编号
			Cell cell2 = row.createCell(1, CellType.STRING);
			cell2.setCellValue(o[0].toString());
			// 日期
			Cell cell3 = row.createCell(2, CellType.STRING);
			cell3.setCellValue(o[1].toString());
			// 楼盘名称
			Cell cell4 = row.createCell(3, CellType.STRING);
			cell4.setCellValue(o[2].toString());
			// 单元编号
			Cell cell5 = row.createCell(4, CellType.STRING);
			cell5.setCellValue(o[3].toString());
			// 姓名
			Cell cell6 = row.createCell(5, CellType.STRING);
			cell6.setCellValue(o[4].toString());
			// 应收款id
			Cell cell7 = row.createCell(6, CellType.NUMERIC);
			cell7.setCellValue(o[5].toString());
			// 楼阁名称
			Cell cell8 = row.createCell(7, CellType.STRING);
			cell8.setCellValue(o[6].toString());
			// fid
			Cell cell9 = row.createCell(8, CellType.NUMERIC);
			cell9.setCellValue(i);
			// 单据编号
			Cell cell10 = row.createCell(9, CellType.STRING);
			cell10.setCellValue(o[7].toString());
			// 收费项目
			Cell cell11 = row.createCell(10, CellType.STRING);
			cell11.setCellValue(o[8].toString());
			// 总金额
			Cell cell12 = row.createCell(11, CellType.NUMERIC);
			cell12.setCellValue(new BigDecimal(o[9].toString()).doubleValue());
			// 物业管理费+含税金额
			Cell cell13 = row.createCell(12, CellType.NUMERIC);
			cell13.setCellValue(
					StringUtils.isNotEmpty(o[10].toString()) ? new BigDecimal(o[10].toString()).doubleValue() : 0);
			// 车位管理费+含税金额
			Cell cell14 = row.createCell(13, CellType.NUMERIC);
			cell14.setCellValue(
					StringUtils.isNotEmpty(o[11].toString()) ? new BigDecimal(o[11].toString()).doubleValue() : 0);
			// 别墅管理费+含税金额
			Cell cell15 = row.createCell(14, CellType.NUMERIC);
			cell15.setCellValue(
					StringUtils.isNotEmpty(o[12].toString()) ? new BigDecimal(o[12].toString()).doubleValue() : 0);
			// 花园管理费+含税金额
			Cell cell16 = row.createCell(15, CellType.NUMERIC);
			cell16.setCellValue(
					StringUtils.isNotEmpty(o[13].toString()) ? new BigDecimal(o[13].toString()).doubleValue() : 0);
			// 花园及停车位管理费+含税金额
			Cell cell17 = row.createCell(16, CellType.NUMERIC);
			cell17.setCellValue(
					StringUtils.isNotEmpty(o[14].toString()) ? new BigDecimal(o[14].toString()).doubleValue() : 0);
			// 景观大道路灯电费+含税金额
			Cell cell18 = row.createCell(17, CellType.NUMERIC);
			cell18.setCellValue(
					StringUtils.isNotEmpty(o[15].toString()) ? new BigDecimal(o[15].toString()).doubleValue() : 0);
			// 公共电费分摊+含税金额
			Cell cell19 = row.createCell(18, CellType.NUMERIC);
			cell19.setCellValue(
					StringUtils.isNotEmpty(o[16].toString()) ? new BigDecimal(o[16].toString()).doubleValue() : 0);
			// 二区车库公共电费+含税金额
			Cell cell20 = row.createCell(19, CellType.NUMERIC);
			cell20.setCellValue(
					StringUtils.isNotEmpty(o[17].toString()) ? new BigDecimal(o[17].toString()).doubleValue() : 0);
			// 三区车库公共电费+含税金额
			Cell cell21 = row.createCell(20, CellType.NUMERIC);
			cell21.setCellValue(
					StringUtils.isNotEmpty(o[18].toString()) ? new BigDecimal(o[18].toString()).doubleValue() : 0);
			// 空中花园公共电费+含税金额
			Cell cell22 = row.createCell(21, CellType.NUMERIC);
			cell22.setCellValue(
					StringUtils.isNotEmpty(o[19].toString()) ? new BigDecimal(o[19].toString()).doubleValue() : 0);
			// 车库公共电费+含税金额
			Cell cell23 = row.createCell(22, CellType.NUMERIC);
			cell23.setCellValue(
					StringUtils.isNotEmpty(o[20].toString()) ? new BigDecimal(o[20].toString()).doubleValue() : 0);
			// 露天车位管理服务费+含税金额
			Cell cell24 = row.createCell(23, CellType.NUMERIC);
			cell24.setCellValue(
					StringUtils.isNotEmpty(o[21].toString()) ? new BigDecimal(o[21].toString()).doubleValue() : 0);
			// 公共水费分摊+含税金额
			Cell cell25 = row.createCell(24, CellType.NUMERIC);
			cell25.setCellValue(
					StringUtils.isNotEmpty(o[22].toString()) ? new BigDecimal(o[22].toString()).doubleValue() : 0);
			// 车库公共水费分摊+含税金额
			Cell cell26 = row.createCell(25, CellType.NUMERIC);
			cell26.setCellValue(
					StringUtils.isNotEmpty(o[23].toString()) ? new BigDecimal(o[23].toString()).doubleValue() : 0);
			// 宿舍物业管理费+含税金额
			Cell cell27 = row.createCell(26, CellType.NUMERIC);
			cell27.setCellValue(
					StringUtils.isNotEmpty(o[24].toString()) ? new BigDecimal(o[24].toString()).doubleValue() : 0);
			// 宿舍租金+含税金额
			Cell cell28 = row.createCell(27, CellType.NUMERIC);
			cell28.setCellValue(
					StringUtils.isNotEmpty(o[25].toString()) ? new BigDecimal(o[25].toString()).doubleValue() : 0);
			// 商铺物业管理费+含税金额
			Cell cell39 = row.createCell(28, CellType.NUMERIC);
			cell39.setCellValue(
					StringUtils.isNotEmpty(o[26].toString()) ? new BigDecimal(o[26].toString()).doubleValue() : 0);
			// 商铺租金+含税金额
			Cell cell30 = row.createCell(29, CellType.NUMERIC);
			cell30.setCellValue(
					StringUtils.isNotEmpty(o[27].toString()) ? new BigDecimal(o[27].toString()).doubleValue() : 0);
			// 单元id
			Cell cell31 = row.createCell(30, CellType.NUMERIC);
			cell31.setCellValue(Integer.parseInt(o[28].toString()));
			// 业主id
			Cell cell32 = row.createCell(31, CellType.NUMERIC);
			cell32.setCellValue(Integer.parseInt(o[29].toString()));
			i++;
		}

		File ysd = new File(fileName);

		if (!ysd.getParentFile().exists()) {
			ysd.getParentFile().mkdirs();
		}

		try {
			OutputStream os = new FileOutputStream(ysd);
			wb.write(os);
			wb.close();
			os.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}

		return fileName;
	}

	private String saveYsdChange(List<Object[]> ysdList, String startDate, String endDate) {
		String fileName = communityContextInfo.kingdeeFilePath + "/应收单减免(" + startDate + "_" + endDate + ").xlsx";
		XSSFWorkbook wb = new XSSFWorkbook();
		Sheet sheet = wb.createSheet("应收单减免");
		String[] titles = { "fid", "单据编号", "日期", "楼盘名称", "单元编号", "姓名", "应收款id", "楼阁名称", "fid", "单据编号", "收费项目", "总金额",
				"物业管理费+含税金额", "车位管理费+含税金额", "别墅管理费+含税金额", "花园管理费+含税金额", "花园及停车位管理费+含税金额", "景观大道路灯电费+含税金额",
				"公共电费分摊+含税金额", "二区车库公共电费+含税金额", "三区车库公共电费+含税金额", "空中花园公共电费+含税金额", "车库公共电费+含税金额", "露天车位管理服务费+含税金额",
				"公共水费分摊+含税金额", "车库公共水费分摊+含税金额", "宿舍物业管理费+含税金额", "宿舍租金+含税金额", "商铺物业管理费+含税金额", "商铺租金+含税金额", "违约金+含税金额",
				"单元id", "业主id", "应收日期" };
		// 表头
		Row title = sheet.createRow(0);
		for (int i = 0; i < titles.length; i++) {
			Cell tt = title.createCell(i, CellType.STRING);
			tt.setCellValue(titles[i]);
		}

		int i = 1;
		for (Object[] o : ysdList) {
			Row row = sheet.createRow(i);

			// fid
			Cell cell1 = row.createCell(0, CellType.NUMERIC);
			cell1.setCellValue(i);
			// 单据编号
			Cell cell2 = row.createCell(1, CellType.STRING);
			cell2.setCellValue(o[0].toString());
			// 日期
			Cell cell3 = row.createCell(2, CellType.STRING);
			cell3.setCellValue(o[1].toString());
			// 楼盘名称
			Cell cell4 = row.createCell(3, CellType.STRING);
			cell4.setCellValue(o[2].toString());
			// 单元编号
			Cell cell5 = row.createCell(4, CellType.STRING);
			cell5.setCellValue(o[3].toString());
			// 姓名
			Cell cell6 = row.createCell(5, CellType.STRING);
			cell6.setCellValue(o[4].toString());
			// 应收款id
			Cell cell7 = row.createCell(6, CellType.NUMERIC);
			cell7.setCellValue(o[5].toString());
			// 楼阁名称
			Cell cell8 = row.createCell(7, CellType.STRING);
			cell8.setCellValue(o[6].toString());
			// fid
			Cell cell9 = row.createCell(8, CellType.NUMERIC);
			cell9.setCellValue(i);
			// 单据编号
			Cell cell10 = row.createCell(9, CellType.STRING);
			cell10.setCellValue(o[7].toString());
			// 收费项目
			Cell cell11 = row.createCell(10, CellType.STRING);
			cell11.setCellValue(o[8].toString());
			// 总金额
			Cell cell12 = row.createCell(11, CellType.NUMERIC);
			cell12.setCellValue(new BigDecimal(o[9].toString()).doubleValue());
			// 物业管理费+含税金额
			Cell cell13 = row.createCell(12, CellType.NUMERIC);
			cell13.setCellValue(
					StringUtils.isNotEmpty(o[10].toString()) ? new BigDecimal(o[10].toString()).doubleValue() : 0);
			// 车位管理费+含税金额
			Cell cell14 = row.createCell(13, CellType.NUMERIC);
			cell14.setCellValue(
					StringUtils.isNotEmpty(o[11].toString()) ? new BigDecimal(o[11].toString()).doubleValue() : 0);
			// 别墅管理费+含税金额
			Cell cell15 = row.createCell(14, CellType.NUMERIC);
			cell15.setCellValue(
					StringUtils.isNotEmpty(o[12].toString()) ? new BigDecimal(o[12].toString()).doubleValue() : 0);
			// 花园管理费+含税金额
			Cell cell16 = row.createCell(15, CellType.NUMERIC);
			cell16.setCellValue(
					StringUtils.isNotEmpty(o[13].toString()) ? new BigDecimal(o[13].toString()).doubleValue() : 0);
			// 花园及停车位管理费+含税金额
			Cell cell17 = row.createCell(16, CellType.NUMERIC);
			cell17.setCellValue(
					StringUtils.isNotEmpty(o[14].toString()) ? new BigDecimal(o[14].toString()).doubleValue() : 0);
			// 景观大道路灯电费+含税金额
			Cell cell18 = row.createCell(17, CellType.NUMERIC);
			cell18.setCellValue(
					StringUtils.isNotEmpty(o[15].toString()) ? new BigDecimal(o[15].toString()).doubleValue() : 0);
			// 公共电费分摊+含税金额
			Cell cell19 = row.createCell(18, CellType.NUMERIC);
			cell19.setCellValue(
					StringUtils.isNotEmpty(o[16].toString()) ? new BigDecimal(o[16].toString()).doubleValue() : 0);
			// 二区车库公共电费+含税金额
			Cell cell20 = row.createCell(19, CellType.NUMERIC);
			cell20.setCellValue(
					StringUtils.isNotEmpty(o[17].toString()) ? new BigDecimal(o[17].toString()).doubleValue() : 0);
			// 三区车库公共电费+含税金额
			Cell cell21 = row.createCell(20, CellType.NUMERIC);
			cell21.setCellValue(
					StringUtils.isNotEmpty(o[18].toString()) ? new BigDecimal(o[18].toString()).doubleValue() : 0);
			// 空中花园公共电费+含税金额
			Cell cell22 = row.createCell(21, CellType.NUMERIC);
			cell22.setCellValue(
					StringUtils.isNotEmpty(o[19].toString()) ? new BigDecimal(o[19].toString()).doubleValue() : 0);
			// 车库公共电费+含税金额
			Cell cell23 = row.createCell(22, CellType.NUMERIC);
			cell23.setCellValue(
					StringUtils.isNotEmpty(o[20].toString()) ? new BigDecimal(o[20].toString()).doubleValue() : 0);
			// 露天车位管理服务费+含税金额
			Cell cell24 = row.createCell(23, CellType.NUMERIC);
			cell24.setCellValue(
					StringUtils.isNotEmpty(o[21].toString()) ? new BigDecimal(o[21].toString()).doubleValue() : 0);
			// 公共水费分摊+含税金额
			Cell cell25 = row.createCell(24, CellType.NUMERIC);
			cell25.setCellValue(
					StringUtils.isNotEmpty(o[22].toString()) ? new BigDecimal(o[22].toString()).doubleValue() : 0);
			// 车库公共水费分摊+含税金额
			Cell cell26 = row.createCell(25, CellType.NUMERIC);
			cell26.setCellValue(
					StringUtils.isNotEmpty(o[23].toString()) ? new BigDecimal(o[23].toString()).doubleValue() : 0);
			// 宿舍物业管理费+含税金额
			Cell cell27 = row.createCell(26, CellType.NUMERIC);
			cell27.setCellValue(
					StringUtils.isNotEmpty(o[24].toString()) ? new BigDecimal(o[24].toString()).doubleValue() : 0);
			// 宿舍租金+含税金额
			Cell cell28 = row.createCell(27, CellType.NUMERIC);
			cell28.setCellValue(
					StringUtils.isNotEmpty(o[25].toString()) ? new BigDecimal(o[25].toString()).doubleValue() : 0);
			// 商铺物业管理费+含税金额
			Cell cell39 = row.createCell(28, CellType.NUMERIC);
			cell39.setCellValue(
					StringUtils.isNotEmpty(o[26].toString()) ? new BigDecimal(o[26].toString()).doubleValue() : 0);
			// 商铺租金+含税金额
			Cell cell30 = row.createCell(29, CellType.NUMERIC);
			cell30.setCellValue(
					StringUtils.isNotEmpty(o[27].toString()) ? new BigDecimal(o[27].toString()).doubleValue() : 0);
			// 违约金+含税金额
			Cell cell31 = row.createCell(30, CellType.NUMERIC);
			cell31.setCellValue(
					StringUtils.isNotEmpty(o[28].toString()) ? new BigDecimal(o[28].toString()).doubleValue() : 0);

			// 单元id
			Cell cell32 = row.createCell(31, CellType.NUMERIC);
			cell32.setCellValue(Integer.parseInt(o[29].toString()));
			// 业主id
			Cell cell33 = row.createCell(32, CellType.NUMERIC);
			cell33.setCellValue(Integer.parseInt(o[30].toString()));
			// 应收日期
			Cell cell34 = row.createCell(33, CellType.STRING);
			cell34.setCellValue(o[31].toString());
			i++;
		}

		File ysd = new File(fileName);

		if (!ysd.getParentFile().exists()) {
			ysd.getParentFile().mkdirs();
		}

		try {
			OutputStream os = new FileOutputStream(ysd);
			wb.write(os);
			wb.close();
			os.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}

		return fileName;
	}

	private String saveShouju(List<Object[]> sjList, String startDate, String endDate) {
		String fileName = communityContextInfo.kingdeeFilePath + "/收据(" + startDate + "_" + endDate + ").xlsx";
		Set<String> codeList = new HashSet<>();
		XSSFWorkbook wb = new XSSFWorkbook();
		Sheet sheet = wb.createSheet("收据");

		String[] titles = { "fid", "收据编号", "收款日期", "业主", "单元", "备注", "收款方式", "科耐制单人", "区域", "现金/内部转账", "POS/微信/工行",
				"农村信用社", "支付宝", "建行", "农村信用社代收", "微信支付", "工商银行代收", "转款", "建设银行代收", "客户", "房间号", "单据编号", "农业银行代收",
				"三水农业银行", "佛山农商银行", "fid", "收据编号", "收费项目", "属期", "金额", "别墅管理费+别墅管理费项目", "别墅管理费+金额", "物业管理费+物业管理费项目",
				"物业管理费+金额", "花园管理费+花园管理费项目", "花园管理费+金额", "车位管理费+车位管理费项目", "车位管理费+金额", "施工服务费+施工服务费项目", "施工服务费+金额",
				"花园及停车位管理费+花园及停车位管理费项目", "花园及停车位管理费+金额", "景观大道路灯电费+景观大道路灯电费项目", "景观大道路灯电费+金额", "公共电费分摊+公共电费分摊项目",
				"公共电费分摊+金额", "二区车库公共电费+二区车库公共电费项目", "二区车库公共电费+金额", "三区车库公共电费+三区车库公共电费项目", "三区车库公共电费+金额",
				"空中花园公共电费+空中花园公共电费项目", "空中花园公共电费+金额", "车库公共电费+车库公共电费项目", "车库公共电费+金额", "露天车位管理服务费+露天车位管理服务费项目",
				"露天车位管理服务费+金额", "场地费+场地费项目", "场地费+金额", "出租车位管理服务费+出租车位管理服务费项目", "出租车位管理服务费+金额", "商铺物业管理费+商铺物业管理费项目",
				"商铺物业管理费+金额", "工程维修+工程维修项目", "工程维修+金额", "小区临时停车收费+小区临时停车收费项目", "小区临时停车收费+金额", "车库公共水费分摊+车库公共水费分摊项目",
				"车库公共水费分摊+金额", "日常办公费+日常办公费项目", "日常办公费+金额", "砂石等费用+砂石等费用项目", "砂石等费用+金额", "损害财产赔偿款+损害财产赔偿款项目",
				"损害财产赔偿款+含税金额", "有偿绿化服务费+有偿绿化服务费项目", "有偿绿化服务费+金额", "有偿清洁服务费+有偿清洁服务费项目", "有偿清洁服务费+不含税金额",
				"IC卡工本费+IC卡工本费项目", "IC卡工本费+金额", "社区活动收入+社区活动收入项目", "社区活动收入+金额", "证件工本费+证件工本费项目", "证件工本费+金额",
				"违约金+违约金项目", "违约金+金额", "代收车位租金+代收车位租金项目", "代收车位租金+金额", "代收代缴房屋办证费+代收代缴房屋办证费项目", "代收代缴房屋办证费+金额",
				"员工餐费+员工餐费项目", "员工餐费+金额", "代收电费+代收电费项目", "代收电费+金额", "宿舍物业管理费+宿舍物业管理费项目", "宿舍物业管理费+金额", "垃圾清运费+垃圾清运费项目",
				"垃圾清运费+金额", "代收水费+代收水费项目", "代收水费+金额", "物业管理费保证金+物业管理费保证金项目", "物业管理费保证金+金额", "重型机械进场押金+重型机械进场押金项目",
				"重型机械进场押金+金额", "租金保证金+租金保证金项目", "租金保证金+金额", "装修保证金+装修保证金项目", "装修保证金+金额", "宿舍租金+宿舍租金项目", "宿舍租金+金额",
				"公共设施保证金+公共设施保证金项目", "公共设施保证金+金额", "穿梭车车费+穿梭车车费项目", "穿梭车车费+金额", "游泳池门票+游泳池门票项目", "游泳池门票+金额",
				"水电周转保证金+水电周转保证金项目", "水电周转保证金+金额", "商铺租金+商铺租金项目", "商铺租金+金额", "公共水费分摊+公共水费分摊项目", "公共水费分摊+金额",
				"小卖部食品+小卖部食品项目", "小卖部食品+金额", "花园及停车位管理费违约金+项目", "花园及停车位管理费违约金+金额", "露天车位管理服务费违约金+项目", "露天车位管理服务费违约金+金额",
				"车位管理费违约金+项目", "车位管理费违约金+金额", "有偿服务费+有偿服务费项目", "有偿服务费+金额", "露天车位管理服务费2+项目", "露天车位管理服务费2+金额",
				"露天车位管理服务费3+项目", "露天车位管理服务费3+金额", "露天车位管理服务费4+项目", "露天车位管理服务费4+金额", "商业街临时停车费+项目", "商业街临时停车费+金额",
				"儿童公园及精灵屋门票款+项目", "儿童公园及精灵屋门票款+金额", "电费+电费项目", "电费+金额", "水费+水费项目", "水费+水费金额", "往来款+往来款项目", "往来款+往来款金额",
				"江南邻里充电桩（充电服务费）+江南邻里充电桩（充电服务费）项目","江南邻里充电桩（充电服务费）+金额",
				"江南邻里充电桩（充电费）+江南邻里充电桩（充电费）项目","江南邻里充电桩（充电费）收入+金额",
				"惠福电动车充电桩收入+惠福电动车充电桩收入项目","惠福电动车充电桩收入收入+金额",
				"惠福园区充电桩（充电服务费）+惠福园区充电桩（充电服务费）项目","惠福园区充电桩（充电服务费）+金额",
				"惠福园区充电桩（充电费）+惠福园区充电桩（充电费）项目","惠福园区充电桩（充电费）收入+金额",
				"单元id", "业主id" };

		// 表头
		Row title = sheet.createRow(0);
		for (int i = 0; i < titles.length; i++) {
			Cell tt = title.createCell(i, CellType.STRING);
			tt.setCellValue(titles[i]);
		}
		int i = 1;
		int j = 1;
		for (Object[] o : sjList) {
			Row row = sheet.createRow(i);
			if (!codeList.contains(o[24].toString())) {
				codeList.add(o[24].toString());
				// fid
				Cell cell1 = row.createCell(0, CellType.NUMERIC);
				cell1.setCellValue(j);
				j++;
			}

			// 收据编号
			Cell cell2 = row.createCell(1, CellType.STRING);
			cell2.setCellValue(o[0].toString());
			// 收款日期:
			Cell cell3 = row.createCell(2, CellType.STRING);
			cell3.setCellValue(o[1].toString());
			// 业主
			Cell cell4 = row.createCell(3, CellType.STRING);
			cell4.setCellValue(null != o[2] ? o[2].toString() : "");
			// 单元
			Cell cell5 = row.createCell(4, CellType.STRING);
			cell5.setCellValue(o[3].toString());
			// 备注
			Cell cell6 = row.createCell(5, CellType.STRING);
			cell6.setCellValue(null != o[4] ? o[4].toString() : "");
			// 收款方式
			Cell cell7 = row.createCell(6, CellType.STRING);
			cell7.setCellValue(o[5].toString());
			// 科耐制单人
			Cell cell8 = row.createCell(7, CellType.STRING);
			cell8.setCellValue(null != o[6] ? o[6].toString() : "");
			// 区域
			Cell cell9 = row.createCell(8, CellType.STRING);
			cell9.setCellValue(null != o[7] ? o[7].toString() : "");
			// 现金/内部转账
			Cell cell10 = row.createCell(9, CellType.NUMERIC);
			cell10.setCellValue(
					StringUtils.isNotEmpty(o[8].toString()) ? new BigDecimal(o[8].toString()).doubleValue() : 0);
			// POS/微信/工行：
			Cell cell11 = row.createCell(10, CellType.NUMERIC);
			cell11.setCellValue(
					StringUtils.isNotEmpty(o[9].toString()) ? new BigDecimal(o[9].toString()).doubleValue() : 0);
			// 农村信用社
			Cell cell12 = row.createCell(11, CellType.NUMERIC);
			cell12.setCellValue(
					StringUtils.isNotEmpty(o[10].toString()) ? new BigDecimal(o[10].toString()).doubleValue() : 0);
			// 支付宝
			Cell cell13 = row.createCell(12, CellType.NUMERIC);
			cell13.setCellValue(
					StringUtils.isNotEmpty(o[11].toString()) ? new BigDecimal(o[11].toString()).doubleValue() : 0);
			// 建行
			Cell cell14 = row.createCell(13, CellType.NUMERIC);
			cell14.setCellValue(
					StringUtils.isNotEmpty(o[12].toString()) ? new BigDecimal(o[12].toString()).doubleValue() : 0);
			// 农村信用社代收
			Cell cell15 = row.createCell(14, CellType.NUMERIC);
			cell15.setCellValue(
					StringUtils.isNotEmpty(o[13].toString()) ? new BigDecimal(o[13].toString()).doubleValue() : 0);
			// 微信支付
			Cell cell16 = row.createCell(15, CellType.NUMERIC);
			cell16.setCellValue(
					StringUtils.isNotEmpty(o[14].toString()) ? new BigDecimal(o[14].toString()).doubleValue() : 0);
			// 工商银行代收
			Cell cell17 = row.createCell(16, CellType.NUMERIC);
			cell17.setCellValue(
					StringUtils.isNotEmpty(o[15].toString()) ? new BigDecimal(o[15].toString()).doubleValue() : 0);
			// 转款
			Cell cell18 = row.createCell(17, CellType.NUMERIC);
			cell18.setCellValue(
					StringUtils.isNotEmpty(o[16].toString()) ? new BigDecimal(o[16].toString()).doubleValue() : 0);
			// 建设银行代收
			Cell cell19 = row.createCell(18, CellType.NUMERIC);
			cell19.setCellValue(
					StringUtils.isNotEmpty(o[17].toString()) ? new BigDecimal(o[17].toString()).doubleValue() : 0);
			// 客户
			Cell cell20 = row.createCell(19, CellType.STRING);
			cell20.setCellValue(o[18].toString());
			// 房间号
			Cell cell21 = row.createCell(20, CellType.STRING);
			cell21.setCellValue(o[19].toString());
			// 单据编号：
			Cell cell22 = row.createCell(21, CellType.STRING);
			cell22.setCellValue(o[20].toString());
			// 农业银行代收
			Cell cell23 = row.createCell(22, CellType.NUMERIC);
			cell23.setCellValue(
					StringUtils.isNotEmpty(o[21].toString()) ? new BigDecimal(o[21].toString()).doubleValue() : 0);
			// 三水农业银行
			Cell cell24 = row.createCell(23, CellType.NUMERIC);
			cell24.setCellValue(
					StringUtils.isNotEmpty(o[22].toString()) ? new BigDecimal(o[22].toString()).doubleValue() : 0);
			// 佛山农商银行
			Cell cell25 = row.createCell(24, CellType.NUMERIC);
			cell25.setCellValue(
					StringUtils.isNotEmpty(o[23].toString()) ? new BigDecimal(o[23].toString()).doubleValue() : 0);
			// 单据内码
			Cell cell26 = row.createCell(25, CellType.NUMERIC);
			cell26.setCellValue(i);
			// 收据编号
			Cell cell27 = row.createCell(26, CellType.STRING);
			cell27.setCellValue(o[24].toString());
			// 收费项目
			Cell cell28 = row.createCell(27, CellType.STRING);
			cell28.setCellValue(o[25].toString());
			// 属期
			Cell cell29 = row.createCell(28, CellType.STRING);
			cell29.setCellValue(o[26].toString());
			// 金额
			Cell cell30 = row.createCell(29, CellType.NUMERIC);
			cell30.setCellValue(
					StringUtils.isNotEmpty(o[27].toString()) ? new BigDecimal(o[27].toString()).doubleValue() : 0);
			// 别墅管理费+别墅管理费项目
			Cell cell31 = row.createCell(30, CellType.STRING);
			cell31.setCellValue(o[28].toString());
			// 别墅管理费+金额
			Cell cell32 = row.createCell(31, CellType.NUMERIC);
			cell32.setCellValue(
					StringUtils.isNotEmpty(o[29].toString()) ? new BigDecimal(o[29].toString()).doubleValue() : 0);
			// 物业管理费+物业管理费项目
			Cell cell33 = row.createCell(32, CellType.STRING);
			cell33.setCellValue(o[30].toString());
			// 物业管理费+金额
			Cell cell34 = row.createCell(33, CellType.NUMERIC);
			cell34.setCellValue(
					StringUtils.isNotEmpty(o[31].toString()) ? new BigDecimal(o[31].toString()).doubleValue() : 0);
			// 花园管理费+花园管理费项目
			Cell cell35 = row.createCell(34, CellType.STRING);
			cell35.setCellValue(o[32].toString());
			// 花园管理费+金额
			Cell cell36 = row.createCell(35, CellType.NUMERIC);
			cell36.setCellValue(
					StringUtils.isNotEmpty(o[33].toString()) ? new BigDecimal(o[33].toString()).doubleValue() : 0);
			// 车位管理费+车位管理费项目
			Cell cell37 = row.createCell(36, CellType.STRING);
			cell37.setCellValue(o[34].toString());
			// 车位管理费+金额
			Cell cell38 = row.createCell(37, CellType.NUMERIC);
			cell38.setCellValue(
					StringUtils.isNotEmpty(o[35].toString()) ? new BigDecimal(o[35].toString()).doubleValue() : 0);
			// 施工服务费+施工服务费项目
			Cell cell39 = row.createCell(38, CellType.STRING);
			cell39.setCellValue(o[36].toString());
			// 施工服务费+金额
			Cell cell40 = row.createCell(39, CellType.NUMERIC);
			cell40.setCellValue(
					StringUtils.isNotEmpty(o[37].toString()) ? new BigDecimal(o[37].toString()).doubleValue() : 0);
			// 花园及停车位管理费+花园及停车位管理费项目
			Cell cell41 = row.createCell(40, CellType.STRING);
			cell41.setCellValue(o[38].toString());
			// 花园及停车位管理费+金额
			Cell cell42 = row.createCell(41, CellType.NUMERIC);
			cell42.setCellValue(
					StringUtils.isNotEmpty(o[39].toString()) ? new BigDecimal(o[39].toString()).doubleValue() : 0);
			// 景观大道路灯电费+景观大道路灯电费项目
			Cell cell43 = row.createCell(42, CellType.STRING);
			cell43.setCellValue(o[40].toString());
			// 景观大道路灯电费+金额
			Cell cell44 = row.createCell(43, CellType.NUMERIC);
			cell44.setCellValue(
					StringUtils.isNotEmpty(o[41].toString()) ? new BigDecimal(o[41].toString()).doubleValue() : 0);
			// 公共电费分摊+公共电费分摊项目
			Cell cell45 = row.createCell(44, CellType.STRING);
			cell45.setCellValue(o[42].toString());
			// 公共电费分摊+金额
			Cell cell46 = row.createCell(45, CellType.NUMERIC);
			cell46.setCellValue(
					StringUtils.isNotEmpty(o[43].toString()) ? new BigDecimal(o[43].toString()).doubleValue() : 0);
			// 二区车库公共电费+二区车库公共电费项目
			Cell cell47 = row.createCell(46, CellType.STRING);
			cell47.setCellValue(o[44].toString());
			// 二区车库公共电费+金额
			Cell cell48 = row.createCell(47, CellType.NUMERIC);
			cell48.setCellValue(
					StringUtils.isNotEmpty(o[45].toString()) ? new BigDecimal(o[45].toString()).doubleValue() : 0);
			// 三区车库公共电费+三区车库公共电费项目
			Cell cell49 = row.createCell(48, CellType.STRING);
			cell49.setCellValue(o[46].toString());
			// 三区车库公共电费+金额
			Cell cell50 = row.createCell(49, CellType.NUMERIC);
			cell50.setCellValue(
					StringUtils.isNotEmpty(o[47].toString()) ? new BigDecimal(o[47].toString()).doubleValue() : 0);
			// 空中花园公共电费+空中花园公共电费项目
			Cell cell51 = row.createCell(50, CellType.STRING);
			cell51.setCellValue(o[48].toString());
			// 空中花园公共电费+金额
			Cell cell52 = row.createCell(51, CellType.NUMERIC);
			cell52.setCellValue(
					StringUtils.isNotEmpty(o[49].toString()) ? new BigDecimal(o[49].toString()).doubleValue() : 0);
			// 车库公共电费+车库公共电费项目
			Cell cell53 = row.createCell(52, CellType.STRING);
			cell53.setCellValue(o[50].toString());
			// 车库公共电费+金额
			Cell cell54 = row.createCell(53, CellType.NUMERIC);
			cell54.setCellValue(
					StringUtils.isNotEmpty(o[51].toString()) ? new BigDecimal(o[51].toString()).doubleValue() : 0);
			// 露天车位管理服务费+露天车位管理服务费项目
			Cell cell55 = row.createCell(54, CellType.STRING);
			cell55.setCellValue(o[52].toString());
			// 露天车位管理服务费+金额
			Cell cell56 = row.createCell(55, CellType.NUMERIC);
			cell56.setCellValue(
					StringUtils.isNotEmpty(o[53].toString()) ? new BigDecimal(o[53].toString()).doubleValue() : 0);
			// 场地费+场地费项目
			Cell cell57 = row.createCell(56, CellType.STRING);
			cell57.setCellValue(o[54].toString());
			// 场地费+金额
			Cell cell58 = row.createCell(57, CellType.NUMERIC);
			cell58.setCellValue(
					StringUtils.isNotEmpty(o[55].toString()) ? new BigDecimal(o[55].toString()).doubleValue() : 0);
			// 出租车位管理服务费+出租车位管理服务费项目
			Cell cell59 = row.createCell(58, CellType.STRING);
			cell59.setCellValue(o[56].toString());
			// 出租车位管理服务费+金额
			Cell cell60 = row.createCell(59, CellType.NUMERIC);
			cell60.setCellValue(
					StringUtils.isNotEmpty(o[57].toString()) ? new BigDecimal(o[57].toString()).doubleValue() : 0);
			// 商铺物业管理费+商铺物业管理费项目
			Cell cell61 = row.createCell(60, CellType.STRING);
			cell61.setCellValue(o[58].toString());
			// 商铺物业管理费+金额
			Cell cell62 = row.createCell(61, CellType.NUMERIC);
			cell62.setCellValue(
					StringUtils.isNotEmpty(o[59].toString()) ? new BigDecimal(o[59].toString()).doubleValue() : 0);
			// 工程维修+工程维修项目
			Cell cell63 = row.createCell(62, CellType.STRING);
			cell63.setCellValue(o[60].toString());
			// 工程维修+金额
			Cell cell64 = row.createCell(63, CellType.NUMERIC);
			cell64.setCellValue(
					StringUtils.isNotEmpty(o[61].toString()) ? new BigDecimal(o[61].toString()).doubleValue() : 0);
			// 小区临时停车收费+小区临时停车收费项目
			Cell cell65 = row.createCell(64, CellType.STRING);
			cell65.setCellValue(o[62].toString());
			// 小区临时停车收费+金额
			Cell cell66 = row.createCell(65, CellType.NUMERIC);
			cell66.setCellValue(
					StringUtils.isNotEmpty(o[63].toString()) ? new BigDecimal(o[63].toString()).doubleValue() : 0);
			// 车库公共水费分摊+车库公共水费分摊项目
			Cell cell67 = row.createCell(66, CellType.STRING);
			cell67.setCellValue(o[64].toString());
			// 车库公共水费分摊+金额
			Cell cell68 = row.createCell(67, CellType.NUMERIC);
			cell68.setCellValue(
					StringUtils.isNotEmpty(o[65].toString()) ? new BigDecimal(o[65].toString()).doubleValue() : 0);
			// 日常办公费+日常办公费项目
			Cell cell69 = row.createCell(68, CellType.STRING);
			cell69.setCellValue(o[66].toString());
			// 日常办公费+金额
			Cell cell70 = row.createCell(69, CellType.NUMERIC);
			cell70.setCellValue(
					StringUtils.isNotEmpty(o[67].toString()) ? new BigDecimal(o[67].toString()).doubleValue() : 0);
			// 砂石等费用+砂石等费用项目
			Cell cell71 = row.createCell(70, CellType.STRING);
			cell71.setCellValue(o[68].toString());
			// 砂石等费用+金额
			Cell cell72 = row.createCell(71, CellType.NUMERIC);
			cell72.setCellValue(
					StringUtils.isNotEmpty(o[69].toString()) ? new BigDecimal(o[69].toString()).doubleValue() : 0);
			// 损害财产赔偿款+损害财产赔偿款项目
			Cell cell73 = row.createCell(72, CellType.STRING);
			cell73.setCellValue(o[70].toString());
			// 损害财产赔偿款+含税金额
			Cell cell74 = row.createCell(73, CellType.NUMERIC);
			cell74.setCellValue(
					StringUtils.isNotEmpty(o[71].toString()) ? new BigDecimal(o[71].toString()).doubleValue() : 0);
			// 有偿绿化服务费+有偿绿化服务费项目
			Cell cell75 = row.createCell(74, CellType.STRING);
			cell75.setCellValue(o[72].toString());
			// 有偿绿化服务费+金额
			Cell cell76 = row.createCell(75, CellType.NUMERIC);
			cell76.setCellValue(
					StringUtils.isNotEmpty(o[73].toString()) ? new BigDecimal(o[73].toString()).doubleValue() : 0);
			// 有偿清洁服务费+有偿清洁服务费项目
			Cell cell77 = row.createCell(76, CellType.STRING);
			cell77.setCellValue(o[74].toString());
			// 有偿清洁服务费+金额
			Cell cell78 = row.createCell(77, CellType.NUMERIC);
			cell78.setCellValue(
					StringUtils.isNotEmpty(o[75].toString()) ? new BigDecimal(o[75].toString()).doubleValue() : 0);
			// IC卡工本费+IC卡工本费项目
			Cell cell79 = row.createCell(78, CellType.STRING);
			cell79.setCellValue(o[76].toString());
			// IC卡工本费+金额
			Cell cell80 = row.createCell(79, CellType.NUMERIC);
			cell80.setCellValue(
					StringUtils.isNotEmpty(o[77].toString()) ? new BigDecimal(o[77].toString()).doubleValue() : 0);
			// 社区活动收入+社区活动收入项目
			Cell cell81 = row.createCell(80, CellType.STRING);
			cell81.setCellValue(o[78].toString());
			// 社区活动收入+金额
			Cell cell82 = row.createCell(81, CellType.NUMERIC);
			cell82.setCellValue(
					StringUtils.isNotEmpty(o[79].toString()) ? new BigDecimal(o[79].toString()).doubleValue() : 0);
			// 证件工本费+证件工本费项目
			Cell cell83 = row.createCell(82, CellType.STRING);
			cell83.setCellValue(o[80].toString());
			// 证件工本费+金额
			Cell cell84 = row.createCell(83, CellType.NUMERIC);
			cell84.setCellValue(
					StringUtils.isNotEmpty(o[81].toString()) ? new BigDecimal(o[81].toString()).doubleValue() : 0);
			// 违约金+违约金项目
			Cell cell85 = row.createCell(84, CellType.STRING);
			cell85.setCellValue(o[82].toString());
			// 违约金+金额
			Cell cell86 = row.createCell(85, CellType.NUMERIC);
			cell86.setCellValue(
					StringUtils.isNotEmpty(o[83].toString()) ? new BigDecimal(o[83].toString()).doubleValue() : 0);
			// 代收车位租金+代收车位租金项目
			Cell cell87 = row.createCell(86, CellType.STRING);
			cell87.setCellValue(o[84].toString());
			// 代收车位租金+金额
			Cell cell88 = row.createCell(87, CellType.NUMERIC);
			cell88.setCellValue(
					StringUtils.isNotEmpty(o[85].toString()) ? new BigDecimal(o[85].toString()).doubleValue() : 0);
			// 代收代缴房屋办证费+代收代缴房屋办证费项目
			Cell cell89 = row.createCell(88, CellType.STRING);
			cell89.setCellValue(o[86].toString());
			// 代收代缴房屋办证费+金额
			Cell cell90 = row.createCell(89, CellType.NUMERIC);
			cell90.setCellValue(
					StringUtils.isNotEmpty(o[87].toString()) ? new BigDecimal(o[87].toString()).doubleValue() : 0);
			// 员工餐费+员工餐费项目
			Cell cell91 = row.createCell(90, CellType.STRING);
			cell91.setCellValue(o[88].toString());
			// 员工餐费+金额
			Cell cell92 = row.createCell(91, CellType.NUMERIC);
			cell92.setCellValue(
					StringUtils.isNotEmpty(o[89].toString()) ? new BigDecimal(o[89].toString()).doubleValue() : 0);
			// 代收电费+代收电费项目
			Cell cell93 = row.createCell(92, CellType.STRING);
			cell93.setCellValue(o[90].toString());
			// 代收电费+金额
			Cell cell94 = row.createCell(93, CellType.NUMERIC);
			cell94.setCellValue(
					StringUtils.isNotEmpty(o[91].toString()) ? new BigDecimal(o[91].toString()).doubleValue() : 0);
			// 宿舍物业管理费+宿舍物业管理费项目
			Cell cell95 = row.createCell(94, CellType.STRING);
			cell95.setCellValue(o[92].toString());
			// 宿舍物业管理费+金额
			Cell cell96 = row.createCell(95, CellType.NUMERIC);
			cell96.setCellValue(
					StringUtils.isNotEmpty(o[93].toString()) ? new BigDecimal(o[93].toString()).doubleValue() : 0);
			// 垃圾清运费+垃圾清运费项目
			Cell cell97 = row.createCell(96, CellType.STRING);
			cell97.setCellValue(o[94].toString());
			// 垃圾清运费+金额
			Cell cell98 = row.createCell(97, CellType.NUMERIC);
			cell98.setCellValue(
					StringUtils.isNotEmpty(o[95].toString()) ? new BigDecimal(o[95].toString()).doubleValue() : 0);
			// 代收水费+代收水费项目
			Cell cell99 = row.createCell(98, CellType.STRING);
			cell99.setCellValue(o[96].toString());
			// 代收水费+金额
			Cell cell100 = row.createCell(99, CellType.NUMERIC);
			cell100.setCellValue(
					StringUtils.isNotEmpty(o[97].toString()) ? new BigDecimal(o[97].toString()).doubleValue() : 0);
			// 物业管理费保证金+物业管理费保证金项目
			Cell cell101 = row.createCell(100, CellType.STRING);
			cell101.setCellValue(o[98].toString());
			// 物业管理费保证金+金额
			Cell cell102 = row.createCell(101, CellType.NUMERIC);
			cell102.setCellValue(
					StringUtils.isNotEmpty(o[99].toString()) ? new BigDecimal(o[99].toString()).doubleValue() : 0);
			// 重型机械进场押金+重型机械进场押金项目
			Cell cell103 = row.createCell(102, CellType.STRING);
			cell103.setCellValue(o[100].toString());
			// 重型机械进场押金+金额
			Cell cell104 = row.createCell(103, CellType.NUMERIC);
			cell104.setCellValue(
					StringUtils.isNotEmpty(o[101].toString()) ? new BigDecimal(o[101].toString()).doubleValue() : 0);
			// 租金保证金+租金保证金项目
			Cell cell105 = row.createCell(104, CellType.STRING);
			cell105.setCellValue(o[102].toString());
			// 租金保证金+金额
			Cell cell106 = row.createCell(105, CellType.NUMERIC);
			cell106.setCellValue(
					StringUtils.isNotEmpty(o[103].toString()) ? new BigDecimal(o[103].toString()).doubleValue() : 0);
			// 装修保证金+装修保证金项目
			Cell cel1107 = row.createCell(106, CellType.STRING);
			cel1107.setCellValue(o[104].toString());
			// 装修保证金+金额
			Cell cell108 = row.createCell(107, CellType.NUMERIC);
			cell108.setCellValue(
					StringUtils.isNotEmpty(o[105].toString()) ? new BigDecimal(o[105].toString()).doubleValue() : 0);
			// 宿舍租金+宿舍租金项目
			Cell cell109 = row.createCell(108, CellType.STRING);
			cell109.setCellValue(o[106].toString());
			// 宿舍租金+金额
			Cell cell110 = row.createCell(109, CellType.NUMERIC);
			cell110.setCellValue(
					StringUtils.isNotEmpty(o[107].toString()) ? new BigDecimal(o[107].toString()).doubleValue() : 0);
			// 公共设施保证金+公共设施保证金项目
			Cell cell111 = row.createCell(110, CellType.STRING);
			cell111.setCellValue(o[108].toString());
			// 公共设施保证金+金额
			Cell cell112 = row.createCell(111, CellType.NUMERIC);
			cell112.setCellValue(
					StringUtils.isNotEmpty(o[109].toString()) ? new BigDecimal(o[109].toString()).doubleValue() : 0);
			// 穿梭车车费+穿梭车车费项目
			Cell cell113 = row.createCell(112, CellType.STRING);
			cell113.setCellValue(o[110].toString());
			// 穿梭车车费+金额
			Cell cell114 = row.createCell(113, CellType.NUMERIC);
			cell114.setCellValue(
					StringUtils.isNotEmpty(o[111].toString()) ? new BigDecimal(o[111].toString()).doubleValue() : 0);
			// 游泳池门票+游泳池门票项目
			Cell cell115 = row.createCell(114, CellType.STRING);
			cell115.setCellValue(o[112].toString());
			// 游泳池门票+金额
			Cell cell116 = row.createCell(115, CellType.NUMERIC);
			cell116.setCellValue(
					StringUtils.isNotEmpty(o[113].toString()) ? new BigDecimal(o[113].toString()).doubleValue() : 0);
			// 水电周转保证金+水电周转保证金项目
			Cell cell117 = row.createCell(116, CellType.STRING);
			cell117.setCellValue(o[114].toString());
			// 水电周转保证金+金额
			Cell cell118 = row.createCell(117, CellType.NUMERIC);
			cell118.setCellValue(
					StringUtils.isNotEmpty(o[115].toString()) ? new BigDecimal(o[115].toString()).doubleValue() : 0);
			// 商铺租金+商铺租金项目
			Cell cell119 = row.createCell(118, CellType.STRING);
			cell119.setCellValue(o[116].toString());
			// 商铺租金+金额
			Cell cell120 = row.createCell(119, CellType.NUMERIC);
			cell120.setCellValue(
					StringUtils.isNotEmpty(o[117].toString()) ? new BigDecimal(o[117].toString()).doubleValue() : 0);
			// 公共水费分摊+公共水费分摊项目
			Cell cell121 = row.createCell(120, CellType.STRING);
			cell121.setCellValue(o[118].toString());
			// 公共水费分摊+金额
			Cell cell122 = row.createCell(121, CellType.NUMERIC);
			cell122.setCellValue(
					StringUtils.isNotEmpty(o[119].toString()) ? new BigDecimal(o[119].toString()).doubleValue() : 0);
			// 小卖部食品+小卖部食品项目
			Cell cell123 = row.createCell(122, CellType.STRING);
			cell123.setCellValue(o[120].toString());
			// 小卖部食品+金额
			Cell cell124 = row.createCell(123, CellType.NUMERIC);
			cell124.setCellValue(
					StringUtils.isNotEmpty(o[121].toString()) ? new BigDecimal(o[121].toString()).doubleValue() : 0);
			// 花园及停车位管理费违约金+项目
			Cell cell125 = row.createCell(124, CellType.STRING);
			cell125.setCellValue(o[122].toString());
			// 花园及停车位管理费违约金+金额
			Cell cell126 = row.createCell(125, CellType.NUMERIC);
			cell126.setCellValue(
					StringUtils.isNotEmpty(o[123].toString()) ? new BigDecimal(o[123].toString()).doubleValue() : 0);
			// 露天车位管理服务费违约金+项目
			Cell cell127 = row.createCell(126, CellType.STRING);
			cell127.setCellValue(o[124].toString());
			// 露天车位管理服务费违约金+金额
			Cell cell128 = row.createCell(127, CellType.NUMERIC);
			cell128.setCellValue(
					StringUtils.isNotEmpty(o[125].toString()) ? new BigDecimal(o[125].toString()).doubleValue() : 0);
			// 车位管理费违约金+项目
			Cell cell129 = row.createCell(128, CellType.STRING);
			cell129.setCellValue(o[126].toString());
			// 车位管理费违约金+金额
			Cell cell130 = row.createCell(129, CellType.NUMERIC);
			cell130.setCellValue(
					StringUtils.isNotEmpty(o[127].toString()) ? new BigDecimal(o[127].toString()).doubleValue() : 0);
			// 有偿服务费+有偿服务费项目
			Cell cell131 = row.createCell(130, CellType.STRING);
			cell131.setCellValue(o[128].toString());
			// 有偿服务费+金额
			Cell cell132 = row.createCell(131, CellType.NUMERIC);
			cell132.setCellValue(
					StringUtils.isNotEmpty(o[129].toString()) ? new BigDecimal(o[129].toString()).doubleValue() : 0);
			// 露天车位管理服务费2+项目
			Cell cell133 = row.createCell(132, CellType.STRING);
			cell133.setCellValue(o[130].toString());
			// 露天车位管理服务费2+含税金额
			Cell cell134 = row.createCell(133, CellType.NUMERIC);
			cell134.setCellValue(
					StringUtils.isNotEmpty(o[131].toString()) ? new BigDecimal(o[131].toString()).doubleValue() : 0);
			// 露天车位管理服务费3+项目
			Cell cell135 = row.createCell(134, CellType.STRING);
			cell135.setCellValue(o[132].toString());
			// 露天车位管理服务费3+含税金额
			Cell cell136 = row.createCell(135, CellType.NUMERIC);
			cell136.setCellValue(
					StringUtils.isNotEmpty(o[133].toString()) ? new BigDecimal(o[133].toString()).doubleValue() : 0);
			// 露天车位管理服务费4+项目
			Cell cell137 = row.createCell(136, CellType.STRING);
			cell137.setCellValue(o[134].toString());
			// 露天车位管理服务费4+含税金额
			Cell cell138 = row.createCell(137, CellType.NUMERIC);
			cell138.setCellValue(
					StringUtils.isNotEmpty(o[135].toString()) ? new BigDecimal(o[135].toString()).doubleValue() : 0);
			// 商业街临时停车费+项目
			Cell cell139 = row.createCell(138, CellType.STRING);
			cell139.setCellValue(o[136].toString());
			// 商业街临时停车费+金额
			Cell cell140 = row.createCell(139, CellType.NUMERIC);
			cell140.setCellValue(
					StringUtils.isNotEmpty(o[137].toString()) ? new BigDecimal(o[137].toString()).doubleValue() : 0);
			// 儿童公园及精灵屋门票款+项目
			Cell cell141 = row.createCell(140, CellType.STRING);
			cell141.setCellValue(o[138].toString());
			// 儿童公园及精灵屋门票款+含税金额
			Cell cell142 = row.createCell(141, CellType.NUMERIC);
			cell142.setCellValue(
					StringUtils.isNotEmpty(o[139].toString()) ? new BigDecimal(o[139].toString()).doubleValue() : 0);
			// 电费+电费项目
			Cell cell143 = row.createCell(142, CellType.STRING);
			cell143.setCellValue(o[140].toString());
			// 电费+金额
			Cell cell144 = row.createCell(143, CellType.NUMERIC);
			cell144.setCellValue(
					StringUtils.isNotEmpty(o[141].toString()) ? new BigDecimal(o[141].toString()).doubleValue() : 0);
			// 水费+水费项目
			Cell cell145 = row.createCell(144, CellType.STRING);
			cell145.setCellValue(o[142].toString());
			// 水费+水费金额
			Cell cell146 = row.createCell(145, CellType.NUMERIC);
			cell146.setCellValue(
					StringUtils.isNotEmpty(o[143].toString()) ? new BigDecimal(o[143].toString()).doubleValue() : 0);
			// 往来款+往来款项目
			Cell cell147 = row.createCell(146, CellType.STRING);
			cell147.setCellValue(o[144].toString());
			// 往来款+往来款金额
			Cell cell148 = row.createCell(147, CellType.NUMERIC);
			cell148.setCellValue(
					StringUtils.isNotEmpty(o[145].toString()) ? new BigDecimal(o[145].toString()).doubleValue() : 0);
			
			// 江南邻里充电桩（充电服务费）+江南邻里充电桩（充电服务费）项目
			Cell cell149 = row.createCell(148, CellType.STRING);
			cell149.setCellValue(o[146].toString());
			// 江南邻里充电桩（充电服务费）+金额
			Cell cell150 = row.createCell(149, CellType.NUMERIC);
			cell150.setCellValue(
					StringUtils.isNotEmpty(o[147].toString()) ? new BigDecimal(o[147].toString()).doubleValue() : 0);
			
			// 江南邻里充电桩（充电费）+江南邻里充电桩（充电费）项目
			Cell cell151 = row.createCell(150, CellType.STRING);
			cell151.setCellValue(o[148].toString());
			// 江南邻里充电桩（充电费）+金额
			Cell cell152 = row.createCell(151, CellType.NUMERIC);
			cell152.setCellValue(
					StringUtils.isNotEmpty(o[149].toString()) ? new BigDecimal(o[149].toString()).doubleValue() : 0);
			
			// 惠福电动车充电桩收入+惠福电动车充电桩收入项目
			Cell cell153 = row.createCell(152, CellType.STRING);
			cell153.setCellValue(o[150].toString());
			// 惠福电动车充电桩收入收入+金额
			Cell cell154 = row.createCell(153, CellType.NUMERIC);
			cell154.setCellValue(
					StringUtils.isNotEmpty(o[151].toString()) ? new BigDecimal(o[151].toString()).doubleValue() : 0);
			
			// 惠福园区充电桩（充电服务费）+惠福园区充电桩（充电服务费）项目
			Cell cell155 = row.createCell(154, CellType.STRING);
			cell155.setCellValue(o[152].toString());
			// 惠福园区充电桩（充电服务费）+金额
			Cell cell156 = row.createCell(155, CellType.NUMERIC);
			cell156.setCellValue(
					StringUtils.isNotEmpty(o[153].toString()) ? new BigDecimal(o[153].toString()).doubleValue() : 0);
			
			// 惠福园区充电桩（充电费）+惠福园区充电桩（充电费）项目
			Cell cell157 = row.createCell(156, CellType.STRING);
			cell157.setCellValue(o[154].toString());
			// 惠福园区充电桩（充电费）+金额
			Cell cell158 = row.createCell(157, CellType.NUMERIC);
			cell158.setCellValue(
					StringUtils.isNotEmpty(o[155].toString()) ? new BigDecimal(o[155].toString()).doubleValue() : 0);

			// 单元ID
			Cell cell159 = row.createCell(158, CellType.NUMERIC);
			cell159.setCellValue(Integer.parseInt(o[156].toString()));
			// 业主ID
			Cell cell160 = row.createCell(159, CellType.NUMERIC);
			cell160.setCellValue(Integer.parseInt(o[157].toString()));
			i++;
		}

		codeList.clear();
		File shouju = new File(fileName);

		if (!shouju.getParentFile().exists()) {
			shouju.getParentFile().mkdirs();
		}

		try {
			OutputStream os = new FileOutputStream(shouju);
			wb.write(os);
			wb.close();
			os.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return fileName;
	}

	private String saveBaseData(Map<String, List<Object[]>> baseDataMap, String startDate, String endDate) {
		String fileName = communityContextInfo.kingdeeFilePath + "/基础数据(" + startDate + "_" + endDate + ").xlsx";
		XSSFWorkbook wb = new XSSFWorkbook();

		baseDataMap.keySet().forEach(b -> {
			if (b.equals("楼盘")) {
				Sheet sheet = wb.createSheet("楼盘");
				String[] titles = { "fitemid", "楼盘编号", "楼盘名称" };
				// 表头
				Row title = sheet.createRow(0);
				for (int i = 0; i < titles.length; i++) {
					Cell tt = title.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
				}

				int i = 1;
				for (Object[] o : baseDataMap.get(b)) {
					Row row = sheet.createRow(i);

					// fitemid
					Cell cell1 = row.createCell(0, CellType.NUMERIC);
					cell1.setCellValue(i);
					// 楼盘编号
					Cell cell2 = row.createCell(1, CellType.STRING);
					cell2.setCellValue(o[0].toString());
					// 楼盘名称
					Cell cell3 = row.createCell(2, CellType.STRING);
					cell3.setCellValue(o[1].toString());

					i++;
				}
			} else if (b.equals("楼阁")) {
				Sheet sheet = wb.createSheet("楼阁");
				String[] titles = { "fitemid", "楼阁编号", "楼阁名称" };
				// 表头
				Row title = sheet.createRow(0);
				for (int i = 0; i < titles.length; i++) {
					Cell tt = title.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
				}

				int i = 1;
				for (Object[] o : baseDataMap.get(b)) {
					Row row = sheet.createRow(i);

					// fitemid
					Cell cell1 = row.createCell(0, CellType.NUMERIC);
					cell1.setCellValue(i);
					// 楼阁编号
					Cell cell2 = row.createCell(1, CellType.STRING);
					cell2.setCellValue(o[0].toString());
					// 楼阁名称
					Cell cell3 = row.createCell(2, CellType.STRING);
					cell3.setCellValue(o[1].toString());

					i++;
				}
			} else if (b.equals("单元")) {
				Sheet sheet = wb.createSheet("单元");
				String[] titles = { "fitemid", "单元编号", "单元名称", "单元ID", "楼阁ID", "单元面积" };
				// 表头
				Row title = sheet.createRow(0);
				for (int i = 0; i < titles.length; i++) {
					Cell tt = title.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
				}

				int i = 1;
				for (Object[] o : baseDataMap.get(b)) {
					Row row = sheet.createRow(i);

					// fitemid
					Cell cell1 = row.createCell(0, CellType.NUMERIC);
					cell1.setCellValue(i);
					// 单元编号
					Cell cell2 = row.createCell(1, CellType.STRING);
					cell2.setCellValue(i + "");
					// 单元名称
					Cell cell3 = row.createCell(2, CellType.STRING);
					cell3.setCellValue(o[0].toString());
					// 单元ID
					Cell cell4 = row.createCell(3, CellType.STRING);
					cell4.setCellValue(o[1].toString());
					// 楼阁ID
					Cell cell5 = row.createCell(4, CellType.STRING);
					cell5.setCellValue(o[2].toString());
					// 单元面积
					Cell cell6 = row.createCell(5, CellType.STRING);
					cell6.setCellValue(o[3].toString());

					i++;
				}

			} else if (b.equals("业主")) {
				Sheet sheet = wb.createSheet("业主");
				String[] titles = { "fitemid", "业主编号", "业主姓名", "业主id", "单元id", "楼盘id", "楼阁id" };
				// 表头
				Row title = sheet.createRow(0);
				for (int i = 0; i < titles.length; i++) {
					Cell tt = title.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
				}

				int i = 1;
				for (Object[] o : baseDataMap.get(b)) {
					Row row = sheet.createRow(i);
					// fitemid
					Cell cell1 = row.createCell(0, CellType.NUMERIC);
					cell1.setCellValue(i);
					// 业主编号
					Cell cell2 = row.createCell(1, CellType.STRING);
					cell2.setCellValue(o[0].toString());
					// 业主姓名
					Cell cell3 = row.createCell(2, CellType.STRING);
					cell3.setCellValue(o[1].toString());
					// 业主id
					Cell cell4 = row.createCell(3, CellType.STRING);
					cell4.setCellValue(o[2].toString());
					// 单元id
					Cell cell5 = row.createCell(4, CellType.STRING);
					cell5.setCellValue(o[3].toString());
					// 楼盘id
					Cell cell6 = row.createCell(5, CellType.STRING);
					cell6.setCellValue(o[4].toString());
					// 楼阁id
					Cell cell7 = row.createCell(6, CellType.STRING);
					cell7.setCellValue(o[5].toString());

					i++;
				}
			} else {
				Sheet sheet = wb.createSheet("收费项目");
				String[] titles = { "fitemid", "收费项目编号", "收费项目名称" };
				// 表头
				Row title = sheet.createRow(0);
				for (int i = 0; i < titles.length; i++) {
					Cell tt = title.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
				}
				int i = 1;
				for (Object[] o : baseDataMap.get(b)) {
					Row row = sheet.createRow(i);
					// fitemid
					Cell cell1 = row.createCell(0, CellType.NUMERIC);
					cell1.setCellValue(i);
					// 收费项目编号
					Cell cell2 = row.createCell(1, CellType.STRING);
					cell2.setCellValue(o[0].toString());
					// 收费项目名称
					Cell cell3 = row.createCell(2, CellType.STRING);
					cell3.setCellValue(o[1].toString());
					i++;
				}
			}

		});

		File payitemsName = new File(fileName);

		if (!payitemsName.getParentFile().exists()) {
			payitemsName.getParentFile().mkdirs();
		}

		try {
			OutputStream os = new FileOutputStream(payitemsName);
			wb.write(os);
			wb.close();
			os.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}

		return fileName;
	}

	@SuppressWarnings("unchecked")
	private Boolean sendYsd(String fileName) {
		try {
			Map<String, String> para = new HashMap<>();
			para.put("fileName", fileName);
			String resStr = HttpClientUtil.jsonPost(communityContextInfo.getKingdeeUrl() + "/addYsd", "UTF-8",
					mapper.writeValueAsString(para), null);
			Map<String, Object> res = mapper.readValue(resStr, HashMap.class);
			if (res.get("ret").equals("0000")) {
				return true;
			} else {
				return false;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			return false;
		}
	}

	@SuppressWarnings("unchecked")
	private Boolean sendYsdChange(String fileName) {
		try {
			Map<String, String> para = new HashMap<>();
			para.put("fileName", fileName);
			String resStr = HttpClientUtil.jsonPost(communityContextInfo.getKingdeeUrl() + "/addYsdChange", "UTF-8",
					mapper.writeValueAsString(para), null);
			Map<String, Object> res = mapper.readValue(resStr, HashMap.class);
			if (res.get("ret").equals("0000")) {
				return true;
			} else {
				return false;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			return false;
		}
	}

	@SuppressWarnings("unchecked")
	private Boolean sendShouju(String fileName) {
		try {
			Map<String, String> para = new HashMap<>();
			para.put("fileName", fileName);
			String resStr = HttpClientUtil.jsonPost(communityContextInfo.getKingdeeUrl() + "/addShouju", "UTF-8",
					mapper.writeValueAsString(para), null);
			Map<String, Object> res = mapper.readValue(resStr, HashMap.class);
			if (res.get("ret").equals("0000")) {
				return true;
			} else {
				return false;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			return false;
		}
	}

	@SuppressWarnings("unchecked")
	private Boolean sendBaseData(String fileName) {
		try {
			Map<String, String> para = new HashMap<>();
			para.put("fileName", fileName);
			String resStr = HttpClientUtil.jsonPost(communityContextInfo.getKingdeeUrl() + "/addBaseData", "UTF-8",
					mapper.writeValueAsString(para), null);
			Map<String, Object> res = mapper.readValue(resStr, HashMap.class);
			if (res.get("ret").equals("0000")) {
				return true;
			} else {
				return false;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			return false;
		}
	}

	@SuppressWarnings("unchecked")
	private LinkedList<Object[]> queryYsdList(String startDate, String endDate) {
		StringBuilder sql = new StringBuilder(
				"SELECT aa.receivablesno AS fbillno,aa.receivabledate AS fdate,aa.districtname AS flpmc,aa.unitcode AS fdybh,"
						+ "aa.username AS fyzxm,aa.receivablesid AS ftext4,aa.buildingname AS ftext1,aa.receivablesno AS fbillno1,aa.payitemsname AS ftext5,"
						+ "aa.receivableamount AS famount36,"
						+ "IF(aa.payitemsname='物业管理费',aa.receivableamount,'') AS 'fAmount',"
						+ "IF(aa.payitemsname='车位管理费',aa.receivableamount,'') AS 'fAmount3',"
						+ "IF(aa.payitemsname='别墅管理费',aa.receivableamount,'') AS 'fAmount6',"
						+ "IF(aa.payitemsname='花园管理费',aa.receivableamount,'') AS 'fAmount9',"
						+ "IF(aa.payitemsname='花园及停车位管理费',aa.receivableamount,'') AS 'fAmount12',"
						+ "IF(aa.payitemsname='景观大道路灯电费',aa.receivableamount,'') AS 'fAmount15',"
						+ "IF(aa.payitemsname='公共电费分摊',aa.receivableamount,'') AS 'fAmount18',"
						+ "IF(aa.payitemsname='二区车库公共电费',aa.receivableamount,'') AS 'fAmount21',"
						+ "IF(aa.payitemsname='三区车库公共电费',aa.receivableamount,'') AS 'fAmount24',"
						+ "IF(aa.payitemsname='空中花园公共电费',aa.receivableamount,'') AS 'fAmount27',"
						+ "IF(aa.payitemsname='车库公共电费',aa.receivableamount,'') AS 'fAmount30',"
						+ "IF(aa.payitemsname='露天车位管理服务费',aa.receivableamount,'') AS 'fAmount33',"
						+ "IF(aa.payitemsname='公共水费分摊',aa.receivableamount,'') AS 'fAmount38',"
						+ "IF(aa.payitemsname='车库公共水费分摊',aa.receivableamount,'') AS 'fAmount41',"
						+ "IF(aa.payitemsname='宿舍物业管理费',aa.receivableamount,'') AS 'fAmount44',"
						+ "IF(aa.payitemsname='宿舍租金',aa.receivableamount,'') AS 'fAmount47',"
						+ "IF(aa.payitemsname='商铺物业管理费',aa.receivableamount,'') AS 'fAmount50',"
						+ "IF(aa.payitemsname='商铺租金',aa.receivableamount,'') AS 'fAmount53',aa.id as fbase2,aa.memberid as fbase3 "
						+ "FROM v_yingshou aa WHERE aa.receivableamount<>0"
						+ " AND ((aa.chargecategory!='分摊费' and aa.receivabledate>='" + startDate + "' "
						+ "AND aa.receivabledate<='" + endDate + "') "
						+ "or (aa.chargecategory='分摊费' and aa.receivabledate>=date_add('" + startDate
						+ "',INTERVAL -1 MONTH) " + "AND receivabledate<=LAST_DAY('" + endDate
						+ "'- INTERVAL 1 MONTH))) " + "AND aa.receivablesno IS NOT NULL"
						+ " ORDER BY aa.districtname,aa.buildingname,aa.unitcode,aa.receivabledate,aa.payitemsname");

		LinkedList<Object[]> result = new LinkedList<>(communityReceivablesDao.createSQLQuery(sql.toString()).list());
		return result;
	}

	@SuppressWarnings("unchecked")
	private LinkedList<Object[]> queryYsdChangeList(String startDate, String endDate) {
		StringBuilder sql = new StringBuilder(
				"SELECT aa.receivablesno AS fbillno,aa.changedate AS fdate,aa.districtname AS flpmc,aa.unitcode AS fdybh,"
						+ "aa.username AS fyzxm,aa.receivablesid AS ftext4,aa.buildingname AS ftext1,aa.receivablesno AS fbillno1,aa.payitemsname AS ftext5,"
						+ "aa.changeamount AS famount36,"
						+ "IF(aa.payitemsname='物业管理费',aa.changeamount,'') AS 'fAmount',"
						+ "IF(aa.payitemsname='车位管理费',aa.changeamount,'') AS 'fAmount3',"
						+ "IF(aa.payitemsname='别墅管理费',aa.changeamount,'') AS 'fAmount6',"
						+ "IF(aa.payitemsname='花园管理费',aa.changeamount,'') AS 'fAmount9',"
						+ "IF(aa.payitemsname='花园及停车位管理费',aa.changeamount,'') AS 'fAmount12',"
						+ "IF(aa.payitemsname='景观大道路灯电费',aa.changeamount,'') AS 'fAmount15',"
						+ "IF(aa.payitemsname='公共电费分摊',aa.changeamount,'') AS 'fAmount18',"
						+ "IF(aa.payitemsname='二区车库公共电费',aa.changeamount,'') AS 'fAmount21',"
						+ "IF(aa.payitemsname='三区车库公共电费',aa.changeamount,'') AS 'fAmount24',"
						+ "IF(aa.payitemsname='空中花园公共电费',aa.changeamount,'') AS 'fAmount27',"
						+ "IF(aa.payitemsname='车库公共电费',aa.changeamount,'') AS 'fAmount30',"
						+ "IF(aa.payitemsname='露天车位管理服务费',aa.changeamount,'') AS 'fAmount33',"
						+ "IF(aa.payitemsname='公共水费分摊',aa.changeamount,'') AS 'fAmount38',"
						+ "IF(aa.payitemsname='车库公共水费分摊',aa.changeamount,'') AS 'fAmount41',"
						+ "IF(aa.payitemsname='宿舍物业管理费',aa.changeamount,'') AS 'fAmount44',"
						+ "IF(aa.payitemsname='宿舍租金',aa.changeamount,'') AS 'fAmount47',"
						+ "IF(aa.payitemsname='商铺物业管理费',aa.changeamount,'') AS 'fAmount50',"
						+ "IF(aa.payitemsname='商铺租金',aa.changeamount,'') AS 'fAmount53',"
						+ "IF(aa.payitemsname LIKE \'%违约金%\',aa.changeamount,'') AS 'famount152',aa.id as fbase2,aa.memberid as fbase3,aa.receivabledate  "
						+ "FROM (SELECT a.receivablesno,DATE_FORMAT(b.changedate,\'%Y-%m-%d\') AS changedate,a.districtname,a.unitcode,"
						+ "a.username,a.receivablesid,a.buildingname,a.payitemsname,b.changeamount,a.id,a.memberid,a.chargecategory,a.receivabledate "
						+ "FROM (SELECT aa.receivablesno,aa.receivabledate,aa.districtname,aa.unitcode,bb.username,aa.receivablesid,aa.buildingname,"
						+ "aa.payitemsname,aa.receivableamount,aa.id,bb.memberid,aa.chargecategory "
						+ "FROM(SELECT c.id,d.receivablesno,DATE_FORMAT(d.receivabledate,\'%Y-%m-%d\') AS receivabledate,a.districtname,c.unitcode,"
						+ "IF(d.payitemsname IN('露天车位管理服务费','露天车位管理服务费2','露天车位管理服务费3','露天车位管理服务费4') ,'露天车位管理服务费',d.payitemsname) AS payitemsname,"
						+ "d.receivableamount,d.id AS receivablesid,b.buildingname,d.chargecategory "
						+ "FROM t_community_district a INNER JOIN t_community_building b ON a.id=b.districtid "
						+ "INNER JOIN t_community_property c ON b.id=c.buildingid "
						+ "INNER JOIN t_community_receivables d ON c.id=d.estateid "
						+ "INNER JOIN t_community_pay_items e ON e.id=d.payItemId AND ((e.paydate=32 AND e.isreceivables=1) OR e.id=15)) aa "
						+ "INNER JOIN (SELECT a.id,c.id AS memberid,c.username,a.estatestate,b.billingdate,IFNULL(b.terminationdate,'2099-12-31 00:00:00') AS terminationdate "
						+ "FROM t_community_property a,t_community_member_property b,t_account c "
						+ "WHERE a.id=b.propertyid AND b.memberid=c.id) bb "
						+ "ON aa.id=bb.id AND aa.receivabledate>=bb.billingdate AND (aa.receivabledate<=bb.terminationdate OR "
						+ "(aa.receivabledate>bb.terminationdate AND YEAR(aa.receivabledate)=YEAR(bb.terminationdate) AND MONTH(aa.receivabledate)=MONTH(bb.terminationdate))) "
						+ ") a INNER JOIN t_community_receivables_changes b " + "ON a.receivablesid=b.receivablesid  AND b.state=1 "
						+ ") aa WHERE aa.changedate>='" + startDate + "' AND changedate<='" + endDate
						+ "' and aa.receivablesno IS NOT NULL"
						+ " ORDER BY aa.districtname,aa.buildingname,aa.unitcode,aa.changedate,aa.payitemsname");

		LinkedList<Object[]> result = new LinkedList<>(communityReceivablesDao.createSQLQuery(sql.toString()).list());
		return result;
	}

	@SuppressWarnings("unchecked")
	private LinkedList<Object[]> queryShoujuList(String startDate, String endDate) {
		LinkedList<Object[]> result = new LinkedList<>();
		StringBuilder sql = new StringBuilder(
				"SELECT aa.code1 AS code1,aa.receiptdate AS 收款日期,aa.payername AS 业主,aa.unitcode AS 单元,aa.comment AS 备注,"
						+ "aa.paymentMethod AS 收款方式,aa.agent AS 制单人,aa.address AS 区域,"
						+ "IF(aa.paymentmethod='现金' OR aa.paymentmethod='内部转帐',aa.totalamount,'') AS '现金/内部转账',"
						+ "IF(aa.paymentmethod='POS通' OR aa.paymentmethod='POS机' OR aa.paymentmethod='微信' OR aa.paymentmethod='工行' OR aa.paymentmethod='工商银行' OR (aa.paymentmethod='银行代收' AND aa.agent='工商银行'),aa.totalamount,'') AS 'POS/微信/工行',"
						+ "IF(aa.paymentmethod='农村信用合作社' OR (aa.paymentmethod='银行代收' AND aa.agent='农村信用合作社'),aa.totalamount,'') AS '农村信用社',"
						+ "IF(aa.paymentmethod='支付宝' or aa.paymentmethod='支付宝小程序支付',aa.totalamount,'') AS '支付宝',"
						+ "IF(aa.paymentmethod='建设银行' OR (aa.paymentmethod='银行代收' AND aa.agent='建设银行'),aa.totalamount,'') AS '建行',"
						+ "IF(aa.paymentmethod='农村信用合作社' OR (aa.paymentmethod='银行代收' AND aa.agent='农村信用合作社'),aa.totalamount,'') AS '农村信用社代收',"
						+ "IF(aa.paymentmethod='微信支付',aa.totalamount,'') AS '微信支付',"
						+ "IF(aa.paymentmethod='工商银行' OR aa.paymentmethod='工行' OR (aa.paymentmethod='银行代收' AND aa.agent='工商银行'),aa.totalamount,'') AS '工商银行代收',"
						+ "IF(aa.paymentmethod='转账' OR aa.paymentmethod='转帐',aa.totalamount,'') AS '转款',"
						+ "IF(aa.paymentmethod='建设银行' OR (aa.paymentmethod='银行代收' AND aa.agent='建设银行'),aa.totalamount,'') AS '建设银行代收',"
						+ "aa.payername AS 客户," + "aa.roomnumber AS 房间号," + "aa.code1 AS 单据编号,"
						+ "IF(aa.paymentmethod='银行代收' AND aa.agent='农业银行',aa.totalamount,'') AS '农业银行代收',"
						+ "IF(aa.paymentmethod='三水农业银行',aa.totalamount,'') AS '三水农业银行',"
						+ "IF(aa.paymentmethod='佛山农商银行' OR (aa.paymentmethod='银行代收' AND aa.agent='佛山农商银行'),aa.totalamount,'') AS '佛山农商银行',"
						+ "aa.code1 AS 收据编号,aa.payitemsname,aa.paymentperiod,aa.totalamount,"
						+ "IF(aa.payitemsname='别墅管理费',aa.payitemsname,'') AS '别墅管理费+别墅管理费项目',"
						+ "IF(aa.payitemsname='别墅管理费',aa.totalAmount,'') AS '别墅管理费+金额',"
						+ "IF(aa.payitemsname='物业管理费',aa.payitemsname,'') AS '物业管理费+物业管理费项目',"
						+ "IF(aa.payitemsname='物业管理费',aa.totalAmount,'') AS '物业管理费+金额',"
						+ "IF(aa.payitemsname='花园管理费',aa.payitemsname,'') AS '花园管理费+花园管理费项目',"
						+ "IF(aa.payitemsname='花园管理费',aa.totalAmount,'') AS '花园管理费+金额',"
						+ "IF(aa.payitemsname='车位管理费',aa.payitemsname,'') AS '车位管理费+车位管理费项目',"
						+ "IF(aa.payitemsname='车位管理费',aa.totalAmount,'') AS '车位管理费+金额',"
						+ "IF(aa.payitemsname='施工服务费',aa.payitemsname,'') AS '施工服务费+施工服务费项目',"
						+ "IF(aa.payitemsname='施工服务费',aa.totalAmount,'') AS '施工服务费+金额',"
						+ "IF(aa.payitemsname='花园及停车位管理费',aa.payitemsname,'') AS '花园及停车位管理费+花园及停车位管理费项目',"
						+ "IF(aa.payitemsname='花园及停车位管理费',aa.totalAmount,'') AS '花园及停车位管理费+金额',"
						+ "IF(aa.payitemsname='景观大道路灯电费',aa.payitemsname,'') AS '景观大道路灯电费+景观大道路灯电费项目',"
						+ "IF(aa.payitemsname='景观大道路灯电费',aa.totalAmount,'') AS '景观大道路灯电费+金额',"
						+ "IF(aa.payitemsname='公共电费分摊',aa.payitemsname,'') AS '公共电费分摊+公共电费分摊项目',"
						+ "IF(aa.payitemsname='公共电费分摊',aa.totalAmount,'') AS '公共电费分摊+金额',"
						+ "IF(aa.payitemsname='二区车库公共电费',aa.payitemsname,'') AS '二区车库公共电费+二区车库公共电费项目',"
						+ "IF(aa.payitemsname='二区车库公共电费',aa.totalAmount,'') AS '二区车库公共电费+金额',"
						+ "IF(aa.payitemsname='三区车库公共电费',aa.payitemsname,'') AS '三区车库公共电费+三区车库公共电费项目',"
						+ "IF(aa.payitemsname='三区车库公共电费',aa.totalAmount,'') AS '三区车库公共电费+金额',"
						+ "IF(aa.payitemsname='空中花园公共电费',aa.payitemsname,'') AS '空中花园公共电费+空中花园公共电费项目',"
						+ "IF(aa.payitemsname='空中花园公共电费',aa.totalAmount,'') AS '空中花园公共电费+金额',"
						+ "IF(aa.payitemsname='车库公共电费',aa.payitemsname,'') AS '车库公共电费+车库公共电费项目',"
						+ "IF(aa.payitemsname='车库公共电费',aa.totalAmount,'') AS '车库公共电费+金额',"
						+ "IF(aa.payitemsname='露天车位管理服务费',aa.payitemsname,'') AS '露天车位管理服务费+露天车位管理服务费项目',"
						+ "IF(aa.payitemsname='露天车位管理服务费',aa.totalAmount,'') AS '露天车位管理服务费+金额',"
						+ "IF(aa.payitemsname='场地费',aa.payitemsname,'') AS '场地费+场地费项目',"
						+ "IF(aa.payitemsname='场地费',aa.totalAmount,'') AS '场地费+金额',"
						+ "IF(aa.payitemsname='出租车位管理服务费',aa.payitemsname,'') AS '出租车位管理服务费+出租车位管理服务费项目',"
						+ "IF(aa.payitemsname='出租车位管理服务费',aa.totalAmount,'') AS '出租车位管理服务费+金额',"
						+ "IF(aa.payitemsname='商铺物业管理费',aa.payitemsname,'') AS '商铺物业管理费+商铺物业管理费项目',"
						+ "IF(aa.payitemsname='商铺物业管理费',aa.totalAmount,'') AS '商铺物业管理费+金额',"
						+ "IF(aa.payitemsname='工程维修',aa.payitemsname,'') AS '工程维修+工程维修项目',"
						+ "IF(aa.payitemsname='工程维修',aa.totalAmount,'') AS '工程维修+金额',"
						+ "IF(aa.payitemsname='小区临时停车收费',aa.payitemsname,'') AS '小区临时停车收费+小区临时停车收费项目',"
						+ "IF(aa.payitemsname='小区临时停车收费',aa.totalAmount,'') AS '小区临时停车收费+金额',"
						+ "IF(aa.payitemsname='车库公共水费分摊',aa.payitemsname,'') AS '车库公共水费分摊+车库公共水费分摊项目',"
						+ "IF(aa.payitemsname='车库公共水费分摊',aa.totalAmount,'') AS '车库公共水费分摊+金额',"
						+ "IF(aa.payitemsname='日常办公费',aa.payitemsname,'') AS '日常办公费+日常办公费项目',"
						+ "IF(aa.payitemsname='日常办公费',aa.totalAmount,'') AS '日常办公费+金额',"
						+ "IF(aa.payitemsname='砂石等费用',aa.payitemsname,'') AS '砂石等费用+砂石等费用项目',"
						+ "IF(aa.payitemsname='砂石等费用',aa.totalAmount,'') AS '砂石等费用+金额',"
						+ "IF(aa.payitemsname='损害财产赔偿款',aa.payitemsname,'') AS '损害财产赔偿款+损害财产赔偿款项目',"
						+ "IF(aa.payitemsname='损害财产赔偿款',aa.totalAmount,'') AS '损害财产赔偿款+金额',"
						+ "IF(aa.payitemsname='有偿绿化服务费',aa.payitemsname,'') AS '有偿绿化服务费+有偿绿化服务费项目',"
						+ "IF(aa.payitemsname='有偿绿化服务费',aa.totalAmount,'') AS '有偿绿化服务费+金额',"
						+ "IF(aa.payitemsname='有偿清洁服务费',aa.payitemsname,'') AS '有偿清洁服务费+有偿清洁服务费项目',"
						+ "IF(aa.payitemsname='有偿清洁服务费',aa.totalAmount,'') AS '有偿清洁服务费+金额',"
						+ "IF(aa.payitemsname='IC卡工本费',aa.payitemsname,'') AS 'IC卡工本费+IC卡工本费项目',"
						+ "IF(aa.payitemsname='IC卡工本费',aa.totalAmount,'') AS 'IC卡工本费+金额',"
						+ "IF(aa.payitemsname='社区活动收入',aa.payitemsname,'') AS '社区活动收入+社区活动收入项目',"
						+ "IF(aa.payitemsname='社区活动收入',aa.totalAmount,'') AS '社区活动收入+金额',"
						+ "IF(aa.payitemsname='证件工本费',aa.payitemsname,'') AS '证件工本费+证件工本费项目',"
						+ "IF(aa.payitemsname='证件工本费',aa.totalAmount,'') AS '证件工本费+金额',"
						+ "IF(aa.payitemsname='违约金',aa.payitemsname,'') AS '违约金+违约金项目',"
						+ "IF(aa.payitemsname='违约金',aa.totalAmount,'') AS '违约金+金额',"
						+ "IF(aa.payitemsname='代收车位租金',aa.payitemsname,'') AS '代收车位租金+代收车位租金项目',"
						+ "IF(aa.payitemsname='代收车位租金',aa.totalAmount,'') AS '代收车位租金+金额',"
						+ "IF(aa.payitemsname='代收代缴房屋办证费',aa.payitemsname,'') AS '代收代缴房屋办证费+代收代缴房屋办证费项目',"
						+ "IF(aa.payitemsname='代收代缴房屋办证费',aa.totalAmount,'') AS '代收代缴房屋办证费+金额',"
						+ "IF(aa.payitemsname='员工餐费',aa.payitemsname,'') AS '员工餐费+员工餐费项目',"
						+ "IF(aa.payitemsname='员工餐费',aa.totalAmount,'') AS '员工餐费+金额',"
						+ "IF(aa.payitemsname='代收电费',aa.payitemsname,'') AS '代收电费+代收电费项目',"
						+ "IF(aa.payitemsname='代收电费',aa.totalAmount,'') AS '代收电费+金额',"
						+ "IF(aa.payitemsname='宿舍物业管理费',aa.payitemsname,'') AS '宿舍物业管理费+宿舍物业管理费项目',"
						+ "IF(aa.payitemsname='宿舍物业管理费',aa.totalAmount,'') AS '宿舍物业管理费+金额',"
						+ "IF(aa.payitemsname='垃圾清运费',aa.payitemsname,'') AS '垃圾清运费+垃圾清运费项目',"
						+ "IF(aa.payitemsname='垃圾清运费',aa.totalAmount,'') AS '垃圾清运费+金额',"
						+ "IF(aa.payitemsname='代收水费',aa.payitemsname,'') AS '代收水费+代收水费项目',"
						+ "IF(aa.payitemsname='代收水费',aa.totalAmount,'') AS '代收水费+金额',"
						+ "IF(aa.payitemsname='物业管理费保证金',aa.payitemsname,'') AS '物业管理费保证金+物业管理费保证金项目',"
						+ "IF(aa.payitemsname='物业管理费保证金',aa.totalAmount,'') AS '物业管理费保证金+金额',"
						+ "IF(aa.payitemsname='重型机械进场押金',aa.payitemsname,'') AS '重型机械进场押金+重型机械进场押金项目',"
						+ "IF(aa.payitemsname='重型机械进场押金',aa.totalAmount,'') AS '重型机械进场押金+金额',"
						+ "IF(aa.payitemsname='租金保证金',aa.payitemsname,'') AS '租金保证金+租金保证金项目',"
						+ "IF(aa.payitemsname='租金保证金',aa.totalAmount,'') AS '租金保证金+金额',"
						+ "IF(aa.payitemsname='装修保证金',aa.payitemsname,'') AS '装修保证金+装修保证金项目',"
						+ "IF(aa.payitemsname='装修保证金',aa.totalAmount,'') AS '装修保证金+金额',"
						+ "IF(aa.payitemsname='宿舍租金',aa.payitemsname,'') AS '宿舍租金+宿舍租金项目',"
						+ "IF(aa.payitemsname='宿舍租金',aa.totalAmount,'') AS '宿舍租金+金额',"
						+ "IF(aa.payitemsname='公共设施保证金',aa.payitemsname,'') AS '公共设施保证金+公共设施保证金项目',"
						+ "IF(aa.payitemsname='公共设施保证金',aa.totalAmount,'') AS '公共设施保证金+金额',"
						+ "IF(aa.payitemsname='穿梭车车费',aa.payitemsname,'') AS '穿梭车车费+穿梭车车费项目',"
						+ "IF(aa.payitemsname='穿梭车车费',aa.totalAmount,'') AS '穿梭车车费+金额',"
						+ "IF(aa.payitemsname='游泳池门票',aa.payitemsname,'') AS '游泳池门票+游泳池门票项目',"
						+ "IF(aa.payitemsname='游泳池门票',aa.totalAmount,'') AS '游泳池门票+金额',"
						+ "IF(aa.payitemsname='水电周转保证金',aa.payitemsname,'') AS '水电周转保证金+游泳池门票项目',"
						+ "IF(aa.payitemsname='水电周转保证金',aa.totalAmount,'') AS '水电周转保证金+金额',"
						+ "IF(aa.payitemsname='商铺租金',aa.payitemsname,'') AS '商铺租金+商铺租金项目',"
						+ "IF(aa.payitemsname='商铺租金',aa.totalAmount,'') AS '商铺租金+金额',"
						+ "IF(aa.payitemsname='公共水费分摊',aa.payitemsname,'') AS '公共水费分摊+公共水费分摊项目',"
						+ "IF(aa.payitemsname='公共水费分摊',aa.totalAmount,'') AS '公共水费分摊+金额',"
						+ "IF(aa.payitemsname='小卖部食品',aa.payitemsname,'') AS '小卖部食品+小卖部食品项目',"
						+ "IF(aa.payitemsname='小卖部食品',aa.totalAmount,'') AS '小卖部食品+金额',"
						+ "IF(aa.payitemsname='花园及停车位管理费违约金',aa.payitemsname,'') AS '花园及停车位管理费违约金+花园及停车位管理费违约金项目',"
						+ "IF(aa.payitemsname='花园及停车位管理费违约金',aa.totalAmount,'') AS '花园及停车位管理费违约金+金额',"
						+ "IF(aa.payitemsname='露天车位管理服务费违约金',aa.payitemsname,'') AS '露天车位管理服务费违约金+露天车位管理服务费违约金项目',"
						+ "IF(aa.payitemsname='露天车位管理服务费违约金',aa.totalAmount,'') AS '露天车位管理服务费违约金+金额',"
						+ "IF(aa.payitemsname='车位管理费违约金',aa.payitemsname,'') AS '车位管理费违约金+车位管理费违约金项目',"
						+ "IF(aa.payitemsname='车位管理费违约金',aa.totalAmount,'') AS '车位管理费违约金+金额',"
						+ "IF(aa.payitemsname='有偿服务费',aa.payitemsname,'') AS '有偿服务费+有偿服务费项目',"
						+ "IF(aa.payitemsname='有偿服务费',aa.totalAmount,'') AS '有偿服务费+金额',"
						+ "IF(aa.payitemsname='露天车位管理服务费2',aa.payitemsname,'') AS '露天车位管理服务费2+露天车位管理服务费2项目',"
						+ "IF(aa.payitemsname='露天车位管理服务费2',aa.totalAmount,'') AS '露天车位管理服务费2+金额',"
						+ "IF(aa.payitemsname='露天车位管理服务费3',aa.payitemsname,'') AS '露天车位管理服务费3+露天车位管理服务费3项目',"
						+ "IF(aa.payitemsname='露天车位管理服务费3',aa.totalAmount,'') AS '露天车位管理服务费3+金额',"
						+ "IF(aa.payitemsname='露天车位管理服务费4',aa.payitemsname,'') AS '露天车位管理服务费4+露天车位管理服务费4项目',"
						+ "IF(aa.payitemsname='露天车位管理服务费4',aa.totalAmount,'') AS '露天车位管理服务费4+金额',"
						+ "IF(aa.payitemsname='商业街临时停车费',aa.payitemsname,'') AS '商业街临时停车费+商业街临时停车费项目',"
						+ "IF(aa.payitemsname='商业街临时停车费',aa.totalAmount,'') AS '商业街临时停车费+金额',"
						+ "IF(aa.payitemsname='儿童公园及精灵屋门票款',aa.payitemsname,'') AS '儿童公园及精灵屋门票款+儿童公园及精灵屋门票款项目',"
						+ "IF(aa.payitemsname='儿童公园及精灵屋门票款',aa.totalAmount,'') AS '儿童公园及精灵屋门票款+金额',"
						+ "IF(aa.payitemsname='电费',aa.payitemsname,'') AS '电费+电费项目',"
						+ "IF(aa.payitemsname='电费',aa.totalAmount,'') AS '电费+金额',"
						+ "IF(aa.payitemsname='水费',aa.payitemsname,'') AS '水费+水费项目',"
						+ "IF(aa.payitemsname='水费',aa.totalAmount,'') AS '水费+金额',"
						+ "IF(aa.payitemsname='往来款',aa.payitemsname,'') AS '往来款+往来款项目',"
						+ "IF(aa.payitemsname='往来款',aa.totalAmount,'') AS '往来款+金额', "
						+"IF(aa.payitemsname='江南邻里充电桩（充电服务费）',aa.payitemsname,'') AS '江南邻里充电桩（充电服务费）+江南邻里充电桩（充电服务费）项目', "
						+"IF(aa.payitemsname='江南邻里充电桩（充电服务费）',aa.totalAmount,'') AS '江南邻里充电桩（充电服务费）+金额', "
						+"IF(aa.payitemsname='江南邻里充电桩（充电费）',aa.payitemsname,'') AS '江南邻里充电桩（充电费）+江南邻里充电桩（充电费）项目', "
						+"IF(aa.payitemsname='江南邻里充电桩（充电费）',aa.totalAmount,'') AS '江南邻里充电桩（充电费）+金额', "
						+"IF(aa.payitemsname='惠福电动车充电桩收入',aa.payitemsname,'') AS '惠福电动车充电桩收入+惠福电动车充电桩收入项目', "
						+"IF(aa.payitemsname='惠福电动车充电桩收入',aa.totalAmount,'') AS '惠福电动车充电桩收入+金额', "
						+"IF(aa.payitemsname='惠福园区充电桩（充电服务费）',aa.payitemsname,'') AS '惠福园区充电桩（充电服务费）+惠福园区充电桩（充电服务费）项目', "
						+"IF(aa.payitemsname='惠福园区充电桩（充电服务费）',aa.totalAmount,'') AS '惠福园区充电桩（充电服务费）+金额', "
						+"IF(aa.payitemsname='惠福园区充电桩（充电费）',aa.payitemsname,'') AS '惠福园区充电桩（充电费）+惠福园区充电桩（充电费）项目', "
						+"IF(aa.payitemsname='惠福园区充电桩（充电费）',aa.totalAmount,'') AS '惠福园区充电桩（充电费）+金额', "
						+" aa.estateid,aa.memberid "
						+ "FROM v_shouju aa WHERE aa.totalamount<>0 AND aa.receiptdate>='" + startDate
						+ "' AND aa.receiptdate<='" + endDate + "' "
						+ "ORDER BY aa.receiptdate,aa.code1,aa.payitemsname");

		LinkedList<Object[]> query = new LinkedList<>(communityReceivablesDao.createSQLQuery(sql.toString()).list());

		// 构造返回数据结构
		Set<String> codeSet = new HashSet<>();

		for (Object[] o : query) {
			Object[] t = new Object[o.length];
			Arrays.fill(t, "");
			if (!codeSet.contains(o[0].toString())) {
				codeSet.add(o[0].toString());
				System.arraycopy(o, 0, t, 0, o.length);
			} else {
				System.arraycopy(o, 24, t, 24, o.length - 24);
			}
			result.add(t);
		}
		codeSet.clear();
		return result;
	}

	@SuppressWarnings("unchecked")
	private Map<String, List<Object[]>> queryBaseDataMap(String startDate, String endDate) {
		Map<String, List<Object[]>> baseDataMap = new HashMap<>();

		// 获取楼盘列表
		LinkedList<Object[]> districtList = new LinkedList<>(communityDistrictDao.createSQLQuery(
				"select a.districtCode,a.districtName from t_community_district a where a.state=1 and a.createTime>='"
						+ startDate + " 00:00:00' and a.createTime<='" + endDate + " 23:59:59' ")
				.list());
		if (districtList.size() > 0) {
			baseDataMap.put("楼盘", districtList);
		}

		// 获取楼阁列表
		LinkedList<Object[]> buildingList = new LinkedList<>(communityBuildingDao.createSQLQuery(
				"SELECT a.buildingCode,a.buildingName FROM t_community_building a WHERE a.state=1 and a.createtime>='"
						+ startDate + " 00:00:00' and a.createTime<='" + endDate + " 23:59:59'")
				.list());
		if (buildingList.size() > 0) {
			baseDataMap.put("楼阁", buildingList);
		}

		// 获取单元列表
		LinkedList<Object[]> estateList = new LinkedList<>(communityEstateDao.createSQLQuery(
				"SELECT a.unitCode,a.id,a.buildingid,a.buildingarea FROM t_community_property a WHERE a.state=1 and a.createTime>='"
						+ startDate + " 00:00:00' and a.createTime<='" + endDate
						+ " 23:59:59' and a.propertyType='ESTATE'")
				.list());
		if (estateList.size() > 0) {
			baseDataMap.put("单元", estateList);
		}

		// 获取业主列表
		LinkedList<Object[]> memberList = new LinkedList<>(communityEstateDao
				.createSQLQuery("SELECT a.memberid,b.username,b.id,a.propertyid,d.districtid,c.buildingid "
						+ "FROM t_community_member_property a,t_account b,t_community_property c,t_community_building d "
						+ "WHERE a.memberid=b.id AND a.propertyid=c.id AND c.buildingid=d.id "
						+ "AND a.iscurrentowner=1 AND a.createtime>='" + startDate + " 00:00:00' and a.createTime<='"
						+ endDate + " 23:59:59'")
				.list());
		if (memberList.size() > 0) {
			baseDataMap.put("业主", memberList);
		}

		// 获取收费项目
		LinkedList<Object[]> itemsNameList = new LinkedList<>(communityEstateDao.createSQLQuery(
				"SELECT a.id,a.itemsName FROM t_community_pay_items a where a.state=1 and a.createTime>='" + startDate
						+ " 00:00:00' and a.createTime<='" + endDate + " 23:59:59'")
				.list());
		if (itemsNameList.size() > 0) {
			// 保存收费项目文件
			baseDataMap.put("收费项目", itemsNameList);
		}

		return baseDataMap;
	}

}