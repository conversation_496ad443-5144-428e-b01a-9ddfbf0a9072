package com.foshan.service.community.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.foshan.dao.generic.Page;
import com.foshan.entity.PlatformUserEntity;
import com.foshan.entity.community.CommunityBuildingEntity;
import com.foshan.entity.community.CommunityDistrictEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityMemberPropertyEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityPropertyEntity;
import com.foshan.entity.community.CommunityReceiptReceivablesEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.community.vo.EstateVo;
import com.foshan.entity.community.vo.ProprietorInfoVo;
import com.foshan.entity.community.vo.ReservedFieldVo;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityBuilding;
import com.foshan.form.community.CommunityDistrict;
import com.foshan.form.community.CommunityEstateForm;
import com.foshan.form.community.CommunityEstateTreeForm;
import com.foshan.form.community.CommunityEstateUnitCodeForm;
import com.foshan.form.community.CommunityMemberForm;
import com.foshan.form.community.CommunityMemberPropertyForm;
import com.foshan.form.community.CommunityPaymentAccountForm;
import com.foshan.form.community.CommunityPropertyForm;
import com.foshan.form.community.OperationDiaryForm;
import com.foshan.form.community.ProprietorInfoForm;
import com.foshan.form.community.request.CommunityBuildingReq;
import com.foshan.form.community.request.CommunityEstateReq;
import com.foshan.form.community.request.CommunityProprietorInfoReq;
import com.foshan.form.community.request.CommunityReceivablesReq;
import com.foshan.form.community.request.ExportExcelReq;
import com.foshan.form.community.request.ImportExcelReq;
import com.foshan.form.community.response.communityDistrict.GetCommunityDistrictListRes;
import com.foshan.form.community.response.communityEstate.GetCommunityEstateInfoRes;
import com.foshan.form.community.response.communityEstate.GetCommunityEstateListRes;
import com.foshan.form.community.response.communityEstate.GetCommunityEstateTreeRes;
import com.foshan.form.community.response.communityEstate.GetCommunityEstateUnitCodeListRes;
import com.foshan.form.community.response.communityProprietorInfo.GetProprietorInfoListRes;
import com.foshan.form.community.response.communityProprietorInfo.GetProprietorInfoRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityEstateService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.DigestUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.HttpClientUtil;
import com.foshan.util.SpringHandler;
import com.foshan.util.community.CommunityCache;

@Transactional
@Service("communityEstateService")
public class CommunityEstateServiceImpl extends GenericCommunityService implements ICommunityEstateService {

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getCommunityEstateList(CommunityEstateReq req) {
		GetCommunityEstateListRes res = new GetCommunityEstateListRes();
		Page<CommunityEstateEntity> page = new Page<CommunityEstateEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		Object userObj = getPrincipal(true);

		String hql = queryCondition(req, userObj);
		page = communityEstateDao.queryPage(page, "select distinct a " + hql);

		EstateVo estateVo = estateVoDao.getUniqueByHql("select new com.foshan.entity."
				+ "community.vo.EstateVo(SUM(a.buildingArea) as buildingAreaTotal )" + hql, "");
		res.setBuildingAreaTotal(null != estateVo.getBuildingAreaTotal()
				? estateVo.getBuildingAreaTotal().setScale(2, RoundingMode.HALF_UP).toString()
				: "0");
		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Map<String, String> bankNoMap = null;
		try {
			bankNoMap = mapper.readValue(communityContextInfo.bankNo, Map.class);
		} catch (JsonMappingException e1) {
			e1.printStackTrace();
		} catch (JsonProcessingException e1) {
			e1.printStackTrace();
		}
		for (CommunityEstateEntity o : page.getResultList()) {
			CommunityEstateForm communityEstateForm = new CommunityEstateForm();
			if (null == req.getIsAcceptanceReceive()
					|| null != req.getIsAcceptanceReceive() && req.getIsAcceptanceReceive() != 1) {
				for (CommunityMemberPropertyEntity memberProperty : o.getMemberPropertyList()) {
					CommunityMemberPropertyForm memberProperForm = new CommunityMemberPropertyForm();
					if (null != memberProperty.getMember()) {
						CommunityMemberForm memberForm = new CommunityMemberForm();
						memberForm.setMemberId(memberProperty.getMember().getId());
						memberForm.setHomePhone(memberProperty.getMember().getHomePhone());
						memberForm.setNickName(memberProperty.getMember().getNickName());
						memberForm.setPhone(memberProperty.getMember().getPhone());
						memberForm.setRegistName(memberProperty.getMember().getRegistName());
						memberForm.setSex(memberProperty.getMember().getSex());
						memberForm.setSmartcardId(memberProperty.getMember().getSmartcardId());
						memberForm.setUserName(StringUtils.isNotEmpty(memberProperty.getMember().getUserName())
								? memberProperty.getMember().getUserName()
								: "");
						memberProperForm.setMemberForm(memberForm);
					}

					memberProperForm.setMemberType(memberProperty.getMemberType());
					memberProperForm.setAuditState(memberProperty.getAuditState());
					memberProperForm.setIsCurrentMember(memberProperty.getIsCurrentMember());
					memberProperForm.setBillingDate(null != memberProperty.getBillingDate()
							? DateUtil.formatLongFormat(memberProperty.getBillingDate())
							: "");
					memberProperForm.setCreateTime(
							null != o.getCreateTime() ? DateUtil.formatLongFormat(o.getCreateTime()) : "");
					memberProperForm.setIsCurrentOwner(memberProperty.getIsCurrentOwner());
					memberProperForm.setRecordDate(null != memberProperty.getRecordDate()
							? DateUtil.formatLongFormat(memberProperty.getRecordDate())
							: "");
					memberProperForm.setTerminationDate(null != memberProperty.getTerminationDate()
							? DateUtil.formatLongFormat(memberProperty.getTerminationDate())
							: "");
//					memberProperForm.setAcceptanceDate(null != memberProperty.getAcceptanceDate()
//							? DateUtil.formatLongFormat(memberProperty.getAcceptanceDate())
//							: "");
					memberProperForm.setMemberPropertyId(memberProperty.getId());
					memberProperForm.setEndDate(
							null != memberProperty.getEndDate() ? DateUtil.formatLongFormat(memberProperty.getEndDate())
									: "");
					memberProperForm.setCarInfo(memberProperty.getCarInfo());
					memberProperForm.setComment(memberProperty.getComment());
					communityEstateForm.getMemberPropertyList().add(memberProperForm);
					if (null != memberProperty.getParentProperty()) {
						CommunityPropertyForm parentPropertyForm = new CommunityPropertyForm();

						parentPropertyForm.setPropertyId(memberProperty.getParentProperty().getId());
						parentPropertyForm.setPropertyName(memberProperty.getParentProperty().getPropertyName());
						if (memberProperty.getParentProperty() instanceof CommunityEstateEntity) {
							CommunityEstateEntity property = (CommunityEstateEntity) memberProperty.getParentProperty();
							parentPropertyForm.setUnitCode(property.getUnitCode());
						}
						memberProperForm.setParentPropertyForm(parentPropertyForm);
					}
				}
				CommunityPaymentAccountForm communityPaymentAccountForm = new CommunityPaymentAccountForm();
				if (null != o.getPaymentAccount()) {
					communityPaymentAccountForm.setCommunityPaymentAccountId(o.getPaymentAccount().getId());
					communityPaymentAccountForm.setCreateTime(null != o.getPaymentAccount().getCreateTime()
							? DateUtil.formatLongFormat(o.getPaymentAccount().getCreateTime())
							: "");
					communityPaymentAccountForm.setLastModifyTime(null != o.getPaymentAccount().getLastModifyTime()
							? DateUtil.formatLongFormat(o.getPaymentAccount().getLastModifyTime())
							: "");
					communityPaymentAccountForm.setState(o.getPaymentAccount().getState());
					communityPaymentAccountForm.setBankAccount(o.getPaymentAccount().getBankAccount());
					communityPaymentAccountForm.setBankName(o.getPaymentAccount().getBankName());
					communityPaymentAccountForm.setAccountName(o.getPaymentAccount().getAccountName());
					communityPaymentAccountForm.setIdNumber(o.getPaymentAccount().getIdNumber());
					communityPaymentAccountForm.setBankNo(bankNoMap.containsKey(o.getPaymentAccount().getBankName())
							? bankNoMap.get(o.getPaymentAccount().getBankName())
							: "");
				}
				communityEstateForm.setPaymentAccountForm(communityPaymentAccountForm);

			}
			communityEstateForm.setCreateTime(o.getCreateTime() != null ? sdf.format(o.getCreateTime()) : "");
			communityEstateForm
					.setLastModifyTime(o.getLastModifyTime() != null ? sdf.format(o.getLastModifyTime()) : "");
			communityEstateForm.setPropertyName(o.getPropertyName());
			communityEstateForm.setEstateState(o.getEstateState());
			communityEstateForm.setIsAcceptanceReceive(o.getIsAcceptanceReceive());
			communityEstateForm.setUsageTerm(o.getUsageTerm());
			communityEstateForm.setOrientation(o.getOrientation());

			communityEstateForm.setFloor(o.getFloor());
			communityEstateForm.setRoomNumber(o.getRoomNumber());
			communityEstateForm.setUsableArea(null != o.getUsableArea() ? o.getUsableArea().toString() : "");
			communityEstateForm.setChargingArea(null != o.getChargingArea() ? o.getChargingArea().toString() : "");
			communityEstateForm
					.setAdditionalArea(null != o.getAdditionalArea() ? o.getAdditionalArea().toString() : "");
			communityEstateForm.setDecoration(o.getDecoration());
			communityEstateForm.setEstateType(o.getEstateType());
			communityEstateForm.setAcceptanceDate(
					null != o.getAcceptanceDate() ? DateUtil.formatLongFormat(o.getAcceptanceDate()) : "");
			communityEstateForm.setComment(o.getComment());
			if (null != o.getBuilding()) {
				CommunityBuilding communityBuildingForm = new CommunityBuilding();
				communityBuildingForm.setCommunityBuildingId(o.getBuilding().getId());
				communityBuildingForm.setAddress(o.getBuilding().getAddress());
				communityBuildingForm.setBuildingCode(o.getBuilding().getBuildingCode());
				communityBuildingForm.setBuildingName(o.getBuilding().getBuildingName());
				communityBuildingForm.setBuildingPermitNum(o.getBuilding().getBuildingPermitNum());
				communityBuildingForm.setBuildingType(o.getBuilding().getBuildingType());
				communityEstateForm.setBuildingForm(communityBuildingForm);
			}
			if (null != o.getAsset()) {
				communityEstateForm.setAssetForm(getAsset(o.getAsset()));
			}
			communityEstateForm.setReservedField(o.getReservedField());
			communityEstateForm.setEstateId(o.getId());
			communityEstateForm.setUnitCode(o.getUnitCode());
			communityEstateForm.setBuildingArea(null != o.getBuildingArea() ? o.getBuildingArea().toString() : "");
			communityEstateForm.setRentState(o.getRentState());
			communityEstateForm.setSpecialAllocationFlag(o.getSpecialAllocationFlag());
			communityEstateForm.setReasonArrears(StringUtils.isNotEmpty(o.getReasonArrears()) ? o.getReasonArrears():"");
			res.getEstateList().add(communityEstateForm);
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	public String queryCondition(CommunityEstateReq req, Object userObj) {
		StringBuilder hql = new StringBuilder(
				"from CommunityEstateEntity a  " + "inner join a.building c inner join c.district d ");
		if (StringUtils.isNotEmpty(req.getParentUnitCode())
				|| (null == req.getIsVacancy() || (null != req.getIsVacancy() && req.getIsVacancy() != 1))
						&& (null != req.getMemberId() || null != req.getMemberType()
								|| StringUtils.isNotEmpty(req.getPhone()) || StringUtils.isNotEmpty(req.getUserName())
								|| StringUtils.isNotEmpty(req.getHomePhone())
								|| StringUtils.isNotEmpty(req.getStartTime())
								|| StringUtils.isNotEmpty(req.getEndTime())
								|| StringUtils.isNotEmpty(req.getMemberTypeList()) || null != req.getIsCurrentMember()
								|| null != req.getIsCurrentOwner() || StringUtils.isNotEmpty(req.getIdCard())
								|| StringUtils.isNotEmpty(req.getContactPerson())
								|| StringUtils.isNotEmpty(req.getEmergencyContact()))
				|| (null != req.getIsBinding() && req.getIsBinding() == 1)
				|| StringUtils.isNotEmpty(req.getHomePhone())) {
			hql.append("inner join a.memberPropertyList b inner join b.member e ")
					.append(StringUtils.isNotEmpty(req.getParentUnitCode()) ? " inner join b.parentProperty  h " : "");
		}

		if (StringUtils.isNotEmpty(req.getMeterIdList())) {
			hql.append("inner join a.meterList f ");
		}
		if (StringUtils.isNotEmpty(req.getPayItemsIdList())) {
			hql.append("inner join a.payItemsList g ");
		}

		if ((null == req.getIsAcceptanceReceive()
				|| null != req.getIsAcceptanceReceive() && req.getIsAcceptanceReceive() != 1)
				&& ((null != userObj && userObj instanceof CommunityMemberEntity) || null != req.getMemberId())) {
			if (null != req.getIsBinding() && req.getIsBinding() == 1) {
				if (null == req.getMemberId()) {
					CommunityMemberEntity member = (CommunityMemberEntity) userObj;
					req.setMemberId(member.getId());
				}
				hql.append("where b.member.id=" + req.getMemberId())
						.append(null != req.getState() ? " and a.state=" + req.getState()
								: " and a.state=" + EntityContext.RECORD_STATE_VALID);
			} else {
				hql.append("where ")
						.append(null != req.getState() ? "  a.state=" + req.getState()
								: "  a.state=" + EntityContext.RECORD_STATE_VALID)
						.append(" and a.id not in(select b.property.id from CommunityMemberPropertyEntity b where b.member.id="
								+ req.getMemberId() + " )");
			}
		} else if (StringUtils.isNotEmpty(req.getMeterIdList())) {
			hql.append(" inner join a.meterList m where m.id in(" + req.getMeterIdList() + ")");
		} else {
			hql.append(" where ").append(null != req.getState() ? "  a.state=" + req.getState()
					: "  a.state=" + EntityContext.RECORD_STATE_VALID);
		}
		hql.append(StringUtils.isNotEmpty(req.getUserName()) ? " and b.member.userName like '%" + req.getUserName() + "%'"
						: "")
		.append(StringUtils.isNotEmpty(req.getHomePhone()) ? " and b.member.homePhone like '%" + req.getHomePhone() + "%'"
						: "");
		hql.append(null != req.getMemberType() ? " and b.member.memberType = " + req.getMemberType() : "");
		hql.append(StringUtils.isNotEmpty(req.getPropertyName())
				? " and a.propertyName like'%" + req.getPropertyName() + "%'"
				: "").append(null != req.getBuildingId() ? " and c.id=" + req.getBuildingId() : 
					(StringUtils.isNotEmpty(req.getBuildingIdList()) ? " and c.id in("+req.getBuildingIdList()+")":""))
				.append(StringUtils.isNotEmpty(req.getUnitCode()) ? " and a.unitCode like'%" + req.getUnitCode() + "%'"
						: "")
				.append(StringUtils.isNotEmpty(req.getParentUnitCode())
						? " and h.unitCode like'%" + req.getParentUnitCode() + "%'"
						: "")
				.append(null != req.getEstateState() ? " and a.estateState=" + req.getEstateState() : "")
				.append(null != req.getDistrictdId() ? " and d.id=" + req.getDistrictdId() : "")
				.append(null != req.getBuildingType() ? " and c.buildingType= " + req.getBuildingType() : "")
				.append(StringUtils.isNotEmpty(req.getBuildingTypeList())
						? " and c.buildingType in(" + req.getBuildingTypeList() + ")"
						: "")
				.append(StringUtils.isNotEmpty(req.getEstateType())
						? " and a.estateType like'%" + req.getEstateType() + "%'"
						: "")
				.append(StringUtils.isNotEmpty(req.getRoomNumber()) ? " and a.roomNumber='" + req.getRoomNumber() + "'"
						: "")
				.append(StringUtils.isNotEmpty(req.getMemberTypeList())
						? " and b.memberType in(" + req.getMemberTypeList() + ")"
						: "")
				.append(null != req.getDistrictId() ? " and d.id=" + req.getDistrictId() : "")
				.append(null != req.getIsCurrentMember() ? " and b.isCurrentMember=" + req.getIsCurrentMember() : "")
				.append(null != req.getIsCurrentOwner() ? " and b.isCurrentOwner=" + req.getIsCurrentOwner() : "")
				.append(StringUtils.isNotEmpty(req.getIdCard()) ? " and e.idCard like'%" + req.getIdCard() + "%'" : "")
				.append(StringUtils.isNotEmpty(req.getPreciseUnitCode())
						? " and a.unitCode ='" + req.getPreciseUnitCode() + "'"
						: "")
				.append(StringUtils.isNotEmpty(req.getPhone()) ? " and e.phone like'%" + req.getPhone() + "%'" : "")
				.append(StringUtils.isNotEmpty(req.getHomePhone())
						? " and e.homePhone like'%" + req.getHomePhone() + "%'"
						: "")
				.append(StringUtils.isNotEmpty(req.getContactPerson())
						? " and e.contactPerson like'%" + req.getContactPerson() + "%'"
						: "")
				.append(StringUtils.isNotEmpty(req.getEmergencyContact())
						? " and e.emergencyContact like'%" + req.getEmergencyContact() + "%'"
						: "")
				.append(null != req.getFloor() ? " and a.floor=" + req.getFloor() : "")
				.append(null != req.getAccountContractNoIsNotNull() && req.getAccountContractNoIsNotNull() == 1
						? " and (a.reservedField not like'%\"accountContractNo\":\"\"%') "
								+ "and a.reservedField like'%accountCustomerId%'"
						: "")
				.append(null != req.getIsSendMessage()
						? " and (IFNULL (REPLACE (JSON_EXTRACT (a.reservedField,'$.isSendMessage'),'\"',''),1))="
								+ req.getIsSendMessage()
						: "")
				.append(StringUtils.isNotEmpty(req.getMeterIdList()) ? " and f.id in(" + req.getMeterIdList() + ")"
						: "")
				.append(StringUtils.isNotEmpty(req.getPayItemsIdList())
						? " and g.id in(" + req.getPayItemsIdList() + ")"
						: "")
				// .append(null!=req.getRentState() ? " and a.rentState="+req.getRentState() :
				// "")
				.append(StringUtils.isNotEmpty(req.getRentStateList())
						? " and  a.rentState in(" + req.getRentStateList() + ")"
						: "")
				.append(StringUtils.isNotEmpty(req.getSpecialAllocationFlagList()) ? 
						" and a.specialAllocationFlag in("+req.getSpecialAllocationFlagList()+")":"");
		if (null != req.getIsVacancy() && req.getIsVacancy() == 1) {
//			hql.append(" and (a.id not in(select property.id from CommunityMemberPropertyEntity) "
//					+ "or (a.id  in(SELECT mp.property.id FROM CommunityMemberPropertyEntity mp WHERE mp.isCurrentMember=0 ")
//					.append(StringUtils.isNotEmpty(req.getStartTime())
//							? " and mp.billingDate >='" + req.getStartTime() + "'"
//							: "")
//					.append(StringUtils.isNotEmpty(req.getEndTime()) ? " and mp.endDate<='" + req.getEndTime() + "'"
//							: "");
//			hql.append("AND mp.id NOT "
//					+ "IN(SELECT mp2.id FROM CommunityMemberPropertyEntity mp2 WHERE mp2.isCurrentMember=1 ))))");
			hql.append(
					" and a.id not in(SELECT mp.property.id FROM CommunityMemberPropertyEntity mp WHERE mp.isCurrentMember=1 and mp.memberType=0 )");
		} else {
			hql.append(StringUtils.isNotEmpty(req.getStartTime()) ? " and b.billingDate >='" + req.getStartTime() + "'"
					: "")
					.append(StringUtils.isNotEmpty(req.getEndTime()) ? " and b.endDate<='" + req.getEndTime() + "'"
							: "");
		}
		hql.append(" ORDER BY c.buildingOrder asc,a.floor asc, a.roomNumber asc");
		return hql.toString();
	}

	public IResponse getCommunityEstateTree(CommunityEstateReq req) {
		GetCommunityEstateTreeRes res = new GetCommunityEstateTreeRes();

		/*
		 * if(null == req.getBuildingId()) {
		 * res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
		 * res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO); return res; }
		 */
		Object userObj = getPrincipal(true);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityEstateEntity a  ");

		if (null != userObj && userObj instanceof CommunityMemberEntity) {
			CommunityMemberEntity member = (CommunityMemberEntity) userObj;
			hql.append(" inner join a.memberList b where b.id=" + member.getId())
					.append(null != req.getState() ? " and a.state=" + req.getState()
							: " and a.state=" + EntityContext.RECORD_STATE_VALID);
		} else {
			hql.append(" where ").append(null != req.getState() ? "  a.state=" + req.getState()
					: "  a.state=" + EntityContext.RECORD_STATE_VALID);
		}
		hql.append(StringUtils.isNotEmpty(req.getPropertyName())
				? " and a.propertyName like'%" + req.getPropertyName() + "%'"
				: "").append(null != req.getBuildingId() ? " and a.building.id=" + req.getBuildingId() : "")
				.append(StringUtils.isNotEmpty(req.getUnitCode()) ? " and a.unitCode like'%" + req.getUnitCode() + "%'"
						: "")
				.append(null != req.getEstateState() ? " a.estateState()=" + req.getEstateState() : "")
				.append(null != req.getEstateType() ? " and a.estateType=" + req.getEstateType() : "");

		hql.append(" ORDER BY FLOOR,roomNumber asc");
		List<CommunityEstateEntity> list = communityEstateDao.getListByHql(hql.toString(), "");

		Map<Integer, Integer> map = new HashMap<Integer, Integer>();
		if (null != list) {
			int i = 0;
			for (CommunityEstateEntity o : list) {
				CommunityEstateTreeForm tree = null;
				if (map.containsKey(o.getFloor())) {
					tree = res.getEstateTree().get(map.get(o.getFloor()));
				} else {
					tree = new CommunityEstateTreeForm();
					tree.setLabel("第" + o.getFloor() + "层");
					tree.setValue(o.getFloor());
					map.put(o.getFloor(), i);
					res.getEstateTree().add(i, tree);
					i++;
				}
				CommunityEstateForm communityEstateForm = new CommunityEstateForm();
				communityEstateForm.setEstateId(o.getId());
				communityEstateForm.setPropertyName(o.getPropertyName());
				communityEstateForm.setLabel(o.getUnitCode());
				communityEstateForm.setFloor(o.getFloor());
				communityEstateForm.setEstateType(o.getEstateType());
				communityEstateForm.setRoomNumber(o.getRoomNumber());
				communityEstateForm.setEstateState(o.getEstateState());

				tree.getChildren().add(communityEstateForm);
			}
		}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@CacheEvict(cacheNames = "unitCodeListCache", allEntries = true, beforeInvocation = false, condition = "#result.ret eq '0000'")
	@Audit(operate = "新增单元")
	public IResponse addCommunityEstate(CommunityEstateReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getBuildingId()) {
			CommunityBuildingEntity building = communityBuildingDao.get(req.getBuildingId());

			if (null == building) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			CommunityEstateEntity parentProperty = null;
			if (null != req.getParentPropertyId()) {
				parentProperty = communityEstateDao.get(req.getParentPropertyId());
				if (null == parentProperty) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
			}
			CommunityEstateEntity estate = new CommunityEstateEntity();

			estate.setState(EntityContext.RECORD_STATE_VALID);
			estate.setBuilding(building);
			estate.setRentState(req.getRentState());

			estate.setPropertyName(StringUtils.isNotEmpty(req.getPropertyName()) ? req.getPropertyName() : "");
			estate.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment() : "");
			estate.setEstateState(null != req.getEstateState() ? req.getEstateState() : null);
			estate.setIsAcceptanceReceive(null != req.getIsAcceptanceReceive() ? req.getIsAcceptanceReceive() : null);
			estate.setBuildingArea(StringUtils.isNotEmpty(req.getBuildingArea()) ? new BigDecimal(req.getBuildingArea())
					: new BigDecimal(0));
			estate.setUsageTerm(StringUtils.isNotEmpty(req.getUsageTerm()) ? req.getUsageTerm() : "");
			estate.setUnitCode(StringUtils.isNotEmpty(req.getUnitCode()) ? req.getUnitCode() : "");
			estate.setOrientation(StringUtils.isNotEmpty(req.getOrientation()) ? req.getOrientation() : "");
			estate.setFloor(req.getFloor());
			estate.setRoomNumber(StringUtils.isNotEmpty(req.getRoomNumber()) ? req.getRoomNumber() : "");
			estate.setUsableArea(StringUtils.isNotEmpty(req.getUsableArea()) ? new BigDecimal(req.getUsableArea())
					: new BigDecimal(0));
			estate.setChargingArea(StringUtils.isNotEmpty(req.getChargingArea()) ? new BigDecimal(req.getChargingArea())
					: new BigDecimal(0));
			estate.setAdditionalArea(
					StringUtils.isNotEmpty(req.getAdditionalArea()) ? new BigDecimal(req.getAdditionalArea())
							: new BigDecimal(0));
			estate.setSpecialFeeFlag(null != req.getSpecialFeeFlag() ? req.getSpecialFeeFlag() : 0);
			estate.setSpecialAllocationFlag(
					null != req.getSpecialAllocationFlag() ? req.getSpecialAllocationFlag() : 0);
			estate.setDecoration(StringUtils.isNotEmpty(req.getDecoration()) ? req.getDecoration() : "");
			estate.setAsset(null != req.getAssetId() ? assetDao.get(req.getAssetId()) : null);
			estate.setEstateType(req.getEstateType());
			estate.setSpecialAllocationFlag(null!=req.getSpecialAllocationFlag() ? 
					req.getSpecialAllocationFlag() : 0);

			ReservedFieldVo reservedField = null;
			if (StringUtils.isNotEmpty(req.getReservedField())) {
				try {
					reservedField = mapper.readValue(req.getReservedField(), ReservedFieldVo.class);
					reservedField.setPropertyParkingArea(StringUtils.isNotEmpty(reservedField.getPropertyParkingArea()) ? 
							reservedField.getPropertyParkingArea() : "0");
					reservedField.setPropertyParkingNum(StringUtils.isNotEmpty(reservedField.getPropertyParkingNum()) ? 
							reservedField.getPropertyParkingNum() : "0");
					reservedField.setDefenceParkingArea(StringUtils.isNotEmpty(reservedField.getDefenceParkingArea()) ? 
							reservedField.getDefenceParkingArea() : "0");
					reservedField.setDefenceParkingNum(StringUtils.isNotEmpty(reservedField.getDefenceParkingNum()) ? 
							reservedField.getDefenceParkingNum() : "0");
					reservedField.setWaterMeterBase(StringUtils.isNotEmpty(reservedField.getWaterMeterBase()) ? 
							reservedField.getWaterMeterBase() : "0");
					reservedField.setElectricMeterBase(StringUtils.isNotEmpty(reservedField.getElectricMeterBase()) ? 
							reservedField.getElectricMeterBase() : "0");
					
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			} else {
				reservedField = new ReservedFieldVo();
			}

			try {
				estate.setReservedField(mapper.writeValueAsString(reservedField));
				estate.setAcceptanceDate(StringUtils.isNotEmpty(req.getAcceptanceDate())
						? DateUtil.parseLongFormat(req.getAcceptanceDate())
						: null);
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			} catch (ParseException e) {
				e.printStackTrace();
			}
			estate.setPaymentAccount(
					null != req.getPaymentAccountId() ? communityPaymentAccountDao.get(req.getPaymentAccountId())
							: null);
			;
			communityEstateDao.save(estate);
			if (CommunityCache.estateList.containsKey(estate.getId())) {
				CommunityCache.estateList.remove(estate.getId());
			} else {
				CommunityCache.estateList.put(estate.getId(), estate);
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "修改单元")
	public IResponse modifyCommunityEstate(CommunityEstateReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getEstateId() && null != req.getBuildingId()) {
			CommunityEstateEntity estate = communityEstateDao.get(req.getEstateId());
			CommunityBuildingEntity building = communityBuildingDao.get(req.getBuildingId());
			if (null == estate || null == building) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			CommunityEstateEntity parentProperty = null;
			if (null != req.getParentPropertyId()) {
				parentProperty = communityEstateDao.get(req.getParentPropertyId());
				if (null == parentProperty) {
					res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
					res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
					return res;
				}
			}
			estate.setState(EntityContext.RECORD_STATE_VALID);
			estate.setBuilding(null != building ? building : estate.getBuilding());
			try {
				estate.setAcceptanceDate(StringUtils.isNotEmpty(req.getAcceptanceDate())
						? DateUtil.parseLongFormat(req.getAcceptanceDate())
						: estate.getAcceptanceDate());
			} catch (ParseException e) {
				e.printStackTrace();
			}
			estate.setRentState(null != req.getRentState() ? req.getRentState() : estate.getRentState());
			estate.setPropertyName(
					StringUtils.isNotEmpty(req.getPropertyName()) ? req.getPropertyName() : estate.getPropertyName());
			estate.setComment(StringUtils.isNotEmpty(req.getComment()) ? req.getComment() : estate.getComment());
			estate.setEstateState(null != req.getEstateState() ? req.getEstateState() : estate.getEstateState());
			estate.setIsAcceptanceReceive(null != req.getIsAcceptanceReceive() ? req.getIsAcceptanceReceive()
					: estate.getIsAcceptanceReceive());
			estate.setBuildingArea(StringUtils.isNotEmpty(req.getBuildingArea()) ? new BigDecimal(req.getBuildingArea())
					: estate.getBuildingArea());
			estate.setUsageTerm(
					StringUtils.isNotEmpty(req.getUsageTerm()) ? req.getUsageTerm() : estate.getUsageTerm());
			estate.setUnitCode(StringUtils.isNotEmpty(req.getUnitCode()) ? req.getUnitCode() : estate.getUnitCode());
			estate.setOrientation(
					StringUtils.isNotEmpty(req.getOrientation()) ? req.getOrientation() : estate.getOrientation());
			estate.setFloor(null != req.getFloor() ? req.getFloor() : estate.getFloor());
			estate.setRoomNumber(
					StringUtils.isNotEmpty(req.getRoomNumber()) ? req.getRoomNumber() : estate.getRoomNumber());
			estate.setUsableArea(StringUtils.isNotEmpty(req.getUsableArea()) ? new BigDecimal(req.getUsableArea())
					: estate.getUsableArea());
			estate.setChargingArea(StringUtils.isNotEmpty(req.getChargingArea()) ? new BigDecimal(req.getChargingArea())
					: estate.getChargingArea());
			estate.setAdditionalArea(
					StringUtils.isNotEmpty(req.getAdditionalArea()) ? new BigDecimal(req.getAdditionalArea())
							: estate.getAdditionalArea());
			estate.setDecoration(
					StringUtils.isNotEmpty(req.getDecoration()) ? req.getDecoration() : estate.getDecoration());
			estate.setAsset(null != req.getAssetId() ? assetDao.get(req.getAssetId()) : null);
			estate.setEstateType(
					StringUtils.isNotEmpty(req.getEstateType()) ? req.getEstateType() : estate.getEstateType());
			ReservedFieldVo reservedField = null;
			if (StringUtils.isNotEmpty(req.getReservedField())) {
				try {
					reservedField = mapper.readValue(req.getReservedField(), ReservedFieldVo.class);
//					ReservedFieldVo rfv =  mapper.readValue(estate.getReservedField(), ReservedFieldVo.class);
//					
//					BigDecimal buildingArea = new BigDecimal("0");
//					
//					BigDecimal propertyParkingArea =  new BigDecimal("0");
//					Integer propertyParkingNum = 0;
//					BigDecimal defenceParkingArea = new BigDecimal("0");
//					Integer defenceParkingNum = 0;
//          			List<CommunityMemberPropertyEntity> parkingList = communityMemberPropertyDao.
//          					getListByHql("select distinct a from CommunityMemberPropertyEntity a where "
//          							+ "a.isCurrentMember=1 and a.memberType=0  and a.parentProperty.id="
//          							+estate.getId(), "");
//          			if(null!=parkingList) {
//          				for(CommunityMemberPropertyEntity parking : parkingList) {
//    						CommunityEstateEntity o = (CommunityEstateEntity) parking.getProperty();
//    						buildingArea = o.getBuildingArea();
//    						if(o.getEstateType().equals("产权车位")) {
//    							propertyParkingArea = propertyParkingArea.add(buildingArea);
//    							propertyParkingNum = propertyParkingNum+1;
//    						}else if(o.getEstateType().equals("人防车位")) {
//    							defenceParkingArea = defenceParkingArea.add(buildingArea);
//    							defenceParkingNum = defenceParkingNum+1;
//    						}
//          				}
//          			}
//					reservedField.setPropertyParkingArea(propertyParkingArea.toString());
//					reservedField.setPropertyParkingNum(propertyParkingNum.toString());
//					reservedField.setDefenceParkingArea(defenceParkingArea.toString());
//					reservedField.setDefenceParkingNum(defenceParkingNum.toString());
//
//					reservedField.setWaterMeterBase(StringUtils.isNotEmpty(reservedField.getWaterMeterBase()) ? 
//							reservedField.getWaterMeterBase() : rfv.getWaterMeterBase());
//					reservedField.setElectricMeterBase(StringUtils.isNotEmpty(reservedField.getElectricMeterBase()) ? 
//							reservedField.getElectricMeterBase() : rfv.getElectricMeterBase());
					estate.setReservedField(mapper.writeValueAsString(reservedField));
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			} else {
				estate.setReservedField(estate.getReservedField());
			}
			estate.setPaymentAccount(
					null != req.getPaymentAccountId() ? communityPaymentAccountDao.get(req.getPaymentAccountId())
							: null);
			estate.setLastModifyTime(new Timestamp(new Date().getTime()));

			estate.setSpecialFeeFlag(
					null != req.getSpecialFeeFlag() ? req.getSpecialFeeFlag() : estate.getSpecialFeeFlag());
			estate.setSpecialAllocationFlag(null!=req.getSpecialAllocationFlag() ? 
					req.getSpecialAllocationFlag() : estate.getSpecialAllocationFlag());
			if (CommunityCache.estateList.containsKey(estate.getId())) {
				CommunityCache.estateList.remove(estate.getId());
			} else {
				CommunityCache.estateList.put(estate.getId(), estate);
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@CacheEvict(cacheNames = "unitCodeListCache", allEntries = true, beforeInvocation = false, condition = "#result.ret eq '0000'")
	@Audit(operate = "删除单元")
	public IResponse deleteCommunityEstate(CommunityEstateReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getEstateId()) {
			CommunityEstateEntity Estate = communityEstateDao.get(req.getEstateId());
			if (null == Estate) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			Estate.setState(EntityContext.RECORD_STATE_INVALID);

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	public IResponse getCommunityEstateInfo(CommunityEstateReq req) {
		GetCommunityEstateInfoRes res = new GetCommunityEstateInfoRes();
		if (null != req.getEstateId()) {
			CommunityEstateEntity estate = communityEstateDao.get(req.getEstateId());
			if (null == estate) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			CommunityEstateForm communityEstateForm = new CommunityEstateForm();
			communityEstateForm.setEstateId(estate.getId());
			communityEstateForm.setCreateTime(estate.getCreateTime() != null ? sdf.format(estate.getCreateTime()) : "");
			communityEstateForm.setLastModifyTime(
					estate.getLastModifyTime() != null ? sdf.format(estate.getLastModifyTime()) : "");
			communityEstateForm.setPropertyName(estate.getPropertyName());
			communityEstateForm.setEstateState(estate.getEstateState());
			communityEstateForm.setIsAcceptanceReceive(estate.getIsAcceptanceReceive());
			communityEstateForm.setUsageTerm(estate.getUsageTerm());
			communityEstateForm.setOrientation(estate.getOrientation());
			communityEstateForm
					.setEstateType(StringUtils.isNotEmpty(estate.getEstateType()) ? estate.getEstateType() : "");
			communityEstateForm.setIsReserved(estate.getIsReserved());
			communityEstateForm.setUnitAddress(estate.getBuilding().getDistrict().getDistrictName()
					+ estate.getBuilding().getBuildingName() + estate.getFloor() + estate.getRoomNumber());

			communityEstateForm.setFloor(estate.getFloor());
			communityEstateForm.setRoomNumber(estate.getRoomNumber());
			communityEstateForm.setUsableArea(null != estate.getUsableArea() ? estate.getUsableArea().toString() : "");
			communityEstateForm
					.setChargingArea(null != estate.getChargingArea() ? estate.getChargingArea().toString() : "");
			communityEstateForm
					.setAdditionalArea(null != estate.getAdditionalArea() ? estate.getAdditionalArea().toString() : "");
			communityEstateForm.setDecoration(estate.getDecoration());
			communityEstateForm.setComment(estate.getComment());
			communityEstateForm.setSpecialFeeFlag(estate.getSpecialFeeFlag());
			communityEstateForm.setSpecialAllocationFlag(estate.getSpecialAllocationFlag());
			communityEstateForm.setIsAcceptanceReceive(estate.getIsAcceptanceReceive());
			communityEstateForm.setRentState(estate.getRentState());

			if (null != estate.getBuilding()) {
				CommunityBuilding communityBuildingForm = new CommunityBuilding();
				communityBuildingForm.setCommunityBuildingId(estate.getBuilding().getId());
				communityBuildingForm.setAddress(estate.getBuilding().getAddress());
				communityBuildingForm.setBuildingCode(estate.getBuilding().getBuildingCode());
				communityBuildingForm.setBuildingName(estate.getBuilding().getBuildingName());
				communityBuildingForm.setBuildingPermitNum(estate.getBuilding().getBuildingPermitNum());
				if (null != estate.getBuilding().getDistrict()) {
					CommunityDistrict districtForm = new CommunityDistrict();
					districtForm.setCommunityDistrictId(estate.getBuilding().getDistrict().getId());
					districtForm.setDistrictName(estate.getBuilding().getDistrict().getDistrictName());
					districtForm.setAddress(estate.getBuilding().getDistrict().getAddress());
					districtForm.setDistrictCode(estate.getBuilding().getDistrict().getDistrictCode());
					communityBuildingForm.setDistrictForm(districtForm);
					;
				}
				communityEstateForm.setBuildingForm(communityBuildingForm);
			}
			if (null != estate.getAsset()) {
				communityEstateForm.setAssetForm(getAsset(estate.getAsset()));
			}
			if (null != estate.getPaymentAccount()) {
				CommunityPaymentAccountForm communityPaymentAccountForm = new CommunityPaymentAccountForm();
				communityPaymentAccountForm.setCommunityPaymentAccountId(estate.getPaymentAccount().getId());
				communityPaymentAccountForm.setCreateTime(null != estate.getPaymentAccount().getCreateTime()
						? DateUtil.formatLongFormat(estate.getPaymentAccount().getCreateTime())
						: "");
				communityPaymentAccountForm.setLastModifyTime(null != estate.getPaymentAccount().getLastModifyTime()
						? DateUtil.formatLongFormat(estate.getPaymentAccount().getLastModifyTime())
						: "");
				communityPaymentAccountForm.setState(estate.getPaymentAccount().getState());
				communityPaymentAccountForm.setBankAccount(estate.getPaymentAccount().getBankAccount());
				communityPaymentAccountForm.setBankName(estate.getPaymentAccount().getBankName());
				communityPaymentAccountForm.setAccountName(estate.getPaymentAccount().getAccountName());
				communityPaymentAccountForm.setIdNumber(estate.getPaymentAccount().getIdNumber());
				communityEstateForm.setPaymentAccountForm(communityPaymentAccountForm);
			}

			communityEstateForm
					.setBuildingArea(null != estate.getBuildingArea() ? estate.getBuildingArea().toString() : "");
			communityEstateForm.setUnitCode(estate.getUnitCode());
			communityEstateForm.setAcceptanceDate(
					null != estate.getAcceptanceDate() ? DateUtil.formatLongFormat(estate.getAcceptanceDate()) : "");
			Collections.sort(estate.getMemberPropertyList(), new Comparator<CommunityMemberPropertyEntity>() {
				public int compare(CommunityMemberPropertyEntity o1, CommunityMemberPropertyEntity o2) {
					if (null == o1.getBillingDate() || null == o2.getBillingDate()) {
						return 0;
					}
					if (o2.getBillingDate().before(o1.getBillingDate())) {
						return -1;
					}
					if (o1.getBillingDate() == o2.getBillingDate())
						return 0;
					return 1;
				}
			});
			List<CommunityMemberPropertyForm> memberPropertyFormList = new ArrayList<>();
			for (CommunityMemberPropertyEntity o : estate.getMemberPropertyList()) {
				CommunityMemberPropertyForm memberProperForm = new CommunityMemberPropertyForm();
				memberProperForm.setCreateTime(sdf.format(o.getCreateTime()));
				if (null != o.getMember()) {
					CommunityMemberForm memberForm = new CommunityMemberForm();
					memberForm.setMemberId(o.getMember().getId());
					memberForm.setRelation(
							StringUtils.isNotEmpty(o.getMember().getRelation()) ? o.getMember().getRelation() : "");
					memberForm.setEmail(o.getMember().getEmail());
					memberForm.setHomePhone(o.getMember().getHomePhone());
					memberForm.setNickName(o.getMember().getNickName());
					memberForm.setOfficePhone(o.getMember().getOfficePhone());
					memberForm.setPhone(o.getMember().getPhone());
					memberForm.setRegistName(o.getMember().getRegistName());
					memberForm.setSex(o.getMember().getSex());
					memberForm.setSmartcardId(o.getMember().getSmartcardId());
					memberForm.setIdCard(
							StringUtils.isNotEmpty(o.getMember().getIdCard()) ? o.getMember().getIdCard() : "");
					memberForm.setHomeAddress(
							StringUtils.isNotEmpty(o.getMember().getHomeAddress()) ? o.getMember().getHomeAddress()
									: "");
					memberForm.setHeadImage(
							StringUtils.isNotEmpty(o.getMember().getHeadImage()) ? o.getMember().getHeadImage() : "");
					memberForm.setUserName(
							StringUtils.isNotEmpty(o.getMember().getUserName()) ? o.getMember().getUserName() : "");
					memberForm.setBirthday(o.getMember().getBirthday());
					memberForm.setNativePlace(o.getMember().getNativePlace());
					memberForm.setNation(o.getMember().getNation());
					memberForm.setPoliceStation(o.getMember().getPoliceStation());
					memberForm.setPostalCode(o.getMember().getPostalCode());
					memberForm.setCompany(o.getMember().getCompany());
					memberForm.setContactPerson(o.getMember().getContactPerson());
					memberForm.setEmergencyContact(o.getMember().getEmergencyContact());
					memberForm.setComment(o.getMember().getComment());
//					memberForm.setBuyersName(o.getMember().getBuyersName());
//					memberForm.setBuyersAddress(o.getMember().getBuyersAddress());
//					memberForm.setBusinessType(o.getMember().getBusinessType());
//					memberForm.setInvoiceType(o.getMember().getInvoiceType());
//					memberForm.setPaytaxNo(o.getMember().getPaytaxNo());
//					memberForm.setBuyersBankAccount(o.getMember().getBuyersBankAccount());
					memberForm.setIdType(o.getMember().getIdType());

					memberProperForm.setMemberForm(memberForm);
					// communityEstateForm.getMemberPropertyList().add(memberProperForm);
				}

				// memberProperForm.setPropertyForm(getCommunityPropertyForm(o.getProperty(),null,null));
				memberProperForm.setMemberType(o.getMemberType());
				memberProperForm.setAuditState(o.getAuditState());
				memberProperForm.setIsCurrentMember(o.getIsCurrentMember());
				memberProperForm.setBillingDate(
						null != o.getBillingDate() ? DateUtil.formatLongFormat(o.getBillingDate()) : "");
				memberProperForm
						.setCreateTime(null != o.getCreateTime() ? DateUtil.formatLongFormat(o.getCreateTime()) : "");
				memberProperForm.setIsCurrentOwner(o.getIsCurrentOwner());
				memberProperForm
						.setRecordDate(null != o.getRecordDate() ? DateUtil.formatLongFormat(o.getRecordDate()) : "");
				memberProperForm.setTerminationDate(
						null != o.getTerminationDate() ? DateUtil.formatLongFormat(o.getTerminationDate()) : "");
//				memberProperForm.setAcceptanceDate(
//						null != o.getAcceptanceDate() ? DateUtil.formatLongFormat(o.getAcceptanceDate()) : "");
				memberProperForm.setMemberPropertyId(o.getId());
				memberProperForm.setEndDate(null != o.getEndDate() ? DateUtil.formatLongFormat(o.getEndDate()) : "");
				memberProperForm.setCarInfo(o.getCarInfo());

				memberProperForm.setParentPropertyForm(getCommunityPropertyForm(o.getParentProperty(), null, null));
				if (null != o.getIsCurrentMember() && o.getIsCurrentMember() == 1) {
					communityEstateForm.getMemberPropertyList().add(memberProperForm);
				} else {
					memberPropertyFormList.add(memberProperForm);
				}
			}
			communityEstateForm.getMemberPropertyList().addAll(memberPropertyFormList);
//			if (null != estate.getPaymentAccount()) {
//				CommunityPaymentAccountForm communityPaymentAccountForm = new CommunityPaymentAccountForm();
//				communityPaymentAccountForm.setCommunityPaymentAccountId(estate.getPaymentAccount().getId());
//				communityPaymentAccountForm.setCreateTime(null != estate.getPaymentAccount().getCreateTime()
//						? DateUtil.formatLongFormat(estate.getPaymentAccount().getCreateTime())
//						: "");
//				communityPaymentAccountForm.setLastModifyTime(null != estate.getPaymentAccount().getLastModifyTime()
//						? DateUtil.formatLongFormat(estate.getPaymentAccount().getLastModifyTime())
//						: "");
//				communityPaymentAccountForm.setState(estate.getPaymentAccount().getState());
//				communityPaymentAccountForm.setBankAccount(estate.getPaymentAccount().getBankAccount());
//				communityPaymentAccountForm.setBankName(estate.getPaymentAccount().getBankName());
//				communityPaymentAccountForm.setAccountName(estate.getPaymentAccount().getAccountName());
//				communityEstateForm.setPaymentAccountForm(communityPaymentAccountForm);
//			}
			communityEstateForm.setReservedField(
					StringUtils.isNotEmpty(estate.getReservedField()) ? estate.getReservedField() : "");

			res.setEstateForm(communityEstateForm);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Cacheable(cacheNames = "unitCodeListCache", key = "#req.unitCode + '|' + #req.buildingType", unless = "#result.getUnitCodeList().size() == 0")
	public GetCommunityEstateUnitCodeListRes getCommunityEstateUnitCodeList(CommunityEstateReq req) {
		GetCommunityEstateUnitCodeListRes res = new GetCommunityEstateUnitCodeListRes();
		if (StringUtils.isEmpty(req.getUnitCode())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO + "UnitCode不能为空！");
			return res;
		}

		Page<CommunityEstateEntity> page = new Page<CommunityEstateEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

		Map<String, Object> params = new HashMap<String, Object>();
		params.put("unitCode", req.getUnitCode() + "%");
		StringBuilder hql = new StringBuilder("select e from CommunityEstateEntity e");
		if (req.getBuildingType() != null) {
			hql.append(
					" inner join e.building b where e.unitCode like :unitCode and b.buildingType like :buildingType");
			params.put("buildingType", req.getBuildingType());
		} else {
			hql.append(" where e.unitCode like :unitCode");
		}
		hql.append(" order by e.unitCode");
		page = communityEstateDao.queryPage(page, hql.toString(), params);

		for (CommunityEstateEntity estate : page.getResultList()) {
			CommunityEstateUnitCodeForm form = new CommunityEstateUnitCodeForm();
			form.setEstateId(estate.getId());
			form.setEstateType(estate.getEstateType());
			form.setBuildingType(estate.getBuilding() != null ? estate.getBuilding().getBuildingType() : null);
			form.setUnitCode(estate.getUnitCode());
			res.getUnitCodeList().add(form);
		}

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "单元绑定收费项目")
	public IResponse estateBindingPayItems(CommunityEstateReq req) {
		GenericResponse res = new GenericResponse();

		if (StringUtils.isNotEmpty(req.getPayItemsIdList())) {
			List<CommunityPayItemsEntity> list = new ArrayList<CommunityPayItemsEntity>();

//			 StringBuilder hql = new StringBuilder("select distinct a from CommunityEstateEntity a  " +
//			 "inner join a.payItemsList b where b.id in("+req.getPayItemsIdList()+")");
//			 List<CommunityEstateEntity> estateList =
//			 communityEstateDao.getListByHql(hql.toString(), ""); 
			String[] payItemsIds = req.getPayItemsIdList().split(",");
			for (String payItemsId : payItemsIds) {
				CommunityPayItemsEntity payItems = communityPayItemsDao.get(Integer.parseInt(payItemsId));
				if (null != payItems) {
					list.add(payItems);
//					 estateList.forEach(o->{
//						 o.getPayItemsList().remove(payItems); 
//					 }); 
				}
			}

			if (StringUtils.isNotEmpty(req.getBuildingIdList())) {
				String[] buildingIds = req.getBuildingIdList().split(",");
				for (String id : buildingIds) {
					CommunityBuildingEntity building = communityBuildingDao.get(Integer.valueOf(id));
					if (null != building.getPropertyList()) {
						building.getPropertyList().forEach(p -> {
							if (null != p) {
								for (CommunityPayItemsEntity items : list) {
									if (!items.getPropertyList().contains(p)) {
										p.getPayItemsList().add(items);
									}
								}
							}
						});
					}
				}
			} else if (StringUtils.isNotEmpty(req.getDistrictIdList())) {
				String[] districtIds = req.getDistrictIdList().split(",");
				for (String id : districtIds) {
					CommunityDistrictEntity district = communityDistrictDao.get(Integer.valueOf(id));
					if (null != district.getBuildingList()) {
						district.getBuildingList().forEach(o -> {
							if (null != o.getPropertyList()) {
								o.getPropertyList().forEach(p -> {
									if (null != p) {
										for (CommunityPayItemsEntity items : list) {
											if (!items.getPropertyList().contains(p)) {
												p.getPayItemsList().add(items);
											}
										}
									}
								});
							}
						});
					}
				}
			} else if (StringUtils.isNotEmpty(req.getEstateIdList())) {
				String[] propertyId = req.getEstateIdList().split(",");
				for (String id : propertyId) {
					CommunityPropertyEntity property = communityPropertyDao.get(Integer.valueOf(id));
					if (null != property) {
						for (CommunityPayItemsEntity items : list) {
							if (!items.getPropertyList().contains(property)) {
								property.getPayItemsList().add(items);
							}
						}
					}
				}
			}

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "单元解绑收费项目")
	public IResponse estateUnbindingPayItems(CommunityEstateReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getPayItemsIdList()) && (StringUtils.isNotEmpty(req.getBuildingIdList())
				|| StringUtils.isNotEmpty(req.getDistrictIdList()) || StringUtils.isNotEmpty(req.getEstateIdList()))) {
			List<CommunityPayItemsEntity> list = new ArrayList<CommunityPayItemsEntity>();
			String[] payItemsIds = req.getPayItemsIdList().split(",");
			for (String payItemsId : payItemsIds) {
				CommunityPayItemsEntity payItems = communityPayItemsDao.get(Integer.parseInt(payItemsId));
				if (null != payItems) {
					list.add(payItems);
				}
			}
			if (StringUtils.isNotEmpty(req.getBuildingIdList())) {
				String[] buildingIds = req.getBuildingIdList().split(",");
				for (String id : buildingIds) {
					CommunityBuildingEntity building = communityBuildingDao.get(Integer.valueOf(id));
					if (null != building.getPropertyList()) {
						building.getPropertyList().forEach(p -> {
							if (null != p) {
								for (CommunityPayItemsEntity items : list) {
									if (items.getPropertyList().contains(p)) {
										p.getPayItemsList().remove(items);
									}
								}
							}
						});
					}
				}
			} else if (StringUtils.isNotEmpty(req.getDistrictIdList())) {
				String[] districtIds = req.getDistrictIdList().split(",");
				for (String id : districtIds) {
					CommunityDistrictEntity district = communityDistrictDao.get(Integer.valueOf(id));
					if (null != district.getBuildingList()) {
						district.getBuildingList().forEach(o -> {
							if (null != o.getPropertyList()) {
								o.getPropertyList().forEach(p -> {
									if (null != p) {
										for (CommunityPayItemsEntity items : list) {
											if (items.getPropertyList().contains(p)) {
												p.getPayItemsList().remove(items);
											}
										}
									}
								});
							}
						});
					}
				}
			} else if (StringUtils.isNotEmpty(req.getEstateIdList())) {
				String[] propertyId = req.getEstateIdList().split(",");
				for (String id : propertyId) {
					CommunityPropertyEntity property = communityPropertyDao.get(Integer.valueOf(id));
					if (null != property) {
						for (CommunityPayItemsEntity items : list) {
							if (items.getPropertyList().contains(property)) {
								property.getPayItemsList().remove(items);
							}
						}
					}
				}
			}

			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	public IResponse getSelectedCommunityEstateTree(CommunityEstateReq req) {
		GetCommunityDistrictListRes res = new GetCommunityDistrictListRes();
//		if (null == req.getMeterId() && null == req.getPayItemsId()) {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//			return res;
//		}
////		if (null != req.getDepth() && req.getDepth() == 1) {
////			StringBuilder hql = new StringBuilder("select distinct a from CommunityDistrictEntity a where a.state="
////					+ EntityContext.RECORD_STATE_VALID);
////			hql.append(" ORDER BY a.id desc");
////			List<CommunityDistrictEntity> list = communityDistrictDao.getListByHql(hql.toString(), "");
////
////			StringBuilder isSelectedHql = new StringBuilder(
////					"select distinct a from CommunityDistrictEntity a inner join a.buildingList b "
////							+ " inner join b.propertyList c"
////							+ (null != req.getPayItemsId() ? " inner join c.payItemsList d " : "")
////							+ (null != req.getMeterId() ? " inner join c.meterList e" : "") + " where a.state= "
////							+ EntityContext.RECORD_STATE_VALID);
////			isSelectedHql.append(null != req.getPayItemsId() ? " and d.id=" + req.getPayItemsId() : "")
////					.append(null != req.getMeterId() ? "  and e.id=" + req.getMeterId() : "");
////			List<CommunityDistrictEntity> isSelectedHqlList = communityDistrictDao
////					.getListByHql(isSelectedHql.toString(), "");
////			list.forEach(o -> {
////				CommunityDistrictForm communityDistrictForm = new CommunityDistrictForm();
////				communityDistrictForm.setCommunityDistrictId(o.getId());
////				communityDistrictForm.setDistrictName(o.getDistrictName());
////				if (null != isSelectedHqlList && isSelectedHqlList.contains(o)) {
////					communityDistrictForm.setIsSelected(1);
////				} else {
////					communityDistrictForm.setIsSelected(0);
////				}
////
////				res.getCommunityDistrictList().add(communityDistrictForm);
////			});
////		} else {
//			StringBuilder hql = new StringBuilder("select distinct a from CommunityEstateEntity a  "
//					+ "inner join a.building c inner join c.district d where 1=1 ");
//			hql.append(StringUtils.isNotEmpty(req.getUnitCode()) ? " and a.unitCode like'%" + req.getUnitCode() + "%'"
//					: "")
//					.append(StringUtils.isNotEmpty(req.getDistrictIdList())
//							? " and d.id in(" + req.getDistrictIdList() + ")"
//							: "")
//					.append(StringUtils.isNotEmpty(req.getBuildingIdList())
//							? " and c.id in(" + req.getBuildingIdList() + ")"
//							: "");
//			List<CommunityEstateEntity> list = communityEstateDao.getListByHql(hql.toString(), "");
//			Map<Integer, Map<String, Object>> dataMap = new HashMap<Integer, Map<String, Object>>();
//			CommunityPayItemsEntity communityPayItems = null != req.getPayItemsId()
//					? communityPayItemsDao.get(req.getPayItemsId())
//					: null;
//			CommunityMeterEntity communityMeter = null != req.getMeterId() ? communityMeterDao.get(req.getMeterId())
//					: null;
//			// list.forEach(o->{
//			for (CommunityEstateEntity o : list) {
//				List<CommunityEstateForm> estateList = new ArrayList<CommunityEstateForm>();
//				Map<String, Object> districtMap = null;
//				if (dataMap.containsKey(o.getBuilding().getDistrict().getId())) {
//					districtMap = dataMap.get(o.getBuilding().getDistrict().getId());
//
//					CommunityDistrictForm districtForm = (CommunityDistrictForm) districtMap.get("districtForm");
//
//					@SuppressWarnings("unchecked")
//					Map<Integer, Map<String, Object>> buildingDateMap = (Map<Integer, Map<String, Object>>) districtMap
//							.get("building");
//
//					Map<String, Object> buildingMap = null;
//					CommunityBuildingForm buildingForm = null;
//					if (buildingDateMap.containsKey(o.getBuilding().getId())) {
//						buildingMap = buildingDateMap.get(o.getBuilding().getId());
//						buildingForm = (CommunityBuildingForm) buildingMap.get("buildingForm");
//						estateList = (List<CommunityEstateForm>) buildingMap.get("estateList");
//					} else {
//						buildingMap = new HashMap<String, Object>();
//						buildingForm = new CommunityBuildingForm();
//						buildingForm.setCommunityBuildingId(o.getBuilding().getId());
//						buildingForm.setBuildingName(o.getBuilding().getBuildingName());
//					}
//
//					CommunityEstateForm estateForm = new CommunityEstateForm();
//					estateForm.setEstateId(o.getId());
//					estateForm.setUnitCode(o.getUnitCode());
//					estateForm.setPropertyName(o.getPropertyName());
//					if ((null != communityPayItems && o.getPayItemsList().contains(communityPayItems))
//							|| (null != communityMeter && o.getMeterList().contains(communityMeter))) {
//						estateForm.setIsSelected(1);
//						districtForm.setIsSelected(1);
//						buildingForm.setIsSelected(1);
//					} else {
//						estateForm.setIsSelected(0);
//						districtForm.setIsSelected(0);
//						buildingForm.setIsSelected(0);
//					}
//
//					estateList.add(estateForm);
//					buildingMap.put("buildingForm", buildingForm);
//					buildingMap.put("estateList", estateList);
//					buildingDateMap.put(o.getBuilding().getId(), buildingMap);
//					districtMap.put("districtForm", districtForm);
//					districtMap.put("building", buildingDateMap);
//				} else {
//					districtMap = new HashMap<String, Object>();
//					CommunityDistrictForm districtForm = new CommunityDistrictForm();
//					districtForm.setCommunityDistrictId(o.getBuilding().getDistrict().getId());
//					districtForm.setDistrictName(o.getBuilding().getDistrict().getDistrictName());
//					Map<Integer, Map<String, Object>> buildingDateMap = new HashMap<Integer, Map<String, Object>>();
//					Map<String, Object> buildingMap = new HashMap<String, Object>();
//					CommunityBuildingForm buildingForm = new CommunityBuildingForm();
//					buildingForm.setCommunityBuildingId(o.getBuilding().getId());
//					buildingForm.setBuildingName(o.getBuilding().getBuildingName());
//
//					CommunityEstateForm estateForm = new CommunityEstateForm();
//					estateForm.setEstateId(o.getId());
//					estateForm.setUnitCode(o.getUnitCode());
//					estateForm.setPropertyName(o.getPropertyName());
//					if ((null != communityPayItems && o.getPayItemsList().contains(communityPayItems))
//							|| (null != communityMeter && o.getMeterList().contains(communityMeter))) {
//
//					} else if ((null != communityPayItems && o.getPayItemsList().contains(communityPayItems))
//							|| (null != communityMeter && o.getMeterList().contains(communityMeter))) {
//						estateForm.setIsSelected(1);
//						districtForm.setIsSelected(1);
//						buildingForm.setIsSelected(1);
//					} else {
//						estateForm.setIsSelected(0);
//						districtForm.setIsSelected(0);
//						buildingForm.setIsSelected(0);
//					}
//
//					estateList.add(estateForm);
//					buildingMap.put("buildingForm", buildingForm);
//					buildingMap.put("estateList", estateList);
//					buildingDateMap.put(o.getBuilding().getId(), buildingMap);
//					districtMap.put("districtForm", districtForm);
//					districtMap.put("building", buildingDateMap);
//					dataMap.put(o.getBuilding().getDistrict().getId(), districtMap);
//				}
//			}
//			for (Integer districtKey : dataMap.keySet()) {
//				Map<String, Object> districtMap = dataMap.get(districtKey);
//				CommunityDistrictForm districtForm = (CommunityDistrictForm) districtMap.get("districtForm");
//				@SuppressWarnings("unchecked")
//				Map<Integer, Map<String, Object>> buildingDateMap = (Map<Integer, Map<String, Object>>) districtMap
//						.get("building");
//				for (Integer buildingKey : buildingDateMap.keySet()) {
//					Map<String, Object> buildingMap = buildingDateMap.get(buildingKey);
//					CommunityBuildingForm buildingForm = (CommunityBuildingForm) buildingMap.get("buildingForm");
//					List<CommunityEstateForm> estateList = (List<CommunityEstateForm>) buildingMap.get("estateList");
//					buildingForm.setEstateList(estateList);
//					districtForm.getBuildingList().add(buildingForm);
//				}
//				res.getCommunityDistrictList().add(districtForm);
//			}
//		//}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@SuppressWarnings("unchecked")
	public IResponse exportEstateList(CommunityEstateReq req, HttpServletResponse response) {
		ExportExcelRes res = new ExportExcelRes();

		Object userObj = getPrincipal(true);

		List<CommunityEstateEntity> list = communityEstateDao
				.getListByHql("select distinct a " + queryCondition(req, userObj), "");

		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("unitCode", "单位编号");
		heardMap.put("buildingName", "所属楼栋");
		heardMap.put("floor", "楼层");
		heardMap.put("roomNumber", "房号/车位号");
		heardMap.put("licensePlateNo", "车牌");
		heardMap.put("parentUnitCode", "车位绑定单元");
		heardMap.put("estateType", "房产类型");
		heardMap.put("estateState", "房屋状态");
		heardMap.put("userName", "业主姓名");
		heardMap.put("actualOwner", "实际产权人");
		heardMap.put("phone", "业主电话");
		
		heardMap.put("buildingArea", "建筑面积(m²)");
		heardMap.put("usableArea", "使用面积(m²)");
		heardMap.put("additionalArea", "附加面积(m²)");
		
		heardMap.put("billingDate", "计费开始时间");
		heardMap.put("endDate", "计费截止时间");
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();

		list.forEach(o -> {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("unitCode", o.getUnitCode());
			map.put("estateType", o.getEstateType());
			if (null != o.getEstateState()) {
				switch (o.getEstateState()) {
				case 0:
					map.put("estateState", "未收楼");
					break;
				case 1:
					if (o.getBuilding().getBuildingType() == 3) {
						map.put("estateState", "出售");
					} else {
						map.put("estateState", "已入住");
					}
					break;
				case 2:
					map.put("estateState", "装修中");
					break;
				case 3:
					map.put("estateState", "出租");
					break;
				case 4:
					map.put("estateState", "离退");
					break;
				case 5:
					map.put("estateState", "自主");
					break;
				case 6:
					map.put("estateState", "空置");
					break;
				default:
					map.put("estateState", "其他");
					break;
				}
			} else {
				map.put("estateState", "");
			}

			map.put("buildingName", (null != o.getBuilding() ? o.getBuilding().getBuildingName() : ""));
			map.put("floor", null!=o.getFloor() ? o.getFloor().toString():"");
			map.put("roomNumber", null!=o.getRoomNumber() ? o.getRoomNumber():"");
			map.put("buildingArea", null!=o.getBuildingArea() ? o.getBuildingArea().toString():"");
			map.put("usableArea", null!=o.getUsableArea()? o.getUsableArea().toString():"");
			map.put("additionalArea", null!=o.getAdditionalArea()? o.getAdditionalArea().toString():"");
			o.getMemberPropertyList().forEach(p -> {
				if (p.getIsCurrentMember() == 1) {
					map.put("parentUnitCode",
							null != p.getParentProperty()
									? ((CommunityEstateEntity) p.getParentProperty()).getUnitCode()
									: "");
					map.put("userName", null != p.getMember() ? p.getMember().getUserName() : "");
					map.put("phone", null != p.getMember() ? p.getMember().getPhone() : "");
					map.put("billingDate",
							null != p.getBillingDate() ? DateUtil.formatShortFormat(p.getBillingDate()) : "");
					map.put("endDate", null != p.getEndDate() ? DateUtil.formatShortFormat(p.getEndDate()) : "");
					if (StringUtils.isNotEmpty(p.getCarInfo())) {
						try {
							Map<String, String> carInfo = mapper.readValue(p.getCarInfo(), Map.class);
							map.put("licensePlateNo", carInfo.get("licensePlateNo"));
							map.put("actualOwner", carInfo.containsKey("actualOwner")? carInfo.get("actualOwner"):"");
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
					} else {
						map.put("licensePlateNo", "");
					}
				}
			});

			dataList.add(map);
		});

		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

		return res;
	}
	
	public  IResponse sendCommunitySms(CommunityEstateReq req) {
		GenericResponse res = new GenericResponse();
		if(StringUtils.isEmpty(req.getSmsCode())
				|| (StringUtils.isEmpty(req.getEstateIdList()) && 
						StringUtils.isEmpty(req.getBuildingIdList()) &&
						StringUtils.isEmpty(req.getDistrictIdList()) )) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		StringBuilder sql = new StringBuilder(
				"SELECT b.id,b.unitCode,"
				+ "d.homePhone ,b.paymentAccountId FROM  `t_community_property` b  "
				+ "INNER JOIN t_community_member_property c ON c.propertyId=b.id   "
				+ "INNER JOIN t_account d ON d.id=c.memberId INNER JOIN `t_community_building` e ON e.id=b.buildingId "
				+ " INNER JOIN  t_community_district f on f.id=e.districtId  "
				+ "WHERE  c.memberType=0 "
				+ "AND c.isCurrentMember=1 AND e.buildingType!=2 AND e.buildingType!=3 ");
		sql.append(StringUtils.isNotEmpty(req.getEstateIdList()) ? "  and b.id in("+req.getEstateIdList()+")" :"")
			.append(StringUtils.isNotEmpty(req.getBuildingIdList()) ? "  and e.id in("+req.getBuildingIdList()+")" :"")
			.append(StringUtils.isNotEmpty(req.getDistrictIdList()) ? "  and f.id in("+req.getDistrictIdList()+")" :"")
			.append((null==req.getSmsStatus() ||null!=req.getSmsStatus() && 0==req.getSmsStatus()) ? "" 
					: "  and (IFNULL (REPLACE (JSON_EXTRACT (`b`.`reservedField`,'$.isSendMessage'),'\"',''),1))=1 ")
			.append("  GROUP BY b.id");

		SessionFactory sessionFactory = (SessionFactory) SpringHandler.getBean("faithSessionFactory");
		Session session = sessionFactory.openSession();
		@SuppressWarnings("unchecked")
		List<Object[]> resultList = session.createNativeQuery(sql.toString()).list();
		for (Object[] temp : resultList) {
			//String unitCode = (null != temp[1] ? temp[1].toString() : "");
			String homePhone = (null != temp[2] ? temp[2].toString() : "");
			//String paymentAccountId = (null != temp[3] ? temp[3].toString() : "");
//			if(homePhone.contains("、")) {
//				homePhone = homePhone.split("、")[0];
//			}else if(homePhone.contains("/")){
//				homePhone = homePhone.split("/")[0];
//			}else if(homePhone.contains("，")){
//				homePhone = homePhone.split("，")[0];
//			}else if(homePhone.contains(" ")){
//				homePhone = homePhone.split(" ")[0];
//			}else if(homePhone.contains("\\\\")){
//				homePhone = homePhone.split("\\\\")[0];
//			}else if(homePhone.contains(",")){
//				homePhone = homePhone.split(",")[0];
//			}
			if(StringUtils.isNotEmpty(homePhone)) {
				sendSms(homePhone.replaceAll("、", ",").replaceAll("/", ",")
						.replaceAll("，", ",").replaceAll(" ", ",")
						.replaceAll("\\\\", ","),"",req.getSmsCode(),req.getSmsContent());
			}
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	

	
	public IResponse getProprietorInfoList(CommunityEstateReq req) {
		GetProprietorInfoListRes res = new GetProprietorInfoListRes();
		Page<CommunityEstateEntity> page = new Page<CommunityEstateEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		Object userObj = getPrincipal(true);
		String hql = queryCondition(req, userObj);
		page = communityEstateDao.queryPage(page, "select distinct a " + hql);

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		res.getProprietorInfoList().addAll(getProprietorInfoFormList(page.getResultList()));
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public List<ProprietorInfoForm> getProprietorInfoFormList(List<CommunityEstateEntity> estateList) {
		List<ProprietorInfoForm> proprietorInfoList = new ArrayList<ProprietorInfoForm>(); 
		int i=1;
		for (CommunityEstateEntity o : estateList) {
			ProprietorInfoForm proprietorInfoForm = getProprietorInfoForm(o);
			proprietorInfoForm.setSerialNumber(i+"");;
			proprietorInfoList.add(proprietorInfoForm);
			i++;
		}
		return proprietorInfoList;
	}
	
	public ProprietorInfoForm getProprietorInfoForm(CommunityEstateEntity o) {
		ProprietorInfoForm proprietorInfoForm = new ProprietorInfoForm();
		for (CommunityMemberPropertyEntity memberProperty : o.getMemberPropertyList()) {
			if(null!=memberProperty.getIsCurrentMember() &&
					memberProperty.getIsCurrentMember()==1 && memberProperty.getMemberType()==0) {
				proprietorInfoForm.setPhone(memberProperty.getMember().getPhone());
				proprietorInfoForm.setMemberId(memberProperty.getMember().getId());
				proprietorInfoForm.setUserName(memberProperty.getMember().getUserName());
				proprietorInfoForm.setReasonArrears(StringUtils.isNotEmpty(o.getReasonArrears()) ? 
						o.getReasonArrears() : "");
				ProprietorInfoVo reservedField = null;
				if (StringUtils.isNotEmpty(memberProperty.getMember().getExpandField())) {
					try {
						reservedField = mapper.readValue(memberProperty.getMember().getExpandField(), ProprietorInfoVo.class);
						proprietorInfoForm.setActiveGroup(StringUtils.isNotEmpty(reservedField.getActiveGroup()) ? 
								reservedField.getActiveGroup() : "");
						proprietorInfoForm.setAssociatedProperty(StringUtils.isNotEmpty(reservedField.getAssociatedProperty()) ? 
								reservedField.getAssociatedProperty() : "");
						proprietorInfoForm.setComplaintsContent(StringUtils.isNotEmpty(reservedField.getComplaintsContent()) ? 
								reservedField.getComplaintsContent() : "");
						proprietorInfoForm.setComplaintsNumber(StringUtils.isNotEmpty(reservedField.getComplaintsNumber()) ? 
								reservedField.getComplaintsNumber() : "");
						proprietorInfoForm.setDirectGroupAction(StringUtils.isNotEmpty(reservedField.getDirectGroupAction()) ? 
								reservedField.getDirectGroupAction() : "");
						proprietorInfoForm.setFamilySize(StringUtils.isNotEmpty(reservedField.getFamilySize()) ? 
								reservedField.getFamilySize() : "");
						proprietorInfoForm.setFamilyMember(StringUtils.isNotEmpty(reservedField.getFamilyMember()) ? 
								reservedField.getFamilyMember() : "");
						proprietorInfoForm.setHavePet(StringUtils.isNotEmpty(reservedField.getHavePet()) ? 
								reservedField.getHavePet() : "");
						proprietorInfoForm.setHobby(StringUtils.isNotEmpty(reservedField.getHobby()) ? 
								reservedField.getHobby() : "");
						proprietorInfoForm.setMajorRepair(StringUtils.isNotEmpty(reservedField.getMajorRepair()) ? 
								reservedField.getMajorRepair() : "");
						proprietorInfoForm.setOpinionLeader(StringUtils.isNotEmpty(reservedField.getOpinionLeader()) ? 
								reservedField.getOpinionLeader() : "");
						proprietorInfoForm.setParticipationGroupAction(StringUtils.isNotEmpty(reservedField.getParticipationGroupAction()) ? 
								reservedField.getParticipationGroupAction() : "");
						proprietorInfoForm.setProfession(StringUtils.isNotEmpty(reservedField.getProfession()) ? 
								reservedField.getProfession() : "");
						proprietorInfoForm.setRelation(StringUtils.isNotEmpty(reservedField.getRelation()) ? 
								reservedField.getRelation() : "");
						
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
				} 
			}
		}
		if(null!=o.getEstateState()) {
			switch(o.getEstateState()) {
			case 0:
				proprietorInfoForm.setEstateState("未收楼");break;
			case 1:
				proprietorInfoForm.setEstateState("已入住");break;
			case 2:
				proprietorInfoForm.setEstateState("装修中");break;
			case 3:
				proprietorInfoForm.setEstateState("出租");break;
			case 4:
				proprietorInfoForm.setEstateState("离退");break;
			case 5:
				proprietorInfoForm.setEstateState("自住(已收楼 未入住)");break;
			case 6:
				proprietorInfoForm.setEstateState("空置");break;
			default:
				proprietorInfoForm.setEstateState("");break;
			}
		}else {
			proprietorInfoForm.setEstateState("");
		}

		if (null != o.getBuilding()) {
			proprietorInfoForm.setDistrictName(null!=o.getBuilding().getDistrict()?
					o.getBuilding().getDistrict().getDistrictName():"");
			proprietorInfoForm.setBuildingName(o.getBuilding().getBuildingName());
		}else {
			proprietorInfoForm.setDistrictName("");
			proprietorInfoForm.setBuildingName("");
		}
		proprietorInfoForm.setEstateId(o.getId());
		proprietorInfoForm.setUnitCode(o.getUnitCode());
		proprietorInfoForm.setBuildingArea(null != o.getBuildingArea() ? o.getBuildingArea().toString() : "");
		return proprietorInfoForm;
	}
	
	public IResponse getProprietorInfo(CommunityEstateReq req) {
		GetProprietorInfoRes res = new GetProprietorInfoRes();
		if (null!=req.getEstateId()) {
			CommunityEstateEntity estate = communityEstateDao.get(req.getEstateId());
			if (null == estate) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			res.setProprietorInfo(getProprietorInfoForm(estate));
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	@Override
	@Audit(operate = "修改业主信息")
	public IResponse modifyProprietorInfo(CommunityProprietorInfoReq req) {
		GenericResponse res = new GenericResponse();
		Object userObj = getPrincipal(true);
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		
		if(!(userObj instanceof PlatformUserEntity)) { 
			res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
			return res;
		}
		if (null != req.getMemberId()) {
			CommunityMemberEntity member = communityMemberDao.get(req.getMemberId());
			if (null != member) {
				PlatformUserEntity user = (PlatformUserEntity)userObj;
				ProprietorInfoVo reservedField = null;
				JSONObject json = null; 
				List<OperationDiaryForm> diaryFormList = null;
				try {
					if (StringUtils.isNotEmpty(member.getExpandField())) {
						reservedField = mapper.readValue(member.getExpandField(), ProprietorInfoVo.class);
					}else {
						reservedField = new ProprietorInfoVo();
					}
					if(StringUtils.isNoneEmpty(member.getOperationLog())) {
						json = new JSONObject(member.getOperationLog());
						diaryFormList =mapper.readValue(json.get("diary").toString(), ArrayList.class);
					}else {
						json = new JSONObject();
						diaryFormList = new ArrayList<OperationDiaryForm>();
					}
					StringBuilder str = new StringBuilder();
					String profession = StringUtils.isNoneEmpty(req.getProfession()) ? req.getProfession() : "";
					String familySize = StringUtils.isNoneEmpty(req.getFamilySize()) ? req.getFamilySize() : "";
					String familyMember = StringUtils.isNoneEmpty(req.getFamilyMember()) ? req.getFamilyMember() : "";
					String relation = StringUtils.isNoneEmpty(req.getRelation()) ? req.getRelation() : "";
					String majorRepair = StringUtils.isNoneEmpty(req.getMajorRepair()) ? req.getMajorRepair() : "";
					String complaintsNumber = StringUtils.isNoneEmpty(req.getComplaintsNumber()) ? req.getComplaintsNumber() : "";
					String complaintsContent = StringUtils.isNoneEmpty(req.getComplaintsContent()) ? req.getComplaintsContent() : "";
					String participationGroupAction = StringUtils.isNoneEmpty(req.getParticipationGroupAction()) ? req.getParticipationGroupAction() : "";
					String directGroupAction = StringUtils.isNoneEmpty(req.getDirectGroupAction()) ? req.getDirectGroupAction() : "";
					String activeGroup =  StringUtils.isNoneEmpty(req.getActiveGroup()) ? req.getActiveGroup() : "";
					String havePet = StringUtils.isNoneEmpty(req.getHavePet()) ? req.getHavePet() : "";
					String opinionLeader= StringUtils.isNoneEmpty(req.getOpinionLeader()) ? req.getOpinionLeader() : "";
					String hobby = StringUtils.isNoneEmpty(req.getHobby()) ? req.getHobby() : "";
					String associatedProperty = StringUtils.isNoneEmpty(req.getAssociatedProperty()) ? req.getAssociatedProperty() : "";
					String reasonArrears = StringUtils.isNoneEmpty(req.getReasonArrears()) ? req.getReasonArrears() : "";
					if(null!=req.getEstateId()) {
						CommunityEstateEntity estate = communityEstateDao.get(req.getEstateId());
						if(null!=estate) {
							estate.setReasonArrears(reasonArrears);
							str.append("单元："+estate.getUnitCode()+";");
						}
					}
					
					str.append("职业:"+profession+";")
						.append("家庭人数:"+familySize+";")
						.append("家庭成员:"+familyMember+";")
						.append("是否关系客户:"+relation+";")
						.append("是否有过重大报修:"+majorRepair+";")
						.append("以往投诉次数:"+complaintsNumber+";")
						.append("投诉内容:"+complaintsContent+";")
						.append("是否参与过群诉:"+participationGroupAction+";")
						.append("是否主持过群诉:"+directGroupAction+";")
						.append("业主群中是否活越:"+activeGroup+";")
						.append("是否喂养宠物:"+havePet+";")
						.append("是否意见领袖:"+opinionLeader+";")
						.append("业余爱好:"+hobby+";")
						.append("关联房产:"+associatedProperty+";")
						.append("欠费原因："+reasonArrears);
					reservedField.setActiveGroup(StringUtils.isNotEmpty(activeGroup) ? 
							activeGroup : "");
					reservedField.setAssociatedProperty(StringUtils.isNotEmpty(associatedProperty) ? 
							associatedProperty : "");
					reservedField.setComplaintsContent(StringUtils.isNotEmpty(complaintsContent) ? 
							complaintsContent : "");
					reservedField.setComplaintsNumber(StringUtils.isNotEmpty(complaintsNumber) ? 
							complaintsNumber : "");
					reservedField.setDirectGroupAction(StringUtils.isNotEmpty(directGroupAction) ? 
							directGroupAction : "");
					reservedField.setFamilySize(StringUtils.isNotEmpty(familySize) ? 
							familySize : "");
					reservedField.setFamilyMember(StringUtils.isNotEmpty(familyMember) ? 
							familyMember : "");
					reservedField.setHavePet(StringUtils.isNotEmpty(havePet) ? 
							havePet : "");
					reservedField.setHobby(StringUtils.isNotEmpty(hobby) ? 
							hobby : "");
					reservedField.setMajorRepair(StringUtils.isNotEmpty(majorRepair) ? 
							majorRepair : "");
					reservedField.setOpinionLeader(StringUtils.isNotEmpty(opinionLeader) ? 
							opinionLeader : "");
					reservedField.setParticipationGroupAction(StringUtils.isNotEmpty(participationGroupAction) ? 
							participationGroupAction : "");
					reservedField.setProfession(StringUtils.isNotEmpty(profession) ? 
							profession : "");
					reservedField.setRelation(StringUtils.isNotEmpty(relation) ? 
							relation : "");
					//reservedField.setReasonArrears(reasonArrears);
					String expandField = mapper.writeValueAsString(reservedField);
					member.setExpandField(expandField);
					
					OperationDiaryForm diaryForm = new OperationDiaryForm("修改业主信息",
								str.toString(),DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
					diaryFormList.add(diaryForm);
					member.setOperationLog(json.put("diary", diaryFormList).toString());
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	public IResponse exporProprietorInfo(CommunityEstateReq req, HttpServletResponse response) {
		ExportExcelRes res = new ExportExcelRes();
		Object userObj = getPrincipal(true);
		String hql = queryCondition(req, userObj);
		List<CommunityEstateEntity> estateList = communityEstateDao.getListByHql("select distinct a " +hql, "");
		List<ProprietorInfoForm> proprietorInfoFormList = getProprietorInfoFormList(estateList);
		List<Map<String,Object>> dataList = new ArrayList<>(); 
		try {
			String str = mapper.writeValueAsString(proprietorInfoFormList);
			dataList = mapper.readValue(str, List.class);
		} catch (JsonProcessingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("serialNumber", "序号");
		heardMap.put("districtName", "区域");
		heardMap.put("buildingName", "楼栋");
		heardMap.put("unitCode", "单元");
		heardMap.put("userName", "业主姓名");
		heardMap.put("phone", "联系电话");
		heardMap.put("buildingArea", "建筑面积");
		heardMap.put("estateState", "房屋状态");
		heardMap.put("profession", "职业");
		heardMap.put("familySize", "家庭人数");
		heardMap.put("familyMember", "家庭成员");
		heardMap.put("relation", "是否关系客户（备注行业）");
		heardMap.put("majorRepair", "是否有过重大报修");
		heardMap.put("complaintsNumber", "以往投诉次数");
		heardMap.put("complaintsContent", "投诉内容");
		heardMap.put("participationGroupAction", "是否参与过群诉");
		heardMap.put("directGroupAction", "是否主持过群诉");
		heardMap.put("activeGroup", "业主群中是否活越");
		heardMap.put("havePet", "是否喂养宠物");
		heardMap.put("opinionLeader", "是否意见领袖");
		heardMap.put("hobby", "业余爱好");
		heardMap.put("associatedProperty", "关联房产");
		heardMap.put("reasonArrears", "欠费原因");

		//List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}
		return res;
	}
	
	
	@Audit(operate = "导入业主维护信息")
	public IResponse importProprietorInfo(HttpServletRequest request) {
		try {
			GenericResponse res  = proprietorInfoHandler(uploadFile(request));
			return res;
		} catch (IllegalStateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	@SuppressWarnings({ "resource", "unused", "unchecked" })
	private GenericResponse proprietorInfoHandler(File excelFile) throws IOException {
		GenericResponse res  = new GenericResponse();
		Object userObj = getPrincipal(true);
		if (null == userObj) {
			res.setRet(ResponseContext.RES_PERM_UNLOGIN_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNLOGIN_INFO);
			return res;
		}
		
		if(!(userObj instanceof PlatformUserEntity)) { 
			res.setRet(ResponseContext.RES_PERM_UNAUTHORIZED_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_UNAUTHORIZED_INFO);
			return res;
		}
		
		FileInputStream inputStream = new FileInputStream(excelFile);
		Workbook workBook = null;
		if (excelFile.getName().endsWith("xls")) {
			workBook = new HSSFWorkbook(inputStream);
		} else if (excelFile.getName().endsWith("xlsx")) {
			workBook = new XSSFWorkbook(inputStream);
		} else {
			//logger.info(excelFile + "不是excel类型文件！！！");
			return null;
		}
		PlatformUserEntity user = (PlatformUserEntity)userObj;
		Sheet sheet = workBook.getSheetAt(workBook.getActiveSheetIndex());
		StringBuilder info = new StringBuilder();
		int[] resultInt = new int[] { 0 };
		Map<String,Object> map =  new HashMap<String,Object>();
		sheet.forEach(o -> {
			if (o.getRowNum() > 0) {	
				String unitCode = cell2Str(o.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
				CommunityEstateEntity estate = communityEstateDao.findUnique("select a from CommunityEstateEntity a where a.unitCode='"+unitCode+"'");
				if(null!=estate) {
					String phone = cell2Str(o.getCell(5, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
					CommunityMemberEntity member = communityMemberDao.findUnique("select a from CommunityMemberEntity a "
							+ " inner join a.memberPropertyList b inner join b.property c where a.phone='"
							+phone+"' and c.id="+estate.getId(),"");
					if(null!=member) {
						ProprietorInfoVo reservedField = null;
						JSONObject json = null; 
						List<OperationDiaryForm> diaryFormList = null;
						try {
							if (StringUtils.isNotEmpty(member.getExpandField())) {
								reservedField = mapper.readValue(member.getExpandField(), ProprietorInfoVo.class);
							}else {
								reservedField = new ProprietorInfoVo();
							}
							if(StringUtils.isNoneEmpty(member.getOperationLog())) {
								json = new JSONObject(member.getOperationLog());
								diaryFormList =mapper.readValue(json.get("diary").toString(), ArrayList.class);
							}else {
								json = new JSONObject();
								diaryFormList = new ArrayList<OperationDiaryForm>();
							}
							StringBuilder str = new StringBuilder();
							String profession =cell2Str(o.getCell(8, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String familySize = cell2Str(o.getCell(9, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String familyMember = cell2Str(o.getCell(10, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String relation = cell2Str(o.getCell(11, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String majorRepair = cell2Str(o.getCell(12, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String complaintsNumber = cell2Str(o.getCell(13, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String complaintsContent = cell2Str(o.getCell(14, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String participationGroupAction = cell2Str(o.getCell(15, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String directGroupAction = cell2Str(o.getCell(16, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String activeGroup =  cell2Str(o.getCell(17, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String havePet = cell2Str(o.getCell(18, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String opinionLeader= cell2Str(o.getCell(19, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String hobby = cell2Str(o.getCell(20, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String associatedProperty = cell2Str(o.getCell(21, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							String reasonArrears = cell2Str(o.getCell(22, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
							str.append("职业:"+profession+";")
								.append("家庭人数:"+familySize+";")
								.append("家庭成员:"+familyMember+";")
								.append("是否关系客户:"+relation+";")
								.append("是否有过重大报修:"+majorRepair+";")
								.append("以往投诉次数:"+complaintsNumber+";")
								.append("投诉内容:"+complaintsContent+";")
								.append("是否参与过群诉:"+participationGroupAction+";")
								.append("是否主持过群诉:"+directGroupAction+";")
								.append("业主群中是否活越:"+activeGroup+";")
								.append("是否喂养宠物:"+havePet+";")
								.append("是否意见领袖:"+opinionLeader+";")
								.append("业余爱好:"+hobby+";")
								.append("关联房产:"+associatedProperty+";")
								.append("欠费原因:"+reasonArrears+";");
							reservedField.setActiveGroup(StringUtils.isNotEmpty(activeGroup) ? 
									activeGroup : "");
							reservedField.setAssociatedProperty(StringUtils.isNotEmpty(associatedProperty) ? 
									associatedProperty : "");
							reservedField.setComplaintsContent(StringUtils.isNotEmpty(complaintsContent) ? 
									complaintsContent : "");
							reservedField.setComplaintsNumber(StringUtils.isNotEmpty(complaintsNumber) ? 
									complaintsNumber : "");
							reservedField.setDirectGroupAction(StringUtils.isNotEmpty(directGroupAction) ? 
									directGroupAction : "");
							reservedField.setFamilySize(StringUtils.isNotEmpty(familySize) ? 
									familySize : "");
							reservedField.setFamilyMember(StringUtils.isNotEmpty(familyMember) ? 
									familyMember : "");
							reservedField.setHavePet(StringUtils.isNotEmpty(havePet) ? 
									havePet : "");
							reservedField.setHobby(StringUtils.isNotEmpty(hobby) ? 
									hobby : "");
							reservedField.setMajorRepair(StringUtils.isNotEmpty(majorRepair) ? 
									majorRepair : "");
							reservedField.setOpinionLeader(StringUtils.isNotEmpty(opinionLeader) ? 
									opinionLeader : "");
							reservedField.setParticipationGroupAction(StringUtils.isNotEmpty(participationGroupAction) ? 
									participationGroupAction : "");
							reservedField.setProfession(StringUtils.isNotEmpty(profession) ? 
									profession : "");
							reservedField.setRelation(StringUtils.isNotEmpty(relation) ? 
									relation : "");
//							reservedField.setReasonArrears(StringUtils.isNotEmpty(reasonArrears) ? 
//									reasonArrears : "");
							String expandField = mapper.writeValueAsString(reservedField);
							member.setExpandField(expandField);
							
							OperationDiaryForm diaryForm = new OperationDiaryForm("导入业主信息",
										str.toString(),DateUtil.formatLongFormat(new Date()),user.getName(),user.getId(),"U");
							diaryFormList.add(diaryForm);
							member.setOperationLog(json.put("diary", diaryFormList).toString());
						} catch (JsonMappingException e) {
							e.printStackTrace();
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}
					
					}else {
						info.append("【"+phone+"】手机号不存在！");
					}
				}else {
					info.append("【"+unitCode+"】单元编号不存在！");
				}
			}
		});

		res.setRetInfo(StringUtils.isNotEmpty(info.toString()) ? info.toString() : ResponseContext.RES_SUCCESS_CODE_INFO);
		res.setRet(ResponseContext.RES_SUCCESS_CODE);

		return res;
	}
	public File uploadFile(HttpServletRequest request) throws IllegalStateException, IOException {
		MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
		MultipartFile file = multiRequest.getFile("file");
		String tmpFile = System.getProperty("java.io.tmpdir");
		//String tmpFile = "F:/temp";
		tmpFile = tmpFile + File.separator + System.currentTimeMillis() + "-"
				+ DigestUtil.getMD5Str(file.getOriginalFilename()) + "-" + CodeUtil.getId(10000)
				+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
		file.transferTo(new File(tmpFile));
		File excelFile = new File(tmpFile);
		
		return excelFile;
	}
	private String cell2Str(Cell cell) {

		String str = "";
		if (cell.getCellType() == CellType.STRING) {
			str = StringUtils.isNotEmpty(cell.getStringCellValue()) ? cell.getStringCellValue() :"";
		} else if (cell.getCellType() == CellType.NUMERIC) {
			str = String.valueOf(cell.getNumericCellValue());
			if(str.contains("E10")) {
				DecimalFormat decimalFormat=new DecimalFormat("#");
				str = decimalFormat.format(Double.valueOf(str));
			}
		}else if(cell.getCellType() == CellType.FORMULA){
			str = cell.getCellFormula();
		}else if(cell.getCellType() == CellType.BOOLEAN){
			str=cell.getBooleanCellValue()+"";
		}else if(cell.getCellType() == CellType.ERROR){
			str=cell.getErrorCellValue()+"";
		}else if(cell.getCellType() == CellType.BLANK || cell.getCellType() == CellType. _NONE){
			str="";
		}else {
			//Date date = cell.getDateCellValue();
			//str=DateUtil.format(cell.getDateCellValue(),1);
			str="";
		}
		return str;
	}
	
	
	@SuppressWarnings("unchecked")
	public IResponse exporGradeEstate(CommunityEstateReq req, HttpServletResponse response) {
		ExportExcelRes res = new ExportExcelRes();
		Object userObj = getPrincipal(true);

		List<CommunityEstateEntity> list = communityEstateDao
				.getListByHql("select distinct a FROM CommunityEstateEntity a WHERE (a.estateType IN('别墅','双拼别墅','普通洋房',"
						+ "'独立别墅','联排别墅','高层','') OR  a.estateType IS NULL) AND a.unitCode NOT LIKE'%商铺%' "
						+ "AND a.unitCode NOT LIKE'%发%' AND a.unitCode NOT LIKE'%地%' AND a.state =1 "
						+ " ", "");

		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("unitCode", "单位编号");
		heardMap.put("estateType", "房产类型");
		heardMap.put("estateState", "房屋状态");
		heardMap.put("userName", "业主姓名");
		heardMap.put("phone", "业主电话");
		heardMap.put("grade", "等级");

		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
		
		List<Map<String, Object>> dataBList = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> dataCList = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> dataDList = new ArrayList<Map<String, Object>>();

		list.forEach(o -> {
			boolean oneGrade = false; 
			boolean twoGrade = false;
			boolean threeGrade = false;
			boolean fourGrade = false;
			
			boolean arrearage = false;
			
			
			StringBuilder sql1 = new StringBuilder(
					"select a from CommunityReceivablesEntity a inner join a.estate b inner join a.payItem c where b.id="
							+ o.getId() +" and c.chargeCategory=1 and a.receivableDate >'2018-01-01 00:00:00' "
									+ "AND a.receivableDate <'2025-04-01 00:00:00' AND a.chargeCategory='管理费' and a.receivableAmount!=a.receivedAmount  ORDER BY a.receivableDate DESC");
			List<CommunityReceivablesEntity> rl = communityReceivablesDao.getListByHql(sql1.toString());
			if(null==rl || (null!=rl && rl.size()<=0)) {
				StringBuilder sql = new StringBuilder(
						"select a from CommunityReceivablesEntity a inner join a.estate b inner join a.payItem c where b.id="
								+ o.getId() +" and c.chargeCategory=1 and a.receivableDate >'2018-01-01 00:00:00' "
										+ "AND a.receivableDate <'2025-04-01 00:00:00' AND a.chargeCategory='管理费'  ORDER BY a.receivableDate DESC");
				List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao.getListByHql(sql.toString());
				
				for(CommunityReceivablesEntity r : receivablesList) {
					if(r.getReceivableAmount().compareTo(r.getReceivedAmount())==0) {

						Date oneGradeEndLater = null;

						Date twoGradeEndLater = null;
						Date twoGradeStartLater = null;
						
						Date  threeGradeEndLater = null;
						Date  threeGradeStartLater = null;
						//if(null!=req.getGrade() && req.getGrade()==1) {
							try {
								oneGradeEndLater= DateUtil.parse( DateUtil.format(Date.from(r.getReceivableDate().toInstant()
								.atZone(ZoneId.systemDefault())
								.toLocalDate().plusMonths(1)
								.with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay(ZoneId.systemDefault()).toInstant()),0)+" 23:59:59",1);
								//}else if(null!=req.getGrade() && req.getGrade()==2){
								twoGradeStartLater=Date.from(r.getReceivableDate().toInstant()
					                    .atZone(ZoneId.systemDefault())
					                    .toLocalDate().plusMonths(2).withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
								
								twoGradeEndLater=DateUtil.parse( DateUtil.format(Date.from(r.getReceivableDate().toInstant()
			                    .atZone(ZoneId.systemDefault())
			                    .toLocalDate().plusMonths(6)
			                    .with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay(ZoneId.systemDefault()).toInstant()),0)+" 23:59:59",1);
							//}else if(null!=req.getGrade() && req.getGrade()==3){
								threeGradeStartLater= Date.from(r.getReceivableDate().toInstant()
					                    .atZone(ZoneId.systemDefault())
					                    .toLocalDate().plusMonths(7).withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
								
								threeGradeEndLater= DateUtil.parse( DateUtil.format(Date.from(r.getReceivableDate().toInstant()
			                    .atZone(ZoneId.systemDefault())
			                    .toLocalDate().plusMonths(12)
			                    .with(TemporalAdjusters.lastDayOfMonth()).atStartOfDay(ZoneId.systemDefault()).toInstant()),0)+" 23:59:59",1);
							//}
							} catch (ParseException e) {
								// TODO Auto-generated catch block
								e.printStackTrace();
							}
					
						
						//r.getReceiptReceivablesList().forEach(rr->{
						for(CommunityReceiptReceivablesEntity rr : r.getReceiptReceivablesList()) {
							Date receiptDate = rr.getReceipt().getReceiptDate();
							if(receiptDate.compareTo(oneGradeEndLater)<0||receiptDate.compareTo(oneGradeEndLater)==0) {
								oneGrade = true;
							}else if((receiptDate.compareTo(twoGradeStartLater)>0||receiptDate.compareTo(twoGradeStartLater)==0) &&
									(receiptDate.compareTo(twoGradeEndLater)<0||receiptDate.compareTo(twoGradeEndLater)==0)) {
								twoGrade = true;
							}else if((receiptDate.compareTo(threeGradeStartLater)>0||receiptDate.compareTo(threeGradeStartLater)==0) &&
									(receiptDate.compareTo(threeGradeEndLater)<0||receiptDate.compareTo(threeGradeEndLater)==0)){
								threeGrade = true;
							}else {
								fourGrade = true;
								//break;
							}
						}
					}else {
						arrearage = true;
						break;
					}

				}
				if(!arrearage&&oneGrade &&!twoGrade&& !threeGrade &&!fourGrade) {
					Map<String, Object>  map =getMap(o);
					map.put("grade", "A");
					dataList.add(map);
				}else if(!arrearage&&oneGrade && twoGrade&& !threeGrade &&!fourGrade) {
					Map<String, Object>  map =getMap(o);
					map.put("grade", "B");
					dataBList.add(map);
				}else if(!arrearage&&oneGrade && twoGrade&& threeGrade &&!fourGrade) {
					Map<String, Object>  map =getMap(o);
					map.put("grade", "C");
					dataCList.add(map);
					
				}else if(!arrearage&&oneGrade && twoGrade&& threeGrade &&fourGrade) {
					Map<String, Object>  map =getMap(o);
					map.put("grade", "D");
					dataDList.add(map);
				}
			}
			
			
//			if((null!=req.getGrade() && req.getGrade()==1 && oneGrade &&!twoGrade&& !threeGrade &&!fourGrade) ||
//					(null!=req.getGrade() && req.getGrade()==2 && oneGrade && twoGrade&& !threeGrade &&!fourGrade) ||
//					(null!=req.getGrade() && req.getGrade()==3 && oneGrade && twoGrade&& threeGrade &&!fourGrade)
//					) {
			
				

		});
		dataList.addAll(dataBList);
		dataList.addAll(dataCList);
		dataList.addAll(dataDList);
		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"E:\\", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

		return res;
	}
	
	public Map<String, Object> getMap (CommunityEstateEntity o){
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("unitCode", o.getUnitCode());
		map.put("estateType", o.getEstateType());
		if (null != o.getEstateState()) {
			switch (o.getEstateState()) {
			case 0:
				map.put("estateState", "未收楼");
				break;
			case 1:
				if (o.getBuilding().getBuildingType() == 3) {
					map.put("estateState", "出售");
				} else {
					map.put("estateState", "已入住");
				}
				break;
			case 2:
				map.put("estateState", "装修中");
				break;
			case 3:
				map.put("estateState", "出租");
				break;
			case 4:
				map.put("estateState", "离退");
				break;
			case 5:
				map.put("estateState", "自主");
				break;
			case 6:
				map.put("estateState", "空置");
				break;
			default:
				map.put("estateState", "其他");
				break;
			}
		} else {
			map.put("estateState", "");
		}

		map.put("buildingName", (null != o.getBuilding() ? o.getBuilding().getBuildingName() : ""));
		map.put("floor", null!=o.getFloor() ? o.getFloor().toString():"");
		map.put("roomNumber", null!=o.getRoomNumber() ? o.getRoomNumber():"");
		map.put("buildingArea", null!=o.getBuildingArea() ? o.getBuildingArea().toString():"");
		map.put("usableArea", null!=o.getUsableArea()? o.getUsableArea().toString():"");
		map.put("additionalArea", null!=o.getAdditionalArea()? o.getAdditionalArea().toString():"");
		o.getMemberPropertyList().forEach(p -> {
			if (p.getIsCurrentMember() == 1) {
				map.put("parentUnitCode",
						null != p.getParentProperty()
								? ((CommunityEstateEntity) p.getParentProperty()).getUnitCode()
								: "");
				map.put("userName", null != p.getMember() ? p.getMember().getUserName() : "");
				map.put("phone", null != p.getMember() ? p.getMember().getPhone() : "");
				map.put("billingDate",
						null != p.getBillingDate() ? DateUtil.formatShortFormat(p.getBillingDate()) : "");
				map.put("endDate", null != p.getEndDate() ? DateUtil.formatShortFormat(p.getEndDate()) : "");
				if (StringUtils.isNotEmpty(p.getCarInfo())) {
					try {
						Map<String, String> carInfo = mapper.readValue(p.getCarInfo(), Map.class);
						map.put("licensePlateNo", carInfo.get("licensePlateNo"));
						map.put("actualOwner", carInfo.containsKey("actualOwner")? carInfo.get("actualOwner"):"");
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
				} else {
					map.put("licensePlateNo", "");
				}
			}
		});
		return map;
	}
	
	
	@SuppressWarnings("unchecked")
	public IResponse exportApportionmentScopeEstateList(CommunityEstateReq req, HttpServletResponse response) {
		ExportExcelRes res = new ExportExcelRes();
		if(StringUtils.isEmpty(req.getPayItemsIdList())) {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo("收费项目ID"+ResponseContext.RES_NULL_ERROR_INFO);
			return res;
		}
		Object userObj = getPrincipal(true);
		List<CommunityEstateEntity> list = communityEstateDao
				.getListByHql("select distinct a " + queryCondition(req, userObj), "");

		Map<String, String> heardMap = new LinkedHashMap<String, String>();
		heardMap.put("unitCode", "单位编号");
		heardMap.put("floor", "楼层");
		heardMap.put("roomNumber", "房号");
		heardMap.put("buildingArea", "建筑面积(m²)");
		heardMap.put("usableArea", "使用面积(m²)");
		heardMap.put("estateType", "房产类型");
		heardMap.put("estateState", "房屋状态");
		
		List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();

		list.forEach(o -> {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("unitCode", o.getUnitCode());
			map.put("estateType", o.getEstateType());
			map.put("floor", null!=o.getFloor() ? o.getFloor().toString():"");
			map.put("roomNumber", null!=o.getRoomNumber() ? o.getRoomNumber():"");
			map.put("buildingArea", null!=o.getBuildingArea() ? o.getBuildingArea().toString():"");
			map.put("usableArea", null!=o.getUsableArea()? o.getUsableArea().toString():"");
			//map.put("additionalArea", null!=o.getAdditionalArea()? o.getAdditionalArea().toString():"");
			if (null != o.getEstateState()) {
				switch (o.getEstateState()) {
				case 0:
					map.put("estateState", "未收楼");
					break;
				case 1:
					if (o.getBuilding().getBuildingType() == 3) {
						map.put("estateState", "出售");
					} else {
						map.put("estateState", "已入住");
					}
					break;
				case 2:
					map.put("estateState", "装修中");
					break;
				case 3:
					map.put("estateState", "出租");
					break;
				case 4:
					map.put("estateState", "离退");
					break;
				case 5:
					map.put("estateState", "自主");
					break;
				case 6:
					map.put("estateState", "空置");
					break;
				default:
					map.put("estateState", "其他");
					break;
				}
			} else {
				map.put("estateState", "");
			}

			dataList.add(map);
		});

		try {
			ExcelExportUtil.exportExcelFile(heardMap, dataList, Short.valueOf("9"), Short.valueOf("500"), null, "", "",
					"", response);
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
			res.setRetInfo("导出异常！");
		}

		return res;
	}
}
