package com.foshan.service.community;

import com.foshan.form.community.request.CommunityPaymentRecordReq;
import com.foshan.form.community.request.CommunityPaymentReq;
import com.foshan.form.request.PaymentNotifyReq;
import com.foshan.form.response.IResponse;

public interface ICommunityPaymentRecordService {
    public IResponse getCommunityPaymentRecordList(CommunityPaymentRecordReq req);
	public IResponse addCommunityPaymentRecord(CommunityPaymentRecordReq req);
	public IResponse modifyCommunityPaymentRecord(CommunityPaymentRecordReq req);
	public IResponse deleteCommunityPaymentRecord(CommunityPaymentRecordReq req);
	public IResponse getCommunityPaymentRecordInfo(CommunityPaymentRecordReq req);
	public IResponse addCommunityReceive(CommunityPaymentReq req) throws RuntimeException; 
	public IResponse paymentNotify(PaymentNotifyReq req);
	public IResponse queryPaymentResult(CommunityPaymentRecordReq req);
	public IResponse addCommunityReceiveByMember(CommunityPaymentReq req) throws RuntimeException;
	//public IResponse addReceivablesNO(CommunityReceivablesReq req);
	public IResponse getCommunityPaymentRecordStatusByCode(CommunityPaymentRecordReq req);
}

