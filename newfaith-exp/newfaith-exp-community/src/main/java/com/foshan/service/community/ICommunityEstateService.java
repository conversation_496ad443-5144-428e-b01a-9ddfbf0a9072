package com.foshan.service.community;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityEstateReq;
import com.foshan.form.community.request.CommunityProprietorInfoReq;
import com.foshan.form.response.IResponse;

public interface ICommunityEstateService {
	public IResponse getCommunityEstateList(CommunityEstateReq req);
	public IResponse addCommunityEstate(CommunityEstateReq req);
	public IResponse modifyCommunityEstate(CommunityEstateReq req);
	public IResponse deleteCommunityEstate(CommunityEstateReq req);
	public IResponse getCommunityEstateInfo(CommunityEstateReq req);
	public IResponse getCommunityEstateTree(CommunityEstateReq req);
	public IResponse getCommunityEstateUnitCodeList(CommunityEstateReq req);
	public IResponse estateBindingPayItems(CommunityEstateReq req);
	public IResponse getSelectedCommunityEstateTree(CommunityEstateReq req) ;
	public IResponse estateUnbindingPayItems(CommunityEstateReq req);
	public IResponse exportEstateList(CommunityEstateReq req,HttpServletResponse response);
	public  IResponse sendCommunitySms(CommunityEstateReq req);
	public IResponse getProprietorInfoList(CommunityEstateReq req);
	public IResponse exporProprietorInfo(CommunityEstateReq req, HttpServletResponse response);
	public IResponse importProprietorInfo(HttpServletRequest request);
	public IResponse modifyProprietorInfo(CommunityProprietorInfoReq req);
	public IResponse getProprietorInfo(CommunityEstateReq req);
	public IResponse exporGradeEstate(CommunityEstateReq req,HttpServletResponse response);
	public IResponse exportApportionmentScopeEstateList(CommunityEstateReq req, HttpServletResponse response);
}
