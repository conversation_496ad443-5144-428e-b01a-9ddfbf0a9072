package com.foshan.service.community;

import com.foshan.form.community.request.CommunityDecorationItemsReq;
import com.foshan.form.response.IResponse;

public interface ICommunityDecorationItemsService {
    public IResponse getCommunityDecorationItemsList(CommunityDecorationItemsReq req);
	public IResponse addCommunityDecorationItems(CommunityDecorationItemsReq req);
	public IResponse modifyCommunityDecorationItems(CommunityDecorationItemsReq req);
	public IResponse deleteCommunityDecorationItems(CommunityDecorationItemsReq req);
	public IResponse getCommunityDecorationItemsInfo(CommunityDecorationItemsReq req);
}

