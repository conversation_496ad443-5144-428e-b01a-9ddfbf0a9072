package com.foshan.service.community;

import com.foshan.form.community.request.MessageAccountReq;
import com.foshan.form.response.IResponse;

public interface IMessageAccountService {
    public IResponse getMessageAccountList(MessageAccountReq req);
	public IResponse addMessageAccount(MessageAccountReq req);
	public IResponse modifyMessageAccount(MessageAccountReq req);
	public IResponse deleteMessageAccount(MessageAccountReq req);
	public IResponse getMessageAccountInfo(MessageAccountReq req);
}

