package com.foshan.service.community.impl.statistics;

import static java.util.stream.Collectors.groupingBy;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.foshan.form.community.statistics.CommunityReceivablesChangesVo;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;
import com.foshan.util.community.CommunityCache;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ReceivablesChangesUtil {
	public static String getReceivablesChangesSql(Integer[] districtIds, String receivableStartDate,
			String receivableEndDate, String changeStartDate, String changeEndDate) {
		StringBuilder sql = new StringBuilder("SELECT a.districtcode,a.districtname,b.buildingname,c.unitcode,")
				.append("d.payitemsname,d.receivableamount,YEAR(d.receivabledate) AS receivableyear,")
				.append("MONTH(d.receivabledate) AS receivablemonth,d.receivabledate,d.changedate,")
				.append("d.changeamount,d.comment,d.recorder ")
				.append("FROM t_community_district a ")
				.append("INNER JOIN t_community_building b ON a.id =b.districtid AND a.id<>11 ")
				.append(null != districtIds && districtIds.length > 0
						? "and a.id in(" + StringUtils.join(districtIds, ",") + ") "
						: " ")
				.append("INNER JOIN t_community_property c ON b.id=c.buildingid ")
				.append("INNER JOIN t_community_receivables_changes d ON d.state=1 AND d.changetype=0 AND c.id=d.estateid ")
				.append(" and d.receivableDate>='" + receivableStartDate + " 00:00:00' ")
				.append(" and d.receivableDate<='" + receivableEndDate + " 23:59:59' ")
				.append(" and d.changeDate>='" + changeStartDate + " 00:00:00' ")
				.append(" and d.changeDate<='" + changeEndDate + " 23:59:59' ")
				.append("order by a.districtOrder,b.buildingOrder,c.unitCode,d.changeDate,d.receivableDate");

		return sql.toString();
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static void transReceivableChangeExcel(String[] titles, HttpServletResponse response) {
		if (CommunityCache.cache.containsKey("减免透视")) {
			Map<String, Collection> temp = CommunityCache.cache.get("减免透视");
			LinkedList<CommunityReceivablesChangesVo> voList = new LinkedList<>();

			for (String o : temp.keySet()) {
				voList = (LinkedList<CommunityReceivablesChangesVo>) temp.get(o);
			}

			Map<String, List<CommunityReceivablesChangesVo>> receiptMap = (HashMap<String, List<CommunityReceivablesChangesVo>>) voList
					.stream().collect(groupingBy(CommunityReceivablesChangesVo::getNewDistrictCode));

			if (receiptMap.size() > 1) {
				receiptMap.put("000全区", voList);
			}

			TreeSet<String> keys = new TreeSet<>(Comparator.naturalOrder());
			receiptMap.keySet().forEach(o -> {
				keys.add(o);
			});

			XSSFWorkbook wb = new XSSFWorkbook();

			for (String o : keys) {

				LinkedList<CommunityReceivablesChangesVo> tempList = new LinkedList(receiptMap.get(o));

				Sheet sheet = wb.createSheet(o.substring(3));

				Map<String, XSSFCellStyle> cellStyle = ExcelUtil.getExcelStyle(wb);

				Row title = sheet.createRow(0);
				for (int i = 0; i < titles.length; i++) {
					Cell tt = title.createCell(i, CellType.STRING);
					tt.setCellValue(titles[i]);
					tt.setCellStyle(cellStyle.get("title"));
				}

				int i = 1;
				for (CommunityReceivablesChangesVo vo : tempList) {
					Row row = sheet.createRow(i);
					// 楼盘名称
					Cell cell1 = row.createCell(0, CellType.STRING);
					cell1.setCellStyle(cellStyle.get("cell_left"));
					cell1.setCellValue(vo.getDistrictName());

					// 楼阁
					Cell cell2 = row.createCell(1, CellType.STRING);
					cell2.setCellStyle(cellStyle.get("cell_left"));
					cell2.setCellValue(vo.getBuildingName());

					// 单元编号
					Cell cell3 = row.createCell(2, CellType.STRING);
					cell3.setCellStyle(cellStyle.get("cell_left"));
					cell3.setCellValue(vo.getUnitCode());

					// 收费项目
					Cell cell4 = row.createCell(3, CellType.STRING);
					cell4.setCellStyle(cellStyle.get("cell_left"));
					cell4.setCellValue(vo.getPayItemsName());

					// 应收金额
					Cell cell5 = row.createCell(4, CellType.STRING);
					cell5.setCellStyle(cellStyle.get("cell_right2"));
					cell5.setCellValue(new BigDecimal(vo.getReceivableAmount()).doubleValue());

					// 应收年
					Cell cell6 = row.createCell(5, CellType.STRING);
					cell6.setCellStyle(cellStyle.get("cell_left"));
					cell6.setCellValue(vo.getReceivableYear());

					// 应收月
					Cell cell_add1 = row.createCell(6, CellType.STRING);
					cell_add1.setCellStyle(cellStyle.get("cell_left"));
					cell_add1.setCellValue(vo.getReceivableMonth());

					// 应收日期
					Cell cell6_add2 = row.createCell(7, CellType.STRING);
					cell6_add2.setCellStyle(cellStyle.get("cell_left"));
					cell6_add2.setCellValue(vo.getReceivableDate());

					// 减免日期
					Cell cell6_add3 = row.createCell(8, CellType.STRING);
					cell6_add3.setCellStyle(cellStyle.get("cell_left"));
					cell6_add3.setCellValue(vo.getChangeDate());

					// 减免金额
					Cell cell7 = row.createCell(9, CellType.NUMERIC);
					cell7.setCellStyle(cellStyle.get("cell_right2"));
					cell7.setCellValue(new BigDecimal(vo.getChangeAmount()).doubleValue());

					// 备注
					Cell cell8 = row.createCell(10, CellType.NUMERIC);
					cell8.setCellStyle(cellStyle.get("cell_left"));
					cell8.setCellValue(vo.getComment());

					// 经办人
					Cell cell9 = row.createCell(11, CellType.NUMERIC);
					cell9.setCellStyle(cellStyle.get("cell_left"));
					cell9.setCellValue(vo.getRecorder());
					i++;
				}

				// 合计行
				Row total = sheet.createRow(i);
				Cell totalStr = total.createCell(0, CellType.STRING);
				totalStr.setCellValue("合计");
				totalStr.setCellStyle(cellStyle.get("total_double"));

				sheet.setColumnWidth(3, 15 * 256);

				Cell t4 = total.createCell(4, CellType.NUMERIC);
				t4.setCellStyle(cellStyle.get("total_double"));
				t4.setCellFormula("SUM("+sheet.getRow(1).getCell(4).getAddress()+":"+sheet.getRow(i-1).getCell(4).getAddress()+")");
				sheet.autoSizeColumn(4, true);

				Cell t5 = total.createCell(5, CellType.STRING);
				t5.setCellStyle(cellStyle.get("total_double"));

				Cell t6 = total.createCell(6, CellType.STRING);
				t6.setCellStyle(cellStyle.get("total_double"));

				Cell t7 = total.createCell(7, CellType.STRING);
				t7.setCellStyle(cellStyle.get("total_double"));

				Cell t8 = total.createCell(8, CellType.STRING);
				t8.setCellStyle(cellStyle.get("total_double"));

				CellRangeAddress region3 = new CellRangeAddress(i, i, 0, 3);
				sheet.addMergedRegion(region3);
				ExcelUtil.addMergeCellBorder(region3, sheet);

				Cell t9 = total.createCell(9, CellType.NUMERIC);
				t9.setCellStyle(cellStyle.get("total_double"));
				t9.setCellFormula("SUM("+sheet.getRow(1).getCell(9).getAddress()+":"+sheet.getRow(i-1).getCell(9).getAddress()+")");
				sheet.autoSizeColumn(9, true);

				Cell t10 = total.createCell(10, CellType.STRING);
				t10.setCellStyle(cellStyle.get("total_double"));
				sheet.setColumnWidth(10, 50 * 256);

				Cell t11 = total.createCell(11, CellType.STRING);
				t11.setCellStyle(cellStyle.get("total_double"));
				sheet.autoSizeColumn(11, true);

				// 冻结首行
				sheet.createFreezePane(0, 1, 0, 1);

				// 增加筛选框
				CellRangeAddress c = CellRangeAddress.valueOf("A1:L1");
				sheet.setAutoFilter(c);

			}
			String fileName = "减免透视" + DateUtil.formatByStyle(new Date(), "_yyyy_MM_dd_HH_mm_ss") + ".xlsx";

			try {
				ExcelExportUtil.export(response, wb, fileName);
			} catch (IOException e) {
				log.error(e.getMessage());
			}

		}
	}
}
