package com.foshan.service.community.impl;

import java.sql.Timestamp;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.WarningMessageEntity;
import com.foshan.form.community.EventRecordForm;
import com.foshan.form.community.WarningMessageForm;
import com.foshan.form.community.WarningMessageRecordForm;
import com.foshan.form.community.request.WarningMessageReq;
import com.foshan.form.community.response.warningMessage.GetWarningMessageInfoRes;
import com.foshan.form.community.response.warningMessage.GetWarningMessageListRes;
import com.foshan.form.community.response.warningMessage.GetWarningMessageRecordList;
import com.foshan.form.community.response.warningMessage.ModifyWarningMessageRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.IWarningMessageService;
import com.foshan.util.DateUtil;

@Transactional
@Service("warningMessageService")
public class WarningMessageServiceImpl extends GenericCommunityService implements IWarningMessageService{

	@Override
	public IResponse getWarningMessageList(WarningMessageReq req) {
		GetWarningMessageListRes res = new GetWarningMessageListRes();
		Page<WarningMessageEntity> page = new Page<WarningMessageEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from WarningMessageEntity a where 1=1 ");
		hql.append(StringUtils.isNotEmpty(req.getEventType()) ? " and a.eventType like'%"+req.getEventType()+"%'" :"")
		.append(StringUtils.isNotEmpty(req.getNotEventType()) ? " and a.eventType not like'"+req.getNotEventType()+"'" :"");
		hql.append(" ORDER BY a.id desc");
		page = warningMessageDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			WarningMessageForm warningMessageForm = new WarningMessageForm();
			warningMessageForm.setWarningMessageId(o.getId());
            warningMessageForm.setAlarmTime(o.getAlarmTime());
            warningMessageForm.setDeviceCode(o.getDeviceCode());
            warningMessageForm.setDeviceName(o.getDeviceName());
            warningMessageForm.setEventRecordId(o.getEventRecordId());
            warningMessageForm.setEventType(o.getEventType());
            warningMessageForm.setJsonData(o.getJsonData());
            warningMessageForm.setLevelName(o.getLevelName());
            warningMessageForm.setHandleComment(o.getHandleComment());
            warningMessageForm.setHandleState(o.getHandleState());
            warningMessageForm.setHandleTime(null != o.getHandleTime() ? DateUtil.formatLongFormat(o.getHandleTime()) : "");
			res.getWarningMessageList().add(warningMessageForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@Override
	public IResponse getWarningMessageRecordList(WarningMessageReq req) {
		GetWarningMessageRecordList res = new GetWarningMessageRecordList();
		Page<WarningMessageEntity> page = new Page<WarningMessageEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from WarningMessageEntity a where 1=1 ");
		hql.append(StringUtils.isNotEmpty(req.getStartTime()) ? " and a.alarmTime>='"+req.getStartTime()+"'" :"")
			.append(StringUtils.isNotEmpty(req.getEndTime()) ? " and a.alarmTime<='"+req.getEndTime()+"'" :"")
			.append(null!=req.getHandleState() ? " a.handleState="+req.getHandleState():"")
			.append(StringUtils.isNotEmpty(req.getEventType()) ? " and a.eventType like'%"+req.getEventType()+"%'" :"");
		hql.append(" ORDER BY a.id desc");
		page = warningMessageDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		WarningMessageRecordForm warningMessageRecordForm = new WarningMessageRecordForm();
		warningMessageRecordForm.setNow_count(page.getPageSize());
		warningMessageRecordForm.setNow_curr(page.getCurrentPage());
		warningMessageRecordForm.setSum_count(page.getTotalCount());
		if(page.getCurrentPage()==1) {
			warningMessageRecordForm.set_first_page(true);
		}else {
			warningMessageRecordForm.set_first_page(false);
		}
		page.getResultList().forEach(o -> {
			EventRecordForm eventRecordForm = new EventRecordForm();
			
			eventRecordForm.setWarningMessageId(o.getId());
			eventRecordForm.setEvent_processing_status_id(null);
			if(StringUtils.isNotEmpty(o.getEventRecordId()) && o.getEventRecordId().equals("eventRecordId")) {
				eventRecordForm.setEvent_record_id(0);
				eventRecordForm.setDevice_name("银龄守护卡");
				eventRecordForm.setEvent_metadata(o.getDeviceName());
			}else {
				eventRecordForm.setEvent_record_id(StringUtils.isNotEmpty(o.getEventRecordId()) 
						? Integer.valueOf(o.getEventRecordId()): 0);
				eventRecordForm.setDevice_name(o.getDeviceName());
				eventRecordForm.setEvent_metadata("");
			}
			if(null != o.getDevice()&& null!=o.getDevice().getEstate()) {
				eventRecordForm.setUnitCode(o.getDevice().getEstate().getUnitCode());
			}else {
				eventRecordForm.setUnitCode("");
			}
			if(null!=o.getDevice() && null!=o.getDevice().getEstate()) {
				CommunityEstateEntity estate = o.getDevice().getEstate(); 
				eventRecordForm.setUnitCode(estate.getUnitCode());
				eventRecordForm.setUnitAddress(estate.getBuilding().getDistrict().getDistrictName()
						+ estate.getBuilding().getBuildingName() + estate.getFloor() + estate.getRoomNumber());
			}

			eventRecordForm.setEvent_time(o.getAlarmTime());
			eventRecordForm.setEvent_type_name(o.getEventType());
			eventRecordForm.setGeneral_resources_min_url("");
			eventRecordForm.setEvent_processing_status_id(0);
			warningMessageRecordForm.getList_data().add(eventRecordForm);
		});
		try {
			res.setData(mapper.writeValueAsString(warningMessageRecordForm));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

/*	@Override
	public IResponse addWarningMessage(WarningMessageReq req) {
		AddWarningMessageRes res = new AddWarningMessageRes();
		//if () {
			WarningMessageEntity warningMessage = new WarningMessageEntity();
			
            warningMessage.setAlarmTime(req.getAlarmTime());
            warningMessage.setDeviceId(req.getDeviceId());
            warningMessage.setDeviceName(req.getDeviceName());
            warningMessage.setEventRecordId(req.getEventRecordId());
            warningMessage.setEventType(req.getEventType());
            warningMessage.setJsonData(req.getJsonData());
            warningMessage.setLevelName(req.getLevelName());
			warningMessageDao.save(warningMessage);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}*/

	@Override
	public IResponse modifyWarningMessage(WarningMessageReq req) {
		ModifyWarningMessageRes res = new ModifyWarningMessageRes();
		if (null!=req.getWarningMessageId() ) {
			WarningMessageEntity warningMessage = warningMessageDao.get(req.getWarningMessageId()) ;
			if(null != warningMessage){
                warningMessage.setHandleComment(StringUtils.isNotEmpty(req.getHandleComment()) ?req.getHandleComment() :"");
                warningMessage.setHandleState(null != req.getHandleState() ? req.getHandleState() : warningMessage.getHandleState());
                warningMessage.setHandleTime(new Timestamp(new Date().getTime()));
				res.setWarningMessageId(warningMessage.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

/*	@Override
	public IResponse deleteWarningMessage(WarningMessageReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getWarningMessageId()) {
		WarningMessageEntity warningMessage = warningMessageDao.get(req.getWarningMessageId());
			if (null != warningMessage) {
				warningMessageDao.deleteById(req.getWarningMessageId());
				//warningMessage.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}*/

	@Override
	public IResponse getWarningMessageInfo(WarningMessageReq req) {
		GetWarningMessageInfoRes res = new GetWarningMessageInfoRes();
		if (null != req.getWarningMessageId()) {
			WarningMessageEntity warningMessage = warningMessageDao.get(req.getWarningMessageId());
			if (null != warningMessage) {
				WarningMessageForm warningMessageForm = new WarningMessageForm();
				if(null!=warningMessage.getDevice() && null!=warningMessage.getDevice().getEstate()) {
					CommunityEstateEntity estate = warningMessage.getDevice().getEstate(); 
					warningMessageForm.setUnitCode(estate.getUnitCode());
					warningMessageForm.setUnitAddress(estate.getBuilding().getDistrict().getDistrictName()
							+ estate.getBuilding().getBuildingName() + estate.getFloor() + estate.getRoomNumber());
				}
				warningMessageForm.setWarningMessageId(warningMessage.getId());
                warningMessageForm.setAlarmTime(warningMessage.getAlarmTime());
                warningMessageForm.setDeviceCode(warningMessage.getDeviceCode());
                warningMessageForm.setDeviceName(warningMessage.getDeviceName());
                warningMessageForm.setEventRecordId(warningMessage.getEventRecordId());
                warningMessageForm.setEventType(warningMessage.getEventType());
                warningMessageForm.setJsonData(warningMessage.getJsonData());
                warningMessageForm.setLevelName(warningMessage.getLevelName());
                warningMessageForm.setHandleComment(warningMessage.getHandleComment());
                warningMessageForm.setHandleState(warningMessage.getHandleState());
                warningMessageForm.setHandleTime(null != warningMessage.getHandleTime() ? 
                		DateUtil.formatLongFormat(warningMessage.getHandleTime()) : "");
				res.setWarningMessageForm(warningMessageForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}