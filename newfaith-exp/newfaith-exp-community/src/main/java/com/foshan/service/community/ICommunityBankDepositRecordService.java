package com.foshan.service.community;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityBankDepositRecordReq;
import com.foshan.form.response.IResponse;

public interface ICommunityBankDepositRecordService {
    public IResponse getCommunityBankDepositRecordList(CommunityBankDepositRecordReq req);
	public IResponse addCommunityBankDepositRecord(CommunityBankDepositRecordReq req);
	public IResponse modifyCommunityBankDepositRecord(CommunityBankDepositRecordReq req);
	public IResponse deleteCommunityBankDepositRecord(CommunityBankDepositRecordReq req);
	public IResponse getCommunityBankDepositRecordInfo(CommunityBankDepositRecordReq req);
	public IResponse  exportCommunityBankDepositRecordList(CommunityBankDepositRecordReq req,HttpServletResponse response);
}

