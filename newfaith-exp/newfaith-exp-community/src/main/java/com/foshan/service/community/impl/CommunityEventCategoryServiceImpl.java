package com.foshan.service.community.impl;

import static java.util.Comparator.comparingInt;
import static java.util.stream.Collectors.groupingBy;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityEventCategoryEntity;
import com.foshan.entity.community.CommunityEventCategoryItemsEntity;
import com.foshan.entity.community.CommunityEventsEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityEventCategoryForm;
import com.foshan.form.community.CommunityEventCategoryItemsForm;
import com.foshan.form.community.request.CommunityEventCategoryReq;
import com.foshan.form.community.response.communityEventCategory.AddCommunityEventCategoryRes;
import com.foshan.form.community.response.communityEventCategory.GetCommunityEventCategoryInfo;
import com.foshan.form.community.response.communityEventCategory.GetCommunityEventCategoryListRes;
import com.foshan.form.community.response.communityEventCategory.ModifyCommunityEventCategoryRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityEventCategoryService;


@Transactional
@Service("communityEventCategoryService")
public  class CommunityEventCategoryServiceImpl extends GenericCommunityService implements ICommunityEventCategoryService {

	@SuppressWarnings("unchecked")
	@Override
	public IResponse getCommunityEventCategoryList(CommunityEventCategoryReq req) {
		GetCommunityEventCategoryListRes res = new GetCommunityEventCategoryListRes();
		Page<CommunityEventCategoryEntity> page = new Page<CommunityEventCategoryEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);

		StringBuilder hql = new StringBuilder("select distinct a from CommunityEventCategoryEntity a ");
	
		hql.append(" where").append(null != req.getState() ? "  a.state=" + req.getState()  :
			"  a.state="+EntityContext.RECORD_STATE_VALID)
		.append( null!=req.getIsDispatching() ? " and a.isDispatching=" + req.getIsDispatching() :"")
		.append(null!=req.getParentEventCategoryId() ?" and a.parentEventCategory.id="+req.getParentEventCategoryId():" and a.parentEventCategory is null")
		.append(StringUtils.isNotEmpty(req.getCategoryName()) ?
				" and a.categoryName like '%" + req.getCategoryName() + "%'" :"")
		.append(null!=req.getCategoryType()? " and categoryType="+req.getCategoryType():" and (categoryType=0 or categoryType is null)");

		page = communityEventCategoryDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		ObjectMapper mapper = new ObjectMapper();
		page.getResultList().forEach(o -> {
			CommunityEventCategoryForm eventCategoryForm = new CommunityEventCategoryForm(o.getId(),
					o.getCategoryName(),o.getCategoryLevel(),o.getIsDispatching(),
					sdf.format(o.getCreateTime()),sdf.format(o.getLastModifyTime()),o.getIcon());
			eventCategoryForm.setOrders(o.getOrders());
			o.getEventCategoryItemsList().forEach(p->{
				List<Object> optionslList = new ArrayList<Object>();
				try {
					if(StringUtils.isNoneEmpty(p.getOptions())) {
						optionslList =mapper.readValue(p.getOptions(), ArrayList.class);
					}
				} catch (JsonParseException e) {
					e.printStackTrace();
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JSONException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
				CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(p.getId(),
						p.getItemName(),p.getItemkey(),p.getOrders(),p.getDataType(),optionslList,p.getIsRequiredng()) ;
				eventCategoryItemsForm.setReflectionField(p.getReflectionField());
				eventCategoryItemsForm.setSubEventCategoryItemsList(getSubEventCategoryItemsList(p.getSubEventCategoryItemsList()));
				eventCategoryForm.getEventCategoryItemList().add(eventCategoryItemsForm);
			});
			Map<Integer, List<CommunityEventsEntity>> collect1 = (Map<Integer, List<CommunityEventsEntity>>) o.getCommunityEventsList()
			    .parallelStream().collect(groupingBy(CommunityEventsEntity::getEventState));
			eventCategoryForm.setPendingCount(collect1.containsKey(1) ? collect1.get(1).size() : 0);
			eventCategoryForm.setCompleteCount(collect1.containsKey(5) ? collect1.get(5).size() : 0);
			eventCategoryForm.setCategoryType(eventCategoryForm.getCategoryType());
			eventCategoryForm.getEventCategoryItemList().sort(comparingInt(CommunityEventCategoryItemsForm::getOrders));
			eventCategoryForm.setCategoryType(o.getCategoryType());
			
			if(null != o.getParentEventCategory()) {
				CommunityEventCategoryForm parentEventCategoryForm = new CommunityEventCategoryForm(
					o.getParentEventCategory().getId(),o.getParentEventCategory().getCategoryName(),
					o.getParentEventCategory().getCategoryLevel(),o.getParentEventCategory().getIsDispatching(),
					sdf.format(o.getParentEventCategory().getCreateTime()),
					sdf.format(o.getParentEventCategory().getLastModifyTime()),o.getParentEventCategory().getIcon());
				eventCategoryForm.setParentEventCategoryForm(parentEventCategoryForm);
			}
			if(null != req.getDepth() && req.getDepth()>=1) {
				eventCategoryForm.setSubEventCategoryList(getSubEventCategoryList(o.getSubEventCategoryList(),req.getDepth()));
			}else if(null == req.getDepth()) {
				eventCategoryForm.setSubEventCategoryList(getSubEventCategoryList(o.getSubEventCategoryList(),2));
			}
			eventCategoryForm.setIsSend(o.getIsSend());
			eventCategoryForm.setDefaultParameter(o.getDefaultParameter());
			eventCategoryForm.getSubEventCategoryList().sort(comparingInt(CommunityEventCategoryForm::getOrders));
			res.getEventCategoryList().add(eventCategoryForm);

		});
		res.getEventCategoryList().sort(comparingInt(CommunityEventCategoryForm::getOrders));
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@SuppressWarnings("unchecked")
	public List<CommunityEventCategoryForm> getSubEventCategoryList(List<CommunityEventCategoryEntity> list,Integer depth){
		List<CommunityEventCategoryForm> subEventCategoryList = new ArrayList<CommunityEventCategoryForm>();
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		depth--;
		for(CommunityEventCategoryEntity o : list){
			CommunityEventCategoryForm eventCategoryForm = new CommunityEventCategoryForm(o.getId(),
					o.getCategoryName(),o.getCategoryLevel(),o.getIsDispatching(),
					sdf.format(o.getCreateTime()),sdf.format(o.getLastModifyTime()),o.getIcon());
			eventCategoryForm.setOrders(o.getOrders());
			Map<Integer, List<CommunityEventsEntity>> collect1 = (Map<Integer, List<CommunityEventsEntity>>) o.getCommunityEventsList()
				    .parallelStream().collect(groupingBy(CommunityEventsEntity::getEventState));
			eventCategoryForm.setPendingCount(collect1.containsKey(1) ? collect1.get(1).size() : 0);
			o.getEventCategoryItemsList().forEach(p->{
				List<Object> optionslList = new ArrayList<Object>();
				try {
					if(StringUtils.isNoneEmpty(p.getOptions())) {
						optionslList =mapper.readValue(p.getOptions(), ArrayList.class);
					}
				} catch (JsonParseException e) {
					e.printStackTrace();
				} catch (JsonMappingException e) {
					e.printStackTrace();
				} catch (JSONException e) {
					e.printStackTrace();
				} catch (IOException e) {
					e.printStackTrace();
				}
				CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(p.getId(),
						p.getItemName(),p.getItemkey(),p.getOrders(),p.getDataType(),optionslList,p.getIsRequiredng()) ;
				eventCategoryItemsForm.setReflectionField(p.getReflectionField());
				eventCategoryItemsForm.setSubEventCategoryItemsList(getSubEventCategoryItemsList(p.getSubEventCategoryItemsList()));
				eventCategoryForm.getEventCategoryItemList().add(eventCategoryItemsForm);
			});
			eventCategoryForm.setCategoryType(depth);
			eventCategoryForm.setCategoryType(o.getCategoryType());
			eventCategoryForm.setIsSend(o.getIsSend());
			eventCategoryForm.setDefaultParameter(o.getDefaultParameter());
			eventCategoryForm.getEventCategoryItemList().sort(comparingInt(CommunityEventCategoryItemsForm::getOrders));
			eventCategoryForm.getSubEventCategoryList().sort(comparingInt(CommunityEventCategoryForm::getOrders));
			if(null != depth && depth>=1) {
				eventCategoryForm.setSubEventCategoryList(getSubEventCategoryList(o.getSubEventCategoryList(),depth));
			}
			subEventCategoryList.add(eventCategoryForm);
		}
		
		return subEventCategoryList;
	}
	
	@SuppressWarnings("unchecked")
	public List<CommunityEventCategoryItemsForm> getSubEventCategoryItemsList(List<CommunityEventCategoryItemsEntity> list){
		List<CommunityEventCategoryItemsForm> subItemsList = new ArrayList<CommunityEventCategoryItemsForm>();
		for(CommunityEventCategoryItemsEntity o : list){
			List<Object> optionslList = new ArrayList<Object>();
			try {
				if(StringUtils.isNoneEmpty(o.getOptions())) {
					optionslList =mapper.readValue(o.getOptions(), ArrayList.class);
				}
			} catch (JsonParseException e) {
				e.printStackTrace();
			} catch (JsonMappingException e) {
				e.printStackTrace();
			} catch (JSONException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(o.getId(),
					o.getItemName(),o.getItemkey(),o.getOrders(),o.getDataType(),optionslList,o.getIsRequiredng());
			eventCategoryItemsForm.setItemTag(StringUtils.isNotEmpty(o.getItemTag()) ? o.getItemTag() :"");
			eventCategoryItemsForm.setItemTag(StringUtils.isNotEmpty(o.getItemTag()) ? o.getItemTag() :"");
			eventCategoryItemsForm.setReflectionField(o.getReflectionField());
			subItemsList.add(eventCategoryItemsForm);
		}
		return subItemsList;
	}

	@Audit(operate = "新增事件类型")
	@Override
	public IResponse addCommunityEventCategory(CommunityEventCategoryReq req) {
		AddCommunityEventCategoryRes res = new AddCommunityEventCategoryRes();
		if (StringUtils.isNotEmpty(req.getCategoryName()) 
				&& null!=req.getIsDispatching() ) {
			CommunityEventCategoryEntity parentEventCategor = null!=req.getParentEventCategoryId() ? 
					communityEventCategoryDao.get(req.getParentEventCategoryId()) : null;
			CommunityEventCategoryEntity eventCategory = new CommunityEventCategoryEntity();
			eventCategory.setCategoryName(req.getCategoryName());
			eventCategory.setIsDispatching(req.getIsDispatching());
			eventCategory.setCategoryLevel(null!=parentEventCategor ? parentEventCategor.getCategoryLevel()+1 : 1);
			eventCategory.setParentEventCategory(parentEventCategor);
			eventCategory.setState(EntityContext.RECORD_STATE_VALID);
			eventCategory.setOrders(null!=req.getOrders() ? req.getOrders() :1);
			eventCategory.setIcon(StringUtils.isNotEmpty(req.getIcon()) ? req.getIcon() : "");
			eventCategory.setLastModifyTime(new Timestamp(new Date().getTime()));
			eventCategory.setCategoryType(req.getCategoryType());
			eventCategory.setCategoryType(null!=req.getCategoryType() ? req.getCategoryType():0);
			eventCategory.setIsSend(null!=req.getIsSend() ? req.getIsSend() :0);
			eventCategory.setDefaultParameter(StringUtils.isNotEmpty(req.getDefaultParameter())? req.getDefaultParameter():"");
			communityEventCategoryDao.save(eventCategory);

			res.setEventCategoryId(eventCategory.getId());
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Audit(operate = "修改事件类型")
	@Override
	public IResponse modifyCommunityEventCategory(CommunityEventCategoryReq req) {
		ModifyCommunityEventCategoryRes res = new ModifyCommunityEventCategoryRes();
		if (null!=req.getEventCategoryId() && StringUtils.isNotEmpty(req.getCategoryName()) 
				&& null!=req.getIsDispatching() ) {
			CommunityEventCategoryEntity eventCategory = communityEventCategoryDao.get(req.getEventCategoryId());
			if(null != eventCategory) {
				CommunityEventCategoryEntity parentEventCategor = null!=req.getParentEventCategoryId() ? 
						communityEventCategoryDao.get(req.getParentEventCategoryId()) : null;
				eventCategory.setCategoryName(req.getCategoryName());
				eventCategory.setIsDispatching(req.getIsDispatching());
				eventCategory.setCategoryLevel(null!=parentEventCategor ? parentEventCategor.getCategoryLevel()+1 : 1);
				eventCategory.setParentEventCategory(parentEventCategor);
				eventCategory.setLastModifyTime(new Timestamp(new Date().getTime()));
				eventCategory.setIcon(StringUtils.isNotEmpty(req.getIcon()) ? req.getIcon() : eventCategory.getIcon());
				eventCategory.setOrders(null!=req.getOrders() ? req.getOrders() : eventCategory.getOrders());
				eventCategory.setLastModifyTime(new Timestamp(new Date().getTime()));
				eventCategory.setCategoryType(null!=req.getCategoryType() ? req.getCategoryType() : eventCategory.getCategoryType());
				eventCategory.setIsSend(null!=req.getIsSend() ? req.getIsSend() :eventCategory.getIsSend());
				eventCategory.setDefaultParameter(StringUtils.isNotEmpty(req.getDefaultParameter())? req.getDefaultParameter():eventCategory.getDefaultParameter());
				communityEventCategoryDao.save(eventCategory);

				res.setEventCategoryId(eventCategory.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Audit(operate = "删除事件类型")
	@Override
	public IResponse deleteCommunityEventCategory(CommunityEventCategoryReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getEventCategoryId()) {
			CommunityEventCategoryEntity eventCategory = communityEventCategoryDao.get(req.getEventCategoryId());
			if (null != eventCategory) {
				eventCategory.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@SuppressWarnings("unchecked")
	//@ResourcePermit
	@Override
	public IResponse getCommunityEventCategoryInfo(CommunityEventCategoryReq req) {
		GetCommunityEventCategoryInfo res = new GetCommunityEventCategoryInfo();
		if (null != req.getEventCategoryId()) {
			CommunityEventCategoryEntity eventCategory = communityEventCategoryDao.get(req.getEventCategoryId());
			if (null != eventCategory) {
				DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
				CommunityEventCategoryForm eventCategoryForm = new CommunityEventCategoryForm(eventCategory.getId(),
						eventCategory.getCategoryName(),eventCategory.getCategoryLevel(),eventCategory.getIsDispatching()
						,sdf.format(eventCategory.getCreateTime()),sdf.format(eventCategory.getLastModifyTime()),eventCategory.getIcon());
				eventCategoryForm.setOrders(eventCategory.getOrders());
				Map<Integer, List<CommunityEventsEntity>> collect1 = (Map<Integer, List<CommunityEventsEntity>>) eventCategory.getCommunityEventsList()
					    .parallelStream().collect(groupingBy(CommunityEventsEntity::getEventState));
				eventCategoryForm.setPendingCount(collect1.containsKey(1) ? collect1.get(1).size() : 0);
				eventCategoryForm.setCategoryType(eventCategory.getCategoryType());
				ObjectMapper mapper = new ObjectMapper();
				eventCategory.getEventCategoryItemsList().forEach(p->{
					List<Object> optionslList = new ArrayList<Object>();
					try {
						if(StringUtils.isNoneEmpty(p.getOptions())) {
							optionslList =mapper.readValue(p.getOptions(), ArrayList.class);
						}
					} catch (JsonParseException e) {
						e.printStackTrace();
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (JSONException e) {
						e.printStackTrace();
					} catch (IOException e) {
						e.printStackTrace();
					}
					CommunityEventCategoryItemsForm eventCategoryItemsForm = new CommunityEventCategoryItemsForm(p.getId(),
							p.getItemName(),p.getItemkey(),p.getOrders(),p.getDataType(),optionslList,p.getIsRequiredng()) ;
					eventCategoryItemsForm.setSubEventCategoryItemsList(getSubEventCategoryItemsList(p.getSubEventCategoryItemsList()));
					eventCategoryForm.getEventCategoryItemList().add(eventCategoryItemsForm);
				});
				eventCategoryForm.getEventCategoryItemList().sort(comparingInt(CommunityEventCategoryItemsForm::getOrders));
				if(null != eventCategory.getParentEventCategory()) {
					CommunityEventCategoryForm parentEventCategoryForm = new CommunityEventCategoryForm(
							eventCategory.getParentEventCategory().getId(),eventCategory.getParentEventCategory().getCategoryName(),
							eventCategory.getParentEventCategory().getCategoryLevel(),eventCategory.getParentEventCategory().getIsDispatching(),
							sdf.format(eventCategory.getParentEventCategory().getCreateTime()),
							sdf.format(eventCategory.getParentEventCategory().getLastModifyTime()),eventCategory.getParentEventCategory().getIcon());
					eventCategoryForm.setParentEventCategoryForm(parentEventCategoryForm);
				}
				if(null != req.getDepth() && req.getDepth()>=1) {
					eventCategoryForm.setSubEventCategoryList(getSubEventCategoryList(eventCategory.getSubEventCategoryList(),req.getDepth()));
				}else if(null == req.getDepth()) {
					eventCategoryForm.setSubEventCategoryList(getSubEventCategoryList(eventCategory.getSubEventCategoryList(),2));
				}
				eventCategoryForm.setIsSend(eventCategory.getIsSend());
				eventCategoryForm.setDefaultParameter(eventCategory.getDefaultParameter());
				eventCategoryForm.getSubEventCategoryList().sort(comparingInt(CommunityEventCategoryForm::getOrders));
				res.setCommunityEventCategoryForm(eventCategoryForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}
