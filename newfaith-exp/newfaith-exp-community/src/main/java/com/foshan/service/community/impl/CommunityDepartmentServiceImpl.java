package com.foshan.service.community.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.form.community.request.CommunityDepartmentReq;
import com.foshan.form.response.IResponse;
import com.foshan.service.community.ICommunityDepartmentService;

@Transactional
@Service("communityDepartmentService")
public class CommunityDepartmentServiceImpl extends GenericCommunityService implements ICommunityDepartmentService{

	@Override
	public IResponse getCommunityDepartmentList(CommunityDepartmentReq req) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public IResponse addCommunityDepartment(CommunityDepartmentReq req) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public IResponse modifyCommunityDepartment(CommunityDepartmentReq req) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public IResponse deleteCommunityDepartment(CommunityDepartmentReq req) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public IResponse getCommunityDepartmentInfo(CommunityDepartmentReq req) {
		// TODO Auto-generated method stub
		return null;
	}

}
