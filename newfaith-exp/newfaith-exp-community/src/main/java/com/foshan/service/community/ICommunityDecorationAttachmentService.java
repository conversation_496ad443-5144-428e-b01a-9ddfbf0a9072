package com.foshan.service.community;

import com.foshan.form.community.request.CommunityDecorationAttachmentReq;
import com.foshan.form.response.IResponse;

public interface ICommunityDecorationAttachmentService {
    public IResponse getCommunityDecorationAttachmentList(CommunityDecorationAttachmentReq req);
	public IResponse addCommunityDecorationAttachment(CommunityDecorationAttachmentReq req);
	public IResponse modifyCommunityDecorationAttachment(CommunityDecorationAttachmentReq req);
	public IResponse deleteCommunityDecorationAttachment(CommunityDecorationAttachmentReq req);
	public IResponse getCommunityDecorationAttachmentInfo(CommunityDecorationAttachmentReq req);
}

