package com.foshan.service.community;

import com.foshan.form.community.request.CommunityRefundNotifyReq;
import com.foshan.form.community.request.CommunityRefundReq;
import com.foshan.form.response.IResponse;

public interface ICommunityRefundService {
    public IResponse getCommunityRefundList(CommunityRefundReq req);
	public IResponse addCommunityRefund(CommunityRefundReq req);
	public IResponse modifyCommunityRefund(CommunityRefundReq req);
	public IResponse deleteCommunityRefund(CommunityRefundReq req);
	public IResponse getCommunityRefundInfo(CommunityRefundReq req);
	public IResponse refundNotify(CommunityRefundNotifyReq req);
}

