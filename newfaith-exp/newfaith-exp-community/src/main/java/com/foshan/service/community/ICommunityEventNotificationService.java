package com.foshan.service.community;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityEventNotificationReq;
import com.foshan.form.response.IResponse;

public interface ICommunityEventNotificationService {
	public IResponse getCommunityEventNotificationList(CommunityEventNotificationReq req);
	public IResponse addCommunityEventNotification(CommunityEventNotificationReq req);
	public IResponse modifyCommunityEventNotification(CommunityEventNotificationReq req);
	public IResponse deleteCommunityEventNotification(CommunityEventNotificationReq req);
	public IResponse getCommunityEventNotificationInfo(CommunityEventNotificationReq req);
	public IResponse exportCommunityEventNotification(CommunityEventNotificationReq req,HttpServletResponse response);
}
