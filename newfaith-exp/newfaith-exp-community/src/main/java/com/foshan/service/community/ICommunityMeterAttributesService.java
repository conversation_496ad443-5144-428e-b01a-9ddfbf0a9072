package com.foshan.service.community;

import com.foshan.form.community.request.CommunityMeterAttributesReq;
import com.foshan.form.response.IResponse;

public interface ICommunityMeterAttributesService {
    public IResponse getCommunityMeterAttributesList(CommunityMeterAttributesReq req);
	public IResponse addCommunityMeterAttributes(CommunityMeterAttributesReq req);
	public IResponse modifyCommunityMeterAttributes(CommunityMeterAttributesReq req);
	public IResponse deleteCommunityMeterAttributes(CommunityMeterAttributesReq req);
	public IResponse getCommunityMeterAttributesInfo(CommunityMeterAttributesReq req);
	public IResponse getCommunityMeterPriceHistoryList(CommunityMeterAttributesReq req);
	public IResponse attributesBindingMeter(CommunityMeterAttributesReq req);
	public IResponse attributesUnbindingMeter(CommunityMeterAttributesReq req);
}

