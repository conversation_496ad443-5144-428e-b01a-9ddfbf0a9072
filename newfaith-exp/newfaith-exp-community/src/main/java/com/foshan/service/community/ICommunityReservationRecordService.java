package com.foshan.service.community;

import com.foshan.form.community.request.CommunityReservationRecordReq;
import com.foshan.form.response.IResponse;

public interface ICommunityReservationRecordService {
	public IResponse getCommunityReservationRecordList(CommunityReservationRecordReq req);
	public IResponse addCommunityReservationRecord(CommunityReservationRecordReq req);
	public IResponse modifyCommunityReservationRecord(CommunityReservationRecordReq req);
	public IResponse deleteCommunityReservationRecord(CommunityReservationRecordReq req);
	public IResponse getCommunityReservationRecordInfo(CommunityReservationRecordReq req);
}
