package com.foshan.service.community.impl;

import static java.util.Comparator.comparingInt;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.foshan.service.annotation.Audit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityReservatPeriodEntity;
import com.foshan.entity.community.CommunityReservationActivitiesEntity;
import com.foshan.entity.community.CommunityReservationDateEntity;
import com.foshan.entity.community.CommunityReservationPresetPeriodEntity;
import com.foshan.entity.community.CommunityReservationStrategyEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityEstateForm;
import com.foshan.form.community.CommunityReservationActivitiesForm;
import com.foshan.form.community.CommunityReservationStrategyForm;
import com.foshan.form.community.request.CommunityReservationActivitiesReq;
import com.foshan.form.community.response.reservationActivities.GetReservationActivitiesInfo;
import com.foshan.form.community.response.reservationActivities.GetReservationActivitiesListRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.ICommunityReservationActivitiesService;
import com.foshan.util.DateUtil;

@Transactional
@Service("communityReservationActivitiesService")
public class CommunityReservationActivitiesServiceImpl extends GenericCommunityService implements ICommunityReservationActivitiesService {

	@Override
	public IResponse getCommunityReservationActivitiesList(CommunityReservationActivitiesReq req) {
		GetReservationActivitiesListRes res = new GetReservationActivitiesListRes();
		Page<CommunityReservationActivitiesEntity> page = new Page<CommunityReservationActivitiesEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		//Object userObj = getPrincipal(true);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityReservationActivitiesEntity a  ");
	
		hql.append(" where").append(null != req.getState() ? "  a.state=" + req.getState()  :
			"  a.state="+EntityContext.RECORD_STATE_VALID)
			.append(null!=req.getStrategyId() ?" and a.strategy.id="+req.getStrategyId():"")
			.append(null!=req.getPublicAreaId()?" and a.estate.id="+req.getPublicAreaId():"")

			.append(StringUtils.isNotEmpty(req.getActivitiesName()) ?
				" and a.activitiesName like '%" + req.getActivitiesName() + "%'" :"");
		hql.append(" ORDER BY a.orders ASC");
		page = communityReservationActivitiesDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
		page.getResultList().forEach(o -> {
			CommunityReservationActivitiesForm activities = new CommunityReservationActivitiesForm(
					o.getId(),o.getActivitiesName(),o.getCentent(),o.getAgreement(),
					sdf.format(o.getStartTime()),sdf.format(o.getEndTime()),o.getNeedConfirmAgreement(),
					sdf.format(o.getCreateTime()),sdf.format(o.getLastModifyTime()),o.getState(),o.getOrders()) ;
			activities.setEmploy(o.getEmploy());
			CommunityReservationStrategyForm strategy = new CommunityReservationStrategyForm(o.getStrategy().getId(),
					o.getStrategy().getStrategyName(),o.getStrategy().getPeriodType());
			activities.setStrategyForm(strategy);
			CommunityEstateForm publicArea = new CommunityEstateForm();
			CommunityEstateEntity property = o.getEstate();
			publicArea.setPropertyId(property.getId());
			publicArea.setUnitCode(property.getUnitCode());
			publicArea.setCreateTime(sdf.format(property.getCreateTime()));
			publicArea.setLastModifyTime(sdf.format(property.getLastModifyTime()));
			publicArea.setPropertyName(property.getPropertyName());
			activities.setPublicArea(publicArea);
			res.getReservationActivitiesList().add(activities);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Audit(operate = "新增预约活动")
	@Override
	public IResponse addCommunityReservationActivities(CommunityReservationActivitiesReq req) {
		GenericResponse res = new GenericResponse();
		if (null!= req.getStrategyId() && null != req.getPublicAreaId() &&
				StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime()) &&
				StringUtils.isNotEmpty(req.getActivitiesName())) {
			if(null != req.getNeedConfirmAgreement() && req.getNeedConfirmAgreement()==1 && StringUtils.isEmpty(req.getAgreement())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}else if(null == req.getNeedConfirmAgreement()) {
				req.setNeedConfirmAgreement(0);
			}
			CommunityReservationStrategyEntity strategy = 
					communityReservationStrategyDao.get(req.getStrategyId());
			CommunityEstateEntity communityPublicArea = 
					communityEstateDao.get(req.getPublicAreaId());
			if(null == strategy || null==communityPublicArea) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			CommunityReservationActivitiesEntity activities = new CommunityReservationActivitiesEntity();
			activities.setActivitiesName(req.getActivitiesName());
			activities.setAgreement(StringUtils.isNotEmpty(req.getAgreement()) ? req.getAgreement() :"");
			activities.setCentent(StringUtils.isNotEmpty(req.getCentent()) ? req.getCentent() :"");
			activities.setEndTime(Timestamp.valueOf(req.getEndTime()));
			activities.setNeedConfirmAgreement(req.getNeedConfirmAgreement());
			activities.setOrders(null!=req.getOrders() ? req.getOrders() : 100);
			activities.setEstate(communityPublicArea);
			activities.setStartTime(Timestamp.valueOf(req.getStartTime()));
			activities.setStrategy(strategy);
			activities.setLastModifyTime(new Timestamp(new Date().getTime()));
			activities.setEmploy(null!=req.getEmploy() ? req.getEmploy() : 0);
			String startDate = req.getStartTime().split(" ")[0];
			String endDate = req.getEndTime().split(" ")[0];
			List<Date> dateList = getDateList(startDate,endDate);
			activities.setState(EntityContext.RECORD_STATE_VALID);
			communityReservationActivitiesDao.save(activities);
			generateReservationDate(activities,dateList);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	
	public void generateReservationDate(CommunityReservationActivitiesEntity activities,List<Date> dateList) {
		if(activities.getReservationDateList().size()>0) {
			for(CommunityReservationDateEntity reservationDate :activities.getReservationDateList()) {
				reservationDate.setState(EntityContext.RECORD_STATE_INVALID);
				reservationDate.getPeriodList().forEach(o->{
					o.setState(EntityContext.RECORD_STATE_INVALID);
				});
			}
		}
		if(activities.getStrategy().getPeriodType()==0) {
			activities.getStrategy().getPresetPeriodList().sort(comparingInt(CommunityReservationPresetPeriodEntity::getOrders));
			for(Date date : dateList) {
				CommunityReservationDateEntity reservationDate =communityReservationDateDao.findUnique(
						"from CommunityReservationDateEntity a where a.activities.id="+activities.getId()+
						" and a.reservationDate='"+DateUtil.format(date,0)+"'", "");
				if(null != reservationDate) {
					reservationDate.setState(EntityContext.RECORD_STATE_VALID);
					for(CommunityReservationPresetPeriodEntity o : activities.getStrategy().getPresetPeriodList()){
						CommunityReservatPeriodEntity period = communityReservatPeriodDao.findUnique(
								" select distinct a  from CommunityReservatPeriodEntity a where a.reservationDate.id="+reservationDate.getId()+
								" and a.presetPeriodId='"+o.getId()+"'", "");
						if(null != period) {
							period.setState(EntityContext.RECORD_STATE_VALID);
						}else {
							period = new CommunityReservatPeriodEntity();
							period.setEndTime(o.getEndTime());
							period.setMaxNum(o.getMaxNum());
							period.setOverplusNum(o.getMaxNum());
							period.setPresetPeriodId(o.getId());
							period.setReservationDate(reservationDate);
							period.setStartTime(o.getStartTime());
							period.setState(EntityContext.RECORD_STATE_VALID);
							period.setOrders(o.getOrders());
							communityReservatPeriodDao.save(period);
							reservationDate.getPeriodList().add(period);
						}
					}
				}else {
					reservationDate = new CommunityReservationDateEntity();
					reservationDate.setActivities(activities);
					reservationDate.setIsStart(1);
					reservationDate.setReservationDate(new java.sql.Date(date.getTime()));
					reservationDate.setState(EntityContext.RECORD_STATE_VALID);
					communityReservationDateDao.save(reservationDate);
					for(CommunityReservationPresetPeriodEntity o : activities.getStrategy().getPresetPeriodList()){
						CommunityReservatPeriodEntity period = new CommunityReservatPeriodEntity();
						period.setEndTime(o.getEndTime());
						period.setMaxNum(o.getMaxNum());
						period.setOverplusNum(o.getMaxNum());
						period.setPresetPeriodId(o.getId());
						period.setReservationDate(reservationDate);
						period.setStartTime(o.getStartTime());
						period.setOrders(o.getOrders());
						period.setState(EntityContext.RECORD_STATE_VALID);
						communityReservatPeriodDao.saveOrUpdate(period);
						//reservationDate.getPeriodList().add(period);
					}
				}
			}
		}
	}
	
	public List<Date> getDateList(String startTime,String endTime) {
		List<Date> list = new ArrayList<Date>();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		try{
		    Date startDate = dateFormat.parse(startTime);
		    Date endDate = dateFormat.parse(endTime);
		    Calendar calendar = Calendar.getInstance();
		    calendar.setTime(startDate);
		    while(calendar.getTime().before(endDate)){   
		        //System.out.println(dateFormat.format(calendar.getTime()));
		        list.add(calendar.getTime());
		        calendar.add(Calendar.DAY_OF_MONTH, 1);
		    }
		    //System.out.println(dateFormat.format(endDate));
		    list.add(endDate);
		}
		catch(Exception e){
		    e.printStackTrace();
		}
		return list;
	}

	@Audit(operate = "修改预约活动")
	@Override
	public IResponse modifyCommunityReservationActivities(CommunityReservationActivitiesReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getActivitiesId() && null!= req.getStrategyId() && null != req.getPublicAreaId() &&
				StringUtils.isNotEmpty(req.getStartTime()) && StringUtils.isNotEmpty(req.getEndTime()) &&
				null!=req.getActivitiesId() && StringUtils.isNotEmpty(req.getActivitiesName())) {
			if(null != req.getNeedConfirmAgreement() && req.getNeedConfirmAgreement()==1 && StringUtils.isEmpty(req.getAgreement())) {
				res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
				return res;
			}else if(null == req.getNeedConfirmAgreement()) {
				req.setNeedConfirmAgreement(0);
			}
			CommunityReservationActivitiesEntity activities = communityReservationActivitiesDao.get(req.getActivitiesId());
			CommunityReservationStrategyEntity strategy = 
					communityReservationStrategyDao.get(req.getStrategyId());
			CommunityEstateEntity communityPublicArea = 
					communityEstateDao.get(req.getPublicAreaId());
			if(null == activities || null == strategy || null==communityPublicArea) {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
				return res;
			}
			activities.setActivitiesName(req.getActivitiesName());
			activities.setAgreement(StringUtils.isNotEmpty(req.getAgreement()) ? req.getAgreement() :"");
			activities.setCentent(StringUtils.isNotEmpty(req.getCentent()) ? req.getCentent() :"");
			activities.setEndTime(Timestamp.valueOf(req.getEndTime()));
			activities.setNeedConfirmAgreement(req.getNeedConfirmAgreement());
			activities.setOrders(null!=req.getOrders() ? req.getOrders() : 100);
			activities.setEstate(communityPublicArea);
			activities.setStartTime(Timestamp.valueOf(req.getStartTime()));
			activities.setStrategy(strategy);
			activities.setEmploy(null!=req.getEmploy() ? req.getEmploy() : 0);
			activities.setLastModifyTime(new Timestamp(new Date().getTime()));

			String startDate = req.getStartTime().split(" ")[0];
			String endDate = req.getEndTime().split(" ")[0];
			List<Date> dateList = getDateList(startDate,endDate);
			generateReservationDate(activities,dateList);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Audit(operate = "删除预约活动")
	@Override
	public IResponse deleteCommunityReservationActivities(CommunityReservationActivitiesReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getActivitiesId()) {
			CommunityReservationActivitiesEntity activities = communityReservationActivitiesDao.get(req.getActivitiesId());
			if (null != activities) {
				activities.setState(EntityContext.RECORD_STATE_INVALID);
				activities.getReservationDateList().forEach(o->{
					o.setState(EntityContext.RECORD_STATE_INVALID);
					o.getPeriodList().forEach(p->{
						p.setState(EntityContext.RECORD_STATE_INVALID);
					});
				});
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityReservationActivitiesInfo(CommunityReservationActivitiesReq req) {
		GetReservationActivitiesInfo res = new GetReservationActivitiesInfo();
		if (null != req.getActivitiesId()) {
			CommunityReservationActivitiesEntity activities = communityReservationActivitiesDao.get(req.getActivitiesId());
			if (null != activities) {
				DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
				CommunityReservationActivitiesForm activitiesForm = new CommunityReservationActivitiesForm(
						activities.getId(),activities.getActivitiesName(),activities.getCentent(),activities.getAgreement(),
						sdf.format(activities.getStartTime()),sdf.format(activities.getEndTime()),activities.getNeedConfirmAgreement(),
						sdf.format(activities.getCreateTime()),sdf.format(activities.getLastModifyTime()),activities.getState(),activities.getOrders()) ;
				CommunityReservationStrategyForm strategy = new CommunityReservationStrategyForm(activities.getStrategy().getId(),
						activities.getStrategy().getStrategyName(),activities.getStrategy().getPeriodType());
				activitiesForm.setEmploy(activities.getEmploy());
				activitiesForm.setStrategyForm(strategy);
				
				CommunityEstateForm publicArea = new CommunityEstateForm();
				CommunityEstateEntity property = activities.getEstate();
				publicArea.setPropertyId(property.getId());
				publicArea.setCreateTime(sdf.format(property.getCreateTime()));
				publicArea.setLastModifyTime(sdf.format(property.getLastModifyTime()));
				publicArea.setPropertyName(property.getPropertyName());
				publicArea.setUnitCode(property.getUnitCode());
				activitiesForm.setPublicArea(publicArea);
				
				res.setCommunityReservationActivitiesForm(activitiesForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

}
