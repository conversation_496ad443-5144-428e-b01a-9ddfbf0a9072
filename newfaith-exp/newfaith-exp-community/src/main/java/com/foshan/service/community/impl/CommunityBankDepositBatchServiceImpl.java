package com.foshan.service.community.impl;

import static java.util.stream.Collectors.groupingBy;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.foshan.dao.generic.Page;
import com.foshan.entity.community.CommunityBankDepositBatchEntity;
import com.foshan.entity.community.CommunityBankDepositEstatePayItemsEntity;
import com.foshan.entity.community.CommunityBankDepositRecordEntity;
import com.foshan.entity.community.CommunityEstateEntity;
import com.foshan.entity.community.CommunityPayItemsEntity;
import com.foshan.entity.community.CommunityReceiptEntity;
import com.foshan.entity.community.CommunityReceiptReceivablesEntity;
import com.foshan.entity.community.CommunityReceivablesEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.BankDepositEstatePayItemsForm;
import com.foshan.form.community.CommunityBankDepositBatchForm;
import com.foshan.form.community.CommunityEstateForm;
import com.foshan.form.community.CommunityPayItemsForm;
import com.foshan.form.community.request.CommunityBankDepositBatchReq;
import com.foshan.form.community.response.communityBankDepositBatch.AddCommunityBankDepositBatchRes;
import com.foshan.form.community.response.communityBankDepositBatch.GetBankDepositEstatePayItemsListRes;
import com.foshan.form.community.response.communityBankDepositBatch.GetCommunityBankDepositBatchInfoRes;
import com.foshan.form.community.response.communityBankDepositBatch.GetCommunityBankDepositBatchListRes;
import com.foshan.form.community.response.communityBankDepositBatch.ModifyCommunityBankDepositBatchRes;
import com.foshan.form.community.response.exportExcel.ExportExcelRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityBankDepositBatchService;
import com.foshan.util.DateUtil;
import com.foshan.util.ExcelExportUtil;

@Transactional
@Service("communityBankDepositBatchService")
public class CommunityBankDepositBatchServiceImpl extends GenericCommunityService implements ICommunityBankDepositBatchService{

	@Override
	public IResponse getCommunityBankDepositBatchList(CommunityBankDepositBatchReq req) {
		GetCommunityBankDepositBatchListRes res = new GetCommunityBankDepositBatchListRes();
		Page<CommunityBankDepositBatchEntity> page = new Page<CommunityBankDepositBatchEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityBankDepositBatchEntity a where 1=1 ");
		hql.append(null!=req.getState() ? " and a.state="+req.getState(): "and a.state="+EntityContext.RECORD_STATE_VALID)
			.append(null!=req.getTemplateType()? " and a.templateType ="+req.getTemplateType() : "")
			.append(StringUtils.isNotEmpty(req.getStartTime()) ? " and a.startTime>='"+req.getStartTime()+"'" : "")
			.append(StringUtils.isNotEmpty(req.getEndTime()) ? " and a.endTime<='"+req.getEndTime()+"'" : "")
			.append(null!=req.getBatchState() ? " and a.batchState="+req.getBatchState() :"")
			.append(StringUtils.isNotEmpty(req.getCreditedBank()) ? " and a.creditedBank like'%"+req.getCreditedBank()+"%'" : "")
			.append(StringUtils.isNotEmpty(req.getDepositStartDate()) ? " and a.depositDate>='"+req.getDepositStartDate()+"'" : "")
			.append(StringUtils.isNotEmpty(req.getDepositEndDate()) ? " and a.depositDate<='"+req.getDepositEndDate()+"'" : "");
		
		hql.append(" ORDER BY a.createTime desc");
		page = communityBankDepositBatchDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityBankDepositBatchForm communityBankDepositBatchForm = new CommunityBankDepositBatchForm();
			communityBankDepositBatchForm.setCommunityBankDepositBatchId(o.getId());
            communityBankDepositBatchForm.setCreateTime(null != o.getCreateTime()? DateUtil.formatLongFormat(o.getCreateTime()) : "");
            communityBankDepositBatchForm.setLastModifyTime(null != o.getLastModifyTime()? DateUtil.formatLongFormat(o.getLastModifyTime()) : "");
            communityBankDepositBatchForm.setState(o.getState());
            communityBankDepositBatchForm.setTemplateType(o.getTemplateType());
            communityBankDepositBatchForm.setComment(o.getComment());
            communityBankDepositBatchForm.setDepositDate(null != o.getDepositDate()? DateUtil.formatLongFormat(o.getDepositDate()) : "");
            communityBankDepositBatchForm.setEndTime(null != o.getEndTime()? DateUtil.formatLongFormat(o.getEndTime()) : "");
            communityBankDepositBatchForm.setPayItems(o.getPayItems());
            communityBankDepositBatchForm.setStartTime(null != o.getStartTime()? DateUtil.formatLongFormat(o.getStartTime()) : "");
            communityBankDepositBatchForm.setOwnerBank(StringUtils.isNotEmpty(o.getOwnerBank()) ? 
            		o.getOwnerBank() :"");
            communityBankDepositBatchForm.setBatchState(o.getBatchState());
            communityBankDepositBatchForm.setCreditedBank(StringUtils.isNotEmpty(o.getCreditedBank()) ? 
            		o.getCreditedBank() :"");
            o.getPayItemsList().forEach(p->{
    			CommunityPayItemsForm communityPayItemsForm = new CommunityPayItemsForm();
    			communityPayItemsForm.setCommunityPayItemsId(p.getId());
    			communityPayItemsForm.setChargeCategory(p.getChargeCategory());
    			communityPayItemsForm.setComment(p.getComment());
    			communityPayItemsForm.setEndTime(null != p.getEndTime() ? DateUtil.formatLongFormat(p.getEndTime()) : "");
    			communityPayItemsForm.setPriceUnit(p.getPriceUnit());
    			communityPayItemsForm.setIsReceivables(p.getIsReceivables());
    			communityPayItemsForm.setItemsName(p.getItemsName());
    			communityPayItemsForm.setOldData(p.getOldData());
    			communityPayItemsForm.setOldId(p.getOldId());
    			communityPayItemsForm.setPayDate(p.getPayDate());
    			communityPayItemsForm.setPrice(null != p.getPrice() ? p.getPrice().toString() : "");
    			communityPayItemsForm.setComment(StringUtils.isNotEmpty(p.getComment()) ?  p.getComment() :"");
    			communityPayItemsForm.setFeeCalType(null!=p.getFeeCalType() ? p.getFeeCalType() : null);
    			communityPayItemsForm.setIsBreach(null!=p.getIsBreach() ? p.getIsBreach() : null);
    			communityPayItemsForm.setBreachRatio(null!=p.getBreachRatio() ? p.getBreachRatio().toString() :"");
    			communityPayItemsForm
    					.setStartTime(null != p.getStartTime() ? DateUtil.formatLongFormat(p.getStartTime()) : "");
    			communityBankDepositBatchForm.getPayItemsList().add(communityPayItemsForm);
            });

			res.getCommunityBankDepositBatchList().add(communityBankDepositBatchForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}

	@Override
	@Audit(operate = "新增划账批次")
	public IResponse addCommunityBankDepositBatch(CommunityBankDepositBatchReq req) {
		AddCommunityBankDepositBatchRes res = new AddCommunityBankDepositBatchRes();
//		if (null != req.getTemplateType()) {
			CommunityBankDepositBatchEntity communityBankDepositBatch = new CommunityBankDepositBatchEntity();
			
            communityBankDepositBatch.setLastModifyTime(new Timestamp(new Date().getTime()));
            communityBankDepositBatch.setState(EntityContext.RECORD_STATE_VALID);
            communityBankDepositBatch.setTemplateType(req.getTemplateType());
            communityBankDepositBatch.setComment(req.getComment());
            try {
                communityBankDepositBatch.setDepositDate(StringUtils.isNotEmpty(req.getDepositDate()) ? 
                    DateUtil.parseLongFormat(req.getDepositDate()) : null);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            try {
                communityBankDepositBatch.setEndTime(StringUtils.isNotEmpty(req.getEndTime()) ? 
                    DateUtil.parseLongFormat(req.getEndTime()) : null);
            } catch (ParseException e) {
                e.printStackTrace();
            }
			if (StringUtils.isNotEmpty(req.getPayItemsIdList())) {
				String[] payItemsIds = req.getPayItemsIdList().split(",");
				for (String payItemsId : payItemsIds) {
					CommunityPayItemsEntity payItems = communityPayItemsDao.get(Integer.parseInt(payItemsId));
					if(null != payItems) {
						communityBankDepositBatch.setPayItems(StringUtils.isNotEmpty(communityBankDepositBatch.getPayItems()) ? 
								(communityBankDepositBatch.getPayItems()+payItems.getItemsName()+",") : (payItems.getItemsName()+","));
						communityBankDepositBatch.getPayItemsList().add(payItems);
					}
				}
				if(communityBankDepositBatch.getPayItems().endsWith(",")) {
					communityBankDepositBatch.setPayItems(communityBankDepositBatch.getPayItems().
							substring(0, communityBankDepositBatch.getPayItems().length()-1));
				}
			}
            try {
                communityBankDepositBatch.setStartTime(StringUtils.isNotEmpty(req.getStartTime()) ? 
                    DateUtil.parseLongFormat(req.getStartTime()) : null);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            communityBankDepositBatch.setOwnerBank(StringUtils.isNotEmpty(req.getOwnerBank()) ? 
            		req.getOwnerBank() :"");
            communityBankDepositBatch.setBatchState(0);
            communityBankDepositBatch.setCreditedBank(StringUtils.isNotEmpty(req.getCreditedBank()) ? 
            		req.getCreditedBank() :"");
            getReceivables(communityBankDepositBatch);
			communityBankDepositBatchDao.save(communityBankDepositBatch);
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
//		} else {
//			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
//			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
//		}
		return res;
	}
	
	public void getReceivables(CommunityBankDepositBatchEntity bankDepositBatch) {
		String bankNameSql1 = "";
		String bankNameSql2 = "";
		if (StringUtils.isNotEmpty(bankDepositBatch.getOwnerBank())) {
			String[] bankNameList = bankDepositBatch.getOwnerBank().split(",");
			if (bankNameList.length > 0) {
				StringBuilder bankName1 = new StringBuilder();
				StringBuilder bankName2 = new StringBuilder();
				bankName1.append("(");
				bankName2.append("(");
				for (int i = 0; i < bankNameList.length; i++) {
					bankName1.append(" c.bankName ='" + bankNameList[i] + "' or");
					bankName2.append(" c2.bankName ='" + bankNameList[i] + "' or");
				}
				bankNameSql1 = bankName1.subSequence(0, bankName1.length() - 2).toString() + ")";
				bankNameSql2 = bankName2.subSequence(0, bankName2.length() - 2).toString() + ")";
			}
		}
		StringBuilder payItemsIds = new StringBuilder();
		bankDepositBatch.getPayItemsList().forEach(o -> {
			payItemsIds.append(o.getId() + ",");
		});

		StringBuilder basicSql = new StringBuilder(
				"  FROM `t_community_receivables` a INNER JOIN t_community_property b ON b.id=a.estateId "
				+ "INNER JOIN t_community_payment_account c ON c.id=b.paymentAccountId   "
				+ "INNER JOIN t_community_member_property mp on mp.propertyId=b.id WHERE "
				+ " a.receivableDate >= mp.billingDate  and mp.isCurrentMember=1 and "
				+ " a.receivableAmount>a.receivedAmount AND a.bankDepositRecordId IS NULL AND a.lockMark=0 AND a.breachReceivablesId IS NULL "
				+ " AND  a.id NOT IN(");
		//排除所有不划扣应该收款			
		basicSql.append("SELECT DISTINCT a1.id FROM t_community_receivables a1 inner join `t_community_bankdeposit_estate_payitems` "
				+ "b1 on b1.payItemsId=a1.payItemId AND b1.estateId=a1.estateId where b1.depositType=0 "+
				(null!=bankDepositBatch.getStartTime() ? " and a1.receivableDate>='"+
				DateUtil.formatLongFormat(bankDepositBatch.getStartTime())+"'" :"")+
				(null!=bankDepositBatch.getEndTime() ? " and a1.receivableDate<='"+
				DateUtil.formatLongFormat(bankDepositBatch.getEndTime())+"'" :"")
				+ "and a1.receivableAmount>a1.receivedAmount AND a1.bankDepositRecordId IS NULL )");
		basicSql.append(StringUtils.isNotEmpty(bankNameSql1) ? " AND " + bankNameSql1 :"");
		basicSql.append(StringUtils.isNotEmpty(payItemsIds.toString()) ? " and a.payItemId in("+
		payItemsIds.toString().substring(0, payItemsIds.toString().length()-1) + ")" : "")
			.append(null!=bankDepositBatch.getStartTime() ? " and a.receivableDate>='"+
		DateUtil.formatLongFormat(bankDepositBatch.getStartTime())+"'" :"")
			.append(null!=bankDepositBatch.getEndTime() ? " and a.receivableDate<='"+
		DateUtil.formatLongFormat(bankDepositBatch.getEndTime())+"'" :"");
		
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT DISTINCT v.*  from  (SELECT DISTINCT a.* "+basicSql)
		//加上违约金
			.append(" UNION SELECT DISTINCT a3.* FROM `t_community_receivables` a3 where a3.breachReceivablesId in(SELECT DISTINCT a2.id ")
			.append("  FROM `t_community_receivables` a2 INNER JOIN t_community_property b2 ON b2.id=a2.estateId "
					+ "INNER JOIN t_community_payment_account c2 ON c2.id=b2.paymentAccountId  INNER JOIN t_community_member_property mp2 on mp2.propertyId=b2.id"
					+ "  WHERE a2.receivableDate >= mp2.billingDate and mp2.isCurrentMember=1   "
					+ "  AND a2.bankDepositRecordId IS NULL AND a2.breachReceivablesId IS NULL AND a2.lockMark=0 AND a2.id NOT IN("
					+" SELECT DISTINCT  r1.id FROM  t_community_receivables r1  INNER JOIN `t_community_bankdeposit_estate_payitems`  bep "
					+ "  on bep.payItemsId = r1.payItemId AND bep.estateId = r1.estateId WHERE 1=1 "+//r1.receivableAmount > r1.receivedAmount
					(null!=bankDepositBatch.getStartTime() ? " and  r1.receivableDate>='"+
							DateUtil.formatLongFormat(bankDepositBatch.getStartTime())+"'" :"")+
							(null!=bankDepositBatch.getEndTime() ? " and r1.receivableDate<='"+
							DateUtil.formatLongFormat(bankDepositBatch.getEndTime())+"'" :"")
					+ "  AND r1.bankDepositRecordId IS NULL )")
			.append(StringUtils.isNotEmpty(bankNameSql2) ? " AND " + bankNameSql2:"")
			.append(null!=bankDepositBatch.getStartTime() ? " and a2.receivableDate>='"+
					DateUtil.formatLongFormat(bankDepositBatch.getStartTime())+"'" :"")
						.append(null!=bankDepositBatch.getEndTime() ? " and a2.receivableDate<='"+
					DateUtil.formatLongFormat(bankDepositBatch.getEndTime())+"'" :"")	
			.append(StringUtils.isNotEmpty(payItemsIds.toString()) ? " and a2.payItemId in("+
								payItemsIds.toString().substring(0, payItemsIds.toString().length()-1) + ")" : "")
			.append(")")
			.append(" and a3.receivableAmount>a3.receivedAmount AND a3.bankDepositRecordId IS NULL AND a3.lockMark=0 ")
			.append(null!=bankDepositBatch.getStartTime() ? " and a3.receivableDate>='"+
			DateUtil.formatLongFormat(bankDepositBatch.getStartTime())+"'" :"")
			.append(null!=bankDepositBatch.getEndTime() ? " and a3.receivableDate<='"+
			DateUtil.formatLongFormat(bankDepositBatch.getEndTime())+"'" :"")
			.append(StringUtils.isNotEmpty(payItemsIds.toString()) ? " and a3.payItemId in("+
					payItemsIds.toString().substring(0, payItemsIds.toString().length()-1) + ")" : "");
		sql.append(") v ");
//		sql.append("where  v.id not in(");
//		//排除不划扣新违约金：
//		sql.append("SELECT w.id FROM `t_community_receivables` w INNER JOIN t_community_receivables r"
//				+ " ON w.`breachReceivablesId` = r.id INNER JOIN `t_community_bankdeposit_estate_payitems` b ON (r.`estateId` = b.`estateId` "
//				+ " AND r.`payItemId` = b.`payItemsId`) WHERE w.receivableAmount > w.receivedAmount AND w.lockMark = 0 AND w.breachReceivablesId IS NOT NULL ")
//			.append(null!=bankDepositBatch.getStartTime() ? " and w.receivableDate>='"+
//				DateUtil.formatLongFormat(bankDepositBatch.getStartTime())+"'" :"")
//			.append(null!=bankDepositBatch.getEndTime() ? " and w.receivableDate<='"+
//				DateUtil.formatLongFormat(bankDepositBatch.getEndTime())+"'" :"")
//			.append(" UNION ")
//		//排除不划扣旧违约金 
//			.append("SELECT DISTINCT w.id FROM`t_community_receivables` w INNER JOIN `t_community_bankdeposit_estate_payitems` b "
//				+ " ON w.`estateId` = b.`estateId` INNER JOIN `t_community_pay_items` t  ON t.`id` = b.`payItemsId` WHERE t.`isBreach` = 1 AND t.breachName IS NOT NULL"
//				+ " AND w.receivableAmount > w.receivedAmount AND w.lockMark = 0 AND w.breachReceivablesId IS NULL AND w.`payItemsName`=t.breachName")
//			.append(null!=bankDepositBatch.getStartTime() ? " and w.receivableDate>='"+
//				DateUtil.formatLongFormat(bankDepositBatch.getStartTime())+"'" :"")
//			.append(null!=bankDepositBatch.getEndTime() ? " and w.receivableDate<='"+
//				DateUtil.formatLongFormat(bankDepositBatch.getEndTime())+"'" :"")
//			.append(")");
		/*//排除不划扣新违约金：
		sql.append("SELECT "
				+ "  w.id "
				+ "FROM "
				+ "  `t_community_receivables` w "
				+ "  INNER JOIN t_community_receivables r "
				+ "    ON w.`breachReceivablesId` = r.id "
				+ "  INNER JOIN `t_community_bankdeposit_estate_payitems` b "
				+ "    ON ("
				+ "      r.`estateId` = b.`estateId` "
				+ "      AND r.`payItemId` = b.`payItemsId` "
				+ "    ) "
				+ "WHERE w.receivableAmount > w.receivedAmount "
				+ "  AND w.lockMark = 0 "
				+ "  AND w.breachReceivablesId IS NOT NULL ")
		.append("UNION ")
		//排除不划扣旧违约金 
		.append("SELECT "
				+ "  DISTINCT w.id "
				+ "FROM "
				+ "  `t_community_receivables` w "
				+ "WHERE w.`payItemsName` IN "
				+ "  (SELECT DISTINCT "
				+ "    t.breachName "
				+ "  FROM "
				+ "      `t_community_pay_items` t "
				+ "    INNER JOIN `t_community_bankdeposit_estate_payitems` b "
				+ "      ON ("
				+ "        t.`id` = b.`payItemsId` "
				+ "        AND  w.`estateId` = b.`estateId` "
				+ "      ) "
				+ "  WHERE t.`isBreach` = 1 AND t.breachName IS NOT NULL) "
				+ "  AND w.receivableAmount > w.receivedAmount "
				+ "  AND w.lockMark = 0 "
				+ "  AND w.breachReceivablesId IS NULL ")*/
			
		
		List<CommunityReceivablesEntity> receivablesList = communityReceivablesDao.getListBySql(sql.toString(), "");
		if(null != receivablesList) {
			Map<CommunityEstateEntity, List<CommunityReceivablesEntity>> collect1 = (Map<CommunityEstateEntity, List<CommunityReceivablesEntity>>) receivablesList
					.parallelStream().collect(groupingBy(CommunityReceivablesEntity :: getEstate));
			for(CommunityEstateEntity estate : collect1.keySet()) {
				BigDecimal receivableAmountTotal = new BigDecimal(0);
				CommunityBankDepositRecordEntity depositRecord = new CommunityBankDepositRecordEntity();
				depositRecord.setBankAccount(estate.getPaymentAccount().getBankAccount());
				depositRecord.setState(EntityContext.RECORD_STATE_VALID);
				depositRecord.setIsDeposited(0);
				depositRecord.setEstate(estate);
				depositRecord.setLastModifyTime(new Timestamp(new Date().getTime()));
				depositRecord.setBankDepositBatch(bankDepositBatch);
				depositRecord.setDepositComment(estate.getUnitCode());
				for(CommunityReceivablesEntity o : collect1.get(estate)) {
					depositRecord.getReceivablesList().add(o);
					BigDecimal receivableAmount = o.getReceivableAmount()
							.subtract(o.getReceivedAmount());
					receivableAmountTotal = receivableAmountTotal.add(receivableAmount);
					o.setLockMark(1);
					o.setBankDepositRecord(depositRecord);
				}
				bankDepositBatch.getBankDepositRecordList().add(depositRecord);
				depositRecord.setDepositAmount(receivableAmountTotal);
				depositRecord.setOutstandingAmount(receivableAmountTotal);
				communityBankDepositRecordDao.save(depositRecord);
			}
			
		}
	}

	@Override
	@Audit(operate = "修改划账批次")
	public IResponse modifyCommunityBankDepositBatch(CommunityBankDepositBatchReq req) {
		ModifyCommunityBankDepositBatchRes res = new ModifyCommunityBankDepositBatchRes();
		if (null!=req.getCommunityBankDepositBatchId() ) {
			/*CommunityBankDepositBatchEntity communityBankDepositBatch = communityBankDepositBatchDao.get(req.getCommunityBankDepositBatchId()) ;
			if(null != communityBankDepositBatch){
                communityBankDepositBatch.setLastModifyTime(new Timestamp(new Date().getTime()));
                communityBankDepositBatch.setState(req.getState());
                //communityBankDepositBatch.setTemplateType(req.getTemplateType());
                communityBankDepositBatch.setComment(req.getComment());
                try {
                    communityBankDepositBatch.setDepositDate(StringUtils.isNotEmpty(req.getDepositDate()) ? 
                        DateUtil.parseLongFormat(req.getDepositDate()) : null);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                try {
                    communityBankDepositBatch.setEndTime(StringUtils.isNotEmpty(req.getEndTime()) ? 
                        DateUtil.parseLongFormat(req.getEndTime()) : null);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
    			if (StringUtils.isNotEmpty(req.getPayItemsIdList())) {
    				String[] payItemsIds = req.getPayItemsIdList().split(",");
    				communityBankDepositBatch.setPayItemsList(new ArrayList<CommunityPayItemsEntity>());
    				communityBankDepositBatch.setPayItems("");
    				for (String payItemsId : payItemsIds) {
    					CommunityPayItemsEntity payItems = communityPayItemsDao.get(Integer.parseInt(payItemsId));
    					if(null != payItems) {
    						communityBankDepositBatch.setPayItems(StringUtils.isNotEmpty(communityBankDepositBatch.getPayItems()) ? 
    								(communityBankDepositBatch.getPayItems()+payItems.getItemsName()+",") : (payItems.getItemsName()+","));
    						communityBankDepositBatch.getPayItemsList().add(payItems);
    					}
    				}
					if(communityBankDepositBatch.getPayItems().endsWith(",")) {
						communityBankDepositBatch.setPayItems(communityBankDepositBatch.getPayItems().
								substring(0, communityBankDepositBatch.getPayItems().length()-1));
					}
    			}
                try {
                    communityBankDepositBatch.setStartTime(StringUtils.isNotEmpty(req.getStartTime()) ? 
                        DateUtil.parseLongFormat(req.getStartTime()) : null);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                communityBankDepositBatch.setOwnerBank(StringUtils.isNotEmpty(req.getOwnerBank()) ? 
                		req.getOwnerBank() : communityBankDepositBatch.getOwnerBank());
//                communityBankDepositBatch.setBatchState(null!=req.getBatchState() ? 
//                		req.getBatchState() : communityBankDepositBatch.getBatchState());
                communityBankDepositBatch.setCreditedBank(StringUtils.isNotEmpty(req.getCreditedBank()) ? 
                		req.getCreditedBank() : communityBankDepositBatch.getCreditedBank());
				res.setCommunityBankDepositBatchId(communityBankDepositBatch.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}*/

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除划账批次")
	public IResponse deleteCommunityBankDepositBatch(CommunityBankDepositBatchReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityBankDepositBatchId()) {
					CommunityBankDepositBatchEntity communityBankDepositBatch =communityBankDepositBatchDao.get(req.getCommunityBankDepositBatchId());
			if (null != communityBankDepositBatch) {
				if(communityBankDepositBatch.getBatchState() == 1) {
					res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
					res.setRetInfo("对不起，已记账的数据不能删除！");
					return res;
				}
				List<CommunityBankDepositRecordEntity> bankDepositRecordList= communityBankDepositRecordDao.getListByHql("select distinct a from CommunityBankDepositRecordEntity a  "
						+ "where a.bankDepositBatch.id="+req.getCommunityBankDepositBatchId());
				Iterator<CommunityBankDepositRecordEntity> it = bankDepositRecordList.iterator();
				int i=0;
		        while(it.hasNext()){
					CommunityBankDepositRecordEntity o = it.next();
					communityReceivablesDao.executeUpdate(
							"update CommunityReceivablesEntity a set a.lockMark=0 ,a.bankDepositRecord=null where a.bankDepositRecord.id=" + o.getId());
					o.setReceivablesList(null);
					o.setBankDepositBatch(null);
					o.setEstate(null);
					communityBankDepositRecordDao.delete(o);
					it.remove();
					//communityBankDepositRecordDao.delete(o);
					if (i!=0 && i % 20 == 0) {
						communityReceivablesDao.flush();
						communityReceivablesDao.clear();
						communityBankDepositRecordDao.flush();
						communityBankDepositRecordDao.clear();
					}
					i++;
				}
		        
				communityBankDepositBatch.setBankDepositRecordList(null);
				communityBankDepositBatch.setPayItemsList(null);
				communityBankDepositBatchDao.delete(communityBankDepositBatch);
	
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
//			if (session != null) {
//				session.close();
//			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityBankDepositBatchInfo(CommunityBankDepositBatchReq req) {
		GetCommunityBankDepositBatchInfoRes res = new GetCommunityBankDepositBatchInfoRes();
		if (null != req.getCommunityBankDepositBatchId()) {
			CommunityBankDepositBatchEntity communityBankDepositBatch = communityBankDepositBatchDao.get(req.getCommunityBankDepositBatchId());
			if (null != communityBankDepositBatch) {
				CommunityBankDepositBatchForm communityBankDepositBatchForm = new CommunityBankDepositBatchForm();
				communityBankDepositBatchForm.setCommunityBankDepositBatchId(communityBankDepositBatch.getId());
                communityBankDepositBatchForm.setCreateTime(null != communityBankDepositBatch.getCreateTime()? DateUtil.formatLongFormat(communityBankDepositBatch.getCreateTime()) : "");
                communityBankDepositBatchForm.setLastModifyTime(null != communityBankDepositBatch.getLastModifyTime()? DateUtil.formatLongFormat(communityBankDepositBatch.getLastModifyTime()) : "");
                communityBankDepositBatchForm.setState(communityBankDepositBatch.getState());
                communityBankDepositBatchForm.setTemplateType(communityBankDepositBatch.getTemplateType());
                communityBankDepositBatchForm.setComment(communityBankDepositBatch.getComment());
                communityBankDepositBatchForm.setDepositDate(null != communityBankDepositBatch.getDepositDate()? DateUtil.formatLongFormat(communityBankDepositBatch.getDepositDate()) : "");
                communityBankDepositBatchForm.setEndTime(null != communityBankDepositBatch.getEndTime()? DateUtil.formatLongFormat(communityBankDepositBatch.getEndTime()) : "");
                communityBankDepositBatchForm.setPayItems(communityBankDepositBatch.getPayItems());
                communityBankDepositBatchForm.setStartTime(null != communityBankDepositBatch.getStartTime()? DateUtil.formatLongFormat(communityBankDepositBatch.getStartTime()) : "");
                communityBankDepositBatchForm.setOwnerBank(StringUtils.isNotEmpty(communityBankDepositBatch.getOwnerBank()) ? 
                        		communityBankDepositBatch.getOwnerBank() :"");
                communityBankDepositBatchForm.setBatchState(communityBankDepositBatch.getBatchState());
                communityBankDepositBatchForm.setCreditedBank(StringUtils.isNotEmpty(communityBankDepositBatch.getCreditedBank()) ? 
                		communityBankDepositBatch.getCreditedBank() :"");
                communityBankDepositBatch.getPayItemsList().forEach(p->{
        			CommunityPayItemsForm communityPayItemsForm = new CommunityPayItemsForm();
        			communityPayItemsForm.setCommunityPayItemsId(p.getId());
        			communityPayItemsForm.setChargeCategory(p.getChargeCategory());
        			communityPayItemsForm.setComment(p.getComment());
        			communityPayItemsForm.setEndTime(null != p.getEndTime() ? DateUtil.formatLongFormat(p.getEndTime()) : "");
        			communityPayItemsForm.setPriceUnit(p.getPriceUnit());
        			communityPayItemsForm.setIsReceivables(p.getIsReceivables());
        			communityPayItemsForm.setItemsName(p.getItemsName());
        			communityPayItemsForm.setOldData(p.getOldData());
        			communityPayItemsForm.setOldId(p.getOldId());
        			communityPayItemsForm.setPayDate(p.getPayDate());
        			communityPayItemsForm.setPrice(null != p.getPrice() ? p.getPrice().toString() : "");
        			communityPayItemsForm.setComment(StringUtils.isNotEmpty(p.getComment()) ?  p.getComment() :"");
        			communityPayItemsForm.setFeeCalType(null!=p.getFeeCalType() ? p.getFeeCalType() : null);
        			communityPayItemsForm.setIsBreach(null!=p.getIsBreach() ? p.getIsBreach() : null);
        			communityPayItemsForm.setBreachRatio(null!=p.getBreachRatio() ? p.getBreachRatio().toString() :"");
        			communityPayItemsForm
        					.setStartTime(null != p.getStartTime() ? DateUtil.formatLongFormat(p.getStartTime()) : "");
        			communityBankDepositBatchForm.getPayItemsList().add(communityPayItemsForm);
                });
                res.setCommunityBankDepositBatchForm(communityBankDepositBatchForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	@Audit(operate = "新增非划扣收费项目")
	public IResponse addBankDepositEstatePayItems(CommunityBankDepositBatchReq req) {
		GenericResponse res = new GenericResponse();
		if (StringUtils.isNotEmpty(req.getEstatePayItemsIdList())) {
			String[] estatePayItemsIds = req.getEstatePayItemsIdList().split(",");
			for (String estatePayItemsId : estatePayItemsIds) {
				String[] ids = estatePayItemsId.split("_");
				Integer depositType = ids.length>2 ? Integer.parseInt(estatePayItemsId.split("_")[2]) : null;
				CommunityEstateEntity estate = communityEstateDao.get(Integer.parseInt(ids[0]));
				CommunityPayItemsEntity payItems = communityPayItemsDao.get(Integer.parseInt(ids[1]));
				CommunityBankDepositEstatePayItemsEntity bankDepositEstatePayItems = 
						communityBankDepositEstatePayItemsDao.getUniqueByHql("select a from CommunityBankDepositEstatePayItemsEntity "
								+ "a where a.payItems.id="+payItems.getId()+" and a.estate.id="+estate.getId(), "");
				if(null == bankDepositEstatePayItems) {
					bankDepositEstatePayItems = new CommunityBankDepositEstatePayItemsEntity();
					bankDepositEstatePayItems.setDepositType(depositType);
					bankDepositEstatePayItems.setEstate(estate);
					bankDepositEstatePayItems.setPayItems(payItems);
					communityBankDepositEstatePayItemsDao.save(bankDepositEstatePayItems);
				}else {
					bankDepositEstatePayItems.setDepositType(depositType);
				}
			}
				
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
		}

		return res;
	}
	
	@Audit(operate = "修改非划扣收费项目")
	public IResponse modifyBankDepositEstatePayItems(CommunityBankDepositBatchReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getBankDepositEstatePayItemsId()) {
			CommunityBankDepositEstatePayItemsEntity bankDepositEstatePayItems = communityBankDepositEstatePayItemsDao.get(req.getBankDepositEstatePayItemsId());
			if(null != bankDepositEstatePayItems) {
				bankDepositEstatePayItems.setDepositType(req.getDepositType());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
		}

		return res;
	}
	
	@Audit(operate = "删除非划扣收费项目")
	public IResponse deleteBankDepositEstatePayItems(CommunityBankDepositBatchReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getBankDepositEstatePayItemsId()) {
			CommunityBankDepositEstatePayItemsEntity bankDepositEstatePayItems = communityBankDepositEstatePayItemsDao.get(req.getBankDepositEstatePayItemsId());
			if(null != bankDepositEstatePayItems) {
				bankDepositEstatePayItems.setEstate(null);
				bankDepositEstatePayItems.setPayItems(null);
				communityBankDepositEstatePayItemsDao.delete(bankDepositEstatePayItems);
				
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
		}

		return res;
	}

	
	public IResponse  getBankDepositEstatePayItemsList(CommunityBankDepositBatchReq req){
		GetBankDepositEstatePayItemsListRes res = new GetBankDepositEstatePayItemsListRes();
		Page<CommunityBankDepositEstatePayItemsEntity> page = new Page<CommunityBankDepositEstatePayItemsEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		page = communityBankDepositEstatePayItemsDao.queryPage(page, getBankDepositEstatePayItemsHql(req));

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
//		List<CommunityPayItemsEntity> payItemsEntityList = communityPayItemsDao.getListBySql("SELECT DISTINCT a.* "
//				+ "FROM t_community_pay_items a INNER JOIN t_community_bankdeposit_batch_payitems bbp "
//				+ "ON bbp.payItemsId=a.id WHERE bbp.bankDepositBatchId IN( SELECT MAX(id) "
//				+ "FROM `t_community_bank_deposit_batch`  WHERE state=1)", "");
		page.getResultList().forEach(o -> {
			BankDepositEstatePayItemsForm bankDepositEstatePayItems = new BankDepositEstatePayItemsForm();
			bankDepositEstatePayItems.setBankDepositEstatePayItemsId(o.getId());
			if(null != o.getEstate()) {
				CommunityEstateForm estateForm = new CommunityEstateForm();
				CommunityEstateEntity estate = (CommunityEstateEntity) o.getEstate();
				estateForm.setUnitCode(estate.getUnitCode());
				estateForm.setEstateId(estate.getId());
				bankDepositEstatePayItems.setEstate(estateForm);
			}
			if(null != o.getPayItems()) {
				CommunityPayItemsForm payItemsForm = new CommunityPayItemsForm();
				payItemsForm.setCommunityPayItemsId(o.getPayItems().getId());
				payItemsForm.setBreachName(o.getPayItems().getItemsName());
				bankDepositEstatePayItems.setPayItems(payItemsForm);
			}
			bankDepositEstatePayItems.setDepositType(o.getDepositType());
			res.getBankDepositEstatePayItemsList().add(bankDepositEstatePayItems);
			
//			bankDepositEstatePayItems.setEstateId(o.getId());
//			bankDepositEstatePayItems.setUnitCode(o.getUnitCode());

//			o.getBankDepositPayItemsList().forEach(p->{
//				if(!payItemsEntityList.contains(p)) {
//					bankDepositEstatePayItems.setAddItemsName((null!=bankDepositEstatePayItems.getAddItemsName()?
//							bankDepositEstatePayItems.getAddItemsName():"")
//							+p.getItemsName()+",");
//				}
//				bankDepositEstatePayItems.setItemsName((null!=bankDepositEstatePayItems.getItemsName() ? 
//						bankDepositEstatePayItems.getItemsName():"")+
//						p.getItemsName()+",");
//				bankDepositEstatePayItems.setPayItemsIdList((null!=bankDepositEstatePayItems.getPayItemsIdList()?
//						bankDepositEstatePayItems.getPayItemsIdList():"")+
//						p.getId()+",");
//			});
//			payItemsEntityList.forEach(p->{
//				if(!o.getBankDepositPayItemsList().contains(p)) {
//					bankDepositEstatePayItems.setReduceItemsName((null!=bankDepositEstatePayItems.getReduceItemsName()?
//							bankDepositEstatePayItems.getReduceItemsName():"")
//							+p.getItemsName()+",");
//				}
//			});
//			
//			bankDepositEstatePayItems.setAddItemsName(StringUtils.isNotEmpty(bankDepositEstatePayItems.getAddItemsName()) ?
//					bankDepositEstatePayItems.getAddItemsName().substring(0, bankDepositEstatePayItems.getAddItemsName().length()-1):"");
//			bankDepositEstatePayItems.setItemsName(StringUtils.isNotEmpty(bankDepositEstatePayItems.getItemsName()) ?
//					bankDepositEstatePayItems.getItemsName().substring(0, bankDepositEstatePayItems.getItemsName().length()-1):"");
//			bankDepositEstatePayItems.setReduceItemsName(StringUtils.isNotEmpty(bankDepositEstatePayItems.getReduceItemsName()) ?
//					bankDepositEstatePayItems.getReduceItemsName().substring(0, bankDepositEstatePayItems.getReduceItemsName().length()-1):"");
//			bankDepositEstatePayItems.setPayItemsIdList(StringUtils.isNotEmpty(bankDepositEstatePayItems.getPayItemsIdList()) ?
//					bankDepositEstatePayItems.getPayItemsIdList().substring(0, bankDepositEstatePayItems.getPayItemsIdList().length()-1):"");
//			res.getBankDepositEstatePayItemsList().add(bankDepositEstatePayItems);
			
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	public String getBankDepositEstatePayItemsHql(CommunityBankDepositBatchReq req) {
		StringBuilder hql = new StringBuilder("select distinct a from CommunityBankDepositEstatePayItemsEntity a inner join  a.estate b inner join a.payItems c ");
		hql.append(StringUtils.isNotEmpty(req.getUnitCode()) ? " where b.unitCode like'%" + req.getUnitCode() + "%'"
				: "")
		.append(null!=req.getDepositType() ? " a.depositType="+req.getDepositType():"");
		
		return hql.toString();
	}
	public IResponse  exportBankDepositEstatePayItems(CommunityBankDepositBatchReq req,HttpServletResponse response) {
		ExportExcelRes res = new ExportExcelRes();
		Map<String,String> heardMap = new LinkedHashMap<String,String>();
		heardMap.put("unitCode", "单元编号");
		heardMap.put("itemsName", "项目名称");
//		heardMap.put("reduceItemsName", "减少项目名称");
//		heardMap.put("addItemsName", "新增项目名称");
//		heardMap.put("payItemsIdList", "已选收费项目ID");
		heardMap.put("depositType", "类型");
		List<Map<String,Object>> dataList = new ArrayList<Map<String,Object>>();
		List<CommunityBankDepositEstatePayItemsEntity> list = communityBankDepositEstatePayItemsDao.getListByHql(getBankDepositEstatePayItemsHql(req),"");
		if(null != list) {
			list.forEach(o->{
				Map<String,Object> map = new HashMap<String,Object>();
				map.put("unitCode", null!=o.getEstate() ? ((CommunityEstateEntity) o.getEstate()).getUnitCode() : "");
				map.put("itemsName", o.getPayItems().getItemsName());
                switch(o.getDepositType()) {
                case 0 :map.put("depositType", "不划扣收费设定与违约金"); break;
                case 1 : map.put("depositType", "仅不划扣违约金"); break;
                default : map.put("depositType", ""); break;
                }
				dataList.add(map);
			});
		}
		try {
			ExcelExportUtil.exportExcelFile (heardMap,dataList,
					Short.valueOf("9"),Short.valueOf("500"),null,"","","",
					 response);
		} catch (Exception e) {
			e.printStackTrace();
            res.setRet(ResponseContext.RES_INVALID_PARAM_CODE);
            res.setRetInfo("导出异常！");
		}
		return res;
	}
	
	public IResponse  setBatchState(CommunityBankDepositBatchReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityBankDepositBatchId()) {
			CommunityBankDepositBatchEntity communityBankDepositBatch = communityBankDepositBatchDao.get(req.getCommunityBankDepositBatchId());
			if (null != communityBankDepositBatch && communityBankDepositBatch.getBatchState() == 0) {
//				StringBuilder hql = new StringBuilder("select distinct a from CommunityBankDepositRecordEntity a inner join a.estate b where 1=1 ");
//				hql.append(" and a.bankDepositBatch.id="+req.getCommunityBankDepositBatchId() +" and isDeposited!=1");
//				List<CommunityBankDepositRecordEntity> list = communityBankDepositRecordDao.getListByHql(hql.toString(), "");
//				if(null!=list && list.size()>0) {
//	                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
//					res.setRetInfo("对不起，记账失败！还有“未回盘”或“回盘失败”的银行划账记录；");
//					return res;
//				}
				
				for(CommunityBankDepositRecordEntity bankDepositRecord : communityBankDepositBatch.getBankDepositRecordList()) {
					if(bankDepositRecord.getIsDeposited().equals(1)) {
						CommunityEstateEntity estate = bankDepositRecord.getEstate();
						CommunityReceiptEntity receipt = new CommunityReceiptEntity();
						receipt.setPaymentType(0);
						receipt.setReceiptDate(communityBankDepositBatch.getDepositDate());
						receipt.setReceiptCode(getReceiptCode("银行划账", receipt.getReceiptDate()));
						
						receipt.setAmount(bankDepositRecord.getDepositAmount());
						receipt.setPaymentMethod("银行代收");
						receipt.setReceiptType("收据");
						

						receipt.setAgent(communityBankDepositBatch.getCreditedBank());
					
						receipt.setExchangeRate(new BigDecimal(1));
						receipt.setFeeType(0);
						receipt.setHaveInvoice(0);
						receipt.setMoneytype("人民币");
						receipt.setHaveInvoice(0);;
						receipt.setBankDepositRecord(bankDepositRecord);
						
						estate.getMemberPropertyList().forEach(o->{
							if(o.getIsCurrentMember()==1 && o.getMemberType()==0) {
								receipt.setPayerName(o.getMember().getUserName());
							}
						});
						receipt.setEstate(estate);
						receipt.setState(EntityContext.RECORD_STATE_VALID);
						String bankAccount = bankDepositRecord.getBankAccount();
						receipt.setBankAccount(bankAccount);
				        String str = StringUtils.left(bankAccount, 13);
						receipt.setComment("银行代收 账户:"+StringUtils.rightPad(str, StringUtils.length(bankAccount), "*"));
						communityReceiptDao.save(receipt);
						
						for(CommunityReceivablesEntity receivables : bankDepositRecord.getReceivablesList()) {
							BigDecimal receivedAmount = new BigDecimal(0);
							CommunityReceiptReceivablesEntity receiptReceivables = new CommunityReceiptReceivablesEntity();
							receivables.setLockMark(0);
							receivables.setBankDepositRecord(null);
							//estate = receivables.getEstate();
							receivedAmount = receivables.getReceivableAmount().subtract(receivables.getReceivedAmount());
							receiptReceivables.setReceivables(receivables);
							receiptReceivables.setReceipt(receipt);
							receivables.setLastModifyTime(new Timestamp(System.currentTimeMillis()));
							receiptReceivables.setReceivedAmount(receivables.getReceivedAmount());
							receiptReceivables.setCurrentAmount(receivedAmount);
							receiptReceivables.setInvoiceState(0);
							communityReceiptReceivablesDao.save(receiptReceivables);
							receivables.setReceivedAmount(receivables.getReceivableAmount());
							// 修改补差额对象
							modifySubReceivaedManageAmount(receiptReceivables.getId(), receivables, receivedAmount, receipt.getReceiptDate() ,0);
						}
						
					}else {
						for(CommunityReceivablesEntity receivables : bankDepositRecord.getReceivablesList()) {
							receivables.setLockMark(0);
							receivables.setBankDepositRecord(null);
						}
					}
				}
				communityBankDepositBatch.setBatchState(1);
				
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}
	
	public IResponse  rollbackBatchState(CommunityBankDepositBatchReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityBankDepositBatchId()) {
			CommunityBankDepositBatchEntity communityBankDepositBatch = 
					communityBankDepositBatchDao.get(req.getCommunityBankDepositBatchId());
			if (null != communityBankDepositBatch && communityBankDepositBatch.getBatchState() == 1) {
				for(CommunityBankDepositRecordEntity bankDepositRecord : communityBankDepositBatch.getBankDepositRecordList()) {
					List<CommunityReceiptEntity> list = communityReceiptDao.getListByHql("select a from CommunityReceiptEntity a "
							+ "where a.bankDepositRecord.id="+bankDepositRecord.getId() ,"");
					Iterator<CommunityReceiptEntity> receiptIt = list.iterator();
			        while(receiptIt.hasNext()){
			        	CommunityReceiptEntity receipt = receiptIt.next();
	
						Iterator<CommunityReceiptReceivablesEntity> it = receipt.getReceiptReceivablesList().iterator();
				        while(it.hasNext()){
				        	CommunityReceiptReceivablesEntity o = it.next();
				        	CommunityReceivablesEntity receivables = o.getReceivables();
							if(o.getReceivedAmount().compareTo(new BigDecimal("0")) == 0) {
								receivables.setReceivedAmount(receivables.getReceivedAmount().subtract(o.getCurrentAmount()));
							}else {
								List<CommunityReceiptReceivablesEntity> rrList = communityReceiptReceivablesDao.getListByHql(
										"select a from CommunityReceiptReceivablesEntity a where a.receivables.id="+receivables.getId(),"");
								BigDecimal receivedAmount = new BigDecimal("0");
								for(CommunityReceiptReceivablesEntity p : rrList) {
									receivedAmount = receivedAmount.add(p.getCurrentAmount());
								}
									
								receivables.setReceivedAmount(receivedAmount.subtract(o.getCurrentAmount()));
							}
							receivables.setLockMark(1);
							receivables.getReceiptReceivablesList().remove(o);
							receivables.setBankDepositRecord(bankDepositRecord);
							o.setReceivables(null);
							o.setReceipt(null);
							communityReceiptReceivablesDao.delete(o);
							it.remove();
						}
				        receipt.setBankDepositRecord(null);	
				        receipt.setReceiptReceivablesList(null);
				        receipt.setEstate(null);
				        communityReceiptDao.delete(receipt);
				    }
			        
				}
				communityBankDepositBatch.setBatchState(0);
				
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
//	
//
//	
//	private String getReceiptCode(String receiptType) {
//		String prefixStr = getReceiptCodePrefix(receiptType);
//		Integer lastValue = generateSnLastValue(prefixStr);
//		String result = getReceiptCodePrefix(receiptType) + "_" + DateUtil.formatByStyle(new Date(), "yyyyMM") + "_" + CodeUtil.getCode(6, lastValue.toString()) ;
//		return result;
//	}
//	
//	private String getReceiptCodePrefix(String receiptType) {
//		if(StringUtils.isNotEmpty(receiptType)) {
//			if(receiptType.equals("系统收据")) {
//				return "XTSJ";
//			}
//			else if(receiptType.equals("其它收据")) {
//				return "QTSJ";
//			}
//			else if(receiptType.equals("银行划账")) {
//				return "YHSK";
//			}
//			else if(receiptType.equals("微信支付")) {
//				return "WXPAY";
//			}
//			else if(receiptType.equals("支付宝支付")) {
//				return "ALPAY";
//			}
//			else {
//				return "XTSJ"; 
//			}
//		}
//		else {
//			return "XTSJ"; 
//		}
//	}
	
}