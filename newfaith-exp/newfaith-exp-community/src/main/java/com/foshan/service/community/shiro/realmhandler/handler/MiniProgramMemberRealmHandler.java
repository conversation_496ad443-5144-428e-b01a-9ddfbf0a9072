package com.foshan.service.community.shiro.realmhandler.handler;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.AccountException;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.CredentialsException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.pam.UnsupportedTokenException;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.entity.RoleEntity;
import com.foshan.entity.community.CommunityMemberEntity;
import com.foshan.entity.community.CommunityRoleEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.form.community.CommunityRoleForm;
import com.foshan.form.community.request.CommunityMemberLoginReq;
import com.foshan.form.community.response.communityMember.CommunityMemberLoginRes;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.service.community.shiro.PhoneToken;
import com.foshan.service.community.shiro.realmhandler.AbstractMemberRealmHandler;
import com.foshan.service.community.shiro.realmhandler.RealmHandlerType;
import com.foshan.util.DigestUtil;
import com.foshan.util.WeiXinApiUtil;

/**
 * 党员小程序登陆授权 加密数据和初始向量非空的情况，新建用户，且进行登陆。否则直接做校验
 * 
 * <AUTHOR>
 *
 */
@Component
@RealmHandlerType("5")
public class MiniProgramMemberRealmHandler extends AbstractMemberRealmHandler {
	

	private final static Logger logger = LoggerFactory.getLogger(MiniProgramMemberRealmHandler.class);

	/*
	 * 微信小程序方式登陆的身份校验，查询相关的用户信息（此处用phone作为用户的id存于principals[0]，小程序opendi作为密码），并通过SimpleAuthenticationInfo返回，shiro框架通过SimpleAuthenticationInfo中principals[0]做为id及其他传递的数据进行校验。
	 * 此处限制用户登陆错误次数，错误次数存在缓存
	 * 返回用户信息数据前将部分用户信息存于principals[1]
	 */
	@Override
	public SimpleAuthenticationInfo handleAuthenticationInfo(AuthenticationToken token, String realmName) {
		String phone = (String) token.getPrincipal();

		if (StringUtils.isNotEmpty(phone)) {

			String hql = "from CommunityMemberEntity m where m.phone='" + phone + "' and phoneVerifyState = 1 and userState = 1";
			CommunityMemberEntity member = communityMemberDao.getUniqueByHql(hql);
			if (null != member) {
				String password = member.getMiniProgramOpenId();
				List<Object> principals = new ArrayList<Object>();
				principals.add(member.getPhone());
				CommunityMemberEntity shiroMember = new CommunityMemberEntity();
				shiroMember.setId(member.getId());
				shiroMember.setPhone(member.getPhone());
				shiroMember.setWeixin(member.getWeixin());
				shiroMember.setMiniProgramOpenId(member.getMiniProgramOpenId());
				shiroMember.setPassword(member.getPassword());
				principals.add(shiroMember);
				if (null != password) {
					return new SimpleAuthenticationInfo(principals, password, realmName);
				}
			} else {
				throw new UnknownAccountException();// 没找到帐号
			}
		}
		return null;
	}

	/*
	 * 微信小程序登陆处理逻辑：1、直接登录（只传code），2、注册/关联小程序openid后登录（传code+加密数据和初始向量）
	 * 先判断加密数据和初始向量是否非空，非空则执行注册后登录流程，空则执行直接登录流程
	 * 
	 * 1、直接登录（只传code）
	 * 前端先向微信获取code，
	 * 调用登陆接口时传递code到后端，后端通过code获取用户小程序openid，
	 * 后端根据小程序openid进行身份校验：先根据小程序openid查询用户数据，如果用户不存在则返回信息引导用户注册。
	 * 查询得到的phone作为校验token的id，小程序openid作为校验token的password
	 * 
	 * 2、注册/关联小程序openid后登录（传code+加密数据和初始向量）
	 * 前端先向微信获取code，加密数据和初始向量
	 * 调用登陆接口时传递code到后端，后端通过code获取用户小程序openid，
	 * 后端判断加密数据和初始向量非空则注册/关联小程序openid后登录，后端根据小程序openid进行身份校验：先根据小程序openid和微信返回的电话号码查询用户数据，判断用户属于未注册还是未关联小程序openid，如未注册则注册，如未关联则关联。
	 * 最后将查询得到的phone作为校验token的id，小程序openid作为校验token的password
	 * 
	 */
	@SuppressWarnings("unchecked")
	@Override
	public IResponse memberLogin(CommunityMemberLoginReq req, HttpServletRequest request, Subject curUser) {
		CommunityMemberLoginRes res = new CommunityMemberLoginRes();
		PhoneToken token = null;
		String wechatCode = req.getWechatCode();
		if (StringUtils.isBlank(wechatCode)) {
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO + "缺少js_code");
			return res;
		}

		// 先查看是否存在openid，无则引导注册，有则登陆
		@SuppressWarnings("unused")
		String accessTokenRes = null;

		//通过code向微信获取用户openid，code由前端向微信获取
		WeiXinApiUtil.JsCodeToSessionResult wxResult = WeiXinApiUtil.getSessionKeyByCode(
				contextInfo.getMiniProgramAppid(), contextInfo.getMiniProgramAppsecret(), wechatCode);

		String openid = wxResult.getOpenid();
		String phone = "";

		// 加密数据和初始向量非空的情况，新建用户，且进行登陆。否则直接做校验
		String encryptedData = req.getEncryptedData();
		String iv = req.getIv();
		// String signature = req.getSignature();
		if (StringUtils.isNotBlank(encryptedData) && StringUtils.isNotBlank(iv)) {
			String sessionKey = wxResult.getSession_key();
			Base64.Decoder decoder = Base64.getDecoder();
			byte[] dataByte = decoder.decode(encryptedData);
			byte[] keyByte = decoder.decode(sessionKey);
			byte[] ivByte = decoder.decode(iv);

			try {
				logger.debug("dataByte【" + dataByte + "】（keyByte ：" + keyByte + "）ivByte【" + ivByte + "】");
				String openData = DigestUtil.decrypt("AES/CBC/PKCS7Padding", dataByte, keyByte, ivByte, "UTF-8");
				logger.debug("微信用户openid【" + openid + "】（微信code " + wechatCode + "）加密数据明文为【" + openData + "】");
				ObjectMapper mapper = new ObjectMapper();
				mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
				OpenDataResult result = mapper.readValue(openData, OpenDataResult.class);
				// 开放数据签名校验
//				String openDataSha1 = SHA1Util.getSHA1(result + sessionKey);
//				logger.info("本地数据签名：" + openDataSha1);
//				if (!openDataSha1.equals(signature)) {
//					res.setRet(ResponseContext.RES_PERM_UNVALID_CODE);
//					res.setRetInfo(ResponseContext.RES_PERM_UNVALID_INFO);
//					return res;
//				}

				// 根据appid进行有效性校验
				WaterMark watermark = result.getWatermark();
				String watermarkAppid = watermark.getAppid();
				if (!watermarkAppid.equals(contextInfo.getMiniProgramAppid())) {
					logger.info("appid校验失败， watermark.appid：" + watermark.getAppid());
					res.setRet(ResponseContext.RES_PERM_UNVALID_CODE);
					res.setRetInfo(ResponseContext.RES_PERM_UNVALID_INFO);
					return res;
				}
				// 解密后获取到电话号码
				phone = result.getPhoneNumber();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			if (StringUtils.isBlank(phone)) {
				res.setRet(ResponseContext.RES_PERM_UNVALID_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_UNVALID_INFO + "获取电话号码失败！");
				return res;
			}
			CommunityMemberEntity newMember = communityMemberDao.getUniqueByHql("from CommunityMemberEntity where phone = '" + phone
					+ "' and phoneVerifyState = " + EntityContext.RECORD_STATE_VALID);
			// 判断是否已经绑定该openid
			CommunityMemberEntity miniProgramMember = communityMemberDao.getUniqueByHql("from CommunityMemberEntity where miniProgramOpenId = '"
					+ openid + "' and phoneVerifyState = " + EntityContext.RECORD_STATE_VALID);
			if (null == newMember && null == miniProgramMember) {
				// 用户未注册，且openid未被关联，则注册且关联openid
				String[] rolerId = communityContextInfo.getMemberRoleId().split(",");
				List<RoleEntity> roleList = new ArrayList<RoleEntity>();
				for (String id : rolerId) {
					RoleEntity role = roleDao.get(Integer.valueOf(id));
					if (null != role) {
						roleList.add(role);
					}
				}
				newMember = new CommunityMemberEntity();
				newMember.setPhone(phone);
				newMember.setNickName(phone);
				newMember.setLoginName(phone);
				newMember.setRegistName("WX" + phone);
				newMember.setCommunityRoleList(roleList);
				newMember.setPhoneVerifyState(EntityContext.RECORD_STATE_VALID);
				newMember.setMiniProgramOpenId(openid);
				newMember.setUserState(EntityContext.RECORD_STATE_VALID);
				communityMemberDao.save(newMember);
				logger.info("新注册用户：电话号码【" + phone + "】关联小程序openid【" + openid + "】");
			} else if (null != newMember && null == miniProgramMember) {
				// 用户已经注册，openid未被关联，则关联openid
				newMember.setMiniProgramOpenId(openid);
				logger.info("已注册用户：电话号码【" + phone + "】关联openid【" + openid + "】");
			} else if (null != miniProgramMember && !phone.equals(miniProgramMember.getPhone())) {
				// openid已经被非本电话号码用户关联
				String phoneBind = miniProgramMember.getPhone();
				logger.info("电话号码【" + phoneBind + "】已经与小程序openid【" + miniProgramMember.getMiniProgramOpenId() + "】绑定！");
				res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
				res.setRetInfo("该小程序账号已经与手机号码为" + phoneBind.substring(0, 3) + "****" + phoneBind.substring(7, 11)
						+ "的账号绑定,请解绑后再进行绑定");
				return res;
			}
		} else {
			CommunityMemberEntity m = communityMemberDao.getUniqueByHql("from CommunityMemberEntity where miniProgramOpenId = '" + openid
					+ "' and phoneVerifyState = " + EntityContext.RECORD_STATE_VALID);
			if (null == m) {
				res.setRet(ResponseContext.RES_PERM_NONEXISTENT_CODE);
				res.setRetInfo(ResponseContext.RES_PERM_NONEXISTENT_INFO + "请先完成注册/小程序授权。");
				return res;
			}
			phone = m.getPhone();
		}

		token = new PhoneToken(phone, openid, req.getType());
		token.setRememberMe(true);

		try {
			curUser.login(token);
			curUser.getSession().setTimeout(1800000);// 180000毫秒/30分钟超时
			PrincipalCollection principals = curUser.getPrincipals();
			List<Object> principalList = principals.asList();
			CommunityMemberEntity principal = (CommunityMemberEntity) principalList.get(1);
			res.setMemberId(principal.getId());
			res.setPhone(principal.getPhone());
			res.setWeixin(principal.getWeixin());
			res.setSmartcardId(principal.getSmartcardId());
			List<RoleEntity> communityRoleList = principal.getCommunityRoleList();
			List<CommunityRoleForm> communityRoleFormList = new ArrayList<CommunityRoleForm>();
			communityRoleList.forEach(o->{
				CommunityRoleForm r = new CommunityRoleForm();
				r.setRoleId(o.getId());
				r.setRoleName(o.getRoleName());
				r.setDisplayName(o.getDisplayName());
				communityRoleFormList.add(r);
			});
			res.setCommunityRoleList(communityRoleFormList);
			Session session = curUser.getSession();
			CommunityMemberEntity member = (CommunityMemberEntity) session.getAttribute("PrincipalEntity");
			if (StringUtils.isEmpty(member.getPassword())) {
				res.setIsEmptyPassword(1);
			} else {
				res.setIsEmptyPassword(0);
			}
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			return res;
		} catch (UnknownAccountException e0) {// 账号不存在
			res.setRet(ResponseContext.RES_PERM_NONEXISTENT_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
			return res;

		} catch (IncorrectCredentialsException e1) {// 账号/密码错误
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
			return res;
		} catch (LockedAccountException e2) {// 账号被锁定
			res.setRet(ResponseContext.RES_PERM_LOCK_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO + "账号被锁定");
			return res;
		} catch (AccountException e3) {// 账号异常
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
			return res;
		} catch (CredentialsException e4) {// 凭证异常
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
			return res;
		} catch (UnsupportedTokenException e5) {// 不支持的token异常
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
			return res;
		} catch (AuthenticationException e6) {// 未知登录异常
			res.setRet(ResponseContext.RES_PERM_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_PERM_ERROR_INFO);
			return res;
		}
	}

	// 加密数据json
	public static class OpenDataResult {
		public OpenDataResult() {
			super();
		}

		private String phoneNumber;// 用户绑定的手机号（国外手机号会有区号）
		private String purePhoneNumber;// 没有区号的手机号
		private String countryCode;// 区号
		private WaterMark watermark;// 水印

		public String getPhoneNumber() {
			return phoneNumber;
		}

		public void setPhoneNumber(String phoneNumber) {
			this.phoneNumber = phoneNumber;
		}

		public String getPurePhoneNumber() {
			return purePhoneNumber;
		}

		public void setPurePhoneNumber(String purePhoneNumber) {
			this.purePhoneNumber = purePhoneNumber;
		}

		public String getCountryCode() {
			return countryCode;
		}

		public void setCountryCode(String countryCode) {
			this.countryCode = countryCode;
		}

		public WaterMark getWatermark() {
			return watermark;
		}

		public void setWatermark(WaterMark watermark) {
			this.watermark = watermark;
		}

	}

	// 水印json
	public static class WaterMark {
		public WaterMark() {

		}

		private String appid;
		private Integer timestamp;

		public String getAppid() {
			return appid;
		}

		public void setAppid(String appid) {
			this.appid = appid;
		}

		public Integer getTimestamp() {
			return timestamp;
		}

		public void setTimestamp(Integer timestamp) {
			this.timestamp = timestamp;
		}

	}
}
