package com.foshan.service.community;

import com.foshan.form.community.request.WarningMessageReq;
import com.foshan.form.response.IResponse;

public interface IWarningMessageService {
    public IResponse getWarningMessageList(WarningMessageReq req);
    public IResponse getWarningMessageRecordList(WarningMessageReq req);
//	public IResponse addWarningMessage(WarningMessageReq req);
	public IResponse modifyWarningMessage(WarningMessageReq req);
//	public IResponse deleteWarningMessage(WarningMessageReq req);
	public IResponse getWarningMessageInfo(WarningMessageReq req);
}
