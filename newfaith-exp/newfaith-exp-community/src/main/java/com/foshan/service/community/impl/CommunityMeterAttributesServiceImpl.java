package com.foshan.service.community.impl;

import org.springframework.stereotype.Service;

import com.foshan.entity.community.CommunityMeterAttributesEntity;
import com.foshan.entity.community.CommunityMeterEntity;
import com.foshan.entity.community.CommunityMeterPriceHistoryEntity;
import com.foshan.entity.context.EntityContext;
import com.foshan.service.annotation.Audit;
import com.foshan.service.community.ICommunityMeterAttributesService;
import com.foshan.util.DateUtil;

import org.springframework.transaction.annotation.Transactional;
import com.foshan.form.community.request.CommunityMeterAttributesReq;
import com.foshan.form.community.response.communityMeterAttributes.AddCommunityMeterAttributesRes;
import com.foshan.form.community.response.communityMeterAttributes.ModifyCommunityMeterAttributesRes;
import com.foshan.form.community.response.communityMeterPriceHistory.GetCommunityMeterPriceHistoryListRes;
import com.foshan.form.community.response.communityMeterAttributes.GetCommunityMeterAttributesInfoRes;
import com.foshan.form.community.response.communityMeterAttributes.GetCommunityMeterAttributesListRes;
import com.foshan.form.community.CommunityMeterAttributesForm;
import com.foshan.form.community.CommunityMeterForm;
import com.foshan.form.community.CommunityMeterPriceHistoryForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.form.response.IResponse;
import com.foshan.dao.generic.Page;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Transactional
@Service("communityMeterAttributesService")
public class CommunityMeterAttributesServiceImpl extends GenericCommunityService implements ICommunityMeterAttributesService{

	@Override
	public IResponse getCommunityMeterAttributesList(CommunityMeterAttributesReq req) {
		GetCommunityMeterAttributesListRes res = new GetCommunityMeterAttributesListRes();
		Page<CommunityMeterAttributesEntity> page = new Page<CommunityMeterAttributesEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityMeterAttributesEntity a where 1=1 ");
		hql.append(null!=req.getCategory() ? " and a.category="+req.getCategory():"")
			.append(StringUtils.isNotEmpty(req.getAttributeName()) ? " and a.attributeName like'%"+req.getAttributeName()+"%'":"");
		hql.append(" ORDER BY a.id desc");
		page = communityMeterAttributesDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityMeterAttributesForm communityMeterAttributesForm = new CommunityMeterAttributesForm();
			communityMeterAttributesForm.setCommunityMeterAttributesId(o.getId());
            communityMeterAttributesForm.setAttributeName(o.getAttributeName());
            communityMeterAttributesForm.setCategory(o.getCategory());
            communityMeterAttributesForm.setComment(o.getComment());
            communityMeterAttributesForm.setMeasureUnit(o.getMeasureUnit());
            communityMeterAttributesForm.setRanges(o.getRanges());
            communityMeterAttributesForm.setRate(null!=o.getRate() ? o.getRate().toString() : "");
            communityMeterAttributesForm.setUnitPrice(null!=o.getUnitPrice() ? o.getUnitPrice().toString() : "");
			res.getCommunityMeterAttributesList().add(communityMeterAttributesForm);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@Override
	@Audit(operate = "新增表属性")
	public IResponse addCommunityMeterAttributes(CommunityMeterAttributesReq req) {
		AddCommunityMeterAttributesRes res = new AddCommunityMeterAttributesRes();
		if (StringUtils.isNotEmpty(req.getUnitPrice())) {
			CommunityMeterAttributesEntity communityMeterAttributes = new CommunityMeterAttributesEntity();
            communityMeterAttributes.setAttributeName(req.getAttributeName());
            communityMeterAttributes.setCategory(req.getCategory());
            communityMeterAttributes.setComment(req.getComment());
            communityMeterAttributes.setMeasureUnit(req.getMeasureUnit());
            communityMeterAttributes.setRanges(req.getRanges());
            communityMeterAttributes.setRate(StringUtils.isNotEmpty(req.getRate()) ?  new BigDecimal(req.getRate()) : null);
            communityMeterAttributes.setUnitPrice(StringUtils.isNotEmpty(req.getUnitPrice()) ?  new BigDecimal(req.getUnitPrice()).setScale(4, RoundingMode.HALF_UP) : null);
			communityMeterAttributesDao.save(communityMeterAttributes);
			CommunityMeterPriceHistoryEntity meterPriceHistory = new CommunityMeterPriceHistoryEntity();
			meterPriceHistory.setChangeDate(new Date());
			meterPriceHistory.setMeterAttributes(communityMeterAttributes);
			meterPriceHistory.setOperationType(0);
			meterPriceHistory.setUnitPrice(communityMeterAttributes.getUnitPrice());
			meterPriceHistory.setState(EntityContext.RECORD_STATE_VALID);
			meterPriceHistory.setLastModifyTime(new Timestamp(new Date().getTime()));
			communityMeterPriceHistoryDao.save(meterPriceHistory);
			
			res.setRet(ResponseContext.RES_SUCCESS_CODE);
			res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "修改表属性")
	public IResponse modifyCommunityMeterAttributes(CommunityMeterAttributesReq req) {
		ModifyCommunityMeterAttributesRes res = new ModifyCommunityMeterAttributesRes();
		if (null!=req.getCommunityMeterAttributesId() && StringUtils.isNotEmpty(req.getUnitPrice())) {
			CommunityMeterAttributesEntity communityMeterAttributes = communityMeterAttributesDao.get(req.getCommunityMeterAttributesId()) ;
			if(null != communityMeterAttributes){
                communityMeterAttributes.setAttributeName(req.getAttributeName());
                communityMeterAttributes.setCategory(req.getCategory());
                communityMeterAttributes.setComment(req.getComment());
                communityMeterAttributes.setMeasureUnit(req.getMeasureUnit());
                communityMeterAttributes.setRanges(req.getRanges());
                communityMeterAttributes.setRate(StringUtils.isNotEmpty(req.getRate()) ?  new BigDecimal(req.getRate()) : null);
                BigDecimal UnitPrice = StringUtils.isNotEmpty(req.getUnitPrice()) ?  
                		new BigDecimal(req.getUnitPrice()).setScale(4, RoundingMode.HALF_UP) : null;
                if(null != UnitPrice && UnitPrice.compareTo(communityMeterAttributes.getUnitPrice()) != 0) {
                	communityMeterAttributes.setUnitPrice(UnitPrice);
        			CommunityMeterPriceHistoryEntity meterPriceHistory = new CommunityMeterPriceHistoryEntity();
        			meterPriceHistory.setChangeDate(new Date());
        			meterPriceHistory.setMeterAttributes(communityMeterAttributes);
        			meterPriceHistory.setOperationType(0);
        			meterPriceHistory.setUnitPrice(UnitPrice);
        			meterPriceHistory.setState(EntityContext.RECORD_STATE_VALID);
        			meterPriceHistory.setLastModifyTime(new Timestamp(new Date().getTime()));
        			communityMeterPriceHistoryDao.save(meterPriceHistory);
                }
                
				res.setCommunityMeterAttributesId(communityMeterAttributes.getId());
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}

	@Override
	@Audit(operate = "删除表属性")
	public IResponse deleteCommunityMeterAttributes(CommunityMeterAttributesReq req) {
		GenericResponse res = new GenericResponse();
		if (null != req.getCommunityMeterAttributesId()) {
		CommunityMeterAttributesEntity communityMeterAttributes = communityMeterAttributesDao.get(req.getCommunityMeterAttributesId());
			if (null != communityMeterAttributes) {
				communityMeterAttributesDao.deleteById(req.getCommunityMeterAttributesId());
				//communityMeterAttributes.setState(EntityContext.RECORD_STATE_INVALID);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
                res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityMeterAttributesInfo(CommunityMeterAttributesReq req) {
		GetCommunityMeterAttributesInfoRes res = new GetCommunityMeterAttributesInfoRes();
		if (null != req.getCommunityMeterAttributesId()) {
			CommunityMeterAttributesEntity communityMeterAttributes = communityMeterAttributesDao.get(req.getCommunityMeterAttributesId());
			if (null != communityMeterAttributes) {
				CommunityMeterAttributesForm communityMeterAttributesForm = new CommunityMeterAttributesForm();
				communityMeterAttributesForm.setCommunityMeterAttributesId(communityMeterAttributes.getId());
                communityMeterAttributesForm.setAttributeName(communityMeterAttributes.getAttributeName());
                communityMeterAttributesForm.setCategory(communityMeterAttributes.getCategory());
                communityMeterAttributesForm.setComment(communityMeterAttributes.getComment());
                communityMeterAttributesForm.setMeasureUnit(communityMeterAttributes.getMeasureUnit());
                communityMeterAttributesForm.setRanges(communityMeterAttributes.getRanges());
                communityMeterAttributesForm.setRate(null!=communityMeterAttributes.getRate() ? communityMeterAttributes.getRate().toString() : "");
                communityMeterAttributesForm.setUnitPrice(null!=communityMeterAttributes.getUnitPrice() ? communityMeterAttributes.getUnitPrice().toString() : "");
				communityMeterAttributes.getMeterList().forEach(o->{
					CommunityMeterForm communityMeterForm = new CommunityMeterForm();
					communityMeterForm.setIsApportioned(o.getIsApportioned());
					communityMeterForm.setCommunityMeterId(o.getId());
	                communityMeterForm.setAllocationMethod(o.getAllocationMethod());
	                communityMeterForm.setComment(o.getComment());
	                communityMeterForm.setExpirationDates(null != o.getExpirationDates()? DateUtil.formatLongFormat(o.getExpirationDates()) : "");
	                communityMeterForm.setInitialData(null!=o.getInitialData() ? o.getInitialData().toString() : "");
	                communityMeterForm.setInstallationsite(o.getInstallationsite());
	                communityMeterForm.setIsCommon(o.getIsCommon());
	                communityMeterForm.setLevel(o.getLevel());
	                communityMeterForm.setMeterCode(o.getMeterCode());
	                communityMeterForm.setMeterName(o.getMeterName());
	                communityMeterForm.setOldData(o.getOldData());
	                communityMeterForm.setOldId(o.getOldId());

	                communityMeterForm.setChargeCategory(o.getChargeCategory());
	                communityMeterForm.setIsDisabled(o.getIsDisabled());
	                communityMeterForm.setPayItemsName(o.getPayItemsName());
	                communityMeterAttributesForm.getMeterList().add(communityMeterForm);
				});
                res.setCommunityMeterAttributesForm(communityMeterAttributesForm);
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			} else {
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}
		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}

		return res;
	}

	@Override
	public IResponse getCommunityMeterPriceHistoryList(CommunityMeterAttributesReq req) {
		GetCommunityMeterPriceHistoryListRes res = new GetCommunityMeterPriceHistoryListRes();
		Page<CommunityMeterPriceHistoryEntity> page = new Page<CommunityMeterPriceHistoryEntity>();
		page.setPageSize(null != req.getPageSize() ? req.getPageSize() : 10);
		page.setBeginCount((null != req.getRequestPage() ? req.getRequestPage() : 1 - 1) * page.getPageSize());
		page.setCurrentPage(null != req.getRequestPage() ? req.getRequestPage() : 1);
		StringBuilder hql = new StringBuilder("select distinct a from CommunityMeterPriceHistoryEntity a inner join a.meterAttributes b where 1=1 ");
		hql.append(null!=req.getCommunityMeterAttributesId() ? " and b.id="+req.getCommunityMeterAttributesId():"")
			.append(StringUtils.isNotEmpty(req.getAttributeName()) ? " and b.attributeName like'%"+req.getAttributeName()+"%'":"");
		hql.append(" ORDER BY a.id desc");
		page = communityMeterPriceHistoryDao.queryPage(page, hql.toString());

		res.setTotalResult(page.getTotalCount());
		res.setPageSize(page.getPageSize());
		res.setCurrentPage(page.getCurrentPage());
		res.setTotal(page.getTotalPage());
		page.getResultList().forEach(o -> {
			CommunityMeterPriceHistoryForm communityMeterPriceHistory = new CommunityMeterPriceHistoryForm();
			communityMeterPriceHistory.setCommunityMeterPriceHistoryId(o.getId());
			communityMeterPriceHistory.setAttributeName(o.getMeterAttributes().getAttributeName());
			communityMeterPriceHistory.setCreateTime(DateUtil.formatLongFormat(o.getCreateTime()));
			communityMeterPriceHistory.setMeterAttributesId(o.getMeterAttributes().getId());
			communityMeterPriceHistory.setOperationType(o.getOperationType());
			communityMeterPriceHistory.setUnitPrice(o.getUnitPrice().toString());

			res.getCommunityMeterPriceHistoryFormList().add(communityMeterPriceHistory);
		});
		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}
	
	@Override
	@Audit(operate = "表属性绑定表")
	public IResponse attributesBindingMeter(CommunityMeterAttributesReq req) {
		GenericResponse res = new GenericResponse();
		if (null!=req.getCommunityMeterAttributesId() && StringUtils.isNotEmpty(req.getMeterIdList())) {
			CommunityMeterAttributesEntity communityMeterAttributes = communityMeterAttributesDao.get(req.getCommunityMeterAttributesId()) ;
			if(null != communityMeterAttributes){
				String[] meterIds = req.getMeterIdList().split(",");
				communityMeterAttributes.setMeterList(null);
				List<CommunityMeterEntity> meterList = new ArrayList<CommunityMeterEntity>();
				for (String meterId : meterIds) {
					meterList.add(communityMeterDao.get(Integer.parseInt(meterId)));
				}
				communityMeterAttributes.setMeterList(meterList);
                
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}
	
	@Override
	@Audit(operate = "表属性解绑表")
	public IResponse attributesUnbindingMeter(CommunityMeterAttributesReq req) {
		GenericResponse res = new GenericResponse();
		if (null!=req.getCommunityMeterAttributesId() && StringUtils.isNotEmpty(req.getMeterIdList())) {
			CommunityMeterAttributesEntity communityMeterAttributes = communityMeterAttributesDao.get(req.getCommunityMeterAttributesId()) ;
			if(null != communityMeterAttributes){
				String[] meterIds = req.getMeterIdList().split(",");
				//List<CommunityMeterEntity> meterList = new ArrayList<CommunityMeterEntity>();
				for (String meterId : meterIds) {
					CommunityMeterEntity meter =  communityMeterDao.get(Integer.parseInt(meterId));
					
					if(communityMeterAttributes.getMeterList().contains(meter)) {
						communityMeterAttributes.getMeterList().remove(meter);
					}
				}
                
				res.setRet(ResponseContext.RES_SUCCESS_CODE);
				res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
			}else{
				res.setRet(ResponseContext.RES_DATA_ERROR_CODE);
				res.setRetInfo(ResponseContext.RES_DATA_ERROR_INFO);
			}

		} else {
			res.setRet(ResponseContext.RES_NULL_ERROR_CODE);
			res.setRetInfo(ResponseContext.RES_NULL_ERROR_INFO);
		}
		return res;
	}


}