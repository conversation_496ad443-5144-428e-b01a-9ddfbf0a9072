package com.foshan.service.community;

import javax.servlet.http.HttpServletResponse;

import com.foshan.form.community.request.CommunityInvoiceReq;
import com.foshan.form.response.IResponse;

public interface ICommunityInvoiceService {
	
	public IResponse issueInvoice(CommunityInvoiceReq req);
	public IResponse cancelRedDashedApplication(CommunityInvoiceReq req);
	public IResponse queryResult(CommunityInvoiceReq req);
	public IResponse generateXml(CommunityInvoiceReq req,HttpServletResponse response);
	public IResponse getCommunityInvoiceList(CommunityInvoiceReq req);
	public void communityQueryInvoiceResultTask();
    public IResponse getbWSms();
    public IResponse verifyBWSms(CommunityInvoiceReq req);
	
}
