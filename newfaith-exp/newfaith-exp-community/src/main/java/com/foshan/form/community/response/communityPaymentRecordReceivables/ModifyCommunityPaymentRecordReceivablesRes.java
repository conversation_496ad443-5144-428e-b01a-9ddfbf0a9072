package com.foshan.form.community.response.communityPaymentRecordReceivables;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收的付款记录(ModifyCommunityPaymentRecordReceivablesRes)")
@JsonInclude(Include.NON_NULL)
public  class ModifyCommunityPaymentRecordReceivablesRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer communityPaymentRecordReceivablesId;
  @ApiModelProperty(value = "",example="1")
    private Integer paymentRecordId;
    @ApiModelProperty(value = "",example="1")
    private Integer receivablesId;
  
}
