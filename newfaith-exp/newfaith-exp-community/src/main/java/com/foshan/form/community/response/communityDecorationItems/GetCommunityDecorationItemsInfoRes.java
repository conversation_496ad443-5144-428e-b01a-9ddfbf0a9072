package com.foshan.form.community.response.communityDecorationItems;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityDecorationItemsForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="装修项目项目(GetCommunityDecorationItemsInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityDecorationItemsInfoRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "装修项目项目对象")
	private CommunityDecorationItemsForm communityDecorationItemsForm ; 

}
