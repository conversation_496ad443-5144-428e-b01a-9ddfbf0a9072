package com.foshan.form.community;


import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="事件类型项目(CommunityEventCategoryItemsForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityEventCategoryItemsForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1117568742951876137L;
	@ApiModelProperty(value = "事件类型项目ID",example="1")
	private Integer eventCategoryItemsId;
	@ApiModelProperty(value = "项目名称",example="1")
	private String itemName;
	@ApiModelProperty(value = "项目名称对应key",example="1")
	private String itemkey;
	@ApiModelProperty(value = "排序",example="1")
	private Integer orders;
	@ApiModelProperty(value = "数据类型 0：字符串；1：数字；2：时间；3：文件；4：单选；5：多选；6：日期；7：日期范围；8:时间范围；",example="1")
	private Integer dataType;
	@ApiModelProperty(value = "是否必填，0：否；1是；",example="1")
	private Integer isRequiredng;
	@ApiModelProperty(value = "标签，区别于其他事件项目，用于装修项目或其他特别项目")
	private String itemTag;
	@ApiModelProperty(value = "映射字段（云之家）")
	private String reflectionField;
	@ApiModelProperty(value = "是否级联，0：否；1：是；",example="1")
	private Integer isCascade;
	@ApiModelProperty(value = "级联值")
	private String cascadeValue;
	@ApiModelProperty(value = "当dataType=4或5时，对应的选项数据")
	private List<Object> options = new ArrayList<Object>();
	@ApiModelProperty(value = "子事件类型项目")
	private List<CommunityEventCategoryItemsForm> subEventCategoryItemsList = new ArrayList<CommunityEventCategoryItemsForm>();
	
	public CommunityEventCategoryItemsForm(Integer eventCategoryItemsId,String itemName,String itemkey,
			Integer orders,Integer dataType,List<Object> options,Integer isRequiredng) {
		this.eventCategoryItemsId = eventCategoryItemsId;
		this.itemName = itemName;
		this.itemkey = itemkey;
		this.orders = orders;
		this.dataType = dataType;
		this.options = options;
		this.isRequiredng = isRequiredng;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
