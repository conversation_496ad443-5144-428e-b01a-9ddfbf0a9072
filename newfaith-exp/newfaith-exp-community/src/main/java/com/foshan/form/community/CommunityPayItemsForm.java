package com.foshan.form.community;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = " 收费项目设定(CommunityPayItemsForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityPayItemsForm implements IForm {

	private static final long serialVersionUID = 301396383145281610L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer communityPayItemsId;
	@ApiModelProperty(value = "'费用类别 1:管理费；2：车位费；3：分摊费；4：商管部；5：违约金；6：其他；", example = "1")
	private Integer chargeCategory;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "结束时间")
	private String endTime;
	@ApiModelProperty(value = "价格单位（如：元/平方米）")
	private String priceUnit;
	@ApiModelProperty(value = "是否生成应收款", example = "1")
	private Integer isReceivables;
	@ApiModelProperty(value = "项目名称")
	private String itemsName;
	@ApiModelProperty(value = "管理费计算类型：1、按收费面积计算--管理费；2、按附加面积计算--花园管理费;3、按个数计算--车位租用")
	private Integer feeCalType;
	@ApiModelProperty(value = "费用计算公式")
	private String feeFormula;
	@ApiModelProperty(value = "旧表数据")
	private String oldData;
	@ApiModelProperty(value = "表ID")
	private String oldId;
	@ApiModelProperty(value = "交款日")
	private Integer payDate;
	@ApiModelProperty(value = "单价")
	private String price;
	@ApiModelProperty(value = "开始时间")
	private String startTime;
	@ApiModelProperty(value = "开始时间")
	private String createTime;
	@ApiModelProperty(value = "修改时间")
	private String lastModifyTime;
	@ApiModelProperty(value = "状态 0--无效数据 1--有效数据")
	private Integer state;
	@ApiModelProperty(value = "是否产生违约金:0--不产生违约金 1--产生违约金")
	private Integer isBreach;
	@ApiModelProperty(value = "违约金名称")
	private String breachName;
    @ApiModelProperty(value = "违约金系数")
    private String breachRatio;
    @ApiModelProperty(value = "扣费优先级，值越大优先级越大", example = "1")
    private Integer priority;
    @ApiModelProperty(value = "是否默认银行划扣:0--否 1--是", example = "1")
	private Integer isBankDeposit;
    @ApiModelProperty(value = "发票信息")
    private String inviceInfo;
    @ApiModelProperty(value = "互斥组Id", example = "1")
   	private Integer groupFee; 
    @ApiModelProperty(value = "是否存参加优惠活动:0--否 1--是", example = "1")
    private Integer enableBenefitPolicy;
	@ApiModelProperty(value = "优惠策略开始生效时间(yyyy-MM-dd HH:mm:ss)")
	private String benefitStartDate;
    @ApiModelProperty(value = "取消优惠后的物业费单价（元），即不符合优惠后的物业费单价，数字保留两位小数", example = "2.00")
    private BigDecimal benefitOrginPrice; 
	@ApiModelProperty(value = "")
	private List<CommunityPayItemsPriceForm> payItemsPriceList = new ArrayList<>();
    
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
