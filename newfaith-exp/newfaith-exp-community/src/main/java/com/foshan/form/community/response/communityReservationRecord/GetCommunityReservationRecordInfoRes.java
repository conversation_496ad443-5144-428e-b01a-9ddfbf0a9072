package com.foshan.form.community.response.communityReservationRecord;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityReservationRecordForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取预约记录详情对象(GetCommunityReservationRecordInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityReservationRecordInfoRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5505884410738076096L;
	@ApiModelProperty(value = "预约记录对象列表")
	private CommunityReservationRecordForm reservationRecordForm ; 

}
