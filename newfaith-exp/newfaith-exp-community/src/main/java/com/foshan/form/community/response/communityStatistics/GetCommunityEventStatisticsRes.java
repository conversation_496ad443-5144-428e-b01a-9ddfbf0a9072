package com.foshan.form.community.response.communityStatistics;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区事项统计返回对象(GetCommunityEventStatisticsRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCommunityEventStatisticsRes extends BaseResponse {
    /**
     *
     */
    private static final long serialVersionUID = 6060850119011003034L;

    @ApiModelProperty(value = "事项分布")
    private Map<String ,Integer> eventDistribution;
    @ApiModelProperty(value = "事项状态")
    private Map<String ,List<Map<String , Integer>>> eventStates;
    @ApiModelProperty(value = "事项状态")
    private List<Map<String ,Map<String , Integer>>> monthlyEventStatistics;


    public GetCommunityEventStatisticsRes(String ret, String retInfo){
        super(ret,retInfo);
    }


}
