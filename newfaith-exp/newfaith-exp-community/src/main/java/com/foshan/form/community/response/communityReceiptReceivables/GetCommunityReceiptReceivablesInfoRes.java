package com.foshan.form.community.response.communityReceiptReceivables;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityReceiptReceivablesForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收款-收款单据(GetCommunityReceiptReceivablesInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityReceiptReceivablesInfoRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2127929019825831074L;
	@ApiModelProperty(value = "应收款-收款单据对象")
	private CommunityReceiptReceivablesForm communityReceiptReceivablesForm ; 

}
