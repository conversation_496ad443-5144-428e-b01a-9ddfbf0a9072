package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收统计对象(ReceivablesStatisticsForm)")
@JsonInclude(Include.NON_NULL)
public  class ReceivablesStatisticsForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7008022650577582468L;
    @ApiModelProperty(value = "小区名称")
    private String districtName;
    @ApiModelProperty(value = "已收金额")
    private String receivedAmount;
    @ApiModelProperty(value = "应收金额")
    private String receivableAmount;
    @ApiModelProperty(value = "收缴率")
    private String collectionRate;
    
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
