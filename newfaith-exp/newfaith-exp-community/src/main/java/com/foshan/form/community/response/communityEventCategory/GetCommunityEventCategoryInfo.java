package com.foshan.form.community.response.communityEventCategory;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityEventCategoryForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取事件类型详情返回对象(GetCommunityEventCategoryInfo)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityEventCategoryInfo extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7525055669612578036L;
	@ApiModelProperty(value = "事件类型")
	private CommunityEventCategoryForm communityEventCategoryForm;

}
