package com.foshan.form.community.response.communityDecorationAttachment;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="装修项目附件(ModifyCommunityDecorationAttachmentRes)")
@JsonInclude(Include.NON_NULL)
public  class ModifyCommunityDecorationAttachmentRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer communityDecorationAttachmentId;
  @ApiModelProperty(value = "附件名称")
    private String attachmentName;
    @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "排序",example="1")
    private Integer orders;
    @ApiModelProperty(value = "",example="1")
    private Integer attachmentId;
  
}
