package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 非划扣收费项目(CommunityBankDepositEstatePayItemsForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityBankDepositEstatePayItemsForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3948604849229544749L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer bankDepositEstatePayItemsId;
    @ApiModelProperty(value = "0-全部不划扣 1-仅不划扣违约金（若收费项目产生违约金，则需要根据此标识判断）",example="1")
    private Integer depositType;
    @ApiModelProperty(value = "单元")
    private CommunityEstateForm estateForm;
    @ApiModelProperty(value = "收费项目")
    private CommunityPayItemsForm payItemsForm;
    
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
