package com.foshan.form.community.response.deviceCorrelation;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.DeviceCorrelationForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="账号设备表(GetDeviceCorrelationListRes)")
public class GetDeviceCorrelationListRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "账号设备表列表")
	private List<DeviceCorrelationForm> deviceCorrelationList = new ArrayList<DeviceCorrelationForm>(); 

}
