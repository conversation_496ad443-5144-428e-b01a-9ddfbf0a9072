package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="商品(CommunityGoodsForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityGoodsForm {
	
//	@ApiModelProperty(value = "百望：0.正常行,默认值1.折扣行2.被折扣行3.混合行，需开票接口处理折扣",example="1")
//	private Integer lineKind;
//	@ApiModelProperty(value = "商品名称")
//	private String goodsName;
//	@ApiModelProperty(value = "商品税收分类编码")
//	private String taxKindCode;
//	@ApiModelProperty(value = "商品税目")
//	private String taxItem;
//	@ApiModelProperty(value = "商品编码,纳税人自行编码")
//	private String goodsCode;
//	@ApiModelProperty(value = "规格型号")
//	private String specName;
//	@ApiModelProperty(value = "计量单位元/米，元/顿，元/件")
//	private String saleUnit;
//	@ApiModelProperty(value = "商品数量")
//	private String qty;
//	@ApiModelProperty(value = "税率")
//	private String taxRate;
//	@ApiModelProperty(value = "含税单价")
//	private String price;
//	@ApiModelProperty(value = "含税金额")
//	private String totalAmount;
//	@ApiModelProperty(value = "不含税单价")
//	private String nonTaxPrice;
//	@ApiModelProperty(value = "不含税金额")
//	private String amount;
//	@ApiModelProperty(value = "税额")
//	private String taxAmount;
//	@ApiModelProperty(value = "百望：含税标志0不含税,1含税")
//	private String taxFlag;
//	@ApiModelProperty(value = "百望：免税类型,零税率标识,0.是正常税率,1.免税,2.不征税,3.普通零税率")
//	private String dutyFree;
//	@ApiModelProperty(value = "百望：是否使用优惠政策标识，0-不使用优惠政策；1-使用优惠政策")
//	private String couponFlag;
//	@ApiModelProperty(value = "优惠政策名称，例如：免税,先征后退,简易征收,按5%简易征收等")
//	private String couponPolicy;
//	@ApiModelProperty(value = "外部流水ID，用于回传时业务系统确定那行发票商品")
//	private String outId;
	@ApiModelProperty(value = "发票行性质 百望：0.正常行,默认值1.折扣行2.被折扣行3.混合行，需开票接口处理折扣",example="1")
	private Integer lineKind;
	@ApiModelProperty(value = "商品名称")
	private String goodsName;
	@ApiModelProperty(value = "商品税收分类编码")
	private String taxKindCode;
	@ApiModelProperty(value = "规格型号")
	private String specName;
	@ApiModelProperty(value = "计量单位元/米，元/顿，元/件")
	private String saleUnit;
	@ApiModelProperty(value = "商品数量")
	private String qty;
	@ApiModelProperty(value = "税率")
	private String taxRate;
	@ApiModelProperty(value = "含税单价")
	private String price;
	@ApiModelProperty(value = "含税金额")
	private String totalAmount;
	@ApiModelProperty(value = "不含税单价")
	private String nonTaxPrice;
	@ApiModelProperty(value = "不含税金额")
	private String amount;
	@ApiModelProperty(value = "税额")
	private String taxAmount;
	@ApiModelProperty(value = "百望：含税标志0不含税,1含税")
	private String taxFlag;
	@ApiModelProperty(value = "百望：免税类型,零税率标识,0.是正常税率,1.免税,2.不征税,3.普通零税率")
	private String dutyFree;
	@ApiModelProperty(value = "百望：是否使用优惠政策标识，0-不使用优惠政策；1-使用优惠政策")
	private String couponFlag;
	@ApiModelProperty(value = "优惠政策名称，例如：免税,先征后退,简易征收,按5%简易征收等")
	private String couponPolicy;
	@ApiModelProperty(value = "明细行号")
	private String lineNo;
	@ApiModelProperty(value = "商品编码,纳税人自行编码")
	private String goodsCode;
	@ApiModelProperty(value = "外部流水ID，用于回传时业务系统确定那行发票商品")
	private String outId;
}
