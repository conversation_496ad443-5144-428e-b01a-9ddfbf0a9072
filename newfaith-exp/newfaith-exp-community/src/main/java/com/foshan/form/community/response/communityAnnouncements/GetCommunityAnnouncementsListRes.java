package com.foshan.form.community.response.communityAnnouncements;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.community.CommunityAnnouncementsForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取社区公告返回列表对象(AddCommunityAnnouncementsListRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCommunityAnnouncementsListRes extends BasePageResponse {
    /**
     *
     */
    private static final long serialVersionUID = -9219834223739434308L;
    @ApiModelProperty(value = "社区公告返回列表")
    private List<CommunityAnnouncementsForm> communityAnnouncementsFormList = new ArrayList<CommunityAnnouncementsForm>();
}
