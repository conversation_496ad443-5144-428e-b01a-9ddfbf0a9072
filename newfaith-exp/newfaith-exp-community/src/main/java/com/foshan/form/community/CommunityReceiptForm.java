package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = " 收款单据(CommunityReceiptForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityReceiptForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1548114504954972466L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer communityReceiptId;
	@ApiModelProperty(value = "经办人")
	private String agent;
	@ApiModelProperty(value = "金额")
	private String amount;
	@ApiModelProperty(value = "交易银行账号")
	private String bankAccount;
	@ApiModelProperty(value = "出纳")
	private String cashier;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "本位币金额")
	private String currencyAmount;
	@ApiModelProperty(value = "汇率")
	private String exchangeRate;
	@ApiModelProperty(value = "币种")
	private String moneytype;
	@ApiModelProperty(value = "旧表数据")
	private String oldData;
	@ApiModelProperty(value = "表ID")
	private String oldId;
	@ApiModelProperty(value = "姓名")
	private String payerName;
	@ApiModelProperty(value = "收款方式名称，如：现金、微信、支付宝、银行转账等")
	private String paymentMethod;
	@ApiModelProperty(value = "收款类型 （0：应收款；1、退款； 2：预收）", example = "1")
	private Integer paymentType;
	@ApiModelProperty(value = "打印次数", example = "1")
	private Integer printNum;
	@ApiModelProperty(value = "单据编号")
	private String receiptCode;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "收款日期")
	private String receiptDate;
	@ApiModelProperty(value = "单据类别:收据、系统收据、其它收据、银行划账、微信支付、支付宝支付")
	private String receiptType;
	@ApiModelProperty(value = "主管")
	private String supervisor;
	@ApiModelProperty(value = "微信交易流水号")
	private String outTradeNo;
	@ApiModelProperty(value = "微信商户订单号")
	private String wxorderNo;
	@ApiModelProperty(value = "房屋单元ID" ,example="1")
	private String estateId;
	@ApiModelProperty(value = "单元地址（小区名称+栋号+单元号层数+房号）例如：御江南十六区5座2403")
	private String estateAddress;
	@ApiModelProperty(value = "单据pdf地址")
	private String pdfUrl;
	@ApiModelProperty(value = "是否已开发票 0:否；1：是；",example="1")
	private Integer haveInvoice;
	@ApiModelProperty(value = "费用类别 0:周期性收费；1：一次性收费；",example="1")
	private Integer feeType;
    @ApiModelProperty(value = "单据编号前缀，传对应的中文名称，后台转成对应的前缀：系统收据：XTSJ_，其它收据：QTSJ_，银行划账：YHSK_，微信支付：WXPAY_，支付宝支付：ALPAY_，默认为：XTSJ_，")
    private String receiptCodePrefix;
	@ApiModelProperty(value = "实收款列表")
	private List<CommunityReceiptReceivablesForm> receiptReceivablesList = new ArrayList<CommunityReceiptReceivablesForm>();
	@ApiModelProperty(value = "调整金额")
	private List<CommunityReceivablesChangesForm> receivablesChangesList =  new ArrayList<>();
	@ApiModelProperty(value = "收费项目")
	private CommunityPayItemsForm payItemsForm;
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
