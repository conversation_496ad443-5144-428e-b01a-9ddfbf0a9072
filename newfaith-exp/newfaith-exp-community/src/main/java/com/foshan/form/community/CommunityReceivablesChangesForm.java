package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = " 应收款变更（减加）(CommunityReceivablesChangesForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityReceivablesChangesForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 391216209868989167L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer communityReceivablesChangesId;
	@ApiModelProperty(value = "批准人")
	private String approver;
	@ApiModelProperty(value = "减加金额")
	private String changeAmount;
	@ApiModelProperty(value = "减加日期")
	private String changeDate;
	@ApiModelProperty(value = "减加类型 0:减免；1：添加；", example = "1")
	private Integer changeType;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "旧表数据")
	private String oldData;
	@ApiModelProperty(value = "表ID")
	private String oldId;
	@ApiModelProperty(value = "收费项目")
	private String payItemsName;
	@ApiModelProperty(value = "应收金额")
	private String receivableAmount;
	@ApiModelProperty(value = "应收日期")
	private String receivableDate;
	@ApiModelProperty(value = "已收金额")
	private String receivedAmount;
	@ApiModelProperty(value = "减加人（减加人）")
	private String recorder;
	@ApiModelProperty(value = "退款方式:0--现金，1--银行转账，3--在线支付原路退还", example = "1")
	private Integer refundMethod;
	@ApiModelProperty(value = "减加人（减加人）")
	private String refundMethodName;
	@ApiModelProperty(value = "退款方式名称")
	private CommunityReceivablesForm receivablesForm;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
