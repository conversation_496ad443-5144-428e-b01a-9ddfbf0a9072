package com.foshan.form.community.response.communityReceivables;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import com.foshan.form.community.CommunityReceivablesForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收款(GetCommunityReceivablesListReq)")
public class GetCommunityReceivablesListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5859752722638313333L;
	
	@ApiModelProperty(value = "小区名称")
	private String districtName;
	@ApiModelProperty(value = "地址")
	private String unitAddress;
	@ApiModelProperty(value = "房号")
	private String unitCode;
	@ApiModelProperty(value = "户主姓名")
	private String accountName;
	@ApiModelProperty(value = "收费面积")
	private String chargingArea;
	private List<CommunityReceivablesForm> communityReceivablesList = new ArrayList<>(); 
	@ApiModelProperty(value = "reultType = 0 （默认）应收款列表返回<estateId,<receiveDate,<payItemsName,Set<communityReceivables>>>>")
	private Map<Integer,Map<String,Map<String,Set<CommunityReceivablesForm>>>> receivablesList = new TreeMap<>();
	@ApiModelProperty(value = "reultType = 2 应收款列表返回<estateId,<payItemsName,Set<communityReceivables>>>")
	private Map<Integer,Map<String,Set<CommunityReceivablesForm>>> receivablesToNameList = new TreeMap<>();
	
	@ApiModelProperty(value = "总未收金额")
	private BigDecimal totalReceivableAmount = BigDecimal.ZERO;
	@ApiModelProperty(value = "总已收金额")
	private BigDecimal totalReceivedAmount = BigDecimal.ZERO;
	@ApiModelProperty(value = "总未收金额")
	private BigDecimal totalNotReceivedAmount = BigDecimal.ZERO;
	
}
