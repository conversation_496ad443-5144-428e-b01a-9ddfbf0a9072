package com.foshan.form.community.response.communityPropertyService;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="创建维修单(CreatePropertyServicePdfRes)")
@JsonInclude(Include.NON_NULL)
public  class CreatePropertyServicePdfRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3191241623184986490L;
	@ApiModelProperty(value = "pdf地址")
	private String pdfUrl;

}
