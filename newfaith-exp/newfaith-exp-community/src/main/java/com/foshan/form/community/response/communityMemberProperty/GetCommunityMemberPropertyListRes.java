package com.foshan.form.community.response.communityMemberProperty;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityMemberPropertyForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取会员资产列表返回对象(GetCommunityMemberPropertyListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityMemberPropertyListRes extends BasePageResponse {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = 2449872802497663047L;
	@ApiModelProperty(value = "会员资产信息",example="1")
	private List<CommunityMemberPropertyForm> memberPropertyList = new ArrayList<CommunityMemberPropertyForm>();


	public GetCommunityMemberPropertyListRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}


}
