package com.foshan.form.community.response.communityBankDepositBatch;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="银行划账批次(CommunityBankDepositBatchRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityBankDepositBatchRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer communityBankDepositBatchId;
  @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
    private Integer state;
    @ApiModelProperty(value = "银行名称")
    private String bankName;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "划入日期")
    private String depositDate;
    @ApiModelProperty(value = "截止日期")
    private String endTime;
    @ApiModelProperty(value = "收费项目(多个用逗号隔开 例如：别墅管理费,车库公共电费,车位管理费,车位管理费违约金)")
    private String payItems;
    @ApiModelProperty(value = "起始日期")
    private String startTime;
  
}
