package com.foshan.form.community.statistics;

import java.util.Map;
import java.util.TreeMap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "收款透视统计数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityReceiptVo implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = 5176964954240373687L;
	@ApiModelProperty(value = "楼盘编号")
	private String districtCode;
	@ApiModelProperty(value = "楼盘名称")
	private String districtName;
	@ApiModelProperty(value = "单据编号")
	private String receiptCode;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "楼阁")
	private String buildingName;
	@ApiModelProperty(value = "应收日期")
	private String receivableDate;
	@ApiModelProperty(value = "应收月份")
	private String receivableMonth;
	@ApiModelProperty(value = "收款日期")
	private String receiptDate;
	@ApiModelProperty(value = "收款方式")
	private String paymentmethod;
	@ApiModelProperty(value = "经办人")
	private String agent;
	private Map<String, String> feeMap = new TreeMap<>();

	public String getNewDistrictCode() {
		return (districtCode.length() == 1 ? "00" + districtCode
				: districtCode.length() == 2 ? "0" + districtCode : districtCode) + districtName;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
