package com.foshan.form.community.response.communityPaymentRecord;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityPaymentRecordForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="在线支付发起的付款记录(GetCommunityPaymentRecordListReq)")
public class GetCommunityPaymentRecordListRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "在线支付发起的付款记录列表")
	private List<CommunityPaymentRecordForm> communityPaymentRecordList = new ArrayList<CommunityPaymentRecordForm>(); 

}
