package com.foshan.form.community;

import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "租赁单元表金额对象(RentMeterAllocationForm)")
@JsonInclude(Include.NON_NULL)
public class RentMeterAllocationForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8916841251596113464L;
	@ApiModelProperty(value = "分摊量")
	private String allocationNum;
	@ApiModelProperty(value = "分摊金额")
	private String allocationAmount;
	@ApiModelProperty(value = "单元ID",example="1")
	private Integer estateId;
	@ApiModelProperty(value = "单元编码")
	private String unitCode;
	@ApiModelProperty(value = "表ID",example="1")
	private Integer meterId;
	@ApiModelProperty(value = "收费项目ID",example="1")
	private Integer payItemsId;
	@ApiModelProperty(value = "收费项目名称")
	private String payItemsName;
	@ApiModelProperty(value = "额外用量")
	private String additionalAmount;
	@ApiModelProperty(value = "额外用量说明")
	private String additionalInstructions;
	@ApiModelProperty(value = "本次读数")
	private String recordNum;
	@ApiModelProperty(value = "上次读数")
	private String lastNum;
	@ApiModelProperty(value = "单价")
	private String unitPrice;
	@ApiModelProperty(value = "抄表日期")
	private String recordDate;
	@ApiModelProperty(value = "应收金额")
	private String receivableAmount;
	@ApiModelProperty(value = "类别（1:电表、2:水表、3:临时表、4:代收水表、5:代收电表）",example="1")
	private Integer category;
	@ApiModelProperty(value = "开始时间")
	private String startDate;
	@ApiModelProperty(value = "结束时间")
	private String endDate;
	@ApiModelProperty(value = "表名称")
	private String meterName;
	@ApiModelProperty(value = "表编号")
	private String meterCode;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
