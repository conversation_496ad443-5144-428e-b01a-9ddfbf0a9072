package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 总表(CommunityMeterForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityMeterForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8773122455082320906L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityMeterId;
    @ApiModelProperty(value = "分摊类别：1按面积分摊，2自定义公式、3按个数",example="1")
    private Integer allocationMethod;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "表坏日期")
    private String expirationDates;
    @ApiModelProperty(value = "底数")
    private String initialData;
	@ApiModelProperty(value = "是否分摊 0-否，1-是")
	private Integer isApportioned;
	@ApiModelProperty(value = "分摊周期 单位:月")
	private Integer allocationPeriod;
    @ApiModelProperty(value = "安装地点")
    private String installationsite;
    @ApiModelProperty(value = "是否共用表 0-不共用（用户表），1-共用（总表）")
    private Integer isCommon;
    @ApiModelProperty(value = "状态 0--无效数据 1--有效数据")
    private Integer state;
    @ApiModelProperty(value = "层次（树形结构层次）",example="1")
    private Integer level;
    @ApiModelProperty(value = "表编号")
    private String meterCode;
    @ApiModelProperty(value = "表名称")
    private String meterName;
    @ApiModelProperty(value = "旧表数据")
    private String oldData;
    @ApiModelProperty(value = "旧表ID")
    private String oldId;
    @ApiModelProperty(value = "",example="1")
    private Integer meterAttributesId;
    @ApiModelProperty(value = "",example="1")
    private Integer formulaId;
    @ApiModelProperty(value = "",example="1")
    private Integer parentMeterId;
    @ApiModelProperty(value = "收费项目类别（冗余）")
    private String chargeCategory;
    @ApiModelProperty(value = "是否坏表 0-否，1-是",example="1")
    private Integer isDisabled;
    @ApiModelProperty(value = "收费项目")
    private String payItemsName;
    @ApiModelProperty(value = "单元编号")
    private String unitCode;
    @ApiModelProperty(value = "上次分摊日期")
    private String lastAllocateDate;
    @ApiModelProperty(value = "父表")
    private CommunityMeterForm parentMeterForm;
    @ApiModelProperty(value = "子表")
    private List<CommunityMeterForm> subMeterList = new ArrayList<CommunityMeterForm>();
    @ApiModelProperty(value = "属性")
    private CommunityMeterAttributesForm meterAttributesForm;
    @ApiModelProperty(value = "自定义分摊公式")
    private CommunityMeterFormulaForm meterFormulaForm;
    @ApiModelProperty(value = "收费项目")
    private CommunityPayItemsForm payItemsForm;
    

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
