package com.foshan.form.community.response.communityMeterRecord;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="抄表(CommunityMeterRecordRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityMeterRecordRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -2179691173789400345L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityMeterRecordId;
    @ApiModelProperty(value = "recordDate",example="1")
    private String expirationDates;
    @ApiModelProperty(value = "是否归零",example="1")
    private Boolean isZero;
    @ApiModelProperty(value = "旧表数据",example="1")
    private String oldData;
    @ApiModelProperty(value = "表ID",example="1")
    private String oldId;
    @ApiModelProperty(value = "本次读数",example="1")
    private String recordNum;
    @ApiModelProperty(value = "抄表人",example="1")
    private String recorder;
    @ApiModelProperty(value = "",example="1")
    private Integer meterId;
    @ApiModelProperty(value = "",example="1")
    private Integer parentMeterRecordId;

}
