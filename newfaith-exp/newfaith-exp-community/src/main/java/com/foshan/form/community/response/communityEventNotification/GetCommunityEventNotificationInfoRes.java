package com.foshan.form.community.response.communityEventNotification;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityEventNotificationForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="件通知详情(GetCommunityEventNotificationInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityEventNotificationInfoRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3739195029258588171L;
	@ApiModelProperty(value = "件通知对象")
	private CommunityEventNotificationForm eventNotificationForm ; 

}
