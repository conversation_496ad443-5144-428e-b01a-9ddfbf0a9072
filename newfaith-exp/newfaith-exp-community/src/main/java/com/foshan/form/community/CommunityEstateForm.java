package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="房产(CommunityEstateForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityEstateForm extends CommunityPropertyForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8052699755265325141L;
	@ApiModelProperty(value = "房产ID", example = "1")
	private Integer estateId;
    @ApiModelProperty(value = "房屋状态：0:未收楼;1:已入住;2:装修中;3:出租;4:离退;5:自主", example = "1")
    private Integer estateState;
	@ApiModelProperty(value = "是否验收 0否；1是", example = "1")
	private Integer isAcceptanceReceive;
	@ApiModelProperty(value = "建筑面积")
	private String buildingArea;
	@ApiModelProperty(value = "使用期限")
	private String usageTerm;
	@ApiModelProperty(value = "单元编号")
	protected String unitCode;
	@ApiModelProperty(value = "朝向")
	protected String orientation;
    @ApiModelProperty(value = "所属楼层", example = "1")
    private Integer floor;
    @ApiModelProperty(value = "房号")
    private String roomNumber;
    @ApiModelProperty(value = "使用面积")
    private String usableArea;
    @ApiModelProperty(value = "收费面积")
    private String chargingArea;
    @ApiModelProperty(value = "附加面积")
    private String additionalArea;
    @ApiModelProperty(value = "装修标准")
    private String decoration;
    @ApiModelProperty(value = "房产类型  如：住宅 、商铺 、车位 、公共区域", example = "1")
    private String estateType;
    @ApiModelProperty(value = "图片ID", example = "1")
    private Integer assetId;
	@ApiModelProperty(value = "unitCode(单元编号),页面树状结构用")
	private String label;
    @ApiModelProperty(value = "是否可预约的 0否 1是", example = "1")
    private Integer isReserved;
    @ApiModelProperty(value = "特殊费用标识 0：正常计费 1、不计管理费 2、不计分摊费 3、不计违约金 4、全部不计 5、不计租金", example = "1")
    private Integer specialFeeFlag;
	@ApiModelProperty(value = "预留字段", example = "1")
	protected String reservedField;
	@ApiModelProperty(value = "是否选中", example = "1")
	protected Integer isSelected;
	@ApiModelProperty(value = "收楼日期")
	private String acceptanceDate;
	@ApiModelProperty(value = "单元地址")
	private String unitAddress;
	@ApiModelProperty(value = "特殊分摊标识 0：正常计费 1、不分电表 2、不分水表 3、全部不分")
	private Integer specialAllocationFlag;
	@ApiModelProperty(value = "欠费原因")
	private String reasonArrears;
	
}
