package com.foshan.form.community.response.communityPaymentRecord;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="在线支付发起的付款记录(CommunityPaymentRecordRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityPaymentRecordRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer communityPaymentRecordId;
  @ApiModelProperty(value = "金额")
    private String amount;
    @ApiModelProperty(value = "交易银行账号")
    private String bankAccount;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "支付人员终端ID，付款人员微信的openId")
    private String payerIdentify;
    @ApiModelProperty(value = "姓名")
    private String payerName;
    @ApiModelProperty(value = "付款日期")
    private String paymentDate;
    @ApiModelProperty(value = "付款方式：微信小程序，微信扫码，微信刷卡")
    private String paymentMethod;
    @ApiModelProperty(value = "付款流水编号")
    private String paymentRecordCode;
    @ApiModelProperty(value = "收款类型 （0：应收款； 2：预收；）",example="1")
    private Integer paymentType;
    @ApiModelProperty(value = "0--未发起（初始化），1--已经发起，2--支付成功，3--支付失败")
    private Boolean status;
    @ApiModelProperty(value = "第三方在线支付系统交易流水号")
    private String wxTradeNo;
    @ApiModelProperty(value = "",example="1")
    private Integer estateId;
    @ApiModelProperty(value = "",example="1")
    private Integer receiptId;
  
}
