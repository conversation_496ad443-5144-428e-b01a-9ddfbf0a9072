package com.foshan.form.community;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 自定义分摊公式(CommunityMeterFormulaForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityMeterFormulaForm implements IForm {
	private static final long serialVersionUID = -5181288797522617936L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityMeterFormulaId;
    @ApiModelProperty(value = "备注",example="1")
    private String comment;
    @ApiModelProperty(value = "动态参数")
	private Map<String,BigDecimal> dynamicParameter = new HashMap<>();  
    @ApiModelProperty(value = "上次调整时间")
    private String lastDate;
    @ApiModelProperty(value = "公式名称",example="1")
    private String formulaName;
    @ApiModelProperty(value = "旧表数据",example="1")
    private String oldData;
    @ApiModelProperty(value = "表ID",example="1")
    private String oldId;
    @ApiModelProperty(value = "表ID")
    private CommunityFormulaTempleteForm templeteForm;
    @ApiModelProperty(value = "日志")
    private List<LinkedHashMap> operateLog = new ArrayList<>();//OperationDiaryForm

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
