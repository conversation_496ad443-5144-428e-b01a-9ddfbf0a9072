package com.foshan.form.community.response.communityMeasures;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityMeasuresForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="单元欠费采取措施表(GetCommunityMeasuresInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityMeasuresInfoRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "单元欠费采取措施表对象")
	private CommunityMeasuresForm communityMeasuresForm ; 

}
