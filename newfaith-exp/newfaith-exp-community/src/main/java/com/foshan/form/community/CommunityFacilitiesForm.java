package com.foshan.form.community;


import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="设施管理(CommunityFacilitiesForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityFacilitiesForm extends CommunityPropertyForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -1081775726949293129L;
	
	@ApiModelProperty(value = "事件ID",example="1")
	private Integer eventId;
	@ApiModelProperty(value = "型号")
	private String model;
	@ApiModelProperty(value = "条形码")
	private String barcodes;
	@ApiModelProperty(value = "生产商")
	private String manufacturers;
	@ApiModelProperty(value = "供应商")
	private String supplier;
	@ApiModelProperty(value = "售后电话")
	private String supportPhone;
	@ApiModelProperty(value = "设备类型  0门禁",example="1")
	private Integer facilitiesType;
	@Column(columnDefinition = "int(2) comment '设备状态，0未启用,1在用，3报废'")
	private Integer facilitiesState;
	@ApiModelProperty(value = "投入使用日期")
	protected String useDate;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
