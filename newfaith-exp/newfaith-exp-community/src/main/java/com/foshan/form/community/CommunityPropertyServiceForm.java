package com.foshan.form.community;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="报修服务对象(CommunityPropertyServiceForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityPropertyServiceForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1162126167640869845L;
	@ApiModelProperty(value = "报修服务ID", example = "1")
	private Integer propertyServiceId;
	@ApiModelProperty(value = "创建时间")
	protected String createTime;
	@ApiModelProperty(value = "报修号")
	private String eventsCode;
	@ApiModelProperty(value = "标题")
	private String title;
    @ApiModelProperty(value = "报修时间")
    private String reportTime;
	@ApiModelProperty(value = "服务状态（新增、跟进、维修、完成）")
	private String serviceState;
	@ApiModelProperty(value = "说明（备注）")
	private String centent;
	@ApiModelProperty(value = "经办人（接待员）")
	private String agent;
	@ApiModelProperty(value = "申请人")
	private String client;
	@ApiModelProperty(value = "联系电话")
	private String phone;
	@ApiModelProperty(value = "资产Id", example = "1")
	private Integer propertyId;
	@ApiModelProperty(value = "维修地点")
	private String address;
	@ApiModelProperty(value = "报修类别")
	private String serviceType;
	@ApiModelProperty(value = "报修内容")
	private String  reportContent;
	@ApiModelProperty(value = "主管部门")
	private String competentDepartment;
	@ApiModelProperty(value = "维修员")
	private String handler;
    @ApiModelProperty(value = "派工时间")
    private String dispatchingTime;
    @ApiModelProperty(value = "完成时间")
    private String completionTime;
	@ApiModelProperty(value = "维修内容")
	private String serviceContent;
	@ApiModelProperty(value = "验收员")
	private String receiver;
	@ApiModelProperty(value = "服务评价（不满意、基本满意、满意、很满意、非常满意）")
	private String  serviceEvaluation;
	@ApiModelProperty(value = "人工费")
	private String laborCost;
	@ApiModelProperty(value = "材料费")
	private String materialCost;
	@ApiModelProperty(value = "是否入账 0否 1是", example = "1")
	private Integer isGeneratedBills;
    @ApiModelProperty(value = "旧表数据")
    private String oldData;
    @ApiModelProperty(value = "旧表ID")
    private String oldId;
	@ApiModelProperty(value = "收费项目ID", example = "1")
	private Integer payItemsId;
    @ApiModelProperty(value = "单元编号")
    private String unitCode;
	@ApiModelProperty(value = "回访意见")
	private String feedback;
	@ApiModelProperty(value = "回访人")
	private String visitCommissione;
	@ApiModelProperty(value = "订单类别：0-有偿单，1-外发单", example = "1")
	private Integer orderCategory;
	
	public CommunityPropertyServiceForm(Integer propertyServiceId,String eventsCode,String title,String reportTime,
			String serviceState,String centent,String agent,String client,String phone,Integer propertyId,String address,
			String serviceType,String  reportContent,String competentDepartment,String handler,String dispatchingTime,
			String completionTime,String serviceContent,String receiver,String  serviceEvaluation,String laborCost,
			String materialCost,Integer isGeneratedBills,Integer orderCategory) {
		this.propertyServiceId = propertyServiceId;
		this.eventsCode = eventsCode;
		this.title = title;
		this.reportTime = reportTime;
		this.serviceState = serviceState;
		this.centent = centent;
		this.agent = agent;
		this.client = client;
		this.phone = phone;
		this.propertyId = propertyId;
		this.address = address;
		this.serviceType = serviceType;
		this.reportContent = reportContent;
		this.competentDepartment = competentDepartment;
		this.handler = handler;
		this.dispatchingTime = dispatchingTime;
		this.completionTime = completionTime;
		this.serviceContent = serviceContent;
		this.receiver = receiver;
		this.serviceEvaluation = serviceEvaluation;
		this.laborCost = laborCost;
		this.materialCost = materialCost;
		this.isGeneratedBills = isGeneratedBills;
		this.orderCategory = orderCategory;
	}
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	

}
