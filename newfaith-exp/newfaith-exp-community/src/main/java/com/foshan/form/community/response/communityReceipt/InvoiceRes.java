package com.foshan.form.community.response.communityReceipt;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="开发票返回(InvoiceRes)")
@JsonInclude(Include.NON_NULL)
public  class InvoiceRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8337557389195877515L;
    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;
    @ApiModelProperty(value = "发票号码")
    private String invoiceNo;
    @ApiModelProperty(value = "发票类型代")
    private String invoiceKindCode;
    @ApiModelProperty(value = "开票日期")
    private String invoiceDate;
    @ApiModelProperty(value = "PDF下载地址")
    private String pdfUrl;
    @ApiModelProperty(value = "xml下载地址")
    private String xmlUrl;
    @ApiModelProperty(value = "ofd下载地址")
    private String ofdUrl;
    @ApiModelProperty(value = "发票请求流水号")
    private String reqSerialNo;
    @ApiModelProperty(value = "红字发票信息确认单编号")
    private String redInfoNo;
    @ApiModelProperty(value = "红字发票信息确认单id")
    private String redInfoId;
}
