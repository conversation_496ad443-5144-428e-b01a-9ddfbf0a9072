package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="预约日期对象(CommunityReservationDateForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityReservationDateForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 449276660688881012L;
	@ApiModelProperty(value = "活动ID", example = "1")
	private Integer reservationDateId;
	@ApiModelProperty(value = "是否开始预约 0否；1是", example = "1")
	private Integer isStart;
	@ApiModelProperty(value = "状态 0--无效数据 1--有效数据", example = "1")
	private Integer state;
	@ApiModelProperty(value = "日期")
	private String reservationDate;
	@ApiModelProperty(value = "预约时间段")
	private List<CommunityReservatPeriodForm> periodFormList = new ArrayList<CommunityReservatPeriodForm>();
	
	public CommunityReservationDateForm(Integer reservationDateId,String reservationDate,Integer isStart,Integer state) {
		this.reservationDateId = reservationDateId;
		this.reservationDate = reservationDate;
		this.isStart = isStart;
		this.state = state;
	}
	
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
