package com.foshan.form.community.response.communityDistrict;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="小区(ModifyCommunityDistrictRes)")
@JsonInclude(Include.NON_NULL)
public  class ModifyCommunityDistrictRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 7564880555572644492L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityDistrictId;
    @ApiModelProperty(value = "地址",example="1")
    private String address;
    @ApiModelProperty(value = "占地面积",example="1")
    private String areaSize;
    @ApiModelProperty(value = "户数",example="1")
    private String housingNumber;
    @ApiModelProperty(value = "小区名称",example="1")
    private String name;
    @ApiModelProperty(value = "土地年限",example="1")
    private String period;
    @ApiModelProperty(value = "容积率",example="1")
    private String ratio;
    @ApiModelProperty(value = "",example="1")
    private Integer communityId;
    @ApiModelProperty(value = "幢数",example="1")
    private String buildingNumber;
    @ApiModelProperty(value = "备注",example="1")
    private String comment;
    @ApiModelProperty(value = "竣工日期",example="1")
    private String completionDate;
    @ApiModelProperty(value = "小区编号",example="1")
    private String districtCode;
    @ApiModelProperty(value = "移交日期",example="1")
    private String handoverDate;
    @ApiModelProperty(value = "人口数",example="1")
    private String population;
    @ApiModelProperty(value = "拿地时间",example="1")
    private String takeTime;

}
