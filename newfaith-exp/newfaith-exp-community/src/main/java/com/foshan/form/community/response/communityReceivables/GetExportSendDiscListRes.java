package com.foshan.form.community.response.communityReceivables;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.ExportSendDiscForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取划账送盘数据列表(GetExportSendDiscListRes)")
public class GetExportSendDiscListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 5859752722638313333L;
	@ApiModelProperty(value = "划账送盘数据列表")
	private List<ExportSendDiscForm> exportSendDiscList = new ArrayList<ExportSendDiscForm>(); 
	
}
