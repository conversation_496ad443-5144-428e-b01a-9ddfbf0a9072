package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 告警消息(EventRecordForm)")
@JsonInclude(Include.NON_NULL)
public class EventRecordForm {
	
	private Integer event_record_id;
	private Integer warningMessageId;
	private String general_resources_min_url;
	private String event_type_name;
	private String event_time;
	private String device_name;
	private Integer event_processing_status_id;
	private String event_metadata;
	private String unitCode;
	private String unitAddress;

}
