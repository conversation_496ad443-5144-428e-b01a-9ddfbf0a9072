package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AssetForm;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 装修项目附件(CommunityDecorationAttachmentForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityDecorationAttachmentForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer communityDecorationAttachmentId;
  @ApiModelProperty(value = "附件名称")
    private String attachmentName;
    @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "排序",example="1")
    private Integer orders;
    @ApiModelProperty(value = "",example="1")
    private Integer attachmentId;
    @ApiModelProperty(value = "附件")
    private AssetForm assetForm;
    @ApiModelProperty(value = "装修项目")
    private List<CommunityDecorationItemsBriefForm> decorationItemsBriefList = new ArrayList<>();
  
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
