package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 装修项目项目(CommunityDecorationItemsBriefForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityDecorationItemsBriefForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityDecorationItemsId;
    @ApiModelProperty(value = "项目名称")
    private String itemName;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
