package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class OptionsVoForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7167405884828527808L;
	@ApiModelProperty(value = "选项ID" , example = "1")
	private Integer optionsId;
	@ApiModelProperty(value = "选项名称" )
	private String OptionName;


	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
