package com.foshan.form.community.response.communityBankDepositRecord;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityBankDepositRecordForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="银行划账记录(GetCommunityBankDepositRecordInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityBankDepositRecordInfoRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "银行划账记录对象")
	private CommunityBankDepositRecordForm communityBankDepositRecordForm ; 

}
