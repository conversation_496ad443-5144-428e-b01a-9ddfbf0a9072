package com.foshan.form.community;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="预设记录对象(CommunityReservationRecordForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityReservationRecordForm implements IForm {

	
	/**
	 * 
	 */
	private static final long serialVersionUID = -8837001153119245115L;
	@ApiModelProperty(value = "记录ID", example = "1")
	private Integer recordId;
	@ApiModelProperty(value = "说明")
	private String centent;
	@ApiModelProperty(value = "预约人数", example = "1")
	protected Integer reservationNum;	
	@ApiModelProperty(value = "预约状态 0预约成功 1已核销 2取消", example = "1")
	protected Integer reservationState;
	@ApiModelProperty(value = "活动对象")
	private CommunityReservationActivitiesForm activitiesForm;
	@ApiModelProperty(value = "预约日期对象")
	private CommunityReservationDateForm reservationDateForm;
	@ApiModelProperty(value = "会员")
	private CommunityMemberForm member;
	
	public CommunityReservationRecordForm(Integer recordId,String centent,Integer reservationNum,Integer reservationState) {
		this.recordId = recordId;
		this.centent = centent;
		this.reservationNum = reservationNum;
		this.reservationState = reservationState;
	}
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
