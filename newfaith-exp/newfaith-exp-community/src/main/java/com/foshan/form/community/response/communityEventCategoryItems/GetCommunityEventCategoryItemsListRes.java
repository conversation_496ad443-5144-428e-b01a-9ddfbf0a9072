package com.foshan.form.community.response.communityEventCategoryItems;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityEventCategoryItemsForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取事件类型项目返回列表对象(GetCommunityEventCategoryItemsListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityEventCategoryItemsListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -781239875620978748L;
	@ApiModelProperty(value = "事件类型项目对象列表")
	private List<CommunityEventCategoryItemsForm> eventCategoryItemsList = new ArrayList<CommunityEventCategoryItemsForm>();
	

	public GetCommunityEventCategoryItemsListRes(String ret, String retInfo) {
		super(ret, retInfo);
	}


}
