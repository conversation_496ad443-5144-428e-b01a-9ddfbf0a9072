package com.foshan.form.community.response.communityStatistics;

import java.util.LinkedList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.response.BaseResponse;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetReceiptDjbFileListRes extends BaseResponse {
	/**
	* 
	*/
	private static final long serialVersionUID = 7673621069077885879L;
	private List<String> fileList = new LinkedList<>();;
}
