package com.foshan.form.community.response.communityStatistics;

import java.util.LinkedList;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取住户信息列表")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetMemberPropertyRes<T extends IForm> extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 6398216536889017744L;
	private String fileName;
	private LinkedList<T> dataList = new LinkedList<>();
}
