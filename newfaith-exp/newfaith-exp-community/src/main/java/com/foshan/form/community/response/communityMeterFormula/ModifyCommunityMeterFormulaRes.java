package com.foshan.form.community.response.communityMeterFormula;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="自定义分摊公式(ModifyCommunityMeterFormulaRes)")
@JsonInclude(Include.NON_NULL)
public  class ModifyCommunityMeterFormulaRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1544891396258552904L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityMeterFormulaId;
    @ApiModelProperty(value = "备注",example="1")
    private String comment;
    @ApiModelProperty(value = "公式描述",example="1")
    private String formulaDetail;
    @ApiModelProperty(value = "公式名称",example="1")
    private String formulaName;
    @ApiModelProperty(value = "旧表数据",example="1")
    private String oldData;
    @ApiModelProperty(value = "表ID",example="1")
    private String oldId;

}
