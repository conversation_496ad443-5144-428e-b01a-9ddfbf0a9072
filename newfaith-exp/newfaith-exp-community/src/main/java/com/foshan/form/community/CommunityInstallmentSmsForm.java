package com.foshan.form.community;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import com.foshan.form.UserForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="账单短信(CommunityInstallmentSmsForm)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityInstallmentSmsForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4067573363355127911L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer installmentSmsId;
	@ApiModelProperty(value = "名称")
	private String installmentName;
    @ApiModelProperty(value = "期数",example="1")
    private Integer periods;
    @ApiModelProperty(value = "金额",example="1")
    private String amount;
    @ApiModelProperty(value = "发送日期")
    private String sendDate;
    @ApiModelProperty(value = "发送状态，0：否；1：已发；",example="1")
    private Integer sendStatus;
    @ApiModelProperty(value = "支付状态，0：未支付；1：已支付；",example="1")
    private Integer payStatus;
    @ApiModelProperty(value = "使用状态，0：未使用；1：已使用；",example="1")
    private Integer useStatus;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "最后修改时间")
	private String lastModifyTime;
	@ApiModelProperty(value = "单元ID",example="1")
	private Integer estateId;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "子账单短信")
	private List<CommunityInstallmentSmsForm> subInstallmentSmsList = new ArrayList<>();
	@ApiModelProperty(value = "管家")
	private List<UserForm> userList = new ArrayList<>();
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
