package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.ColumnForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区栏目对象(CommunityColumnForm)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityColumnForm extends ColumnForm {/**
	 * 
	 */
	private static final long serialVersionUID = 5734251252712765724L;
	@ApiModelProperty(value = "初审数量",example="1")
	private Integer firstAuditCount;
	@ApiModelProperty(value = "终审数量",example="1")
	private Integer finalAuditCount;

}
