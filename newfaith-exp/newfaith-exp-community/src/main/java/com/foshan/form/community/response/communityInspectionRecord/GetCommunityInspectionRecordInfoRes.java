package com.foshan.form.community.response.communityInspectionRecord;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AssetForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取巡查记录返回详情对象(GetCommunityInspectionRecordInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityInspectionRecordInfoRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4415036398289787200L;
	private Integer inspectionRecordId;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "签到时间")
	private String lastModifyTime;
	@ApiModelProperty(value = "签到人")
	private String recorder;
	@ApiModelProperty(value = "签到 0：否；1：已签到；",example="1")
	private Integer signIn;
	@ApiModelProperty(value = "订单编号")
	private String orderCode;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "姓名")
	private String userName;
	@ApiModelProperty(value = "手机号")
	private String phone;
	@ApiModelProperty(value = "项目")
	private String centent;
	@ApiModelProperty(value = "巡查意见")
	private String opinion;
	@ApiModelProperty(value = "事件ID",example="1")
	private Integer eventId;
	@ApiModelProperty(value = "单元ID",example="1")
	private Integer estateId;
	@ApiModelProperty(value = "巡查项")
	//private String itemsDetail;
	private List<Object> itemsDetail = new ArrayList<>();
	@ApiModelProperty(value = "图片")
	private AssetForm assetForm;
	
}
