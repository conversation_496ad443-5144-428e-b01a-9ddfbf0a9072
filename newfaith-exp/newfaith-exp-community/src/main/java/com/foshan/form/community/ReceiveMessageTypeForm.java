package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 接收消息类型表(ReceiveMessageTypeForm)")
@JsonInclude(Include.NON_NULL)
public  class ReceiveMessageTypeForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer receiveMessageTypeId;
  @ApiModelProperty(value = "对象名称")
    private String objectName;
    @ApiModelProperty(value = "类型值，0：应急广播；1：小程序；2：短信；",example="1")
    private Integer typeValue;
  
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
