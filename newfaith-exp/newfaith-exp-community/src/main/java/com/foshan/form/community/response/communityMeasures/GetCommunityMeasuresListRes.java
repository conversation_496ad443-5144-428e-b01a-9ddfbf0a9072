package com.foshan.form.community.response.communityMeasures;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityMeasuresForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="单元欠费采取措施表(GetCommunityMeasuresListReq)")
public class GetCommunityMeasuresListRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "单元欠费采取措施表列表")
	private List<CommunityMeasuresForm> communityMeasuresList = new ArrayList<CommunityMeasuresForm>(); 

}
