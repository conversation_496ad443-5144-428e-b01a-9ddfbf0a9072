package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="设备关联(DeviceCorrelationForm)")
@JsonInclude(Include.NON_NULL)
public  class DeviceCorrelationForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer deviceCorrelationId;
    @ApiModelProperty(value = "时间")
    private String createTime;
	@ApiModelProperty(value = "区域码（应急广播时需要）")
	private String regionCode;
    @ApiModelProperty(value = "")
    private DeviceForm device;
    @ApiModelProperty(value = "")
    private ReceiveMessageTypeForm receiveMessageType;
    @ApiModelProperty(value = "")
    private CommunityMemberForm member;
  
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
