package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = " 应收款(CommunityReceivablesForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityReceivablesForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8473377373566422501L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer communityReceivablesId;
	@ApiModelProperty(value = "项目类别")
	private String chargeCategory;
	@ApiModelProperty(value = "费用类别(用于排序) 1:管理费；2：车位费；3：分摊费；4：商管部；5：违约金；6：其它类；7：押金类；8:有偿服务费；9:出租租金", example = "1")
	private Integer payItemsCategory;
	@ApiModelProperty(value = "项目来源")
	private String chargeSource;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "截止日期")
	private String endTime;
	@ApiModelProperty(value = "收费项目")
	private String payItemsName;
	@ApiModelProperty(value = "应收属期")
	private String paymentPeriod;
	@ApiModelProperty(value = "应收金额")
	private String receivableAmount;
	@ApiModelProperty(value = "应收日期")
	private String receivableDate;
	@ApiModelProperty(value = "数量", example = "1")
	private Integer quantity;
	@ApiModelProperty(value = "子房产Id")
	private Integer subEstateId;
	@ApiModelProperty(value = "已收金额")
	private String receivedAmount;
	@ApiModelProperty(value = "未收金额")
	private String notReceivedAmount;
	@ApiModelProperty(value = "来源备注")
	private String sourceNotes;
	@ApiModelProperty(value = "起始日期（分摊金额要写上）")
	private String startTime;
	@ApiModelProperty(value = "房产Id", example = "1")
	private Integer estateId;
	@ApiModelProperty(value = "费用类型 1--周期性收费 0--一次性收费", example = "1")
	private Integer feeType;
	@ApiModelProperty(value = "费用项目", example = "1")
	private Integer payItemId;
	@ApiModelProperty(value = "调整金额")
	private List<CommunityReceivablesChangesForm> receivablesChangesList =  new ArrayList<>();
	@ApiModelProperty(value = "收费项目")
	private CommunityPayItemsForm payItemsForm;
	@ApiModelProperty(value = "违约金系数")
	private String breachRatio;
	@ApiModelProperty(value = "本次收款金额")
	private String amount;
	@ApiModelProperty(value = "0:未上锁;1:系统锁定;2:人工锁定；", example = "1")
	private Integer lockMark;
	@ApiModelProperty(value = "应收编号")
	private String receivablesNO;
	@ApiModelProperty(value = "管理费取消优惠补收差额详情")
	private String benefitValue;
	private CommunityEstateForm estateForm;
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		if (this == o)
			return 0;
		if (o == null)
			return -1;
		CommunityReceivablesForm other = (CommunityReceivablesForm) o;
		if(null == other.getReceivableDate())
			return -1;
		int result = other.receivableDate.compareTo(this.receivableDate);
		if(result == 0) {
			return other.communityReceivablesId.compareTo(this.communityReceivablesId);
		}
		return result;
	}

	@Override
	public int hashCode() {
		return Objects.hash(chargeCategory, communityReceivablesId, estateId, payItemsName, receivableDate,
				subEstateId);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CommunityReceivablesForm other = (CommunityReceivablesForm) obj;
		return Objects.equals(chargeCategory, other.chargeCategory)
				&& Objects.equals(communityReceivablesId, other.communityReceivablesId)
				&& Objects.equals(estateId, other.estateId) && Objects.equals(payItemsName, other.payItemsName)
				&& Objects.equals(receivableDate, other.receivableDate)
				&& Objects.equals(subEstateId, other.subEstateId);
	}
	
	@Override
	public String toString() {
		return "CommunityReceivablesForm [communityReceivablesId=" + communityReceivablesId + ", chargeCategory="
				+ chargeCategory + ", chargeSource=" + chargeSource + ", comment=" + comment + ", endTime=" + endTime
				+ ", payItemsName=" + payItemsName + ", paymentPeriod=" + paymentPeriod + ", receivableAmount="
				+ receivableAmount + ", receivableDate=" + receivableDate + ", quantity=" + quantity + ", subEstateId="
				+ subEstateId + ", receivedAmount=" + receivedAmount + ", notReceivedAmount=" + notReceivedAmount
				+ ", sourceNotes=" + sourceNotes + ", startTime=" + startTime + ", estateId=" + estateId + ", feeType="
				+ feeType + ", payItemId=" + payItemId + ", receivablesChangesList=" + receivablesChangesList
				+ ", payItemsForm=" + payItemsForm + ", breachRatio=" + breachRatio + ", amount=" + amount
				+ ", lockMark=" + lockMark + ", receivablesNO=" + receivablesNO + ", estateForm=" + estateForm + "]";
	}
	
	
}
