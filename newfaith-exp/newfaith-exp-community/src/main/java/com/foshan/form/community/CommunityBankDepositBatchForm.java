package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 银行划账批次(CommunityBankDepositBatchForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityBankDepositBatchForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer communityBankDepositBatchId;
  @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
    private Integer state;
    @ApiModelProperty(value = "0：工商银行；1：农业银行；2：农村信用社；3：建设银行；4：全国平台；",example="1")
    private Integer templateType;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "划入日期")
    private String depositDate;
    @ApiModelProperty(value = "截止日期")
    private String endTime;
    @ApiModelProperty(value = "收费项目(多个用逗号隔开 例如：别墅管理费,车库公共电费,车位管理费,车位管理费违约金)")
    private String payItems;
    @ApiModelProperty(value = "起始日期")
    private String startTime;
	@ApiModelProperty(value = "业主银行（筛选条件）")
	private String ownerBank;
	@ApiModelProperty(value = "入账银行（引入到应收款中的经办人）")
	private String creditedBank;
    @ApiModelProperty(value = "批次状态 0 未记账，1已记账",example="1")
    private Integer batchState;
    @ApiModelProperty(value = "收费项目")
    private List<CommunityPayItemsForm> payItemsList = new ArrayList<CommunityPayItemsForm>();
  
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
