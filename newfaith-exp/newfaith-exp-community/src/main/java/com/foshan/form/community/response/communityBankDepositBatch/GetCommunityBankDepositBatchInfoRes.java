package com.foshan.form.community.response.communityBankDepositBatch;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;
import com.foshan.form.community.CommunityBankDepositBatchForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="银行划账批次(GetCommunityBankDepositBatchInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityBankDepositBatchInfoRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "银行划账批次对象")
	private CommunityBankDepositBatchForm communityBankDepositBatchForm ; 

}
