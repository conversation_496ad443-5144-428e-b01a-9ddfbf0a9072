package com.foshan.form.community.response.communityReceivablesChanges;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收款变更（减加）(CommunityReceivablesChangesRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityReceivablesChangesRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 6487920471400015395L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityReceivablesChangesId;
  @ApiModelProperty(value = "批准人")
    private String approver;
    @ApiModelProperty(value = "减加金额")
    private String changeAmount;
    @ApiModelProperty(value = "减加日期")
    private String changeDate;
    @ApiModelProperty(value = "减加类型 0:减免；1：添加；",example="1")
    private Integer changeType;
    @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "旧表数据")
    private String oldData;
    @ApiModelProperty(value = "表ID")
    private String oldId;
    @ApiModelProperty(value = "收费项目")
    private String payItemsName;
    @ApiModelProperty(value = "应收金额")
    private String receivableAmount;
    @ApiModelProperty(value = "应收日期")
    private String receivableDate;
    @ApiModelProperty(value = "已收金额")
    private String receivedAmount;
    @ApiModelProperty(value = "减加人（减加人）")
    private String recorder;
    @ApiModelProperty(value = "",example="1")
    private Integer receivablesId;
  
}
