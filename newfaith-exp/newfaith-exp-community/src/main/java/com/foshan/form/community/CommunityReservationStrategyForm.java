package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="预策略对象(CommunityReservationStrategyForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityReservationStrategyForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -991277250952061690L;
	@ApiModelProperty(value = "预策略ID", example = "1")
	private Integer strategyId;
	@ApiModelProperty(value = "策略名称")
	private String strategyName;
	@ApiModelProperty(value = "1法定节假日 2工作日 2=0不限", example = "1")
	protected Integer periodType;	

	
	public CommunityReservationStrategyForm(Integer strategyId,String strategyName,Integer periodType) {
		this.strategyId = strategyId;
		this.strategyName = strategyName;
		this.periodType = periodType;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
