package com.foshan.form.community.response.communityReceivables;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="电视端生成催缴二维码(CreatePaymentQrCodeRes)")
@JsonInclude(Include.NON_NULL)
public class CreatePaymentQrCodeRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2206169590051703696L;
	@ApiModelProperty(value = "png图片url")
    private String imgUrl;

}
