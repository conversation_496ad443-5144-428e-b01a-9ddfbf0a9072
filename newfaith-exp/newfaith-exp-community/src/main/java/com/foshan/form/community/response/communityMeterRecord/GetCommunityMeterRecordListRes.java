package com.foshan.form.community.response.communityMeterRecord;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityMeterRecordForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="抄表(GetCommunityMeterRecordListReq)")
public class GetCommunityMeterRecordListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -9129007366059807180L;
	@ApiModelProperty(value = "抄表列表")
	private List<CommunityMeterRecordForm> communityMeterRecordList = new ArrayList<CommunityMeterRecordForm>(); 

}
