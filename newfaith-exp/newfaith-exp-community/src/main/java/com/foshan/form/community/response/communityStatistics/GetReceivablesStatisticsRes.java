package com.foshan.form.community.response.communityStatistics;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.ReceivablesStatisticsForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取应收统计返回对象(GetReceivablesStatisticsRes)")
@JsonInclude(Include.NON_NULL)
public class GetReceivablesStatisticsRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1072532509474492659L;
    @ApiModelProperty(value = "总已收金额")
    private String totalReceivedAmount;
    @ApiModelProperty(value = "总应收金额")
    private String totalReceivableAmount;
	@ApiModelProperty(value = "应收统计对象")
	List<ReceivablesStatisticsForm> receivablesStatisticsFormList = new ArrayList<ReceivablesStatisticsForm>();

}
