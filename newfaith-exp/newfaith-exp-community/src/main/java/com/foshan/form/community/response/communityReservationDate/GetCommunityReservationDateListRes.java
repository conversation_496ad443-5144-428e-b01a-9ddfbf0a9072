package com.foshan.form.community.response.communityReservationDate;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityReservationDateForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取活动预约日期返回列表对象(GetCommunityReservationDateListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityReservationDateListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 822088332783081250L;
	@ApiModelProperty(value = "活动预约日期列表")
	private List<CommunityReservationDateForm> reservationDateList = new ArrayList<CommunityReservationDateForm>(); 


}
