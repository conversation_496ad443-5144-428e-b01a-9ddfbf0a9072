package com.foshan.form.community.response.communityEstate;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.ProprietorInfoForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取业主信息详情返回对象(GetProprietorInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetProprietorInfoRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3226175121203219736L;
	@ApiModelProperty(value = "业主信息")
	private ProprietorInfoForm proprietorInfo;
}
