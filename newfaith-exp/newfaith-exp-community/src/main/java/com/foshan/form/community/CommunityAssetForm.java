package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.AssetForm;
import com.foshan.form.ColumnForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区媒资对象(CommunityAssetForm)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityAssetForm extends AssetForm {
    private static final long serialVersionUID = 5988573270513557990L;
    /**
     *
     */
    @ApiModelProperty(value = "审核状态  0:初始化；1:初审通过；2：初审不通过；3：终审通过；4：终审不通过",example="1")
    private Integer auditState;
    @ApiModelProperty(value = "内容类别 0:社区微视频；1：社区互助；2:闲置转让；3:小区活动；4：公告类",example="1")
    private Integer contentType;
    @ApiModelProperty(value = "创建用户Id")
    private Integer memberId;
    @ApiModelProperty(value = "创建管理员Id")
    private Integer userId;
    @ApiModelProperty(value = "栏目")
    private List<ColumnForm> columnList = new ArrayList<ColumnForm>();
    @ApiModelProperty(value = "是否置顶，是：0，否：1")
    private Integer isTop;
    @ApiModelProperty(value = "发布时间",example="2019-01-01 00:00:00")
    private String createTime;
	@ApiModelProperty(value = "资源排序值")
	private Integer orderNumber;
	@ApiModelProperty(value = "上架时间")
	private String upshelfTime;
	@ApiModelProperty(value = "上架ID",example="1")
	private Integer upshelfColumnId;
}
