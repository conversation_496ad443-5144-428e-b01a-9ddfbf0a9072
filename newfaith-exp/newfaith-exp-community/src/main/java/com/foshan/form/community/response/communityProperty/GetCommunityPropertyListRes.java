package com.foshan.form.community.response.communityProperty;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityPropertyForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获支资产返回列表对象(GetCommunityPropertyListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityPropertyListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4169411566375159878L;
	@ApiModelProperty(value = "资产对象列表")
	private List<CommunityPropertyForm> propertyFormList = new ArrayList<CommunityPropertyForm>(); 

}
