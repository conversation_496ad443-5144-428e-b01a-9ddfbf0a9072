package com.foshan.form.community.response.communityReceivables;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityReceivablesForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收款(GetCommunityReceivablesInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityReceivablesInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -4286241163147768104L;
	@ApiModelProperty(value = "应收款对象")
	private CommunityReceivablesForm communityReceivablesForm ; 

}
