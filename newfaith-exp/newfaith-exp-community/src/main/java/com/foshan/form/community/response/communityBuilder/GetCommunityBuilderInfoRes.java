package com.foshan.form.community.response.communityBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityBuilderForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="开发商(GetCommunityBuilderInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityBuilderInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 3547673000173245576L;
	@ApiModelProperty(value = "开发商对象")
	private CommunityBuilderForm communityBuilderForm ; 

}
