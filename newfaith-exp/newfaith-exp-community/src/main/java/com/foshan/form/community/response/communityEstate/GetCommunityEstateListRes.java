package com.foshan.form.community.response.communityEstate;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityEstateForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取单元返回列表对象(GetCommunityEstateListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityEstateListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -2714589693124663347L;
	@ApiModelProperty(value = "单元对象列表")
	private List<CommunityEstateForm> EstateList = new ArrayList<CommunityEstateForm>();
	@ApiModelProperty(value = "总建筑面积")
	private String buildingAreaTotal;
	

	public GetCommunityEstateListRes(String ret, String retInfo) {
		super(ret, retInfo);
	}
}
