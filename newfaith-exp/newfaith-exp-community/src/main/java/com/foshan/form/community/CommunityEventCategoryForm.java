package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="事件类型对象(CommunityEventCategoryForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityEventCategoryForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -3434709387839988754L;
	@ApiModelProperty(value = "事件类型ID",example="1")
	private Integer eventCategoryId;
	@ApiModelProperty(value = "类型名称",example="1")
	private String categoryName;
	@ApiModelProperty(value = "1一级，2-二级",example="1")
	private Integer categoryLevel;
	@ApiModelProperty(value = "是否派单:0否1是",example="1")
	private Integer isDispatching;
	@ApiModelProperty(value = "创建时间",example="1")
	private String createTime;
	@ApiModelProperty(value = "修改时间",example="1")
	private String lastModifyTime;
	@ApiModelProperty(value = "图标（小程序端显示的图标）")
	private String icon;
	@ApiModelProperty(value = "排序",example="1")
	private Integer orders;
	@ApiModelProperty(value = "待处理数",example="1")
	private Integer pendingCount=0;
	@ApiModelProperty(value = "完成数",example="1")
	private Integer completeCount=0;
	@ApiModelProperty(value = "类型，1:装修申请",example="1")
	private Integer categoryType;
	@ApiModelProperty(value = "是发送云之家:0否 1是",example="1")
	private Integer isSend;
	@ApiModelProperty(value = "默认参数（云之家）",example="1")
	private String defaultParameter;
	@ApiModelProperty(value = "父事件类型")
	private CommunityEventCategoryForm parentEventCategoryForm;
	@ApiModelProperty(value = "子事件类型")
	private List<CommunityEventCategoryForm> subEventCategoryList = new ArrayList<CommunityEventCategoryForm>();
	@ApiModelProperty(value = "父事件类型")
	private List<CommunityEventCategoryItemsForm> eventCategoryItemList = new ArrayList<CommunityEventCategoryItemsForm>();
	
	public CommunityEventCategoryForm(Integer eventCategoryId,String categoryName,Integer categoryLevel,
			Integer isDispatching,String createTime,String lastModifyTime,String icon) {
		this.eventCategoryId = eventCategoryId;
		this.categoryName = categoryName;
		this.categoryLevel = categoryLevel;
		this.isDispatching = isDispatching;
		this.createTime = createTime;
		this.lastModifyTime = lastModifyTime;
		this.icon = icon;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
