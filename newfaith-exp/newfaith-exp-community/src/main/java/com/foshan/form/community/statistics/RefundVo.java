package com.foshan.form.community.statistics;

import java.util.Map;
import java.util.TreeMap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "应收报表统计数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RefundVo implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = 5569001727780002582L;
	@ApiModelProperty(value = "楼盘编码")
	private String districtCode;
	@ApiModelProperty(value = "楼盘名称")
	private String districtName;
	@ApiModelProperty(value = "楼阁")
	private String buildingName;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "单元号")
	private String roomCode;
	@ApiModelProperty(value = "房号")
	private String roomNumber;
	@ApiModelProperty(value = "业主姓名")
	private String userName;
	@ApiModelProperty(value = "总退款金额")
	private String totalRefund;
	@ApiModelProperty(value = "总未退款金额")
	private String totalArrears;
	@ApiModelProperty(value = "总金额")
	private String totalReceivable;
	private Map<String, String> feeMap = new TreeMap<>();

	public String getNewDistrictCode() {
		return (districtCode.length() == 1 ? "00" + districtCode
				: districtCode.length() == 2 ? "0" + districtCode : districtCode) + districtName;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
