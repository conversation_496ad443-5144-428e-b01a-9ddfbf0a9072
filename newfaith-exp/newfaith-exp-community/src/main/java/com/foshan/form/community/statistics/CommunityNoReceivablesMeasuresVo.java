package com.foshan.form.community.statistics;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "应收未收措施报表数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityNoReceivablesMeasuresVo implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = 5671927271542195849L;
	@ApiModelProperty(value = "楼盘编号")
	private String districtCode;
	@ApiModelProperty(value = "楼盘名称")
	private String districtName;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "姓名")
	private String userName;
	@ApiModelProperty(value = "欠费时段")
	private String period;
	@ApiModelProperty(value = "总欠费金额")
	private String totalArrears;
	@ApiModelProperty(value = "违约金欠费金额")
	private String totalSubArrears;
	@ApiModelProperty(value = "总欠费费用类别")
	private String totalChargecategory;
	@ApiModelProperty(value = "措施")
	private String noReceivablesMeasures;
	@ApiModelProperty(value = "进展1")
	private String measures1;
	@ApiModelProperty(value = "进展2")
	private String measures2;
	@ApiModelProperty(value = "单元Id")
	private Integer estateId;
	
	
	public static CommunityNoReceivablesMeasuresVo getCommunityNoReceivablesMeasuresVo(Object[] o) {
		CommunityNoReceivablesMeasuresVo vo = null;
		if (null != o) {
			try {
				vo = new CommunityNoReceivablesMeasuresVo();
				vo.setDistrictCode(o[0].toString().length()==1?"0"+o[0].toString():o[0].toString());
				vo.setDistrictName(o[1].toString());
				vo.setUnitCode(o[2].toString());
				vo.setUserName(o[3].toString());
				vo.setPeriod(o[4].toString());
				vo.setTotalArrears(o[5].toString());
				vo.setTotalSubArrears(null!=o[6]?o[6].toString():"");
				vo.setTotalChargecategory(o[7].toString());	
				vo.setNoReceivablesMeasures(o[8].toString());
				vo.setMeasures1(o[9].toString());
				vo.setMeasures2(o[10].toString());
				vo.setEstateId(Integer.parseInt(o[11].toString()));
			} catch (Exception ex) {
				ex.printStackTrace();
				vo = null;
			}
		}
		return vo;
	}
	
	public String getNewDistrictCode() {
		return (districtCode.length() == 1 ? "00" + districtCode
				: districtCode.length() == 2 ? "0" + districtCode : districtCode) + districtName;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
