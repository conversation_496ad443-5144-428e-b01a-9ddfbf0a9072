package com.foshan.form.community.statistics;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Setter
@NoArgsConstructor
@Slf4j
@ApiModel(value = "应收减免透视统计数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityReceivablesChangesVo implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = 4059379452482909472L;
	@ApiModelProperty(value = "楼盘编号")
	private String districtCode;
	@ApiModelProperty(value = "楼盘名称")
	private String districtName;
	@ApiModelProperty(value = "楼阁名称")
	private String buildingName;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "收费项目")
	private String payItemsName;
	@ApiModelProperty(value = "应收金额")
	private String receivableAmount;
	@ApiModelProperty(value = "应收年")
	private String receivableYear;
	@ApiModelProperty(value = "应收月")
	private String receivableMonth;
	@ApiModelProperty(value = "应收日期")
	private String receivableDate;
	@ApiModelProperty(value = "减免日期")
	private String changeDate;
	@ApiModelProperty(value = "减免金额")
	private String changeAmount;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "减加人")
	private String recorder;
	
	public static CommunityReceivablesChangesVo getCommunityReceivablesChangesVo(Object[] o) {
		CommunityReceivablesChangesVo vo = null;
		if (null != o) {
			try {
				vo = new CommunityReceivablesChangesVo();
				vo.setDistrictCode(o[0].toString().length()==1?"0"+o[0].toString():o[0].toString());
				vo.setDistrictName(o[1].toString());
				vo.setBuildingName(o[2].toString());
				vo.setUnitCode(o[3].toString());
				vo.setPayItemsName(o[4].toString());
				vo.setReceivableAmount(null!=o[5]?o[5].toString():"");
				vo.setReceivableYear(o[6].toString());
				vo.setReceivableMonth(o[7].toString());
				vo.setReceivableDate(o[8].toString());
				vo.setChangeDate(null!=o[9]?o[9].toString():"");
				vo.setChangeAmount(null!=o[10]?o[10].toString():"");
				vo.setComment(null!=o[11]?o[11].toString():"");
				vo.setRecorder(null!=o[12]?o[12].toString():"");

				
			}catch (Exception ex) {
				log.error(ex.getMessage() + ":CommunityReceivablesChangesVo(" + o[0].toString() + "" + o[1].toString() + ""
						+ o[2].toString() + ")数据转换失败");
				vo = null;
			}
		}
		return vo;
	}
	
	
	public String getNewDistrictCode() {
		return (districtCode.length() == 1 ? "00" + districtCode
				: districtCode.length() == 2 ? "0" + districtCode : districtCode) + districtName;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
