package com.foshan.form.community.response.communityInstallmentSms;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityInstallmentSmsForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="账单分期详情(GetCommunityInstallmentSmsInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityInstallmentSmsInfoRes extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8490190662944836326L;
	
	private CommunityInstallmentSmsForm installmentSms;

}
