package com.foshan.form.community;


import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应急广播(EmergencyMessageForm)")
@JsonInclude(Include.NON_NULL)
public  class EmergencyMessageForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4297513200016380153L;
	@ApiModelProperty(value = "应急广播ID", example = "1")
	private Integer emergencyMessageId;
	@ApiModelProperty(value = "发布机构名称")
	private String senderName;
	@ApiModelProperty(value = "发布机构编码")
	private String senderCode;
	@ApiModelProperty(value = "消息编码")
	private String msgCode;
	@ApiModelProperty(value = "发送时间")
	private String sendTime;
	@ApiModelProperty(value = "播发开始时间")
	private String startTime;
	@ApiModelProperty(value = "播发结束时间")
	private String endTime;
	@ApiModelProperty(value = "消息类型 1：实际播发2：取消播发3：平台演练播发4：前端演练播发5：终端演练播发", example = "1")
	private Integer msgType;
	@ApiModelProperty(value = "事件类型编码; 10000:突发事件;11A00:水旱灾害;11B00:气象灾害;11C00:地震灾害;11D00:地质灾害")
	private String eventType;
	@ApiModelProperty(value = "事件级别 0：未知级别（Unknown）1：1级（特别重大/红色预警/Red）"
			+ "2：3级（重大/橙色预警/ Orange）3：3级（较大/黄色预警/ Yellow）4：4级（一般/蓝色预警/ Blue）", example = "1")
	private Integer severity;
	@ApiModelProperty(value = "消息标题文本")
	private String msgTitle;
	@ApiModelProperty(value = "消息内容文本")
	private String msgDesc;
	@ApiModelProperty(value = "区域编码")
	private String areaCode;
	@ApiModelProperty(value = "播发状态代码 0：未处理1：等待播发，指未到消息播发时间2：播发中"
			+ "3：播发成功4：播发失败，包括播发全部失败、播发部分失败、未按要求播发等情况5：播发取消", example = "1")
	private Integer stateCode;
	@ApiModelProperty(value = "播发状态描述")
	private String stateDesc;
	@ApiModelProperty(value = "实际覆盖率")
	private String coverageRate;
	@ApiModelProperty(value = "实际调用资源响应统计 格式为（半角字符逗号）:\"\r\n"
			+ "			+ \"实际调用应急广播平台数,实际调用应急广播适配器数,实际调用传输覆盖播出设备数,实际调用终端数")
	private String resBrdStat;
	@ApiModelProperty(value = "反馈时间")
	private String feedbackTime;
	@ApiModelProperty(value = "压缩包文件路径")
	private String tarFilePath;
	@ApiModelProperty(value = "父消息")
	private EmergencyMessageForm parentEmergencyMessage;
	@ApiModelProperty(value = "子消息")
	private List<EmergencyMessageForm> subEmergencyMessageList = new ArrayList<>();
	
	

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
