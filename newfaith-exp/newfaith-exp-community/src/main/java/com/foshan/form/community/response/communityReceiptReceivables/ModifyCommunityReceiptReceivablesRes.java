package com.foshan.form.community.response.communityReceiptReceivables;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "应收款-收款单据(ModifyCommunityReceiptReceivablesRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyCommunityReceiptReceivablesRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8428763921214053130L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer communityReceiptReceivablesId;
	@ApiModelProperty(value = "本次收款")
	private String currentAmount;
	@ApiModelProperty(value = "已收金额")
	private String receivedAmount;
	@ApiModelProperty(value = "", example = "1")
	private Integer receiptId;
	@ApiModelProperty(value = "", example = "1")
	private Integer receivablesId;

}
