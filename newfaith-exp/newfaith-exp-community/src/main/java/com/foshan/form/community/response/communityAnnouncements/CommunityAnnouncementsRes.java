package com.foshan.form.community.response.communityAnnouncements;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.community.CommunityAnnouncementsForm;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="公告返回对象(CommunityAnnouncementsRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityAnnouncementsRes extends BaseResponse {

    /**
     *
     */
    private static final long serialVersionUID = -5993918518658159090L;
    @ApiModelProperty(value = "公告信息")
    private CommunityAnnouncementsForm communityAnnouncementsForm;

    public CommunityAnnouncementsRes(String ret, String retInfo){
        super(ret, retInfo);
    }
}
