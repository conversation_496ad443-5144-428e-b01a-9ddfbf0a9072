package com.foshan.form.community.response.communityPayItems;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityPayItemsForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="收费项目返回列表(GetCommunityPayItemsListReq)")
public class GetCommunityPayItemsListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -3283838301982288154L;
	@ApiModelProperty(value = "收费项目列表")
	private List<CommunityPayItemsForm> communityPayItemsList = new ArrayList<CommunityPayItemsForm>(); 

}
