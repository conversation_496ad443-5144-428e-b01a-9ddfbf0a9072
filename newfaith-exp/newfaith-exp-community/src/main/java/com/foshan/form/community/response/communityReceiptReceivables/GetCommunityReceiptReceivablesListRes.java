package com.foshan.form.community.response.communityReceiptReceivables;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityReceiptReceivablesForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="应收款-收款单据(GetCommunityReceiptReceivablesListReq)")
public class GetCommunityReceiptReceivablesListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -5596985411325267832L;
	@ApiModelProperty(value = "应收款-收款单据列表")
	private List<CommunityReceiptReceivablesForm> communityReceiptReceivablesList = new ArrayList<CommunityReceiptReceivablesForm>(); 

}
