package com.foshan.form.community;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CommunityInviceInfoForm {
	@ApiModelProperty(value = "税率")
	private String taxRate;
	@ApiModelProperty(value = "税收编码")
	private String taxCode;
	@ApiModelProperty(value = "经营项目")
	private String businessScope;
	@ApiModelProperty(value = "税务项目名称")
	private String taxProductName;
	@ApiModelProperty(value = "开发票单位")
	private String inviceUnit;
	@ApiModelProperty(value = "优惠政策标识 0:使用优惠政策;1:不使用（默认）")
	private String discountIdentify;
	@ApiModelProperty(value = "优惠政策详情 免税,100%先征后退,50%先征后退,不征税,先征后退,即征即退100%,即征即退30%,即征即退50%,"
			+ "即征即退70%,按3%易征收,按5%简易征收,按5%简易征收减按1.5%计征,稀土产品,简易征收,超税负12%即征即退,超税负3%即征即退,超税负6%即征即退,超税负8%即征即退")
	private String discountDetails;
}
