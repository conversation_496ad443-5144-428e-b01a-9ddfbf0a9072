package com.foshan.form.community;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "单元违约金详情(CommunityReceivablesBreachDetailForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityReceivablesBreachDetailForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7735878061052900229L;
	@ApiModelProperty(value = "应收款Id")
	private Integer receivablesId;
	@ApiModelProperty(value = "收费项目Id")
	private Integer payItemId;
	@ApiModelProperty(value = "违约金名称")
	private String breachName;
	@ApiModelProperty(value = "违约开始日期")
	private Date breachBeginDate;
	@ApiModelProperty(value = "违约结束日期")
	private Date breachEndDate;
	@ApiModelProperty(value = "累计总滞纳金")
	private BigDecimal breachAmount;
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public String toString() {
		return "CommunityReceivablesBreachDetailForm [receivablesId=" + receivablesId + ", payItemId=" + payItemId
				+ ", breachName=" + breachName + ", breachBeginDate=" + breachBeginDate + ", breachEndDate="
				+ breachEndDate + ", breachAmount=" + breachAmount + "]";
	}

	
	
}
