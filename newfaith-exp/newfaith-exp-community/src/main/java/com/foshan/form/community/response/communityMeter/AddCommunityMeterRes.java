package com.foshan.form.community.response.communityMeter;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="总表(CommunityMeterRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityMeterRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -7330662382124977650L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityMeterId;
    @ApiModelProperty(value = "分摊类别：1按面积分摊，2自定义公式、3按个数",example="1")
    private Integer allocationMethod;
    @ApiModelProperty(value = "备注",example="1")
    private String comment;
    @ApiModelProperty(value = "表坏日期",example="1")
    private String expirationDates;
    @ApiModelProperty(value = "底数",example="1")
    private String initialData;
    @ApiModelProperty(value = "安装地点",example="1")
    private String installationsite;
    @ApiModelProperty(value = "是否共用表",example="1")
    private Boolean isCommon;
    @ApiModelProperty(value = "层次（树形结构层次）",example="1")
    private Integer level;
    @ApiModelProperty(value = "表编号",example="1")
    private String meterCode;
    @ApiModelProperty(value = "表名称",example="1")
    private String meterName;
    @ApiModelProperty(value = "旧表数据",example="1")
    private String oldData;
    @ApiModelProperty(value = "旧表ID",example="1")
    private String oldId;
    @ApiModelProperty(value = "",example="1")
    private Integer meterAttributesId;
    @ApiModelProperty(value = "",example="1")
    private Integer formulaId;
    @ApiModelProperty(value = "",example="1")
    private Integer parentMeterId;

}
