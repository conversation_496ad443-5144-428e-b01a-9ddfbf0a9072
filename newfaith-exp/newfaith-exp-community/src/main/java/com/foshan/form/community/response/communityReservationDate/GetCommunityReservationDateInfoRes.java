package com.foshan.form.community.response.communityReservationDate;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityReservationDateForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取活动预约日期详情对象(GetCommunityReservationDateInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityReservationDateInfoRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5505884410738076096L;
	@ApiModelProperty(value = "活动预约日期对象列表")
	private CommunityReservationDateForm communityReservationDateForm ; 

}
