package com.foshan.form.community;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AssetForm;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="事件(CommunityEventNotificationForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityEventNotificationForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -9063689563995030478L;
	@ApiModelProperty(value = "通知ID",example="1")
	private Integer notificationId;
	@ApiModelProperty(value = "事件ID",example="1")
	private Integer eventsId;
	@ApiModelProperty(value = "分类：0：暂停施工通知书、1：安全告知函、2：复工通知书、3：违规整改告知函",example="1")
	private Integer category;
    @ApiModelProperty(value = "数据状态",example="1")
    private Integer state;
    @ApiModelProperty(value = "修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    @ApiModelProperty(value = "文件")
    private AssetForm fileForm;
	@ApiModelProperty(value = "提交人")
	private String submitter;
	@ApiModelProperty(value = "标题")
	private String title;
    @ApiModelProperty(value = "订单编号")
    private String orderCode;
    @ApiModelProperty(value = "单元编号")
    private String unitcode;
    @ApiModelProperty(value = "装修项目")
    private String centent;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	

}
