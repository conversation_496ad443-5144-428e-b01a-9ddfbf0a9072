package com.foshan.form.community.statistics;

import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import com.foshan.util.DateUtil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Setter
@NoArgsConstructor
@Slf4j
@ApiModel(value = "应收报表统计数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MemberPropertyVo implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = -316706210342149258L;
	@ApiModelProperty(value = "楼盘编码")
	private String districtCode;
	@ApiModelProperty(value = "楼盘名称")
	private String districtName;
	@ApiModelProperty(value = "楼阁名称")
	private String buildingName;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "房间号")
	private String roomNumber;
	@ApiModelProperty(value = "建筑面积")
	private String buildingArea;
	@ApiModelProperty(value = "使用面积")
	private String usableArea;
	@ApiModelProperty(value = "收费面积")
	private String chargingArea;
	@ApiModelProperty(value = "附加面积")
	private String additionalArea;
	@ApiModelProperty(value = "单元类型")
	private String estateType;
	@ApiModelProperty(value = "产权车位面积")
	private String propertyParkingArea;
	@ApiModelProperty(value = "产权车位个数")
	private String propertyParkingNum;
	@ApiModelProperty(value = "人防车位面积")
	private String defenceParkingArea;
	@ApiModelProperty(value = "人防车位个数")
	private String defenceParkingNum;
	@ApiModelProperty(value = "单元状态")
	private String estateState;
	@ApiModelProperty(value = "业主姓名")
	private String userName;
	@ApiModelProperty(value = "入住日期")
	private String recordDate;
	@ApiModelProperty(value = "计费日期")
	private String billingDate;
	@ApiModelProperty(value = "离退日期")
	private String terminationDate;
	@ApiModelProperty(value = "业主类型")
	private String memberType;
	@ApiModelProperty(value = "生日")
	private String birthDay;
	@ApiModelProperty(value = "性别")
	private String sex;
	@ApiModelProperty(value = "联系电话")
	private String homePhone;
	@ApiModelProperty(value = "证件类型")
	private String idType;
	@ApiModelProperty(value = "证件号码")
	private String idcard;
	@ApiModelProperty(value = "紧急联系人")
	private String contactPerson;
	@ApiModelProperty(value = "紧急联系人联系方式")
	private String emergencyContact;
	@ApiModelProperty(value = "购方名称")
	private String buyersName;
	@ApiModelProperty(value = "购方地址")
	private String buyersAddress;
	@ApiModelProperty(value = "购方银行账号")
	private String buyersBankAccount;
	@ApiModelProperty(value = "购方邮箱")
	private String buyerEmail;
	@ApiModelProperty(value = "划账银行名称")
	private String bankName;
	@ApiModelProperty(value = "划账银行账号")
	private String bankAccount;
	@ApiModelProperty(value = "水表底数")
	private String waterMeterBase;
	@ApiModelProperty(value = "电表底数")
	private String electricMeterBase;
	@ApiModelProperty(value = "账号名")
	private String accountName;
	@ApiModelProperty(value = "联系地址")
	private String homeAddress;
	@ApiModelProperty(value = "单元备注")
	private String estateMemo;
	@ApiModelProperty(value = "业主备注")
	private String memberMemo;
	@ApiModelProperty(value = "收楼日期")
	private String acceptanceDate;
	
	public static MemberPropertyVo getMemberPropertyVo(Object[] o) {
		MemberPropertyVo vo = null;
		if (null != o) {
			try {
				vo = new MemberPropertyVo();
				vo.setDistrictName(o[0].toString());
				vo.setBuildingName(o[1].toString());
				vo.setUnitCode(o[2].toString());
				vo.setRoomNumber(o[3].toString());
				vo.setBuildingArea(null!=o[4]?o[4].toString():"");
				vo.setUsableArea(null!=o[5]?o[5].toString():"");
				vo.setChargingArea(null!=o[6]?o[6].toString():"");
				vo.setAdditionalArea(null!=o[7]?o[7].toString():"");
				vo.setEstateType(null!=o[8]?o[8].toString():"");
				vo.setPropertyParkingArea(null!=o[9]?o[9].toString():"");
				vo.setPropertyParkingNum(null!=o[10]?o[10].toString():"");
				vo.setDefenceParkingArea(null!=o[11]?o[11].toString():"");
				vo.setDefenceParkingNum(null!=o[12]?o[12].toString():"");
				vo.setEstateState(null!=o[13]?o[13].toString():"");
				vo.setUserName(null!=o[14] ? o[14].toString():"");
				vo.setAcceptanceDate(null != o[15] ? DateUtil.formatLongFormat((Timestamp)o[15] ) : "");
				vo.setRecordDate(null!=o[16]?o[16].toString():"");
				vo.setBillingDate(null!=o[17]?o[17].toString():"");
				vo.setTerminationDate(null!=o[18]?o[18].toString():"");
				vo.setMemberType(null!=o[19]?o[19].toString():"");
				vo.setBirthDay(null!=o[20]?o[20].toString():"");
				vo.setSex(null!=o[21]?o[21].toString():"");
				vo.setHomePhone(null!=o[22]?o[22].toString():"");
				vo.setIdType(null!=o[23]?o[23].toString():"");
				vo.setIdcard(null!=o[24]?o[24].toString():"");
				vo.setContactPerson(null!=o[25]?o[25].toString():"");
				vo.setEmergencyContact(null!=o[26]?o[26].toString():"");
				vo.setBuyersName(null!=o[27]?o[27].toString():"");
				vo.setBuyersAddress(null!=o[28]?o[28].toString():"");
				vo.setBuyersBankAccount(null!=o[29]?o[29].toString():"");
				vo.setBuyerEmail(null!=o[30]?o[30].toString():"");
				vo.setBankName(null!=o[31]?o[31].toString():"");
				vo.setBankAccount(null!=o[32]?o[32].toString():"");
				vo.setAccountName(null!=o[33]?o[33].toString():"");
				vo.setDistrictCode(o[34].toString().length()==1?"0"+o[34].toString():o[34].toString());
				vo.setHomeAddress(null!=o[35]?o[35].toString():"");
				vo.setEstateMemo(null!=o[36]?o[36].toString():"");
				vo.setMemberMemo(null!=o[37]?o[37].toString():"");
				vo.setWaterMeterBase(null!=o[38]?o[38].toString():"");
				vo.setElectricMeterBase(null!=o[39]?o[39].toString():"");
				
 
			} catch (Exception ex) {
				log.error(ex.getMessage() + ":MemberPropertyVo(" + o[0].toString() + "" + o[1].toString() + ""
						+ o[2].toString() + ")数据转换失败");
				vo = null;
			}
		}
		
		return vo;
	}
	
	public String getNewDistrictCode() {
		return (districtCode.length() == 1 ? "00" + districtCode
				: districtCode.length() == 2 ? "0" + districtCode : districtCode) + districtName;
	}
	
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
