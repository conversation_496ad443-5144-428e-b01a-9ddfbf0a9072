package com.foshan.form.community.statistics;

import java.util.Map;
import java.util.TreeMap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@Getter
@Setter
@NoArgsConstructor
@Slf4j
@ApiModel(value = "每日收款数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class EverydayReceiptVo implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = -8180819085500872298L;
	@ApiModelProperty(value = "收款方式")
	private String paymentMethod;
	@ApiModelProperty(value = "楼盘编码")
	private String districtCode;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "单据编号")
	private String receiptCode;
	@ApiModelProperty(value = "楼栋排序")
	private Integer buildingOrder;
	private Map<String,String> feeMap = new TreeMap<>();
	
	public static EverydayReceiptVo getEverydayReceiptVo1(String[] feeTitle,Object[] o) {
		EverydayReceiptVo vo = null;
		if (null != o) {
			try {
				vo = new EverydayReceiptVo();
				vo.setPaymentMethod(o[0].toString());
				vo.setDistrictCode(o[1].toString());
				vo.setUnitCode(o[2].toString());
				vo.setReceiptCode(o[3].toString());
				Map<String,String> temp = new TreeMap<>();
				for(int i=3;i<=o.length;i++) {
					temp.put(feeTitle[i-3], null!=o[i].toString()?o[i].toString():"");
				}
				vo.setFeeMap(temp);
			} catch (Exception ex) {
				log.error(ex.getMessage() + ":EverydayReceiptVo(" + o[2].toString() + ":::" + o[3].toString()+ ")数据转换失败");
				vo = null;
			}
		}
		return vo;
	}
	
	public String getNewUnitCode() {
		return (buildingOrder<10?"00"+buildingOrder:buildingOrder>99?buildingOrder+"":"0"+buildingOrder)+unitCode;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
