package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "消息信息对象(MsgBasicInfoForm)")
@JsonInclude(Include.NON_NULL)
public class MsgBasicInfoForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3334048478544710821L;
	@ApiModelProperty(value = "发布机构名称")
	private String senderName;
	@ApiModelProperty(value = "消息的类型，1：实际播发;2：取消播发;3：平台演练播发;4：前端演练播发;5：终端演练播发;",example="1")
	private Integer msgType;
	@ApiModelProperty(value = "发布机构编码")
	private String senderCode;
	@ApiModelProperty(value = "发布时间")
	private String sendTime;
	@ApiModelProperty(value = "事件类型编码; 10000:突发事件;11A00:水旱灾害;11B00:气象灾害;11C00:地震灾害;11D00:地质灾害")
	private String eventType;
	@ApiModelProperty(value = "事件级别; 0：未知级别（Unknown）;1：1级（特别重大/红色预警/Red）;2：2级（重大/橙色预警/ Orange）;"
			+ "3：3级（较大/黄色预警/ Yellow）;4：4级（一般/蓝色预警/ Blue）" ,example="1")
	private Integer severity;
	@ApiModelProperty(value = "播发开始时间")
	private String startTime;
	@ApiModelProperty(value = "播发结束时间")
	private String endTime;
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
