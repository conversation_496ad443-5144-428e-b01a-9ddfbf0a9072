package com.foshan.form.community.response.communityBuilding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityBuildingForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="楼栋(GetCommunityBuildingInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityBuildingInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -1042499289594761096L;
	@ApiModelProperty(value = "楼栋对象")
	private CommunityBuildingForm communityBuildingForm ; 

}
