package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="部门对象(CommunityDepartmentForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityDepartmentForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -3434709387839988754L;
	@ApiModelProperty(value = "部门ID",example="1")
	private Integer departmentId;
	
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
