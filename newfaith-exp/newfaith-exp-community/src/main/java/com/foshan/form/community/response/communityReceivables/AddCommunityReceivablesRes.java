package com.foshan.form.community.response.communityReceivables;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityReceivablesForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "应收款(CommunityReceivablesRes)")
@JsonInclude(Include.NON_NULL)
public class AddCommunityReceivablesRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7917340087190235461L;
	@ApiModelProperty(value = "应收款列表")
	private List<CommunityReceivablesForm> receivablesFormList = new ArrayList<>();;
	

}
