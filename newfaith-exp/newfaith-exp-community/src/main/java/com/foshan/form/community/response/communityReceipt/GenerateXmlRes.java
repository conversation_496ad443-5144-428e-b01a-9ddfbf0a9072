package com.foshan.form.community.response.communityReceipt;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="生成XML返回对象(GenerateXmlRes)")
@JsonInclude(Include.NON_NULL)
public  class GenerateXmlRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 2556565331531934625L;
    @ApiModelProperty(value = "zip下载地址")
    private String zipUrl;
}
