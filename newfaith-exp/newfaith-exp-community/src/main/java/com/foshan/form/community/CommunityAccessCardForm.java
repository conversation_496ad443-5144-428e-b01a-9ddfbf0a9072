package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="门禁卡对象(CommunityAccessCardForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityAccessCardForm extends  CommunityEntranceFacilitiesForm {
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 4207119630281020273L;
    @ApiModelProperty(value = "门禁卡类型(业主、临时施工、一次性、访客、物品放行)", example = "1")
    private Integer cardType;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;


	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
