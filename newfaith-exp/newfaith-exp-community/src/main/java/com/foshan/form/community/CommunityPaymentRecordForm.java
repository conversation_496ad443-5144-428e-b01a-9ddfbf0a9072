package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = " 在线支付发起的付款记录(CommunityPaymentRecordForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityPaymentRecordForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID", example = "1")
	private Integer communityPaymentRecordId;
	@ApiModelProperty(value = "金额")
	private String amount;
	@ApiModelProperty(value = "交易银行账号")
	private String bankAccount;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "支付人员终端ID，付款人员微信的openId")
	private String payerIdentify;
	@ApiModelProperty(value = "姓名")
	private String payerName;
	@ApiModelProperty(value = "付款日期")
	private String paymentDate;
	@ApiModelProperty(value = "付款方式：1、微信：WX_PUBLIC--公众号支付;WX_NAVITE_QR--微信二维码支付;WX_MOBILE_WEBSITE--手机网站支付;WX_MINIPROGRAM--微信小程序;WX_PAYCODE--微信付款码支付;AL_NAVITE_QR--支付宝二维码支付;AL_PAYCODE--支付宝付款码支付.2、线下：OFFLINE_POS--线下pos机;OFFLINE_CASH--线下现金;OFFLINE_TRANSFER--线下转账")
	private String paymentMethod;
	@ApiModelProperty(value = "付款流水编号")
	private String paymentRecordCode;
	@ApiModelProperty(value = "收款类型 （0：应收款； 2：预收；）", example = "1")
	private Integer paymentType;
	@ApiModelProperty(value = "0--未发起（初始化），1--已经发起，2--支付成功，3--支付失败")
	private Integer status;
	@ApiModelProperty(value = "第三方在线支付系统交易流水号")
	private String outTradeNo;
	@ApiModelProperty(value = "房产的单元号")
	private String unitCode;
	@ApiModelProperty(value = "", example = "1")
	private Integer estateId;
	@ApiModelProperty(value = "", example = "1")
	private Integer receiptId;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
