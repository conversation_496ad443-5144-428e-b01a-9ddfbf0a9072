package com.foshan.form.community.response.communityDistrict;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityDistrictForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="小区(GetCommunityDistrictListReq)")
public class GetCommunityDistrictListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 4192483559679794251L;
	@ApiModelProperty(value = "小区列表")
	private List<CommunityDistrictForm> communityDistrictList = new ArrayList<CommunityDistrictForm>(); 

}
