package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="根据输入字符查询相近的前10个单编号列表元素(CommunityEstateUnitCodeForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityEstateUnitCodeForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6990102938953026886L;
	@ApiModelProperty(value = "单元Id")
	private Integer estateId;
    @ApiModelProperty(value = "单元类型  如：住宅 、商铺 、车位 、公共区域")
    private String estateType;
    @ApiModelProperty(value = "楼栋类型")
    private Integer buildingType;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}


}
