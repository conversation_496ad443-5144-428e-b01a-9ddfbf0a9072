package com.foshan.form.community.response.communityMeterAllocation;


import java.util.ArrayList;
import java.util.List;

import com.foshan.entity.community.vo.ExportAllocationVo;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="总表分摊结果导出(ExportAllocationListRes)")
public class ExportAllocationListRes  extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2944057498611515868L;
	private String[] title;
	private List<ExportAllocationVo> expportAllocationList = new ArrayList<>();
}
