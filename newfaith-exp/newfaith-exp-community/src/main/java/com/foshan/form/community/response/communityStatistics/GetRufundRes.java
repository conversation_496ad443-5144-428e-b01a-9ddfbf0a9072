package com.foshan.form.community.response.communityStatistics;

import java.util.LinkedList;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取退款报表列表")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetRufundRes<T extends IForm> extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = 6838646375453879467L;
	private String fileName;
	private String[] titles;
	private LinkedList<T> dataList = new LinkedList<>();
}
