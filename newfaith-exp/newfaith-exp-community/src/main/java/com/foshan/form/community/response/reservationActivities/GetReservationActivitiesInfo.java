package com.foshan.form.community.response.reservationActivities;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityReservationActivitiesForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获活动详情对象(GetReservationActivitiesInfo)")
@JsonInclude(Include.NON_NULL)
public class GetReservationActivitiesInfo extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1372762541789093461L;
	@ApiModelProperty(value = "活动对象列表")
	private CommunityReservationActivitiesForm communityReservationActivitiesForm ; 


}
