package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="楼栋(CommunityBuilding)")
@JsonInclude(Include.NON_NULL)
public class CommunityBuilding implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8614952558320668821L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityBuildingId;
    @ApiModelProperty(value = "楼栋地址")
    private String address;
    @ApiModelProperty(value = "楼栋编号")
    private String buildingCode;
    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;
    @ApiModelProperty(value = "建筑许可证号")
    private String buildingPermitNum;
    @ApiModelProperty(value = "楼栋类别(功能) 0:住宅、1:别墅、2:商铺、3:停车场、4:公共区域",example="1")
    private Integer buildingType;
    @ApiModelProperty(value = "封顶日期")
    private String cappingDate;
    @ApiModelProperty(value = "地性质分类 0住宅用地 1商业用地 2工业用地 3综合用地 4 其他用地",example="1")
    private Integer classification;
    @ApiModelProperty(value = "竣工日期")
    private String completionDate;
    @ApiModelProperty(value = "结构类型")
    private String construction;
    @ApiModelProperty(value = "完损等级")
    private String damagedLevel;
    @ApiModelProperty(value = "装修标准")
    private String decoration;
    @ApiModelProperty(value = "层数",example="1")
    private Integer layers;
    @ApiModelProperty(value = "预售许可证号",example="1")
    private String permitLicenceNum;
	@ApiModelProperty(value = "备注")
	private String comment;
    @ApiModelProperty(value = "建筑面积")
    private String buildingArea;
    @ApiModelProperty(value = "实用面积")
    private String usableArea;
	@ApiModelProperty(value = "楼栋排序号",example="1")
	private Integer buildingOrder;
    @ApiModelProperty(value = "小区对象")
    private CommunityDistrict districtForm;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
