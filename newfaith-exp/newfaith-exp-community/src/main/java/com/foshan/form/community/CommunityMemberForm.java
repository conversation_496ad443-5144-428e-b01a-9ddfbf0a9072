package com.foshan.form.community;


import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区会员对象(CommunityMemberForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityMemberForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5758307641866883796L;
	@ApiModelProperty(value = "会员ID", example = "1")
	private Integer memberId;
	@ApiModelProperty(value = "昵称")
	private String nickName;
	@ApiModelProperty(value = "email")
	private String email;
	@ApiModelProperty(value = "家庭电话")
	private String homePhone;
	@ApiModelProperty(value = "办公电话")
	private String officePhone;
	@ApiModelProperty(value = "手机号码")
	private String phone;
	@ApiModelProperty(value = "性别：0-女，1-男", example = "1")
	private Integer sex;
	@ApiModelProperty(value = "CA卡号")
	private String smartcardId;
	@ApiModelProperty(value = "注册名")
	private String registName;
	@ApiModelProperty(value = "身份证")
	private String idCard;
	@ApiModelProperty(value = "是否默认电视客户端（最后一次登陆的账号）：null/0-否，1-是", example = "1")
	private Integer isDefaultTvClient;
	@ApiModelProperty(value = "角色状态 0--无效  1--有效", example = "1")
	private Integer state;
	@ApiModelProperty(value = "头像")
	private String headImage;
	@ApiModelProperty(value = "区域码")
	private String regionCode;
	@ApiModelProperty(value = "职位")
	private String position;
	@ApiModelProperty(value = "注册时间")
	private String registTime;
	@ApiModelProperty(value = "在职状态 0：在职； 1：退休；2：离职；", example = "1")
	private Integer workingState;
	@ApiModelProperty(value = "真实名字")
	private String userName;
	@ApiModelProperty(value = "资产列表")
	private List<CommunityMemberPropertyForm> memberPropertyFormList = new ArrayList<CommunityMemberPropertyForm>();
	
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "0户主1住户成员", example = "1")
	private Integer memberType;
	@ApiModelProperty(value = "紧急联系人姓名")
	private String contactPerson;
	@ApiModelProperty(value = "紧急联系方式")
	private String emergencyContact;
	@ApiModelProperty(value = "与户主关系")
	private String relation;
	@ApiModelProperty(value = "购方名称")
	private String buyersName;
	@ApiModelProperty(value = "购方地址电话")
	private String buyersAddress;
	@ApiModelProperty(value = "企业类型")
	private String businessType;
	@ApiModelProperty(value = "开票类型")
	private String invoiceType;
	@ApiModelProperty(value = "购方税号")
	private String paytaxNo;
	@ApiModelProperty(value = "购方银行账号")
	private String buyersBankAccount;
	@ApiModelProperty(value = "电视号")
	private String tvNo;
	@ApiModelProperty(value = "MAC地址")
	private String mac;
	@ApiModelProperty(value = "IP地址")
	private String ip;
	@ApiModelProperty(value = "微信帐号")
	private String weixinOpenId;
	@ApiModelProperty(value = "微信头像")
	private String weixinAvatar;
	@ApiModelProperty(value = "家庭住址")
	private String homeAddress;
	@ApiModelProperty(value = "公司")
	private String company;
	@ApiModelProperty(value = "邮政编码")
	private String postalCode;
	@ApiModelProperty(value = "民族")
	private  String nation;
	@ApiModelProperty(value = "户口所在地派出所")
	private String policeStation;
	@ApiModelProperty(value = "籍贯")
	private String nativePlace;
	@ApiModelProperty(value = "公司地址")
	private String officeAddress;
	@ApiModelProperty(value = "证件类型")
	private String idType;
	@ApiModelProperty(value = "兑奖区域")
	private String exchangeRegion;
	@ApiModelProperty(value = "兑奖营业厅")
	private String exchangeHall;
	@ApiModelProperty(value = "预约时间")
	private Timestamp orderTime;
	@ApiModelProperty(value = "小程序帐号")
	private String miniProgramOpenId;
	@ApiModelProperty(value = "是否智能卡主（首次绑定），1-是，0-否", example = "1")
	private Integer isSmartcardMaster;
	@ApiModelProperty(value = "用户编码")
	protected String userCode;
	@ApiModelProperty(value = "登录名")
	protected String loginName;
	@ApiModelProperty(value = "生日")
	protected String birthday;
	@ApiModelProperty(value = "电话验证状态 0--未验证 1--验证", example = "1")
	protected Integer phoneVerifyState;
	@ApiModelProperty(value = "邮箱验证状态 0--未验证 1--验证", example = "1")
	protected Integer mailVerifyState;
	@ApiModelProperty(value = "QQ")
	protected String qq;
	@ApiModelProperty(value = "微信昵称")
	protected String weixin;
	@ApiModelProperty(value = "状态 0--无效 1--有效", example = "1")
	protected Integer userState;
	@ApiModelProperty(value = "照片")
	protected String photo;
	@ApiModelProperty(value = "计费结束日期")
	private Date endDate;
	@ApiModelProperty(value = "拓展字段")
	private String expandField;
	@ApiModelProperty(value = "操作日志")
	private String  operationLog;
	@ApiModelProperty(value = "最后修改密码时间")
	private String lastPwdModifyTime;

	
	
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
