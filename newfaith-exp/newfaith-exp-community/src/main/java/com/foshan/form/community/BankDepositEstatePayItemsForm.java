package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="非划扣单元项目(BankDepositEstatePayItemsForm)")
@JsonInclude(Include.NON_NULL)
public  class BankDepositEstatePayItemsForm implements IForm{
	/**
	 * 
	 */
	private static final long serialVersionUID = 8052699755265325141L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer bankDepositEstatePayItemsId;
	@ApiModelProperty(value = "0-全部不划扣 1-仅不划扣违约金", example = "1")
	private Integer depositType;
	@ApiModelProperty(value = "单元")
	private CommunityEstateForm estate;
	@ApiModelProperty(value = "收费项目")
	private CommunityPayItemsForm payItems;
//	@ApiModelProperty(value = "房产ID", example = "1")
//	private Integer estateId;
//	@ApiModelProperty(value = "单元编号")
//	protected String unitCode;
//	@ApiModelProperty(value = "项目名称")
//	private String itemsName;
//	@ApiModelProperty(value = "减少项目名称")
//	private String reduceItemsName;
//	@ApiModelProperty(value = "新增项目名称")
//	private String addItemsName;
//	@ApiModelProperty(value = "已选收费项目ID")
//	private String payItemsIdList;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	

	
}

