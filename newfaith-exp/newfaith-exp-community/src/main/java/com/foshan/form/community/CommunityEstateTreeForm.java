package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="住宅树对象(CommunityResidenceForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityEstateTreeForm implements IForm{
	/**
	 * 
	 */
	private static final long serialVersionUID = 4612265700438120179L;
	@ApiModelProperty(value = "标签")
	private String label;
	@ApiModelProperty(value = "楼层值", example = "1")
	private Integer value;
	@ApiModelProperty(value = "住宅")
	private List<CommunityEstateForm> children = new ArrayList<CommunityEstateForm>();


	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}


}
