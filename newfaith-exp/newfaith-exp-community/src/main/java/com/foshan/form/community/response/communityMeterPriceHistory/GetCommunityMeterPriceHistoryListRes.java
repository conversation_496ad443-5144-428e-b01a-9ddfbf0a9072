package com.foshan.form.community.response.communityMeterPriceHistory;

import java.util.ArrayList;
import java.util.List;

import com.foshan.form.community.CommunityMeterPriceHistoryForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="表调价历史(GetCommunityMeterPriceHistoryListRes)")
public class GetCommunityMeterPriceHistoryListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -9213038898230251999L;
	@ApiModelProperty(value = "表调价历史列表")
	private List<CommunityMeterPriceHistoryForm> communityMeterPriceHistoryFormList = new ArrayList<CommunityMeterPriceHistoryForm>(); 

}
