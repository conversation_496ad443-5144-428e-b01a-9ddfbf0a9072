package com.foshan.form.community;

import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AssetForm;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="合同(CommunityMemberContractForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityMemberContractForm implements IForm {
	 /**
	 * 
	 */
	private static final long serialVersionUID = 3511103091738777884L;
	@ApiModelProperty(value = "合同ID", example = "1")
    private Integer contractId;
	@Column(columnDefinition = "varchar(64) comment '收费项目ID'")
	private String payItemsIdList;
	@Column(columnDefinition = "varchar(64) comment '合同编号'")
	private String contractCode;
	@Column(columnDefinition = "varchar(128) comment '合同名称'")
	private String contractName;
	@Column(columnDefinition = "varchar(512) comment '租金说明'")
	private String rentInfo;
	@Column(columnDefinition = "varchar(2000) comment '合同简要说明'")
	private String contractBrief;
	@Column(columnDefinition = "varchar(512) comment '保证金说明'")
	private String depositInfo;
	@Column(columnDefinition = "varchar(32) comment '经营类别'")
	private String businessCategory;
	@ApiModelProperty(value = "租金付款方式：月付/季度付/半年付/年付'")
	private String rentPayType;
	@ApiModelProperty(value = "购方税号")
	private String paytaxNo;
	@ApiModelProperty(value = "购方名称")
	private String buyersName;
	@ApiModelProperty(value = "合同开始时间")
	private String startTime;
	@ApiModelProperty(value = "合同结束日期")
	private String endTime;
	@ApiModelProperty(value = "开票说明")
	private String invoiceDescription;
    @ApiModelProperty(value = "是否当前有效合同 0-否，1是", example = "1")
    private Integer isEffective;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    @ApiModelProperty(value = "修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "合同文件")
    private AssetForm assetForm;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}