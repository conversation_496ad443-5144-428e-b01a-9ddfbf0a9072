package com.foshan.form.community.statistics;

import java.util.Map;
import java.util.TreeMap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "应收报表统计数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityReceivablesVo implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = 8956638552630054747L;
	@ApiModelProperty(value = "楼盘编码")
	private String districtCode;
	@ApiModelProperty(value = "楼盘名称")
	private String districtName;
	@ApiModelProperty(value = "楼阁")
	private String buildingName;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "应收日期")
	private String receivableDate;
	@ApiModelProperty(value = "姓名")
	private String userName;
	@ApiModelProperty(value = "状态")
	private String estateState;
	@ApiModelProperty(value = "划账银行")
	private String bankNmae;
	@ApiModelProperty(value = "银行账户")
	private String bankAccount;
	@ApiModelProperty(value = "账号名")
	private String accountName;
	@ApiModelProperty(value = "总应收")
	private String totalReceivableAmount;
	@ApiModelProperty(value = "总已收")
	private String totalReceivedAmount;
	@ApiModelProperty(value = "总欠费")
	private String totalArrears;
	@ApiModelProperty(value = "建筑面积")
	private String buildingArea;
	@ApiModelProperty(value = "入住日期")
	private String recordDate;
	@ApiModelProperty(value = "计费日期")
	private String billingDate;
	@ApiModelProperty(value = "备注")
	private String comment;
	private Map<String,String> feeMap = new TreeMap<>();
	
	
	public String getNewDistrictCode() {
		return (districtCode.length() == 1 ? "00" + districtCode
				: districtCode.length() == 2 ? "0" + districtCode : districtCode) + districtName;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
