package com.foshan.form.community.response.communityReceivables;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="生成催缴单(CreatePayingInSlipPdfRes)")
@JsonInclude(Include.NON_NULL)
public  class CreatePayingInSlipPdfRes extends BaseResponse {


    /**
	 * 
	 */
	private static final long serialVersionUID = 5349823310063062013L;
	@ApiModelProperty(value = "pdf文件url")
    private String pdfUrl;

}
