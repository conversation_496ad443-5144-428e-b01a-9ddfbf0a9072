package com.foshan.form.community.statistics;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "收款登记表统计数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReceiptDjbVo implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = 1382262735731758018L;
	@ApiModelProperty(value = "收据日期")
	private String receiptDate;
	@ApiModelProperty(value = "收据编码")
	private String receiptCode;
	@ApiModelProperty(value = "摘要")
	private String unitCode;
	@ApiModelProperty(value = "IC卡工本费远程卡")
	private String fee1;
	@ApiModelProperty(value = "IC卡押金")
	private String fee2;
	@ApiModelProperty(value = "别墅管理费")
	private String fee3;
	@ApiModelProperty(value = "场地费")
	private String fee4;
	@ApiModelProperty(value = "车位管理费")
	private String fee5;
	@ApiModelProperty(value = "出租车位管理服务费")
	private String fee6;
	@ApiModelProperty(value = "出租车位租金")
	private String fee7;
	@ApiModelProperty(value = "穿梭车车费")
	private String fee8;
	@ApiModelProperty(value = "代收代缴房屋办证费")
	private String fee9;
	@ApiModelProperty(value = "电费")
	private String fee10;
	@ApiModelProperty(value = "复印费")
	private String fee11;
	@ApiModelProperty(value = "工本费")
	private String fee12;
	@ApiModelProperty(value = "工程维修")
	private String fee13;
	@ApiModelProperty(value = "公共开支分摊")
	private String fee14;
	@ApiModelProperty(value = "公共设施保证金")
	private String fee15;
	@ApiModelProperty(value = "还借款")
	private String fee16;
	@ApiModelProperty(value = "花园管理费")
	private String fee17;
	@ApiModelProperty(value = "花园及停车位管理费")
	private String fee18;
	@ApiModelProperty(value = "惠福电动车充电桩收入")
	private String fee19;
	@ApiModelProperty(value = "家政清洁费")
	private String fee20;
	@ApiModelProperty(value = "借用宿舍钥匙押金")
	private String fee21;
	@ApiModelProperty(value = "精灵屋门票")
	private String fee22;
	@ApiModelProperty(value = "临时停车收费")
	private String fee23;
	@ApiModelProperty(value = "露天车位管理服务费")
	private String fee24;
	@ApiModelProperty(value = "绿化服务费")
	private String fee25;
	@ApiModelProperty(value = "砂石等费用")
	private String fee26;
	@ApiModelProperty(value = "商铺物业管理费")
	private String fee27;
	@ApiModelProperty(value = "商铺租金")
	private String fee28;
	@ApiModelProperty(value = "商业街临时停车费")
	private String fee29;
	@ApiModelProperty(value = "社区活动收入")
	private String fee30;
	@ApiModelProperty(value = "施工服务费")
	private String fee31;
	@ApiModelProperty(value = "水电周转保证金")
	private String fee32;
	@ApiModelProperty(value = "水费")
	private String fee33;
	@ApiModelProperty(value = "诉讼费")
	private String fee34;
	@ApiModelProperty(value = "宿舍物业管理费")
	private String fee35;
	@ApiModelProperty(value = "宿舍租金")
	private String fee36;
	@ApiModelProperty(value = "损害财产赔偿款")
	private String fee37;
	@ApiModelProperty(value = "往来款")
	private String fee38;
	@ApiModelProperty(value = "违约金")
	private String fee39;
	@ApiModelProperty(value = "物业管理费")
	private String fee40;
	@ApiModelProperty(value = "物业管理费保证金")
	private String fee41;
	@ApiModelProperty(value = "小卖部食品")
	private String fee42;
	@ApiModelProperty(value = "游泳池门票")
	private String fee43;
	@ApiModelProperty(value = "有偿服务费")
	private String fee44;
	@ApiModelProperty(value = "员工餐费")
	private String fee45;
	@ApiModelProperty(value = "重型机械进场押金")
	private String fee46;
	@ApiModelProperty(value = "装修保证金")
	private String fee47;
	@ApiModelProperty(value = "装修垃圾清运费")
	private String fee48;
	@ApiModelProperty(value = "租金")
	private String fee49;
	@ApiModelProperty(value = "租金保证金")
	private String fee50;
	@ApiModelProperty(value = "江南邻里充电桩")
	private String fee51;
	@ApiModelProperty(value = "POS机")
	private String method1;
	@ApiModelProperty(value = "现金")
	private String method2;
	@ApiModelProperty(value = "工商银行")
	private String method3;
	@ApiModelProperty(value = "农村信用合作社")
	private String method4;
	@ApiModelProperty(value = "建行")
	private String method5;
	@ApiModelProperty(value = "农行")
	private String method6;
	@ApiModelProperty(value = "支付宝")
	private String method7;
	@ApiModelProperty(value = "银行代收")
	private String method8;
	@ApiModelProperty(value = "收款方式")
	private String paymentMethod;

	
	public static ReceiptDjbVo getReceiptDjbVo(Object[] o) {
		ReceiptDjbVo vo = null;
		if (null != o) {
			try {
				int i=2;
				vo = new ReceiptDjbVo();
				vo.setReceiptDate(o[0].toString());
				vo.setReceiptCode(o[1].toString());
				vo.setUnitCode(o[2].toString());
				vo.setFee1(null != o[i+1] ? o[i+1].toString() : "");
				vo.setFee2(null != o[i+2] ? o[i+2].toString() : "");
				vo.setFee3(null != o[i+3] ? o[i+3].toString() : "");
				vo.setFee4(null != o[i+4] ? o[i+4].toString() : "");
				vo.setFee5(null != o[i+5] ? o[i+5].toString() : "");
				vo.setFee6(null != o[i+6] ? o[i+6].toString() : "");
				vo.setFee7(null != o[i+7] ? o[i+7].toString() : "");
				vo.setFee8(null != o[i+8] ? o[i+8].toString() : "");
				vo.setFee9(null != o[i+9] ? o[i+9].toString() : "");
				vo.setFee10(null != o[i+10] ? o[i+10].toString() : "");
				vo.setFee11(null != o[i+11] ? o[i+11].toString() : "");
				vo.setFee12(null != o[i+12] ? o[i+12].toString() : "");
				vo.setFee13(null != o[i+13] ? o[i+13].toString() : "");
				vo.setFee14(null != o[i+14] ? o[i+14].toString() : "");
				vo.setFee15(null != o[i+15] ? o[i+15].toString() : "");
				vo.setFee16(null != o[i+16] ? o[i+16].toString() : "");
				vo.setFee17(null != o[i+17] ? o[i+17].toString() : "");
				vo.setFee18(null != o[i+18] ? o[i+18].toString() : "");
				vo.setFee19(null != o[i+19] ? o[i+19].toString() : "");
				vo.setFee20(null != o[i+20] ? o[i+20].toString() : "");
				vo.setFee21(null != o[i+21] ? o[i+21].toString() : "");
				vo.setFee22(null != o[i+22] ? o[i+22].toString() : "");
				vo.setFee23(null != o[i+23] ? o[i+23].toString() : "");
				vo.setFee24(null != o[i+24] ? o[i+24].toString() : "");
				vo.setFee25(null != o[i+25] ? o[i+25].toString() : "");
				vo.setFee26(null != o[i+26] ? o[i+26].toString() : "");
				vo.setFee27(null != o[i+27] ? o[i+27].toString() : "");
				vo.setFee28(null != o[i+28] ? o[i+28].toString() : "");
				vo.setFee29(null != o[i+29] ? o[i+29].toString() : "");
				vo.setFee30(null != o[i+30] ? o[i+30].toString() : "");
				vo.setFee31(null != o[i+31] ? o[i+31].toString() : "");
				vo.setFee32(null != o[i+32] ? o[i+32].toString() : "");
				vo.setFee33(null != o[i+33] ? o[i+33].toString() : "");
				vo.setFee34(null != o[i+34] ? o[i+34].toString() : "");
				vo.setFee35(null != o[i+35] ? o[i+35].toString() : "");
				vo.setFee36(null != o[i+36] ? o[i+36].toString() : "");
				vo.setFee37(null != o[i+37] ? o[i+37].toString() : "");
				vo.setFee38(null != o[i+38] ? o[i+38].toString() : "");
				vo.setFee39(null != o[i+39] ? o[i+39].toString() : "");
				vo.setFee40(null != o[i+40] ? o[i+40].toString() : "");
				vo.setFee41(null != o[i+41] ? o[i+41].toString() : "");
				vo.setFee42(null != o[i+42] ? o[i+42].toString() : "");
				vo.setFee43(null != o[i+43] ? o[i+43].toString() : "");
				vo.setFee44(null != o[i+44] ? o[i+44].toString() : "");
				vo.setFee45(null != o[i+45] ? o[i+45].toString() : "");
				vo.setFee46(null != o[i+46] ? o[i+46].toString() : "");
				vo.setFee47(null != o[i+47] ? o[i+47].toString() : "");
				vo.setFee48(null != o[i+48] ? o[i+48].toString() : "");
				vo.setFee49(null != o[i+49] ? o[i+49].toString() : "");
				vo.setFee50(null != o[i+50] ? o[i+50].toString() : "");
				vo.setFee51(null != o[i+51] ? o[i+51].toString() : "");
				int j=53;
				vo.setMethod1(null != o[j+1] ? o[j+1].toString() : "");
				vo.setMethod2(null != o[j+2] ? o[j+2].toString() : "");
				vo.setMethod3(null != o[j+3] ? o[j+3].toString() : "");
				vo.setMethod4(null != o[j+4] ? o[j+4].toString() : "");
				vo.setMethod5(null != o[j+5] ? o[j+5].toString() : "");
				vo.setMethod6(null != o[j+6] ? o[j+6].toString() : "");
				vo.setMethod7(null != o[j+7] ? o[j+7].toString() : "");
				vo.setMethod8(null != o[j+8] ? o[j+8].toString() : "");
				vo.setPaymentMethod(o[62].toString());
			} catch (Exception ex) {
				ex.printStackTrace();
				vo = null;
			}
		}
		return vo;
	}
	
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
