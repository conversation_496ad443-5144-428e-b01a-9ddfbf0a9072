package com.foshan.form.community.response.communityFormulaTemplete;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityFormulaTempleteForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "公式模版返回对象(CommunityFormulaTempleteRes)")
@JsonInclude(Include.NON_NULL)
public class CommunityFormulaTempleteRes extends BaseResponse {
	/**
	* 
	*/
	private static final long serialVersionUID = 7005880867948973293L;
	private CommunityFormulaTempleteForm templeteForm;
}
