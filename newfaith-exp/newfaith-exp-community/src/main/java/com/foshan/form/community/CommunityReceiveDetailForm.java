package com.foshan.form.community;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = " 收款明细（实收）(CommunityReceiveDetailForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityReceiveDetailForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7273729575575063292L;
	@ApiModelProperty(value = "应收款Id")
	private Integer receivablesId;
	@ApiModelProperty(value = "本次收款")
	private BigDecimal currentAmount;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	@Override
	public String toString() {
		return "{'receivablesId'：" + receivablesId + ", 'currentAmount':" + currentAmount
				+ "}";
	}
	
}
