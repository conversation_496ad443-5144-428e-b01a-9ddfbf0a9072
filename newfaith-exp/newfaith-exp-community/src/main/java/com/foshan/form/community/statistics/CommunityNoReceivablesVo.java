package com.foshan.form.community.statistics;

import java.util.Map;
import java.util.TreeMap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "应收未收报表统计数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityNoReceivablesVo implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = -5566217957464007856L;
	@ApiModelProperty(value = "楼盘编号")
	private String districtCode;
	@ApiModelProperty(value = "楼盘名称")
	private String districtName;
	@ApiModelProperty(value = "楼阁")
	private String buildingName;
	@ApiModelProperty(value = "楼栋类别")
	private String buildingType;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "姓名")
	private String userName;
	@ApiModelProperty(value = "电话")
	private String homephone;
	@ApiModelProperty(value = "建筑面积")
	private String buildingArea;
	@ApiModelProperty(value = "房屋状态")
	private String estateState;
	@ApiModelProperty(value = "划账银行")
	private String bankNmae;
	@ApiModelProperty(value = "银行账户")
	private String bankAccount;
	@ApiModelProperty(value = "账号名")
	private String accountName;
	@ApiModelProperty(value = "三个月")
	private String amount3;
	@ApiModelProperty(value = "三至六个月")
	private String amount36;
	@ApiModelProperty(value = "半年以上")
	private String amount6;
	@ApiModelProperty(value = "合计")
	private String totalAmount;
	
	private Map<String,String> itemMap = new TreeMap<>();
	private Map<String,String> feeMap = new TreeMap<>();
	
	public String getNewDistrictCode() {
		return (districtCode.length() == 1 ? "00" + districtCode
				: districtCode.length() == 2 ? "0" + districtCode : districtCode) + districtName;
	}
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
