package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 告警消息(WarningMessageForm)")
@JsonInclude(Include.NON_NULL)
public  class WarningMessageForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer warningMessageId;
  @ApiModelProperty(value = "发生时间")
    private String alarmTime;
    @ApiModelProperty(value = "设备ID")
    private String deviceCode;
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    @ApiModelProperty(value = "告警事件ID")
    private String eventRecordId;
    @ApiModelProperty(value = "告警事件")
    private String eventType;
    @ApiModelProperty(value = "备用")
    private String jsonData;
    @ApiModelProperty(value = "所在分组")
    private String levelName;
    @ApiModelProperty(value = "单元编号")
    private String unitCode;
    @ApiModelProperty(value = "单元地址")
    private String unitAddress;
    @ApiModelProperty(value = "处理说明")
    private String handleComment;
    @ApiModelProperty(value = "处理状态；0:未处理；1：已处理；",example="1")
    private Integer handleState;
    @ApiModelProperty(value = "处理时间")
    private String handleTime;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
