package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 抄表(CommunityMeterRecordForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityMeterRecordForm implements IForm {


	/**
	 * 
	 */
	private static final long serialVersionUID = 2957023303769168196L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityMeterRecordId;
    @ApiModelProperty(value = "本次读数日期")
    private String recordDate;
    @ApiModelProperty(value = "是否归零",example="1")
    private Integer isZero;
    @ApiModelProperty(value = "旧表数据")
    private String oldData;
    @ApiModelProperty(value = "表ID")
    private String oldId;
    @ApiModelProperty(value = "本次读数")
    private String recordNum;
    @ApiModelProperty(value = "上次读数")
    private String lastNum;
    @ApiModelProperty(value = "上次读数日期")
    private String lastDate;
    @ApiModelProperty(value = "抄表人")
    private String recorder;
    @ApiModelProperty(value = "",example="1")
    private Integer meterId;
    @ApiModelProperty(value = "上一次读表记录")
    private CommunityMeterRecordForm lastRecord;
    @ApiModelProperty(value = "总表")
    private CommunityMeterForm communityMeterForm;
    @ApiModelProperty(value = "开始时间")
	private String createTime;
    @ApiModelProperty(value = "修改时间")
	private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据 1--有效数据",example="1")
	private Integer state;
	@ApiModelProperty(value = "数据异常标识，0：正常；1：异常；",example="1")
	private Integer exceptionState;
	@ApiModelProperty(value = "数据异常备注")
	private String exceptionRemark;
    @ApiModelProperty(value = "额外用量")
    private String additionalAmount;
    @ApiModelProperty(value = "额外用量说明")
    private String additionalInstructions;
    @ApiModelProperty(value = "是否有字记录；0：否；1：是；",example="1")
    private Integer haveSub;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
