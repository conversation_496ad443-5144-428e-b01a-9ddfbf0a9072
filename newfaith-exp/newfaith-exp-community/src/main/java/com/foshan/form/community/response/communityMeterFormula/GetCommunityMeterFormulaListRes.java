package com.foshan.form.community.response.communityMeterFormula;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityMeterFormulaForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="自定义分摊公式(GetCommunityMeterFormulaListReq)")
public class GetCommunityMeterFormulaListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 2505037559960347429L;
	@ApiModelProperty(value = "自定义分摊公式列表")
	private List<CommunityMeterFormulaForm> communityMeterFormulaList = new ArrayList<CommunityMeterFormulaForm>(); 

}
