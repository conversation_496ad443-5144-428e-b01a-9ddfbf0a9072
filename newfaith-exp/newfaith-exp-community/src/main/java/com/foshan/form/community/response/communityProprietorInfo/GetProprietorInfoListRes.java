package com.foshan.form.community.response.communityProprietorInfo;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.ProprietorInfoForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取业主信息详情返回对象(GetProprietorInfoListRes)")
@JsonInclude(Include.NON_NULL)
public class GetProprietorInfoListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8997274102601303521L;
	@ApiModelProperty(value = "业主信息对象列表")
	private List<ProprietorInfoForm> proprietorInfoList = new ArrayList<ProprietorInfoForm>();

}
