package com.foshan.form.community.response.emergencyMessage;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.EmergencyMessageForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取应急广播消息返回列表对象(GetEmergencyMessageListRes)")
@JsonInclude(Include.NON_NULL)
public class GetEmergencyMessageListRes extends BasePageResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -5460466130222330416L;
	@ApiModelProperty(value = "应急广播消息对象列表")
	private List<EmergencyMessageForm> emergencyMessageList = new ArrayList<EmergencyMessageForm>(); 

}
