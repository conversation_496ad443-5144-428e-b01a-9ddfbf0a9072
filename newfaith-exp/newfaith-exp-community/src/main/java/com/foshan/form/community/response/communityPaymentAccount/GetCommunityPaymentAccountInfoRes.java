package com.foshan.form.community.response.communityPaymentAccount;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityPaymentAccountForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="账户信息(GetCommunityPaymentAccountInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityPaymentAccountInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 7556120760685709552L;
	@ApiModelProperty(value = "账户信息对象")
	private CommunityPaymentAccountForm communityPaymentAccountForm ; 

}
