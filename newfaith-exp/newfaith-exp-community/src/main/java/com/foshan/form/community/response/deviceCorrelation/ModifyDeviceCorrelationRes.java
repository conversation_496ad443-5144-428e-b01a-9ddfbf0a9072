package com.foshan.form.community.response.deviceCorrelation;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="修改设备关联返回(ModifyDeviceCorrelationRes)")
@JsonInclude(Include.NON_NULL)
public  class ModifyDeviceCorrelationRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer deviceCorrelationId;
   @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "",example="1")
    private Integer deviceId;
    @ApiModelProperty(value = "",example="1")
    private Integer receiveMessageTypeId;
    @ApiModelProperty(value = "",example="1")
    private Integer accountId;
  
}
