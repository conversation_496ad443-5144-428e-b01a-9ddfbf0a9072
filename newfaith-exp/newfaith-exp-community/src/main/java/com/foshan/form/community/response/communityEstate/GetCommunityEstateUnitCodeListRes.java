package com.foshan.form.community.response.communityEstate;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityEstateUnitCodeForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="根据输入字符查询相近的前10个单编号列表(GetCommunityPropertyUnitCodeList)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityEstateUnitCodeListRes extends BasePageResponse {
	private static final long serialVersionUID = 4169411566375159878L;

	@ApiModelProperty(value = "单元编号列表")
	private List<CommunityEstateUnitCodeForm> unitCodeList = new ArrayList<CommunityEstateUnitCodeForm>();

}
