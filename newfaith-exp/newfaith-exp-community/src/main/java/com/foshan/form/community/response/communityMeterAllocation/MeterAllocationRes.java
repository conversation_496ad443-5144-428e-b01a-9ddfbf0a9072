package com.foshan.form.community.response.communityMeterAllocation;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityMeterAllocationForm;
import com.foshan.form.community.CommunityMeterAllocationItemForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "总表分摊试算返回对象(CalTotalAllocationRes)")
@JsonInclude(Include.NON_NULL)
public class MeterAllocationRes extends BasePageResponse {
	/**
	* 
	*/
	private static final long serialVersionUID = 6167934887642901455L;
	private List<CommunityMeterAllocationForm> allocationList = new ArrayList<>();
	private List<CommunityMeterAllocationItemForm> allocationItemList = new ArrayList<>();
	private Object result;
}
