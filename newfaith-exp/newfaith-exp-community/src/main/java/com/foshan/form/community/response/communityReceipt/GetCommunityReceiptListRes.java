package com.foshan.form.community.response.communityReceipt;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityReceiptForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="收款单据(GetCommunityReceiptListReq)")
public class GetCommunityReceiptListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -4649244888324836270L;
	@ApiModelProperty(value = "收款单据列表")
	private List<CommunityReceiptForm> communityReceiptList = new ArrayList<CommunityReceiptForm>(); 

}
