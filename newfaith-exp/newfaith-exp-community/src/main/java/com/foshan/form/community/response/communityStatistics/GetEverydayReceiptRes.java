package com.foshan.form.community.response.communityStatistics;

import java.util.LinkedList;
import java.util.Set;
import java.util.TreeSet;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "获取每日收款列表")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetEverydayReceiptRes<T extends IForm> extends BasePageResponse {
	/**
	* 
	*/
	private static final long serialVersionUID = 575821362721054002L;
	private String fileName;
	private Set<String> titleSet = new TreeSet<>();
	private LinkedList<T> dataList = new LinkedList<>();
}
