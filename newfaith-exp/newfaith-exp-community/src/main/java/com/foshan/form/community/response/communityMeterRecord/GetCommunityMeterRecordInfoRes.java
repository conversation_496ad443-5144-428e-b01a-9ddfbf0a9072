package com.foshan.form.community.response.communityMeterRecord;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityMeterRecordForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="抄表(GetCommunityMeterRecordInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityMeterRecordInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 6665599877757876103L;
	@ApiModelProperty(value = "抄表对象")
	private CommunityMeterRecordForm communityMeterRecordForm ; 

}
