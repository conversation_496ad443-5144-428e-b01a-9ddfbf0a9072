package com.foshan.form.community.response.communityReceipt;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityReceiptForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="收款单据(GetCommunityReceiptInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityReceiptInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 756982350339284808L;
	@ApiModelProperty(value = "收款单据对象")
	private CommunityReceiptForm communityReceiptForm ; 

}
