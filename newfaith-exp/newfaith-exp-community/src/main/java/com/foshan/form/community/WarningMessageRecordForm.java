package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 告警消息(WarningMessageRecordForm)")
@JsonInclude(Include.NON_NULL)
public class WarningMessageRecordForm {
	
	private boolean is_first_page;
	private Integer now_curr;
	private Integer now_count;
	private Integer sum_count;
	private List<EventRecordForm> list_data = new ArrayList<>() ;

}
