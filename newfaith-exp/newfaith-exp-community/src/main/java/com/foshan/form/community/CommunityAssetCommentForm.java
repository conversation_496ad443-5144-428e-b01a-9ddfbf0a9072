package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;

import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区媒资对象(CommunityAssetForm)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityAssetCommentForm implements IForm {
    private static final long serialVersionUID = 5988573270513557990L;
    /**
     *
     */
    @ApiModelProperty(value = "媒资Id",example="1")
    private Integer assetId;
    @ApiModelProperty(value = "审核状态  0:初始化；1:初审通过；2：初审不通过；3：终审通过；4：终审不通过",example="1")
    private Integer auditState;
    @ApiModelProperty(value = "评论")
    private String summaryShort;
    @ApiModelProperty(value = "创建用户Id")
    private Integer memberId;
    @ApiModelProperty(value = "创建管理员Id")
    private Integer userId;
    @ApiModelProperty(value = "发布时间",example="2019-01-01 00:00:00")
    private String createTime;
	@Override
	public int compareTo(Object arg0) {
		// TODO Auto-generated method stub
		return 0;
	}
}
