package com.foshan.form.community;

import java.math.BigDecimal;
import java.util.Map;
import java.util.TreeMap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "公式模版对象(CommunityFormulaTempleteForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityFormulaTempleteForm implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = -5674606020870517844L;
	@ApiModelProperty(value = "公式模版Id", example = "1")
	private Integer templeteId;
	@ApiModelProperty(value = "公式模版名称")
	private String templeteName;
	@ApiModelProperty(value = "公式模版内容")
	private String templeteInfo;
	@ApiModelProperty(value = "公式模版动态参数")
	private Map<String, BigDecimal> dynamicParameter = new TreeMap<>();

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
