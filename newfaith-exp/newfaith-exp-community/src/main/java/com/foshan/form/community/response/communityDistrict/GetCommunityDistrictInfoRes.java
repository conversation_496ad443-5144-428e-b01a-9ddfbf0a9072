package com.foshan.form.community.response.communityDistrict;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityDistrictForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="小区(GetCommunityDistrictInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityDistrictInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 9132376852734255893L;
	@ApiModelProperty(value = "小区对象")
	private CommunityDistrictForm communityDistrictForm ; 

}
