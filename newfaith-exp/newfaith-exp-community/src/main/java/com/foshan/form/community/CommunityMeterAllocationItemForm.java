package com.foshan.form.community;

import java.math.BigDecimal;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "单元总表分摊对象(CommunityMeterAllocationItemForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityMeterAllocationItemForm implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = -2018605154471895630L;
	@ApiModelProperty(value = "分摊数量")
	private BigDecimal allocationNum;
	@ApiModelProperty(value = "分摊金额")
	private BigDecimal allocationAmount;
	@ApiModelProperty(value = "总分摊id")
	private Integer allocationId;
	@ApiModelProperty(value = "单元Id")
	private Integer estateId;
	@ApiModelProperty(value = "单元编号")
	private String unitCode;
	@ApiModelProperty(value = "收费项目")
	private String payItemsName;
	@ApiModelProperty(value = "总表Id")
	private Integer meterId;
	@ApiModelProperty(value = "总表名称")
	private String meterName;

	

	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}


	public CommunityMeterAllocationItemForm(BigDecimal allocationNum, BigDecimal allocationAmount, Integer allocationId,
			Integer estateId) {
		super();
		this.allocationNum = allocationNum;
		this.allocationAmount = allocationAmount;
		this.allocationId = allocationId;
		this.estateId = estateId;
	}


	public CommunityMeterAllocationItemForm(BigDecimal allocationNum, BigDecimal allocationAmount, Integer allocationId,
			Integer estateId, String unitCode, String payItemsName) {
		super();
		this.allocationNum = allocationNum;
		this.allocationAmount = allocationAmount;
		this.allocationId = allocationId;
		this.estateId = estateId;
		this.unitCode = unitCode;
		this.payItemsName = payItemsName;
	}


	

}
