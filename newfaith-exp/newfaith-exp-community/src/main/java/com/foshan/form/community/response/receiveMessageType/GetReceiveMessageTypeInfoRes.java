package com.foshan.form.community.response.receiveMessageType;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.ReceiveMessageTypeForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="接收消息类型表(GetReceiveMessageTypeInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetReceiveMessageTypeInfoRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "接收消息类型表对象")
	private ReceiveMessageTypeForm receiveMessageTypeForm ; 

}
