package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 账户信息(CommunityPaymentAccountForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityPaymentAccountForm implements IForm {


	/**
	 * 
	 */
	private static final long serialVersionUID = 8609785161181043511L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityPaymentAccountId;
    @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
    private Integer state;
    @ApiModelProperty(value = "银行帐户")
    private String bankAccount;
    @ApiModelProperty(value = "银行行号")
    private String bankNo;
    @ApiModelProperty(value = "划帐银行")
    private String bankName;
    @ApiModelProperty(value = "帐号名")
    private String accountName;
	@ApiModelProperty(value = "开户人证件号")
	private String idNumber;
	@ApiModelProperty(value = "会员")
	private CommunityMemberForm memberForm;
	@ApiModelProperty(value = "单元")
	private List<CommunityEstateForm> estateList = new ArrayList<CommunityEstateForm>();

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
