package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = " 表属性(CommunityMeterAttributesForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityMeterAttributesForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3448643327679268494L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer communityMeterAttributesId;
	@ApiModelProperty(value = "属性名称")
	private String attributeName;
	@ApiModelProperty(value = "类别（1:电表、2:水表、3:临时表、4:代收水表、5:代收电表）")
	private Integer category;
	@ApiModelProperty(value = "备注")
	private String comment;
	@ApiModelProperty(value = "单位")
	private String measureUnit;
	@ApiModelProperty(value = "旧表数据")
	private String oldData;
	@ApiModelProperty(value = "旧表ID")
	private String oldId;
	@ApiModelProperty(value = "量程", example = "1")
	private Integer ranges;
	@ApiModelProperty(value = "倍率")
	private String rate;
	@ApiModelProperty(value = "单价")
	private String unitPrice;
    @ApiModelProperty(value = "表")
    private List<CommunityMeterForm> meterList = new ArrayList<CommunityMeterForm>();
	

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
