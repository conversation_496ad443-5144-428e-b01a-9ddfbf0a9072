package com.foshan.form.community.response.communityPaymentRecord;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityPaymentRecordForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="在线支付发起的付款记录(GetCommunityPaymentRecordInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityPaymentRecordInfoRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "在线支付发起的付款记录对象")
	private CommunityPaymentRecordForm communityPaymentRecordForm ; 

}
