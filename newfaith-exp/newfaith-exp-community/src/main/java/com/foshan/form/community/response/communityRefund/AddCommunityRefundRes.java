package com.foshan.form.community.response.communityRefund;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="退款记录(CommunityRefundRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityRefundRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer communityRefundId;
  @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
    private Integer state;
    @ApiModelProperty(value = "退款目标账号")
    private String account;
    @ApiModelProperty(value = "经办人")
    private String agent;
    @ApiModelProperty(value = "退款金额")
    private String amount;
    @ApiModelProperty(value = "退款目标银行")
    private String bank;
    @ApiModelProperty(value = "出纳")
    private String cashier;
    @ApiModelProperty(value = "退款原因/备注 ")
    private String comment;
    @ApiModelProperty(value = "退款失败原因")
    private String failReason;
    @ApiModelProperty(value = "第三方支付系统交易ID/退款ID")
    private String outTradeNo;
    @ApiModelProperty(value = "收款人")
    private String payee;
    @ApiModelProperty(value = "退款方式:0--现金，1--银行转账，2--内部转账，3--在线支付原路退还",example="1")
    private Integer paymentMethod;
    @ApiModelProperty(value = "业务系统退款编号")
    private String refundCode;
    @ApiModelProperty(value = "支付模块订单退款编号")
    private String paymentRefundCode;
    @ApiModelProperty(value = "退款日期")
    private String refundDate;
    @ApiModelProperty(value = "订单退款状态：0未向支付系统发起退款申请；1已经发起退款请求第三方支付正在处理退款,等处理；2完成退款退已经到达标账户；3支付系统退款出现异常;4支付系统付退款关闭",example="1")
    private Integer refundStatus;
    @ApiModelProperty(value = "主管")
    private String supervisor;
    @ApiModelProperty(value = "",example="1")
    private Integer receiptId;
    @ApiModelProperty(value = "",example="1")
    private Integer refundId;
  
}
