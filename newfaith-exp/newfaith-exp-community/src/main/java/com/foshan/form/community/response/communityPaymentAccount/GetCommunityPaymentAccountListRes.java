package com.foshan.form.community.response.communityPaymentAccount;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityPaymentAccountForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="账户信息(GetCommunityPaymentAccountListReq)")
public class GetCommunityPaymentAccountListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -2653632374698682764L;
	@ApiModelProperty(value = "账户信息列表")
	private List<CommunityPaymentAccountForm> communityPaymentAccountList = new ArrayList<CommunityPaymentAccountForm>(); 

}
