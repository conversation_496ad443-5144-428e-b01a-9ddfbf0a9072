package com.foshan.form.community.response.communityInstallmentSms;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityInstallmentSmsForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取账单分期短信返回列表对象(GetCommunityInstallmentSmsListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityInstallmentSmsListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2101102123785795735L;
	@ApiModelProperty(value = "账单分期短信对象列表")
	private List<CommunityInstallmentSmsForm> installmentSmsFormList = new ArrayList<CommunityInstallmentSmsForm>();
}
