package com.foshan.form.community.response.receiveMessageType;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.ReceiveMessageTypeForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="接收消息类型表(GetReceiveMessageTypeListReq)")
public class GetReceiveMessageTypeListRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "接收消息类型表列表")
	private List<ReceiveMessageTypeForm> receiveMessageTypeList = new ArrayList<ReceiveMessageTypeForm>(); 

}
