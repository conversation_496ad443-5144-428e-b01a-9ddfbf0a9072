package com.foshan.form.community.response.communityMemberProperty;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityMemberPropertyForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取会员资产详情返回对象(GetCommunityMemberPropertyInfoRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityMemberPropertyInfoRes extends BaseResponse {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = 2449872802497663047L;
	@ApiModelProperty(value = "会员资产信息",example="1")
	private CommunityMemberPropertyForm memberProperty;


	public GetCommunityMemberPropertyInfoRes(String ret, String retInfo) {
		super(ret, retInfo);
		// TODO 自动生成的构造函数存根
	}

}
