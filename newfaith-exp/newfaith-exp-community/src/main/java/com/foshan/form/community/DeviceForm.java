package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 设备表(DeviceForm)")
@JsonInclude(Include.NON_NULL)
public  class DeviceForm implements IForm {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer deviceId;
  @ApiModelProperty(value = "备注")
    private String comment;
    @ApiModelProperty(value = "时间")
    private String createTime;
    @ApiModelProperty(value = "设备地址")
    private String deviceAddress;
    @ApiModelProperty(value = "设备编码")
    private String deviceCode;
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    @ApiModelProperty(value = "最后修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
    private Integer state;
	@ApiModelProperty(value = "播放时间（应急广播时用）",example="1")
	private Integer runTime;
	@ApiModelProperty(value = "消息标题")
	private String titleMsg;
    @ApiModelProperty(value = "消息内容")
	private String contentMsg;
    @ApiModelProperty(value = "参数")
    private String parameterJson;
	@ApiModelProperty(value = "设备类型；0:全部；1：仅发消息；2：仅查看（如：摄像头）；",example="1")
	private Integer deviceType;
    @ApiModelProperty(value = "排序值",example="1")
    private Integer orders;
    @ApiModelProperty(value = "")
    private MessageAccountForm messageAccount;
  
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
