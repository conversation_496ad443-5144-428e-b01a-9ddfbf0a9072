package com.foshan.form.community.response.receiveMessageType;

import com.foshan.form.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="接收消息类型表(ReceiveMessageTypeRes)")
@JsonInclude(Include.NON_NULL)
public  class AddReceiveMessageTypeRes extends BaseResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID",example="1")
	private Integer receiveMessageTypeId;
  @ApiModelProperty(value = "对象名称")
    private String objectName;
    @ApiModelProperty(value = "类型值，0：应急广播；1：小程序；2：短信；",example="1")
    private Integer typeValue;
  
}
