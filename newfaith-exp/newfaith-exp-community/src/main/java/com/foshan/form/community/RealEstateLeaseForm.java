package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="不动产租、售服务(RealEstateLeaseForm)")
@JsonInclude(Include.NON_NULL)
public class RealEstateLeaseForm {
	@ApiModelProperty(value = "网签合同备案编号")
	private String contractNo;
	@ApiModelProperty(value = "不动产地址")
	private String realEstatePlace;
	@ApiModelProperty(value = "详细地址")
	private String fullAddress;
	@ApiModelProperty(value = "跨地市标志")
	private String acrossFlag;
	@ApiModelProperty(value = "土地增值税项目编号")
	private String landTaxItemNo;
	@ApiModelProperty(value = "核定计税价格")
	private String appraiseAmount;
	@ApiModelProperty(value = "实际成交含税金额")
	private String realAmount;
	@ApiModelProperty(value = "产权证书号")
	private String munimentNo;
	@ApiModelProperty(value = "面积单位")
	private String areaUnit;
	@ApiModelProperty(value = "租赁期起止")
	private String leaseTerm;


}