package com.foshan.form.community.response.communityBuilding;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityBuildingForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="楼栋(GetCommunityBuildingListReq)")
public class GetCommunityBuildingListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 9127667805730392614L;
	@ApiModelProperty(value = "楼栋列表")
	private List<CommunityBuildingForm> communityBuildingList = new ArrayList<CommunityBuildingForm>(); 

}
