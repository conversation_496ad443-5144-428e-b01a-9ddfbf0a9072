package com.foshan.form.community.response.communityRefund;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityRefundForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="退款记录(GetCommunityRefundListReq)")
public class GetCommunityRefundListRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "退款记录列表")
	private List<CommunityRefundForm> communityRefundList = new ArrayList<CommunityRefundForm>(); 

}
