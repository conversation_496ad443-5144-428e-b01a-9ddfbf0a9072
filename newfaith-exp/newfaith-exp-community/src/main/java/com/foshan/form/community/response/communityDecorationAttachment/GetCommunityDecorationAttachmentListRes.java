package com.foshan.form.community.response.communityDecorationAttachment;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityDecorationAttachmentForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="装修项目附件(GetCommunityDecorationAttachmentListReq)")
public class GetCommunityDecorationAttachmentListRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "装修项目附件列表")
	private List<CommunityDecorationAttachmentForm> communityDecorationAttachmentList = new ArrayList<CommunityDecorationAttachmentForm>(); 

}
