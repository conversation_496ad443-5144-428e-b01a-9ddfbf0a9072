package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="门禁设施对象(CommunityEntranceFacilitiesForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityEntranceFacilitiesForm extends CommunityFacilitiesForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -7159403865994612770L;
	@ApiModelProperty(value = "位置")
	private String location;
	@ApiModelProperty(value = "类型",example="1")
	private Integer types;
	@ApiModelProperty(value = "内网IP")
	private String networkIP;

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
