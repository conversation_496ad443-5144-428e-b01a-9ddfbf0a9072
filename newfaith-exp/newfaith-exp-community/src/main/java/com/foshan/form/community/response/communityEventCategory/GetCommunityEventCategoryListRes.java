package com.foshan.form.community.response.communityEventCategory;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityEventCategoryForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取事件类型返回列表对象(GetCommunityEventCategoryListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityEventCategoryListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7543977477991761203L;
	@ApiModelProperty(value = "事件类型对象列表")
	private List<CommunityEventCategoryForm> eventCategoryList = new ArrayList<CommunityEventCategoryForm>();

	

	public GetCommunityEventCategoryListRes(String ret, String retInfo) {
		super(ret, retInfo);
	}

}
