package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;
import com.foshan.form.RegionForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value=" 开发商(CommunityBuilderForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityBuilderForm implements IForm {


	/**
	 * 
	 */
	private static final long serialVersionUID = -6448538248607683786L;
	@ApiModelProperty(value = "ID",example="1")
	private Integer communityBuilderId;
    @ApiModelProperty(value = "名称")
    private String builderName;
    @ApiModelProperty(value = "地址")
    private String builderAddress;
    @ApiModelProperty(value = "法人代表")
    private String legalRepresentative;
	@ApiModelProperty(value = "区域")
	private RegionForm regionForm;
    @ApiModelProperty(value = "数据状态",example="1")
    private Integer state;
    @ApiModelProperty(value = "修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    
    public CommunityBuilderForm(Integer communityBuilderId,String builderName,String builderAddress,String legalRepresentative) {
    	this.communityBuilderId = communityBuilderId;
    	this.builderName = builderName;
    	this.builderAddress = builderAddress;
    	this.legalRepresentative = legalRepresentative;
    }

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
