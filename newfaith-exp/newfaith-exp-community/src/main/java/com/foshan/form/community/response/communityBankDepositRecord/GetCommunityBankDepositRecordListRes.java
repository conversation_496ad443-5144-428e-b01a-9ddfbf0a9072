package com.foshan.form.community.response.communityBankDepositRecord;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityBankDepositRecordForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="银行划账记录(GetCommunityBankDepositRecordListReq)")
public class GetCommunityBankDepositRecordListRes extends BasePageResponse {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "银行划账记录列表")
	private List<CommunityBankDepositRecordForm> communityBankDepositRecordList = new ArrayList<CommunityBankDepositRecordForm>(); 
	@ApiModelProperty(value = "成功总条数" , example = "1")
	private Integer depositNumber;
	@ApiModelProperty(value = "划账成功的总金额")
	private String depositAmount;
	@ApiModelProperty(value = "总金额")
	private String totalAmount;

}
