package com.foshan.form.community.response.reservationActivities;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityReservationActivitiesForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获支活动返回列表对象(GetReservationActivitiesListRes)")
@JsonInclude(Include.NON_NULL)
public class GetReservationActivitiesListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7170295367858115401L;
	@ApiModelProperty(value = "用户对象列表")
	private List<CommunityReservationActivitiesForm> reservationActivitiesList = new ArrayList<CommunityReservationActivitiesForm>(); 

}
