package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "辅助数据对象(AuxiliaryForm)")
@JsonInclude(Include.NON_NULL)
public  class AuxiliaryForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 2790252817502894232L;
	@ApiModelProperty(value = "辅助数据描述,标识应急广播节目资源文件名或音视频流地址")
	private String auxiliaryDesc;
	@ApiModelProperty(value = "辅助数据类型",example="1")
	private Integer auxiliaryType;
	@ApiModelProperty(value = "辅助数据文件大小",example="1")
	private Integer size;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
