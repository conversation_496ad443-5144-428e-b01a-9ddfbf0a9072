package com.foshan.form.community.statistics;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "应收未收报表统计数据")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityNoReceivablesSummaryVo implements IForm {
	/**
	* 
	*/
	private static final long serialVersionUID = -7530218758528658905L;
	private String districtName;
	private String item1;
	private String fee1;
	private String item2;
	private String fee2;
	private String item3;
	private String fee3;
	private String item4;
	private String fee4;
	private String item5;
	private String fee5;
	private String amount3Num;
	private String amount3;
	private String amount6Num;
	private String amount6;

	public static CommunityNoReceivablesSummaryVo getCommunityNoReceivablesSummaryVo(Object[] o) {
		CommunityNoReceivablesSummaryVo vo = null;
		if (null != o) {
			try {
				vo = new CommunityNoReceivablesSummaryVo();
				vo.setDistrictName(o[0].toString());
				vo.setItem1(null != o[1] ? o[1].toString() : "");
				vo.setFee1(null != o[2] ? o[2].toString() : "");
				vo.setItem2(null != o[3] ? o[3].toString() : "");
				vo.setFee2(null != o[4] ? o[4].toString() : "");
				vo.setItem3(null != o[5] ? o[5].toString() : "");
				vo.setFee3(null != o[6] ? o[6].toString() : "");
				vo.setItem4(null != o[7] ? o[7].toString() : "");
				vo.setFee4(null != o[8] ? o[8].toString() : "");
				vo.setItem5(null != o[9] ? o[9].toString() : "");
				vo.setFee5(null != o[10] ? o[10].toString() : "");
				vo.setAmount3Num(null != o[11] ? o[11].toString() : "");
				vo.setAmount3(null != o[12] ? o[12].toString() : "");
				vo.setAmount6Num(null != o[13] ? o[13].toString() : "");
				vo.setAmount6(null != o[14] ? o[14].toString() : "");
			} catch (Exception ex) {
				ex.printStackTrace();
				vo = null;
			}
		}
		return vo;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
