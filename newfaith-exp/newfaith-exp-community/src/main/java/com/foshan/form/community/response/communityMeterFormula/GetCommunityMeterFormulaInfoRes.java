package com.foshan.form.community.response.communityMeterFormula;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityMeterFormulaForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="自定义分摊公式(GetCommunityMeterFormulaInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityMeterFormulaInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -5724482261944299033L;
	@ApiModelProperty(value = "自定义分摊公式对象")
	private CommunityMeterFormulaForm communityMeterFormulaForm ; 

}
