package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="业主信息(ProprietorInfoForm)")
@JsonInclude(Include.NON_NULL)
public class ProprietorInfoForm  implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -3234009740741269074L;
	@ApiModelProperty(value = "房产ID", example = "1")
	private Integer estateId;
	@ApiModelProperty(value = "序号")
	private String serialNumber;
    @ApiModelProperty(value = "小区名称")
    private String districtName;
    @ApiModelProperty(value = "楼栋名称")
    private String buildingName;
    @ApiModelProperty(value = "单元编号")
    private String unitCode;
    @ApiModelProperty(value = "房屋状态：0:未收楼;1:已入住;2:装修中;3:出租;4:离退;5:自主", example = "1")
    private String estateState;
	@ApiModelProperty(value = "建筑面积")
	private String buildingArea;
	@ApiModelProperty(value = "手机号码")
	private String phone;
	@ApiModelProperty(value = "真实名字")
	private String userName;
	@ApiModelProperty(value = "职业")
	private String profession="";
	@ApiModelProperty(value = "家庭人数")
	private String familySize="";
	@ApiModelProperty(value = "家庭成员")
	private String familyMember="";
	@ApiModelProperty(value = "是否关系客户")
	private String relation="";
	@ApiModelProperty(value = "是否有过重大报修")
	private String majorRepair="";
	@ApiModelProperty(value = "以往投诉次数")
	private String complaintsNumber="";
	@ApiModelProperty(value = "投诉内容")
	private String complaintsContent="";
	@ApiModelProperty(value = "是否参与过群诉")
	private String participationGroupAction="";
	@ApiModelProperty(value = "是否主持过群诉")
	private String directGroupAction="";
	@ApiModelProperty(value = "业主群中是否活越")
	private String activeGroup="";
	@ApiModelProperty(value = "是否喂养宠物")
	private String havePet="";
	@ApiModelProperty(value = "是否意见领袖")
	private String opinionLeader="";
	@ApiModelProperty(value = "业余爱好")
	private String hobby="";
	@ApiModelProperty(value = "关联房产")
	private String associatedProperty="";
	@ApiModelProperty(value = "业主ID", example = "1")
	private Integer memberId;
	@ApiModelProperty(value = "欠费原因")
	private String reasonArrears="";
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	
}
