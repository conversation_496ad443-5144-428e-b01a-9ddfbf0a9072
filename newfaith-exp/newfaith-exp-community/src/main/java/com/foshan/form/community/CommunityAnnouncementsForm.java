package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.IForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="社区公告(CommunityAnnouncementForm)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommunityAnnouncementsForm implements IForm {
    private static final long serialVersionUID = -5181872300042256090L;

    /**
     *
     */
    @ApiModelProperty(value = "公告ID", example = "1")
    private Integer announcementId;
    @ApiModelProperty(value = "公告审核状态：0--未审核 1--已审核", example = "0")
    private Integer status;
    @ApiModelProperty(value = "公告内容")
    private String content;
    @ApiModelProperty(value = "公告标题")
    private String title;
    @ApiModelProperty(value = "发布时间")
    private String createTime;
    @ApiModelProperty(value = "发布者")
    private String publisher;
    @ApiModelProperty(value = "审核者")
    private String reviewer;
    @ApiModelProperty(value = "公告类型：0--活动公告 1--物业公告 2--社区政务", example = "0")
    private Integer announcementsType;
    @ApiModelProperty(value = "是否置顶：0--否 1--是", example = "0")
    private Integer isTop;
    @ApiModelProperty(value = "公告排序值", example = "0")
    private Integer announcementsOrders;


    @Override
    public int compareTo(Object o) {
        return 0;
    }
}
