package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModelProperty;

@JsonInclude(Include.NON_NULL)
public class OperationDiaryForm implements IForm {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 2942111447957973205L;
	@ApiModelProperty(value = "标题" )
	private String title;
	@ApiModelProperty(value = "操作内容" )
	private String opinion;
	@ApiModelProperty(value = "操作时间" )
	private String time;
	@ApiModelProperty(value = "操作人" )
	private String author;
	@ApiModelProperty(value = "操作人ID" , example = "1")
	private Integer userId;
	@ApiModelProperty(value = "用户类型" )
	private String type;
	
	public OperationDiaryForm() {
		super();
	}
	public OperationDiaryForm(String title,String opinion,String time,String author,Integer userId,String type) {
		this.title = title;
		this.opinion = opinion;
		this.time = time;
		this.author = author;
		this.userId = userId;
		this.type = type;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getOpinion() {
		return opinion;
	}

	public void setOpinion(String opinion) {
		this.opinion = opinion;
	}

	public String getTime() {
		return time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	public String getAuthor() {
		return author;
	}

	public void setAuthor(String author) {
		this.author = author;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
		

}
