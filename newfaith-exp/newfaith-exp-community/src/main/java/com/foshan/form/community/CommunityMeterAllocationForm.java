package com.foshan.form.community;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "总表分摊对象(CommunityMeterAllocationForm)")
@JsonInclude(Include.NON_NULL)
public class CommunityMeterAllocationForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -1701581516988224427L;
	@ApiModelProperty(value = "分摊ID")
	private Integer allocationId;
	@ApiModelProperty(value = "分摊日期")
	private String allocationDate;
	@ApiModelProperty(value = "分摊结束日期")
	private String allocationEndDate;
	@ApiModelProperty(value = "总分摊数量")
	private BigDecimal allocationNum;
	@ApiModelProperty(value = "总分摊金额")
	private BigDecimal allocationAmount;
	@ApiModelProperty(value = "分摊单价")
	private BigDecimal unitPrice;
	@ApiModelProperty(value = "分摊表附加用量")
	private BigDecimal additionalAmount;
	@ApiModelProperty(value = "本次分摊动态参数")
	private Map<String,BigDecimal> dynamicParameter;
	@ApiModelProperty(value = "总表Id")
	private Integer meterId;
	@ApiModelProperty(value = "总表名称")
	private String meterName;
	@ApiModelProperty(value = "总表编号")
	private String meterCode;
	@ApiModelProperty(value = "分摊费用名称")
	private String payItemsName;
	@ApiModelProperty(value = "分摊公式模版Id")
	private Integer templeteId;
	@ApiModelProperty(value = "单元分摊列表")
	private Set<CommunityMeterAllocationItemForm>  itemList = new HashSet<>();
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
