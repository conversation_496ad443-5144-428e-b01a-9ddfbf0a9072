package com.foshan.form.community;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="调价历史(CommunityMeterPriceHistoryForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityMeterPriceHistoryForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2365048052103316028L;
	@ApiModelProperty(value = "ID", example = "1")
	private Integer communityMeterPriceHistoryId;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "操作类型 0：新增；1：修改；", example = "1")
	private Integer operationType;
	@ApiModelProperty(value = "调整单价")
	private String unitPrice;
	@ApiModelProperty(value = "表属性ID", example = "1")
	private Integer meterAttributesId;
	@ApiModelProperty(value = "属性名称")
	private String attributeName;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
