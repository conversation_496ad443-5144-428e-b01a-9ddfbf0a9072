package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "应急广播消息内容对象(MsgContentForm)")
@JsonInclude(Include.NON_NULL)
public class MsgContentForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8634228006256038361L;
	@ApiModelProperty(value = "语种代码;示例：汉语3字母语种代码的术代码为“zho”")
	private String languageCode;
	@ApiModelProperty(value = "消息标题文本")
	private String msgTitle;
	@ApiModelProperty(value = "消息内容文本")
	private String msgDesc;
	@ApiModelProperty(value = "覆盖区域编码")
	private String areaCode;
	@ApiModelProperty(value = "文件ID（PM3）")
	private Integer fileId;
	@ApiModelProperty(value = "辅助数据对象")
	private AuxiliaryForm  auxiliary;
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
