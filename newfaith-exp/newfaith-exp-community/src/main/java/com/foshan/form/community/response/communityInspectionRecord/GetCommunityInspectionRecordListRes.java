package com.foshan.form.community.response.communityInspectionRecord;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityInspectionRecordForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取巡查记录返回列表对象(GetCommunityInspectionRecordListRes)")
@JsonInclude(Include.NON_NULL)
public class GetCommunityInspectionRecordListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -8824841907988023485L;
	@ApiModelProperty(value = "巡查记录对象列表")
	private List<CommunityInspectionRecordForm> inspectionRecordFormList = new ArrayList<CommunityInspectionRecordForm>();


}
