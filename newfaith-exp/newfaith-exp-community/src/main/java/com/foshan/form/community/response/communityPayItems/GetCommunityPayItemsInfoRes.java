package com.foshan.form.community.response.communityPayItems;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;
import com.foshan.form.community.CommunityPayItemsForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="收费项目设定(GetCommunityPayItemsInfoRes)")
@JsonInclude(Include.NON_NULL)
public  class GetCommunityPayItemsInfoRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 2452207543252910587L;
	@ApiModelProperty(value = "收费项目设定对象")
	private CommunityPayItemsForm communityPayItemsForm ; 

}
