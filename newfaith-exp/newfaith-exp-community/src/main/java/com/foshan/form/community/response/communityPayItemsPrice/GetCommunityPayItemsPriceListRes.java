package com.foshan.form.community.response.communityPayItemsPrice;

import java.util.ArrayList;
import java.util.List;
import com.foshan.form.community.CommunityPayItemsPriceForm;
import com.foshan.form.response.BasePageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="收费项目子表返回列表(GetCommunityPayItemsPriceListRes)")
public class GetCommunityPayItemsPriceListRes extends BasePageResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = -3371153743873519067L;
	@ApiModelProperty(value = "收费项目子表列表")
	private List<CommunityPayItemsPriceForm> payItemsPriceList = new ArrayList<CommunityPayItemsPriceForm>(); 

}
