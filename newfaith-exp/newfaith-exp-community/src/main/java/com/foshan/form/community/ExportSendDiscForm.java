package com.foshan.form.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="银行划账-特殊划扣单元项目(ExportSendDiscForm)")
@JsonInclude(Include.NON_NULL)
public  class ExportSendDiscForm implements IForm {
	/**
	 * 
	 */
	private static final long serialVersionUID = 8052699755265325141L;
	@ApiModelProperty(value = "房产ID", example = "1")
	private Integer estateId;
	@ApiModelProperty(value = "单元编号")
	protected String unitCode;
	@ApiModelProperty(value = "帐号名")
	private String accountName;
	@ApiModelProperty(value = "银行帐户")
	private String bankAccount;
	@ApiModelProperty(value = "划帐银行")
	private String bankName;
	@ApiModelProperty(value = "应收金额")
	private String receivableAmount;
	
	
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
	

	
}
