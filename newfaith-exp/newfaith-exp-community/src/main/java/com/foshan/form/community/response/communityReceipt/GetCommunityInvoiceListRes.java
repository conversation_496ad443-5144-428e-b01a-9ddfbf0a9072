package com.foshan.form.community.response.communityReceipt;

import java.util.ArrayList;
import java.util.List;

import com.foshan.form.community.CommunityInvoiceForm;
import com.foshan.form.response.BasePageResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="获取发票列表(GetCommunityInvoiceListRes)")
public class GetCommunityInvoiceListRes extends BasePageResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3199432113019321262L;
	@ApiModelProperty(value = "发票列表")
	private List<CommunityInvoiceForm> invoiceList = new ArrayList<CommunityInvoiceForm>(); 

}
