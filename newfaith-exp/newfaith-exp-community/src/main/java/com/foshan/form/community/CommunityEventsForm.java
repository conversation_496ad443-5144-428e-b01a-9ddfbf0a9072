package com.foshan.form.community;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.AssetForm;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="事件(CommunityEventForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityEventsForm implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4058977212474244575L;
	@ApiModelProperty(value = "事件ID",example="1")
	private Integer eventId;
	@ApiModelProperty(value = "创建时间")
	private String createTime;
	@ApiModelProperty(value = "修改时间")
	private String lastModifyTime;
	@ApiModelProperty(value = "审核意见")
	private String auditOpinion;
	@ApiModelProperty(value = "说明")
	private String centent;
	@ApiModelProperty(value = "申请详情")
	private String itemsdetail;
	@ApiModelProperty(value = "标题")
	private String title;
//	@ApiModelProperty(value = "日志")
//	private String diary;
	@ApiModelProperty(value = "类型：0普通类型、1装修申请",example="1")
	private Integer eventType;
	@ApiModelProperty(value = "日志")
	private Map<String,Object> diary;
	@ApiModelProperty(value = "状态：0初始化、1待审核、2审核通过（待派单）、3审核不通过、4已派单、5已完成、6已撤销、7待支付费用、8进行中、9装修完成、10验收中、11验收不通过、12验收通过、13申请退款",example="1")
	private Integer eventState;
	@ApiModelProperty(value = "状态 0--无效数据  1--有效数据",example="1")
	private Integer state;
    @ApiModelProperty(value = "订单编号")
    private String orderCode;
	@ApiModelProperty(value = "事件分类")
	private CommunityEventCategoryForm communityEventCategoryForm;
	@ApiModelProperty(value = "会员")
	private CommunityMemberForm member;
	@ApiModelProperty(value = "资产")
	private CommunityPropertyForm property;
	@ApiModelProperty(value = "发送状态:0未发送 1已发送成功 2发送失败",example="1")
	private Integer sendstatus;
	@ApiModelProperty(value = "云之家请求参数")
	private String parameterReq;
    @ApiModelProperty(value = "验收详情")
    private String checkAcceptItems;
    @ApiModelProperty(value = "验收时间")
    private String acceptanceTime;
	@ApiModelProperty(value = "退款方式:0、抵扣物业费 1、原路或现金退回 2、银行卡",example="1")
	private Integer refundMethod;
	@ApiModelProperty(value = "银行号码")
	private String bankAccount;
	@ApiModelProperty(value = "银行名称")
	private String bankName;
	@ApiModelProperty(value = "开户人")
	private String accountName;
    @ApiModelProperty(value = "资料审核人")
    private String informationAuditor;
    @ApiModelProperty(value = "资料审核时间")
    private String informationAuditionTime;
    @ApiModelProperty(value = "存在押金:0、否 1、押金未退;2:押金已退",example="1")
    private Integer haveCashPledge;
	@ApiModelProperty(value = "申请详情")
	private List<Object> itemsdetailList;
    @ApiModelProperty(value = "二维码")
    private AssetForm qrCodeForm;
    @ApiModelProperty(value = "施工证")
    private AssetForm constructionPermitForm;
    @ApiModelProperty(value = "应收款列表")
    private List<CommunityReceivablesForm> receivablesList = new ArrayList<CommunityReceivablesForm>();
    @ApiModelProperty(value = "收据图片列表")
    List<AssetForm> receiptImageList = new ArrayList<AssetForm>();
	@ApiModelProperty(value = "身份证图片ID")
	List<AssetForm> idCardImageList = new ArrayList<AssetForm>();
	@ApiModelProperty(value = "银行图片ID")
	List<AssetForm> bankImageList = new ArrayList<AssetForm>();
    @ApiModelProperty(value = "图片列表")
    List<AssetForm> imageList = new ArrayList<AssetForm>();
	
	public CommunityEventsForm(Integer eventId,String createTime,String lastModifyTime,String auditOpinion,
			String centent,String title,Integer eventState,Integer state,Map<String,Object> diary) {
		this.eventId = eventId;
		this.createTime = createTime;
		this.lastModifyTime = lastModifyTime;
		this.auditOpinion = auditOpinion;
		this.centent = centent;
		this.title = title;
		this.eventState = eventState;
		this.state = state;
		this.diary = diary;
	}

	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}

}
