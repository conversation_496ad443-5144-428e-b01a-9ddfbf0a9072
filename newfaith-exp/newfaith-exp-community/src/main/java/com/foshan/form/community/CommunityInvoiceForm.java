package com.foshan.form.community;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="发票对象(CommunityInvoiceForm)")
@JsonInclude(Include.NON_NULL)
public  class CommunityInvoiceForm implements IForm {

	
	/**
	 * 
	 */
	private static final long serialVersionUID = 2690799943008432621L;
	@ApiModelProperty(value = "发票ID", example = "1")
	private Integer invoiceId;
	@ApiModelProperty(value = "开票人")
	private String agent;
	@ApiModelProperty(value = "购方名称")
	private String buyerName;
	@ApiModelProperty(value = "购方地址")
	private String buyerAddre;
	@ApiModelProperty(value = "购方银行账号")
	private String buyerBankAccount;
	@ApiModelProperty(value = "购方识别号")
	private String buyerCode;
	@ApiModelProperty(value = "操作类型：0－开蓝票；1－开红票，2申请红票，3 撤销申请红票'", example = "1")
	private Integer invoiceType;
	@ApiModelProperty(value = "发票代码")
	private String invoiceCode;
	@ApiModelProperty(value = "发票编号")
	private String invoiceNum;
	@ApiModelProperty(value = "发票请求流水号")
	private String invoiceSn;
	@ApiModelProperty(value = "红冲原因")
	private String redDashedReason;
	@ApiModelProperty(value = "发票总金额")
	private String totalAmount;
	@ApiModelProperty(value = "税额")
	private String taxAmount;
	@ApiModelProperty(value = "平台下载地址")
	private String platUrl;
	@ApiModelProperty(value = "本地下载地址")
	private String localUrl;
	@ApiModelProperty(value = "请求返回码")
	private String returnCode;
	@ApiModelProperty(value = "请求返回信息")
	private String returnMessage;
	@ApiModelProperty(value = "操作状态 0 不成功，1成功，2正在开（操作类型为 0,1的时候用）'", example = "1")
	private Integer invoiceState;
    @ApiModelProperty(value = "数据状态",example="1")
    private Integer state;
	 @ApiModelProperty(value = "正票是否红冲,0-否，1是",example="1")
	private Integer isRedDashed;
    @ApiModelProperty(value = "修改时间")
    private String lastModifyTime;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
	@Override
	public int compareTo(Object o) {
		// TODO Auto-generated method stub
		return 0;
	}
}
