package com.foshan.form.community.response.communityReceiptReceivables;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.community.CommunityReceiptReceivablesForm;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="收款操作返回对象(AddCommunityReceiveRes)")
@JsonInclude(Include.NON_NULL)
public  class AddCommunityReceiveRes extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8990773831785384390L;
	@ApiModelProperty(value = "本次收款生成的实收记录")
	private List<CommunityReceiptReceivablesForm> commnuityReceiptReceivablesFormList;
	@ApiModelProperty(value = "在线小程序支付返回参数，ret=0000 ，paymentOnlineResultInfo.returnCode = SUCCESS 且 返回communityPaymentRecordId代表发起在线支付成功！返回报文样例：1、微信小程序支付response:\n" + 
			"{\n" + 
			"    \"ret\": \"0000\",\n" + 
			"    \"retInfo\": \"操作成功！\",\n" + 
			"    \"paymentOnlineResultInfo\": {\n" + 
			"        \"orderResult\": {\n" + 
			"            \"appId\": \"wx290a7vce166dc3304\",\n" + 
			"            \"nonceStr\": \"55b24888c04549d8b461b692b4d3ec36\",\n" + 
			"            \"package\": \"prepay_id=wx20211202144029704222\",\n" + 
			"            \"paySign\": \"DDA11E8B5188E83C7F3188883E29545756C7C2ADB10C854D0EEBB7D73B3B1B84\",\n" + 
			"            \"signType\": \"HMAC-SHA256\",\n" + 
			"            \"timeStamp\": \"1638427227552\"\n" + 
			"        },\n" + 
			"        \"paymentSessionSn\": \"PS00202112020762734605\",\n" + 
			"        \"returnCode\": \"SUCCESS\",\n" + 
			"        \"returnInfo\": \"创建订单成功\"\n" + 
			"    },\n" + 
			"    \"communityPaymentRecordId\": 21\n" + 
			"}\n" + 
			"\n" + 
			"2、微信付款码支付 response:\n" + 
			"{\n" + 
			"    \"ret\": \"0000\",\n" + 
			"    \"retInfo\": \"操作成功！\",\n" + 
			"    \"paymentOnlineResultInfo\": {\n" + 
			"        \"orderResult\": {\n" + 
			"            \"bankType\": \"CMC\",\n" + 
			"            \"totalAmount\": \"1\"\n" + 
			"        },\n" + 
			"        \"paymentSessionSn\": \"PS00202112021609169442\",\n" + 
			"        \"returnCode\": \"SUCCESS\",\n" + 
			"        \"returnInfo\": \"创建订单成功\"\n" + 
			"    }\n" + 
			"}\n" + 
			"\n" + 
			"3、微信二维码支付 response:\n" + 
			"{\n" + 
			"    \"ret\": \"0000\",\n" + 
			"    \"retInfo\": \"操作成功！\",\n" + 
			"    \"paymentOnlineResultInfo\": {\n" + 
			"        \"orderResult\": {\n" + 
			"            \"imageBase64\": \"/9j/4AAQSkZJRgABAgAAAQABAAD/2wBDAA...\"\n" + 
			"        },\n" + 
			"        \"paymentSessionSn\": \"PS00202112020294782853\",\n" + 
			"        \"returnCode\": \"SUCCESS\",\n" + 
			"        \"returnInfo\": \"创建订单成功\"\n" + 
			"    },\n" + 
			"    \"communityPaymentRecordId\": 17\n" + 
			"}\n" + 
			"4、支付宝小程序支付"+
			"{\n"
			+ "    \"ret\": \"0000\",\n"
			+ "    \"retInfo\": \"操作成功！\",\n"
			+ "    \"paymentOnlineResultInfo\": {\n"
			+ "        \"orderResult\": {\n"
			+ "            \"appId\": \"2021004122678017\",\n"
			+ "            \"outTradeNo\": \"PS00202311221112790945\",\n"
			+ "            \"timeStamp\": \"1700616895577\",\n"
			+ "            \"tradeNo\": \"2023112222001484191402487048\"\n"
			+ "        },\n"
			+ "        \"paymentSessionSn\": \"PS00202311221112790945\",\n"
			+ "        \"returnCode\": \"SUCCESS\",\n"
			+ "        \"returnInfo\": \"成功发起收款\"\n"
			+ "    },\n"
			+ "    \"communityPaymentRecordId\": 6290\n"
			+ "}")
    private Map<String,Object> paymentOnlineResultInfo;
	@ApiModelProperty(value = "业务系统支付会话记录,用于管理后台查询是否支付成功，注意由于在支付的结果是异步通知的，所以需要用该ID去轮询查询支付记录的状态来确认是否收款成功")
	private Integer communityPaymentRecordId;
	@ApiModelProperty(value = "业务系统支付会话记录,用于催缴费查询是否支付成功，注意由于在支付的结果是异步通知的，所以需要用该Code去轮询查询支付记录的状态来确认是否收款成功")
	private String communityPaymentRecordCode;
	@ApiModelProperty(value = "支付成功后生成的收据ID")
	private Integer receiptId;
}
