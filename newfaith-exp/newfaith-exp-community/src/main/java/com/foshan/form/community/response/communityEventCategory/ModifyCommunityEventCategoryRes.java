package com.foshan.form.community.response.communityEventCategory;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.response.BaseResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="修改事件类型返回对象(ModifyCommunityEventCategoryRes)")
@JsonInclude(Include.NON_NULL)
public class ModifyCommunityEventCategoryRes extends BaseResponse {


	/**
	 * 
	 */
	private static final long serialVersionUID = 1439175448488492848L;
	@ApiModelProperty(value = "事件类型Id",example="1")
	private Integer eventCategoryId;

}
