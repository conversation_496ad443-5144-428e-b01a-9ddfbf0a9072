package com.foshan.entity.community;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_event_notification")
@org.hibernate.annotations.Table(appliesTo = "t_community_event_notification",comment="事件通知表") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityEventNotificationEntity extends Community {

	private static final long serialVersionUID = 7171207623728922821L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
	@Column(columnDefinition = "int(2) comment '分类：0：暂停施工通知书、1：安全告知函、2：复工通知书、3：违规整改告知函'")
	private Integer category;
	@Column(columnDefinition = "varchar(64) comment '提交人'")
	private String submitter;
	@Column(columnDefinition = "varchar(256) comment '标题'")
	private String title;
    
	@ManyToOne(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "fileId", referencedColumnName = "id", nullable = true)
	private AssetEntity notificationFile;
	
	@ManyToOne(targetEntity = CommunityEventsEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventId", referencedColumnName = "id", nullable = true)
	private CommunityEventsEntity event;
}
