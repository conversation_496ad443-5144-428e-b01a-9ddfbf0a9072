package com.foshan.entity.community;

import java.sql.Timestamp;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.IEntityBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_warning_message")
@org.hibernate.annotations.Table(appliesTo = "t_warning_message",comment="告警消息") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class WarningMessageEntity implements IEntityBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = 3553609457711484045L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(2000) comment '设备名称'")
	private String deviceName;
	@Column(columnDefinition = "varchar(64) comment '设备ID'")
	private String deviceCode;
	@Column(columnDefinition = "varchar(64) comment '所在分组'")
	private String levelName;
	@Column(columnDefinition = "varchar(64) comment '发生时间'")
	private String alarmTime;
	@Column(columnDefinition = "varchar(64) comment '告警事件'")
	private String eventType;
	@Column(columnDefinition = "varchar(64) comment '告警事件ID'")
	private String eventRecordId;
	@Column(columnDefinition = "varchar(3000) comment '备用'")
	private String jsonData;
	@Column(columnDefinition = "varchar(512) comment '处理说明'")
	private String handleComment;
	@Column(columnDefinition = "int(2) comment '处理状态；0:未处理；1：已处理；'")
	private Integer handleState;
	@Column(columnDefinition = "Timestamp  comment '处理时间'")
	protected Timestamp handleTime;
	
	@ManyToOne(targetEntity = DeviceEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "deviceId", referencedColumnName = "id", nullable = true)
	private DeviceEntity device;
}
