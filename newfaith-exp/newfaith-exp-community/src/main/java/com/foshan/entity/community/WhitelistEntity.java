package com.foshan.entity.community;


import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * 
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_whitelist")
@org.hibernate.annotations.Table(appliesTo = "t_whitelist",comment="白名单")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class WhitelistEntity implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = -2971908864014159832L;
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(columnDefinition = "varchar(64) comment '该段ip描述'")
    private String description;
    @Column(columnDefinition = "varchar(64) comment '结束ip，点分十进制'")
    private String endIP;
    @Column(columnDefinition = "varchar(64) comment '开始ip，点分十进制'")
    private String startIP;
    @Column(columnDefinition = "varchar(64) comment '白名单分组代码'")
    private String groupCode;
    @Column(columnDefinition = "varchar(64) comment '白名单分组id'")
    private Integer groupId;
    @Column(columnDefinition = "varchar(64) comment '白名单分组名'")
    private String groupName;

}
