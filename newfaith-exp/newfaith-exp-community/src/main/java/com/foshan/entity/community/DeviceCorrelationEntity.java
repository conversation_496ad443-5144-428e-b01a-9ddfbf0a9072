package com.foshan.entity.community;

import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AccountEntity;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_device_correlation")
@org.hibernate.annotations.Table(appliesTo = "t_device_correlation",comment="设备关联表") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class DeviceCorrelationEntity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5543463882358501285L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '时间'")
	protected Timestamp createTime;
	@Column(columnDefinition = "varchar(64) comment '区域码（应急广播时需要）'")
	private String regionCode;
	
	@ManyToOne(targetEntity = DeviceEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "deviceId", referencedColumnName = "id", nullable = true)
	private DeviceEntity device;
	
	@ManyToOne(targetEntity = ReceiveMessageTypeEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "receiveMessageTypeId", referencedColumnName = "id", nullable = true)
	private ReceiveMessageTypeEntity receiveMessageType;
	
	@ManyToOne(targetEntity = CommunityMemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "accountId", referencedColumnName = "id", nullable = true)
	private CommunityMemberEntity member;

}
