package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.EntityObject;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_payment_record", uniqueConstraints = {
		@UniqueConstraint(columnNames = "paymentRecordCode") })
@org.hibernate.annotations.Table(appliesTo = "t_community_payment_record", comment = "在线支付发起的付款记录")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityPaymentRecordEntity extends EntityObject {
	/**
	 * 在线付款记录
	 */
	private static final long serialVersionUID = -3285561037070925302L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(48) comment '付款流水编号'")
	private String paymentRecordCode;
	@Column(columnDefinition = "datetime comment '付款日期'")
	private Date paymentDate;
	@Column(columnDefinition = "varchar(64) comment '姓名'")
	private String payerName;
	@Column(columnDefinition = "decimal(10,2) comment '金额'")
	private BigDecimal amount;
	@Column(columnDefinition = "varchar(64) comment '付款方式：1、微信：WX_PUBLIC--公众号支付;WX_NAVITE_QR--微信二维码支付;WX_MOBILE_WEBSITE--手机网站支付;WX_MINIPROGRAM--微信小程序;WX_PAYCODE--微信付款码支付;AL_NAVITE_QR--支付宝二维码支付;AL_PAYCODE--支付宝付款码支付.2、线下：OFFLINE_POS--线下pos机;OFFLINE_CASH--线下现金;OFFLINE_TRANSFER--线下转账'")
	private String paymentMethod;
	@Column(columnDefinition = "text comment '收款详情序列化JSON'")
	private String paymentDetail;
	@Column(columnDefinition = "varchar(512) comment '备注'")
	private String comment;
	@Column(columnDefinition = "int(2) comment '收款类型 （0：应收款；1、退款； 2：预收；）'")
	private Integer paymentType;
	@Column(columnDefinition = "int(2) comment '是否已开发票 0:否；1：是；'")
	private Integer haveInvoice;
	@Column(columnDefinition = "varchar(64) comment '交易银行账号'")
	private String bankAccount;
	@Column(columnDefinition = "varchar(64) comment '支付模块的支付会话流水号'")
	private String paymentSessionSn;
	@Column(columnDefinition = "varchar(64) comment '第三方在线支付系统交易流水号'")
	private String outTradeNo;
	@Column(columnDefinition = "varchar(64) comment '支付人员终端ID，付款人员微信的openId'")
	private String payerIdentify;
	@Column(columnDefinition = "varchar(64) comment '第三方收款系统支付方式标识,只作记录，不需要第三方支付系统收款时，该值为空'")
	private String paymentScene;
	@Column(columnDefinition = "int(2) default 0 comment '0--未发起（初始化），1--已经发起，2--支付成功，3--支付失败'")
	private Integer status;
	@Column(columnDefinition = "varchar(32) comment '收据编号'")
	private String receiptCode;
	
	@ManyToOne(targetEntity = CommunityEstateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "estateId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityEstateEntity estate;

	@ManyToOne(targetEntity = CommunityReceiptEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "receiptId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityReceiptEntity receipt;
}
