package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_meter_price_history")
@org.hibernate.annotations.Table(appliesTo = "t_community_meter_price_history", comment = "表调价历史")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityMeterPriceHistoryEntity extends Community{

	/**
	 * 
	 */
	private static final long serialVersionUID = -8508263918697818332L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
    @Column(columnDefinition = "decimal(10,4) comment '调整单价'")
    private BigDecimal unitPrice;
    @Column(columnDefinition = "datetime comment '调价时间'")
    private Date changeDate;
    @Column(columnDefinition = "int(1) comment '操作类型 0：新增；1：修改；'")
    private Integer operationType;

	@ManyToOne(targetEntity = CommunityMeterAttributesEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "meterAttributesId", referencedColumnName = "id", nullable = true)
	private CommunityMeterAttributesEntity meterAttributes;
}
