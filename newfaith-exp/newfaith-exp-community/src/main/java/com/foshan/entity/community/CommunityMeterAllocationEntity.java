package com.foshan.entity.community;


import java.math.BigDecimal;
import java.util.Date;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_meter_allocation")
@org.hibernate.annotations.Table(appliesTo = "t_community_meter_allocation",comment="公用表分摊总数") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityMeterAllocationEntity extends Community{

	/**
	 * 公用表分摊总数
	 */
	private static final long serialVersionUID = 5574066690882508361L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "datetime comment '分摊开始日期'")
	private Date allocationDate;
	@Column(columnDefinition = "datetime comment '分摊结束日期'")
	private Date allocationEndDate;
	@Column(columnDefinition = "int(4) comment '分摊日期:年'")
	private Integer allocationYear;
	@Column(columnDefinition = "int(2) comment '分摊日期:月'")
	private Integer allocationMonth;
	@Column(columnDefinition = "decimal(10,2) comment '分摊总量'")
	private BigDecimal allocationNum;
	@Column(columnDefinition = "decimal(10,4) comment '分摊总金额'")
	private BigDecimal allocationAmount;
	@Column(columnDefinition = "decimal(14,8) comment '分摊单价'")
	private BigDecimal unitPrice;
	@Column(columnDefinition = "decimal(10,2) comment '额外用量'")
	private BigDecimal additionalAmount;
	@Column(columnDefinition = "int(11) comment '分摊公式模版Id'")
	private Integer templeteId;
	@Column(columnDefinition = "varchar(100) comment '本次分摊动态参数json格式存放'")
	private String dynamicParameter;
	@Column(columnDefinition = "varchar(64) comment '表ID'")
	private String oldId;

		
	@ManyToOne(targetEntity = CommunityMeterEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "meterId", referencedColumnName = "id", nullable = true)
	private CommunityMeterEntity meter;
		
}
