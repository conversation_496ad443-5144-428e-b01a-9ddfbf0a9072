package com.foshan.entity.community;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.ColumnEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Entity
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@DiscriminatorValue("C")
public class CommunityColumnEntity extends ColumnEntity {


    private static final long serialVersionUID = 7764195988849940750L;

//    @ManyToMany(targetEntity = CommunityAssetEntity.class, fetch = FetchType.LAZY)
//    @JoinTable(name = "t_upshelf_column", inverseJoinColumns ={@JoinColumn(name = "assetId", referencedColumnName = "id"),@JoinColumn(name = "type",referencedColumnName = "contentType"),@JoinColumn(name = "orderNumber", referencedColumnName = "assetOrders")}, joinColumns = @JoinColumn(name = "columnId", referencedColumnName = "id") )
//    @JsonIgnore
//    private List<CommunityAssetEntity> assetList = new ArrayList<CommunityAssetEntity>();

    @ManyToMany(targetEntity = CommunityDistrictEntity.class, fetch = FetchType.LAZY)
    @JoinTable(name = "t_column_district",joinColumns = @JoinColumn(name = "columnId",referencedColumnName = "id"),inverseJoinColumns = @JoinColumn(name = "districtId",referencedColumnName = "id"))
    @JsonIgnore
    private List<CommunityDistrictEntity> districtList = new ArrayList<CommunityDistrictEntity>();

}
