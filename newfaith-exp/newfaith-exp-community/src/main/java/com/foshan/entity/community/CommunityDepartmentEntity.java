package com.foshan.entity.community;

import javax.persistence.CascadeType;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.DepartmentEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@DiscriminatorValue("C")
public class CommunityDepartmentEntity extends DepartmentEntity {

	private static final long serialVersionUID = 5801355181336146743L;

	@ManyToOne(targetEntity = CommunityDistrictEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "districtId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityDistrictEntity district;
}
