package com.foshan.entity.community.vo;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
/**
 * 
 * <AUTHOR>
 * 十六区优惠取消后，应收调整数据数据对象
 */
@JsonInclude(Include.NON_EMPTY)
@Data
@Getter
@Setter
public class CancelBenefitDetailVo {
	/*
	 * 补收应收管理费差额总数
	 */
	private BigDecimal totalSubManageAmount;
	/*
	 * 补收应收违约金差额总数
	 */
	private BigDecimal totalSubBreachAmount;
	/**
	 * 补收应收差额明细
	 */
	private Set<CancelBenefitDetailItemVo> items = new TreeSet<CancelBenefitDetailItemVo>(Collections.reverseOrder());
	
	public CancelBenefitDetailVo(BigDecimal totalSubManageAmount,BigDecimal totalSubBreachAmount, List<CancelBenefitDetailItemVo> itemList) {
		super();
		this.totalSubManageAmount = totalSubManageAmount;
		this.totalSubBreachAmount = totalSubBreachAmount;
		items.clear();
		for(CancelBenefitDetailItemVo i : itemList) {
			items.add(i);	
		}
	}
	
	public CancelBenefitDetailVo() {
		super();
	}

	public CancelBenefitDetailVo(BigDecimal totalSubManageAmount, BigDecimal totalSubBreachAmount,
			Set<CancelBenefitDetailItemVo> items) {
		super();
		this.totalSubManageAmount = totalSubManageAmount;
		this.totalSubBreachAmount = totalSubBreachAmount;
		this.items = items;
	}

}
