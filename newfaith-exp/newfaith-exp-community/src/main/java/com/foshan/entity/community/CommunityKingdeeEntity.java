package com.foshan.entity.community;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_kingdee",uniqueConstraints = { @UniqueConstraint(columnNames = { "fileType",
		"startDate","endDate"}) })
@org.hibernate.annotations.Table(appliesTo = "t_community_kingdee", comment = "金蝶入账记录")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityKingdeeEntity extends Community {
	/**
	* 
	*/
	private static final long serialVersionUID = 8355199088562967943L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "date comment '入账文件导入时间'")
	private Date importDate;
	@Column(columnDefinition = "date comment '开始时间'")
	private Date startDate;
	@Column(columnDefinition = "date comment '结束时间'")
	private Date endDate;
	@Column(columnDefinition = "varchar(100) comment '入账文件名称'")
	private String fileName;
	@Column(columnDefinition = "varchar(10) comment '入账文件类型 应收单、收据、基础数据'")
	private String fileType;
	@Column(columnDefinition = "int(1) default 0 comment '入账状态 0-生成，1-成功 2-失败'")
	private Integer importState;
}
