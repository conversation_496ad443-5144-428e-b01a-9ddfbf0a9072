package com.foshan.entity.community;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_emergency_message")
@org.hibernate.annotations.Table(appliesTo = "t_emergency_message",comment="应急广播消息") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class EmergencyMessageEntity implements IEntityBean {


	/**
	 * 
	 */
	private static final long serialVersionUID = -8031779998644884318L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '发布机构名称'")
	private String senderName;
	@Column(columnDefinition = "varchar(64) comment '发布机构编码'")
	private String senderCode;
	@Column(columnDefinition = "varchar(64) comment '消息编码'")
	private String msgCode;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '发送时间'")
	private Timestamp sendTime;
	@Column(columnDefinition = "Timestamp comment '播发开始时间'")
	private Timestamp startTime;
	@Column(columnDefinition = "Timestamp  comment '播发结束时间'")
	private Timestamp endTime;
	@Column(columnDefinition = "int(10) comment '消息类型 1：实际播发2：取消播发"
			+ "3：平台演练播发4：前端演练播发5：终端演练播发'")
	private Integer msgType;
	@Column(columnDefinition = "varchar(64) comment '事件类型编码; 10000:突发事件;11A00:水旱灾害;11B00:气象灾害;11C00:地震灾害;11D00:地质灾害'")
	private String eventType;
	@Column(columnDefinition = "int(10) comment '事件级别 0：未知级别（Unknown）1：1级（特别重大/红色预警/Red）"
			+ "2：3级（重大/橙色预警/ Orange）3：3级（较大/黄色预警/ Yellow）4：4级（一般/蓝色预警/ Blue）'")
	private Integer severity;
	@Column(columnDefinition = "varchar(256) comment '消息标题文本'")
	private String msgTitle;
	@Column(columnDefinition = "varchar(5000) comment '消息内容文本'")
	private String msgDesc;
	@Column(columnDefinition = "varchar(1024) comment '区域编码'")
	private String areaCode;
	@Column(columnDefinition = "int(10) comment '播发状态代码 0：未处理1：等待播发，指未到消息播发时间2：播发中"
			+ "3：播发成功4：播发失败，包括播发全部失败、播发部分失败、未按要求播发等情况5：播发取消'")
	private Integer stateCode;
	@Column(columnDefinition = "varchar(64) comment '播发状态描述'")
	private String stateDesc;
	@Column(columnDefinition = "varchar(64) comment '实际覆盖率'")
	private String coverageRate;
	@Column(columnDefinition = "varchar(32) comment '实际调用资源响应统计 格式为（半角字符逗号）:"
			+ "实际调用应急广播平台数,实际调用应急广播适配器数,实际调用传输覆盖播出设备数,实际调用终端数'")
	private String resBrdStat;
	@Column(columnDefinition = "Timestamp  comment '反馈时间'")
	private Timestamp feedbackTime;
	@Column(columnDefinition = "varchar(256) comment '压缩包文件路径'")
	private String tarFilePath;
	
	@ManyToOne(targetEntity = EmergencyMessageEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentEmergencyMessageId", referencedColumnName = "id", nullable = true)
	private EmergencyMessageEntity parentEmergencyMessage;
	@OneToMany(targetEntity = EmergencyMessageEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentEmergencyMessageId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<EmergencyMessageEntity> subEmergencyMessageList = new ArrayList<EmergencyMessageEntity>();
	@OneToOne(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "assetId", referencedColumnName = "id", nullable = true, foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
	private AssetEntity asset;
}
