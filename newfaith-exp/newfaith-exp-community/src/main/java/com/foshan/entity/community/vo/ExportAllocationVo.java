package com.foshan.entity.community.vo;



import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;


@Data
@Slf4j
@ApiModel(value="总表分摊导出对象(ExportAllocationVo)")
public class ExportAllocationVo {
	@ApiModelProperty(value = "楼盘名称")
	private String districtName="";
	@ApiModelProperty(value = "楼阁")
	private String buildingName="";
	@ApiModelProperty(value = "单元编号")
	private String unitCode="";
	@ApiModelProperty(value = "楼层")
	private String floor="";
	@ApiModelProperty(value = "房号")
	private String roomnumber="";
	@ApiModelProperty(value = "姓名")
	private String username="";
	@ApiModelProperty(value = "收费面积")
	private String chargingarea="";
	@ApiModelProperty(value = "产权车位面积")
	private String propertyParkingArea;
	@ApiModelProperty(value = "产权车位个数")
	private String propertyParkingNum;
	@ApiModelProperty(value = "人防车位面积")
	private String defenceParkingArea;
	@ApiModelProperty(value = "人防车位个数")
	private String defenceParkingNum;
	@ApiModelProperty(value = "入住日期")
	private String recorddate="";
	@ApiModelProperty(value = "费用1")
	private String fee1="";
	@ApiModelProperty(value = "费用2")
	private String fee2="";
	@ApiModelProperty(value = "费用3")
	private String fee3="";
	@ApiModelProperty(value = "费用4")
	private String fee4="";
	@ApiModelProperty(value = "费用5")
	private String fee5="";
	@ApiModelProperty(value = "费用6")
	private String fee6="";
	@ApiModelProperty(value = "费用7")
	private String fee7="";
	@ApiModelProperty(value = "费用8")
	private String fee8="";
	@ApiModelProperty(value = "费用9")
	private String fee9="";
	@ApiModelProperty(value = "费用10")
	private String fee10="";
	@ApiModelProperty(value = "费用11")
	private String fee11="";
	@ApiModelProperty(value = "费用12")
	private String fee12="";
	@ApiModelProperty(value = "费用13")
	private String fee13="";
	@ApiModelProperty(value = "费用14")
	private String fee14="";
	@ApiModelProperty(value = "费用15")
	private String fee15="";
	@ApiModelProperty(value = "合计")
	private String totalFee="";

	/**
	 * @param o
	 * @return
	 */
	public static ExportAllocationVo getExportAllocationVo(Object[] o) {
		ExportAllocationVo vo = null;
		if (null != o) {
			try {
				vo = new ExportAllocationVo();
				vo.setDistrictName(o[0].toString());
				vo.setBuildingName(o[1].toString());
				vo.setUnitCode(o[2].toString());
				vo.setFloor(o[3].toString());
				vo.setRoomnumber(o[4].toString());
				vo.setUsername(o[5].toString());
				vo.setChargingarea(o[6].toString());
				vo.setPropertyParkingArea(o[7].toString());
				vo.setPropertyParkingNum(o[8].toString());
				vo.setDefenceParkingArea(o[9].toString());
				vo.setDefenceParkingNum(o[10].toString());
				vo.setRecorddate(o[11].toString());
				vo.setFee1(o[12].toString());
				vo.setFee2(o[13].toString());
				vo.setFee3(o[14].toString());
				vo.setFee4(o[15].toString());
				vo.setFee5(o[16].toString());
				vo.setFee6(o[17].toString());
				vo.setFee7(o[18].toString());
				vo.setFee8(o[19].toString());
				vo.setFee9(o[20].toString());
				vo.setFee10(o[21].toString());
				vo.setFee11(o[22].toString());
				vo.setFee12(o[23].toString());
				vo.setFee13(o[24].toString());
				vo.setFee14(o[25].toString());
				vo.setFee15(o[26].toString());
				vo.setTotalFee(o[27].toString());
			} catch (Exception ex) {
				log.error(ex.getMessage() + ":ExportAllocationVo(" + o[0].toString() + "" + o[1].toString() + ""
						+ o[2].toString() + ")数据转换失败");
				vo = null;
			}
		}
		return vo;
	}
}
