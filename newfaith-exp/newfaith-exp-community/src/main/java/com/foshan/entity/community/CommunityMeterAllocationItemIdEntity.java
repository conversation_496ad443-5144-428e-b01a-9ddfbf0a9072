package com.foshan.entity.community;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Embeddable;

import lombok.Data;

@Data
@Embeddable
public class CommunityMeterAllocationItemIdEntity implements Serializable{
	
	

	/**
	 * 
	 */
	private static final long serialVersionUID = 7930756822112704946L;
	@Column(columnDefinition = "int(11) comment '总分摊Id'")
	private Integer allocationId;
    @Column(columnDefinition = "varchar(32) comment '单元编号'")
	private String unitCode;
	@Column(columnDefinition = "int(4) comment '开始分摊-年'")
	private Integer allocationStartYear;
	@Column(columnDefinition = "int(2) comment '开始分摊-月'")
	private Integer allocationStartMonth;
	@Column(columnDefinition = "int(4) comment '结束分摊-年'")
	private Integer allocationEndYear;
	@Column(columnDefinition = "int(2) comment '结束分摊-月'")
	private Integer allocationEndMonth;
	
	public CommunityMeterAllocationItemIdEntity() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	public CommunityMeterAllocationItemIdEntity(Integer allocationId, String unitCode, Integer allocationStartYear,
			Integer allocationStartMonth, Integer allocationEndYear, Integer allocationEndMonth) {
		super();
		this.allocationId = allocationId;
		this.unitCode = unitCode;
		this.allocationStartYear = allocationStartYear;
		this.allocationStartMonth = allocationStartMonth;
		this.allocationEndYear = allocationEndYear;
		this.allocationEndMonth = allocationEndMonth;
	}




	
	
}
