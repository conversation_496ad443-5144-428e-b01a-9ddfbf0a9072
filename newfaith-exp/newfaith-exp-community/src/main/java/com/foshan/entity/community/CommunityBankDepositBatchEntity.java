package com.foshan.entity.community;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_bank_deposit_batch")
@org.hibernate.annotations.Table(appliesTo = "t_community_bank_deposit_batch",comment="银行划账批次") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityBankDepositBatchEntity extends Community {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1201147159654166787L;
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
	@Column(columnDefinition = "datetime comment '起始日期'")
	protected Date startTime;
	@Column(columnDefinition = "datetime comment '截止日期'")
	private Date endTime;
	@Column(columnDefinition = "datetime comment '划入日期'")
	private Date depositDate;
	@Column(columnDefinition = "varchar(512) comment '业主银行（筛选条件）'")
	private String ownerBank;
	@Column(columnDefinition = "varchar(64) comment '入账银行（引入到应收款中的经办人）'")
	private String creditedBank;
    @Column(columnDefinition = "int(2) comment '0：工商银行；1：农业银行；2：农村信用社；3：建设银行；4：全国平台；'")
    private Integer templateType;
    @Column(columnDefinition = "int(2) comment '批次状态 0 未记账，1已记账'")
    private Integer batchState;
	@Column(columnDefinition = "varchar(512) comment '收费项目(多个用逗号隔开 例如：别墅管理费,车库公共电费,车位管理费,车位管理费违约金)'")
	private String payItems;
	@Column(columnDefinition = "varchar(512) comment '备注'")
	private String comment;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '创建时间'")
	protected Timestamp createTime;
	
	@OneToMany(targetEntity = CommunityBankDepositRecordEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "bankDepositBatchId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityBankDepositRecordEntity> bankDepositRecordList = new ArrayList<CommunityBankDepositRecordEntity>();
	
	@ManyToMany(targetEntity = CommunityPayItemsEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_bankdeposit_batch_payitems", joinColumns = @JoinColumn(name = "bankDepositBatchId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "payItemsId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityPayItemsEntity> payItemsList = new ArrayList<CommunityPayItemsEntity>();

}
