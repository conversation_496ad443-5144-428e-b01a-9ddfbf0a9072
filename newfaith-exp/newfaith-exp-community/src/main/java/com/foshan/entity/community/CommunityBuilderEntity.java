package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.RegionEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_builder")
@org.hibernate.annotations.Table(appliesTo = "t_community_builder",comment="开发商") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityBuilderEntity extends Community {
	/**
	 * 开发商
	 */
	private static final long serialVersionUID = -4419466550732891686L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '名称'")
	private String builderName;
	@Column(columnDefinition = "varchar(64) comment '地址'")
	private String builderAddress;
	@Column(columnDefinition = "varchar(64) comment '法人代表'")
	private String legalRepresentative;
	
	@ManyToOne(targetEntity = RegionEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "regionId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private RegionEntity region;
	@ManyToMany(targetEntity = CommunityDistrictEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_builder_district", joinColumns = @JoinColumn(name = "builderId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "districtId", referencedColumnName = "id"))
	private List<CommunityDistrictEntity> districtList = new ArrayList<CommunityDistrictEntity>();

}
