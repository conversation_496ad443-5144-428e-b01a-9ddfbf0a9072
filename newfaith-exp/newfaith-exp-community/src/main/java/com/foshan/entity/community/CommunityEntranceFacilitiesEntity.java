package com.foshan.entity.community;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("ENTRANCE")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityEntranceFacilitiesEntity extends CommunityFacilitiesEntity{
	/**
	 * 门禁设施
	 */
	private static final long serialVersionUID = -7951462266540050096L;
	@Column(columnDefinition = "varchar(64) comment '位置'")
	private String location;
	@Column(columnDefinition = "varchar(64) comment '内网IP'")
	private String networkIP;
	@Column(columnDefinition = "int(2) comment ''")
	private Integer types;
}
