package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_receipt_receivables")
@org.hibernate.annotations.Table(appliesTo = "t_community_receipt_receivables",comment="实收款")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityReceiptReceivablesEntity implements IEntityBean {
	/**
	 * 实收款
	 */
	private static final long serialVersionUID = -2592739768472981418L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
    @Column(columnDefinition = "decimal(10,2) comment '已收金额'")
    private BigDecimal receivedAmount;
    @Column(columnDefinition = "decimal(10,2) comment '本次收款'")
    private BigDecimal currentAmount;
	@Column(columnDefinition = "int(1) default 0 comment '0未开，1已开，2正在开'")
	private Integer invoiceState;

	@ManyToOne(targetEntity = CommunityReceivablesEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "receivablesId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityReceivablesEntity receivables;
	
	@ManyToOne(targetEntity = CommunityReceiptEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "receiptId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityReceiptEntity receipt;
	
	@ManyToMany(targetEntity = CommunityInvoiceEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_receipt_receivables_invoice", joinColumns = @JoinColumn(name = "receiptReceivablesId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "invoiceId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityInvoiceEntity> invoiceList = new ArrayList<CommunityInvoiceEntity>();
}
