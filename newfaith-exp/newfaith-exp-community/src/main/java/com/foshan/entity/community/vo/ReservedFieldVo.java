package com.foshan.entity.community.vo;

import java.util.LinkedList;
import java.util.List;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@ToString
public class ReservedFieldVo {
	//产权停车位面积
	private String propertyParkingArea = "0";
	//产权停车位个数
	private String propertyParkingNum="0";
	//人防停车位面积
	private String defenceParkingArea = "0";
	//人防停车位个数
	private String defenceParkingNum="0";
	//水表底数
	private String waterMeterBase = "0";
	//电表底数
	private String electricMeterBase = "0";
	//银行划扣全国平台客户编号
	private String accountCustomerId="";
	//银行划扣全国平台协议号
	private String accountContractNo="";
	//协议时间
	private String accountContractTime="";
	//存量协议
	private String accountContractComment="";
	//应缴费是否需要短信提醒 0：否；1是
	private Integer isSendMessage=1;
	//应收未收措施
	private List<String> noReceivablesMeasures=new LinkedList<>();
	//应收未收进展
	private List<String> measures1=new LinkedList<>();
	//应收未收进展2
	private List<String> measures2=new LinkedList<>();
		
}
