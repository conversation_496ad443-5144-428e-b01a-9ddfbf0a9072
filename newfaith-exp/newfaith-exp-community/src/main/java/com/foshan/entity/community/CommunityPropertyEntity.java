package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_property")
@org.hibernate.annotations.Table(appliesTo = "t_community_property",comment="资产管理")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@DiscriminatorColumn(name = "propertyType", discriminatorType = DiscriminatorType.STRING)
@DiscriminatorValue("PROPERTY")
public class CommunityPropertyEntity extends Community {
	/**
	 *  资产管理
	 */
	private static final long serialVersionUID = -6833957541379743826L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	protected Integer id;
	@Column(columnDefinition = "varchar(512) comment '名称'")
	protected String propertyName;
	@Column(columnDefinition = "varchar(512) comment '备注'")
	protected String comment;
	@Column(columnDefinition = "varchar(64) comment '表ID'")
	protected String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	protected String oldData;
    @Column(columnDefinition = "int(2) comment '租用状态，0-不可租用；1-可租用（未出租）；2-已出租；'")
    protected Integer rentState;

	@ManyToMany(targetEntity = CommunityUserEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_property_user", joinColumns = @JoinColumn(name = "propertyId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "userId", referencedColumnName = "id"))
	private List<CommunityUserEntity> userList = new ArrayList<CommunityUserEntity>();
	
	@OneToMany(targetEntity = CommunityEventsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "propertyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityEventsEntity> eventsList = new ArrayList<CommunityEventsEntity>();
	
	@OneToMany(targetEntity = CommunityMemberPropertyEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "propertyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMemberPropertyEntity> memberPropertyList = new ArrayList<CommunityMemberPropertyEntity>();
	
	@OneToMany(targetEntity = CommunityMemberPropertyEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentPropertyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMemberPropertyEntity> subMemberPropertyList = new ArrayList<CommunityMemberPropertyEntity>();
	
	@ManyToOne(targetEntity = CommunityBuildingEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "buildingId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityBuildingEntity building;
	
	@ManyToOne(targetEntity = AssetEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "assetId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private AssetEntity asset;
	
	@ManyToMany(targetEntity = CommunityPayItemsEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_property_pay_items", joinColumns = @JoinColumn(name = "propertyId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "payItemsId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityPayItemsEntity> payItemsList = new ArrayList<CommunityPayItemsEntity>();
	
	@ManyToOne(targetEntity = CommunityPaymentAccountEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "paymentAccountId", referencedColumnName = "id", nullable = true)
	private CommunityPaymentAccountEntity paymentAccount;
	
	@ManyToOne(targetEntity = CommunityPropertyEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentPropertyId", referencedColumnName = "id", nullable = true)
	protected CommunityPropertyEntity parentProperty;
	/*
	@OneToMany(targetEntity = CommunityPropertyEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentPropertyId", referencedColumnName = "id", nullable = true, foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
	@JsonIgnore
	private List<CommunityPropertyEntity> subPropertyList = new ArrayList<CommunityPropertyEntity>();*/
	
	@ManyToMany(targetEntity = CommunityMeterEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_meter_property", joinColumns = @JoinColumn(name = "propertyId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "meterId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityMeterEntity> meterList = new ArrayList<CommunityMeterEntity>();
	
	@OneToMany(targetEntity = CommunityBankDepositRecordEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "propertyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityBankDepositRecordEntity> bankDepositRecordList = new ArrayList<CommunityBankDepositRecordEntity>();
	
	@OneToMany(targetEntity = CommunityMeasuresEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "propertyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMeasuresEntity> measuresList = new ArrayList<CommunityMeasuresEntity>();
	
	@ManyToMany(targetEntity = CommunityDecorationItemsEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_decoration_items_property", joinColumns = @JoinColumn(name = "propertyId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "decorationItemsId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityDecorationItemsEntity> decorationItemsList = new ArrayList<CommunityDecorationItemsEntity>();
	
//	@ManyToMany(targetEntity = CommunityPayItemsEntity.class, fetch = FetchType.LAZY)
//	@JoinTable(name = "t_community_bankDeposit_estate_payItems", joinColumns = @JoinColumn(name = "propertyId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "payItemsId", referencedColumnName = "id"))
//	@JsonIgnore
//	private List<CommunityPayItemsEntity> bankDepositPayItemsList = new ArrayList<CommunityPayItemsEntity>();
	
//	@OneToMany(targetEntity = CommunityBankDepositEstatePayItemsEntity.class, fetch = FetchType.LAZY)
//	@JoinColumn(name = "propertyId", referencedColumnName = "id", nullable = true)
//	@JsonIgnore
//	private List<CommunityBankDepositEstatePayItemsEntity> bankDepositEstatePayItemsList = new ArrayList<>();
}
