package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_reservat_period")
@org.hibernate.annotations.Table(appliesTo = "t_community_reservat_period",comment="预约时间段")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityReservatPeriodEntity implements IEntityBean {

	/**
	 * 预约时间段
	 */
	private static final long serialVersionUID = 5593935723065998533L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "int(10) comment '剩余人数'")
	protected Integer overplusNum;
	@Column(columnDefinition = "int(10) comment '最多可预约人数'")
	protected Integer maxNum;
	@Column(columnDefinition = "varchar(64) comment '开始时间'")
	private String startTime;
	@Column(columnDefinition = "varchar(64) comment '结束时间'")
	private String endTime;
	@Column(columnDefinition = "int(1) default 1 comment '状态 0--无效数据  1--有效数据'")
	protected Integer state;
	@Column(columnDefinition = "int(11) comment '预设时间段ID'")
	protected Integer presetPeriodId;
	@Column(columnDefinition = "int(10) comment '排序值'")
	protected Integer orders;
	
	@OneToMany(targetEntity = CommunityReservationRecordEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "periodId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReservationRecordEntity> reservationRecordList = new ArrayList<CommunityReservationRecordEntity>();
	
	@ManyToOne(targetEntity = CommunityReservationDateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "dateId", referencedColumnName = "id", nullable = true)
	private CommunityReservationDateEntity reservationDate;

}
