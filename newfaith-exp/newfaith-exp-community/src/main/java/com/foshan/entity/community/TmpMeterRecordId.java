package com.foshan.entity.community;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Embeddable;

import lombok.Data;
@Data
@Embeddable
public class TmpMeterRecordId implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -1760914184795304849L;
	@Column(columnDefinition = "varchar(64) comment '额外用量说明'")
	private Integer meterId;
	@Column(columnDefinition = "int(4) comment '开始分摊年'")
    private Integer allocationStartYear;
	@Column(columnDefinition = "int(2) comment '开始分摊月'")
    private Integer allocationStartMonth;
	@Column(columnDefinition = "int(4) comment '结束分摊年'")
    private Integer allocationEndYear;
	@Column(columnDefinition = "int(2) comment '结束分摊月'")
    private Integer allocationEndMonth;
}
