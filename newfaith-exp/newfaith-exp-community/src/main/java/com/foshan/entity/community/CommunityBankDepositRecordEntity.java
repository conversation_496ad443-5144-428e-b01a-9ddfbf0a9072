package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

	
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_bank_deposit_record")
@org.hibernate.annotations.Table(appliesTo = "t_community_bank_deposit_record",comment="银行划账记录") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityBankDepositRecordEntity extends Community {
	/**
	 * 
	 */
	private static final long serialVersionUID = 6548681151148188733L;
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
	@Column(columnDefinition = "varchar(64) comment '银行帐户'")
	protected String bankAccount;
	@Column(columnDefinition = "decimal(10,2) comment '应收未收金额'")
	private BigDecimal outstandingAmount;
	@Column(columnDefinition = "decimal(10,2) comment '应划金额'")
	private BigDecimal depositAmount;
	@Column(columnDefinition = "int(2) comment '划帐标志（0-未回盘，1-回盘，划账成功，2-回盘，划账失败）'")
	private Integer isDeposited;
	@Column(columnDefinition = "varchar(512) comment '回盘备注（一般记录划账失败原因）'")
	private String feedbackComment;
	@Column(columnDefinition = "varchar(512) comment '划扣备注（导出送盘文件的附言，一般为单元的编号）'")
	private String depositComment;
	
	@OneToMany(targetEntity = CommunityReceivablesEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "bankDepositRecordId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReceivablesEntity> receivablesList = new ArrayList<CommunityReceivablesEntity>();
	
	@ManyToOne(targetEntity = CommunityBankDepositBatchEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "bankDepositBatchId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityBankDepositBatchEntity bankDepositBatch;
	
	@ManyToOne(targetEntity = CommunityEstateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "propertyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityEstateEntity estate;
	
	@OneToOne(targetEntity = CommunityReceiptEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "receiptId", referencedColumnName = "id", nullable = true)
	private CommunityReceiptEntity receipt;
}
