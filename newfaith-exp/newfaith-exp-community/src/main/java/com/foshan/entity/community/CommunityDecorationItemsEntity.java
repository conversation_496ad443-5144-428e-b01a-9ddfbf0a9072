package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_decoration_items")
@org.hibernate.annotations.Table(appliesTo = "t_community_decoration_items",comment="装修项目项目") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityDecorationItemsEntity extends Community {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7909929974441968986L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(254) comment '项目名称'")
	private String itemName;
	@Column(columnDefinition = "int(10) comment '排序'")
	private Integer orders;
	@Column(columnDefinition = "int(10) comment '使用状态 0--未使用 1--已使用（业主可以申请）'")
	private Integer beginUse;
	@Column(columnDefinition = "varchar(512) comment '参数'")
	private String parameter;
	
//	@ManyToMany(targetEntity = CommunityPropertyEntity.class, fetch = FetchType.LAZY)
//	@JoinTable(name = "t_community_decoration_items_property", joinColumns = @JoinColumn(name = "decorationItemsId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "propertyId", referencedColumnName = "id"))
//	@JsonIgnore
//	private List<CommunityPropertyEntity> propertyList = new ArrayList<CommunityPropertyEntity>();
	
//	@ManyToMany(targetEntity = CommunityDecorationAttachmentEntity.class, fetch = FetchType.LAZY)
//	@JoinTable(name = "t_community_decoration_items_attachment", joinColumns = @JoinColumn(name = "decorationItemsId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "attachmentId", referencedColumnName = "id"))
//	@JsonIgnore
//	private List<CommunityDecorationAttachmentEntity> attachmentList = new ArrayList<CommunityDecorationAttachmentEntity>();
	
	@OneToMany(targetEntity = CommunityCecorationItemsAttachmentEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "decorationItemsId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityCecorationItemsAttachmentEntity> itemsAttachmentList = new ArrayList<CommunityCecorationItemsAttachmentEntity>();
	
	@ManyToMany(targetEntity = CommunityEventCategoryItemsEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_decoration_items_event_items", joinColumns = @JoinColumn(name = "decorationItemsId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "eventItemsId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityEventCategoryItemsEntity> eventCategoryItemsList = new ArrayList<CommunityEventCategoryItemsEntity>();
	
	@ManyToOne(targetEntity = CommunityEventCategoryEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventCategoryId", referencedColumnName = "id", nullable = true)
	private CommunityEventCategoryEntity eventCategory;
}
