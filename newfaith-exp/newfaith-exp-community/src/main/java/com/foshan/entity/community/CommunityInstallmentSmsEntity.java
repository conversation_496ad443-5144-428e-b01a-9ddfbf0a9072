package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.UserEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_installment_sms")
@org.hibernate.annotations.Table(appliesTo = "t_community_installment_sms",comment="账单分期短信") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityInstallmentSmsEntity extends Community{
	/**
	 * 
	 */
	private static final long serialVersionUID = -5860174593989745246L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '账单分期名称'")
	private String installmentName;
    @Column(columnDefinition = "int(5) comment '期数'")
    private Integer periods;
    @Column(columnDefinition = "decimal(10,2) comment '金额'")
    private BigDecimal amount;
    @Column(columnDefinition = "datetime comment '发送日期'")
    private Date sendDate;
    @Column(columnDefinition = "int(1) comment '发送状态，0：否；1：已发；'")
    private Integer sendStatus;
    @Column(columnDefinition = "int(1) comment '支付状态，0：未支付；1：已支付；'")
    private Integer payStatus;
    @Column(columnDefinition = "int(1) comment '使用状态，0：未使用；1：已使用；'")
    private Integer useStatus;
    
	@ManyToOne(targetEntity = CommunityInstallmentSmsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentId", referencedColumnName = "id", nullable = true)
	private CommunityInstallmentSmsEntity parentInstallmentSms;
	
	@OneToMany(targetEntity = CommunityInstallmentSmsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentId", referencedColumnName = "id", nullable = true, foreignKey = @ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
	@JsonIgnore
	private List<CommunityInstallmentSmsEntity> subInstallmentSmsList = new ArrayList<CommunityInstallmentSmsEntity>();
	
	@ManyToOne(targetEntity = CommunityEstateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "estateId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityEstateEntity estate;
	
	@ManyToMany(targetEntity = CommunityUserEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_installment_sms_user", joinColumns = @JoinColumn(name = "installmentSmsId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "userId", referencedColumnName = "id"))
	private List<UserEntity> userList = new ArrayList<UserEntity>();
}
