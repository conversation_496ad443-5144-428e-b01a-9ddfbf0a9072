package com.foshan.entity.community;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

	
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_contract")
@org.hibernate.annotations.Table(appliesTo = "t_community_contract",comment="租赁合同") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityContractEntity extends Community {
	/**
	 * 
	 */
	private static final long serialVersionUID = -1902732862902572002L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '合同编号'")
	private String contractCode;
	@Column(columnDefinition = "varchar(128) comment '合同名称'")
	private String contractName;
	@Column(columnDefinition = "varchar(512) comment '租金说明'")
	private String rentInfo;
	@Column(columnDefinition = "varchar(2000) comment '合同简要说明'")
	private String contractBrief;
	@Column(columnDefinition = "varchar(512) comment '保证金说明'")
	private String depositInfo;
	@Column(columnDefinition = "varchar(32) comment '经营类别'")
	private String businessCategory;
	@Column(columnDefinition = "varchar(64) comment '租金付款方式：月付/季度付/半年付/年付'")
	private String rentPayType;
	@Column(columnDefinition = "varchar(64) comment '购方税号'")
	private String paytaxNo;
	@Column(columnDefinition = "varchar(64) comment '购方名称'")
	private String buyersName;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '合同开始时间'")
	private Timestamp startTime;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '合同结束日期'")
	private Timestamp endTime;
	@Column(columnDefinition = "varchar(64) comment '开票说明'")
	private String invoiceDescription;
    @Column(columnDefinition = "int(2) comment ' 是否当前有效合同 0-否，1是'")
    private Integer isEffective;
	
	
	@OneToMany(targetEntity = CommunityMemberPropertyEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "contractId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMemberPropertyEntity> memberPropertyList = new ArrayList<CommunityMemberPropertyEntity>();
	
	@OneToMany(targetEntity = CommunityReceivablesEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "receivablesId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReceivablesEntity> receivablesList = new ArrayList<CommunityReceivablesEntity>();
	
	@ManyToOne(targetEntity = AssetEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "assetId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private AssetEntity asset;
	
	@ManyToOne(targetEntity = CommunityMemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	private CommunityMemberEntity member;
	
//	@ManyToMany(targetEntity = CommunityPayItemsPriceEntity.class, fetch = FetchType.LAZY)
//	@JoinTable(name = "t_community_payItems_price_contract", joinColumns = @JoinColumn(name = "contractId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "itemsPriceId", referencedColumnName = "id"))
//	@JsonIgnore
//	private List<CommunityPayItemsPriceEntity> payItemsPriceList = new ArrayList<CommunityPayItemsPriceEntity>();
	
	
}
