package com.foshan.entity.community.vo;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
/**
 * 
 * <AUTHOR>
 * 十六区优惠取消后，应收调整数据数据对象
 */
@JsonInclude(Include.NON_EMPTY)
@Data
@Getter
@Setter
public class CalBreachAmountChangeAmountHistoryResultVo {
	/*
	 * 补收应收管理费差额总数
	 */
	private Date displayDate;
	/*
	 * 补收应收违约金差额总数
	 */
	private BigDecimal totalBreachAmount;
	
	
	@Override
	public String toString() {
		return "CalBreachAmountChangeAmountHistoryResultVo [displayDate=" + displayDate + ", totalBreachAmount="
				+ totalBreachAmount + "]";
	}

	public CalBreachAmountChangeAmountHistoryResultVo(Date displayDate, BigDecimal totalBreachAmount) {
		super();
		this.displayDate = displayDate;
		this.totalBreachAmount = totalBreachAmount;
	}
	
	public CalBreachAmountChangeAmountHistoryResultVo() {
		super();
	}
}
