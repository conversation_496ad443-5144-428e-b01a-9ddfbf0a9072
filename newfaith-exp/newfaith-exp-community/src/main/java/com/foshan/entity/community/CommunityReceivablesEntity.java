package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.entity.EntityObject;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_receivables", uniqueConstraints = { @UniqueConstraint(columnNames = { "payItemsName",
		"receivableDate","receivableAmount","estateId","subEstateId" }) }, indexes = {@Index(name = "idx_estate_receivable", columnList = "estateId,receivableDate,payItemsName,receivableAmount"),@Index(name="idex_breach_receivable",columnList="breachReceivablesId")})
@org.hibernate.annotations.Table(appliesTo = "t_community_receivables", comment = "应收款")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@JsonInclude(Include.NON_NULL)
public class CommunityReceivablesEntity extends EntityObject {
	/**
	 * 应收款
	 */
	private static final long serialVersionUID = 2168095812601014819L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '收费项目'")
	private String payItemsName;
	@Column(columnDefinition = "varchar(64) comment '项目类别'")
	private String chargeCategory;
	@Column(columnDefinition = "varchar(64) comment '项目来源'")
	private String chargeSource;
	@Column(columnDefinition = "varchar(512) comment '来源备注'")
	private String sourceNotes;
	@Column(columnDefinition = "datetime comment '应收日期'")
	private Date receivableDate;
	@Column(columnDefinition = "decimal(10,2) comment '应收金额'")
	private BigDecimal receivableAmount;
	@Column(columnDefinition = "decimal(10,2) default 0 comment '已收金额'")
	private BigDecimal receivedAmount;
	@Column(columnDefinition = "varchar(200) comment '备注'")
	private String comment;
	@Column(columnDefinition = "datetime comment '起始日期（分摊金额要写上）'")
	private Date startTime;
	@Column(columnDefinition = "datetime comment '截止日期'")
	private Date endTime;
	@Column(columnDefinition = "datetime comment '应收属期'")
	private Date paymentPeriod;
	@Column(columnDefinition = "int(11) comment '数量'")
	private Integer quantity;
	@Column(columnDefinition = "int(2) default 0 comment '银行划账锁定标志  0:未上锁;1:系统锁定;2:人工锁定；'")
	private Integer lockMark;
	@Column(columnDefinition = "int(11) comment '子房产Id'")
	private Integer subEstateId;
	@Column(columnDefinition = "varchar(64) comment '表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;
	@Column(columnDefinition = "int(11) comment '生成违约金对应收id'")
	private Integer breachReceivablesId;
	@Column(columnDefinition = "varchar(64) comment '应收编号'")
	private String receivablesNO;
	@Column(columnDefinition = "varchar(512) comment '管理费取消优惠补收差额'")
	private String benefitValue;

	@ManyToOne(targetEntity = CommunityEstateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "estateId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityEstateEntity estate;

	@ManyToOne(targetEntity = CommunityPayItemsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "payItemId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityPayItemsEntity payItem;

	@OneToMany(targetEntity = CommunityReceivablesChangesEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "receivablesId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReceivablesChangesEntity> receivablesChangesList = new ArrayList<CommunityReceivablesChangesEntity>();

	@OneToMany(targetEntity = CommunityReceiptReceivablesEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "receivablesId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReceiptReceivablesEntity> receiptReceivablesList = new ArrayList<>();

	@ManyToOne(targetEntity = CommunityBankDepositRecordEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "bankDepositRecordId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityBankDepositRecordEntity bankDepositRecord;
	
	@ManyToOne(targetEntity = CommunityContractEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "contractId", referencedColumnName = "id", nullable = true)
	private CommunityContractEntity contract;
	
	@ManyToMany(targetEntity = CommunityEventsEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_event_receivables", joinColumns = @JoinColumn(name = "receivablesId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "eventId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityEventsEntity> eventsList = new ArrayList<CommunityEventsEntity>();

	@Override
	public int hashCode() {
		return Objects.hash(chargeCategory, id, payItemsName, receivableDate, subEstateId, estate);
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CommunityReceivablesEntity other = (CommunityReceivablesEntity) obj;
		return Objects.equals(chargeCategory, other.chargeCategory) && Objects.equals(id, other.id)
				&& Objects.equals(payItemsName, other.payItemsName)
				&& Objects.equals(receivableDate, other.receivableDate)
				&& Objects.equals(subEstateId, other.subEstateId)
				&& Objects.equals(estate, other.estate);
	}


	@Override
	public String toString() {
		return "CommunityReceivablesEntity [id=" + id + ", payItemsName=" + payItemsName + ", chargeCategory="
				+ chargeCategory + ", chargeSource=" + chargeSource + ", sourceNotes=" + sourceNotes
				+ ", receivableDate=" + receivableDate + ", receivableAmount=" + receivableAmount + ", receivedAmount="
				+ receivedAmount + ", comment=" + comment + ", startTime=" + startTime + ", endTime=" + endTime
				+ ", paymentPeriod=" + paymentPeriod + ", quantity=" + quantity + ", lockMark=" + lockMark
				+ ", subEstateId=" + subEstateId + ", oldId=" + oldId + ", oldData=" + oldData
				+ ", breachReceivablesId=" + breachReceivablesId + ", estate=" + estate + ", payItem=" + payItem
				+ ", receivablesChangesList=" + receivablesChangesList + ", receiptReceivablesList="
				+ receiptReceivablesList + ", bankDepositRecord=" + bankDepositRecord + "]";
	}
	
	

}
