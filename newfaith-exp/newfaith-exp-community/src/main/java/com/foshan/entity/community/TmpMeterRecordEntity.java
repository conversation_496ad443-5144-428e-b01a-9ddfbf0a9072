package com.foshan.entity.community;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "tmp_meter_record")
@org.hibernate.annotations.Table(appliesTo = "tmp_meter_record", comment = "抄表记录视图临时表")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class TmpMeterRecordEntity implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3958916904968118678L;
	@EmbeddedId
	@AttributeOverrides({ @AttributeOverride(name = "meterId", column = @Column),
			@AttributeOverride(name = "allocationStartYear", column = @Column),
			@AttributeOverride(name = "allocationStartMonth", column = @Column),
			@AttributeOverride(name = "allocationEndYear", column = @Column),
			@AttributeOverride(name = "allocationEndMonth", column = @Column) })
	private TmpMeterRecordId id;
	@Column(columnDefinition = "varchar(64) comment '表编号'")
	private String meterCode;
	@Column(columnDefinition = "varchar(64) comment '表名称'")
	private String meterName;
	@Column(columnDefinition = "int(2) comment '分摊类别：1按面积分摊，2自定义分摊'")
	private Integer allocationMethod;
	@Column(columnDefinition = "varchar(64) comment '收费项目'")
	private String payItemsName;
	@Column(columnDefinition = "decimal(10,2) comment '额外用量'")
	private BigDecimal additionalAmount;
	@Column(columnDefinition = "varchar(64) comment '额外用量说明'")
	private String additionalInstructions;
	@Column(columnDefinition = "datetime comment '表坏日期'")
	private Date expirationDates;
	@Column(columnDefinition = "int(11) comment '表属性Id'")
	private Integer meterAttributesId;
	@Column(columnDefinition = "int(11) comment '表公式Id'")
	private Integer formulaId;
	@Column(columnDefinition = "int(11) comment '表公式模版Id'")
	private Integer templeteId;
	@Column(columnDefinition = "decimal(10,2) comment '倍率'")
	private BigDecimal rate;
	@Column(columnDefinition = "decimal(14,8) comment '单价'")
	private BigDecimal unitPrice;
	@Column(columnDefinition = "varchar(100) comment '动态参数'")
	private String dynamicParameter;
	@Column(columnDefinition = "varchar(64) comment '公式名称'")
	private String formulaName;
	@Column(columnDefinition = "varchar(64) comment '模版公式内容'")
	private String templeteInfo;
	@Column(columnDefinition = "varchar(64) comment '模版公式名称'")
	private String templeteName;
	@Column(columnDefinition = "decimal(10,2) comment '上次读数'")
	private BigDecimal lastNum;
	@Column(columnDefinition = "decimal(10,2) comment '本次读数'")
	private BigDecimal recordNum;
	@Column(columnDefinition = "int(8) comment '单元总数'")
	private Integer estateCount;
	@Column(columnDefinition = "decimal(10,2) comment '总计费面积'")
	private BigDecimal totalChargeArea;
	@Column(columnDefinition = "decimal(10,2) comment '总附加面积'")
	private BigDecimal totalAddArea;
	@Column(columnDefinition = "decimal(10,2) comment '分摊数量'")
	private BigDecimal allocationNum;
	@Column(columnDefinition = "decimal(14,4) comment '分摊金额'")
	private BigDecimal allocationAmount;
	@Column(columnDefinition = "int(11) comment '分摊id'")
	private Integer allocationId;
	@Column(columnDefinition = "int(2) comment '分摊周期 单位:月'")
	private Integer allocationPeriod;

}
