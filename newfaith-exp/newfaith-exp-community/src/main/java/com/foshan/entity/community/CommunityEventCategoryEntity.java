package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_event_category")
@org.hibernate.annotations.Table(appliesTo = "t_community_event_category",comment="事件类型") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityEventCategoryEntity extends Community {

	private static final long serialVersionUID = 8487008155728342198L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '类型名称'")
	private String categoryName;
	@Column(columnDefinition = "int(10) comment '1一级，2-二级'")
	private Integer categoryLevel;
	@Column(columnDefinition = "int(2) comment '是否派单:0否1是'")
	private Integer isDispatching;
	@ApiModelProperty(value = "排序",example="1")
	private Integer orders;
	@Column(columnDefinition = "varchar(256) comment '图标（小程序端显示的图标）'")
	private String icon;
	@Column(columnDefinition = "int(2) comment '类型，1:装修申请;2:巡查项目；'")
	private Integer categoryType;
	@Column(columnDefinition = "int(2)  default 0  comment '是发送云之家:0否 1是'")
	private Integer isSend;
	@Column(columnDefinition = "varchar(512) comment '默认参数（云之家）'")
	private String defaultParameter;

	
	@ManyToOne(targetEntity = CommunityEventCategoryEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentId", referencedColumnName = "id", nullable = true)
	private CommunityEventCategoryEntity parentEventCategory;
	
	@OneToMany(targetEntity = CommunityEventCategoryEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityEventCategoryEntity> subEventCategoryList = new ArrayList<CommunityEventCategoryEntity>();
	
	@OneToMany(targetEntity = CommunityEventsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventCategoryId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityEventsEntity> communityEventsList = new ArrayList<CommunityEventsEntity>();
	
	@OneToMany(targetEntity = CommunityEventCategoryItemsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventCategoryId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityEventCategoryItemsEntity> eventCategoryItemsList = new ArrayList<CommunityEventCategoryItemsEntity>();
	
	@OneToMany(targetEntity = CommunityDecorationItemsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventCategoryId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityDecorationItemsEntity> decorationItemsList = new ArrayList<CommunityDecorationItemsEntity>();
	
}
