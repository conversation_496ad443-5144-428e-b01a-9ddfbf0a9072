package com.foshan.entity.community;

import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("CARD")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityAccessCardEntity extends CommunityEntranceFacilitiesEntity{
	/**
	 * 门禁卡
	 */
	private static final long serialVersionUID = -6208154612013708894L;
	@Column(columnDefinition = "int(2) comment '门禁卡类型(业主、临时施工、一次性、访客、物品放行)'")
	private Integer cardType;
	@Column(columnDefinition = "Timestamp  comment '开始时间'")
	protected Timestamp  startTime;
	@Column(columnDefinition = "Timestamp  comment '结束时间'")
	protected Timestamp  endTime;
}
