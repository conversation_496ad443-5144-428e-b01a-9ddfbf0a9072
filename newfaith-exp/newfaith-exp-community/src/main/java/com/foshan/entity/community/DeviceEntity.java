package com.foshan.entity.community;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_device")
@org.hibernate.annotations.Table(appliesTo = "t_device",comment="设备表") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class DeviceEntity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5901645139848980583L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '设备名称'")
	private String deviceName;
	@Column(columnDefinition = "varchar(32) comment '设备编码'")
	private String deviceCode;
	@Column(columnDefinition = "varchar(256) comment '设备地址'")
	private String deviceAddress;
	@Column(columnDefinition = "varchar(512) comment '备注'")
	private String comment;
	@Column(columnDefinition = "int(2) comment '设备类型；0:全部；1：仅发消息；2：仅查看（如：摄像头）；'")
	private Integer deviceType;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '时间'")
	protected Timestamp createTime;
	@Column(columnDefinition = "Timestamp  comment '最后修改时间'")
	protected Timestamp lastModifyTime;
	@Column(columnDefinition = "int(1) default 1 comment '状态 0--无效数据  1--有效数据'")
	protected Integer state;
	@Column(columnDefinition = "int(11) comment '播放时间（应急广播时用）'",nullable = false)
	private Integer runTime;
	@Column(columnDefinition = "varchar(64) comment '消息标题'")
	private String titleMsg;
	@Column(columnDefinition = "varchar(32) comment '消息内容'")
	private String contentMsg;
	@Column(columnDefinition = "varchar(2000) comment '参数'")
	private String parameterJson;
	@Column(columnDefinition = "int(10) comment '排序值'")
	private Integer orders;
	
	@ManyToOne(targetEntity = MessageAccountEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "messageAccountId", referencedColumnName = "id", nullable = true)
	private MessageAccountEntity messageAccount;
	
	@ManyToOne(targetEntity = CommunityEstateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "estateId", referencedColumnName = "id", nullable = true)
	private CommunityEstateEntity estate;
	
	@OneToMany(targetEntity = DeviceCorrelationEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "deviceId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<DeviceCorrelationEntity> deviceCorrelationList = new ArrayList<DeviceCorrelationEntity>();
	
	@OneToMany(targetEntity = WarningMessageEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "deviceId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<WarningMessageEntity> warningMessageList = new ArrayList<WarningMessageEntity>();
}
