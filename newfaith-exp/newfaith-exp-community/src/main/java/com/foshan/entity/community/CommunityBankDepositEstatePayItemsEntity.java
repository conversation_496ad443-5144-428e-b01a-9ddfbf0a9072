package com.foshan.entity.community;



import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_bankdeposit_estate_payitems")
@org.hibernate.annotations.Table(appliesTo = "t_community_bankdeposit_estate_payitems",comment="非划扣收费项目") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityBankDepositEstatePayItemsEntity implements IEntityBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7456042128361390464L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
    @Column(columnDefinition = "int(1) default 0 comment '0-全部不划扣 1-仅不划扣违约金（若收费项目产生违约金，则需要根据此标识判断）'")
    private Integer depositType;
   

	@ManyToOne(targetEntity = CommunityPayItemsEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "payItemsId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityPayItemsEntity payItems;
	
	@ManyToOne(targetEntity = CommunityEstateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "estateId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityEstateEntity estate;
	
	

}
