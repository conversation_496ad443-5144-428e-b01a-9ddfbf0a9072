package com.foshan.entity.community;



import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("FACILITIES")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityFacilitiesEntity extends CommunityPropertyEntity{

	/**
	 * 设施管理
	 */
	private static final long serialVersionUID = 5318694230737091379L;
/*	@Column(columnDefinition = "varchar(64) comment '型号'")
	private String model;
	@Column(columnDefinition = "varchar(64) comment '条形码'")
	private String barcodes;
	@Column(columnDefinition = "varchar(64) comment '生产商'")
	private String manufacturers;
	@Column(columnDefinition = "varchar(64) comment '供应商'")
	private String supplier;
	@Column(columnDefinition = "varchar(64) comment '售后电话'")
	private String supportPhone;
	@Column(columnDefinition = "int(2) comment '设备类型  0门禁'")
	private Integer facilitiesType;
	@Column(columnDefinition = "int(2) comment '设备状态，0未启用,1在用，3报废'")
	private Integer facilitiesState;
	@Column(columnDefinition = "Timestamp  comment '投入使用日期'")
	protected Timestamp useDate;*/
	
}
