package com.foshan.entity.community;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_announcements")
@org.hibernate.annotations.Table(appliesTo = "t_community_announcements",comment="社区公告") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
@DiscriminatorValue("C")
public class CommunityAnnouncementsEntity implements IEntityBean {
    /**
     * 社区公告
     */
    private static final long serialVersionUID = 1933890692307199198L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(columnDefinition = "varchar(50) comment '公告标题'")
    private String title;
    @Column(columnDefinition = "varchar(5000) comment '公告内容'")
    private String content;
    @Column(columnDefinition = "Timestamp default current_timestamp comment '时间'")
    private Timestamp createTime;
    @Column(columnDefinition = "varchar(50) comment '发布者'")
    private String publisher;
    @Column(columnDefinition = "varchar(50) comment '审核者'")
    private String reviewer;
    @Column(columnDefinition = "int(1) comment '公告审核状态：0--未审核 1--审核通过 2--审核不通过 '")
    private Integer status;
    @Column(columnDefinition = "int(1) comment '公告类型：0--活动公告 1--物业公告 2--社区政务 '")
    private Integer announcementsType;
    @Column(columnDefinition = "int(1) comment '是否置顶：0--否 1--是 '")
    private Integer isTop;





}
