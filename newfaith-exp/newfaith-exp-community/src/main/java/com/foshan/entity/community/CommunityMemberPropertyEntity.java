package com.foshan.entity.community;


import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.CascadeType;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_member_property")
@org.hibernate.annotations.Table(appliesTo = "t_community_member_property",comment="用户资产") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityMemberPropertyEntity implements IEntityBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = -6808920060488514054L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(columnDefinition = "Timestamp default current_timestamp comment '创建时间'")
	protected Timestamp createTime;
	@Column(columnDefinition = "datetime comment '离退日期'")
	private Date terminationDate;
//	@Column(columnDefinition = "datetime comment '收楼日期'")
//	private Date acceptanceDate;
	@Column(columnDefinition = "datetime comment '入住日期'")
	private Date recordDate;
	@Column(columnDefinition = "datetime comment '计费日期'")
	private Date billingDate;
	@Column(columnDefinition = "datetime comment '计费结束日期'")
	private Date endDate;
	@Column(columnDefinition = "int(2) comment '是否为当前扣费帐户 0-否，1-是(业主和租户当前仅能有一个有效扣费)'")
	private Integer isCurrentOwner;
	@Column(columnDefinition = "int(2) comment '0业主 1住户成员（住户成员不需要填写收楼日期与计费日期）2、租户(计费日期填写起租日期，离退日期填写退租日期)'")
	private Integer memberType;
	@Column(columnDefinition = "int(2) comment '审核状态 0：申请中；1：通过；2：拒绝；'")
	private Integer auditState;
	@Column(columnDefinition = "int(2) comment '是否该单元当前有效的用户；0-否,1-是'")
	private Integer isCurrentMember;
	@Column(columnDefinition = "varchar(512) comment '备注'")
	private String comment;
	@Column(columnDefinition = "varchar(1000) comment '车辆信息（json格式存放）'")
	private String carInfo;
	@Column(columnDefinition = "varchar(64) comment '购方名称'")
	private String buyersName;
	@Column(columnDefinition = "varchar(64) comment '购方地址'")
	private String buyersAddress;
	@Column(columnDefinition = "varchar(64) comment '企业类型'")
	private String businessType;
	@Column(columnDefinition = "varchar(64) comment '购方税号'")
	private String paytaxNo;
	@Column(columnDefinition = "varchar(64) comment '购方银行账号'")
	private String buyersBankAccount;
	@Column(columnDefinition = "varchar(64) comment '购方邮箱地址'")
	private String buyerEmail;
	@Column(columnDefinition = "int(2) comment '租用类型 0-长租；1-短租；'")
	private Integer rentType;
	@Column(columnDefinition = "varchar(64) comment '合同信息'")
	private String contractInfo;
	@Column(columnDefinition = "Timestamp  comment '最后修改时间'")
	protected Timestamp lastModifyTime;
	@Column(columnDefinition = "Timestamp  comment '计费日期最后修改时间'")
	protected Timestamp billingDateModify; 

	@ManyToOne(targetEntity = CommunityMemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	private CommunityMemberEntity member;
	
	@ManyToOne(targetEntity = CommunityPropertyEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "parentPropertyId", referencedColumnName = "id", nullable = true)
	private CommunityPropertyEntity parentProperty;
	
	@ManyToOne(targetEntity = CommunityPropertyEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "propertyId", referencedColumnName = "id", nullable = true)
	private CommunityPropertyEntity property;

	@OneToMany(targetEntity = CommunityPayItemsPriceEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberPropertyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityPayItemsPriceEntity> payItemsPriceList = new ArrayList<CommunityPayItemsPriceEntity>();
	
	@ManyToOne(targetEntity = CommunityContractEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "contractId", referencedColumnName = "id", nullable = true)
	private CommunityContractEntity contract;
}
