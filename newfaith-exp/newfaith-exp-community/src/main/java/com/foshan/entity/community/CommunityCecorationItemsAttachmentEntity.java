package com.foshan.entity.community;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_decoration_items_attachment")
@org.hibernate.annotations.Table(appliesTo = "t_community_decoration_items_attachment",comment="装修项目附件") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityCecorationItemsAttachmentEntity implements IEntityBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = -1707339064840661538L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	
	@ManyToOne(targetEntity = CommunityMemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "attachmentId", referencedColumnName = "id", nullable = true)
	private CommunityDecorationAttachmentEntity attachment;
	
	@ManyToOne(targetEntity = CommunityDecorationItemsEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "decorationItemsId", referencedColumnName = "id", nullable = true)
	private CommunityDecorationItemsEntity decorationItems;
	
	@ManyToOne(targetEntity = CommunityDistrictEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "districtId", referencedColumnName = "id", nullable = true)
	private CommunityDistrictEntity district;
	
	@Column(columnDefinition = "varchar(254) comment '房产类型'")
	private String estateType;
	

}
