package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_meter_attributes")
@org.hibernate.annotations.Table(appliesTo = "t_community_meter_attributes",comment="表属性") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityMeterAttributesEntity implements IEntityBean {

	private static final long serialVersionUID = 7708699613751296226L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '属性名称'")
	private String attributeName;
    @Column(columnDefinition = "decimal(10,2) comment '倍率'")
    private BigDecimal rate;
	@Column(columnDefinition = "int(2) comment '类别（1:电表、2:水表、3:临时表、4:代收水表、5:代收电表）'")
	private Integer category;
	@Column(columnDefinition = "int(10) comment '量程'")
	private Integer ranges;
    @Column(columnDefinition = "decimal(14,8) comment '单价'")
    private BigDecimal unitPrice;
	@Column(columnDefinition = "varchar(64) comment '单位'")
	private String measureUnit;
	@Column(columnDefinition = "varchar(512) comment '备注'")
	private String comment;
	
	@Column(columnDefinition = "varchar(64) comment '旧表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;
	
	
	@OneToMany(targetEntity = CommunityMeterEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "meterAttributesId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMeterEntity> meterList = new ArrayList<>();
	
	
	@OneToMany(targetEntity = CommunityMeterPriceHistoryEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "meterAttributesId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMeterPriceHistoryEntity> meterPriceList = new ArrayList<>();
}
