package com.foshan.entity.community;

import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_inspection_record")
@org.hibernate.annotations.Table(appliesTo = "t_community_inspection_record",comment="巡查记录表") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityInspectionRecordEntity implements IEntityBean {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 2050983249889687147L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '时间'")
	protected Timestamp createTime;
	@Column(columnDefinition = "Timestamp  comment '签到时间'")
	protected Timestamp lastModifyTime;
	@Column(columnDefinition = "varchar(32) comment '记录人'")
	private String recorder;
	@Column(columnDefinition = "int(2) comment '签到 0：否；1：已签到；'")
	private Integer signIn;
	@Column(columnDefinition = "varchar(32) comment '巡查意见'")
	private String opinion;
	@Column(columnDefinition = "varchar(3000) comment '巡查项'")
	private String itemsDetail;
	@ManyToOne(targetEntity = AssetEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "imageId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private AssetEntity image;
	
	@ManyToOne(targetEntity = CommunityEventsEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventId", referencedColumnName = "id", nullable = true)
	private CommunityEventsEntity events;
	
	@ManyToOne(targetEntity = CommunityEventCategoryEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventCategoryId", referencedColumnName = "id", nullable = true)
	private CommunityEventCategoryEntity eventCategory;
}
