package com.foshan.entity.community;

import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_measures")
@org.hibernate.annotations.Table(appliesTo = "t_community_measures",comment="单元欠费采取措施表") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityMeasuresEntity implements IEntityBean {/**
	 * 
	 */
	private static final long serialVersionUID = 7785261610558242848L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(32) comment '经手人/录入员'")
	private String operator;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '创建时间'")
	private Timestamp createTime;
	@Column(columnDefinition = "Timestamp  comment '最后修改时间'")
	private Timestamp lastModifyTime;
	@Column(columnDefinition = "Timestamp  comment '采取措施时间'")
	private Timestamp adoptTime;
	@Column(columnDefinition = "int(2) comment '措施类型（0:催缴费通知书、1：电话催缴、2:发律师信、3:准备起诉材料、4:其他类型）'")
	private Integer measuresType;
	@Column(columnDefinition = "int(1) default 1 comment '状态 0--进行中  1--结束'")
	protected Integer measuresState;
	@Column(columnDefinition = "varchar(2000) comment '详情'")
	private String details;
	
	@ManyToOne(targetEntity = CommunityPropertyEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "propertyId", referencedColumnName = "id", nullable = true)
	private CommunityPropertyEntity property;
}
