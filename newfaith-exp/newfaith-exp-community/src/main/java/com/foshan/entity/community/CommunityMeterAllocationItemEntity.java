package com.foshan.entity.community;

import java.math.BigDecimal;

import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_meter_allocation_item")
@org.hibernate.annotations.Table(appliesTo = "t_community_meter_allocation_item", comment = "单元总表分摊")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityMeterAllocationItemEntity extends Community {

	/**
	 * 公用表分摊清单
	 */
	private static final long serialVersionUID = 4696756327110066683L;
	@EmbeddedId
	@AttributeOverrides({ @AttributeOverride(name = "allocationId", column = @Column),
		    @AttributeOverride(name = "unitCode", column = @Column),
			@AttributeOverride(name = "allocationStartYear", column = @Column),
			@AttributeOverride(name = "allocationStartMonth", column = @Column),
			@AttributeOverride(name = "allocationEndYear", column = @Column),
			@AttributeOverride(name = "allocationEndMonth", column = @Column) })
	private CommunityMeterAllocationItemIdEntity id;
	@Column(columnDefinition = "decimal(10,4) comment '分摊量'")
	private BigDecimal allocationNum;
	@Column(columnDefinition = "decimal(10,4) comment '分摊金额'")
	private BigDecimal allocationAmount;
	@Column(columnDefinition = "int(11) comment '单元Id'")
	private Integer estateId;
	@Column(columnDefinition = "varchar(20) comment '公摊费用名称'")
	private String payItemsName;
	@Column(columnDefinition = "int(11) comment '总表Id'")
	private Integer meterId;
	@Column(columnDefinition = "int(2) comment '分摊周期'")
	private Integer allocationPeriod;
	@Column(columnDefinition = "int(11) comment '分摊模版Id'")
	private Integer templeteId;
	@Column(columnDefinition = "int(1) default 0 comment '特殊分摊标识 0：正常计费 1、只计算不分摊'")
	private Integer specialAllocationFlag;


}
