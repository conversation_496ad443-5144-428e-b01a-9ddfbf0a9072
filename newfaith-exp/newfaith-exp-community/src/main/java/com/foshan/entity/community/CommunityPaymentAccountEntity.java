package com.foshan.entity.community;


import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_payment_account")
@org.hibernate.annotations.Table(appliesTo = "t_community_payment_account",comment="账户信息")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityPaymentAccountEntity extends Community {
	/**
	 * 账户信息
	 */
	private static final long serialVersionUID = 2586888931276833617L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '划帐银行'")
	private String bankName;
	@Column(columnDefinition = "varchar(64) comment '银行帐户'")
	private String bankAccount;
	@Column(columnDefinition = "varchar(32) comment '帐号名'")
	private String accountName;
	@Column(columnDefinition = "varchar(32) comment '开户人证件号'")
	private String idNumber;

	
	@ManyToOne(targetEntity = CommunityMemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	private CommunityMemberEntity member;
	
	@OneToMany(targetEntity = CommunityPropertyEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "paymentAccountId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityPropertyEntity> propertyList = new ArrayList<CommunityPropertyEntity>();
}
