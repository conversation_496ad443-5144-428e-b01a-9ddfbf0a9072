package com.foshan.entity.community.vo;


import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@ToString
public class ProprietorInfoVo {
	//职业
	private String profession;
	//家庭人数
	private String familySize;
	//家庭成员
	private String familyMember;
	//是否关系客户
	private String relation;
	//是否有过重大报修
	private String majorRepair;
	//以往投诉次数
	private String complaintsNumber;
	//投诉内容
	private String complaintsContent;
	//是否参与过群诉
	private String participationGroupAction;
	//是否主持过群诉
	private String directGroupAction;
	//业主群中是否活越
	private String activeGroup;
	//是否喂养宠物
	private String havePet;
	//是否意见领袖
	private String opinionLeader;
	//业余爱好
	private String hobby;
	//关联房产
	private String associatedProperty;

}
