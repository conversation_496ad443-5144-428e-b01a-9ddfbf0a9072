package com.foshan.entity.community;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_reservation_activities")
@org.hibernate.annotations.Table(appliesTo = "t_community_reservation_activities",comment="预约活动")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityReservationActivitiesEntity extends Community {

	/**
	 * 预约活动
	 */
	private static final long serialVersionUID = 7048994205982168777L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '预约活动名称'")
	private String activitiesName;
	@Column(columnDefinition = "varchar(5000) comment '介绍'")
	private String centent;
	@Column(columnDefinition = "int(2) comment '需要确认协议 0否1是'")
	private Integer needConfirmAgreement;
	@Column(columnDefinition = "varchar(5000) comment '协议'")
	private String agreement;
	@Column(columnDefinition = "Timestamp  comment '开始时间'")
	private Timestamp startTime;
	@Column(columnDefinition = "Timestamp  comment '结束时间'")
	private Timestamp endTime;
	@Column(columnDefinition = "int(2) comment '使用  0否 1是'")
	private Integer employ;
	@Column(columnDefinition = "int(10) comment '排序值'")
	private Integer orders;

	@OneToMany(targetEntity = CommunityReservationRecordEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "activitiesId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReservationRecordEntity> recordList = new ArrayList<CommunityReservationRecordEntity>();
	
	@OneToMany(targetEntity = CommunityReservationDateEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "activitiesId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReservationDateEntity> reservationDateList = new ArrayList<CommunityReservationDateEntity>();
	
	@ManyToOne(targetEntity = CommunityReservationStrategyEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "strategyId", referencedColumnName = "id", nullable = true)
	private CommunityReservationStrategyEntity strategy;
	
	@ManyToOne(targetEntity = CommunityEstateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "publicAreaId", referencedColumnName = "id", nullable = true)
	private CommunityEstateEntity estate;
}
