package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.EntityObject;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_receipt")
@org.hibernate.annotations.Table(appliesTo = "t_community_receipt",comment="收款单据")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityReceiptEntity extends EntityObject {
	/**
	 * 收款单据
	 */
	private static final long serialVersionUID = -3285561037070925302L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '单据编号'")
	private String receiptCode;
//	@Column(columnDefinition = "varchar(64) comment '单据编号序号'")
//	private String receiptNum;
    @Column(columnDefinition = "datetime comment '收款日期'")
    private Date receiptDate;
	@Column(columnDefinition = "varchar(64) comment '姓名'")
	private String payerName;
    @Column(columnDefinition = "decimal(10,2) comment '金额'")
    private BigDecimal amount;
	@Column(columnDefinition = "varchar(64) comment '币种'")
	private String moneytype;
    @Column(columnDefinition = "decimal(10,2) comment '汇率'")
    private BigDecimal exchangeRate;
    @Column(columnDefinition = "decimal(10,2) comment '本位币金额'")
    private BigDecimal currencyAmount;
	@Column(columnDefinition = "varchar(64) comment '收款方式:1、线上支付：微信：WX_PUBLIC--公众号支付;WX_NAVITE_QR--微信二维码支付;WX_MOBILE_WEBSITE--手机网站支付;WX_MINIPROGRAM--微信小程序;WX_PAYCODE--微信付款码支付;AL_NAVITE_QR--支付宝二维码支付;AL_PAYCODE--支付宝付款码支付.2、线下（标试必须是以OFFLINE_开头）：OFFLINE_POS--线下pos机;OFFLINE_CASH--线下现金;OFFLINE_TRANSFER--线下转账;银行代收--OFFLINE_BANKAGENT,3、其它值为旧数据'")
	private String paymentMethod;
    @Column(columnDefinition = "varchar(512) comment '备注'")
    private String comment;
	@Column(columnDefinition = "varchar(64) comment '经办人'")
	private String agent;
	@Column(columnDefinition = "varchar(64) comment '出纳'")
	private String cashier;
	@Column(columnDefinition = "varchar(64) comment '主管'")
	private String supervisor;
	@Column(columnDefinition = "varchar(64) comment '单据类别'")
	private String receiptType;
	@Column(columnDefinition = "int(2) comment '收款类型 （0：应收款；1：退款； 2：预收；）'")
	private Integer paymentType;
	@Column(columnDefinition = "int(10) comment '打印次数'")
	private Integer printNum;
	@Column(columnDefinition = "varchar(64) comment '交易银行账号'")
	private String bankAccount;
	@Column(columnDefinition = "varchar(64) comment '微信交易流水号'")
	private String wxTradeNo;
	@Column(columnDefinition = "varchar(64) comment '微信商户订单号'")
	private String wxorderNo;
	@Column(columnDefinition = "varchar(256) comment '单元地址（小区名称+栋号+单元号层数+房号）例如：御江南十六区5座2403'")
	private String estateAddress;
	@Column(columnDefinition = "varchar(256) comment '单据pdf'")
	private String pdfUrl;
	@Column(columnDefinition = "int(2) comment '是否已开发票 0未开 1已开发票 2开票中'")
	private Integer haveInvoice;
	@Column(columnDefinition = "int(2) comment '费用类别 0:周期性收费；1：一次性收费；'")
	private Integer feeType;
	@Column(columnDefinition = "varchar(64) comment '表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;
	
	@OneToMany(targetEntity = CommunityReceiptReceivablesEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "receiptId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReceiptReceivablesEntity> receiptReceivablesList = new ArrayList<>();
	
	@ManyToOne(targetEntity = CommunityEstateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "estateId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityEstateEntity estate;
	
	@OneToMany(targetEntity = CommunityPaymentRecordEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "receiptId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityPaymentRecordEntity> paymentRecordList = new ArrayList<>();
	
	//退款记录
	@OneToMany(targetEntity = CommunityReceivablesChangesEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "receiptId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityReceivablesChangesEntity> refundList = new ArrayList<>();
	
	@OneToOne(targetEntity = CommunityBankDepositRecordEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "bankDepositRecordId", referencedColumnName = "id", nullable = true)
	private CommunityBankDepositRecordEntity bankDepositRecord;
	
	@OneToMany(targetEntity = CommunityInvoiceEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "receiptId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityInvoiceEntity> invoiceList = new ArrayList<>();
	
	public static String getReceiptCodePrefix(String receiptType) {
		if (StringUtils.isNotEmpty(receiptType)) {
			if (receiptType.equals("系统收据")) {
				return "XTSJ";
			} else if (receiptType.equals("其它收据")) {
				return "QTSJ";
			} else if (receiptType.equals("银行划账")) {
				return "YHSK";
			} else if (receiptType.equals("微信支付")) {
				return "WXSJ";
			} else if (receiptType.equals("支付宝支付")) {
				return "ALSJ";
			} else {
				return "XTSJ";
			}
		} else {
			return "XTSJ";
		}
	}
	
}
