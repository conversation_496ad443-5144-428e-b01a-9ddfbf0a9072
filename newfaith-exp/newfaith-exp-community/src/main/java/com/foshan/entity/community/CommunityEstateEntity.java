package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("ESTATE")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityEstateEntity extends CommunityPropertyEntity{
	/**
	 * 房产单元
	 */
	private static final long serialVersionUID = 5964871343319725938L;
	
    @Column(columnDefinition = "varchar(32) comment '单元编号'")
    private String unitCode;
    @Column(columnDefinition = "int(5) comment '所属楼层'")
    private Integer floor;
    @Column(columnDefinition = "varchar(100) comment '房号'")
    private String roomNumber;
//    @Column(columnDefinition = "int(1) comment ' 0:已租;1:已售;2:待租;3:待售'")
//    private Integer saleState;
//    @Column(columnDefinition = "int(1) comment ' 0:业主使用;1:租客使用;2:未使用'")
//    private Integer userState;
    @Column(columnDefinition = "decimal(10,4) comment '建筑面积'")
    private BigDecimal buildingArea;
    @Column(columnDefinition = "decimal(10,4) comment '使用面积'")
    private BigDecimal usableArea;
    @Column(columnDefinition = "decimal(10,4) comment '收费面积'")
    private BigDecimal chargingArea;
    @Column(columnDefinition = "decimal(10,4) comment '附加面积'")
    private BigDecimal additionalArea;
    @Column(columnDefinition = "varchar(32) comment '使用期限'")
    private String usageTerm;
//    @Column(columnDefinition = "int(1) comment '土地性质分类 0住宅用地 1商业用地 2工业用地 3综合用地 4 其他用地'")
//    private Integer classification;
    @Column(columnDefinition = "int(1) comment '是否验收 0否；1是'")
    private Integer isAcceptanceReceive;
    @Column(columnDefinition = "varchar(64) comment '朝向'")
    private String orientation;
    @Column(columnDefinition = "varchar(64) comment '装修标准'")
    private String decoration;
    @Column(columnDefinition = "varchar(512) comment '房产类型  如：配电房\\联排别墅\\移动机房\\电表间\\电梯洋房\\电房\\电信房\\独立别墅\\"
    		+ "消防泵房\\消防控制室\\气瓶间\\有线电视\\普通洋房\\摩托车位\\报警阀间\\工具间\\普通车位\\存放间\\复式商铺\\商铺\\发电机房\\双拼别墅\\"
    		+ "储存间\\值班室\\子母车位'")
    private String estateType;
    @Column(columnDefinition = "int(1) comment '是否可预约的 0否 1是'")
    private Integer isReserved;
    @Column(columnDefinition = "int(1) comment '房屋状态：0:未收楼;1:已入住;2:装修中;3:出租;4:离退;5:自主(已收楼 未入住);6：空置'")
    private Integer estateState;
    @Column(columnDefinition = "int(1) default 0 comment '特殊费用标识 0：正常计费 1、不计管理费 2、不计分摊费 3、不计违约金 4、全部不计 5、不计租金'")
    private Integer specialFeeFlag;
	@Column(columnDefinition = "varchar(5000) comment '预留字段'")
	protected String reservedField;
	@Column(columnDefinition = "datetime comment '收楼日期'")
	private Date acceptanceDate;
	@Column(columnDefinition = "int(1) default 0 comment '特殊分摊标识 0：正常计费 1、不分电表 2、不分水表 3、全部不分'")
	private Integer specialAllocationFlag;
	@Column(columnDefinition = "int(1) default 0 comment '租用类型 0-长租，1短租'")
	private Integer rentType;
	@Column(columnDefinition = "varchar(1024) comment '欠费原因'")
	private String reasonArrears;

	@Override
	public String toString() {
		return "CommunityEstateEntity [roomNumber=" + roomNumber + ", usableArea=" + usableArea + ", chargingArea="
				+ chargingArea + ", id=" + id + ", oldId=" + oldId + "]";
	}

	
	
}
