package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_message_account")
@org.hibernate.annotations.Table(appliesTo = "t_message_account",comment="消息账号表") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class MessageAccountEntity implements IEntityBean {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6570920234962423853L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '账号名称'")
	private String accountName;
	@Column(columnDefinition = "varchar(64) comment '密要'")
	private String secretKey;
	@Column(columnDefinition = "varchar(64) comment ''")
	private String salt;
	
	
	@ManyToMany(targetEntity = ReceiveMessageTypeEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_message_account_message_type", joinColumns = @JoinColumn(name = "messageAccountId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "messageTypeId", referencedColumnName = "id"))
	@JsonIgnore
	private List<ReceiveMessageTypeEntity> receiveMessageTypeList = new ArrayList<ReceiveMessageTypeEntity>();
	
	@OneToMany(targetEntity = DeviceEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "messageAccountId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<DeviceEntity> meterRecordList = new ArrayList<DeviceEntity>();
}
