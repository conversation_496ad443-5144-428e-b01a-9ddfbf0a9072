package com.foshan.entity.community;


import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_decoration_attachment")
@org.hibernate.annotations.Table(appliesTo = "t_community_decoration_attachment",comment="装修项目附件") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityDecorationAttachmentEntity implements IEntityBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4684951521321320594L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(254) comment '附件名称'")
	private String attachmentName;
	@Column(columnDefinition = "int(10) comment '排序'")
	private Integer orders;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '时间'")
	protected Timestamp createTime;
	
//	@ManyToMany(targetEntity = CommunityDecorationItemsEntity.class, fetch = FetchType.LAZY)
//	@JoinTable(name = "t_community_decoration_items_attachment", joinColumns = @JoinColumn(name = "attachmentId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "decorationItemsId", referencedColumnName = "id"))
//	@JsonIgnore
//	private List<CommunityDecorationItemsEntity> decorationItemsList = new ArrayList<CommunityDecorationItemsEntity>();
	
	
	@OneToMany(targetEntity = CommunityCecorationItemsAttachmentEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "attachmentId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityCecorationItemsAttachmentEntity> itemsAttachmentList = new ArrayList<CommunityCecorationItemsAttachmentEntity>();
	
	@OneToOne(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "attachmentId", referencedColumnName = "id", nullable = true)
	private AssetEntity asset;

}
