package com.foshan.entity.community;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_formula_templete")
@org.hibernate.annotations.Table(appliesTo = "t_community_formula_templete",comment="公式模版") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityFormulaTempleteEntity extends Community{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 7536035142084110425L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '模版公式名称'")	
	private String templeteName;
	@Column(columnDefinition = "varchar(64) comment '模版公式内容'")
	private String templeteInfo;
	@Column(columnDefinition = "varchar(64) comment '模版公式动态参数json格式存放'")
	private String dynamicParameter;
}
