package com.foshan.entity.community;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_meter")
@org.hibernate.annotations.Table(appliesTo = "t_community_meter",comment="总表") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityMeterEntity implements IEntityBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = -1762028036513247168L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '表名称'")
	private String meterName;
	@Column(columnDefinition = "varchar(64) comment '表编号'")
	private String meterCode;
    @Column(columnDefinition = "decimal(10,2) comment '底数'")
    private BigDecimal initialData;
	@Column(columnDefinition = "varchar(64) comment '安装地点'")
	private String installationsite;
	@Column(columnDefinition = "int(1) comment '是否共用表 0-不共用（用户表），1-共用（总表）'",nullable = false)
	private Integer isCommon;
	@Column(columnDefinition = "int(1) comment '是否分摊 0-否，1-是'",nullable = false)
	private Integer isApportioned;
	@Column(columnDefinition = "int(2) comment '分摊周期,单位:月'")
	private Integer allocationPeriod;
	@Column(columnDefinition = "int(2) comment '分摊类别：1按面积分摊，2自定义分摊'")
	private Integer allocationMethod;
	@Column(columnDefinition = "int(2) comment '是否坏表 0-否，1-是'")
	private Integer isDisabled;
    @Column(columnDefinition = "datetime comment '表坏日期'")
    private Date expirationDates;
	@Column(columnDefinition = "varchar(512) comment '备注'")
	private String comment;
	@Column(columnDefinition = "int(10) comment '层次（树形结构层次）'")
	private Integer level;
	@Column(columnDefinition = "varchar(64) comment '收费项目'")
	private String payItemsName;
	@Column(columnDefinition = "varchar(64) comment '收费项目类别（冗余）'")
	private String chargeCategory;
    @Column(columnDefinition = "datetime comment '上次分摊日期'")
	private Date lastAllocateDate;
	@Column(columnDefinition = "int(1) default 1 comment '状态 0--无效数据  1--有效数据'")
	protected Integer state;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '时间'")
	protected Timestamp createTime;
	@Column(columnDefinition = "Timestamp  comment '最后修改时间'")
	protected Timestamp lastModifyTime;
	
	@Column(columnDefinition = "varchar(64) comment '旧表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;

	@ManyToOne(targetEntity = CommunityMeterEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentMeterId", referencedColumnName = "id", nullable = true)
	private CommunityMeterEntity parentMeter;
	@OneToMany(targetEntity = CommunityMeterEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "parentMeterId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMeterEntity> subMeterList = new ArrayList<CommunityMeterEntity>();
	
	@OneToMany(targetEntity = CommunityMeterRecordEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "meterId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMeterRecordEntity> meterRecordList = new ArrayList<CommunityMeterRecordEntity>();
	
	@ManyToOne(targetEntity = CommunityMeterAttributesEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "meterAttributesId", referencedColumnName = "id", nullable = true)
	private CommunityMeterAttributesEntity meterAttributes;
	
	@ManyToOne(targetEntity = CommunityMeterFormulaEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "formulaId", referencedColumnName = "id", nullable = true)
	private CommunityMeterFormulaEntity meterFormula;
	
	@ManyToMany(targetEntity = CommunityPropertyEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_meter_property", joinColumns = @JoinColumn(name = "meterId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "propertyId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityPropertyEntity> propertyList = new ArrayList<CommunityPropertyEntity>();
	
	@ManyToOne(targetEntity = CommunityPayItemsEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "payItemId", referencedColumnName = "id", nullable = true)
	private CommunityPayItemsEntity payItems;
}
