package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_building")
@org.hibernate.annotations.Table(appliesTo = "t_community_building",comment="楼栋") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityBuildingEntity extends Community {

	/**
	 * 楼栋
	 */
	private static final long serialVersionUID = 6456751677402873531L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '楼栋名称'")
	private String buildingName;
	@Column(columnDefinition = "varchar(64) comment '楼栋编号'")
	private String buildingCode;
    @Column(columnDefinition = "int(2) comment '楼栋类别(功能) 0:住宅、1:别墅、2:商铺、3:停车场、4:公共区域'")
    private Integer buildingType;
    @Column(columnDefinition = "int(5) comment '层数'")
    private Integer layers;
    @Column(columnDefinition = "varchar(64) comment '楼栋地址'")
    private String address;
    @Column(columnDefinition = "varchar(64) comment '建筑许可证号'")
    private String buildingPermitNum;
    @Column(columnDefinition = "varchar(64) comment '预售许可证号'")
    private String permitLicenceNum;
    @Column(columnDefinition = "datetime comment '竣工日期'")
    private Date completionDate;
    @Column(columnDefinition = "datetime comment '封顶日期'")
    private Date cappingDate;
    @Column(columnDefinition = "varchar(64) comment '装修标准'")
    private String decoration;
    @Column(columnDefinition = "varchar(64) comment '结构类型'")
    private String construction;
    @Column(columnDefinition = "varchar(64) comment '完损等级'")
    private String damagedLevel;
    @Column(columnDefinition = "int(2)comment '地性质分类 0住宅用地 1商业用地 2工业用地 3综合用地 4 其他用地'")
    private Integer classification;
	@Column(columnDefinition = "varchar(512) comment '备注'")
	private String comment;
    @Column(columnDefinition = "decimal(10,4) comment '建筑面积'")
    private BigDecimal buildingArea;
    @Column(columnDefinition = "decimal(10,4) comment '实用面积'")
    private BigDecimal usableArea;
	@Column(columnDefinition = "int(10) comment '楼栋排序号'")
	private Integer buildingOrder;
    
	@Column(columnDefinition = "varchar(64) comment '表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;
    
	@ManyToOne(targetEntity = CommunityDistrictEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "districtId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityDistrictEntity district;
	
	@OneToMany(targetEntity = CommunityPropertyEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "buildingId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityPropertyEntity> propertyList = new ArrayList<CommunityPropertyEntity>();

}
