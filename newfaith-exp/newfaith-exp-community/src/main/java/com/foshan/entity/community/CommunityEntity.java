package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;
import com.foshan.entity.RegionEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community")
@org.hibernate.annotations.Table(appliesTo = "t_community",comment="社区") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityEntity implements IEntityBean {

	private static final long serialVersionUID = -5231105693049854701L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '社区名称'")
	private String communityName;
	@Column(columnDefinition = "varchar(256) comment '社区地址'")
	private String communityAddress;
	@Column(columnDefinition = "varchar(256) comment '管辖面积'")
	private String areaSize;
	@ManyToOne(targetEntity = RegionEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "regionId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private RegionEntity region;
	
	@OneToMany(targetEntity = CommunityDistrictEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "communityId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityDistrictEntity> districtList = new ArrayList<CommunityDistrictEntity>();

}
