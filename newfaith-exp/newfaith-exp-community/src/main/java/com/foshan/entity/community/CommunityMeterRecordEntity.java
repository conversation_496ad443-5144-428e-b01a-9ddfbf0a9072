package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_meter_record")
@org.hibernate.annotations.Table(appliesTo = "t_community_meter_record",comment="抄表")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityMeterRecordEntity extends Community {

	private static final long serialVersionUID = 6460513809451957919L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
    @Column(columnDefinition = "datetime comment '本次读数日期'")
    private Date recordDate;
    @Column(columnDefinition = "decimal(10,2) comment '本次读数'")
    private BigDecimal recordNum;
	@Column(columnDefinition = "varchar(64) comment '抄表人'")
	private String recorder;
	@Column(columnDefinition = "int(1) comment '是否归零'",nullable = false)
	private Integer isZero;
	@Column(columnDefinition = "varchar(64) comment '表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;
	@Column(columnDefinition = "int(11) comment '上次读数Id'")
	private Integer lastRecordId;
	@Column(columnDefinition = "int(2) comment '数据异常标识，0：正常；1：异常；'")
	private Integer exceptionState;
	@Column(columnDefinition = "varchar(256) comment '数据异常备注'")
	private String exceptionRemark;
	@Column(columnDefinition = "decimal(10,2) comment '额外用量'")
	private BigDecimal additionalAmount;
	@Column(columnDefinition = "varchar(64) comment '额外用量说明'")
	private String additionalInstructions;
	
	@ManyToOne(targetEntity = CommunityMeterEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "meterId", referencedColumnName = "id", nullable = true)
	private CommunityMeterEntity communityMeter;

}
