package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.EntityObject;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_receivables_changes")
@org.hibernate.annotations.Table(appliesTo = "t_community_receivables_changes",comment="应收款变更（减加）")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityReceivablesChangesEntity extends EntityObject {
	/**
	 * 应收款变更（减加）/退款表
	 */
	private static final long serialVersionUID = 2168095812601014818L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "varchar(64) comment '收费项目'")
	private String payItemsName;
    @Column(columnDefinition = "datetime comment '应收日期'")
    private Date receivableDate;
    @Column(columnDefinition = "decimal(10,2) comment '应收金额'")
    private BigDecimal receivableAmount;
    @Column(columnDefinition = "decimal(10,2) comment '减加金额'")
    private BigDecimal changeAmount;
    @Column(columnDefinition = "decimal(10,2) comment '已收金额'")
    private BigDecimal receivedAmount;
    @Column(columnDefinition = "varchar(512) comment '金额调整/退款备注'")
    private String comment;
    @Column(columnDefinition = "datetime comment '减加日期/退款成功时间'")
    private Date changeDate;
	@Column(columnDefinition = "int(2) comment '减加类型 0:减免；1：添加；2：退款'")
	private Integer changeType;
	@Column(columnDefinition = "varchar(64) comment '减加人（减加人）'")
	private String recorder;
	@Column(columnDefinition = "varchar(64) comment '批准人'")
	private String approver;
	@Column(columnDefinition = "varchar(64) comment '表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;
	
	@ManyToOne(targetEntity = CommunityReceivablesEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "receivablesId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityReceivablesEntity receivables;
	
	@ManyToOne(targetEntity = CommunityEstateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "estateId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityEstateEntity estate;
	
	//以下是退款独有的字段
    @Column(columnDefinition = "varchar(64) comment '第三方支付系统交易ID/退款ID'")
    private String refundOutTradeNo;
    @Column(columnDefinition = "varchar(32) comment '支付模块的退款流水ID'")
    private String paymentRefundCode;
    @Column(columnDefinition = "varchar(32) comment '退款编号'")
    private String refundCode;
    @Column(columnDefinition = "int(8) comment '退款方式:0--现金，1--银行转账，3--在线支付原路退还'")
    private Integer refundMethod;
    @Column(columnDefinition = "varchar(64) comment '退款方式名称:现金，银行转账'")
    private String refundMethodName;
    @Column(columnDefinition = "varchar(32) comment '退款收款人'")
    private String  refundPayee;
    @Column(columnDefinition = "varchar(64) comment '退款目标账号'")
    private String refundAccount;
    @Column(columnDefinition = "varchar(64) comment '退款目标银行'")
    private String refundBank;
    @Column(columnDefinition = "varchar(128) comment '退款失败原因'")
    private String refundFailReason;
    @Column(columnDefinition = "int(4) default 0 comment '订单退款状态：0未向支付系统发起退款申请；1已经发起退款请求第三方支付正在处理退款,等处理；2完成退款退已经到达标账户；3支付系统退款出现异常; 4支付系统付退款关闭'")
    private Integer refundStatus;
	
    // 对应收据
    @ManyToOne(targetEntity = CommunityReceiptEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  	@JoinColumn(name = "receiptId", referencedColumnName = "id", nullable = true)
  	@JsonIgnore
  	private CommunityReceiptEntity receipt;

    
}
