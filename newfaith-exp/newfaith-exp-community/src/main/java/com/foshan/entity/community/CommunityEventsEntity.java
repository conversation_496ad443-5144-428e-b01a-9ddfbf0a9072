package com.foshan.entity.community;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.AssetEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_events")
@org.hibernate.annotations.Table(appliesTo = "t_community_events",comment="事件") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityEventsEntity extends Community {

	private static final long serialVersionUID = 7171207623728922821L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(columnDefinition = "varchar(64) comment '订单编号'")
    private String orderCode;
	@Column(columnDefinition = "varchar(512) comment '标题'")
	private String title;
	@Column(columnDefinition = "varchar(1024) comment '审核意见'")
	protected String auditOpinion;
    @Column(columnDefinition = "varchar(3000) comment '说明'")
    private String centent;
    @Column(columnDefinition = "varchar(3000) comment '申请详情'")
    private String itemsdetail;
	@Column(columnDefinition = "int(2) comment '状态：0初始化、1待审核、2审核通过（待派单）、3审核不通过、4已派单、5已完成、6已撤销、"
			+ "7待支付费用、8进行中、9装修完成、10验收中、11验收申请不通过、12验收通过、13申请退款、14初审通过、15初审不通过'")
	private Integer eventState;
	@Column(columnDefinition = "int(2) comment '类型：0普通类型、1装修申请'")
	private Integer eventType;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '服务时间'")
	private Timestamp servicingTime;
	@Column(columnDefinition = "varchar(3000) comment '操作日志'")
	private String diary;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '开始时间'")
	private Timestamp startTime;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '结束时间'")
	private Timestamp endTime;
	@Column(columnDefinition = "int(2)  default 0  comment '发送状态:0未发送 1已发送成功 2发送失败'")
	private Integer sendstatus;
	@Column(columnDefinition = "varchar(1024) comment '请求参数'")
	private String parameterReq;
	@Column(columnDefinition = "varchar(1024) comment '返回参数'")
	private String parameterRes;
	@Column(columnDefinition = "varchar(10) default 0000 comment '验收状态，从右往左，第一位是工程部状态；"
			+ "第二位客服部；第三位环境部；第四位总经办；数字0表示未收到，1：待办状态，2：审核通过，3：审核不通过；'")
	private String checkAcceptAuditState;
    @Column(columnDefinition = "varchar(10000) comment '验收详情'")
    private String checkAcceptItems;
	@Column(columnDefinition = "Timestamp  comment '验收时间'")
	protected Timestamp acceptanceTime;
	@Column(columnDefinition = "int(2)  default 0  comment '退款方式:0、抵扣物业费 1、现金退回 2、银行卡'")
	private Integer refundMethod;
	@Column(columnDefinition = "varchar(1024) comment '退款信息'")
	private String refundInfo;
	@Column(columnDefinition = "int(2)  default 0  comment '存在押金:0、无押金；1、押金未退;2:押金已退；'")
	private Integer haveCashPledge;
	
	
	@ManyToOne(targetEntity = CommunityPropertyEntity.class, cascade = CascadeType.ALL, fetch = FetchType.EAGER)
	@JoinColumn(name = "propertyId", referencedColumnName = "id", nullable = true)
	private CommunityPropertyEntity property;
	
	@ManyToOne(targetEntity = CommunityEventCategoryEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventCategoryId", referencedColumnName = "id", nullable = true)
	private CommunityEventCategoryEntity eventCategory;
	
	@ManyToOne(targetEntity = CommunityUserEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "handlerId", referencedColumnName = "id", nullable = true)
	private CommunityUserEntity handler;
	
	@ManyToOne(targetEntity = CommunityMemberEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "memberId", referencedColumnName = "id", nullable = true)
	private CommunityMemberEntity member;
	
	@OneToMany(targetEntity = CommunityInspectionRecordEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityInspectionRecordEntity> inspectionRecordList = new ArrayList<CommunityInspectionRecordEntity>();
	
	@ManyToOne(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "qrCodeId", referencedColumnName = "id", nullable = true)
	private AssetEntity qrCode;
	
	@ManyToOne(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "imageId", referencedColumnName = "id", nullable = true)
	private AssetEntity image;
	
	@ManyToOne(targetEntity = AssetEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "constructionPermitId", referencedColumnName = "id", nullable = true)
	private AssetEntity constructionPermit;
	
	@ManyToMany(targetEntity = CommunityReceivablesEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_event_receivables", joinColumns = @JoinColumn(name = "eventId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "receivablesId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityReceivablesEntity> receivablesList = new ArrayList<CommunityReceivablesEntity>();
	
	@OneToMany(targetEntity = CommunityEventNotificationEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "eventId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityEventNotificationEntity> notificationList = new ArrayList<CommunityEventNotificationEntity>();
	
	
}
