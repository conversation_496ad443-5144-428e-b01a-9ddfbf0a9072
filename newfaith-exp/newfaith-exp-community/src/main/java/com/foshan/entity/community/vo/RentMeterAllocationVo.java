package com.foshan.entity.community.vo;


import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@JsonInclude(Include.NON_EMPTY)
@Data
@Slf4j
public class RentMeterAllocationVo {
	//分摊量
	private BigDecimal allocationNum;
	//分摊金额
	private BigDecimal allocationAmount;
	
	private Integer estateId;
	
	private String unitCode;
	
	private String meterName;
	
	private String meterCode;
	
	private Integer meterId;
	
	private Integer payItemsId;
	
	private String payItemsName;
	//额外用量
	private BigDecimal additionalAmount;
	//额外用量说明
	private String additionalInstructions;
	//本次读数
	private BigDecimal recordNum;
	//上次读数
	private BigDecimal lastNum;
	//单价
	private BigDecimal unitPrice;
	//抄表日期
	private String recordDate;
	//应收金额
	private BigDecimal receivableAmount;
	//类别（1:电表、2:水表、3:临时表、4:代收水表、5:代收电表）
	private Integer category;
	
	private LocalDate startDate;
	
	private LocalDate endDate;
}
