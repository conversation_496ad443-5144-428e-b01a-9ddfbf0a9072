package com.foshan.entity.community;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_pay_items")
@org.hibernate.annotations.Table(appliesTo = "t_community_pay_items",comment="收费项目设定")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityPayItemsEntity extends  Community {
	/**
	 * 收费项目设定
	 */
	private static final long serialVersionUID = -3285561037070925302L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "int(2) comment '费用类别 1:管理费；2：车位费；3：分摊费；4：商管部；5：违约金；6：其它类；7：押金类；8:有偿服务费；9:出租租金'")
	private Integer chargeCategory;
	@Column(columnDefinition = "varchar(64) comment '项目名称'")
	private String itemsName;
    @Column(columnDefinition = "decimal(10,2) comment '单价'")
    private BigDecimal price;
    @Column(columnDefinition = "varchar(64) comment '价格单位（如：元/平方米）'")
    private String priceUnit;
    @Column(columnDefinition = "datetime comment '开始时间'")
    private Date startTime;
    @Column(columnDefinition = "datetime comment '结束时间'")
    private Date endTime;
	@Column(columnDefinition = "int(1) default 1 comment '是否应收入账'")
	private Integer isReceivables;
    @Column(columnDefinition = "int(2) comment '交款日，出账日如：1--每月1号，32--下月1日，0--一次性事务收费'")
    private Integer payDate;
    @Column(columnDefinition = "int comment '扣费优先级，值越大优先级越大'")
    private Integer priority;
    @Column(columnDefinition = "varchar(512) comment '备注'")
    private String comment;
    @Column(columnDefinition = "int(1) default 0 comment '管理费计算类型：1、按收费面积计算--管理费；2、按附加面积计算--花园管理费;3、按个数计算--车位租用；4、按使用情况计算--公寓出租水电费'")
    private Integer feeCalType;
//    @Column(columnDefinition = "varchar(64) comment '费用公式'")
//    private String feeFormula;
    @Column(columnDefinition = "int(1) default 0 comment '是否产生违约金:0--不产生违约金 1--产生违约金'")
    private Integer isBreach;
    @Column(columnDefinition = "varchar(30) comment '违约金名称'")
    private String breachName;
    @Column(columnDefinition = "decimal(5,4) default 0 comment '违约金系数'")
    private BigDecimal breachRatio;
	@Column(columnDefinition = "varchar(64) comment '表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;
	@Column(columnDefinition = "varchar(512) comment '发票信息'")
	private String inviceInfo;
	@Column(columnDefinition = "int(1) default 0 comment '是否默认银行划扣:0--否 1--是'")
	private Integer isBankDeposit;
	@Column(columnDefinition = "int(10) comment '收费项目分组，如果几个收费项目在同一个组，且同时关联了同一个单元，每个月的应收款里，这组别的收费项目的应收只能出现一个。目前主要增加针对预收那部分的，已经存在了其中一个的应收，另一个收费项目不生成应收了。'")
	private Integer groupFee;
    @Column(columnDefinition = "int(2) default 0 comment '是否存参加优惠活动:0--否 1--是'")
	private Integer enableBenefitPolicy;
    @Column(columnDefinition = "datetime comment '优惠策略开始生效时间(yyyy-MM-dd HH:mm:ss)'")
    private Date benefitStartDate; 
    @Column(columnDefinition = "decimal(10,2) comment '取消优惠后的物业费单价（元），即不符合优惠后的物业费单价，数字保留两位小数'")
    private BigDecimal benefitOrginPrice; 

	@ManyToMany(targetEntity = CommunityPropertyEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_property_pay_items", joinColumns = @JoinColumn(name = "payItemsId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "propertyId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityPropertyEntity> propertyList = new ArrayList<CommunityPropertyEntity>();
	
//	@ManyToMany(targetEntity = CommunityPropertyEntity.class, fetch = FetchType.LAZY)
//	@JoinTable(name = "t_community_bankDeposit_estate_payItems", joinColumns = @JoinColumn(name = "payItemsId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "propertyId", referencedColumnName = "id"))
//	@JsonIgnore
//	private List<CommunityPropertyEntity> bankDepositEstateList = new ArrayList<CommunityPropertyEntity>();
	
	@ManyToMany(targetEntity = CommunityBankDepositBatchEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_bankdeposit_batch_payitems", joinColumns = @JoinColumn(name = "payItemsId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "bankDepositBatchId", referencedColumnName = "id"))
	@JsonIgnore
	private List<CommunityBankDepositBatchEntity> bankDepositBatchList = new ArrayList<CommunityBankDepositBatchEntity>();
	
	@OneToMany(targetEntity = CommunityMeterEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "payItemId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityMeterEntity> meterList = new ArrayList<CommunityMeterEntity>();
	
	@OneToMany(targetEntity = CommunityBankDepositEstatePayItemsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "payItemsId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityBankDepositEstatePayItemsEntity> bankDepositEstatePayItemsList = new ArrayList<>();
	
	@OneToMany(targetEntity = CommunityPayItemsPriceEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "payItemsId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityPayItemsPriceEntity> payItemsPriceList = new ArrayList<CommunityPayItemsPriceEntity>();
	

	public static String getChargeCategoryStr(Integer chargeCategory) {
		String str="";
		switch(chargeCategory) {
		case 1:
			str="管理费";
			break;
		case 2:
			str="车位费";
			break;
		case 3:
			str="分摊费";
			break;
		case 4:
			str="商管部";
			break;
		case 5:
			str="违约金";
			break;
		case 7:
			str="押金类";
			break;
		case 9:
			str="出租租金";
			break;
		default:
			str="其它类";
			break;	
		}
		return str;
	}

	@Override
	public String toString() {
		return "CommunityPayItemsEntity [id=" + id + ", chargeCategory=" + chargeCategory + ", itemsName=" + itemsName
				+ ", price=" + price + ", priceUnit=" + priceUnit + ", startTime=" + startTime + ", endTime=" + endTime
				+ ", isReceivables=" + isReceivables + ", payDate=" + payDate + ", priority=" + priority + ", comment="
				+ comment + ", feeCalType=" + feeCalType + ", isBreach=" + isBreach + ", breachName=" + breachName
				+ ", breachRatio=" + breachRatio + ", oldId=" + oldId + ", oldData=" + oldData + ", inviceInfo="
				+ inviceInfo + ", isBankDeposit=" + isBankDeposit + ", groupFee=" + groupFee + ", propertyList="
				+ propertyList + ", bankDepositBatchList=" + bankDepositBatchList + ", meterList=" + meterList
				+ ", bankDepositEstatePayItemsList=" + bankDepositEstatePayItemsList + "]";
	}
	
}
