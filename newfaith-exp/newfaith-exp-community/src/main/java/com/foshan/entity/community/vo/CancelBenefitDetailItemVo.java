package com.foshan.entity.community.vo;



import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.foshan.form.IForm;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
/**
 * 
 * <AUTHOR>
 * 十六区优惠取消后，应收调整数据数据对象明细项
 */
@JsonInclude(Include.NON_EMPTY)
@Data
@Getter
@Setter
public class CancelBenefitDetailItemVo implements IForm {

	/**
	 * 
	 */
	private static final long serialVersionUID = -792358240158116139L;
	/**
	 * 被补收应收差额的应收ID
	 */
	private Integer receivablesId;
	/*
	 * 被补收应收差额的应收的应收日期
	 */
	private String receivableDate;
	/*
	 * 补应收管理费差额
	 */
	private BigDecimal subManageAmount;
	/*
	 * 已补应收管理费差额
	 */
	private BigDecimal subReceivedManageAmount;
	/*
	 * 补收的违约金差额
	 */
	private BigDecimal subBreachAmount;
	
	private List<CancelBenefitAmountChangeDetailItem> changeAmountDetail = new ArrayList<CancelBenefitAmountChangeDetailItem>(); 

	public CancelBenefitDetailItemVo(Integer receivablesId,String receivablesDate ,BigDecimal subManageAmount) {
		super();
		this.receivablesId = receivablesId;
		this.receivableDate = receivablesDate;
		this.subManageAmount = subManageAmount;
	}
	
	public CancelBenefitDetailItemVo(Integer receivablesId, String receivableDate, BigDecimal subManageAmount,
			BigDecimal subBreachAmount, List<CancelBenefitAmountChangeDetailItem> changeAmountDetail) {
		super();
		this.receivablesId = receivablesId;
		this.receivableDate = receivableDate;
		this.subManageAmount = subManageAmount;
		this.subBreachAmount = subBreachAmount;
		this.changeAmountDetail = changeAmountDetail;
	}

	public CancelBenefitDetailItemVo() {
		super();
	}

	@Override
	public int compareTo(Object o) {
		//按日期，ID升序
		if (this == o)
			return 0;
		if (o == null)
			return -1;
		CancelBenefitDetailItemVo other = (CancelBenefitDetailItemVo) o;
		if(null == other.getReceivableDate())
			return -1;
		int result = other.receivableDate.compareTo(this.receivableDate);
		if(result == 0) {
			return this.receivablesId.compareTo(other.receivablesId);
		}
		return result;
	}

	@Override
	public String toString() {
		return "CancelBenefitDetailItemVo [receivablesId=" + receivablesId + ", receivableDate=" + receivableDate
				+ ", subManageAmount=" + subManageAmount + ", subBreachAmount=" + subBreachAmount
				+ ", changeAmountDetail=" + changeAmountDetail + "]";
	}

}
