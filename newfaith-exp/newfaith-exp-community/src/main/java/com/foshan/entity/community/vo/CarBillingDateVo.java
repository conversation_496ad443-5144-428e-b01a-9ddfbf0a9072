package com.foshan.entity.community.vo;



import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@JsonInclude(Include.NON_EMPTY)
@Data
@Slf4j
public class CarBillingDateVo {
	private Integer parentPropertyId;
	private Integer propertyId;
	private Date billingDate;
	private Date terminationDate;
	private BigDecimal chargingArea;
	private String estateType;
	private String comment;
	
	
	
	public static CarBillingDateVo getCarBillingDateVo(Object[] o) {
		CarBillingDateVo vo = null;
		if (null != o) {
			try {
				vo = new CarBillingDateVo();
				vo.setParentPropertyId(Integer.parseInt(o[0].toString()));
				vo.setPropertyId(Integer.parseInt(o[1].toString()));
				vo.setBillingDate(Date.from(LocalDate.parse(o[2].toString().subSequence(0, 10))
						.atStartOfDay(ZoneId.systemDefault()).toInstant()));
				vo.setTerminationDate(null!=o[3]?Date.from(LocalDate.parse(o[3].toString().subSequence(0, 10))
						.atStartOfDay(ZoneId.systemDefault()).toInstant()):null);
				vo.setChargingArea(new BigDecimal(o[4].toString()));
				vo.setEstateType(o[5].toString());
				vo.setComment(null!=o[6]?o[6].toString():"");
			} catch (Exception ex) {
				ex.printStackTrace();
				log.error(ex.getMessage() + ":CarBillingDateVo(" + o[0].toString() + ")数据转换失败");
				vo = null;
			}
		}
		return vo;
	}



	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CarBillingDateVo other = (CarBillingDateVo) obj;
		return Objects.equals(billingDate, other.billingDate)
				&& Objects.equals(parentPropertyId, other.parentPropertyId)
				&& Objects.equals(propertyId, other.propertyId)
				&& Objects.equals(terminationDate, other.terminationDate);
	}



	@Override
	public int hashCode() {
		return Objects.hash(billingDate, parentPropertyId, propertyId, terminationDate);
	}




}
