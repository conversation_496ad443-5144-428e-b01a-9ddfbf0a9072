package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_reservation_preset_period")
@org.hibernate.annotations.Table(appliesTo = "t_community_reservation_preset_period",comment="预约预设时间段")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityReservationPresetPeriodEntity implements IEntityBean {


	/**
	 * 预约预设时间段
	 */
	private static final long serialVersionUID = -4125577810146831687L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "int(10) comment '可预约人数'")
	protected Integer maxNum;
	@Column(columnDefinition = "int(10) comment '排序值'")
	protected Integer orders;
	@Column(columnDefinition = "varchar(64) comment '开始时间'")
	private String startTime;
	@Column(columnDefinition = "varchar(64) comment '结束时间'")
	private String endTime;
	
	@ManyToMany(targetEntity = CommunityReservationStrategyEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_strategy_preset_period", joinColumns = @JoinColumn(name = "presetPeriodId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "strategyId", referencedColumnName = "id"))
	private List<CommunityReservationStrategyEntity> strategyList = new ArrayList<CommunityReservationStrategyEntity>();
}
