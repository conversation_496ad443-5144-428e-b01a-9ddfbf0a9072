package com.foshan.entity.community;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.foshan.entity.UserEntity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorColumn(name = "userType", discriminatorType = DiscriminatorType.STRING)
@DiscriminatorValue("COMMUNITY")
public class CommunityUserEntity extends UserEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2399552161486954104L;
	@Column(columnDefinition = "varchar(64) comment '职位'")
	private String position;
	@ManyToOne(targetEntity = CommunityDepartmentEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "departmentId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityDepartmentEntity department;
	
	@ManyToMany(targetEntity = CommunityPropertyEntity.class, fetch = FetchType.LAZY)
	@JoinTable(name = "t_community_property_user", joinColumns = @JoinColumn(name = "userId", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "propertyId", referencedColumnName = "id"))
	private List<CommunityPropertyEntity> propertyList = new ArrayList<CommunityPropertyEntity>();
	
	@OneToMany(targetEntity = CommunityEventsEntity.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "handlerId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private List<CommunityEventsEntity> eventsList = new ArrayList<CommunityEventsEntity>();
}
