package com.foshan.entity.community;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.IEntityBean;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "t_community_property_service")
@org.hibernate.annotations.Table(appliesTo = "t_community_property_service",comment="报修服务") 
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityPropertyServiceEntity implements IEntityBean {
	/**
	 * 
	 */
	private static final long serialVersionUID = -786288711233968636L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;
	@Column(columnDefinition = "Timestamp default current_timestamp comment '创建时间'")
	protected Timestamp createTime;
	@Column(columnDefinition = "varchar(64) comment '报修号'")
	private String eventsCode;
	@Column(columnDefinition = "varchar(256) comment '标题'")
	private String title;
    @Column(columnDefinition = "datetime comment '报修时间'")
    private Date reportTime;
	@Column(columnDefinition = "varchar(32) comment '服务状态（新增、跟进、维修、完成）'")
	private String serviceState;
	@Column(columnDefinition = "varchar(512) comment '说明（备注）'")
	private String centent;
	@Column(columnDefinition = "varchar(32) comment '经办人（接待员）'")
	private String agent;
	@Column(columnDefinition = "varchar(32) comment '申请人'")
	private String client;
	@Column(columnDefinition = "varchar(100) comment '联系电话'")
	private String phone;
//	@Column(columnDefinition = "int(20) comment '资产Id'")
//	private Integer propertyId;
	@Column(columnDefinition = "varchar(512) comment '维修地点'")
	private String address;
	@Column(columnDefinition = "varchar(32) comment '报修类别'")
	private String serviceType;
	@Column(columnDefinition = "varchar(512) comment '报修内容'")
	private String  reportContent;
	@Column(columnDefinition = "varchar(32) comment '主管部门'")
	private String competentDepartment;
	@Column(columnDefinition = "varchar(512) comment '维修员'")
	private String handler;
    @Column(columnDefinition = "datetime comment '派工时间'")
    private Date dispatchingTime;
    @Column(columnDefinition = "datetime comment '完成时间'")
    private Date completionTime;
	@Column(columnDefinition = "varchar(512) comment '维修内容'")
	private String serviceContent;
	@Column(columnDefinition = "varchar(32) comment '验收员'")
	private String receiver;
	@Column(columnDefinition = "varchar(32) comment '服务评价（不满意、基本满意、满意、很满意、非常满意）'")
	private String  serviceEvaluation;
	@Column(columnDefinition = "decimal(10,2) comment '人工费'")
	private BigDecimal laborCost;
	@Column(columnDefinition = "decimal(10,2) comment '材料费'")
	private BigDecimal materialCost;
	@Column(columnDefinition = "int(2) comment '是否入账 0否 1是'")
	private Integer isGeneratedBills;
	@Column(columnDefinition = "int(20) comment '收费项目Id'")
	private Integer payItemsId;
	@Column(columnDefinition = "varchar(64) comment '旧表ID'")
	private String oldId;
	@Column(columnDefinition = "varchar(512) comment '旧表数据'")
	private String oldData;
	@Column(columnDefinition = "varchar(512) comment '回访意见'")
	private String feedback;
	@Column(columnDefinition = "varchar(32) comment '回访人'")
	private String visitCommissione;
	@Column(columnDefinition = "int(2) comment '订单类别：0-有偿单，1-外发单'")
	private Integer orderCategory;
	
	@ManyToOne(targetEntity = CommunityEstateEntity.class, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@JoinColumn(name = "propertyId", referencedColumnName = "id", nullable = true)
	@JsonIgnore
	private CommunityEstateEntity estate;
}
