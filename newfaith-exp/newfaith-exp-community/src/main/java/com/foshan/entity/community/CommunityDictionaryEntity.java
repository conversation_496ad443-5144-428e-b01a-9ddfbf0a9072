package com.foshan.entity.community;


import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.foshan.entity.DictionaryEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("C")
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class CommunityDictionaryEntity extends DictionaryEntity{
	/**
	 * 
	 */
	private static final long serialVersionUID = -5500841380448622273L;

}
