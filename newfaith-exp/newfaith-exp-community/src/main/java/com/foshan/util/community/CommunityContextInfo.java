package com.foshan.util.community;

import java.util.Date;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import com.foshan.util.ContextInfo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Component
@PropertySource(value = "classpath:application.yml",encoding="utf-8")
@Getter
@Setter
@NoArgsConstructor
public class CommunityContextInfo extends ContextInfo {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2201952846597275599L;

	@Value("${faith.memberRoleId}")
	public String memberRoleId;

	@Value("${community.paymentServiceUrl}")
	public String paymentServiceUrl;
	@Value("${community.refundServiceUrl}")
	public String refundServiceUrl;
	@Value("${community.paymentMerchantCode}")
	public String paymentMerchantCode;
	@Value("${community.paymentSecret}")
	public String paymentSecret;
	@Value("${community.paymentNotifyUrl}")
	public String paymentNotifyUrl;
	@Value("${community.paymentReturnUrl}")
	public String paymentReturnUrl;
	@Value("${community.refundNotifyUrl}")
	public String refundNotifyUrl;
	@Value("${community.advanceReceiptsMonth}")
	public Integer advanceReceiptsMonth;
	@Value("${community.queryPaymentResultServiceUrl}")
	public String queryPaymentResultServiceUrl;
	@Value("${community.bankNo}")
	public String bankNo;
	@Value("${community.invoiceUrl}")
	public String invoiceUrl;
	@Value("${community.kingdeeFilePath}")
	public String kingdeeFilePath;
	@Value("${community.kingdeeBaseImportStartDate}")
	public String kingdeeBaseImportStartDate;
	@Value("${community.kingdeeUrl}")
	public String kingdeeUrl;
	@Value("${community.cutoverDate}")
	public Date cutoverDate;
//	@Value("${community.benefit.orginPrice}")
//	public BigDecimal orginPrice;
//	@Value("${community.benefit.benefitPayitemId}")
//	public Integer benefitPayitemId;
//	@Value("${community.benefit.benefitProlicyStartTime}")
//	public Date benefitProlicyStartTime;
}
