package com.foshan.util.community;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.singularsys.jep.Jep;
import com.singularsys.jep.JepException;
import com.singularsys.jep.bigdecimal.BigDecComponents;
import com.singularsys.jep.parser.Node;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JepUtil {

	public static BigDecimal cal(String formula, Map<String, BigDecimal> paraMap,int scale) {
		BigDecimal result = null;

		Jep jep = new Jep(new BigDecComponents((MathContext.DECIMAL64)));
		// 参数赋值
		addParas(jep, paraMap);
		try {
			Node node = jep.parse(formula.toString());
			Object value;
			value = jep.evaluate(node);
			result = new BigDecimal(value.toString());
			result = result.setScale(scale, RoundingMode.HALF_UP);
		} catch (JepException e) {
			StringBuilder para = new StringBuilder();
			paraMap.keySet().forEach(o -> {
				para.append(o + "=" + paraMap.get(o) + ",");
			});
			log.error(e.getMessage() + ":公式【" + formula + "】计算错误,参数【" + para.substring(0, para.length() - 1) + "】");
		}
		return result;
	}

	private static void addParas(Jep jep, Map<String, BigDecimal> paraMap) {
		paraMap.keySet().forEach(o -> {
			try {
				jep.addVariable(o, paraMap.get(o));
			} catch (JepException e) {
				log.error(e.getMessage() + ":参数(" + o + ")添加失败！！！");
			}
		});
	}

	public static BigDecimal cal(String formula, int scale,BigDecimal... paraValues) {
		BigDecimal result = null;
		if (StringUtils.isNotEmpty(formula) && null != paraValues && paraValues.length > 0) {
			String[] paras = parseParas(formula.split("[(\\\\+)\\*/]"));
			if (paras.length == paraValues.length) {
				Map<String, BigDecimal> paraMap = new HashMap<>();
				Integer index = 0;
				for (String para : paras) {
					paraMap.put(para, paraValues[index]);
					index++;
				}
				
				result = cal(formula,paraMap,scale);
				return result;

			} else {
				log.error("参数数量不正确！！！");
				result = new BigDecimal("0");
			}
		} else {
			log.error("公式或者参数不能为空！！！");
			result = new BigDecimal("0");
		}
		return result;
	}

	private static String[] parseParas(String[] paras) {
		StringBuffer temp = new StringBuffer();
		for (String para : paras) {
			if (StringUtils.isNotEmpty(para)) {
				temp.append(para + ";");
			}
		}
		String[] result = temp.toString().split(";");
		return result;
	}

}
