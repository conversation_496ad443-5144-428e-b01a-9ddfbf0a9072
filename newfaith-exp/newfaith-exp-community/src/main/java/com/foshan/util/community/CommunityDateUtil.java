package com.foshan.util.community;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import com.foshan.util.DateUtil;

public class CommunityDateUtil extends DateUtil {

	/*
	 * 计算两个日期间隔天数
	 */
	public static BigDecimal subDate(String startDateStr, String endDateStr) {
		LocalDate startDate = LocalDate.parse(startDateStr);
		LocalDate endDate = LocalDate.parse(endDateStr);
		Long days = ChronoUnit.DAYS.between(startDate, endDate) + 1;
		return new BigDecimal(days.toString());
	}

	/*
	 * 根据给定的日期获取该日期当月最大天数
	 */
	public static BigDecimal monthDays(String billingDate) {
		LocalDate now = LocalDate.parse(billingDate);
		Long days = ChronoUnit.DAYS.between(now.with(TemporalAdjusters.firstDayOfMonth()),
				now.with(TemporalAdjusters.lastDayOfMonth())) + 1;
		return new BigDecimal(days.toString());
	}

	/*
	 * 根据给定时间获取该日期的计费天数
	 */
	public static BigDecimal billingDays(String billingDate) {
		LocalDate now = LocalDate.parse(billingDate);
		Long days = ChronoUnit.DAYS.between(now, now.with(TemporalAdjusters.lastDayOfMonth())) + 1;
		return new BigDecimal(days.toString());
	}

	/*
	 * 获取某个时间段内的计费月份清单
	 */
	public static Map<String, String> cycleMonth(String startDateStr, String endDateStr) {
		Map<String, String> monthMap = new TreeMap<>();

		LocalDate startDate = LocalDate.parse(startDateStr).with(TemporalAdjusters.firstDayOfMonth());
		LocalDate endDate = LocalDate.parse(endDateStr).with(TemporalAdjusters.firstDayOfMonth());

		for (LocalDate i = startDate; i.isBefore(endDate)||i.isEqual(endDate);) {
			monthMap.put(i.toString(),i.with(TemporalAdjusters.lastDayOfMonth()).toString());
			i = i.plus(1, ChronoUnit.MONTHS);
		}

		return monthMap;
	}

	/*
	 * 获取给定时间及未来数量的计费月份清单 例如：给定时间2021-01-03，未来半年的cycle数量为6，则返回从2021-01-01开始未来半年的月份列表
	 */
	public static Map<String, String> cycleMonth(String startDateStr, Integer cycle) {
		LocalDate startDate = LocalDate.parse(startDateStr).with(TemporalAdjusters.firstDayOfMonth());
		LocalDate endDate = startDate.plus(cycle-1, ChronoUnit.MONTHS).with(TemporalAdjusters.lastDayOfMonth());
		return cycleMonth(startDate.toString(), endDate.toString());
	}
	
	/*
	 * Date转LocalDate
	 */
	public static LocalDate dateToLocalDate(Date date) {
		ZoneId zoneId = ZoneId.systemDefault();
		Instant instant = date.toInstant();
		return instant.atZone(zoneId).toLocalDate();
	}
	
	/*
	 * LocalDate转Date
	 */
	public static Date localDateToDate(LocalDate localDate) {
		ZoneId zoneId = ZoneId.systemDefault();
		return Date.from(localDate.atStartOfDay().atZone(zoneId).toInstant());
	}
	
	/**
	 * 获取两段日期的交集,
	 * 需满足:startDate1<=endDate1,startDate2<=endDate2。
	 * 
	 * @param startDate1 开始日期1
	 * @param endDate1   结束日期1
	 * @param startDate2 开始日期2
	 * @param endDate2   结束日期2
	 * @return 1、2两段时间的交集,List<LocalDate>，每1个元素为交集的开始时间，第2个元素为交集的结束时间；如果不存在交集返回的List的Size=0。
	 */
	public static List<LocalDate> getInterSection(LocalDate startDate1, LocalDate endDate1, LocalDate startDate2, LocalDate endDate2) {
		List<LocalDate> resultList =  new  ArrayList<LocalDate>();
		if(startDate1.isAfter(endDate2) || startDate2.isAfter(endDate1) || startDate1.isAfter(endDate1) || startDate2.isAfter(endDate2)) {
			return resultList;
		}
		else {
			if((startDate1.isAfter(startDate2) || startDate1.isEqual(startDate2)) && (startDate1.isBefore(endDate2) || startDate1.isEqual(endDate2))) {
				resultList.add(startDate1);
				if(endDate1.isAfter(endDate2)) {
					resultList.add(endDate2);
				}
				else {
					resultList.add(endDate1);
				}
			}
			else if((startDate1.isBefore(startDate2) ||  startDate1.isEqual(startDate2))  
					&& (endDate1.isAfter(endDate2) || endDate1.isEqual(endDate2))) {
				resultList.add(startDate2);
				resultList.add(endDate2);
			}
			else if((endDate1.isAfter(startDate2) || endDate1.isEqual(startDate2)) && (endDate1.isBefore(endDate2) || endDate1.isEqual(endDate2))) {
				if(startDate1.isBefore(startDate2)) {
					resultList.add(startDate2);
				}
				else {
					resultList.add(startDate1);
				}
				resultList.add(endDate1);
			}
		}
		return resultList;
	}


}
