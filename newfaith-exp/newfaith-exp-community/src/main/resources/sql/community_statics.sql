------ 查询应收报表 ------
--1、获取分区查询sql
SELECT districtCode,CONCAT(
  'select districtCode,districtName,buildingName,',
  'unitCode,receivableDate,userName,estateState,bankName,bankAccount,accountName,',
  'SUM(receivableamount) AS totalReceivableAmount,',
  'SUM(receivedamount) AS totalReceivedAmount,',
  'SUM(arrears) AS totalArrears,buildingArea,recordDate,billingDate,comment,',
  CONCAT(
    GROUP_CONCAT(CONCAT('MAX(CASE payitemsname WHEN ''',payitemsname,''' then receivableamount else 0 end) as ',payitemsname)),
    ',',
    GROUP_CONCAT(CONCAT('MAX(CASE payitemsname WHEN ''',payitemsname,''' then receivedAmount else 0 end) as ',payitemsname,'已收金额')),
    ',',
    GROUP_CONCAT(CONCAT('MAX(CASE payitemsname WHEN ''',payitemsname,''' then arrears else 0 end) as ',payitemsname,'欠费'))),
    ' from v_community_receivables where districtcode=',
    districtcode,
    '',
    '',
    '',
    ' AND receivabledate>=''2023-01-01'' AND receivabledate<=''2023-01-10'' GROUP BY unitcode,receivabledate ',
    'ORDER BY districtOrder,buildingOrder,unitcode,receivabledate') AS subSql 
FROM (
  SELECT DISTINCT districtCode,payitemsname 
  FROM v_community_receivables 
  WHERE receivabledate>='2023-01-01' 
  AND receivabledate<='2023-01-10'     
  GROUP BY unitcode,receivabledate,payitemsname) aa 
GROUP BY districtcode

--2、分区查询sql
SELECT districtCode,districtName,buildingName,unitCode,receivableDate,userName,estateState,bankName,bankAccount,accountName,
SUM(receivableamount) AS totalReceivableAmount,SUM(receivedamount) AS totalReceivedAmount,SUM(arrears) AS totalArrears,
buildingArea,recordDate,billingDate,COMMENT,
MAX(CASE payitemsname WHEN '工程维修' THEN receivableamount ELSE 0 END) AS 工程维修,
MAX(CASE payitemsname WHEN '损害财产赔偿款' THEN receivableamount ELSE 0 END) AS 损害财产赔偿款,
MAX(CASE payitemsname WHEN '工程维修' THEN receivedAmount ELSE 0 END) AS 工程维修已收金额,
MAX(CASE payitemsname WHEN '损害财产赔偿款' THEN receivedAmount ELSE 0 END) AS 损害财产赔偿款已收金额,
MAX(CASE payitemsname WHEN '工程维修' THEN arrears ELSE 0 END) AS 工程维修欠费,
MAX(CASE payitemsname WHEN '损害财产赔偿款' THEN arrears ELSE 0 END) AS 损害财产赔偿款欠费 
FROM v_community_receivables 
WHERE districtcode=01 
AND receivabledate>='2023-01-01' 
AND receivabledate<='2023-01-10' 
GROUP BY unitcode,receivabledate 
ORDER BY districtOrder,buildingOrder,unitcode,receivabledate




------ 查询应收未收报表 ------
--1、获取分区查询sql
SELECT DISTINCT districtcode,
  CONCAT(
    'select aa.districtName,aa.buildingName,aa.buildingType,aa.unitCode,aa.userName,aa.homePhone,aa.buildingArea,aa.amount3,aa.amount36,aa.amount6,','aa.totalAmount,aa.bankName,aa.bankAccount,aa.accountName,aa.districtCode,aa.estateState,',
    GROUP_CONCAT(DISTINCT CONCAT('MAX(CASE payitemsname WHEN ''',payitemsname,''' then CONCAT(DATE_FORMAT(startDate,''%Y.%m''),''～'',DATE_FORMAT(endDate,''%Y.%m'')) ELSE '''' END) as ',payitemsname,'属期,MAX(CASE payitemsname WHEN ''',payitemsname,''' THEN fee ELSE '''' END)  AS ',payitemsname)),
    ' FROM (SELECT aaa.districtname,aaa.districtorder,aaa.districtCode,aaa.buildingname,aaa.buildingType,aaa.buildingOrder,aaa.unitcode,aaa.username,aaa.idcard,aaa.homephone,aaa.buildingarea,',
    'aaa.payitemsname,aaa.bankname,aaa.bankaccount,aaa.accountname,aaa.estateState,',
    'MIN(aaa.receivabledate) AS startDate,MAX(aaa.receivabledate) AS endDate,SUM(aaa.amount1) AS fee,aaa.amount3,aaa.amount36,aaa.amount6,aaa.totalAmount FROM (SELECT a.id as districtid,a.districtName,a.districtOrder,a.districtCode,b.buildingName,b.buildingType,b.buildingOrder,c.unitCode,g.username,g.idcard,g.homephone,c.buildingArea,d.chargecategory,CASE c.estateState WHEN 0 THEN ''未收楼'' WHEN 1 THEN ''已入住'' WHEN 2 THEN ''装修中'' WHEN 3 THEN ''出租'' WHEN 4 THEN ''离退'' WHEN 5 THEN ''自住'' WHEN 6 THEN ''空置'' END AS estatestate,h.bankname,h.bankaccount,h.accountname,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1,totalamount,amount3,amount36,amount6 FROM t_community_district a INNER JOIN t_community_building b ON a.id<>11 and a.districtcode=''',
    districtCode,
    ''' AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid INNER JOIN t_community_property c ON b.id=c.buildingid LEFT JOIN t_community_payment_account h ON c.paymentaccountid=h.id INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY(''2023-10-31'')   INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id INNER JOIN t_community_member_property f ON c.id=f.propertyid AND f.iscurrentowner=1 AND f.iscurrentmember=1 INNER JOIN t_account g ON f.memberid=g.id INNER JOIN (SELECT aa.id,SUM(aa.amount1) AS totalAmount FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e WHERE a.id<>11  and a.districtcode=''',
    districtCode,
    ''' AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY(''2023-10-31'')) aa GROUP BY aa.unitCode) a1 ON c.id=a1.id left JOIN (SELECT aa.id,SUM(aa.amount1) AS amount3 FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e WHERE a.id<>11   and a.districtcode=''',
    districtCode,
    ''' AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 AND d.receivabledate>=DATE_ADD(LAST_DAY(''2023-10-31''),INTERVAL-3 MONTH) AND d.receivableDate<=LAST_DAY(''2023-10-31'')) aa GROUP BY aa.unitCode) a2 ON a1.id=a2.id LEFT JOIN(SELECT aa.id,SUM(aa.amount1) AS amount36 FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e WHERE a.id<>11   and a.districtcode=''',
    districtCode,
    ''' AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 AND d.receivabledate>=DATE_ADD(LAST_DAY(''2023-10-31''), INTERVAL-6 MONTH) AND d.receivabledate<DATE_ADD(LAST_DAY(''2023-10-31''), INTERVAL-3 MONTH)) aa GROUP BY aa.unitCode) a3 ON a1.id=a3.id LEFT JOIN(SELECT aa.id,SUM(aa.amount1) AS amount6 FROM(SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e WHERE a.id<>11   and a.districtcode=''',
    districtCode,
    '''   AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 AND d.receivabledate<DATE_ADD(LAST_DAY(''2023-10-31''),INTERVAL-6 MONTH) ) aa GROUP BY aa.unitCode) a4 ON a1.id=a4.id ) aaa GROUP BY aaa.unitcode,aaa.payitemsname) aa GROUP BY aa.unitcode order by aa.districtorder,aa.buildingOrder,aa.unitCode'
    ) AS subsql 
FROM t_community_district a 
INNER JOIN t_community_building b ON a.id<>11 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid 
INNER JOIN t_community_property c ON b.id=c.buildingid 
INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY('2023-10-31')   
INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id 
INNER JOIN t_community_member_property f ON c.id=f.propertyid AND f.iscurrentowner=1 AND f.iscurrentmember=1 
GROUP BY a.districtcode


--2、分区查询sql
SELECT 
  aa.districtName,aa.buildingName,aa.buildingType,aa.unitCode,aa.userName,aa.homePhone,aa.buildingArea,aa.amount3,aa.amount36,aa.amount6,
  aa.totalAmount,aa.bankName,aa.bankAccount,aa.accountName,aa.districtCode,aa.estateState,
  MAX(CASE payitemsname WHEN '二区车库公共电费' THEN CONCAT(DATE_FORMAT(startDate,'%Y.%m'),'～',DATE_FORMAT(endDate,'%Y.%m')) ELSE '' END) AS 二区车库公共电费属期,
  MAX(CASE payitemsname WHEN '二区车库公共电费' THEN fee ELSE '' END)  AS 二区车库公共电费,
  MAX(CASE payitemsname WHEN '车位管理费' THEN CONCAT(DATE_FORMAT(startDate,'%Y.%m'),'～',DATE_FORMAT(endDate,'%Y.%m')) ELSE '' END) AS 车位管理费属期,
  MAX(CASE payitemsname WHEN '车位管理费' THEN fee ELSE '' END)  AS 车位管理费,
  MAX(CASE payitemsname WHEN '车位管理费违约金' THEN CONCAT(DATE_FORMAT(startDate,'%Y.%m'),'～',DATE_FORMAT(endDate,'%Y.%m')) ELSE '' END) AS 车位管理费违约金属期,
  MAX(CASE payitemsname WHEN '车位管理费违约金' THEN fee ELSE '' END)  AS 车位管理费违约金,
  MAX(CASE payitemsname WHEN '车库公共水费分摊' THEN CONCAT(DATE_FORMAT(startDate,'%Y.%m'),'～',DATE_FORMAT(endDate,'%Y.%m')) ELSE '' END) AS 车库公共水费分摊属期,
  MAX(CASE payitemsname WHEN '车库公共水费分摊' THEN fee ELSE '' END)  AS 车库公共水费分摊,
  MAX(CASE payitemsname WHEN '车库公共电费' THEN CONCAT(DATE_FORMAT(startDate,'%Y.%m'),'～',DATE_FORMAT(endDate,'%Y.%m')) ELSE '' END) AS 车库公共电费属期,
  MAX(CASE payitemsname WHEN '车库公共电费' THEN fee ELSE '' END)  AS 车库公共电费 
  FROM (
    SELECT aaa.districtname,aaa.districtorder,aaa.districtCode,aaa.buildingname,aaa.buildingType,aaa.buildingOrder,aaa.unitcode,aaa.username,
    aaa.idcard,aaa.homephone,aaa.buildingarea,aaa.payitemsname,aaa.bankname,aaa.bankaccount,aaa.accountname,aaa.estateState,
    MIN(aaa.receivabledate) AS startDate,MAX(aaa.receivabledate) AS endDate,SUM(aaa.amount1) AS fee,aaa.amount3,aaa.amount36,aaa.amount6,aaa.totalAmount 
    FROM (
      SELECT a.id AS districtid,a.districtName,a.districtOrder,a.districtCode,b.buildingName,b.buildingType,b.buildingOrder,c.unitCode,
      g.username,g.idcard,g.homephone,c.buildingArea,d.chargecategory,
      CASE c.estateState WHEN 0 THEN '未收楼' WHEN 1 THEN '已入住' WHEN 2 THEN '装修中' WHEN 3 THEN '出租' WHEN 4 THEN '离退' WHEN 5 THEN '自住' WHEN 6 THEN '空置' END AS estatestate,
      h.bankname,h.bankaccount,h.accountname,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,
      (d.receivableamount-d.receivedamount) AS amount1,totalamount,amount3,amount36,amount6 
      FROM t_community_district a 
      INNER JOIN t_community_building b ON a.id<>11 AND a.districtcode='0' AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid 
      INNER JOIN t_community_property c ON b.id=c.buildingid 
      LEFT JOIN t_community_payment_account h ON c.paymentaccountid=h.id 
      INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY('2023-10-31')   
      INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id 
      INNER JOIN t_community_member_property f ON c.id=f.propertyid AND f.iscurrentowner=1 AND f.iscurrentmember=1 
      INNER JOIN t_account g ON f.memberid=g.id 
      INNER JOIN (
        SELECT aa.id,SUM(aa.amount1) AS totalAmount FROM(
          SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,
          d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 
          FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e 
          WHERE a.id<>11 AND a.districtcode='0' AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid 
          AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 
          AND d.receivableDate<=LAST_DAY('2023-10-31')
        ) aa GROUP BY aa.unitCode
       ) a1 ON c.id=a1.id 
       LEFT JOIN (
         SELECT aa.id,SUM(aa.amount1) AS amount3 FROM(
           SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,
           d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 
           FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e 
           WHERE a.id<>11 AND a.districtcode='0' AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid 
           AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 
           AND d.receivabledate>=DATE_ADD(LAST_DAY('2023-10-31'),INTERVAL-3 MONTH) 
           AND d.receivableDate<=LAST_DAY('2023-10-31')
         ) aa GROUP BY aa.unitCode
       ) a2 ON a1.id=a2.id 
       LEFT JOIN (
         SELECT aa.id,SUM(aa.amount1) AS amount36 FROM(
           SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,
           d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 
           FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e 
           WHERE a.id<>11 AND a.districtcode='0' AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid 
           AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 
           AND d.receivabledate>=DATE_ADD(LAST_DAY('2023-10-31'), INTERVAL-6 MONTH) 
           AND d.receivabledate<DATE_ADD(LAST_DAY('2023-10-31'), INTERVAL-3 MONTH)
         ) aa GROUP BY aa.unitCode
       ) a3 ON a1.id=a3.id 
       LEFT JOIN (
         SELECT aa.id,SUM(aa.amount1) AS amount6 FROM(
           SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,
           d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 
           FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e 
           WHERE a.id<>11 AND a.districtcode='0' AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid 
           AND c.id=d.estateid AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 
           AND d.receivabledate<DATE_ADD(LAST_DAY('2023-10-31'),INTERVAL-6 MONTH)
         ) aa GROUP BY aa.unitCode
       ) a4 ON a1.id=a4.id 
   ) aaa GROUP BY aaa.unitcode,aaa.payitemsname
) aa 
GROUP BY aa.unitcode 
ORDER BY aa.districtorder,aa.buildingOrder,aa.unitCode
  
  
------ 查询业主信息报表 ------
SELECT a.districtname,b.buildingname,c.unitcode,c.roomnumber,ROUND(c.buildingarea,2) AS buildingarea,ROUND(c.usablearea,2) AS usablearea,
ROUND(c.chargingarea,2) AS chargingarea,IF(c.additionalarea>0,ROUND(c.additionalarea,2),'') AS additionalarea,c.estatetype,
IF(REPLACE(JSON_EXTRACT(c.reservedField, '$.propertyParkingArea'), '"', '')>0,
ROUND(REPLACE(JSON_EXTRACT(c.reservedField, '$.propertyParkingArea'), '"', ''),2),'') AS propertyParkingArea,
IF(REPLACE(JSON_EXTRACT(c.reservedField, '$.propertyParkingNum'), '"', '')>0,
REPLACE(JSON_EXTRACT(c.reservedField, '$.propertyParkingNum'), '"', ''),'') AS propertyParkingNum,
IF(REPLACE(JSON_EXTRACT(c.reservedField, '$.defenceParkingArea'), '"', '')>0,
ROUND(REPLACE(JSON_EXTRACT(c.reservedField, '$.defenceParkingArea'), '"', ''),2),'') AS defenceParkingArea,
IF(REPLACE(JSON_EXTRACT(c.reservedField, '$.defenceParkingNum'), '"', '')>0,
REPLACE(JSON_EXTRACT(c.reservedField, '$.defenceParkingNum'), '"', ''),'') AS defenceParkingNum,
IF(d.terminationDate IS NOT NULL,'离退',
CASE c.estateState WHEN 0 THEN '未收楼' WHEN 1 THEN '已入住' WHEN 2 THEN '装修中' WHEN 3 THEN '出租' WHEN 4 THEN '离退' WHEN 5 THEN '自住' WHEN 6 THEN '空置' END) AS estatestate,
e.username,DATE_FORMAT(d.recorddate,'%Y-%m-%d') AS recorddate,DATE_FORMAT(d.billingdate,'%Y-%m-%d') AS billingdate,DATE_FORMAT(d.terminationdate,'%Y-%m-%d') AS terminationdate,
CASE d.membertype WHEN 0 THEN '业主' WHEN 1 THEN '住户成员' WHEN 2 THEN '租户' ELSE '' END AS memberType,
e.birthday,CASE e.sex WHEN 0 THEN '女' WHEN 1 THEN '男' WHEN 2 THEN '保密' ELSE '' END AS sex,
e.homePhone,e.idtype,e.idcard,e.contactperson,e.emergencycontact,d.buyersname,d.buyersaddress,d.buyersbankaccount,d.buyeremail,f.bankname,f.bankaccount,
f.accountName,a.districtCode,e.homeAddress,b.comment AS estateComment,d.comment AS memberComment 
FROM t_community_district a 
INNER JOIN t_community_building b ON a.id=b.districtId AND a.id<>11 AND a.id IN(4,5)  
INNER JOIN t_community_property c ON b.id=c.buildingid  
LEFT JOIN t_community_member_property d ON d.memberType<>1 AND c.id=d.propertyid AND d.parentpropertyid IS NULL 
LEFT JOIN t_account e ON d.memberid=e.id 
LEFT JOIN t_community_payment_account f ON c.paymentaccountid=f.id 
ORDER BY a.districtorder,b.buildingorder,c.unitcode


------ 查询应收减免报表 ------
SELECT a.districtcode,a.districtname,b.buildingname,c.unitcode,d.payitemsname,d.receivableamount,YEAR(d.receivabledate) AS receivableyear,
MONTH(d.receivabledate) AS receivablemonth,d.receivabledate,d.changedate,d.changeamount,d.comment,d.recorder 
FROM t_community_district a 
INNER JOIN t_community_building b ON a.id =b.districtid AND a.id<>11  
INNER JOIN t_community_property c ON b.id=c.buildingid 
INNER JOIN t_community_receivables_changes d ON d.state=1 AND d.changetype=0 AND c.id=d.estateid  
AND d.receivableDate>='2022-10-01 00:00:00'  
AND d.receivableDate<='2022-10-31 23:59:59'  
AND d.changeDate>='2023-01-01 00:00:00'  
AND d.changeDate<='2023-12-31 23:59:59' 
ORDER BY a.districtOrder,b.buildingOrder,c.unitCode,d.changeDate,d.receivableDate  

------ 查询收款透视表 ------
--1、获取分组查询sql
SELECT districtcode,CONCAT(
  'select districtName,receiptCode,unitCode,buildingName,receivableDate,receivableMonth,receiptDate,paymentMethod,agent,',
  GROUP_CONCAT(CONCAT('MAX(CASE payitemsname WHEN ''',payitemsname,''' then currentAmount else '''' end) as ',payitemsname)),
  ' from (select a.districtorder,a.districtname,a.receiptcode,a.unitcode,a.buildingname,a.buildingorder,a.receivabledate,a.receivablemonth,',
  'a.receiptDate,a.paymentMethod,a.agent,a.payitemsname,SUM(a.currentAmount) AS currentAmount,a.districtCode ',
  'FROM v_receipt a WHERE a.districtcode=''',
  districtcode,
  '''',
  ' and districtid in(10) ',
  ' and a.receivableDate>=''2010-10-01 00:00:00'' ',
  'AND a.receivableDate<=''2022-10-31 23:59:59'' AND a.receiptDate>=''2010-01-01 00:00:00'' ',
  'AND a.receiptDate<=''2023-12-31 23:59:59'' GROUP BY a.receiptCode,a.payitemsname,a.receivabledate) aa ',
  'GROUP BY aa.receiptCode,aa.receivabledate ORDER BY aa.districtorder,aa.buildingorder,aa.unitcode,aa.receivabledate') AS subsql 
FROM(
  SELECT DISTINCT a.districtcode,a.payitemsname 
  FROM v_receipt a 
  WHERE 1=1 AND districtid IN(10) AND a.receivableDate>='2010-10-01 00:00:00' AND a.receivableDate<='2022-10-31 23:59:59' 
  AND a.receiptDate>='2010-01-01 00:00:00' AND a.receiptDate<='2023-12-31 23:59:59' 
  GROUP BY a.receiptCode,a.payitemsname
) aa 
GROUP BY districtcode

--2、分组查询sql
SELECT districtName,receiptCode,unitCode,buildingName,receivableDate,receivableMonth,receiptDate,paymentMethod,agent,
MAX(CASE payitemsname WHEN '车位管理费' THEN currentAmount ELSE '' END) AS 车位管理费,
MAX(CASE payitemsname WHEN '物业管理费' THEN currentAmount ELSE '' END) AS 物业管理费 
FROM (
  SELECT a.districtorder,a.districtname,a.receiptcode,a.unitcode,a.buildingname,a.buildingorder,a.receivabledate,
  a.receivablemonth,a.receiptDate,a.paymentMethod,a.agent,a.payitemsname,SUM(a.currentAmount) AS currentAmount,a.districtCode 
  FROM v_receipt a 
  WHERE a.districtcode='16' AND districtid IN(10)  
  AND a.receivableDate>='2010-10-01 00:00:00' 
  AND a.receivableDate<='2022-10-31 23:59:59' 
  AND a.receiptDate>='2010-01-01 00:00:00' 
  AND a.receiptDate<='2023-12-31 23:59:59' 
  GROUP BY a.receiptCode,a.payitemsname,a.receivabledate
) aa 
GROUP BY aa.receiptCode,aa.receivabledate 
ORDER BY aa.districtorder,aa.buildingorder,aa.unitcode,aa.receivabledate

------ 查询退款数据 ------
--1、获取分组查询sql
SELECT a.districtcode,CONCAT(
  'select aa.districtCode,aa.districtName,aa.buildingName,',
  'aa.unitcode,aa.roomcode,aa.roomnumber,aa.payername,sum(changeAmount) as totalRefund,',
  'sum(arrears) as totalArrears,sum(amount) as totalReceivable,',
  CONCAT(
    GROUP_CONCAT(DISTINCT CONCAT('MAX(CASE payitemsname WHEN ''',g.payitemsname,''' then changeamount else 0 end) as ',g.payitemsname,'退款金额')),
    ',',
    GROUP_CONCAT(DISTINCT CONCAT('MAX(CASE payitemsname WHEN ''',g.payitemsname,''' then arrears else 0 end) as ',g.payitemsname,'未退金额'))
  ),
  ' ',
  'from (SELECT a.districtcode,a.districtname,b.buildingname,b.buildingorder,c.unitcode,CONCAT(c.floor,c.roomnumber) AS roomcode,',
  'c.roomnumber,e.payername,g.payitemsname,sum(e.amount) as amount,sum(d.changeamount) as changeAmount,',
  'sum(e.amount-d.changeamount) AS arrears,a.districtorder ',
  'FROM t_community_district a INNER JOIN t_community_building b ON a.id= b.districtid AND a.id<>11 and a.districtcode=',
  a.districtcode,
  ' ',
  ' and districtid in(10) ',
  'INNER JOIN t_community_property c ON b.id=c.buildingid ',
  'INNER JOIN t_community_receivables_changes d ON c.id=d.estateid AND d.changetype=2 ',
  'INNER JOIN t_community_receipt e ON d.receiptid=e.id ',
  ' and e.receiptDate>=''2022-10-01 00:00:00'' and e.receiptDate<=''2022-10-31 23:59:59'' ',
  'INNER JOIN t_community_receipt_receivables f ON e.id=f.receiptid ',
  'INNER JOIN t_community_receivables g ON f.receivablesid=g.id ',
  'group by c.unitcode,g.payitemsname ) aa group by aa.unitcode ',
  'ORDER BY aa.districtorder,aa.buildingorder,aa.unitcode') AS subsql 
FROM t_community_district a 
INNER JOIN t_community_building b ON a.id= b.districtid AND a.id<>11 AND districtid IN(10) 
INNER JOIN t_community_property c ON b.id=c.buildingid 
INNER JOIN t_community_receivables_changes d ON c.id=d.estateid AND d.changetype=2  
INNER JOIN t_community_receipt e ON d.receiptid=e.id AND e.receiptDate>='2022-10-01 00:00:00' AND e.receiptDate<='2022-10-31 23:59:59' 
INNER JOIN t_community_receipt_receivables f ON e.id=f.receiptid 
INNER JOIN t_community_receivables g ON f.receivablesid=g.id   
GROUP BY a.districtcode

--2、分组查询sql
SELECT aa.districtCode,aa.districtName,aa.buildingName,aa.unitcode,aa.roomcode,aa.roomnumber,aa.payername,
SUM(changeAmount) AS totalRefund,SUM(arrears) AS totalArrears,SUM(amount) AS totalReceivable,
MAX(CASE payitemsname WHEN '装修保证金' THEN changeamount ELSE 0 END) AS 装修保证金退款金额,
MAX(CASE payitemsname WHEN '装修保证金' THEN arrears ELSE 0 END) AS 装修保证金未退金额 
FROM (
  SELECT a.districtcode,a.districtname,b.buildingname,b.buildingorder,c.unitcode,
  CONCAT(c.floor,c.roomnumber) AS roomcode,c.roomnumber,e.payername,g.payitemsname,
  SUM(e.amount) AS amount,SUM(d.changeamount) AS changeAmount,SUM(e.amount-d.changeamount) AS arrears,a.districtorder 
  FROM t_community_district a 
  INNER JOIN t_community_building b ON a.id= b.districtid AND a.id<>11 AND a.districtcode=16  AND districtid IN(10) 
  INNER JOIN t_community_property c ON b.id=c.buildingid 
  INNER JOIN t_community_receivables_changes d ON c.id=d.estateid AND d.changetype=2 
  INNER JOIN t_community_receipt e ON d.receiptid=e.id AND e.receiptDate>='2022-10-01 00:00:00' AND e.receiptDate<='2022-10-31 23:59:59' 
  INNER JOIN t_community_receipt_receivables f ON e.id=f.receiptid 
  INNER JOIN t_community_receivables g ON f.receivablesid=g.id 
  GROUP BY c.unitcode,g.payitemsname 
) aa 
GROUP BY aa.unitcode 
ORDER BY aa.districtorder,aa.buildingorder,aa.unitcode



------ 查询入住率 ------
SELECT bb.districtcode,bb.districtname,bb.buildingname,
MAX(CASE estatestate WHEN 6 THEN buildingarea ELSE 0 END)+MAX(CASE estatestate WHEN 1 THEN buildingarea ELSE 0 END) AS 总面积,
MAX(CASE estatestate WHEN 1 THEN buildingarea ELSE 0 END)  AS 入住面积,
MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END) AS 总单元数,
MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)  AS 入住单元数,
MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END)  AS 空置单元数,
ROUND(MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)/(MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END))*100,2) AS 入住率,
ROUND(MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END)/(MAX(CASE estatestate WHEN 1 THEN estatecount ELSE 0 END)+MAX(CASE estatestate WHEN 6 THEN estatecount ELSE 0 END))*100,2) AS 空置率 
FROM(
  SELECT aa.districtcode,aa.districtname,aa.districtorder,aa.buildingname,aa.buildingorder,COUNT(aa.unitcode) AS estatecount,SUM(aa.buildingarea) AS buildingarea,aa.estatestate 
  FROM(
    SELECT a.districtcode,a.districtname,a.districtorder,b.id,b.buildingname,b.buildingorder,c.unitcode,IF(c.estatestate<6,1,IF(iscurrentmember IS NOT NULL,1,6)) AS estatestate,
    c.estatetype,c.buildingarea,d.billingdate,d.membertype,d.iscurrentmember,d.memberid 
    FROM t_community_district a 
    INNER JOIN t_community_building b ON a.id=b.districtId AND a.id<>11  
    INNER JOIN t_community_property c ON b.id=c.buildingid 
    LEFT JOIN t_community_member_property d ON c.id=d.propertyid AND (d.terminationDate IS NULL OR d.terminationDate>NOW()) 
    GROUP BY c.unitcode
  ) aa 
  GROUP BY aa.districtcode,aa.buildingname,aa.estatestate 
) bb 
GROUP BY bb.districtcode,bb.buildingname 
ORDER BY bb.districtorder,bb.buildingorder



------ 应收未收措施表 ------
SELECT aa.districtcode,aa.districtname,aa.unitcode,aa.username,CONCAT(DATE_FORMAT(MIN(aa.startdate),'%Y-%m'),'~',DATE_FORMAT(MAX(aa.enddate),'%Y-%m')) AS period,
SUM(aa.arrears) AS totalArrears,
MAX(CASE aa.chargecategory WHEN '违约金' THEN aa.arrears ELSE '' END ) AS subarrears,
GROUP_CONCAT(aa.chargecategory) AS totalChargecategory,aa.noReceivablesMeasures,aa.measures1,aa.measures2,aa.estateid 
FROM(
  SELECT a.districtcode,a.districtname,a.districtOrder,a.buildingOrder,a.unitcode,a.username,a.chargecategory,SUM(a.receivableamount-a.receivedamount) AS arrears,
  MIN(a.receivabledate) AS startdate,MAX(a.receivabledate) AS enddate,a.noReceivablesMeasures,a.measures1,a.measures2,a.estateid FROM (
    SELECT a.id AS districtid,a.districtName,a.districtOrder,a.districtCode,b.buildingName,b.buildingType,b.buildingOrder,c.unitCode,g.username,g.idcard,g.homephone,c.buildingArea,d.chargecategory,
    CASE c.estateState WHEN 0 THEN '未收楼' WHEN 1 THEN '已入住' WHEN 2 THEN '装修中' WHEN 3 THEN '出租' WHEN 4 THEN '离退' WHEN 5 THEN '自住' WHEN 6 THEN '空置' END AS estatestate,
    h.bankname,h.bankaccount,h.accountname,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1,totalamount,amount3,amount36,amount6,
    IF(LENGTH(REPLACE(JSON_EXTRACT(reservedField,'$.noReceivablesMeasures'),'[]',''))=0 OR JSON_EXTRACT(reservedField,'$.noReceivablesMeasures') IS NULL,'',REPLACE(REPLACE(REPLACE(JSON_EXTRACT(reservedField,'$.noReceivablesMeasures'),'[',''),']',''),'"','')) AS noReceivablesMeasures,
    IF(LENGTH(REPLACE(JSON_EXTRACT(reservedField,'$.measures1'),'[]',''))=0 OR JSON_EXTRACT(reservedField,'$.measures1') IS NULL,'',REPLACE(REPLACE(REPLACE(JSON_EXTRACT(reservedField,'$.measures1'),'[',''),']',''),'"','')) AS measures1,
    IF(LENGTH(REPLACE(JSON_EXTRACT(reservedField,'$.measures2'),'[]',''))=0 OR JSON_EXTRACT(reservedField,'$.measures2') IS NULL,'',REPLACE(REPLACE(REPLACE(JSON_EXTRACT(reservedField,'$.measures2'),'[',''),']',''),'"','')) AS measures2,
    c.id AS estateid 
    FROM t_community_district a 
    INNER JOIN t_community_building b ON a.id<>11 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid 
    INNER JOIN t_community_property c ON b.id=c.buildingid 
    LEFT JOIN t_community_payment_account h ON c.paymentaccountid=h.id 
    INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 AND d.receivableDate<=LAST_DAY('2023-10-31')   
    INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id 
    INNER JOIN t_community_member_property f ON c.id=f.propertyid AND f.iscurrentowner=1 AND f.iscurrentmember=1 
    INNER JOIN t_account g ON f.memberid=g.id 
    INNER JOIN (
      SELECT aa.id,SUM(aa.amount1) AS totalAmount FROM(
        SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 
        FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e 
        WHERE a.id<>11 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid 
        AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 
        AND d.receivableDate<=LAST_DAY('2023-10-31')
      ) aa 
      GROUP BY aa.unitCode
    ) a1 ON c.id=a1.id 
    LEFT JOIN (
      SELECT aa.id,SUM(aa.amount1) AS amount3 FROM(
        SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 
        FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e 
        WHERE a.id<>11 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid 
        AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 
        AND d.receivabledate>=DATE_ADD(LAST_DAY('2023-10-31'),INTERVAL-3 MONTH) 
        AND d.receivableDate<=LAST_DAY('2023-10-31')
      ) aa GROUP BY aa.unitCode
    ) a2 ON a1.id=a2.id 
    LEFT JOIN (
      SELECT aa.id,SUM(aa.amount1) AS amount36 FROM(
        SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 
        FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e 
        WHERE a.id<>11 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid 
        AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 
        AND d.receivabledate>=DATE_ADD(LAST_DAY('2023-10-31'),INTERVAL-6 MONTH) 
        AND d.receivabledate<DATE_ADD(LAST_DAY('2023-10-31'), INTERVAL-3 MONTH)
      ) aa GROUP BY aa.unitCode
    ) a3 ON a1.id=a3.id 
    LEFT JOIN(
      SELECT aa.id,SUM(aa.amount1) AS amount6 FROM(
        SELECT c.id,c.unitCode,d.payitemsname,d.receivabledate,d.receivableamount,d.receivedamount,(d.receivableamount-d.receivedamount) AS amount1 
        FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e 
        WHERE a.id<>11 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid 
        AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id AND d.receivableamount-d.receivedamount<>0 
        AND d.receivabledate<DATE_ADD(LAST_DAY('2023-10-31'),INTERVAL-6 MONTH) 
      ) aa GROUP BY aa.unitCode
    ) a4 ON a1.id=a4.id 
  ) a 
  WHERE a.receivabledate<='2023-10-31' 
  GROUP BY a.unitcode,a.chargecategory
) aa 
GROUP BY aa.unitcode 
ORDER BY aa.districtorder,aa.buildingOrder,aa.unitCode

------ 应收未收分析表 ------
SELECT aa.districtname,aa.管理费数量,aa.管理费欠费,aa.公摊费数量,aa.公摊费欠费,aa.代收水电数量,aa.代收水电欠费,aa.违约金数量,
aa.违约金欠费,aa.车位费数量,aa.车位费欠费,bb.amount3num,bb.amount3,cc.amount6num,cc.amount6 
FROM(
  SELECT districtid,districtname,districtorder,buildingtype,
  MAX(CASE payitemsname WHEN '管理费' THEN unitnum ELSE '' END)  AS 管理费数量,
  MAX(CASE payitemsname WHEN '管理费' THEN arrears ELSE '' END)  AS 管理费欠费,
  MAX(CASE payitemsname WHEN '公摊费' THEN unitnum ELSE '' END)  AS 公摊费数量,
  MAX(CASE payitemsname WHEN '公摊费' THEN arrears ELSE '' END)  AS 公摊费欠费,
  MAX(CASE payitemsname WHEN '代收水电' THEN unitnum ELSE '' END)  AS 代收水电数量,
  MAX(CASE payitemsname WHEN '代收水电' THEN arrears ELSE '' END)  AS 代收水电欠费,
  MAX(CASE payitemsname WHEN '违约金' THEN unitnum ELSE '' END)  AS 违约金数量,
  MAX(CASE payitemsname WHEN '违约金' THEN arrears ELSE '' END)  AS 违约金欠费,
  MAX(CASE payitemsname WHEN '车位费' THEN unitnum ELSE '' END)  AS 车位费数量,
  MAX(CASE payitemsname WHEN '车位费' THEN arrears ELSE '' END)  AS 车位费欠费 
  FROM(
    SELECT districtid,districtname,districtorder,buildingtype,COUNT(unitcode) AS unitnum,payitemsname,SUM(arrears) AS arrears 
    FROM(
      SELECT districtid,districtname,districtorder,buildingtype,unitcode,payitemsname,SUM(arrears) AS arrears 
      FROM(
        SELECT districtid,CONCAT(districtname,IF(districtid IN(5,10),IF(buildingtype=0,'住宅','别墅'),'')) AS districtname,districtorder,buildingtype,unitcode,
        IF(
          (districtid IN(4,7,8,9) AND payitemsname IN('别墅管理费','花园管理费')) 
          OR ((districtid=3 OR (districtid IN(5,10) AND buildingtype=1)) AND payitemsname IN('别墅管理费','花园及停车位管理费')) 
          OR ((districtid=2 OR (districtid IN(5,10) AND buildingtype=0)) AND payitemsname IN('物业管理费','车位管理费')) 
          OR (districtid=1 AND payitemsname='车位管理费') 
          OR (districtid=6 AND payitemsname IN('物业管理费','花园管理费','车位管理费','露天车位管理服务费')),
          '管理费',
          IF(
            ((districtid IN(3,4,8,9) OR (districtid IN(5,10) AND buildingtype=1)) AND payitemsname IN('公共电费分摊','景观大道路灯电费')) 
            OR ((districtid IN(2,7) OR (districtid IN(5,10) AND buildingtype=0)) AND payitemsname IN('公共水费分摊','公共电费分摊','景观大道路灯电费','车库公共水费分摊','车库公共电费','空中花园公共电费')) 
            OR (districtid=1 AND payitemsname IN('二区车库公共电费','公共电费分摊','车库公共水费分摊','车库公共电费')) 
            OR (districtid=6 AND payitemsname IN('二区车库公共电费','公共电费分摊','景观大道路灯电费')),
            '公摊费',
            IF(payitemsname IN('代收水费','代收电费'),'代收水电',IF(chargecategory='违约金','违约金',payitemsname))
          )
        ) AS payitemsname,arrears 
        FROM(
          SELECT a.id AS districtid,a.districtName,a.districtorder,b.buildingType,c.unitCode,d.chargecategory,d.payitemsname,(d.receivableamount-d.receivedamount) AS arrears 
          FROM t_community_district a 
          INNER JOIN t_community_building b ON a.id<=10 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid 
          INNER JOIN t_community_property c ON b.id=c.buildingid 
          INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 
          AND d.receivableDate<=LAST_DAY('2023-12-31') 
          INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id 
          WHERE (districtid IN(4,7,8,9) AND payitemsname IN('别墅管理费','花园管理费')) 
          OR ((districtid=3 OR (districtid IN(5,10) AND buildingtype=1)) AND payitemsname IN('别墅管理费','花园及停车位管理费')) 
          OR ((districtid=2 OR (districtid IN(5,10) AND buildingtype=0)) AND payitemsname IN('物业管理费','车位管理费')) 
          OR (districtid=1 AND payitemsname='车位管理费') 
          OR (districtid=6 AND payitemsname IN('物业管理费','花园管理费','车位管理费','露天车位管理服务费')) 
          OR ((districtid IN(3,4,8,9) OR (districtid IN(5,10) AND buildingtype=1)) AND payitemsname IN('公共电费分摊','景观大道路灯电费')) 
          OR ((districtid IN(2,7) OR(districtid IN(5,10) AND buildingtype=0)) AND payitemsname IN('公共水费分摊','公共电费分摊','景观大道路灯电费','车库公共水费分摊','车库公共电费','空中花园公共电费')) 
          OR (districtid=1 AND payitemsname IN('二区车库公共电费','公共电费分摊','车库公共水费分摊','车库公共电费')) 
          OR (districtid=6 AND payitemsname IN('二区车库公共电费','公共电费分摊','景观大道路灯电费')) 
          OR d.payitemsname IN('代收水费','代收电费') 
          OR d.chargecategory='违约金' 
          UNION ALL 
          SELECT a.id AS districtid,a.districtName,a.districtorder,b.buildingType,c.unitCode,d.chargecategory,'车位费' AS payitemsname,(d.receivableamount-d.receivedamount) AS arrears 
          FROM t_community_district a 
          INNER JOIN t_community_building b ON a.id<=10 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid 
          INNER JOIN t_community_property c ON b.id=c.buildingid 
          INNER JOIN t_community_receivables d ON c.id=d.estateid AND d.receivableamount-d.receivedamount<>0 
          AND d.receivableDate<=LAST_DAY('2023-12-31') 
          INNER JOIN t_community_pay_items e ON (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id 
          WHERE d.payitemsname='车位管理费'
        ) a
      ) a 
      GROUP BY districtid,districtname,buildingtype,unitcode,payitemsname 
    ) a 
    GROUP BY districtid,buildingtype,payitemsname
  ) b 
  GROUP BY districtid,buildingtype
) aa 
LEFT JOIN(
  SELECT districtid,buildingtype,COUNT(amount3) AS amount3num,SUM(amount3) AS amount3 
  FROM(
    SELECT districtid,buildingtype,unitcode,SUM(amount1) AS amount3 
    FROM(
      SELECT a.id AS districtid,a.districtName,a.districtorder,b.buildingType,c.unitCode,(d.receivableamount-d.receivedamount) AS amount1 
      FROM  t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e 
      WHERE a.id<=10 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid 
      AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id 
      AND d.receivableamount-d.receivedamount<>0
      AND d.receivabledate>=DATE_ADD(LAST_DAY('2023-12-31'), INTERVAL-3 MONTH) 
      AND d.receivableDate<=LAST_DAY('2023-12-31') 
    ) a GROUP BY unitcode 
  ) a 
  GROUP BY districtid,buildingtype
) bb ON aa.districtid=bb.districtid AND aa.buildingtype=bb.buildingtype 
LEFT JOIN(
  SELECT districtid,buildingtype,COUNT(amount6) AS amount6num,SUM(amount6) AS amount6 
  FROM(
    SELECT districtid,buildingtype,unitcode,SUM(amount1) AS amount6 
    FROM(
      SELECT a.id AS districtid,a.districtName,a.districtorder,b.buildingType,c.unitCode,(d.receivableamount-d.receivedamount) AS amount1 
      FROM t_community_district a,t_community_building b,t_community_property c,t_community_receivables d,t_community_pay_items e 
      WHERE a.id<=10 AND b.id NOT IN(2,3,4,7,17,21,126,127,329,330,331) AND a.id=b.districtid AND b.id=c.buildingid AND c.id=d.estateid 
      AND (e.paydate<>0 OR e.id IN(84)) AND d.payitemid=e.id
      AND d.receivableamount-d.receivedamount<>0  
      AND d.receivabledate<DATE_ADD(LAST_DAY('2023-12-31'), INTERVAL-3 MONTH) 
    ) a GROUP BY unitcode 
  ) a GROUP BY districtid,buildingtype 
) cc ON aa.districtid=cc.districtid AND aa.buildingtype=cc.buildingtype 
ORDER BY districtorder




 
  
  
  
  