<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>newfaith-exp</artifactId>
        <groupId>com.foshan</groupId>
        <version>V200R001B03</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>newfaith-exp-health</artifactId>

    <dependencies>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>newfaith-plugins-auditlog</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>newfaith-plugins-permission</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>newfaith-interface-hospital</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.foshan</groupId>
            <artifactId>newfaith-plugins-sms</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dysmsapi20170525</artifactId>
            <version>2.0.24</version>
            <scope>import</scope>
        </dependency>
    </dependencies>
</project>