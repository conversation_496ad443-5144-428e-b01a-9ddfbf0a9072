package com.foshan.form.generalApi.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.foshan.form.response.BaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value="微信code(GetWechatCodeRes)")
@JsonInclude(JsonInclude.Include.NON_NULL)
public  class GetWechatCodeRes extends BaseResponse {


    /**
     *
     */
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "ID",example="1")
    private String openId;


}
