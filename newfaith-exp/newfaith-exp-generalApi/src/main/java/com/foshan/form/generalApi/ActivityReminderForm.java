package com.foshan.form.generalApi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "活动提醒表单")
public class ActivityReminderForm {
    
    @ApiModelProperty(value = "用户openid", required = true)
    private String openId;
    
    @ApiModelProperty(value = "活动名称", required = true)
    private String activityName;
    
    @ApiModelProperty(value = "活动时间", required = true)
    private String time;
    
    @ApiModelProperty(value = "活动地点", required = true)
    private String location;
} 