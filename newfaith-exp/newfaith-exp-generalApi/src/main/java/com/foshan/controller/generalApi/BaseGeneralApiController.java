package com.foshan.controller.generalApi;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;

import com.foshan.controller.BaseController;
import com.foshan.service.generalApi.*;
import com.foshan.util.ContextInfo;


public class BaseGeneralApiController extends BaseController {

//	@Resource(name = "educationEventService")
//	protected IEducationEventService educationEventService;
//
//	@Resource(name = "educationMemberService")
//	protected IEducationMemberService educationMemberService;

	@Resource(name = "wechatTemplateMessageService")
	protected IWechatTemplateMessageService wechatTemplateMessageService;

	@Autowired
	protected ContextInfo contextInfo;
}

