package com.foshan.service.generalApi.impl;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.foshan.entity.*;
import com.foshan.entity.context.EntityContext;

import com.foshan.form.AssetForm;
import com.foshan.form.RegionForm;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.model.permssion.AccountPrincipalModel;
import com.foshan.model.permssion.PrincipalModel;
import com.foshan.service.impl.GenericService;
import com.foshan.util.CodeUtil;
import com.foshan.util.DateUtil;
import com.foshan.util.HttpClientUtil;
import org.apache.commons.beanutils.Converter;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.Subject;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.ehcache.EhCacheCacheManager;
import org.apache.commons.beanutils.BeanUtilsBean;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.*;


public class GenericGeneralApiService extends GenericService {

	private final static Logger logger = LoggerFactory.getLogger(GenericGeneralApiService.class);

//	@Resource(name = "cacheManager")
	protected EhCacheCacheManager cacheManager;
	
	public static Map<Integer,String> departmentIdMap = new HashMap<>();


	private RegionForm getRegionForm(RegionEntity region) {
		RegionForm regionForm = new RegionForm();
		if (null != region) {
			regionForm.setRegionId(region.getId());
			regionForm.setEndRegionCode(region.getEndRegionCode());
			regionForm.setParentRegionId(null != region.getParentRegion() ? region.getParentRegion().getId() : null);
			regionForm.setRegionCode(region.getRegionCode());
			regionForm.setParentRegionName(
					null != region.getParentRegion() ? region.getParentRegion().getRegionName() : null);
			regionForm.setRegionLevel(region.getRegionLevel());
			regionForm.setRegionName(region.getRegionName());
			regionForm.setStartRegionCode(region.getStartRegionCode());

		}
		return regionForm;
	}


	/**
	 * ******** modify by Genie,直接返回实体，参数失效。
	 */
	protected Object getPrincipal(boolean returnFullEntity) {
		Subject curUser = SecurityUtils.getSubject();
		PrincipalCollection principals = curUser.getPrincipals();
		if (null != principals && !principals.isEmpty()) {
			@SuppressWarnings("unchecked")
			List<Object> principalList = principals.asList();
			Object principal = (null != principalList.get(1) ? principalList.get(1) : null);
			if (principal instanceof AccountPrincipalModel) {
				AccountPrincipalModel partyUser = (AccountPrincipalModel) principal;
				String sql = "select * from t_account where id = '" + partyUser.getId() + "' and userState = '"
						+ EntityContext.RECORD_STATE_VALID + "'";
				Object account = accountDao.getUniqueBySql(sql);
				return account;
			}else if (principal instanceof AccountEntity) {
				AccountEntity shiroAccount = (AccountEntity) principal;
				String sql = "select * from t_account where id = '" + shiroAccount.getId() + "' and userState = '"
						+ EntityContext.RECORD_STATE_VALID + "'";
				Object account = accountDao.getUniqueBySql(sql);
				return account;
			} else if (principal instanceof PlatformUserEntity) {
				PlatformUserEntity shiroPlatformUser = (PlatformUserEntity) principal;
				String sql = "select * from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '"
						+ EntityContext.RECORD_STATE_VALID + "'";
				Object platformUser = platformUserDao.getUniqueBySql(sql);
				return platformUser;
			} else if (principal instanceof PrincipalModel) {
				PrincipalModel shiroPlatformUser = (PrincipalModel) principal;
				String sql = "select * from t_user where id = '" + shiroPlatformUser.getId() + "' and userState = '"
						+ EntityContext.RECORD_STATE_VALID + "'";
				Object platformUser = platformUserDao.getUniqueBySql(sql);
				return platformUser;
			}
			return null;
		} else {
			return null;
		}

	}

	protected AssetForm getAsset(AssetEntity asset) {

		AssetForm assetForm = new AssetForm();

		if (null != asset) {
			assetForm.setAssetId(asset.getId());
			assetForm.setAssetType(asset.getAssetType());
			assetForm.setImageFile(asset.getImageFile());
			assetForm.setAssetName(asset.getAssetName());
			assetForm.setAssetOrders(asset.getAssetOrders());


		}
		return assetForm;
	}

	protected UserEntity getCurrentUser() {
		UserEntity result = null;
		Object principal = getPrincipal(true);
		if(principal == null) {
			return result;
		}
		if(principal instanceof PrincipalModel) {
			PrincipalModel userPrincipalModel = (PrincipalModel) principal;
			result = userDao.get(userPrincipalModel.getId());
		}
		if(principal instanceof PlatformUserEntity) {

			result = (PlatformUserEntity)principal;
		}
		return result;
	}


		public void copyProperties(Object dest, Object orig) {
            try {
				BeanUtilsBean beanUtilsBean = new BeanUtilsBean();

//                beanUtilsBean.getConvertUtils().register(new org.apache.commons.beanutils.converters.BigDecimalConverter(null), BigDecimal.class);
//				beanUtilsBean.getConvertUtils().register(new org.apache.commons.beanutils.converters.DateConverter(null), java.util.Date.class);
//
//				beanUtilsBean.getConvertUtils().register(new org.apache.commons.beanutils.converters.SqlTimestampConverter(null), java.sql.Timestamp.class);
//				beanUtilsBean.getConvertUtils().register(new org.apache.commons.beanutils.converters.SqlDateConverter(null), java.sql.Date.class);
//				beanUtilsBean.getConvertUtils().register(new org.apache.commons.beanutils.converters.SqlTimeConverter(null), java.sql.Time.class);
				beanUtilsBean.getConvertUtils().register(new Converter() {
					public Object convert(Class arg0, Object arg1) {
						if((arg1 instanceof String))
						{
							String str=(String) arg1;
							//判断字符串是否为空或""字符串
							if(str==null||str.trim().length()==0)
								return new BigDecimal(0);
							try {
								return new BigDecimal(str);
							}catch (Exception e){
								e.printStackTrace();
								return new BigDecimal(0);
							}
						}else{
							try {
								return new BigDecimal(String.valueOf(arg1));
							}catch (Exception e){
								e.printStackTrace();
								return new BigDecimal(0);
							}
						}

					}
				},BigDecimal.class);

				beanUtilsBean.copyProperties(dest, orig);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            } catch (InvocationTargetException e) {
                throw new RuntimeException(e);
            }
        }

	public static String createUUId() {
		return UUID.randomUUID().toString().replace("-", "");
	}

	public GenericResponse sendSms(String phone,String messageContent) {
		GenericResponse res = new GenericResponse();

		ObjectMapper objectMapper = new ObjectMapper();
		ObjectNode json = objectMapper.createObjectNode();


		// 发送短信
		json.put("messageContent", messageContent);
		json.put("userNumber", phone);
		json.put("scheduleTime", DateUtil.format(new Date(), 1));
		json.put("f", "1");
//		json.put("templateData", "{\"code\":\""+num+"\"}");
//		json.put("templateCode", "SMS_462050073");

		Map<String, String> result = null;
		try {
			objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
			logger.info("--------------------------------------调用短信接口地址：" + contextInfo.smsInterfaceUrl + ";   参数："
					+ json.toString() + "-------------------------");
			result = HttpClientUtil.post(contextInfo.smsInterfaceUrl, objectMapper.writeValueAsString(json), "utf-8");
			logger.info("--------------------------------------调用短信接口返回：" + result
					+ "----------------------------------------");
		} catch (Exception e) {
			e.printStackTrace();
			res.setRet(ResponseContext.RES_SEND_SMS_CODE);
			res.setRetInfo(ResponseContext.RES_SEND_SMS_INFO);
			return res;
		}

		res.setRet(ResponseContext.RES_SUCCESS_CODE);
		res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
		return res;
	}


}
