package com.foshan.service.generalApi.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.foshan.config.WechatConfig;
import com.foshan.service.generalApi.IWechatAccessTokenService;

import javax.transaction.Transactional;

@Service("wechatAccessTokenService")
@Transactional
public class WechatAccessTokenServiceImpl implements IWechatAccessTokenService {

    private static final Logger logger = LoggerFactory.getLogger(WechatAccessTokenServiceImpl.class);
    
    @Autowired
    private WechatConfig wechatConfig;
    
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 访问令牌缓存
    private static class AccessTokenCache {
        private String token;
        private long expireTime;
        
        public AccessTokenCache(String token) {
            this.token = token;
            // 设置过期时间为当前时间 + 7000秒
            this.expireTime = System.currentTimeMillis() + 7000 * 1000;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() >= expireTime;
        }
    }
    
    private static volatile AccessTokenCache accessTokenCache;
    
    @Override
    public boolean isAccessTokenExpired() {
        return accessTokenCache == null || accessTokenCache.isExpired();
    }
    
    @Override
    public String getAccessToken() {
        // 先检查缓存
        if (!isAccessTokenExpired()) {
            logger.info("从缓存获取access_token");
            return accessTokenCache.token;
        }
        
        // 缓存不存在或已过期，从微信服务器获取新令牌
        synchronized (WechatAccessTokenServiceImpl.class) {
            // 双重检查，防止多线程重复获取
            if (!isAccessTokenExpired()) {
                return accessTokenCache.token;
            }
            
            return fetchNewAccessToken();
        }
    }
    
    @Override
    public String refreshAccessToken() {
        synchronized (WechatAccessTokenServiceImpl.class) {
            return fetchNewAccessToken();
        }
    }
    
    /**
     * 从微信服务器获取新的访问令牌
     */
    private String fetchNewAccessToken() {
        String url = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
                wechatConfig.getAppid(), wechatConfig.getAppsecret());
            
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            JsonNode json = objectMapper.readTree(response.getBody());
            
            if (json.has("access_token")) {
                String token = json.get("access_token").asText();
                // 更新缓存
                accessTokenCache = new AccessTokenCache(token);
                logger.info("成功获取新的access_token并缓存");
                return token;
            } else {
                logger.error("获取access_token失败: " + json.get("errmsg").asText());
                return null;
            }
        } catch (Exception e) {
            logger.error("获取access_token异常", e);
            return null;
        }
    }
}
