package com.foshan.service.generalApi.impl;

import java.util.HashMap;
import java.util.Map;

import com.foshan.form.generalApi.response.GetWechatCodeRes;
import com.foshan.form.response.GenericResponse;
import com.foshan.form.response.IResponse;
import com.foshan.form.response.context.ResponseContext;
import com.foshan.util.DigestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.foshan.config.WechatConfig;
import com.foshan.service.generalApi.IWechatAccessTokenService;
import com.foshan.service.generalApi.IWechatTemplateMessageService;
import com.foshan.form.generalApi.request.WechatTemplateReq;

import javax.transaction.Transactional;

@Service("wechatTemplateMessageService")
@Transactional
public class WechatTemplateMessageServiceImpl extends GenericGeneralApiService implements IWechatTemplateMessageService {

    private static final Logger logger = LoggerFactory.getLogger(WechatTemplateMessageServiceImpl.class);

    @Autowired
    private WechatConfig wechatConfig;

    @Autowired
    private IWechatAccessTokenService wechatAccessTokenService;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 发送模板消息
     */
    @Override
    public IResponse sendTemplateMessage(WechatTemplateReq req) {
        GenericResponse res = new GenericResponse();
        String accessToken = wechatAccessTokenService.getAccessToken();
        if (accessToken == null) {
            res.setRet(ResponseContext.RES_WEIXINAPI_ERROR_CODE);
            res.setRetInfo(ResponseContext.RES_WEIXINAPI_ERROR_INFO);
            return res;
        }

        String sendUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + accessToken;

        try {

            if (!DigestUtil.generateSignature(req.getData(), "F841B679B71DBDB1A89BA8A7FD9B94D6", "SM3").equals(req.getSign())) {
                res.setRet(ResponseContext.RES_PERM_BEYOND_AUTHORITY_CODE);
                res.setRetInfo("校验码错误");
                return res;
            }

            // 构建模板消息数据
            Map<String, Object> messageData = new HashMap<>();
            messageData.put("touser", req.getOpenId());
            messageData.put("template_id", req.getTemplateId());
            if (req.getUrl() != null && !req.getUrl().isEmpty()) {
                messageData.put("url", req.getUrl());
            }

            // 构建模板数据，设置不同字段的颜色
            Map<String, Object> templateData = new HashMap<>();
            req.getData().forEach((key, value) -> {
                Map<String, String> item = new HashMap<>();
                item.put("value", value);
                if (key.equals("first")) {
                    item.put("color", wechatConfig.getColor().getHighlight());
                } else if (key.equals("remark")) {
                    item.put("color", wechatConfig.getColor().getSuccess());
                } else {
                    item.put("color", wechatConfig.getColor().getDefaultColor());
                }
                templateData.put(key, item);
            });
            messageData.put("data", templateData);

            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(objectMapper.writeValueAsString(messageData), headers);

            ResponseEntity<String> response = restTemplate.postForEntity(sendUrl, request, String.class);
            JsonNode json = objectMapper.readTree(response.getBody());

            if (json.get("errcode").asInt() == 0) {
                logger.info("模板消息发送成功 - openId: " + req.getOpenId() + ", templateId: " + req.getTemplateId());
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                return res;
            }else if(json.get("errcode").asInt() == 43101 || json.get("errmsg").asText().contains("user refuse to accept the msg")){
                logger.info("模板消息发送失败: " +json.get("errcode").asText() + " - "+ json.get("errmsg").asText() + " - openId: " + req.getOpenId());
                res.setRet(ResponseContext.RES_WEIXINAPI_ERROR_CODE);
                res.setRetInfo(json.get("errmsg").asText());
                return res;
            }
            else {
                logger.error("模板消息发送失败: " +json.get("errcode").asText() + " - "+ json.get("errmsg").asText() + " - openId: " + req.getOpenId());
                res.setRet(ResponseContext.RES_WEIXINAPI_ERROR_CODE);
                res.setRetInfo(json.get("errmsg").asText());
                return res;
            }

        } catch (Exception e) {
            logger.error("发送模板消息异常 - openId: " + req.getOpenId() + ", templateId: " + req.getTemplateId(), e);
            res.setRet(ResponseContext.RES_WEIXINAPI_ERROR_CODE);
            res.setRetInfo("发送模板消息异常");
            return res;
        }
    }

    @Override
    public IResponse getOpenidByCode(String code) {
        GetWechatCodeRes res = new GetWechatCodeRes();

        String url = "https://api.weixin.qq.com/sns/oauth2/access_token";
        String requestUrl = String.format("%s?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
                url, wechatConfig.getAppid(), wechatConfig.getAppsecret(), code);

        try {
            ResponseEntity<String> response = restTemplate.getForEntity(requestUrl, String.class);
            JsonNode json = objectMapper.readTree(response.getBody());

            if (json.has("openid")) {
                String openid = json.get("openid").asText();
                logger.info("成功获取openid: {}", openid);
                res.setRet(ResponseContext.RES_SUCCESS_CODE);
                res.setRetInfo(ResponseContext.RES_SUCCESS_CODE_INFO);
                res.setOpenId(openid);
            } else {
                logger.error("获取openid失败：{}", json.toString());
                res.setRet(ResponseContext.RES_WEIXINAPI_ERROR_CODE);
                res.setRetInfo("获取openid失败：" + json.get("errmsg").asText());
            }
        } catch (Exception e) {
            logger.error("获取openid异常", e);
            res.setRet(ResponseContext.RES_WEIXINAPI_ERROR_CODE);
            res.setRetInfo("获取openid失败");
        }
        return res;
    }


    public static void main(String[] args) {
        try {
            WechatTemplateMessageServiceImpl service = new WechatTemplateMessageServiceImpl();
            ObjectMapper mapper = new ObjectMapper();
            String openid = "ol2Lk0VeuHnbCafq2hO3ZL-ve5rs";

            // 测试发送模板消息
            System.out.println("\n=== 测试发送模板消 ===");

            // 构建模板数据
            Map<String, String> data = new HashMap<>();
            data.put("character_string11", "10806");  // 设备编号
            data.put("thing8", "人员聚集");           // 事件类型
            data.put("time3", "2024-12-10 18:05:08");   // 告警时间
            data.put("thing4", "915-球机");      // 告警地点

            String enc = DigestUtil.encryptEcb("F841B679B71DBDB1A89BA8A7FD9B94D6",mapper.writeValueAsString(data));
            System.out.println(enc);

            Map reminderResult = mapper.readValue(DigestUtil.decryptEcb("F841B679B71DBDB1A89BA8A7FD9B94D6",enc), Map.class);

            System.out.println(reminderResult.get("thing8"));

            System.out.println(DigestUtil.generateSignature(data, "F841B679B71DBDB1A89BA8A7FD9B94D6", "SM3"));

        } catch (Exception e) {
            System.err.println("测试过程中发生错误：" + e.getMessage());
            e.printStackTrace();
        }
    }
}