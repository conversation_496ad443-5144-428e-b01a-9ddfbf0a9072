package com.foshan.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

@Data
@Configuration
@ConfigurationProperties(prefix = "faith.wechat")
public class WechatConfig {
    private String appid;
    private String appsecret;
    private String templateId;
    private String detailUrl;
    
    private ColorConfig color = new ColorConfig();
    
    @Data
    public static class ColorConfig {
        private String defaultColor;
        private String highlight;
        private String success;
    }
} 