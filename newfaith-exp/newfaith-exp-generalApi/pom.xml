<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.foshan</groupId>
        <artifactId>newfaith-exp</artifactId>
        <version>V200R001B03</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>newfaith-exp-generalApi</artifactId>
    <name>newfaith-exp-generalApi</name>
    <url>http://maven.apache.org</url>
    <dependencies>


        <dependency>
            <groupId>com.foshan</groupId>
            <artifactId>newfaith-core-entity</artifactId>
            <version>V200R001B03</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.foshan</groupId>
            <artifactId>newfaith-core-controller</artifactId>
            <version>V200R001B03</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.foshan</groupId>
            <artifactId>newfaith-plugins-sms</artifactId>
            <version>V200R001B03</version>
        </dependency>

    </dependencies>
</project>